package com.dongni.exam.studyguide.service;

import com.dongni.common.utils.ComparatorEx;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.exam.studyguide.bean.dto.StudyGuideKnowledgeCacheDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideKnowledgeRateDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideQuestionScoreDTO;
import com.dongni.tiku.common.util.MapUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/09/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class StudyGuideKnowledgeServiceTest {
    
    @Autowired
    private StudyGuideKnowledgeService studyGuideKnowledgeService;
    /*
            knowledgeId          deleted treeCode        knowledgeName
        641ab770680d3f0c60f7c77f  FALSE  3.0             集合与常用逻辑用语
        641ab770680d3f0c60f7c780  FALSE  3.0.0           集合
        641ab770680d3f0c60f7c781  FALSE  3.0.0.0         集合的含义与表示
        641ab770680d3f0c60f7c782  FALSE  3.0.0.0.0       集合的概念
        641ab770680d3f0c60f7c783  FALSE  3.0.0.0.0.0     判断元素能否构成集合
        641ab770680d3f0c60f7c784  FALSE  3.0.0.0.0.1     判断是否为同一集合
        641ab770680d3f0c60f7c785  FALSE  3.0.0.0.0.2     根据集合相等关系进行计算
        641ab770680d3f0c60f7c795  FALSE  3.0.0.1         集合间的基本关系
        641ab770680d3f0c60f7c799  TRUE   3.0.0.1.0.2     子集的概念
        641ab770680d3f0c60f7c7b8  FALSE  3.0.1           常用逻辑用语
        641ab770680d3f0c60f7c7b9  FALSE  3.0.1.0         命题及其关系
        641ab770680d3f0c60f7c7ba  FALSE  3.0.1.0.0       命题
        641ab770680d3f0c60f7c7bb  FALSE  3.0.1.0.0.0     命题的概念
        641ab770680d3f0c60f7c7bc  FALSE  3.0.1.0.0.1     判断命题的真假
        641ab770680d3f0c60f7c7bd  FALSE  3.0.1.0.0.2     指出命题的条件和结论
        641ab770680d3f0c60f7c7be  FALSE  3.0.1.0.1       四种命题
        641ab770680d3f0c60f7c7bf  FALSE  3.0.1.0.1.0     写出原命题的否命题及真假判断
        641ab770680d3f0c60f7c7c0  FALSE  3.0.1.0.1.1     写出原命题的逆命题及真假判断
        641ab770680d3f0c60f7c7c1  FALSE  3.0.1.0.1.2     写出原命题的逆否命题及真假判断
        641ab771680d3f0c60f7c7f3  FALSE  3.1             函数与导数
        641ab771680d3f0c60f7c7f4  FALSE  3.1.0           函数及其性质
        641ab771680d3f0c60f7c7f5  FALSE  3.1.0.0         函数及其表示
        641ab771680d3f0c60f7c7f6  FALSE  3.1.0.0.0       函数的定义
        641ab771680d3f0c60f7c7f7  FALSE  3.1.0.0.0.0     函数关系的判断
        641ab771680d3f0c60f7c7f8  FALSE  3.1.0.0.0.1     求函数值
        641ab771680d3f0c60f7c7f9  FALSE  3.1.0.0.0.2     已知函数值求自变量或参数
        641ab771680d3f0c60f7c7fa  FALSE  3.1.0.0.1       区间
        641ab771680d3f0c60f7c7fb  FALSE  3.1.0.0.1.0     区间的定义与表示
        641ab771680d3f0c60f7c7fc  FALSE  3.1.0.0.1.1     区间的关系与运算
        641ab773680d3f0c60f7c947  FALSE  3.2             三角函数与解三角形
        641ab773680d3f0c60f7c948  FALSE  3.2.0           三角函数
        641ab773680d3f0c60f7c949  FALSE  3.2.0.0         任意角和弧度制
        641ab773680d3f0c60f7c94a  FALSE  3.2.0.0.0       周期现象
        641ab773680d3f0c60f7c94b  FALSE  3.2.0.0.1       任意角的概念
        641ab773680d3f0c60f7c94c  FALSE  3.2.0.0.2       终边相同的角
        641ab773680d3f0c60f7c94d  FALSE  3.2.0.0.2.0     找出终边相同的角
        641ab773680d3f0c60f7c94e  FALSE  3.2.0.0.2.1     根据图形写出角（范围）
        641ab773680d3f0c60f7c94f  FALSE  3.2.0.0.3       轴线角
        641ab774680d3f0c60f7c950  FALSE  3.2.0.0.4       象限角
        641ab774680d3f0c60f7c951  FALSE  3.2.0.0.4.0     确定已知角所在象限
        641ab774680d3f0c60f7c952  FALSE  3.2.0.0.4.1     由已知角所在的象限确定某角的范围
        641ab774680d3f0c60f7c953  FALSE  3.2.0.0.4.2     确定n倍角所在象限
        641ab774680d3f0c60f7c954  FALSE  3.2.0.0.4.3     确定n分角所在象限
     */
    @Test
    public void computeKnowledgeRate() {
        List<StudyGuideQuestionScoreDTO> list = new ArrayList<>();
        Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache = new HashMap<>();
        Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache = new HashMap<>();
        list.add(JSONUtil.parse("{\"questionId\":\"101\",\"questionScore\":5.0,\"score\":5.0}", StudyGuideQuestionScoreDTO.class));
        list.add(JSONUtil.parse("{\"questionId\":\"102\",\"questionScore\":5.0,\"score\":4.0}", StudyGuideQuestionScoreDTO.class));
        list.add(JSONUtil.parse("{\"questionId\":\"103\",\"questionScore\":5.0,\"score\":3.0}", StudyGuideQuestionScoreDTO.class));
        list.add(JSONUtil.parse("{\"questionId\":\"104\",\"questionScore\":5.0,\"score\":2.0}", StudyGuideQuestionScoreDTO.class));
        list.add(JSONUtil.parse("{\"questionId\":\"105\",\"questionScore\":5.0,\"score\":1.0}", StudyGuideQuestionScoreDTO.class));
        list.add(JSONUtil.parse("{\"questionId\":\"106\",\"questionScore\":5.0,\"score\":0.0}", StudyGuideQuestionScoreDTO.class));
        Map<String, List<String>> questionId2KnowledgeIdList = MapUtil.of(
                /*
                    641ab770680d3f0c60f7c780  FALSE  3.0.0               集合             ↑
                    641ab770680d3f0c60f7c782  FALSE  3.0.0.0.0           集合的概念        √
                    641ab770680d3f0c60f7c784  FALSE  3.0.0.0.0.1         判断是否为同一集合 √
                    641ab770680d3f0c60f7c799  TRUE   3.0.0.1.0.2         子集的概念        √
                 */
                "101", Stream.of("641ab770680d3f0c60f7c782", "641ab770680d3f0c60f7c784", "641ab770680d3f0c60f7c799").collect(Collectors.toList()),
                /*
                    641ab770680d3f0c60f7c780  FALSE  3.0.0               集合               ↑
                    641ab770680d3f0c60f7c783  FALSE  3.0.0.0.0.0         判断元素能否构成集合 √
                 */
                "102", Stream.of("641ab770680d3f0c60f7c783", "错误的id不参与计算").collect(Collectors.toList()),
                /*
                    641ab771680d3f0c60f7c7f3  FALSE  3.1                 函数与导数   √
                    641ab771680d3f0c60f7c7f4  FALSE  3.1.0               函数及其性质 ↑
                    641ab771680d3f0c60f7c7f6  FALSE  3.1.0.0.0           函数的定义   √
                 */
                "103", Stream.of("641ab771680d3f0c60f7c7f3", "641ab771680d3f0c60f7c7f6").collect(Collectors.toList()),
                /*
                    641ab771680d3f0c60f7c7f4  FALSE  3.1.0               函数及其性质           ↑
                    641ab771680d3f0c60f7c7f8  FALSE  3.1.0.0.0.1         求函数值               √
                    641ab771680d3f0c60f7c7f9  FALSE  3.1.0.0.0.2         已知函数值求自变量或参数 √
                    
                 */
                "104", Stream.of("641ab771680d3f0c60f7c7f8", "641ab771680d3f0c60f7c7f9").collect(Collectors.toList()),
                /*
                    641ab773680d3f0c60f7c948  FALSE  3.2.0               三角函数        ↑
                    641ab773680d3f0c60f7c94b  FALSE  3.2.0.0.1           任意角的概念     √
                    641ab773680d3f0c60f7c94d  FALSE  3.2.0.0.2.0         找出终边相同的角  √
                 */
                "105", Stream.of("641ab773680d3f0c60f7c94b", "641ab773680d3f0c60f7c94d").collect(Collectors.toList()),
                /*
                    641ab773680d3f0c60f7c948  FALSE  3.2.0               三角函数           √↑
                    641ab774680d3f0c60f7c951  FALSE  3.2.0.0.4.0         确定已知角所在象限  √
                 */
                "106", Stream.of("641ab773680d3f0c60f7c948", "641ab774680d3f0c60f7c951").collect(Collectors.toList())
        );
        for (StudyGuideQuestionScoreDTO questionScoreDTO : list) {
            String questionId = questionScoreDTO.getQuestionId();
            List<String> knowledgeIdList = questionId2KnowledgeIdList.get(questionId);
            questionScoreDTO.setKnowledgeIdList(knowledgeIdList);
        }
        
        System.out.println(" -------------- 待计算的数据: ");
        System.out.println(JSONUtil.toJsonFormatted(list));
        
        List<StudyGuideKnowledgeRateDTO> studyGuideKnowledgeRateDTOList = studyGuideKnowledgeService.computeKnowledgeRate(
                list,
                knowledgeId2InfoCache,
                knowledgeTreeCode2InfoCache
        );
        System.out.println(" -------------- 缓存的知识点缓存 knowledgeId2InfoCache");
        List<StudyGuideKnowledgeCacheDTO> knowledgeIdCacheList = knowledgeId2InfoCache.values().stream()
                .sorted(ComparatorEx.ascNullLast(StudyGuideKnowledgeCacheDTO::getTreeCode))
                .collect(Collectors.toList());
        System.out.println(JSONUtil.toJsonFormatted(knowledgeIdCacheList));
        
        System.out.println(" -------------- 缓存的知识点缓存 knowledgeTreeCode2InfoCache");
        List<StudyGuideKnowledgeCacheDTO> knowledgeTreeCodeCacheList = knowledgeTreeCode2InfoCache.values().stream()
                .sorted(ComparatorEx.ascNullLast(StudyGuideKnowledgeCacheDTO::getTreeCode))
                .collect(Collectors.toList());
        System.out.println(JSONUtil.toJsonFormatted(knowledgeTreeCodeCacheList));
        
        System.out.println(" -------------- 计算后的数据");
        List<StudyGuideKnowledgeRateDTO> studyGuideKnowledgeRateDTOSortedList = studyGuideKnowledgeRateDTOList.stream()
                .sorted(ComparatorEx.ascNullLast(StudyGuideKnowledgeRateDTO::getKnowledgeTreeCode))
                .collect(Collectors.toList());
        System.out.println(JSONUtil.toJsonFormatted(studyGuideKnowledgeRateDTOSortedList));
    }
}
