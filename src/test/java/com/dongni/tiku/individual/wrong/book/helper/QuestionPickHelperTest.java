package com.dongni.tiku.individual.wrong.book.helper;

import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.individual.wrong.book.enums.QuestionTypeEnum;
import com.dongni.tiku.individual.wrong.book.helper.knowledge.KnowledgeInfo;
import com.dongni.tiku.individual.wrong.book.helper.question.ExamQuestionResource;
import com.dongni.tiku.individual.wrong.book.helper.question.SimilarQuestion;
import com.dongni.tiku.similar.Range;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/8/27 周三 下午 06:20
 * @Version 1.0.0
 */
public class QuestionPickHelperTest {
    public static void main(String[] args) {
        ExamInfo examInfo1 = new ExamInfo(1L, "第一次考试", 2, LocalDateTime.of(2025, 8, 1, 0, 0));
        ExamInfo examInfo2 = new ExamInfo(2L, "第二次考试", 2, LocalDateTime.of(2025, 8, 2, 0, 0));
        ExamInfo examInfo3 = new ExamInfo(3L, "第一次作业", 6, LocalDateTime.of(2025, 8, 3, 0, 0));
        ExamInfo examInfo4 = new ExamInfo(4L, "第二次作业", 6, LocalDateTime.of(2025, 8, 4, 0, 0));

        KnowledgeInfo knowledgeInfo1 = new KnowledgeInfo("K1", "知识点1");
        KnowledgeInfo knowledgeInfo2 = new KnowledgeInfo("K2", "知识点2");
        KnowledgeInfo knowledgeInfo3 = new KnowledgeInfo("K3", "知识点3");
        KnowledgeInfo knowledgeInfo4 = new KnowledgeInfo("K4", "知识点4");
        KnowledgeInfo knowledgeInfo5 = new KnowledgeInfo("K5", "知识点5");
        KnowledgeInfo knowledgeInfo6 = new KnowledgeInfo("K6", "知识点6");
        KnowledgeInfo knowledgeInfo7 = new KnowledgeInfo("K7", "知识点7");
        KnowledgeInfo knowledgeInfo8 = new KnowledgeInfo("K8", "知识点8");

        ExamQuestionResource question1 = new ExamQuestionResource();
        question1.setQuestionId("Q1");
        question1.setExamInfo(examInfo1);
        question1.setFinallyScore(2.0);
        question1.setScoreValue(4.0);
        question1.setClassScoreRate(0.6);
        question1.setGradeScoreRate(0.7);
        question1.setKnowledgeInfoList(Arrays.asList(knowledgeInfo1, knowledgeInfo2));
        question1.setQuestionTypeEnum(QuestionTypeEnum.CHOICE_QUESTION);

        ExamQuestionResource question1_1 = new ExamQuestionResource();
        question1_1.setQuestionId("Q1");
        question1_1.setExamInfo(examInfo1);
        question1_1.setFinallyScore(3.0);
        question1_1.setScoreValue(5.0);
        question1_1.setClassScoreRate(0.2);
        question1_1.setGradeScoreRate(0.3);
        question1_1.setKnowledgeInfoList(Arrays.asList(knowledgeInfo1, knowledgeInfo2));
        question1_1.setQuestionTypeEnum(QuestionTypeEnum.CHOICE_QUESTION);

        ExamQuestionResource question2 = new ExamQuestionResource();
        question2.setQuestionId("Q2");
        question2.setExamInfo(examInfo1);
        question2.setFinallyScore(1.0);
        question2.setScoreValue(4.0);
        question2.setClassScoreRate(0.5);
        question2.setGradeScoreRate(0.6);
        question2.setKnowledgeInfoList(Arrays.asList(knowledgeInfo1, knowledgeInfo3));
        question2.setQuestionTypeEnum(QuestionTypeEnum.FILL_BLANK_QUESTION);

        ExamQuestionResource question3 = new ExamQuestionResource();
        question3.setQuestionId("Q3");
        question3.setExamInfo(examInfo2);
        question3.setFinallyScore(3.0);
        question3.setScoreValue(5.0);
        question3.setClassScoreRate(0.7);
        question3.setGradeScoreRate(0.8);
        question3.setKnowledgeInfoList(Arrays.asList(knowledgeInfo2, knowledgeInfo4));
        question3.setQuestionTypeEnum(QuestionTypeEnum.SOLVING_QUESTION);

        ExamQuestionResource question4 = new ExamQuestionResource();
        question4.setQuestionId("Q4");
        question4.setExamInfo(examInfo3);
        question4.setFinallyScore(0.0);
        question4.setScoreValue(3.0);
        question4.setClassScoreRate(0.4);
        question4.setGradeScoreRate(0.5);
        question4.setKnowledgeInfoList(Arrays.asList(knowledgeInfo5, knowledgeInfo6));
        question4.setQuestionTypeEnum(QuestionTypeEnum.CHOICE_QUESTION);

        ExamQuestionResource question5 = new ExamQuestionResource();
        question5.setQuestionId("Q5");
        question5.setExamInfo(examInfo4);
        question5.setFinallyScore(2.0);
        question5.setScoreValue(4.0);
        question5.setClassScoreRate(0.6);
        question5.setGradeScoreRate(0.7);
        question5.setKnowledgeInfoList(Arrays.asList(knowledgeInfo7, knowledgeInfo8));
        question5.setQuestionTypeEnum(QuestionTypeEnum.SOLVING_QUESTION);

        ExamQuestionResource question6 = new ExamQuestionResource();
        question6.setQuestionId("Q6");
        question6.setExamInfo(examInfo4);
        question6.setFinallyScore(2.0);
        question6.setScoreValue(4.0);
        question6.setClassScoreRate(0.6);
        question6.setGradeScoreRate(0.7);
        question6.setKnowledgeInfoList(Arrays.asList(knowledgeInfo1, knowledgeInfo3));
        question6.setQuestionTypeEnum(QuestionTypeEnum.SOLVING_QUESTION);

        PickSetting pickSetting = new PickSetting();
        pickSetting.setOriginalQuestionCountLeft(2);
        pickSetting.setOriginalQuestionCountRight(5);
        pickSetting.setChoiceQuestionRatio(0.2);
        pickSetting.setFillBlankQuestionRatio(0.5);
        pickSetting.setSolutionQuestionRatio(0.3);
        pickSetting.setWrongQuestionStudentScoreRateRange(Range.parse("[0.0, 0.6)", BigDecimal::new));
        pickSetting.setKnowledgeStudentGraspRange(Range.parse("[0.0, 0.6)", BigDecimal::new));
        pickSetting.setSimilarQuestion(true);
        pickSetting.setSimilarQuestionCount(2);
        pickSetting.setClassConsolidationQuestion(true);
        pickSetting.setPeriod(2);

        Map<String, Set<SimilarQuestion>> wrongBookSimilarQuestionMap = new HashMap<>();
        SimilarQuestion s1 = new SimilarQuestion("S1", 2, 1);
        SimilarQuestion s2 = new SimilarQuestion("S2", 2, 2);
        wrongBookSimilarQuestionMap.put("Q1", Sets.newHashSet(s1, s2));

        QuestionPickHelper.QuestionPickHelperBuilder builder = QuestionPickHelper.builder();
        builder.setExamQuestionResources(Arrays.asList(question1, question1_1, question2, question3, question4, question5, question6))
                .setPickSetting(pickSetting)
                .setRecommendedQuestionIds(Sets.newHashSet("Q1"))
                .setOtherStudentSimilarQuestionMap(null)
                .setOwnRecommendationService(null) // 该类为SpringBoot管理，这里没有mock，设置为null
                .setWrongBookSimilarQuestionMap(wrongBookSimilarQuestionMap)
                .setDebug(true);
        QuestionPickHelper questionPickHelper = builder.build();
        questionPickHelper.pick();

        Map<String, Set<SimilarQuestion>> pickedQuestionMap = questionPickHelper.getPickedOriginalAndSimilarQuestionMap();
        Map<Integer, Set<String>> pickedOriginalQuestionMap = questionPickHelper.getPerPeriodPickedOriginalQuestionMap();
    }
}