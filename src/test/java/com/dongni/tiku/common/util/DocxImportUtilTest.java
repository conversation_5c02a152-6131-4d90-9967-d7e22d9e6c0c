package com.dongni.tiku.common.util;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * DocxImportUtil test class
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DocxImportUtilTest {

    @Test
    public void testDocxToHtml() {
        // Test with a sample docx file path
        String docxPath = "D:/home/<USER>/docxToHtml.docx";
        
        // Call the conversion method
        String htmlResult = DocxImportUtil.docxToHtml(docxPath);
        
        // Print result for verification
        if (htmlResult != null) {
            System.out.println("Conversion successful!");
            System.out.println("HTML length: " + htmlResult.length());
            System.out.println("HTML preview (first 500 chars):");
            System.out.println(htmlResult.substring(0, Math.min(500, htmlResult.length())));
            
            // Save to file for inspection
            try {
                File outputFile = new File("D:/home/<USER>/converted_output.html");
                outputFile.getParentFile().mkdirs();
                try (FileWriter writer = new FileWriter(outputFile)) {
                    writer.write(htmlResult);
                }
                System.out.println("HTML saved to: " + outputFile.getAbsolutePath());
            } catch (IOException e) {
                System.err.println("Failed to save HTML file: " + e.getMessage());
            }
        } else {
            System.out.println("Conversion failed - result is null");
        }
    }
    
    @Test
    public void testDocxToHtmlWithNullPath() {
        String htmlResult = DocxImportUtil.docxToHtml(null);
        assert htmlResult == null : "Should return null for null path";
        System.out.println("Null path test passed");
    }
    
    @Test
    public void testDocxToHtmlWithEmptyPath() {
        String htmlResult = DocxImportUtil.docxToHtml("");
        assert htmlResult == null : "Should return null for empty path";
        System.out.println("Empty path test passed");
    }
    
    @Test
    public void testDocxToHtmlWithNonExistentFile() {
        String htmlResult = DocxImportUtil.docxToHtml("non_existent_file.docx");
        assert htmlResult == null : "Should return null for non-existent file";
        System.out.println("Non-existent file test passed");
    }
}
