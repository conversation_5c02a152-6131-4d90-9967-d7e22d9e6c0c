create table t_exam_mark_comment
(
    exam_mark_comment_id bigint(19) auto_increment comment '批注ID'
        primary key,
    exam_id              bigint(19)   not null comment '考试Id',
    paper_id             bigint(19)   not null comment '试卷ID',
    class_id             bigint(19)   not null comment '班级id',
    student_id           bigint(19)   not null comment '学生id',
    question_number      int(10)      not null comment '试卷试题编号',
    exam_item_id         bigint(19)   not null comment '考试详细ID',
    teacher_id           bigint(19)   not null comment '老师ID',
    teacher_name         varchar(20)  not null comment '老师姓名',
    comment_url          varchar(800) null comment '批注路径',
    creator_id           bigint(19)   not null comment '创建人编号',
    creator_name         varchar(20)  not null comment '创建人',
    create_date_time     datetime     not null comment '创建时间',
    modifier_id          bigint(19)   not null comment '修改人编号',
    modifier_name        varchar(20)  not null comment '修改人',
    modify_date_time     datetime     not null comment '修改时间',
    constraint logic
        unique (exam_item_id, teacher_id)
) charset = utf8mb4;

create index idx_exam_paper_qn_stu
    on t_exam_mark_comment (exam_id, paper_id, question_number, student_id);