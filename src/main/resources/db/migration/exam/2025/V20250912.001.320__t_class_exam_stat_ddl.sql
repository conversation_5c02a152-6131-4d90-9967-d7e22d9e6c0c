create table if not exists t_class_exam_stat
(
    class_exam_stat_id   bigint(20)  auto_increment comment '考试班级报告表主键' primary key,
    exam_id              bigint(20)  not null comment '考试id',
    class_id             bigint(20)  not null comment '班级id',
    stat_status          tinyint(2)  default 0 not null comment '报告状态：0未公布  1已公布',
    creator_id           bigint(20)  not null comment '创建人Id',
    creator_name         varchar(20) not null comment '创建人',
    create_date_time     datetime    not null comment '创建时间',
    modifier_id          bigint(20)  not null comment '修改人Id',
    modifier_name        varchar(20) not null comment '修改人',
    modify_date_time     datetime    not null comment '修改时间',
    unique key logic (exam_id, class_id)
) comment '考试班级报告';

create index idx_classId on t_class_exam_stat (class_id);