create table if not exists  `t_recognition_card_ext`(
    `recognition_card_ext_id` bigint(20) not null primary key auto_increment comment '识别卡扩展信息ID',
    `bar_num`  varchar(20) default '0' comment '条码生考号',
    `filling_num` varchar(20) default '0' comment '填空生考号',
    `hand_num` varchar(20) default '0' comment '手写生考号',
    `hand_name` varchar(50) default '' comment '手写姓名',
    `qr_num`  varchar(20) default '0' comment '二维码考号',
    `num_status` tinyint(2) default 0 comment '考号状态, 1: 正常, 0: 未识别, -1: 异常',
    `create_date_time` datetime not null comment '创建时间',
    `recognition_card_id` bigint(20) not null comment '识别卡ID',
    `recognition_id` bigint(20) not null comment '识别ID',
    `relative_student_id` bigint(20) not null comment '重新关联学生ID',
    `repeat_num` tinyint(4) default 1 comment '关联学生答题卡重复计数'
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='异常答题卡扩充表';

create unique index `idx_recognition_card_id` on `t_recognition_card_ext`(`recognition_id`,`recognition_card_id`);

create index `idx_recognition_id_relative_student_id` on `t_recognition_card_ext`(`recognition_id`, `relative_student_id`);

