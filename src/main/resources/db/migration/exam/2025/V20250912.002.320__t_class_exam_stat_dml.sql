insert ignore into t_class_exam_stat
    (exam_id, class_id, stat_status, creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time)
select te.exam_id,
       tec.class_id,
       if(tec.class_status = 20, 1, 0),
       te.creator_id,
       te.creator_name,
       now(),
       tec.modifier_id,
       tec.modifier_name,
       now()
from t_exam te
inner join t_exam_class tec on te.exam_id = tec.exam_id
where te.exam_type = 12;