CREATE TABLE `t_homework_weekly_report_student_knowledge`
(
    `homework_weekly_report_student_knowledge_id` BIGINT         NOT NULL AUTO_INCREMENT COMMENT '主键',
    `homework_weekly_report_student_id`           BIGINT         NOT NULL COMMENT '作业周报学生ID',
    `course_id`                                   BIGINT         NOT NULL COMMENT '课程ID',
    `knowledge_id`                                VARCHAR(64)    NOT NULL COMMENT '知识点ID',
    `knowledge_name`                              VARCHAR(255)   NOT NULL COMMENT '知识点名称',
    `knowledge_tree_code`                         VARCHAR(128)   NOT NULL COMMENT '知识点层级编码',
    `student_score_rate`                          DECIMAL(10, 4) DEFAULT NULL COMMENT '学生得分率-缺考为null',
    `class_score_rate`                            DECIMAL(10, 4) NOT NULL COMMENT '班级得分率-整班缺考',
    `student_wrong_question_count`                INT COMMENT '学生错题数-缺考为null',
    `creator_id`                                  BIGINT         NOT NULL COMMENT '创建人ID',
    `creator_name`                                VARCHAR(20)    NOT NULL COMMENT '创建人',
    `create_date_time`                            DATETIME       NOT NULL COMMENT '创建时间',
    `modifier_id`                                 BIGINT         NOT NULL COMMENT '修改人ID',
    `modifier_name`                               VARCHAR(20)    NOT NULL COMMENT '修改人',
    `modify_date_time`                            DATETIME       NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`homework_weekly_report_student_knowledge_id`),
    KEY `idx_student_course` (`homework_weekly_report_student_id`, `course_id`)
) COMMENT = '作业周报学生知识点表';