ALTER table t_exam_result_evaluation ADD column `exam_id` bigint(20) NOT NULL COMMENT '考试id' AFTER evaluation_student_name;
ALTER table t_exam_result_evaluation DROP INDEX examResultStudentIndex;
ALTER table t_exam_result_evaluation DROP INDEX examResultEvaluationStudentIndex;
ALTER table t_exam_result_evaluation ADD UNIQUE KEY  `logicStudentId` (`exam_id`,`student_id`) USING BTREE;
ALTER table t_exam_result_evaluation ADD UNIQUE KEY  `logicEvaluationStudentId` (`exam_id`,`evaluation_student_id`) USING BTREE;