# 上海 物化生政史地：6选3
delete from t_score_level_change_course where area_id = 310000;
replace INTO t_score_level_change_course
(area_id, course_id, course_name, creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time) VALUES
(310000, 5, '物理', 1, '超级管理员', now(), 1, '超级管理员', now()),
(310000, 6, '化学', 1, '超级管理员', now(), 1, '超级管理员', now()),
(310000, 7, '生物', 1, '超级管理员', now(), 1, '超级管理员', now()),
(310000, 8, '政治', 1, '超级管理员', now(), 1, '超级管理员', now()),
(310000, 9, '历史', 1, '超级管理员', now(), 1, '超级管理员', now()),
(310000, 10, '地理', 1, '超级管理员', now(), 1, '超级管理员', now());

# 北京、山东、天津 也和上海一样是物化生政史地6选3
delete from t_score_level_change_course where area_id in (110000,370000,120000);
replace INTO t_score_level_change_course
(area_id, course_id, course_name, creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time)
select t.area_id, course_id, course_name, 1, '超级管理员', now(), 1, '超级管理员', now()
from t_score_level_change_course tslcc
join (SELECT 110000 AS area_id UNION ALL
      SELECT 370000 UNION ALL
      SELECT 120000) t
where tslcc.area_id = 310000;

# 海南 物化生政史地：6选3，但所有科目都需要赋分
delete from t_score_level_change_course where area_id = 460000;
replace INTO t_score_level_change_course
(area_id, course_id, course_name, creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time) VALUES
(460000, 2, '语文', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 3, '数学', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 4, '英语', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 5, '物理', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 6, '化学', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 7, '生物', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 8, '政治', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 9, '历史', 1, '超级管理员', now(), 1, '超级管理员', now()),
(460000, 10, '地理', 1, '超级管理员', now(), 1, '超级管理员', now());

# 浙江 物化生政史地技：7选3 浙江有个技术科目，还是个综合科目
delete from t_score_level_change_course where area_id = 330000;
replace INTO t_score_level_change_course
(area_id, course_id, course_name, creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time) VALUES
(330000, 5, '物理', 1, '超级管理员', now(), 1, '超级管理员', now()),
(330000, 6, '化学', 1, '超级管理员', now(), 1, '超级管理员', now()),
(330000, 7, '生物', 1, '超级管理员', now(), 1, '超级管理员', now()),
(330000, 8, '政治', 1, '超级管理员', now(), 1, '超级管理员', now()),
(330000, 9, '历史', 1, '超级管理员', now(), 1, '超级管理员', now()),
(330000, 10, '地理', 1, '超级管理员', now(), 1, '超级管理员', now()),
(330000, 35, '技术', 1, '超级管理员', now(), 1, '超级管理员', now());

# 河北 物理or历史->首选1,化生政地->再选2
delete from t_score_level_change_course where area_id = 130000;
replace INTO t_score_level_change_course
(area_id, course_id, course_name, creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time) VALUES
(130000, 6, '化学', 1, '超级管理员', now(), 1, '超级管理员', now()),
(130000, 7, '生物', 1, '超级管理员', now(), 1, '超级管理员', now()),
(130000, 8, '政治', 1, '超级管理员', now(), 1, '超级管理员', now()),
(130000, 10, '地理', 1, '超级管理员', now(), 1, '超级管理员', now());

# 辽宁、江苏、福建、湖北、湖南、广东、重庆、甘肃、黑龙江、吉林、安徽、江西、贵州、广西、山西、河南、陕西、内蒙古、四川、云南、宁夏、青海 也和河北一样是物理or历史->首选1,化生政地->再选2
delete from t_score_level_change_course where area_id in (
210000,
320000,
350000,
420000,
430000,
440000,
500000,
620000,
230000,
220000,
340000,
360000,
520000,
450000,
140000,
410000,
610000,
150000,
510000,
530000,
640000,
630000);
replace INTO t_score_level_change_course
(area_id, course_id, course_name, creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time)
select t.area_id, course_id, course_name, 1, '超级管理员', now(), 1, '超级管理员', now()
from t_score_level_change_course tslcc
join (SELECT 210000 AS area_id UNION ALL
      SELECT 320000 UNION ALL
      SELECT 350000 UNION ALL
      SELECT 420000 UNION ALL
      SELECT 430000 UNION ALL
      SELECT 440000 UNION ALL
      SELECT 500000 UNION ALL
      SELECT 620000 UNION ALL
      SELECT 230000 UNION ALL
      SELECT 220000 UNION ALL
      SELECT 340000 UNION ALL
      SELECT 360000 UNION ALL
      SELECT 520000 UNION ALL
      SELECT 450000 UNION ALL
      SELECT 140000 UNION ALL
      SELECT 410000 UNION ALL
      SELECT 610000 UNION ALL
      SELECT 150000 UNION ALL
      SELECT 510000 UNION ALL
      SELECT 530000 UNION ALL
      SELECT 640000 UNION ALL
      SELECT 630000) t
where tslcc.area_id = 130000;