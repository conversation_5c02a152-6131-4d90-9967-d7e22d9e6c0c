CREATE TABLE IF NOT EXISTS t_xkw_question_type
(
    xkw_question_type_id        BIGINT(20)   NOT NULL AUTO_INCREMENT COMMENT '学科网题型 这个是懂你的系统的自增id',
    course_id                   BIGINT(20)   NOT NULL COMMENT '课程id',
    course_name                 VARCHAR(128) NOT NULL COMMENT '课程名称',
    xkw_course_id               BIGINT(20)   NOT NULL COMMENT '学科网课程id',
    xkw_course_name             VARCHAR(255) NOT NULL COMMENT '学科网课程名称',
    xkw_question_type           VARCHAR(128) NOT NULL COMMENT '学科网题型id 这个才是学科网的题型的id',
    xkw_question_type_name      VARCHAR(128) NOT NULL COMMENT '学科网题型名称',
    xkw_question_type_parent    VARCHAR(128) NOT NULL COMMENT '学科网题型id父级',
    xkw_question_type_objective TINYINT(4)   NOT NULL COMMENT '1客观题 0主观题',
    xkw_question_type_ordinal   INT(11)      NOT NULL COMMENT '排序',
    creator_id                  BIGINT(20)   NOT NULL,
    creator_name                VARCHAR(100) NOT NULL,
    create_date_time            DATETIME     NOT NULL,
    modifier_id                 BIGINT(20)   NOT NULL,
    modifier_name               VARCHAR(100) NOT NULL,
    modify_date_time            DATETIME     NOT NULL,
    PRIMARY KEY (xkw_question_type_id),
    UNIQUE KEY course_id (course_id, xkw_question_type),
    UNIQUE KEY xkw_course_id (xkw_course_id, xkw_question_type)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='学科网题型';
