-- 兼职审核任务
CREATE TABLE `t_part_time_review_task` (
	`part_time_review_task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '兼职审核任务主键',
	`relative_task_id` bigint(20) NOT NULL COMMENT '关联的任务主键',
	`relative_task_type` tinyint(4) NOT NULL COMMENT '关联的任务类型-partTimeTeacherType字典值',
	`part_time_reviewer_id` bigint(20) NOT NULL COMMENT '兼职审核员主键',
	`part_time_reviewer_name` varchar(100) NOT NULL COMMENT '兼职审核员名称',
	`receive_type` tinyint(4) NOT NULL COMMENT '接收类型-receiveType字典值',
	`receive_time` datetime DEFAULT NULL COMMENT '每次接收任务的时间,审核中时填充',
	`deprecated` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否过时记录,0-有效;1-过时',
	`creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `creator_name` varchar(20) DEFAULT NULL COMMENT '创建人',
    `create_date_time` datetime DEFAULT NULL COMMENT '创建时间',
    `modifier_id` bigint(20) DEFAULT NULL COMMENT '修改人ID',
    `modifier_name` varchar(20) DEFAULT NULL COMMENT '修改人',
    `modify_date_time` datetime DEFAULT NULL COMMENT '修改时间',
	PRIMARY KEY (`part_time_review_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='兼职审核任务表';
ALTER TABLE `t_part_time_review_task` ADD INDEX `idx_relative_task_id` (relative_task_id);
ALTER TABLE `t_part_time_review_task` ADD INDEX `idx_part_time_reviewer_id` (part_time_reviewer_id);

-- 兼职审核日志表
CREATE TABLE `t_part_time_review_log` (
	`part_time_review_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '兼职审核日志主键',
	`part_time_review_task_id` bigint(20) NOT NULL COMMENT '兼职审核任务主键',
	`status` tinyint(4) DEFAULT NULL COMMENT '关联任务状态',
	`status_name` varchar(20) DEFAULT NULL COMMENT '关联状态任务名称',
	`event_name` varchar(255) NOT NULL COMMENT '发生的事件',
	`operate_time` datetime DEFAULT NULL COMMENT '事件发生的时间',
	`creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `creator_name` varchar(20) DEFAULT NULL COMMENT '创建人',
    `create_date_time` datetime DEFAULT NULL COMMENT '创建时间',
	PRIMARY KEY (`part_time_review_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='兼职审核日志表';
ALTER TABLE `t_part_time_review_log` ADD INDEX `idx_part_time_review_task_id` (part_time_review_task_id);

-- 兼职审核推送表
CREATE TABLE `t_part_time_review_push` (
	`part_time_review_push_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '兼职审核推送主键',
	`part_time_review_task_id` bigint(20) NOT NULL COMMENT '兼职审核任务主键',
	`part_time_review_log_id` bigint(20) NOT NULL COMMENT '兼职审核日志主键',
	`to_user_id` bigint(20) NOT NULL COMMENT '接受人userId',
	`to_user_name` varchar(20) NOT NULL COMMENT '接收人姓名',
	`creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
    `creator_name` varchar(20) DEFAULT NULL COMMENT '创建人',
    `create_date_time` datetime DEFAULT NULL COMMENT '创建时间',
	PRIMARY KEY (`part_time_review_push_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='兼职审核推送记录表-记录推送成功的';
ALTER TABLE `t_part_time_review_push` ADD INDEX `idx_part_time_review_task_id` (part_time_review_task_id);
ALTER TABLE `t_part_time_review_push` ADD INDEX `idx_part_time_review_log_id` (part_time_review_log_id);
ALTER TABLE `t_part_time_review_push` ADD INDEX `idx_to_user_id` (to_user_id);

-- t_mark、t_recommendation添加兼职审核领取状态
ALTER TABLE t_marK ADD COLUMN `part_time_review_receive_status` tinyint(4) NOT NULL DEFAULT 2 COMMENT '兼职审核领取状态(1:不允许领取,2:等待领取,3:已被领取)' AFTER `mark_urgency`;
ALTER TABLE t_recommendation ADD COLUMN `part_time_review_receive_status` tinyint(4) NOT NULL DEFAULT 2 COMMENT '兼职审核领取状态(1:不允许领取,2:等待领取,3:已被领取)' AFTER `system_reason`;