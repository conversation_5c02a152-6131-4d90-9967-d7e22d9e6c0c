
-- 精品题库可以控制开关
CREATE TABLE `t_yiqi_status_log` (
  `yiqi_status_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `school_id` bigint(20) NOT NULL COMMENT '学校Id',
  `school_status_before` tinyint(4) NOT NULL COMMENT '学校状态转换前的状态，0 未开启，1 试用，2到期，3正式，4关闭',
  `school_status_after` tinyint(4) NOT NULL COMMENT '学校状态转换后的状态，0 未开启，1 试用，2到期，3正式，4关闭',
  `yiqi_status_before` tinyint(4) NOT NULL COMMENT '一起作业网状态转换前的状态，0 未开启，1 试用，2到期，3正式，4关闭',
  `yiqi_status_after` tinyint(4) NOT NULL COMMENT '一起作业网状态转换后的状态，0 未开启，1 试用，2到期，3正式，4关闭',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(20) NOT NULL COMMENT '创建人',
  `create_date_time` datetime NOT NULL COMMENT '创建时间',
  `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
  `modifier_name` varchar(20) NOT NULL COMMENT '修改人',
  `modify_date_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`yiqi_status_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='一起作业网状态转换记录表';

ALTER TABLE `t_yiqi_account` MODIFY COLUMN `yiqi_account_id` BIGINT ( 20 ) NOT NULL AUTO_INCREMENT COMMENT '主键id' FIRST,
MODIFY COLUMN `status` TINYINT ( 4 ) NOT NULL DEFAULT 1 COMMENT '学校开通状态，0 未开启，1 正式，2 到期，3 试用，4 关闭' AFTER `user_id`;
ALTER TABLE `t_yiqi_account`
ADD COLUMN `school_group_name` VARCHAR ( 100 ) NULL COMMENT '学校别名' AFTER `school_name`,
ADD COLUMN `expire_time` datetime ( 0 ) NULL COMMENT '到期时间' AFTER `yiqi_username`;
