CREATE TABLE `t_jin_juan_admin` (
    `jin_juan_admin_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `jin_juan_admin_name` varchar(20) NOT NULL COMMENT '金卷题库管理员名称',
    `jin_juan_admin_phone` varchar(13) NOT NULL COMMENT '金卷题库管理员联系方式',
    `jin_juan_admin_phone_aes` varchar(24) NOT NULL COMMENT '加密后金卷题库管理员联系方式',
    `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
    `creator_name` varchar(20) NOT NULL COMMENT '创建人',
    `create_date_time` datetime NOT NULL COMMENT '创建时间',
    `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
    `modifier_name` varchar(20) NOT NULL COMMENT '修改人',
    `modify_date_time` datetime NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`jin_juan_admin_id`) USING BTREE,
    UNIQUE KEY `logic` (`jin_juan_admin_phone_aes`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;