CREATE TABLE `t_union_school_excel` (
    `union_school_excel_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '联考学校导入Excel主键',
    `batch_id` bigint(20) NOT NULL COMMENT '批次ID',
    `file_name` varchar(100) NOT NULL COMMENT '文件名',
    `file_path` varchar(255) NOT NULL COMMENT '文件地址',
    `import_status` tinyint(4) NOT NULL COMMENT '导入完成状态(unionSchoolExcelImportStatus)',
    `match_school_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '匹配学校的状态(unionSchoolMatchSchoolStatus)',
    `school_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '匹配的学校ID，未匹配填充0',
    `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
    `creator_name` varchar(20) NOT NULL COMMENT '创建人',
    `create_date_time` datetime NOT NULL COMMENT '创建时间',
    `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
    `modifier_name` varchar(20) NOT NULL COMMENT '修改人',
    `modify_date_time` datetime NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`union_school_excel_id`) /*T![clustered_index] CLUSTERED */,
    KEY `batchIdx` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin AUTO_INCREMENT=90001;