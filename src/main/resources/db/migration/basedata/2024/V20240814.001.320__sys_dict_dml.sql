# 试卷管理这一项一直就没开发，不知道状态咋变成1了，先统一置为停止使用状态
update sys_dict set status = 0, sort = 0 where en_name = 'examManagerModule' and `key` = 'examPaperManager' ;

# 其余的正常使用项先更新排序
update sys_dict set sort = 1 where en_name = 'examManagerModule' and `key` = 'examMonitor' ;
update sys_dict set sort = 2 where en_name = 'examManagerModule' and `key` = 'examAnswerCardUpload' ;
update sys_dict set sort = 4 where en_name = 'examManagerModule' and `key` = 'examReadArrange' ;
update sys_dict set sort = 5 where en_name = 'examManagerModule' and `key` = 'examReadMonitor' ;
update sys_dict set sort = 6 where en_name = 'examManagerModule' and `key` = 'examStudentManager' ;

REPLACE INTO `sys_dict` (`cn_name`, `en_name`, `label`, `key`, `value`, `status`, `sort`, `remark`, `creator_id`, `creator_name`,
                         `create_date_time`, `modifier_id`, `modifier_name`, `modify_date_time`)
VALUES('诊断管理模块项', 'examManagerModule', '试题设置', 'examQuestionSetting', 7, 1, 3, NULL, 1, '超级管理员', now(), 1,'超级管理员', now());