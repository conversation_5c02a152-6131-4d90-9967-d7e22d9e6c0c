CREATE TABLE `t_course_selection_group` (
        `course_selection_group_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '选科组合主键',
        `course_selection_group_name` varchar(50) NOT NULL COMMENT '学科组合名称',
        `course_selection_group_label` varchar(50) DEFAULT '' COMMENT '学科组合标签',
        `stage` tinyint(4) NOT NULL COMMENT '学段',
        `internal_status` tinyint(4) NOT NULL COMMENT '选课组合内置状态',
        `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
        `creator_name` varchar(20) NOT NULL COMMENT '创建人',
        `create_date_time` datetime NOT NULL COMMENT '创建时间',
        `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
        `modifier_name` varchar(20) NOT NULL COMMENT '修改人',
        `modify_date_time` datetime NOT NULL COMMENT '修改时间',
        PRIMARY KEY (`course_selection_group_id`),
        UNIQUE KEY `logic` (`stage`,`course_selection_group_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='选考科目组';

CREATE TABLE `t_course_selection_group_item` (
        `course_selection_group_item_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
        `course_selection_group_id` bigint(11) NOT NULL COMMENT '选科科目组主键',
        `course_id` bigint(11) DEFAULT NULL COMMENT '课程ID（外语科目为null）',
        `course_name` varchar(50) NOT NULL COMMENT '课程名称',
        `foreign_lang` tinyint(4) NOT NULL COMMENT '是否为外语（0-否，1-是）',
        `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
        `creator_name` varchar(20) NOT NULL COMMENT '创建人',
        `create_date_time` datetime NOT NULL COMMENT '创建时间',
        `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
        `modifier_name` varchar(20) NOT NULL COMMENT '修改人',
        `modify_date_time` datetime NOT NULL COMMENT '修改时间',
        PRIMARY KEY (`course_selection_group_item_id`),
        KEY `csgIdx` (`course_selection_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='选考科目组具体课程';

CREATE TABLE `t_student_course_selection` (
      `student_course_selection_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '学生选科主键',
      `student_id` bigint(20) NOT NULL COMMENT '学生ID',
      `course_selection_group_id` bigint(20) DEFAULT NULL COMMENT '选考科目组ID（为null则是无选考科目组）',
      `foreign_course_id` bigint(20) DEFAULT NULL COMMENT '外语科目ID（为null则是无外语选科）',
      `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
      `creator_name` varchar(20) NOT NULL COMMENT '创建人',
      `create_date_time` datetime NOT NULL COMMENT '创建时间',
      `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
      `modifier_name` varchar(20) NOT NULL COMMENT '修改人',
      `modify_date_time` datetime NOT NULL COMMENT '修改时间',
      PRIMARY KEY (`student_course_selection_id`),
      UNIQUE KEY `logic` (`student_id`),
      KEY `csgIdx` (`course_selection_group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生选考组合和外语类型';

ALTER TABLE `t_course`
    ADD COLUMN `foreign_lang` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否为外语课程（0-否，1-是）' AFTER `member_str`;


UPDATE t_course SET foreign_lang = 1 WHERE course_name = '英语' AND course_type = 1 AND stage = 1;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '英语' AND course_type = 1 AND stage = 2;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '日语' AND course_type = 1 AND stage = 2;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '俄语' AND course_type = 1 AND stage = 2;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '英语' AND course_type = 1 AND stage = 3;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '日语' AND course_type = 1 AND stage = 3;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '俄语' AND course_type = 1 AND stage = 3;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '法语' AND course_type = 1 AND stage = 3;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '西班牙语' AND course_type = 1 AND stage = 3;

UPDATE t_course SET foreign_lang = 1 WHERE course_name = '德语' AND course_type = 1 AND stage = 3;
