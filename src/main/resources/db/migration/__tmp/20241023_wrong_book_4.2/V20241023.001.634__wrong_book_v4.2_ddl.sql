CREATE TABLE `t_wrong_book_student_roster` (
   `wrong_book_student_roster_id` bigint(20) NOT NULL AUTO_INCREMENT,
   `school_id` bigint(20) NOT NULL COMMENT '学校ID',
   `school_name` varchar(100) NOT NULL COMMENT '学校名称',
   `grade_id` bigint(20) DEFAULT NULL COMMENT '年级ID',
   `grade_name` varchar(50) DEFAULT NULL COMMENT '年级名称',
   `class_id` bigint(20) DEFAULT NULL COMMENT '班级ID',
   `class_name` varchar(50) DEFAULT NULL COMMENT '班级名称',
   `student_id` bigint(20) NOT NULL COMMENT '学生ID',
   `student_name` varchar(30) NOT NULL COMMENT '学生名称',
   `student_num` varchar(50) NOT NULL COMMENT '学号',
   `course_id` bigint(20) NOT NULL COMMENT '课程ID',
   `course_name` varchar(200) NOT NULL COMMENT '课程名称',
   `wrong_book_student_roster_status` tinyint(4) NOT NULL COMMENT '导入状态',
   `creator_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人ID',
   `creator_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
   `create_date_time` datetime NOT NULL COMMENT '创建时间',
   `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
   `modifier_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
   `modify_date_time` datetime NOT NULL COMMENT '修改时间',
   PRIMARY KEY (`wrong_book_student_roster_id`) /*T![clustered_index] CLUSTERED */,
   UNIQUE KEY `uk_scid_gid_courseId_sid` (`school_id`,`grade_id`,`course_id`,`student_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='个册学生名单管理';

ALTER TABLE `t_wrong_book`
    ADD COLUMN `wrong_book_auto_mode_status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '错题本任务自动模式（0-初始化，1-手动，2-自动）' AFTER `wrong_book_name`;
