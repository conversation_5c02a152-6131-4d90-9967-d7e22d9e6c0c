CREATE TABLE `t_third_class_header`  (
  `third_class_header_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '班主任表主键',
  `third_school_id` bigint(20) NOT NULL COMMENT '所属学校id',
  `third_grade_id` bigint(20) NOT NULL COMMENT '所属的年级ID',
  `third_class_id` bigint(20) NOT NULL COMMENT '班级主键',
  `third_header_id` bigint(20) NOT NULL COMMENT '班主任，teacherId',
  `header_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '班主任称，teacherName',
  `third_party_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '第三方系统ID',
  `third_primary_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '数据在第三方系统的主键值',
  `deleted` tinyint(4) NOT NULL DEFAULT 0 COMMENT '1 删除，0 未删除',
  `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `creator_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '创建人',
  `create_date_time` datetime NOT NULL COMMENT '创建时间',
  `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
  `modifier_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '修改人',
  `modify_date_time` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`third_class_header_id`) USING BTREE,
  UNIQUE INDEX `logic`(`third_primary_key`, `third_party_id`) USING BTREE,
  INDEX `schoolIdx`(`third_school_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Compact;
