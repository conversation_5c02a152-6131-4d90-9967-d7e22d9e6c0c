CREATE TABLE `t_third_student_course_selection` (
    `third_student_course_selection_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `third_student_id` bigint(20) NOT NULL COMMENT '学生ID',
    `course_selection_group_id` bigint(20) NOT NULL COMMENT '选科组合ID',
    `foreign_course_id` bigint(20) DEFAULT NULL COMMENT '外语课程ID',
    `third_party_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '第三方系统ID',
    `third_primary_key` varchar(100) NOT NULL COMMENT '数据在第三方系统的主键值',
    `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1 删除，0 未删除',
    `creator_id` bigint(20) NOT NULL COMMENT '创建人ID',
    `creator_name` varchar(20) NOT NULL COMMENT '创建人',
    `create_date_time` datetime NOT NULL COMMENT '创建时间',
    `modifier_id` bigint(20) NOT NULL COMMENT '修改人ID',
    `modifier_name` varchar(20) NOT NULL COMMENT '修改人',
    `modify_date_time` datetime NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`third_student_course_selection_id`) /*T![clustered_index] CLUSTERED */,
    KEY `thirdStudentIdx` (`third_student_id`),
    UNIQUE KEY `logic` (`third_party_id`,`third_primary_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

