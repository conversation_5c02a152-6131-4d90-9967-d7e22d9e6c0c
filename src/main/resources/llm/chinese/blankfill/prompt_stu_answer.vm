严格按以下步骤一步一步执行，不可跳跃，前序步骤不允许参照后续步骤内容。
步骤1. 提取图片中的手写体$!{answerTags.get("数据类型")}，手写体可能存在错字或漏字，保留最原始的识别结果即可，忽略印刷体和学生涂划的内容，将识别结果记录在<学生作答>标签中。若学生作答为空，输出未作答。
步骤2. 将标准答案'${answerTags.get("标准答案")}'输出到<标准答案>标签中。
步骤3. 将'若学生作答与标准答案相符，可得${scoreValue}分。$!{answerTags.get("给分标准")}；'输出到<给分标准>中。
步骤4. 将内容「$!{questionTags.get("题干")}」输出到<题干>标签中。
步骤5. 依据<标准答案>和<给分标准>，参考<题干>,评判<学生作答>的内容。评分时注意以下事项:
          注意：<题干>的作用是用于辅助判断学生的作答是否为标准答案外的其他正确答案，无须依据<题干>解题。
          注意：<题干>和<答案>不一定完全匹配，当二者冲突时，以<答案>和<给分标准>的内容为标准。
          注意：多个空时，无特殊说明，答案需要与顺序一致才给分。
      在考虑以上注意事项后，在<思考>标签中给出你的评分结果及评分依据。
步骤6. 在<小题分>标签中记录你在<思考>标签中给出的每个小题的评分。
步骤7. 在<得分>标签中给出<小题分>得分相加得到的总分。
输出示例：
<学生作答>步骤1的执行结果</学生作答>
<标准答案>步骤2的执行结果</标准答案>
<给分标准>步骤3的执行结果</给分标准>
<题干>步骤4的执行结果</题干>
<思考>步骤5的执行过程</思考>
<小题分>步骤6的执行结果<小题分>
<得分>步骤7的结果,只展示分数值数值即可，若无分数，展示0即可</得分>