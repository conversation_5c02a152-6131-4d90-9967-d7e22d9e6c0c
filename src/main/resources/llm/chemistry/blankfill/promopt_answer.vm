1. ${desc}是一道化学填空题的标准答案和给分标准（给分标准可能为空）。这里先提供了一些基础要求：
  - 在表达识别到的化学方程式时，当存在反应条件时，方程式中的“=”和反应条件需要使用 latex 表示，latex 格式表示部分需要用“$”包裹起来，其他部分仍然使用 Unicode 字符表示，加热条件使用Δ表示。

2. 分析以上内容，提取标准答案和给分标准（标准答案已经体现在内容中，无须推理），若标准答案是化学方程式，先将 “≜” 替换为化学方程式加热条件的写法后再输出到<标准答案>中,将给分标准输出到}<给分标准>标签中,给分标准一般形式为'答对xxx给x分，答对yyyy给y分'或'第一空x分，[可选的得分要点]，[可选的格式要求]，第二空y分'，[可选的得分要点]，[可选的格式要求]（满分${score_value}分，若没有特殊说明将分值平均分配到各个空）。
3. 据标准答案，将需作答的填空数量输出到<填空数量>标签中(注意，内容如果是一个整体，只能算一个空)，调整<标准答案>标签的内容格式为：第一空答案:答案1，第二空答案：答案2
4. 根据<标准答案>和<给分标准>，分析此题是否为多选题选项，选择题一般会有类似'选AB，或是选对、选错、漏选之类的信息'，如果可以确定，在<多选题>标签输出true，如果确定不是或不太确定，输出false.
5. 如果标准答案包含数学公式，将内容转换为latex公式，并更新<标准答案>为包含latex公式的内容。分析标准答案内容包含的字符种类，用逗号分割，latex符号算为一个字符，将字符更新到<字符集>标签。字符集的包括汉字，英文字母、数字、小数点、正负号，latex符号，除了汉字、字母、数字用统一类别，其余符号输出具体值，如latex表达式'a^2+\dfrac{\sqrt{2a}+7}{3b}'，输出 a、b、2、3、7、+、\sqrt、\dfrac、^ 作为结果， '{}'因实际中不显示，故忽略。
6. 判断题干是否具备以下特征:
  a.<标准答案>是否具备开放性，如果有，输出<开放性>true</开放性>
  b.<标准答案>是否包含需要有latex公式，如果有，输出<latex>true</latex>
  c.在数据类型标签输出<标准答案>数据类型
    如果<标准答案>是纯数字（内容只有0到9及负号），在数据类型标签输出'数字';
    如果<标准答案>纯小数，在数据类型标签输出'小数或分数';
    如果<标准答案>是简单的分数，在数据类型标签输出'分数或小数';
    如果<标准答案>全部由字母、数字和代数符号组成，在数据类型标签输出'表达式'，公式和文字表述杂合在一起的应该属于'汉字'
    如果<标准答案>是化学方程式，在数据类型标签输出'化学方程式'；
    其他情况，在数据类型标签输出'汉字'；
    同时满足多种类型情况，选最接近的一种即可

输出示例（注意：输出的所有标签都要有结束标签）:
<标准答案>标准答案</标准答案>
<填空数量>需要作答的空格数</填空数量>
<给分标准>给分标准</给分标准>
<数据类型>数字</数据类型>
<开放性>标准答案是否具备开放性</开放性>
<latex>标准答案是否包含latex公式</latex>
<多选题>标准答案是否为多选题题选项</多选题>
<字符集>字符集合用顿号分割，同一个字符出现多次只展示一次，并打乱顺序排列</字符集>