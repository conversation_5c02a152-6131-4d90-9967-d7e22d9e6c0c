spring.profiles.active=dongni-test2-local
server.port=8291
dongni.tomcat.apr.enabled=false


dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=true
dongni.web.token.enabled=true
## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=7776000
dongni.web.device.security.enabled=true
dongni.operation.log.enabled=true
dongni.file-storage.local.temp.path.prefix=./
##### \u61C2\u4F60api\u524D\u7F00
dongni.server-api.prefix=/api
dongni.sensitive.info.secret.key=kBwPdo9Ar3rCuwqL6hupIqxfD4b97EGswYMf1hZhuDw=

# \u95EE\u9898\u53CD\u9988\u53D1\u9001\u9489\u9489\u7FA4  \u7F51\u9605V3.20 \u5185\u6D4B\u5DE5\u4F5C\u6C9F\u901A\u7FA4#\u95EE\u9898\u53CD\u9988\u673A\u5668\u4EBA
feedback.dingtalk.access-token=55594a578413861ae8090abe9001a5d160140fa378a29de4c237a6681ac5d3b6
feedback.dingtalk.sign-secret=

# \u8003\u8BD5\u4E2D\u505A\u7684\u90E8\u5206\u64CD\u4F5C\u53D1\u9001\u6D88\u606F\u5230\u9489\u9489 \u7F51\u9605V3.26-\u89E3\u8026-\u8BD5\u9898\u8BBE\u7F6E
dongni.exam.dingtalk.notice.access-token=bbb70528ae9f022b5c8bf164844da1a531c7054b7886c35271a0e95e1850f1f0
dongni.exam.dingtalk.notice.sign-secret=SEC7a84398c44d26da793f54f749b9f43799e1fa5614d2a47001c85b20f116ed7d1

spring.datasource.basedata.url=*****************************************************************************
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=dongni

spring.datasource.exam.url=*******************************************************************************************************************
spring.datasource.exam.username=dn
spring.datasource.exam.password=dongni

spring.datasource.tiku.url=************************************************************************
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=dongni

spring.datasource.university.url=******************************************************************************
spring.datasource.university.username=dn
spring.datasource.university.password=dongni

spring.datasource.third.url=***************************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=dongni

# flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=************
dongni.redis.password=Dongni2015
dongni.redis.port=6379
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

spring.data.mongodb.basedata.host=**************
spring.data.mongodb.basedata.port=27111
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData2015

spring.data.mongodb.exam.host=**************
spring.data.mongodb.exam.port=27111
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam2015

# \u9898\u5E93
spring.data.mongodb.tiku.host=**************
spring.data.mongodb.tiku.port=27111
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku2015

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host=**************
spring.data.mongodb.analysis.port=27111
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis2015
spring.data.mongodb.analysis.database=analysis

spring.data.mongodb.third.host=**************
spring.data.mongodb.third.port=27111
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer2015
spring.data.mongodb.third.database=third_data_transfer


##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F
dongni.file-storage.type=oss
dongni.file-storage.cdn-url=//cdntest.dongni100.com
### oss \u914D\u7F6E dongni.file-storage.type=oss \u65F6\u751F\u6548
dongni.file-storage.oss.endpoint=oss-cn-shenzhen.aliyuncs.com
dongni.file-storage.oss.access-key-id=wymR4n3BFjKf1iXr
dongni.file-storage.oss.access-key-secret=NyKYR5aBjlOcPO4VJiPCAYCJFqjjLA
dongni.file-storage.oss.role-arn=acs:ram::1195803250657709:role/dongni-develop
dongni.file-storage.oss.bucket-name=dongni-test
dongni.file-storage.oss.web.endpoint=osstest.dongni100.com

# \u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=DN_CS_NEW_

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=https://dalao.dongni100.com/api/


# node\u5730\u5740
dongni.node.host=https://dongnitest2.dongni100.com

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.test.17zuoye.net

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540D
sandbox_host_url=http://127.0.0.1:8291

# \u6781\u8BFE\u4E3B\u673A\u57DF\u540D\u5730\u5740
jike_host_url=http://127.0.0.1:9999
# \u6781\u8BFE\u83B7\u53D6token\u7684key
jike_key=jkjy52b56451acb4653a

# \u6781\u8BFE\u6362\u53D6token\u7684sign
open_app_id_jike=dn5rqzkxjk1nuq5

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=dongni
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://dalao.dongni100.com
dongni.exception.reporter.mail.enabled=false
# \u666E\u5929
dongni.third.putian.host=http://smartedu.mydongtai.cn:8901
dongni.third.putian.client-id=pt-bszh
dongni.third.putian.client-secret=bs1234
dongni.third.putian.redirect-uri=http://smartedu.mydongtai.cn:8086/auth/putian/dongtai

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://opendev.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongniTest
dongni.web.auth.client.password=DongniDev

## \u6D88\u606F\u63A8\u9001
dongni.mq.type=ali-rocket-http
dongni.mq.env=test
dongni.mq.message.topic=dn-dev
dongni.mq.messages[0].client=dn
dongni.mq.messages[0].instance=MQ_INST_1195803250657709_BbXyhSW0
dongni.mq.messages[0].topic=dn-dev
dongni.mq.messages[0].queue[0].id=homework-result
dongni.mq.messages[0].queue[1].id=homework-todo
dongni.mq.FieldMayBeFinal.


# \u9898\u5E93\u901A\u77E5\u5F00\u542F \u9700\u8981\u6570\u636E\u8868/\u5B57\u5178\u652F\u6301 \u8BE6\u89C1com.dongni.tiku.third.common.package-info.java
dongni.tiku.notification.enabled=true

# \u817E\u8BAF\u667A\u6167\u6821\u56ED
dongni.third.tencent.school.host=https://oapi.campus.qq.com
dongni.third.tencent.school.app-id=600149
dongni.third.tencent.school.secret-id=600149
dongni.third.tencent.school.secret-key=1de8d6684f3240e2b07fb5ae3daba571

# \u597D\u4E13\u4E1A\u6D4B\u8BD5\u5BF9\u63A5\u53C2\u6570
dongni.third.haozhuanye.host=https://mysqltest.hqjltech.com/schoolscheduleserv/integration/
dongni.third.haozhuanye.app-key=36vxzplw
dongni.third.haozhuanye.secret-key=1GasIqmukiE/Zc8N8bCj8eTeiyGMoYvbbEM+fSVyP+eg=
dongni.third.haozhuanye.host-only=https://mysqltest.hqjltech.com/

dongni.third.haozhuanye2.host=https://collectortest.hqjltech.com/schoolscheduleserv/integration/
dongni.third.haozhuanye2.app-key=36vxzplw
dongni.third.haozhuanye2.secret-key=1GasIqmukiE/Zc8N8bCj8eTeiyGMoYvbbEM+fSVyP+eg=
dongni.third.haozhuanye2.host-only=https://collectortest.hqjltech.com/

# \u60A6\u8BAF\u6D4B\u8BD5\u5BF9\u63A5\u53C2\u6570
dongni.third.yuexun.guzhang.host=https://t.yuexunit.com/auth/api/v1.0/
dongni.third.yuexun.guzhang.host-only=https://t.yuexunit.com/
dongni.third.yuexun.guzhang.app-key=365116909711360
dongni.third.yuexun.guzhang.app-secret=596addbce83c4cd4949c766bf62aaea1

dongni.third.yuexun.siming.host=http://*************:81/auth/api/v1.0/
dongni.third.yuexun.siming.host-only=http://*************:81/
dongni.third.yuexun.siming.app-key=729276162682880
dongni.third.yuexun.siming.app-secret=13e7b3cf53f747889f19b24b573d723f
dongni.third.yuexun.siming.redirect-uri=http://*************:81

# \u7269\u601D\u6D4B\u8BD5\u73AF\u5883\u5355\u70B9\u767B\u5F55
dongni.third.wusi.host=https://edu.topeti.com/
dongni.third.wusi.app-id=WDxpu1fl
dongni.third.wusi.app-secret=416966af6202df59a69107efe39093cd0a63a100
dongni.third.wusi.redirect-uri=https://www.dongni.com

# i\u6559\u80B2\u5355\u70B9\u767B\u5F55
dongni.third.iedu.host=https://buss.ixiamen.org.cn/
dongni.third.iedu.api.prefix=https://buss.ixiamen.org.cn/oauthapi/prod-api/
dongni.third.iedu.appid=xmcds40a6c04b3019925593716e115c848fd7
dongni.third.iedu.appkey=133bd0303e3a0f2a297b3751d10c058c
dongni.third.iedu.client-id=iedu_dong_ni
dongni.third.iedu.client-key=93d2ae8f4f1946ee870e68ffc3525f12
dongni.third.iedu.redirect-url=https://dalao.dongni100.com/api/base/data/system/auth/ixiamen/callback
dongni.third.iedu.h5.client-id=iedu_dong_ni_h5
dongni.third.iedu.h5.client-key=66740c6d1a69470ab965906e3cd831e5
dongni.third.iedu.h5.redirect-url=https://mdev.dongni100.com/api/base/data/system/auth/ixiamen/mobile/callback

# \u5B66\u79D1\u7F51\u5F00\u653E\u5E73\u53F0 \u63A8\u9898\u529F\u80FD \u6240\u6709\u5B50\u7CFB\u7EDF\u8981\u8BBF\u95EE\u7684\u4E3B\u7CFB\u7EDF\u4FE1\u606F
dongni.third.xkw.xop.env-name=dongni
dongni.third.xkw.xop.main-host=https://dalao.dongni100.com/api
# \u5B66\u79D1\u7F51\u5F00\u653E\u5E73\u53F0 \u63A8\u9898\u529F\u80FD \u6CA1\u6709\u6D4B\u8BD5\u8D26\u53F7
#dongni.third.xkw.xop.gateway-host=https://openapi.xkw.com
#dongni.third.xkw.xop.app-id=101661658223907700
#dongni.third.xkw.xop.secret=B9TMeqTwNuo0fptst4h4xXMUANk3IGuP

# \u6CE8\u91CA

# \u61C2\u4F60\u5FAE\u4FE1\u7B2C\u4E09\u65B9\u5E73\u53F0
dongni.wechat.open.componentAppId=wx2347edae55bfeadc
dongni.wechat.open.componentSecret=0a3bb83a20e4fee06bb60a9c28c3efe6
dongni.wechat.open.componentToken=nzy2015
dongni.wechat.open.componentAesKey=RYoTkcUSR1iDEC83SjIRefB64fkwwBp8SwhWY9ZutC8
dongni.wechat.xmwz.appId=wxd4e90477f1ce6e62
dongni.wechat.xmwz.templateId=bpSsN-219ODS301zv-Zq5DYGSuujHwNg-Hjt-bZoJxU

# word\u8F6Cpdf \u8BF4\u660E\u89C1application.properties\u6216WordToPdfService
dongni.word-to-pdf.env-name=dongni
#dongni.word-to-pdf.main-host=https://www.dongni100.com/api
dongni.word-to-pdf.render-url=http://**************:8291/wordToPdf

# \u9F13\u6559\u901A
gu_jiao_tong.host=http://*************:9501

# WusanInside2AccountService#wusanInside2BizCode
dongni.tiku.third.wusan.inside2.biz-code=100008
# WusanInside2Client#wusanInside2Host
dongni.tiku.third.wusan.inside2.host=https://open-test.53inside.com/api
# WusanInside2ApiTq#wusanInside2RequestToken
dongni.tiku.third.wusan.inside2.request-token=5hbggSo0YZzOQx3ByGjFXB6S4mLaLFHy2GR5XICvrg5A6udjnqKiYIHTadFBEvhW66vdN1rxax6yeSqSVHDcC1CkJxIfjO2Ua4QwHa5JFU6T
# WusanInside2IframeService#wusanInside2IframeHost
dongni.tiku.third.wusan.inside2.iframe-host=https://school-test.53inside.com
