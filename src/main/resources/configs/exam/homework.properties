# çº¿ä¸ä½ä¸æ¨¡å,ç¨æ¥æ¶é´æªæ­¢å¼ºå¶æäº¤|è¯å
homeworkSubmitQueue=HP:submit:queue
# ä½ä¸æéæäº¤éå¶æ¬¡æ°
remindSubmit=HP:remind:${examPaperId}:${classId}
# ä½ä¸å¾å
homeworkTodoQueue=HP:todo:queue
# ä½ä¸æªæ­¢æäº¤æ è®°
endSubmit=HP:end:${examPaperId}:${classId}


# ä½ä¸ä½ç­æ¨¡å 2020å¹´3æ6æ¥

# set ä½ä¸ç­çº§çæªæäº¤studentId
hkStudentUnSubmit=HP:ERI:${cacheId}:student:unSubmit
# set ä½ä¸ç­çº§çå·²æäº¤studentId
hkStudentSubmit=HP:ERI:${cacheId}:student:submit

# set å­¦çä½ç­itemId
hkStudentItem=HP:ERI:${cacheId}:student:${studentId}:item

# set itemæç»ï¼item ç¨æ¥ä½ç­ãéåçåºæ¬ä¿¡æ¯
hkItem=HP:ERI:${cacheId}:item:${examItemId}

# map è¯é¢åè¡¨ä¿¡æ¯ï¼è¯é¢çåºæ¬ä¿¡æ¯
hkQuestionTrace=HP:ERI:${cacheId}:qn:${questionNumber}:trace

# map ä½ä¸ç­çº§ä¿¡æ¯ï¼è¯å·IDãç­çº§ä¿¡æ¯ãç»æ­¢æäº¤ãç»æ­¢éå·ãéå·ç»æ
hkTrace=HP:ERI:${cacheId}:trace
# set ä½ä¸ç­çº§å·²ä½ç­çitemIdï¼æ¹ä¾¿è·åå­¦çç
hkSubmitItemDone=HP:ERI:${cacheId}:item:submit
# zset åå²æ¹æ¹è®°å½,ä¼è®°å½å­¦çä»¥ååæ°
hkHistoryRecord=HP:ERI:${cacheId}:qn:${questionNumber}:history:record
# list æ¹æ¹è®°å½ï¼åªè®°å½ID
hkRecord=HP:ERI:${cacheId}:qn:${questionNumber}:record

# set ä½ä¸æ¸çéåï¼æ¯å¤©å®æ¶ä»è¿éè·å cacheId æ¸ç
hkDone=HP:ERI:cacheId:clean

# ä½ä¸æç»©æ¨é    message:wechat:homework:result:system:${examId}
homeworkResultPush=MSG:WECHAT:HW:RS:SYS:${examId}:${schoolId}

#çæ³æç»©æ¨é
changxiangScorePush=MSG:CHANGXIANG:HW:RS:SYS:${examId}:${schoolId}

#æ¸çåå²æ°æ®
hkRecordClean=HP:ERI:record:start