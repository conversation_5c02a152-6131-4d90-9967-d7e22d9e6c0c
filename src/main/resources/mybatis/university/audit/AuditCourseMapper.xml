<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="AuditCourseMapper">

    <!-- 插入课程自评信息 -->
    <insert id="insertAuditCourse" parameterType="map">
        INSERT INTO t_audit_course(
        audit_id,
        course_id,
        course_name,
        result_analysis,
        paper_analysis,
        course_comment,
        creator_id,
        creator_name,
        create_date_time
        )VALUES (
        #{auditId},
        #{courseId},
        #{courseName},
        #{resultAnalysis},
        #{paperAnalysis},
        #{courseComment},
        #{userId},
        #{userName},
        #{currentTime}
        )
    </insert>

    <!-- 删除课程自评信息 -->
    <delete id="deleteAuditCourse" parameterType="map">
        DELETE FROM t_audit_course
        WHERE audit_id = #{auditId}
    </delete>

    <!-- 更新课程自评信息 -->
    <update id="updateAuditCourse" parameterType="map">
        UPDATE t_audit_course
        SET
        result_analysis = #{resultAnalysis},
        paper_analysis = #{paperAnalysis},
        course_comment = #{courseComment}
        WHERE audit_course_id = #{auditCourseId}
    </update>

    <!-- 查询课程自评信息 -->
    <select id="getAuditCourse" parameterType="map" resultType="map">
        SELECT
        audit_course_id auditCourseId,
        audit_id auditId,
        course_id courseId,
        course_name courseName,
        result_analysis resultAnalysis,
        paper_analysis paperAnalysis,
        course_comment courseComment
        FROM t_audit_course
        WHERE audit_id = #{auditId}
    </select>

</mapper>