<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="StudyGuideWeeklyReportMapper">

  <select id="getWeeklyReportCountByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideWeeklyReportQuery"
    resultType="int">
    select count(1)
    from t_homework_weekly_report thwr
    inner join t_homework_weekly_report_student thwrs on thwr.homework_weekly_report_id = thwrs.homework_weekly_report_id
    where thwrs.student_id = #{studentId}
      and thwrs.status = 1
  </select>

  <select id="getWeeklyReportListByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideWeeklyReportQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.HomeworkWeeklyReportDTO">
    select
        thwr.homework_weekly_report_id homeworkWeeklyReportId,
        thwr.epoch_week epochWeek,
        unix_timestamp(thwr.start_date) * 1000 startDate,
        unix_timestamp(thwr.end_date) * 1000 endDate
    from t_homework_weekly_report thwr
    inner join t_homework_weekly_report_student thwrs on thwr.homework_weekly_report_id = thwrs.homework_weekly_report_id
    where thwrs.student_id = #{studentId}
      and thwrs.status = 1
    order by thwr.epoch_week desc
    <if test="pageSize != null and currentIndex != null">
      limit #{currentIndex, jdbcType=INTEGER}, #{pageSize, jdbcType=INTEGER}
    </if>
  </select>

  <select id="getWeeklyReportByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideWeeklyReportQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.HomeworkWeeklyReportDTO">
    select
        thwr.homework_weekly_report_id homeworkWeeklyReportId,
        thwr.epoch_week epochWeek,
        unix_timestamp(thwr.start_date) * 1000 startDate,
        unix_timestamp(thwr.end_date) * 1000 endDate
    from t_homework_weekly_report thwr
    inner join t_homework_weekly_report_student thwrs on thwr.homework_weekly_report_id = thwrs.homework_weekly_report_id
    where thwr.homework_weekly_report_id = #{homeworkWeeklyReportId}
      and thwrs.student_id = #{studentId}
      and thwrs.status = 1
  </select>

  <select id="getPreviousWeeklyReportByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideWeeklyReportQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.HomeworkWeeklyReportDTO">
    select
      thwr.homework_weekly_report_id homeworkWeeklyReportId,
      thwr.epoch_week epochWeek,
      unix_timestamp(thwr.start_date) * 1000 startDate,
      unix_timestamp(thwr.end_date) * 1000 endDate
    from t_homework_weekly_report thwr
    inner join t_homework_weekly_report_student thwrs on thwr.homework_weekly_report_id = thwrs.homework_weekly_report_id
    where thwr.epoch_week = #{epochWeek} - 1
      and thwrs.student_id = #{studentId}
      and thwrs.status = 1
  </select>

  <select id="getWeeklyReportHomeworkSimpleListByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideWeeklyReportQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.WeeklyReportHomeworkSimple">
    select
        exam_id examId,
        course_id courseId,
        course_name courseName,
        question_count questionCount,
        full_mark fullMark,
        result_status resultStatus,
        student_wrong_question_count studentWrongQuestionCount,
        total_score totalScore
    from t_homework_weekly_report_student thwrs
    inner join t_homework_weekly_report_student_homework thwrsh
        on thwrs.homework_weekly_report_student_id = thwrsh.homework_weekly_report_student_id
    where thwrs.homework_weekly_report_id = #{homeworkWeeklyReportId}
      and thwrs.student_id = #{studentId}
  </select>

  <select id="getWeeklyReportHomeworkDetailListByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideWeeklyReportQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.WeeklyReportHomeworkDetail">
    select
        exam_id examId,
        exam_name examName,
        score_mode scoreMode,
        unix_timestamp(scan_time) * 1000 scanTime,
        course_id courseId,
        course_name courseName,
        question_count questionCount,
        full_mark fullMark,
        result_status resultStatus,
        student_wrong_question_count studentWrongQuestionCount,
        total_score totalScore,
        score_rate scoreRate,
        class_ranking classRanking,
        class_wrong_question_count classWrongQuestionCount,
        class_average_score classAverageScore,
        class_score_rate classScoreRate,
        class_total_student classTotalStudent,
        class_participation_number classParticipationNumber,
        class_absent_number classAbsentNumber,
        class_last_ranking classLastRanking
    from t_homework_weekly_report_student thwrs
    inner join t_homework_weekly_report_student_homework thwrsh
        on thwrs.homework_weekly_report_student_id = thwrsh.homework_weekly_report_student_id
    where thwrs.homework_weekly_report_id = #{homeworkWeeklyReportId}
      and thwrs.student_id = #{studentId}
  </select>

  <select id="getHomeworkWeeklyReportByEpochWeek" parameterType="long"
    resultType="com.dongni.exam.studyguide.bean.dto.HomeworkWeeklyReportIdStatusDTO">
    select
        homework_weekly_report_id homeworkWeeklyReportId,
        status
    from t_homework_weekly_report
    where epoch_week = #{epochWeek}
  </select>

  <insert id="insertHomeworkWeeklyReport" parameterType="map" useGeneratedKeys="true" keyProperty="homeworkWeeklyReportId">
    insert into t_homework_weekly_report (
      epoch_week,
      start_date,
      end_date,
      status,
      creator_id,
      creator_name,
      create_date_time,
      modifier_id,
      modifier_name,
      modify_date_time
    ) values (
      #{epochWeek},
      #{startDate},
      #{endDate},
      0,
      #{userId},
      #{userName},
      now(),
      #{userId},
      #{userName},
      now()
    )
    on duplicate key update
    modifier_id = values(modifier_id),
    modifier_name = values(modifier_name),
    modify_date_time = values(modify_date_time)
  </insert>

  <update id="updateWeeklyReportStatus" parameterType="map">
    update t_homework_weekly_report
    set status = #{status},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = now()
    where homework_weekly_report_id = #{homeworkWeeklyReportId}
  </update>

  <select id="getWeeklyReportStuIdByWeeklyReportIdAndStatus" parameterType="map" resultType="long">
    select student_id
    from t_homework_weekly_report_student
    where homework_weekly_report_id = #{homeworkWeeklyReportId}
      and status = #{status}
  </select>

  <select id="getOneWeeklyReportStuIdByWeeklyReportId" parameterType="long" resultType="long">
    select student_id
    from t_homework_weekly_report_student
    where homework_weekly_report_id = #{homeworkWeeklyReportId}
    limit 1
  </select>

  <insert id="insertHomeworkWeeklyReportStudentList" parameterType="map">
    insert into t_homework_weekly_report_student (
      homework_weekly_report_id,
      student_id,
      student_name,
      status,
      creator_id,
      creator_name,
      create_date_time,
      modifier_id,
      modifier_name,
      modify_date_time
    ) values
    <foreach collection="weeklyReportStudentList" item="item" separator=",">
      (
      #{homeworkWeeklyReportId},
      #{item.studentId},
      #{item.studentName},
      0,
      #{userId},
      #{userName},
      now(),
      #{userId},
      #{userName},
      now()
      )
    </foreach>
    on duplicate key update
    modifier_id = values(modifier_id),
    modifier_name = values(modifier_name),
    modify_date_time = values(modify_date_time)
  </insert>

  <update id="updateWeeklyReportStudentStatus" parameterType="map">
    update t_homework_weekly_report_student
    set status = #{status},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = now()
    where homework_weekly_report_student_id = #{homeworkWeeklyReportStudentId}
  </update>

  <select id="getWeeklyReportStudentId" parameterType="map" resultType="long">
    select homework_weekly_report_student_id
    from t_homework_weekly_report_student
    where homework_weekly_report_id = #{homeworkWeeklyReportId}
      and student_id = #{studentId}
  </select>

  <delete id="deleteWeeklyReportStudent" parameterType="long">
    delete from t_homework_weekly_report_student
    where homework_weekly_report_student_id = #{homeworkWeeklyReportStudentId}
  </delete>

  <delete id="deleteWeeklyReportStudentHomework" parameterType="long">
    delete from t_homework_weekly_report_student_homework
    where homework_weekly_report_student_id = #{homeworkWeeklyReportStudentId}
  </delete>

  <delete id="deleteWeeklyReportStudentKnowledge" parameterType="long">
    delete from t_homework_weekly_report_student_knowledge
    where homework_weekly_report_student_id = #{homeworkWeeklyReportStudentId}
  </delete>

  <insert id="insertHomeworkWeeklyReportStudentHomework" parameterType="map">
    insert into t_homework_weekly_report_student_homework (
      homework_weekly_report_student_id,
      exam_id,
      exam_name,
      score_mode,
      scan_time,
      course_id,
      course_name,
      question_count,
      full_mark,
      result_status,
      student_wrong_question_count,
      total_score,
      score_rate,
      class_ranking,
      class_wrong_question_count,
      class_average_score,
      class_score_rate,
      class_total_student,
      class_participation_number,
      class_absent_number,
      class_last_ranking,
      creator_id,
      creator_name,
      create_date_time,
      modifier_id,
      modifier_name,
      modify_date_time)
      values
    <foreach collection="homeworkList" item="item" separator=",">
      (#{homeworkWeeklyReportStudentId},
      #{item.examId},
      #{item.examName},
      #{item.scoreMode},
      #{item.scanTime},
      #{item.courseId},
      #{item.courseName},
      #{item.questionCount},
      #{item.fullMark},
      #{item.resultStatus},
      #{item.studentWrongQuestionCount},
      #{item.totalScore},
      #{item.scoreRate},
      #{item.classRanking},
      #{item.classWrongQuestionCount},
      #{item.classAverageScore},
      #{item.classScoreRate},
      #{item.classTotalStudent},
      #{item.classParticipationNumber},
      #{item.classAbsentNumber},
      #{item.classLastRanking},
      #{userId},
      #{userName},
      now(),
      #{userId},
      #{userName},
      now()
      )
    </foreach>
  </insert>

  <insert id="insertWeeklyReportStudentKnowledge" parameterType="com.dongni.exam.studyguide.bean.entity.HomeworkWeeklyReportStudentKnowledgeEntity">
    INSERT INTO t_homework_weekly_report_student_knowledge
        (`homework_weekly_report_student_id`, `course_id`, `knowledge_id`, `knowledge_name`, `knowledge_tree_code`, `student_score_rate`,
         `class_score_rate`, `student_wrong_question_count`,
         `creator_id`, `creator_name`, `create_date_time`, `modifier_id`, `modifier_name`, `modify_date_time`)
    VALUES
        <foreach collection="list" item="item" separator=",">
          (#{item.homeworkWeeklyReportStudentId}, #{item.courseId}, #{item.knowledgeId}, #{item.knowledgeName}, #{item.knowledgeTreeCode},
           #{item.studentScoreRate}, #{item.classScoreRate}, #{item.studentWrongQuestionCount},
           #{item.creatorId}, #{item.creatorName}, #{item.createDateTime},
           #{item.modifierId}, #{item.modifierName}, #{item.modifyDateTime})
        </foreach>
  </insert>

  <select id="getStudyGuideWeeklyReportKnowledge" parameterType="com.dongni.exam.studyguide.bean.param.StudyGuideWeeklyReportKnowledgeParam"
          resultType="com.dongni.exam.studyguide.bean.vo.StudyGuideKnowledgeVO">
    SELECT thwrsk.course_id courseId,
           thwrsk.knowledge_id knowledgeId,
           thwrsk.knowledge_name knowledgeName,
           thwrsk.student_score_rate studentScoreRate,
           thwrsk.student_wrong_question_count studentWrongQuestionCount,
           thwrsk.class_score_rate classScoreRate
    FROM t_homework_weekly_report thwr
    INNER JOIN t_homework_weekly_report_student thwrs
        ON thwr.homework_weekly_report_id = thwrs.homework_weekly_report_id
    INNER JOIN t_homework_weekly_report_student_knowledge thwrsk
        ON thwrs.homework_weekly_report_student_id = thwrsk.homework_weekly_report_student_id
    WHERE thwr.homework_weekly_report_id = #{homeworkWeeklyReportId}
        AND thwrs.student_id = #{studentId}
        <if test="courseId != null and courseId != ''">
            AND thwrsk.course_id = #{courseId}
        </if>
    ORDER BY thwrsk.course_id
  </select>
</mapper>
