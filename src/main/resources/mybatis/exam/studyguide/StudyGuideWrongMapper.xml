<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="StudyGuideWrongMapper">
    <select id="getStudyGuideWrongItem" parameterType="com.dongni.exam.studyguide.bean.param.StudyGuideWrongItemGetParam"
            resultType="com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO">
        SELECT study_guide_wrong_item_id studyGuideWrongItemId,
               student_id studentId,
               question_id questionId,
               exam_id examId,
               course_id courseId,
               paper_id paperId,
               class_id classId,
               mark_question_numbers markQuestionNumbers
        FROM t_study_guide_wrong_item
        WHERE student_id = #{studentId}
            AND course_id = #{courseId}
            AND exam_id = #{examId}
    </select>

    <select id="getStudyGuideWrongQuestion" parameterType="com.dongni.exam.studyguide.bean.param.StudyGuideWrongQuestionGetParam"
            resultType="com.dongni.exam.wrong.bean.vo.WrongQuestionItemVO">
        SELECT tsgwi.question_id questionId,
               tqd.difficulty,
               te.exam_id homeworkId,
               te.exam_name homeworkName,
               tec.create_date_time homeworkCreateDateTime,
               tsgwi.study_guide_wrong_item_id studyGuideWrongItemId,
               tsgwi.mark_question_numbers markQuestionNumbers
        FROM t_study_guide_wrong_item tsgwi
        INNER JOIN t_exam te
            ON tsgwi.exam_id = te.exam_id
        INNER JOIN t_exam_class tec
            ON tsgwi.exam_id = tec.exam_id
            AND tsgwi.class_id = tec.class_id
        INNER JOIN t_question_difficulty tqd
            ON tsgwi.question_id = tqd.question_id
        WHERE tsgwi.student_id = #{studentId}
            AND tsgwi.course_id = #{courseId}
            AND tec.create_date_time BETWEEN #{startDate} AND #{endDate}
            <if test="difficultyLeft != null and difficultyRight != null">
                <choose>
                    <when test="difficultyLeft == 0">
                        AND tqd.difficulty >= 0 AND tqd.difficulty &lt;= #{difficultyRight}
                    </when>
                    <otherwise>
                        AND tqd.difficulty > #{difficultyLeft} AND tqd.difficulty &lt;= #{difficultyRight}
                    </otherwise>
                </choose>
            </if>
    </select>

    <select id="getStudyGuideWrongQuestion4Download" parameterType="long"
            resultType="com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDownloadDTO">
        SELECT tsgwi.study_guide_wrong_item_id studyGuideWrongItemId,
               tsgwi.question_id questionId,
               tsgwi.course_id courseId,
               te.exam_id examId,
               te.exam_name examName,
               tec.create_date_time examClassCreateDateTime
        FROM t_study_guide_wrong_item tsgwi
        INNER JOIN t_exam te
            ON tsgwi.exam_id = te.exam_id
        INNER JOIN t_exam_class tec
            ON tsgwi.exam_id = tec.exam_id
            AND tsgwi.class_id = tec.class_id
        WHERE tsgwi.study_guide_wrong_item_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="countStudentGuideWrongGroupByCourse" parameterType="long"
            resultType="com.dongni.exam.studyguide.bean.dto.StudyGuideWrongCourseCountDTO">
        SELECT course_id courseId,
               COUNT(study_guide_wrong_item_id) count
        FROM t_study_guide_wrong_item
        WHERE student_id = #{studentId}
        GROUP BY course_id
    </select>

    <select id="getStudyGuideWrongItemById" parameterType="map" resultType="com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO">
        SELECT study_guide_wrong_item_id studyGuideWrongItemId,
               student_id studentId,
               question_id questionId,
               exam_id examId,
               course_id courseId,
               paper_id paperId,
               class_id classId,
               mark_question_numbers markQuestionNumbers
        FROM t_study_guide_wrong_item
        WHERE student_id = #{studentId}
            AND study_guide_wrong_item_id = #{studyGuideWrongItemId}
    </select>

    <select id="getStudyGuideWrongTagItemById" parameterType="long"
            resultType="com.dongni.exam.studyguide.bean.dto.StudyGuideWrongTagItemDTO">
        SELECT wrong_tag_id wrongTagId,
               tag_name tagName,
               study_guide_wrong_tag_item_id studyGuideWrongTagItemId
        FROM t_study_guide_wrong_tag_item
        WHERE study_guide_wrong_item_id = #{0}
    </select>

    <delete id="deleteTags" parameterType="long">
        DELETE FROM t_study_guide_wrong_tag_item
        WHERE study_guide_wrong_item_id = #{0}
    </delete>

    <insert id="insertTags" parameterType="com.dongni.exam.studyguide.bean.entity.StudyGuideWrongTagItemEntity">
        INSERT INTO t_study_guide_wrong_tag_item
        (study_guide_wrong_item_id, wrong_tag_id, tag_name,
         creator_id, creator_name, create_date_time, modifier_id, modifier_name, modify_date_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.studyGuideWrongItemId}, #{item.wrongTagId}, #{item.tagName},
            #{item.creatorId}, #{item.creatorName}, #{item.createDateTime},
            #{item.modifierId}, #{item.modifierName}, #{item.modifyDateTime})
        </foreach>
    </insert>

    <select id="getStudyGuideWrongQuestionByExamId4Download" parameterType="com.dongni.exam.studyguide.bean.param.StudyGuideWrongQuestionExamDownloadParam"
            resultType="map">
        SELECT tsgwi.study_guide_wrong_item_id studyGuideWrongItemId,
               tsgwi.question_id questionId,
               tsgwi.course_id courseId,
               te.exam_id examId,
               te.exam_name examName,
               tec.create_date_time examClassCreateDateTime
        FROM t_study_guide_wrong_item tsgwi
        INNER JOIN t_exam te
            ON tsgwi.exam_id = te.exam_id
        INNER JOIN t_exam_class tec
            ON tsgwi.exam_id = tec.exam_id
            AND tsgwi.class_id = tec.class_id
        WHERE tsgwi.student_id = #{studentId}
            AND tsgwi.course_id = #{courseId}
            AND tsgwi.exam_id = #{examId}
    </select>
</mapper>