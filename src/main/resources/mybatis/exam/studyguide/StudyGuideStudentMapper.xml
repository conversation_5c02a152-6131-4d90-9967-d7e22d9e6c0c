<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="StudyGuideStudentMapper">

    <select id="getExamCountByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideQuery"
            resultType="int">
        SELECT
            count(*)
        FROM t_exam te
        INNER JOIN t_exam_student tes
            ON te.exam_id = tes.exam_id
            AND tes.student_id = #{studentId}
        INNER JOIN t_exam_class tec
            ON tes.exam_id = tec.exam_id
            AND tes.class_id = tec.class_id
        INNER JOIN t_class_exam_stat tces
            ON tec.exam_id = tces.exam_id
            AND tec.class_id = tces.class_id
            AND tces.stat_status = 1
        WHERE te.exam_type = #{examType}
        ORDER BY tec.create_date_time DESC, te.create_date_time DESC, te.exam_id DESC
    </select>

  <select id="getExamListByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.ExamInfoDTO">
    select
        te.exam_id examId,
        te.exam_name examName,
        unix_timestamp(tec.create_date_time) * 1000 scanTime
    from t_exam te
    inner join t_exam_student tes on te.exam_id = tes.exam_id
    inner join t_exam_class tec on tes.exam_id = tec.exam_id and tes.class_id = tec.class_id
    inner join t_class_exam_stat tces on tec.exam_id = tces.exam_id and tec.class_id = tces.class_id
    where te.exam_type = #{examType}
      and tes.student_id = #{studentId}
      and tces.stat_status = 1
    order by tec.create_date_time desc, te.create_date_time desc, te.exam_id desc
    <if test="pageSize != null and currentIndex != null">
      limit #{currentIndex, jdbcType=INTEGER}, #{pageSize, jdbcType=INTEGER}
    </if>
  </select>

  <select id="getExamByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.ExamInfoDTO">
    select
        te.exam_id examId,
        te.exam_name examName,
        unix_timestamp(tec.create_date_time) * 1000 scanTime
    from t_exam te
    inner join t_exam_student tes on te.exam_id = tes.exam_id
    inner join t_exam_class tec on tes.exam_id = tec.exam_id and tes.class_id = tec.class_id
    inner join t_class_exam_stat tces on tec.exam_id = tces.exam_id and tec.class_id = tces.class_id
    where te.exam_id = #{examId}
      and te.exam_type = #{examType}
      and tes.student_id = #{studentId}
      and tces.stat_status = 1
  </select>

  <select id="getStuStudyGuideListByQuery" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.StuStudyGuideDTO">
    select
        tep.exam_id examId,
        tep.course_id courseId,
        tep.course_name courseName,
        tep.paper_id paperId,
        tep.full_mark fullMark,
        ter.class_id classId,
        ter.result_status resultStatus
    from t_exam_paper tep
    inner join t_exam_result ter on tep.exam_id = ter.exam_id and tep.paper_id = ter.paper_id
    where tep.exam_id in
    <foreach collection="examIds" item="examId" separator="," open="(" close=")">
      #{examId}
    </foreach>
      and ter.student_id = #{studentId}
  </select>

  <select id="getExamListForWeeklyReport" parameterType="com.dongni.exam.studyguide.bean.query.StuStudyGuideQuery"
    resultType="com.dongni.exam.studyguide.bean.dto.ExamInfoStudentIdDTO">
    select
        te.exam_id examId,
        te.exam_name examName,
        tec.create_date_time scanTime,
        tes.student_id studentId
    from t_exam te
    inner join t_exam_student tes on te.exam_id = tes.exam_id
    inner join t_exam_class tec on tes.exam_id = tec.exam_id and tes.class_id = tec.class_id
    inner join t_class_exam_stat tces on tec.exam_id = tces.exam_id and tec.class_id = tces.class_id
    where te.exam_type = #{examType}
    <if test="studentIds != null">
      and tes.student_id in
      <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
        #{studentId}
      </foreach>
    </if>
      and tec.create_date_time >= #{startDate}
      and tec.create_date_time &lt;= #{endDate}
      and tces.stat_status = 1
  </select>

</mapper>
