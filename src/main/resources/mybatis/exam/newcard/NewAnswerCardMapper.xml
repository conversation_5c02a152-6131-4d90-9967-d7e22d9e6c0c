<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="NewAnswerCardMapper">

    <!-- 生成答题卡 -->
    <insert id="insertAnswerCard" parameterType="map" keyProperty="answerCardId" keyColumn="answer_card_id" useGeneratedKeys="true">
        INSERT INTO t_answer_card(
            exam_uploader_id,
            exam_id,
            template_type,
            template_code,
            file_name,
            file_path,
            error_type_code,
            error_type_name,
            answer_card_code,
            error_code,
            answer_card_pair,
            modify_status,
            source,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time,
            batch_id,
            scan_date_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
              #{item.examUploaderId},
              #{item.examId},
              #{item.templateType},
              #{item.templateCode},
              #{item.fileName},
              #{item.filePath},
              #{item.errorTypeCode},
              #{item.errorTypeName},
              #{item.answerCardCode},
              #{item.errorCode},
              #{item.answerCardPair},
              #{item.modifyStatus},
              #{item.source},
              #{item.userId},
              #{item.userName},
              #{item.currentTime},
              #{item.userId},
              #{item.userName},
              #{item.currentTime},
              #{item.batchId},
              #{item.scanDateTime}
            )
        </foreach>
    </insert>

    <!-- 获取第三方绘制需要的答题卡图片 -->
    <select id="selectAnswerCardByTemplate" parameterType="map" resultType="map">
        SELECT
        tac2.answer_card_id   answerCardId,
        tac2.file_name        fileName,
        tac2.file_path        filePath,
        tac2.exam_uploader_id examUploaderId,
        tac2.answer_card_code answerCardCode,
        tac2.answer_card_pair answerCardPair
        FROM t_answer_card tac, t_answer_card tac2
        WHERE tac.exam_uploader_id in <foreach collection="examUploaderIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        <if test="errorTypeCode != null">
            AND tac.error_type_code = #{errorTypeCode}
        </if>
        <if test="batchId != null and batchId != ''">
            AND tac.batch_id = #{batchId}
        </if>
        <if test="errorCodes != null">
            <foreach collection="errorCodes" item="item" open=" AND (" separator=" OR " close=")">
               ( tac.error_code >> #{item} &amp; 1 =1 )
            </foreach>
        </if>
        and tac2.exam_uploader_id = tac.exam_uploader_id and tac2.answer_card_code = tac.answer_card_code
        group by tac2.exam_uploader_id, tac2.batch_id, tac2.file_name
        ORDER BY tac2.exam_uploader_id, tac2.batch_id, tac2.file_name
        limit 0, #{limitSize}
    </select>

    <!-- 根据异常类型查询异常答题卡数量 -->
    <select id="selectAnswerCardErrorCount" parameterType="map" resultType="int">
        SELECT COUNT(distinct tac.answer_card_code)
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        <choose>
            <when test="errorTypeCodeList != null">
                AND (tac.error_type_code IN
                <foreach item="item" index="index" collection="errorTypeCodeList" open="(" separator="," close=")">
                    #{item.code}
                </foreach>
                OR tac.error_type_code IN
                <foreach item="item" index="index" collection="errorTypeCodeList" open="(" separator="," close=")">
                    #{item.code} + #{item.subjectError}
                </foreach>
                )
            </when>
            <otherwise>
                AND tac.error_type_code != 0
            </otherwise>
        </choose>
        AND ((tac.task_id is not null and tac.task_status = 2)
        OR tac.source = 0)
    </select>

    <!-- 根据异常类型查询异常答题卡数量 -->
    <select id="getAnswerCardCode" parameterType="map" resultType="map">
        SELECT
        /*+NO_GROUP_INDEX*/
        answer_card_code answerCardCode,
        answer_card_pair answerCardPair
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        <choose>
            <when test="errorTypeCodeList != null">
                AND (tac.error_type_code IN
                <foreach item="item" index="index" collection="errorTypeCodeList" open="(" separator="," close=")">
                    #{item.code}
                </foreach>
                OR tac.error_type_code IN
                <foreach item="item" index="index" collection="errorTypeCodeList" open="(" separator="," close=")">
                    #{item.code} + #{item.subjectError}
                </foreach>
                )
            </when>
            <otherwise>
                AND tac.error_type_code != 0
            </otherwise>
        </choose>
        AND ((tac.task_id is not null and tac.task_status = 2)
        OR tac.source = 0)
        group by answer_card_code
        order by answer_card_code
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex},#{pageSize}
        </if>
    </select>

    <!-- 查询异常答题卡 -->
    <select id="selectAnswerCardError" parameterType="map" resultType="map">
        SELECT
        tac.answer_card_id answerCardId,
        tac.exam_id examId,
        tac.exam_uploader_id examUploaderId,
        tac.file_name fileName,
        tac.file_path filePath,
        tac.recognition_result recognitionResult,
        tac.recognition_result_info recognitionResultInfo,
        tac.page_number pageNumber,
        tac.answer_card_code answerCardCode,
        tac.answer_card_pair answerCardPair,
        tac.error_type_code errorTypeCode,
        tac.error_type_name errorTypeName,
        tac.student_num studentNum,
        tac.student_exam_num studentExamNum,
        tac.correct_status correctStatus,
        tac.correct_param correctParam,
        tac.correct_path correctPath,
        tes.class_name className,
        tes.student_name studentName
        FROM t_answer_card tac
        LEFT JOIN t_exam_student tes ON tac.exam_id = tes.exam_id AND tac.student_id = tes.student_id
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.answer_card_code in
        <foreach collection="answerCardCodeList" item="item" separator="," close=")" open="(" >
            #{item.answerCardCode}
        </foreach>
    </select>

    <!-- 查询答题卡 -->
    <select id="selectAnswerCardByTaskId" parameterType="map" resultType="map">
        SELECT
        tac.answer_card_id answerCardId,
        tac.exam_id examId,
        tac.exam_uploader_id examUploaderId,
        tac.file_name fileName,
        tac.file_path filePath,
        tac.recognition_result recognitionResult,
        tac.recognition_result_info recognitionResultInfo,
        tac.page_number pageNumber,
        tac.answer_card_code answerCardCode,
        tac.answer_card_pair answerCardPair,
        tac.error_type_code errorTypeCode,
        tac.error_type_name errorTypeName,
        if(error_code >> 4 &amp; 1 = 1, '科目异常', '') subjectError,
        if(error_code >> 3 &amp; 1 = 1, '考号异常', '') studentExamNumError,
        if(error_code >> 6 &amp; 1 = 1, '缺考异常', '') absentError,
        if(error_code >> 7 &amp; 1 = 1, '定位点异常', '') positionError,
        tac.student_id studentId,
        tac.student_num studentNum,
        tac.student_exam_num studentExamNum,
        tac.correct_status correctStatus,
        tac.correct_param correctParam,
        tac.correct_path correctPath,
        tac.error_code <![CDATA[&]]> b'00000000000000000000000010110010' = 0   showCorrectImage
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.task_id = #{taskId}
    </select>

    <!-- 更新答题卡路径 -->
    <update id="updateAnswerCardFilePath" parameterType="java.util.Map">
        UPDATE t_answer_card
        SET
            file_path = #{filePath},
            correct_status = #{correctStatus},
            correct_path = #{correctPath},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE answer_card_id = #{answerCardId}
    </update>

    <select id="getAnswerCardScanCount" parameterType="map" resultType="int">
        select count(1) from t_answer_card as tac
        where tac.exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId != ''">
            and tac.batch_id = #{batchId}
        </if>
    </select>

    <!--根据examUploaderId获取answerCard信息-->
    <select id="getAnswerCardByExamUploaderId" parameterType="map" resultType="map">
        SELECT
            answer_card_id        answerCardId,
            file_path             filePath
        FROM t_answer_card
        WHERE exam_uploader_id = #{examUploaderId}
    </select>

    <select id="findStudentNumberList" parameterType="map" resultType="map">
        SELECT
        ter.exam_result_id   examResultId,
        ter.student_id       studentId,
        ter.student_num      studentNum,
        ter.student_name     studentName,
        ter.class_id         classId,
        ter.class_name       className,
        ter.school_id        schoolId
        FROM t_exam_result ter
        WHERE ter.exam_id = #{examId}
        AND ter.school_id = #{schoolId}
        AND ter.paper_id = #{paperId}
        <if test="classIds != null and classIds.size() > 0">
            AND ter.class_id IN
            <foreach item="item" collection="classIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY ter.student_id
    </select>

    <update id="updateAnswerCardWithTemplateCode" parameterType="map">
        UPDATE t_answer_card tac
            SET tac.template_code = #{templateCode},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE tac.exam_uploader_id = #{examUploaderId}
    </update>

    <select id="getAnswerCards" parameterType="map" resultType="map">
        SELECT
            tac.answer_card_id          answerCardId,
            tac.task_id                 taskId,
            tac.task_status             taskStatus,
            tac.file_path               filePath,
            tac.template_code           templateCode,
            tac.student_id              studentId
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId != ''">
            and tac.batch_id = #{batchId}
        </if>
        and tac.answer_card_code != 0
        ORDER BY tac.answer_card_code
    </select>


    <select id="getAnswerCardTemplateCodeInfo" parameterType="map" resultType="map">
        SELECT tac.template_code          templateCode,
               COUNT(distinct student_id) studentCount
        FROM t_answer_card tac
        WHERE tac.student_id != 0
  AND tac.exam_uploader_id in
        <foreach collection="examUploaderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY tac.template_code
    </select>

    <select id="getAllAnswerCardTemplateCodeInfo" parameterType="map" resultType="map">
        SELECT tac.template_code          templateCode,
        COUNT(distinct student_id) studentCount,
        max(student_id) studentId
        FROM t_answer_card tac
        WHERE  tac.exam_uploader_id in
        <foreach collection="examUploaderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY tac.template_code
    </select>

    <update id="updateAnswerCardWithTask" parameterType="map">
        UPDATE t_answer_card tac
            SET tac.task_id = #{taskId},
                tac.task_status =  #{taskStatus}
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.answer_card_id IN
        <foreach collection="answerCardIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateAnswerCardWithTemplateTask" parameterType="map">
        UPDATE t_answer_card tac
        SET tac.task_id = #{taskId},
        tac.task_status =  #{taskStatus},
        tac.error_type_code = 1,
        tac.student_id = 0,
        tac.student_num = '0',
        tac.student_exam_num = '0',
        tac.class_id = 0,
        tac.modify_status = 1,
        tac.error_code = 0
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.answer_card_id IN
        <foreach collection="answerCardIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--获取还没进行识别完成的答题卡-->
    <select id="queryUnRecognizeAnswerCards" parameterType="map"  resultType="map">
       SELECT
        tac.answer_card_id answerCardId
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.modify_status = 1
        AND tac.source = 1
        <choose>
            <when test="uploadStatus != null and uploadStatus == 5">
                AND (tac.task_id is null OR (tac.task_id is not null AND tac.task_status = 1))
            </when>
            <otherwise>
                AND (tac.task_id is not null AND tac.task_status = 1)
            </otherwise>
        </choose>
    </select>

    <select id="queryUnAssignAnswerCard" parameterType="map"  resultType="map">
        SELECT
        tac.answer_card_id answerCardId
        FROM t_answer_card tac
        <if test="deviceId != null">
            inner join t_upload_batch tub on tac.exam_uploader_id = tub.exam_uploader_id and tac.batch_id = tub.batch_id
        </if>
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.modify_status = 1
        AND tac.source = 1
        AND tac.task_id is null
        <if test="deviceId != null">
            AND tub.device_id = #{deviceId}
        </if>
    </select>

    <select id="queryAnswerCards" parameterType="map" resultType="map">
        SELECT
        tac.answer_card_id answerCardId,
        tac.task_status taskStatus,
        tac.modify_status modifyStatus,
        tac.student_id studentId
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        <if test="taskId != null and taskId != ''">
            AND tac.task_id = #{taskId}
        </if>
        <if test="modifyStatus != null and modifyStatus != ''">
            AND tac.modify_status = #{modifyStatus}
        </if>
    </select>

    <update id="clearTasks" parameterType="map">
        UPDATE t_answer_card tac
        SET tac.task_id = null,
            tac.task_status = 0,
            tac.modify_status = 1,
            tac.student_id = 0,
            tac.student_num = '0',
            tac.student_exam_num = '0',
            tac.class_id = 0
        WHERE tac.exam_uploader_id = #{examUploaderId}
    </update>

    <!--获取正常识别的答题卡-->
    <select id="getRecognizedCount" parameterType="map" resultType="map">
        SELECT
            count(IF(tac.task_id is not null and tac.task_status in (2, 3), 1, NULL))  recognitionCount,
            count(IF(tac.modify_status != 0, 1, NULL))                                 noCompleteCount
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId != ''">
            and tac.batch_id = #{batchId}
        </if>
    </select>

    <!--获取本地调试需要的本场考试的参数-->
    <select id="getRecognizeParams" parameterType="map" resultType="map">
        SELECT s.template_code,s.exam_uploader_id,ss.paper_id,COUNT(1) answerCardCount
        FROM t_answer_card s, t_exam_uploader ss
        WHERE  s.exam_uploader_id = #{examUploaderId}
          AND s.exam_uploader_id=ss.exam_uploader_id
        GROUP BY s.template_code,s.exam_uploader_id
    </select>

    <!--获取本地调试需要的本场考试的参数-->
    <select id="getRecognizeParams2" parameterType="map" resultType="map">
        SELECT s.template_code,s.exam_uploader_id,ss.paper_id,COUNT(1) answerCardCount
        FROM t_answer_card s, t_exam_uploader ss
        WHERE  ss.exam_id = #{examId} and ss.paper_id = #{paperId} and ss.school_id =  #{schoolId}
          AND s.exam_uploader_id=ss.exam_uploader_id
        GROUP BY s.template_code,s.exam_uploader_id
    </select>
    <!--获取答题卡来源数量-->
    <select id="getAnswerCardSource" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.source = #{source}
    </select>

    <select id="getOne" parameterType="map" resultType="map">
        SELECT
        tac.answer_card_id answerCardId,
        tac.task_id taskId,
        tac.task_status taskStatus,
        tac.file_path filePath,
        tac.template_code templateCode,
        tac.correct_path correctPath
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        ORDER BY tac.file_name DESC
        LIMIT #{startIndex}, #{endIndex}
    </select>

    <select id="getAnswerCardCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
    </select>

    <update id="updateTemplateType" parameterType="map">
        UPDATE t_exam_uploader
        SET template_type = #{templateType}
        WHERE exam_uploader_id = #{examUploaderId}
    </update>

    <update id="updateTemplateCode" parameterType="map">
        UPDATE t_answer_card
        SET template_code = #{templateCode},
            template_type = #{templateType}
        WHERE exam_uploader_id = #{examUploaderId} AND answer_card_id in
        <foreach collection="answerCardIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectCurrentExamUploader" parameterType="map" resultType="map">
        SELECT
        exam_uploader_id examUploaderId,
        exam_id examId,
        uploader_id uploaderId
        FROM t_exam_uploader teu
        WHERE teu.exam_id = #{examId}
        AND teu.paper_id = #{paperId}
        AND teu.school_id = #{schoolId}
        ORDER BY teu.upload_status ASC
        LIMIT 1
    </select>

    <select id="selectExamUploaders" parameterType="map" resultType="map">
        SELECT
        exam_uploader_id        examUploaderId,
        exam_id                 examId,
        uploader_id             uploaderId,
        upload_status           uploadStatus
        FROM t_exam_uploader teu
        WHERE teu.exam_id = #{examId}
        AND teu.paper_id = #{paperId}
        AND teu.school_id = #{schoolId}
        ORDER BY teu.upload_status ASC
    </select>

    <select id="selectExamUploadersByTemplateCode" parameterType="map" resultType="map">
        SELECT
            exam_uploader_id        examUploaderId
        FROM t_exam_uploader teu
        WHERE teu.exam_id = #{examId}
          AND teu.paper_id = #{paperId}
          AND teu.default_template_code = #{templateCode}
          AND teu.upload_status != 7
        ORDER BY teu.upload_status ASC
    </select>

    <select id="queryExamPaperInfo" parameterType="map" resultType="map">
        SELECT
            exam_paper_id examPaperId
        FROM t_exam_paper tep
        INNER JOIN (
            SELECT exam_id, paper_id FROM t_exam_uploader WHERE exam_uploader_id = #{examUploaderId}
        )  AS teu ON tep.paper_id = teu.paper_id AND tep.exam_id = teu.exam_id
    </select>

    <select id="selectWholeAnswerCards" parameterType="map" resultType="map">
        SELECT
            answer_card_id answerCardId
        FROM t_answer_card
        WHERE exam_uploader_id = #{examUploaderId}
    </select>

    <select id="getExamLatestUploader" parameterType="map" resultType="map">
        SELECT
            exam_uploader_id examUploaderId,
            exam_id examId,
            upload_status uploadStatus
        FROM t_exam_uploader
        WHERE exam_id = #{examId}
        ORDER BY exam_uploader_id desc
        LIMIT 1
    </select>

    <!--根据examUploaderId获取答题卡 -->
    <select id="getFilePathByExamUploaderId" parameterType="map" resultType="string">
        SELECT file_path
        FROM t_answer_card
        WHERE exam_uploader_id = #{examUploaderId}
    </select>

    <update id="updateErrorType" parameterType="map">
        UPDATE t_answer_card
        SET error_type_code = #{errorTypeCode},
            error_type_name = #{errorTypeName},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        WHERE exam_uploader_id = #{examUploaderId}
            AND error_type_code in (2, 3, 5)
    </update>

    <select id="getTemplateAnswerCards" parameterType="map" resultType="map">
        SELECT
            tac.task_id taskId,
            tac.task_status taskStatus
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId} and tac.task_id like '%:t%';
    </select>


    <!-- 更新答题卡上传人状态 -->
    <update id="updateExamUploaderClientUploadStatus" parameterType="map">
        UPDATE t_exam_uploader
        SET client_upload_status = #{clientUploadStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE exam_uploader_id = #{examUploaderId}
    </update>

    <!-- 更新答题卡上传人状态 -->
    <update id="resetExamUploaderTemplateCode" parameterType="map">
        UPDATE t_exam_uploader
        SET default_template_code = null,
            template_type = 0,
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE exam_uploader_id  in
        <foreach collection="examUploaderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 更新答题卡上传班级状态 -->
    <update id="updateExamUploaderClassClientUploadStatus" parameterType="map">
        UPDATE t_exam_uploader_class
        SET client_upload_status = #{clientUploadStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE exam_uploader_id = #{examUploaderId}
    </update>

    <select id="selectClientUploadStatus" parameterType="map" resultType="map">
        SELECT
            client_upload_status clientUploadStatus,
            exam_uploader_id     examUploaderId
        FROM t_exam_uploader
        WHERE exam_uploader_id in
        <foreach collection="examUploaderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getSmallNormalTaskRecognitionCount" parameterType="map" resultType="int">
        SELECT count(1) count
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.task_id is not null
        AND tac.task_id not like '%:t%' AND task_status = 2
    </select>

    <select id="queryAnswerCardByCode" parameterType="map" resultType="map">
        SELECT
            answer_card_code code,
            count(1) c
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.answer_card_code in
        <foreach collection="codes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY tac.answer_card_code
    </select>

    <select id="getSubjectErrorCardCount" parameterType="map" resultType="int">
        SELECT
            count(1)
        FROM t_answer_card t
        WHERE t.error_type_code >= #{errorTypeCode}
        and t.exam_uploader_id=${examUploaderId}
    </select>

    <select id="getSubjectErrorCardDisCount" parameterType="map" resultType="int">
        SELECT
            COUNT(distinct(t.answer_card_code))
        FROM t_answer_card t
        WHERE t.error_type_code >= #{errorTypeCode}
          and t.exam_uploader_id= #{examUploaderId}
    </select>

    <select id="getSubjectErrorCards" parameterType="map" resultType="map">
        SELECT
            answer_card_id answerCardId,
            exam_uploader_id examUploaderId,
            exam_id examId,
            class_id classId,
            student_id studentId,
            student_num studentNum,
            template_type templateType,
            template_code templateCode,
            file_name fileName,
            file_path filePath,
            recognition_result recognitionResult,
            recognition_result_info recognitionResultInfo,
            page_number pageNumber,
            error_type_code errorTypeCode,
            error_type_name errorTypeName,
            answer_card_code answerCardCode,
            mark_point_json markPointJson,
            correct_param correctParam ,
            correct_status correctStatus,
            correct_path correctPath,
            score_path scorePath,
            modify_status modifyStatus,
            task_id taskId ,
            task_status taskStatus,
            creator_id creatorId,
            creator_name creatorName ,
            create_date_time createDateTime,
            modifier_id modifierId,
            modifier_name modifierName,
            modify_date_time modifyDateTime
        FROM t_answer_card t
        WHERE t.exam_uploader_id = #{examUploaderId}
        AND t.error_type_code >= #{errorTypeCode}
        <if test="answerCardCode != null and answerCardCode != ''">
            AND t.answer_card_code = #{answerCardCode}
        </if>

    </select>

    <delete id="deleteSubjectErrorCads" parameterType="map">
        DELETE FROM t_answer_card
        WHERE exam_uploader_id = #{examUploaderId}
        AND error_type_code >= #{errorTypeCode}
    </delete>

    <insert id="updateErrorTypeCode" parameterType="map">
        INSERT INTO  t_answer_card (
            answer_card_id,
            exam_uploader_id,
            exam_id,
            class_id,
            student_id,
            student_num,
            template_type,
            template_code,
            file_name,
            file_path,
            recognition_result,
            recognition_result_info,
            page_number,
            error_type_code,
            error_type_name,
            answer_card_code,
            mark_point_json,
            correct_param,
            correct_status,
            correct_path,
            score_path,
            modify_status,
            task_id,
            task_status,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.answerCardId},
                #{item.examUploaderId},
                #{item.examId},
                #{item.classId},
                #{item.studentId},
                #{item.studentNum},
                #{item.templateType},
                #{item.templateCode},
                #{item.fileName},
                #{item.filePath},
                #{item.recognitionResult},
                #{item.recognitionResultInfo},
                #{item.pageNumber},
                #{item.errorTypeCode},
                #{item.errorTypeName},
                #{item.answerCardCode},
                #{item.markPointjson},
                #{item.correctParam},
                #{item.correctStatus},
                #{item.correctPath},
                #{item.scorePath},
                #{item.modifyStatus},
                #{item.taskId},
                #{item.taskStatus},
                #{item.creatorId},
                #{item.creatorName},
                #{item.createDateTime},
                #{item.modifierId},
                #{item.modifierName},
                #{item.modifyDateTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        error_type_code=VALUES(error_type_code),
        error_type_name=VALUES(error_type_name)
    </insert>

    <select id="getSubjectErrorCode" parameterType="map" resultType="map">
        SELECT
        /*+NO_GROUP_INDEX*/
        answer_card_code answerCardCode,
        answer_card_pair answerCardPair
        FROM t_answer_card tac
        WHERE tac.exam_uploader_id = #{examUploaderId}
        AND tac.error_type_code >= #{errorTypeCode}
        AND ((tac.task_id is not null and tac.task_status = 2)
        OR tac.source = 0)
        group by answer_card_code
        order by answer_card_code
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex},#{pageSize}
        </if>
    </select>

    <update id="batchUpdateExamResultStatus" parameterType="map">
        UPDATE t_exam_result ter
        SET result_status = #{resultStatus}
        WHERE ter.exam_id = #{examId}
        AND ter.school_id = #{schoolId}
        AND ter.paper_id = #{paperId}
        AND ter.student_id IN
        <foreach collection="studentIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>


    <select id="queryLikelyBlanks" parameterType="map" resultType="int">
        SELECT count(DISTINCT(answer_card_code))
        FROM t_answer_card tac
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id
         INNER JOIN t_exam_result ter on ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id and ter.student_id = tac.student_id
        where ( tac.error_code >> 18 &amp; 1 = 1 )
        AND tac.exam_uploader_id IN
        <foreach collection="examUploaderIds" item="examUploaderId" separator="," open="(" close=")">
            #{examUploaderId}
        </foreach>
        AND
        tac.modify_status = 0
        and ter.result_status = 0
    </select>

    <select id="queryAnswerCardByBatch" parameterType="map" resultType="map">
        select
        tac.answer_card_id answerCardId
        from t_answer_card tac
        where tac.exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId != ''">
            and tac.batch_id = #{batchId}
        </if>
        <if test="exceptionValue != null">
            and (tac.error_code &amp; #{exceptionValue}) > 0
        </if>

    </select>

    <select id="queryDetailLikelyBlanks" parameterType="map" resultType="map">
        SELECT distinct tac.student_id studentId
        FROM t_answer_card tac
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id
        INNER JOIN t_exam_result ter on ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id and ter.student_id = tac.student_id
        where ( tac.error_code >> 18 &amp; 1 = 1 ) AND
        tac.exam_uploader_id IN
        <foreach collection="examUploaderIds" item="examUploaderId" separator="," open="(" close=")">
            #{examUploaderId}
        </foreach>
        AND
        tac.modify_status = 0
        and ter.result_status = 0
        order by tac.student_id
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <update id="updateCheckTemplateStatus" parameterType="map">
        update t_exam_uploader
            set check_template_status = #{checkTemplateStatus}
        where exam_uploader_id = #{examUploaderId}
    </update>

    <select id="queryLowConfident" parameterType="map" resultType="int">
        SELECT count(DISTINCT(answer_card_code))
        FROM t_answer_card tac
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id
        INNER JOIN t_exam_result ter on ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id and ter.student_id = tac.student_id
        where ( tac.error_code  >> 20 &amp; 1 = 1 ) AND
        tac.modify_status = 0 AND
        tac.exam_uploader_id  IN
        <foreach collection="examUploaderIds" item="examUploaderId" separator="," open="(" close=")">
            #{examUploaderId}
        </foreach>
         and
        ter.result_status = 0
    </select>

    <select id="queryDetailLowConfident" parameterType="map" resultType="map">
        SELECT
            tac.student_id studentId,
            tac.low_confident_info lowConfidentInfo
        FROM t_answer_card tac
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id
        INNER JOIN t_exam_result ter on ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id and ter.student_id = tac.student_id
        where ( tac.error_code  >> 20 &amp; 1 = 1 ) AND
        tac.exam_uploader_id IN
        <foreach collection="examUploaderIds" item="examUploaderId" separator="," open="(" close=")">
            #{examUploaderId}
        </foreach>
        and
        tac.modify_status = 0 AND ter.result_status = 0
        <if test="studentId != null and studentId != ''">
            and tac.student_id = #{studentId}
        </if>
        order by tac.student_id
    </select>

    <select id="queryDetailLowConfidentNew" parameterType="map" resultType="map">
        SELECT
        tac.student_id studentId,
        tac.low_confident_info lowConfidentInfo
        FROM t_answer_card tac
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id
        INNER JOIN t_exam_result ter on ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id and ter.student_id = tac.student_id
        where ( tac.error_code  >> 20 &amp; 1 = 1 ) AND
        tac.exam_uploader_id IN
        <foreach collection="examUploaderIds" item="examUploaderId" separator="," open="(" close=")">
            #{examUploaderId}
        </foreach> AND
        tac.modify_status = 0 and ter.result_status = 0
        <if test="studentId != null and studentId != ''">
            and tac.student_id = #{studentId}
        </if>
        order by tac.student_id
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryLowConfidents" parameterType="map" resultType="map">
        select
            tac.exam_uploader_id examUploaderId,
            tac.answer_card_id answerCardId,
            tac.student_id studentId,
            tac.low_confident_info lowConfidentInfo,
            (tac.error_code >> 20 &amp; 1) lowConfident
        from  t_answer_card tac
        inner join t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id
        where teu.exam_id = #{examId} and teu.paper_id = #{paperId} and teu.school_id = #{schoolId}
        and tac.student_id = #{studentId}
    </select>
    
    <update id="updateAnswerCardLowConfidentInfo" parameterType="map">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
        UPDATE t_answer_card  SET
            <if test="allLowConfidentItemUpdated != null">
                error_code = error_code &amp; ~(1 &lt;&lt; 20 ) ,
            </if>
            low_confident_info= #{item.lowConfidentInfo}
        where answer_card_id = #{item.answerCardId}
        </foreach>
    </update>

    <select id="scanningToBeAllocatedCount" resultType="int" parameterType="map">
        SELECT COUNT(1) FROM (
                 SELECT
                        t.exam_uploader_id
                 FROM t_answer_card t
                          INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = t.exam_uploader_id
                 WHERE teu.exam_uploader_id in (
                     SELECT t.exam_uploader_id
                     from t_exam_uploader t
                              INNER JOIN t_exam te on t.exam_id = te.exam_id
                     WHERE t.upload_status IN (3, 4, 5, 6)
                       AND t.modify_date_time > (now() - INTERVAL 30 DAY)
                       AND t.check_template_status = 0
                       and te.version = 3.0
                 )
                   AND t.task_id IS NULL
                 GROUP BY t.exam_uploader_id
             ) tmp;
    </select>

    <select id="scanningToBeAllocated" resultType="map" parameterType="map">
        SELECT
            COUNT(1)                    c,
            t.exam_uploader_id          examUploaderId
        FROM t_answer_card t
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = t.exam_uploader_id
        WHERE teu.exam_uploader_id in (
            SELECT t.exam_uploader_id from t_exam_uploader t
            INNER JOIN t_exam te on t.exam_id = te.exam_id
            WHERE t.upload_status IN (3, 4, 5, 6)
            AND t.modify_date_time > (now() -  INTERVAL 30 DAY)
            AND t.check_template_status = 0 and te.version = 3.0
        )
        AND t.task_id IS NULL
        GROUP BY t.exam_uploader_id
        ORDER BY c ${sort}
        LIMIT #{currentIndex}, #{pageSize};
    </select>

    <select id="queryWaitingRecognitionCards" parameterType="map" resultType="map">
        select
            count(1)               c,
            template_type          templateType,
            modify_status          modifyStatus,
            error_code             errorCodoe
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
        and task_id is null
        order by c,  modify_date_time desc
    </select>

    <select id="getUncheckTemplateExamUploaders" parameterType="map" resultType="long">
        select exam_uploader_id from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and check_template_status = 1;
    </select>

    <update id="updateAnswerCardTemplateInfo" parameterType="list">
        update t_answer_card
        set template_type = #{templateType},
            template_code = #{templateCode}
        where exam_uploader_id = #{examUploaderId}
    </update>

    <select id="queryFirstRecognitionCards" parameterType="map" resultType="map">
        SELECT
            COUNT(1)                        c,
            template_type                   templateType,
            t.batch_id                      batchId,
            tub.need_recombine_card         needRecombineCard
        FROM t_answer_card t
        LEFT JOIN t_upload_batch tub on t.batch_id = tub.batch_id
        where t.exam_uploader_id = #{examUploaderId}
        and task_id is null and task_status = 0
        group by template_type, t.batch_id
        order by c;
    </select>

    <select id="queryContinueRecognitionCards" parameterType="map" resultType="map">
        select
            count(1)               c,
            template_type          templateType
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
        and task_id is null and task_status = 2
        and (error_code = 0 ||  error_code &amp; b'00000000000000001111111111111111' = 0)  and student_id > 0
        group by template_type
        order by c;
    </select>

    <select id="queryCRCards" parameterType="map" resultType="map">
        select
            exam_uploader_id       examUploaderId,
            answer_card_id         answerCardId,
            answer_card_code       answerCardCode,
            modifier_id            userId,
            modifier_name          userName,
            template_type          templateType
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
        and task_id is null and task_status = 2
        and (error_code = 0 ||  error_code &amp; b'00000000000000001111111111111111' = 0) and student_id > 0
        order by answer_card_code
        limit #{maxSize};
    </select>

    <select id="queryFRCards" parameterType="map" resultType="map">
        select
            exam_uploader_id       examUploaderId,
            answer_card_id         answerCardId,
            answer_card_code       answerCardCode,
            modifier_id            userId,
            modifier_name          userName,
            batch_id               batchId,
            template_type          templateType
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
        and task_id is null and task_status = 0
        and modify_status != 0
        and (error_code != 2 or error_code is null)
        order by batch_id, answer_card_code
        limit #{maxSize};
    </select>

    <select id="getExamUploaderBatches" parameterType="map" resultType="map">
        SELECT
            t.batch_id                      batchId,
            t.need_recombine_card         needRecombineCard
        FROM t_upload_batch t
        where t.exam_uploader_id = #{examUploaderId};
    </select>

    <delete id="deleteExamItemRecognition" parameterType="map">
        delete from t_exam_item where exam_id = #{examId} and paper_id = #{paperId}
        and student_id in (
        select student_id from t_answer_card tac where tac.exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId != ''">
            and tac.batch_id = #{batchId}
        </if>
        )
    </delete>

    <update id="resetAnswerCard" parameterType="map">
        UPDATE t_answer_card
        SET template_type = #{templateType},
            template_code = #{templateCode},
            task_id = null,
            task_status = 0,
            error_type_code = 1,
            error_code = 0,
            student_id = 0,
            student_num = '0',
            student_exam_num = '0',
            class_id = 0,
            modify_status = 1
        WHERE exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId != ''">
            AND batch_id = #{batchId}
        </if>
        AND (error_code != 2 OR error_code IS NULL)
    </update>

    <update id="updateAnswerCardErrorCode" parameterType="map">
        update t_answer_card
        set error_code = 0
        where exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId != ''">
            and batch_id = #{batchId}
        </if>
        and (error_code != 2 or error_code is null)
    </update>

    <select id="resetCR" parameterType="map" resultType="long">
        select count(1) from t_answer_card t
        where t.exam_uploader_id = #{examUploaderId}
        and t.template_type = #{templateType}
        and (t.error_code &amp; b'00000000000000001111111111111111' ) > 0
        and t.modify_status != 0;
    </select>

    <select id="queryImmediatelyCards" parameterType="map" resultType="map">
        select
            count(1)                c,
            template_type           templateType
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
        and task_status = 4
        and (error_code != 2 or error_code is null)
        group by template_type
        order by c;
    </select>

    <select id="queryIRCards" parameterType="map" resultType="map">
        select
            exam_uploader_id       examUploaderId,
            answer_card_code       answerCardCode,
            answer_card_id         answerCardId,
            modifier_id            userId,
            modifier_name          userName,
            template_type          templateType
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
        and task_status = 4
        and (error_code != 2 or error_code is null)
        order by answer_card_code
        limit #{maxSize};
    </select>


    <select id="selectAnswerCardErrorCountCompleted" parameterType="map" resultType="int">
        select count(1) from t_answer_card
        where exam_uploader_id = #{examUploaderId}
        and ((error_code &amp; b'00000000000000001111111111111111') > 0 || task_id is null || modify_status > 0)
    </select>

    <select id="getFirstRecognitionCards" parameterType="map" resultType="map">
        select
            answer_card_id          answerCardId,
            answer_card_code        answerCardCode
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
            <if test="batchId != null and batchId != ''">
                and batch_id = #{batchId}
            </if>
            order by answer_card_id asc limit #{templateNumber};
    </select>


    <select id="getHandleAnswerCards" parameterType="map" resultType="map">
        SELECT
            answer_card_id              answerCardId,
            exam_uploader_id            examUploaderId,
            exam_id                     examId,
            class_id                    classId,
            student_id                  studentId,
            student_num                 studentNum,
            student_exam_num            studentExamNum,
            template_type               templateType,
            template_code               templateCode,
            file_name                   fileName,
            file_path                   filePath,
            recognition_result          recognitionResult,
            recognition_result_info     recognitionResultInfo,
            page_number                 pageNumber,
            error_type_code             errorTypeCode,
            error_type_name             errorTypeName,
            answer_card_code            answerCardCode,
            mark_point_json             markPointJson,
            correct_param               correctParam ,
            correct_status              correctStatus,
            correct_path                correctPath,
            score_path                  scorePath,
            modify_status               modifyStatus,
            task_id                     taskId ,
            task_status                 taskStatus,
            creator_id                  creatorId,
            creator_name                creatorName ,
            create_date_time            createDateTime,
            modifier_id                 modifierId,
            modifier_name               modifierName,
            modify_date_time            modifyDateTime,
            error_code                  errorCode,
            recog_confidence            recogConfident,
            batch_id                    batchId
        FROM t_answer_card
        WHERE
        <choose>
            <when test="examUploaderId != null and examUploaderId != ''">
                exam_uploader_id = #{examUploaderId}
                <if test="aliveUploadBatches != null and aliveUploadBatches.size > 0">
                    AND batch_id NOT IN
                    <foreach collection="aliveUploadBatches" item="item" open="(" close=")" separator=",">
                        #{item.batchId}
                    </foreach>
                </if>
                <if test="needBatches != null and needBatches.size > 0">
                    AND batch_id IN
                    <foreach collection="needBatches" item="item" open="(" separator="," close=")">
                        #{item.batchId}
                    </foreach>
                </if>
                <if test="minFileName != null and minFileName != ''">
                    AND file_name >= #{minFileName}
                </if>
            </when>
            <otherwise>
                exam_uploader_id IN (
                    SELECT DISTINCT exam_uploader_id FROM t_exam_uploader
                    WHERE exam_id = #{examId} AND paper_id = #{oldPaperId}
                )
            </otherwise>
        </choose>
        ORDER BY exam_uploader_id, batch_id, file_name;
    </select>

    <insert id="updateAnswerCardWaitingRecognition" parameterType="map">
        INSERT INTO t_answer_card (
            answer_card_id,
            exam_uploader_id,
            exam_id,
            class_id,
            student_id,
            student_exam_num,
            student_num,
            template_type,
            template_code,
            file_name,
            file_path,
            recognition_result,
            recognition_result_info,
            page_number,
            error_type_code,
            error_type_name,
            answer_card_code,
            mark_point_json,
            correct_param,
            correct_status,
            correct_path,
            score_path,
            modify_status,
            task_id,
            task_status,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time,
            error_code,
            recog_confidence,
            answer_card_pair,
            batch_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.answerCardId},
            #{item.examUploaderId},
            #{item.examId},
            #{item.classId},
            #{item.studentId},
            #{item.studentExamNum},
            #{item.studentNum},
            #{item.templateType},
            #{item.templateCode},
            #{item.fileName},
            #{item.filePath},
            #{item.recognitionResult},
            #{item.recognitionResultInfo},
            #{item.pageNumber},
            #{item.errorTypeCode},
            #{item.errorTypeName},
            #{item.answerCardCode},
            #{item.markPointjson},
            #{item.correctParam},
            #{item.correctStatus},
            #{item.correctPath},
            #{item.scorePath},
            #{item.modifyStatus},
            #{item.taskId},
            #{item.taskStatus},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime},
            #{item.errorCode},
            #{item.recogConfidence},
            #{item.answerCardPair},
            #{item.batchId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        exam_uploader_id = VALUES(exam_uploader_id),
        class_id = VALUES(class_id),
        student_id = VALUES(student_id),
        student_exam_num = VALUES(student_exam_num),
        student_num = VALUES(student_num),
        recognition_result = VALUES(recognition_result),
        recognition_result_info = VALUES(recognition_result_info),
        page_number = VALUES(page_number),
        answer_card_code = VALUES(answer_card_code),
        error_type_code = VALUES(error_type_code),
        error_type_name = VALUES(error_type_name),
        mark_point_json = VALUES(mark_point_json),
        correct_param = VALUES(correct_param),
        correct_status = VALUES(correct_status),
        correct_path = VALUES(correct_path),
        score_path = VALUES(score_path),
        modify_status = VALUES(modify_status),
        task_id = VALUES(task_id),
        task_status = VALUES(task_status),
        template_type = VALUES(template_type),
        template_code = VALUES(template_code),
        modifier_id = VALUES(modifier_id),
        modifier_name = VALUES(modifier_name),
        modify_date_time = VALUES(modify_date_time),
        error_code = VALUES(error_code),
        recog_confidence = VALUES(recog_confidence),
        answer_card_pair = VALUES(answer_card_pair),
        batch_id = VALUES(batch_id)
    </insert>

    <delete id="deleteAppendExamUploader" parameterType="map">
        delete from t_exam_uploader
            where exam_id = #{examId} and paper_id = #{oldPaperId}
            and upload_type in (11, 12, 33);
    </delete>

    <delete id="deleteAppendExamUploaderByClass" parameterType="map">
        delete from t_exam_uploader
        where exam_id = #{examId} and exam_uploader_id in (
            <foreach collection="appendExamUploaderList" item="item" separator=",">#{item.examUploaderId}</foreach>
            );
    </delete>
    <select id="getPageNumbers" parameterType="map" resultType="map">
        select
               answer_card_code             answerCardCode,
               count(1)                     c
        from t_answer_card t
        where t.exam_uploader_id = #{examUploaderId}
        <if test="needHandlerBatches != null and needHandlerBatches.size > 0">
            AND batch_id IN
            <foreach collection="needHandlerBatches" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and (error_code != 2 or error_code is null)
        group by answer_card_code
    </select>

    <select id="getExamUploaderRecognitionTaskInfo" parameterType="map" resultType="map">
        select
            teu.exam_uploader_id    examUploaderId,
            tt.create_date_time     taskTime,
            teu.create_date_time    scanTimeDate
        from t_exam_uploader  teu
        left join (
            select tt2.exam_uploader_id, max(tt2.create_date_time) create_date_time from t_recognition_task tt2
            where tt2.exam_uploader_id in
            <foreach collection="examUploads" item="item" open="(" close=")" separator=",">
                #{item.examUploaderId}
            </foreach>
            group by tt2.exam_uploader_id
        ) tt on tt.exam_uploader_id = teu.exam_uploader_id
        where teu.exam_uploader_id in
        <foreach collection="examUploads" item="item" open="(" close=")" separator=",">
            #{item.examUploaderId}
        </foreach>
        order by taskTime asc, scanTimeDate asc
    </select>

    <select id="getAllAnswerCardStudentNotInExamItem" parameterType="map" resultType="int">
        select count(1) from t_exam_uploader teu
        inner join t_answer_card tac on tac.exam_uploader_id = teu.exam_uploader_id and tac.page_number = 1
        left join t_exam_item tei on tei.exam_id = teu.exam_id and tei.paper_id = teu.paper_id
                        and tei.question_number = #{needAnswerQuestion} and tei.student_id = tac.student_id
        where teu.exam_uploader_id = #{examUploaderId}
        and tei.student_id is null
    </select>

    <select id="getOldAllAnswerCardStudentNotInExamItem" parameterType="map" resultType="int">
        SELECT count(1)
        FROM (
            SELECT
                tac.student_id
            FROM t_exam_uploader teu
            INNER JOIN t_answer_card tac ON teu.exam_uploader_id = tac.exam_uploader_id
            INNER JOIN t_exam_result ter ON ter.exam_id = teu.exam_id AND ter.paper_id = teu.paper_id AND ter.school_id = teu.school_id AND tac.student_id = ter.student_id
            WHERE teu.exam_uploader_id = #{examUploaderId} AND ter.result_status = 0
        ) tmp
        WHERE student_id NOT IN (
            SELECT student_id  FROM t_exam_item t
            INNER JOIN t_exam_uploader teu ON t.exam_id = teu.exam_id AND t.paper_id = teu.paper_id AND t.school_id = teu.school_id
            WHERE teu.exam_uploader_id = #{examUploaderId}
        );
    </select>


    <select id="queryLowConfidentHandNumber" parameterType="map" resultType="int">
        SELECT count(DISTINCT(answer_card_code))
        FROM t_answer_card tac
                 INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id and teu.exam_id=tac.exam_id
        where ( tac.error_code  >> 22 &amp; 1 = 1 ) AND
            tac.exam_uploader_id=#{examUploaderId}
    </select>

    <select id="queryLowConfidentHandNumberCrossSchool" parameterType="map" resultType="int">
        SELECT count(DISTINCT(answer_card_code))
        FROM t_answer_card tac
        where
        tac.exam_uploader_id in
        <foreach collection="examUploaderIds" item="examUploaderId" separator="," open="(" close=")">
            #{examUploaderId}
        </foreach>
        and ( tac.error_code >> 22 &amp; 1 = 1 ) and tac.template_type != 0
        <include refid="rightStudentCard"></include>
    </select>

    <sql id="rightStudentCard" >
        and tac.error_code &amp; b'00000000000000001111111111111111' = 0
    </sql>

    <select id="queryDetailLowConfidentHandNumberCrossSchool" parameterType="map" resultType="map">
        SELECT
        tac.student_id studentId,
        tac.low_confident_info lowConfidentInfo,
        tac.exam_uploader_id examUploaderId,
        ter.school_id schoolId
        FROM t_answer_card tac
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id and teu.exam_id=tac.exam_id
        INNER JOIN t_exam_result ter on ter.exam_id = teu.exam_id  and ter.paper_id = teu.paper_id and ter.student_id = tac.student_id
        where ( tac.error_code >> 22 &amp; 1 = 1 ) AND tac.exam_uploader_id in
        <foreach collection="examUploaderIds" item="examUploaderId" separator="," open="(" close=")">
            #{examUploaderId}
        </foreach>
        and tac.template_type != 0
        <include refid="rightStudentCard"></include>
        order by tac.student_id
    </select>


    <select id="queryDetailLowConfidentHandNumber" parameterType="map" resultType="map">
        SELECT
            tac.student_id studentId,
            tac.low_confident_info lowConfidentInfo
        FROM t_answer_card tac
        INNER JOIN t_exam_uploader teu on teu.exam_uploader_id = tac.exam_uploader_id and teu.exam_id=tac.exam_id
        where ( tac.error_code  >> 22 &amp; 1 = 1 ) AND tac.exam_uploader_id=#{examUploaderId} and tac.exam_id=#{examId}
        order by tac.student_id
    </select>


    <select id="getAnswerCardsByExamItemId" parameterType="map" resultType="map">
        SELECT
            tac.answer_card_id            answerCardId,
            tac.exam_uploader_id          examUploaderId,
            tac.answer_card_code          answerCardCode,
            tac.error_code                errorCode,
            tac.low_confident_info        lowConfidentInfo,
            tac.error_code >> 22 &amp; 1      errorValue
        FROM t_exam_item tei
        INNER JOIN t_exam_uploader teu ON tei.exam_id = teu.exam_id AND tei.paper_id = teu.paper_id AND tei.school_id = teu.school_id
        INNER JOIN t_answer_card tac ON tac.exam_uploader_id = teu.exam_uploader_id AND tei.student_id = tac.student_id
        WHERE exam_item_id = #{examItemId};
    </select>

    <update id="ignoreHandleException" parameterType="map">
        update t_answer_card
            set error_code = error_code &amp; ~ #{exceptionValue}
        where exam_uploader_id = #{examUploaderId}
            and answer_card_code = #{answerCardCode}
    </update>

    <update id="ignoreThirdHandleException" parameterType="map">
        update t_answer_card
        set low_confident_info = #{lowConfidentInfo}
        <if test="exceptionValue != null and exceptionValue != ''">
            , error_code = error_code &amp; ~ #{exceptionValue}
        </if>
        where exam_uploader_id = #{examUploaderId}
          and answer_card_code = #{answerCardCode}
    </update>

    <update id="removeScoreHandle" parameterType="map">
        update t_answer_card
            set low_confident_info = #{lowConfidentInfo}
        where exam_uploader_id = #{examUploaderId}
            and answer_card_id = #{answerCardId}
    </update>

    <select id="getExamItemAnswerCardInfo" parameterType="map" resultType="map">
        SELECT
           teu.template_type        templateType,
           tac.template_code        templateCode,
           tac.template_type        acTemplateType,
           teu.exam_id              examId,
           teu.paper_id             paperId,
           teu.exam_uploader_id     examUploaderId,
           tei.question_number      questionNumber,
           teu.upload_status        uploadStatus,
           tei.read_status          readStatus
        FROM t_answer_card tac, t_exam_item tei, t_exam_uploader teu
        WHERE exam_item_id = #{examItemId}
              AND teu.exam_id = tei.exam_id AND teu.paper_id = tei.paper_id
              AND tac.exam_uploader_id = teu.exam_uploader_id AND tac.student_id = tei.student_id AND tac.page_number = 1;
    </select>

    <select id="getNormalStudents" parameterType="map" resultType="map">
        SELECT
              ter.student_id            studentId,
              ter.student_num           studentNum,
              ter.student_name          studentName,
              tes.school_id             schoolId,
              tes.school_name           schoolName,
              teu.exam_uploader_id      examUploaderId,
              ter.class_id              classId,
              ter.class_name            className,
              count(1)                  cardCount,
              1                         answerCardStatus
        FROM t_answer_card tac, t_exam_result ter, t_exam_uploader teu, t_exam_school tes
        <if test="uploadType == 12">
            ,t_exam_uploader_class teuc
        </if>
        WHERE tac.exam_uploader_id = #{examUploaderId}  AND tac.student_id != 0
            AND tac.modify_status = 0 AND tac.error_code <![CDATA[&]]> b'0000000000000001111111111111111' = 0
            AND teu.exam_id = ter.exam_id AND teu.paper_id = ter.paper_id
            AND teu.school_id = ter.school_id AND teu.exam_uploader_id = tac.exam_uploader_id
            AND tac.student_id = ter.student_id
            AND tes.exam_id = teu.exam_id AND tes.school_id = teu.school_id
            <if test="uploadType == 12">
                AND teuc.exam_uploader_id = teu.exam_uploader_id
                AND teuc.class_id = ter.class_id
            </if>
            <if test="searchValue != null and searchValue != ''">
                AND (tac.student_num like concat('%', #{searchValue}, '%')
                OR ter.student_name like concat('%', #{searchValue}, '%'))
            </if>
            <if test="classId != null and classId != ''">
                AND ter.class_id = #{classId}
            </if>
            GROUP BY tac.student_id
            ORDER BY tac.student_id
            <if test="pageSize !=null and currentIndex != null">
                LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
            </if>;
    </select>

    <select id="getNormalStudentCount" parameterType="map" resultType="int">
        SELECT COUNT(DISTINCT tac.student_id)
        FROM t_answer_card tac, t_exam_result ter, t_exam_uploader teu
        <if test="uploadType == 12">
            ,t_exam_uploader_class teuc
        </if>
        WHERE tac.exam_uploader_id = #{examUploaderId}
          AND tac.exam_uploader_id = teu.exam_uploader_id
          AND ter.exam_id = teu.exam_id AND ter.paper_id = teu.paper_id
          AND ter.school_id = teu.school_id AND tac.student_id = ter.student_id
          AND tac.student_id != 0 AND tac.modify_status = 0
          AND tac.error_code <![CDATA[&]]>  b'00000000000000001111111111111111' = 0
          <if test="searchValue != null and searchValue != ''">
              AND (tac.student_num like concat('%', #{searchValue}, '%')
              OR ter.student_name like concat('%', #{searchValue}, '%'))
          </if>
          <if test="uploadType == 12">
            AND teuc.exam_uploader_id = teu.exam_uploader_id
            AND teuc.class_id = ter.class_id
          </if>
          <if test="classId != null and classId != ''">
              AND ter.class_id = #{classId}
          </if>

          ;
    </select>

    <select id="getLoseAnswerCards" parameterType="map" resultType="map">
        SELECT
               batch_id                     batchId,
               answer_card_code             answerCardCode,
               answer_card_id               answerCardId
        FROM t_answer_card
        WHERE exam_uploader_id = #{examUploaderId}
          AND batch_id = #{batchId}
          AND (error_code >> 1 <![CDATA[&]]> 1) = 1
          ORDER BY file_name desc
    </select>

    <update id="ignoreLoseCard" parameterType="map">
        UPDATE t_answer_card
            set error_code = error_code &amp; ~ 2
        WHERE exam_uploader_id = #{examUploaderId}
            AND batch_id = #{batchId}
            AND answer_card_id in
            <foreach collection="cards" item="item" open="(" separator="," close=")">
                #{item.answerCardId}
            </foreach>
    </update>

    <select id="getExamUploaderTemplates" parameterType="map" resultType="map">
        SELECT
                tac.template_type       templateType,
                tac.template_code       templateCode,
                teu.exam_id             examId,
                teu.paper_id            paperId
        FROM t_answer_card tac, t_exam_uploader teu
        WHERE tac.exam_uploader_id = #{examUploaderId}
            AND tac.exam_uploader_id = teu.exam_uploader_id
        GROUP BY tac.template_type, tac.template_code;
    </select>

    <select id="getAnswerCardsByExamItemForHandle" parameterType="map" resultType="map">
        SELECT
            tac.answer_card_id                   answerCardId,
            tac.error_code                       errorCode,
            tac.student_id                       studentId,
            tac.low_confident_info               lowConfidentInfo,
            tei.recognition_value                recognitionValue,
            tei.read_type                        readType,
            tei.question_number                  questionNumber,
            tei.school_id                        schoolId
            <if test="chooseNone != null and chooseNone != ''">
                , tac.error_code <![CDATA[&]]> (1 <![CDATA[<<]]> (#{chooseNone} - 1)) > 0        isChooseNone
            </if>
            <if test="trustyLow != null and trustyLow !='' ">
                , tac.error_code <![CDATA[&]]> (1 <![CDATA[<<]]> (#{trustyLow} - 1)) > 0          isTrustyLow
            </if>
            <if test="singleJudgedMulti != null and singleJudgedMulti !='' ">
                , tac.error_code <![CDATA[&]]> (1 <![CDATA[<<]]> (#{singleJudgedMulti} - 1)) > 0  isSingleJudgedMulti
            </if>
        FROM t_answer_card tac, t_exam_item tei, t_exam_uploader teu
        WHERE tei.exam_item_id = #{examItemId}
            AND tei.exam_id = teu.exam_id AND tei.paper_id = teu.paper_id AND tei.school_id = teu.school_id
            AND tac.exam_uploader_id = teu.exam_uploader_id AND tac.student_id = tei.student_id;
    </select>

    <update id="updateErrorCodeWithBits" parameterType="map">
        UPDATE t_answer_card
                set error_code = error_code <![CDATA[&]]>
                <foreach collection="bits" item="bit" separator="&amp;">
                    ~(1 <![CDATA[<<]]> (#{bit} - 1))
                </foreach>
        WHERE answer_card_id IN  (
          <foreach collection="cards" item="item" separator=",">
            #{item.answerCardId}
          </foreach>
          )
    </update>

    <select id="getStudentExamUploaderStatus" parameterType="map" resultType="map">
        SELECT
                teu.upload_status               uploadStatus,
                teu.exam_uploader_id            examUploaderId
        FROM t_exam_uploader teu, t_answer_card tac
        WHERE
              teu.exam_uploader_id = tac.exam_uploader_id
            AND teu.exam_id = #{examId} AND teu.paper_id = #{paperId}
            AND teu.school_id = #{schoolId}
            AND tac.student_id = #{studentId}
        LIMIT 1;
    </select>

    <select id="getMixStudentExamUploaderStatus" parameterType="map" resultType="map">
        select teu.upload_status    uploadStatus,
               teu.exam_uploader_id examUploaderId
        from t_exam_uploader teu,
             t_answer_card tac
        where teu.exam_uploader_id = #{examUploaderId}
          and tac.exam_uploader_id = teu.exam_uploader_id
          and tac.student_id = #{studentId}
        limit 1;
    </select>

    <select id="getExamUploaderListByClass" parameterType="map" resultType="map">
        SELECT
                exam_uploader_id            examUploaderId,
                upload_type                  uploadType,
                upload_status               uploadStatus
        FROM t_exam_uploader
        WHERE exam_id = #{examId} AND paper_id = #{oldPaperId};
    </select>

    <select id="getAppendCardsByClass" parameterType="map" resultType="map">
        SELECT
        answer_card_id              answerCardId,
        exam_uploader_id            examUploaderId,
        exam_id                     examId,
        class_id                    classId,
        student_id                  studentId,
        student_num                 studentNum,
        template_type               templateType,
        template_code               templateCode,
        file_name                   fileName,
        file_path                   filePath,
        recognition_result          recognitionResult,
        recognition_result_info     recognitionResultInfo,
        page_number                 pageNumber,
        error_type_code             errorTypeCode,
        error_type_name             errorTypeName,
        answer_card_code            answerCardCode,
        mark_point_json             markPointJson,
        correct_param               correctParam ,
        correct_status              correctStatus,
        correct_path                correctPath,
        score_path                  scorePath,
        modify_status               modifyStatus,
        task_id                     taskId ,
        task_status                 taskStatus,
        creator_id                  creatorId,
        creator_name                creatorName ,
        create_date_time            createDateTime,
        modifier_id                 modifierId,
        modifier_name               modifierName,
        modify_date_time            modifyDateTime,
        error_code                  errorCode,
        recog_confidence            recogConfident,
        batch_id                    batchId
        FROM t_answer_card
        WHERE exam_uploader_id IN (
            <foreach collection="examUploaderList" item="item" separator=",">#{item.examUploaderId}</foreach>
        )
        ORDER BY exam_uploader_id, batch_id, file_name;
    </select>

    <select id="getRecognizedTemplate" parameterType="map" resultType="map">
        SELECT
                teu.exam_id                 examId,
                teu.paper_id                paperId,
                tac.template_type           templateType,
                tac.template_code           templateCode
        FROM t_answer_card tac, t_exam_uploader teu
        WHERE tac.exam_uploader_id in
        <foreach collection="examUploaderIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
          AND teu.exam_uploader_id = tac.exam_uploader_id
          AND tac.student_id != 0
        GROUP BY tac.template_code;
    </select>

    <select id="getBatchCardList" parameterType="map" resultType="map">
        select
              file_name         fileName
        from t_answer_card
        where exam_uploader_id = #{examUploaderId} and batch_id = #{batchId};
    </select>

    <select id="getExamUploaderCardCount" parameterType="map" resultType="int">
        select count(1) from t_answer_card
        where  exam_uploader_id = #{examUploaderId};
    </select>

    <select id="getOnlyOneFilePath" parameterType="map" resultType="string">
        select file_path from t_answer_card where exam_uploader_id = #{examUploaderId} limit 1;
    </select>

    <update id="updateAppendExamUploader2ExamUploader" parameterType="map">
        update t_answer_card
            set exam_uploader_id = #{examUploaderId}
        where exam_uploader_id in (<foreach collection="appendExamUploaderIds" item="item" separator=",">#{item}</foreach>);
    </update>

    <update id="updateExamAnswerCardStuNum" parameterType="map">
        update t_exam_student tes, t_answer_card tac
        set tac.student_num = tes.student_num,
            tac.modifier_id = #{userId},
            tac.modifier_name = #{userName},
            tac.modify_date_time = now()
        where tes.exam_id = tac.exam_id and tes.student_id = tac.student_id and tes.student_num != tac.student_num
         and tes.exam_id = #{examId}
        <if test="schoolId != null">
            and tes.school_id = #{schoolId}
        </if>
    </update>

    <update id="updateExamAnswerCardStuExamNum" parameterType="map">
        update t_exam_student tes, t_answer_card tac
        set tac.student_num = tes.student_num,
        tac.modifier_id = #{userId},
        tac.modifier_name = #{userName},
        tac.modify_date_time = now()
        where tes.exam_id = tac.exam_id and tes.student_id = tac.student_id
        and tes.exam_id = #{examId}
        <if test="schoolId != null">
            and tes.school_id = #{schoolId}
        </if>
        and tes.student_id in
        <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </update>

    <update id="updateAnswerCardStuExamNumByExamIdAndStuId" parameterType="com.dongni.exam.plan.bean.StudentExamNumUpdateDTO">
        update t_exam_uploader teu, t_answer_card tac
        set tac.student_exam_num = #{studentExamNum},
            tac.modifier_id = #{modifierId},
            tac.modifier_name = #{modifierName},
            tac.modify_date_time = now()
        where teu.exam_uploader_id = tac.exam_uploader_id
          and teu.exam_id = #{examId}
          and tac.student_id = #{studentId}
    </update>
</mapper>
