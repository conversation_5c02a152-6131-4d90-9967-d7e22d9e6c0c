<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.newcard.dao.NewAnswerCardDao">
    <update id="updateExamUploaderIds2ExamUploaderId" parameterType="map">
        update t_answer_card
            set exam_uploader_id = #{id}
        where exam_uploader_id in (<foreach collection="idList" separator="," item="item">#{item}</foreach>)
    </update>


    <delete id="deleteAnswerCardByExamUploaderId" parameterType="map">
        delete from t_answer_card where exam_uploader_id = #{examUploaderId};
    </delete>

    <select id="getExamPaperStudentCount" parameterType="map" resultType="int">
        select count(1) from
        (
            select count(1) from t_exam_result ter
            left join t_exam_item tei on tei.exam_id = #{examUploaderBo.examId}
                 and tei.paper_id = #{examUploaderBo.paperId}
                 and tei.question_number = #{questionNumber}
                 and tei.read_status in (0, 1, 2)
                 and tei.student_id = ter.student_id
            left join (
                select tac.* from t_exam_uploader teu, t_answer_card tac
                where teu.exam_id = #{examUploaderBo.examId} and teu.paper_id = #{examUploaderBo.paperId}
                <if test="examUploaderBo.schoolId != null and examUploaderBo.schoolId > 0">
                  and teu.school_id = #{examUploaderBo.schoolId}
                </if>
                and teu.upload_status in (2, 3, 4, 5, 6)
                and tac.exam_uploader_id = teu.exam_uploader_id
            ) tac on tac.student_id = ter.student_id
            where ter.exam_id = #{examUploaderBo.examId}
                and ter.paper_id = #{examUploaderBo.paperId}
                <if test="relativeStudentVO.searchValue != null and relativeStudentVO.searchValue != ''">
                    AND (ter.class_name like concat('%',#{relativeStudentVO.searchValue},'%')
                    OR ter.student_name like concat('%',#{relativeStudentVO.searchValue},'%')
                    OR ter.student_exam_num like concat('%',#{relativeStudentVO.searchValue},'%')
                    OR ter.student_name_pinyin like concat('%',#{relativeStudentVO.searchValue},'%'))
                </if>
                <if test="examUploaderBo.schoolId != 0">
                    and ter.school_id = #{examUploaderBo.schoolId}
                </if>
                <if test="examUploaderBo.classIdList != null and examUploaderBo.classIdList.size() > 0">
                    and ter.class_id in (<foreach collection="examUploaderBo.classIdList" item="item" separator=",">#{item}</foreach>)
                </if>
                <if test="relativeStudentVO.classId != null and relativeStudentVO.classId != ''">
                    and ter.class_id = #{relativeStudentVO.classId}
                </if>
                and tei.student_id is null
                group by ter.student_id
                <if test="relativeStudentVO.cardCount != null and relativeStudentVO.cardCount >= 0">
                    <choose>
                        <when test="relativeStudentVO.cardCount >= 3">
                            having count(tac.answer_card_id) >= #{relativeStudentVO.cardCount}
                        </when>
                        <otherwise>
                            having count(tac.answer_card_id) = #{relativeStudentVO.cardCount}
                        </otherwise>
                    </choose>
                </if>
        ) tmp
    </select>

    <select id="getExamPaperStudent" resultType="com.dongni.exam.newcard.bean.VO.ExamStudentVO" parameterType="map">
        select
            ter.exam_result_id          examResultId,
            ter.student_id              studentId,
            ter.student_exam_num        studentExamNum,
            ter.student_num             studentNum,
            ter.student_name            studentName,
            ter.class_name              className,
            ter.class_id                classId,
            count(tac.answer_card_id)   cardCount,
            tes.school_id               schoolId,
            tes.school_name             schoolName
        from t_exam_result ter
        inner join t_exam_school tes on tes.exam_id = ter.exam_id and ter.school_id = tes.school_id
        left join t_exam_item tei on tei.exam_id = #{examUploaderBo.examId}
            and tei.paper_id = #{examUploaderBo.paperId}
            and tei.question_number = #{questionNumber}
            and tei.read_status in (0, 1, 2)
            and tei.student_id = ter.student_id
        left join (
            select tac.* from t_exam_uploader teu, t_answer_card tac
            where teu.exam_id = #{examUploaderBo.examId} and teu.paper_id = #{examUploaderBo.paperId}
            <if test="examUploaderBo.schoolId != null and examUploaderBo.schoolId > 0">
                and teu.school_id = #{examUploaderBo.schoolId}
            </if>
            and teu.upload_status in (2, 3, 4, 5, 6)
            and tac.exam_uploader_id = teu.exam_uploader_id
        ) tac on tac.student_id = ter.student_id
        where ter.exam_id = #{examUploaderBo.examId}
            and ter.paper_id = #{examUploaderBo.paperId}
            <if test="relativeStudentVO.searchValue != null and relativeStudentVO.searchValue != ''">
                AND (ter.class_name like concat('%',#{relativeStudentVO.searchValue},'%')
                OR ter.student_name like concat('%',#{relativeStudentVO.searchValue},'%')
                OR ter.student_exam_num like concat('%',#{relativeStudentVO.searchValue},'%')
                OR ter.student_name_pinyin like concat('%',#{relativeStudentVO.searchValue},'%'))
            </if>
            <if test="examUploaderBo.schoolId != null and examUploaderBo.schoolId > 0">
                and ter.school_id = #{examUploaderBo.schoolId}
            </if>
            <if test="examUploaderBo.classIdList != null and examUploaderBo.classIdList.size() > 0">
                and ter.class_id in (<foreach collection="examUploaderBo.classIdList" item="item" separator=",">#{item}</foreach>)
            </if>
            <if test="relativeStudentVO.classId != null and relativeStudentVO.classId != ''">
                and ter.class_id = #{relativeStudentVO.classId}
            </if>
            and tei.student_id is null
            group by ter.student_id
            <if test="relativeStudentVO.cardCount != null and relativeStudentVO.cardCount >= 0">
                <choose>
                    <when test="relativeStudentVO.cardCount >= 3">
                        having count(tac.answer_card_id) >= #{relativeStudentVO.cardCount}
                    </when>
                    <otherwise>
                        having count(tac.answer_card_id) = #{relativeStudentVO.cardCount}
                    </otherwise>
                </choose>
            </if>
            order by cardCount desc, ter.student_id
            <if test="relativeStudentVO.pageSize !=null and relativeStudentVO.currentIndex != null">
                limit #{relativeStudentVO.currentIndex, jdbcType=INTEGER},#{relativeStudentVO.pageSize, jdbcType=INTEGER}
            </if>
    </select>

    <select id="getStudentIdsByExamUploaderId"  parameterType="map" resultType="java.lang.Long">
        select distinct student_id from t_answer_card where exam_uploader_id = #{examUploaderId} and student_id > 0;
    </select>

    <select id="getStudentCardCount" parameterType="map" resultType="com.dongni.exam.plan.bean.dto.StudentCardDTO">
        select
               tac.student_id                   studentId,
               count(tac.answer_card_id)        cardCount
        from t_answer_card tac, t_exam_uploader teu
        where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
            and tac.exam_uploader_id = teu.exam_uploader_id
          and tac.student_id in (<foreach collection="list" item="item" separator=",">#{item.studentId}</foreach>)
        group by tac.student_id;
    </select>

    <select id="getStudentUploadedStatusByExamUploaderAndStudentIds" parameterType="map"
            resultType="com.dongni.exam.plan.bean.dto.StudentCardDTO">
        select tac.student_id studentId,
               count(1) cardCount
        from t_answer_card tac, t_exam_uploader teu
        where teu.exam_id = #{examUploaderBo.examId} and teu.paper_id = #{examUploaderBo.paperId}
            <if test="examUploaderBo.schoolId != null and examUploaderBo.schoolId > 0">
                and teu.school_id = #{examUploaderBo.schoolId}
            </if>
            and tac.exam_uploader_id = teu.exam_uploader_id
            and tac.student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
            and tac.error_code &amp; b'00000000000000001111111111111111' = 0
        group by tac.student_id;
    </select>

    <select id="getStudentCardStatus" parameterType="map" resultType="com.dongni.exam.newcard.bean.DTO.StudentCardStatusDTO">
        select
               tac.student_id                       studentId,
               tac.error_code >> 6 &amp; 1          resultStatus,
               tac.error_code                       errorCode
        from t_answer_card tac, t_exam_uploader teu
        where teu.exam_id = #{examUploaderBo.examId} and teu.paper_id = #{examUploaderBo.paperId}
        <if test="examUploaderBo.schoolId != null and examUploaderBo.schoolId > 0">
            and teu.school_id = #{examUploaderBo.schoolId}
        </if>
        and tac.exam_uploader_id = teu.exam_uploader_id
        and tac.student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
        and (tac.error_code &amp; b'00000000000000001111111111111111' = 0 or tac.error_code >> 6 &amp; 1 = 1)
        group by tac.student_id;

    </select>

    <select id="getForecastCardCount" resultType="java.lang.Integer" parameterType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO">
        select count(1)  from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
            <if test="schoolId != null and schoolId > 0">
                and school_id = #{schoolId}
            </if>
            <if test="careSchoolIdList != null and careSchoolIdList.size() > 0">
                and school_id in (<foreach collection="careSchoolIdList" item="item" separator=",">#{item}</foreach>)
            </if>
            <if test="classIdList != null and classIdList.size() > 0">
                and class_id in (<foreach collection="classIdList" item="item" separator=",">#{item}</foreach>)
            </if>
    </select>

    <select id="getStudentCardCountBySchool" resultType="com.dongni.exam.plan.bean.dto.StudentCardDTO">
        select tac.student_id studentId,
               count(1)       cardCount
        from t_answer_card tac, t_exam_uploader teu
        where teu.exam_id = #{examId} and paper_id = #{paperId}
            <if test="schoolId > 0">
                and school_id = #{schoolId}
            </if>
            and teu.upload_status in (2, 3, 4, 5, 6)
            and tac.exam_uploader_id = teu.exam_uploader_id
            and tac.student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
            group by tac.student_id;
    </select>

    <update id="updateAnswerCardRepeatByStudentIds">
        update t_answer_card tac, t_exam_uploader teu
            set tac.error_code = (tac.error_code &amp; ~(1 <![CDATA[<<]]> 2) | (1 <![CDATA[<<]]> 2)),
                tac.task_id = null
        where tac.exam_uploader_id = teu.exam_uploader_id
         and teu.exam_id = #{examId} and paper_id = #{paperId}
         <if test="schoolId > 0">and school_id = #{schoolId}</if>
         and teu.upload_status in (2, 3, 4, 5, 6)
         and tac.student_id in (<foreach collection="studentIds" item="item" separator="," >#{item}</foreach>)
    </update>

    <update id="updateAnswerCardUnRepeatByStudentIds">
        update t_answer_card tac, t_exam_uploader teu
            set tac.error_code = (tac.error_code <![CDATA[&]]> ~(1 <![CDATA[<<]]> 2)),
                tac.task_id = null
        where tac.exam_uploader_id = teu.exam_uploader_id
        and teu.exam_id = #{examId} and paper_id = #{paperId}
        <if test="schoolId > 0">and school_id = #{schoolId}</if>
        and teu.upload_status in (2, 3, 4, 5, 6)
        and tac.student_id in (<foreach collection="studentIds" item="item"  separator=",">#{item}</foreach>)
    </update>


    <select id="getExamUploaderWithoutError" parameterType="map" resultType="com.dongni.exam.plan.bean.ExamUploaderBO">
        select
               teu.exam_uploader_id     examUploaderId,
               teu.exam_id              examId,
               teu.paper_id             paperId,
               teu.uploader_id          uploaderId,
               te.correct_mode          correctMode,
               teu.upload_type          uploadType,
               teu.template_type        templateType,
               tmp.templateCode         templateCode,
               tmp.totalCount           totalCount,
               tmp.rightCount           rightCount
        from t_exam_uploader teu,
             (
                 select teu.exam_uploader_id                                                                examUploaderId,
                        count(tac.answer_card_id)                                                           totalCount,
                        count(if(tac.modify_status = 0 and
                                 tac.error_code &amp; b'00000000000000001111111111111111' = 0, 1, NULL))        rightCount,
                        min(tac.template_code)                                                              templateCode
                 from t_exam_uploader teu,
                      t_answer_card tac
                 where teu.exam_id = #{examId}
                   and teu.paper_id = #{paperId}
                   and teu.upload_status in (2, 3, 4, 5, 6)
                   and tac.exam_uploader_id = teu.exam_uploader_id
                 group by tac.exam_uploader_id
             ) tmp,
             t_exam te
        where teu.exam_uploader_id = tmp.examUploaderId and te.exam_id = teu.exam_id
            and te.exam_id = #{examId};
    </select>

    <select id="getExamPaperCardStat" parameterType="map" resultType="com.dongni.exam.plan.bean.vo.ExamPaperCardStatVO">
        select
            count(if(uploaded_status = 1 and result_status = 0, 1, null))        normalStudentCount,
            count(if(result_status = 1, 1, null))                                absentStudentCount,
            count(if(uploaded_status = 0, 1, null))                              unuploadedStudentCount
        from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
          <if test="schoolIds != null and schoolIds.size() > 0">
              and school_id in (<foreach collection="schoolIds" item="item" separator=",">#{item}</foreach>)
          </if>
    </select>

    <select id="getSchoolIds" parameterType="map" resultType="Long">
        select distinct ter.school_id from t_answer_card tac, t_exam_result ter
        where tac.exam_uploader_id in (<foreach collection="examUploaderIds" item="item" separator=",">#{item}</foreach>)
            and tac.page_number = 1
            and ter.exam_id = #{examId} and ter.paper_id = #{paperId} and ter.student_id = tac.student_id;
    </select>


    <update id="resetAnswerCard" parameterType="map">
        UPDATE t_answer_card
        SET template_type = #{templateType},
        template_code = #{templateCode},
        task_id = null,
        task_status = 0,
        error_type_code = 1,
        error_code = 0,
        student_id = 0,
        student_num = '0',
        student_exam_num = '0',
        class_id = 0,
        modify_status = 1
        WHERE exam_uploader_id = #{examUploaderId}
        <if test="batchId != null and batchId > 0">
            AND batch_id = #{batchId}
        </if>
        AND (error_code != 2 OR error_code IS NULL)
    </update>

    <select id="getExamUploaderUncareSchools" resultType="com.dongni.exam.plan.bean.vo.ExamSchoolVO">
        select /*+INL_JOIN(teu, tac, ter, tes)*/ distinct
                tes.school_id     schoolId,
                tes.school_name   schoolName
        from t_exam_uploader teu, t_answer_card tac,
             t_exam_result ter, t_exam_school tes
        where teu.exam_uploader_id = #{examUploaderId}
            and tac.exam_uploader_id = teu.exam_uploader_id  and tac.page_number = 1
            and ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id
            <if test="schoolId != null and schoolId > 0">
                and ter.school_id = teu.school_id
            </if>
            and ter.student_id = tac.student_id
            and tes.exam_id = teu.exam_id and tes.school_id = ter.school_id
    </select>

    <select id="getExamUploaderCompleteStatistic" parameterType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO"
            resultType="com.dongni.exam.card.bean.vo.ExamUploaderCompleteStatVO">
        select count(distinct tac.student_id)     normalStudentCount
        from t_answer_card tac
         where tac.exam_uploader_id = #{examUploaderId}
            and tac.student_id > 0;
    </select>


    <update id="updateExamUploaderTemplateInfo" parameterType="com.dongni.exam.newcard.bean.VO.UpdateTemplateVO">
        update t_exam_uploader
        set template_type = #{templateType},
            <if test="objectItemRecoType != null">
                objectitem_recotype = #{objectItemRecoType},
            </if>
            <if test="templateCode != null">
                default_template_code = #{templateCode},
            </if>
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = NOW()
        where exam_uploader_id = #{examUploaderId}
    </update>

    <select id="getExamUploaderStudentCount" resultType="java.lang.Integer">
        select count(distinct student_id)
        from t_answer_card
        where exam_uploader_id = #{examUploaderId};
    </select>

    <select id="getBatchExceptionCards" resultType="com.dongni.exam.newcard.bean.VO.AnswerCardVO" parameterType="map">
        select answer_card_id   answerCardId,
               student_id       studentId
        from t_answer_card
        where exam_uploader_id = #{examUploaderId} and batch_id = #{batchId}
          and error_code &amp; b'00000000000000001111111111111111' > 1;
    </select>

    <update id="updateStudentAnswerCardNoRelative" parameterType="map">
        update t_answer_card tac, t_exam_uploader teu
        set tac.exam_uploader_id = -tac.exam_uploader_id,
            tac.exam_id = -tac.exam_id,
            tac.student_id = -tac.student_id
        where teu.exam_id = #{examId} and teu.paper_id = #{paperId} and teu.upload_status = 7
            and tac.exam_uploader_id = teu.exam_uploader_id
            and tac.student_id in (<foreach collection="studentIdList" separator="," item="item">#{item}</foreach>);
    </update>

    <select id="getMisMatchCardStudentList" resultType="com.dongni.exam.plan.bean.bo.CardStudentInfoBO"
            parameterType="com.dongni.exam.plan.bean.vo.UpdateResultCardVO">
        select
            tac2.file_name                  fileName,
            correctTac.student_id           correctStudentId,
            correctTac.student_exam_num     correctStudentExamNum,
            correctTac.student_num          correctStudentNum,
            correctTac.class_id             correctClassId,
            correctTac.page_number          pageNumber,
            tac2.student_id                 studentId,
            tac2.student_exam_num           studentExamNum,
            tac2.student_num                studentNum
        from t_answer_card correctTac, t_answer_card tac2
        where correctTac.exam_uploader_id = #{correctExamUploaderId} and tac2.exam_uploader_id = #{examUploaderId}
            and substr(correctTac.file_name, #{correctStartIndex})  = substr(tac2.file_name, #{startIndex})
            and correctTac.student_id != tac2.student_id;
    </select>

    <select id="getCardSameNameStudentIds" resultType="com.dongni.exam.plan.bean.dto.ReassociateCardInfo"
            parameterType="com.dongni.exam.plan.bean.vo.UpdateResultCardVO">
        select
            group_concat(distinct correctTac.student_id)        correctStudentIds,
            count(distinct correctTac.student_id)               correctCount,
            group_concat(distinct tac.student_id)               errorStudentIds,
            count(distinct tac.student_id)                      errorCount
        from
            t_answer_card correctTac,
            t_answer_card tac
        where correctTac.exam_uploader_id = #{correctExamUploaderId}
          and  tac.exam_uploader_id = #{examUploaderId}
          and substr(correctTac.file_name, #{correctStartIndex}) = substr(tac.file_name, #{startIndex});
    </select>


    <update id="updateStudentWithNewExamUploader" parameterType="com.dongni.exam.plan.bean.vo.UpdateResultCardVO">
        update t_answer_card tac2, t_answer_card correctTac
        set tac2.student_id = correctTac.student_id,
            tac2.student_exam_num = correctTac.student_exam_num,
            tac2.student_num = correctTac.student_num,
            tac2.class_id = correctTac.class_id
        where correctTac.exam_uploader_id = #{correctExamUploaderId} and tac2.exam_uploader_id = #{examUploaderId}
          and substr(correctTac.file_name, #{correctStartIndex})  = substr(tac2.file_name, #{startIndex})
          and correctTac.student_id != tac2.student_id
    </update>


    <select id="getAbsentAnswerCardCodes" resultType="java.lang.String">
        select distinct answer_card_code
        from t_answer_card
        where exam_uploader_id in (<foreach collection="examUploaderIds" separator="," item="item">#{item}</foreach>);
    </select>

    <select id="getWrongRelativeDeleteStudents" resultType="com.dongni.exam.plan.bean.vo.ExamStudentResultVO">
        select tac2.student_id
        from t_answer_card tac2
        left join t_answer_card correctTac on correctTac.exam_uploader_id = #{correctExamUploaderId} and correctTac.page_number = 1 and correctTac.student_id = tac2.student_id
        where tac2.exam_uploader_id = #{examUploaderId} and tac2.page_number = 1
          and correctTac.student_id is null;
    </select>

    <select id="getCardTemplateInfos" resultType="com.dongni.exam.newcard.bean.DTO.CardTemplate" parameterType="map">
        select distinct
               exam_uploader_id         examUploaderId,
               template_type            templateType,
               template_code            templateCode
        from t_answer_card
        where exam_uploader_id in (<foreach collection="examUploaderIds" item="item" separator=",">#{item}</foreach>);
    </select>

    <select id="isStudentCardAbsent" resultType="int" parameterType="map">
        select count(1)
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
          and student_id = #{studentId}
          and error_code >> 6 &amp; 1 = 1;
    </select>

    <select id="getExistsExamAndExamUploaderCards" resultType="java.lang.Long">
            select answer_card_id
            from t_answer_card
            where exam_id = #{examId} and exam_uploader_id != #{examUploaderId}
              and file_path like concat('%/', #{examUploaderId}, '/%')
              limit 1;
    </select>

    <select id="getCardListByExamUploaderId" resultType="java.util.Map">
        SELECT
            answer_card_id          answerCardId,
             exam_id                examId,
            exam_uploader_id        examUploaderId,
            file_path               filePath,
            0                       correctStatus,
            correct_path            correctPath,
            correct_param           correctParam,
            page_number             pageNumber
        FROM t_answer_card
        where exam_uploader_id = #{examUploaderId}
          <if test="studentIds != null and studentIds.size() > 0">
              and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
          </if>
          ;
    </select>

    <select id="getBatchFileNames" resultType="java.lang.String" parameterType="map">
        select distinct file_name
        from t_answer_card
        where exam_uploader_id = #{examUploaderId} and batch_id = #{batchId};
    </select>

    <select id="getStudentCardTemplate" resultType="com.dongni.exam.card.bean.TemplateInfo">
        select
            template_type           templateType,
            template_code           templateCode
        from t_answer_card
        where exam_uploader_id = #{examUploaderId} and student_id = #{studentId}
        limit 1;
    </select>

    <select id="getStudentAnswerCardIdsByStudentId" parameterType="map" resultType="java.lang.Long">
        select answer_card_id
        from t_answer_card
        where exam_uploader_id = #{examUploaderId} and student_id = #{studentId}
        order by answer_card_id asc;
    </select>

    <select id="getTemplateCodes" resultType="com.dongni.exam.plan.bean.dto.StudentCardDTO">
        select distinct
               tac.template_code        templateCode,
               tac.template_type        templateType,
               tac.student_id           studentId
        from t_exam_uploader teu, t_answer_card tac
        where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
            and tac.exam_uploader_id = teu.exam_uploader_id
            and tac.student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>);
    </select>

    <select id="getCardRepeatGroupStudentIds" resultType="java.lang.Long">
        select distinct student_id
        from t_answer_card
        where exam_uploader_id in <foreach collection="examUploaderIds" separator="," item="item" open="(" close=")">#{item}</foreach>
        and error_code >> 2 &amp; 1 = 1;
    </select>

    <update id="removeRedundancyCards" parameterType="map">
        update t_answer_card
        set exam_uploader_id = -#{examUploaderId}
        where exam_uploader_id = #{examUploaderId}
            and student_id in (<foreach collection="deleteStudentIds" separator="," item="item">#{item}</foreach>);
    </update>

    <update id="removeRedundancyCorrectStudentCards" parameterType="com.dongni.exam.plan.bean.vo.UpdateResultCardVO">
        update t_answer_card tac
        left join t_answer_card correctTac
                on correctTac.exam_uploader_id = #{correctExamUploaderId}
                and tac.student_id = correctTac.student_id
                and tac.page_number = correctTac.page_number
                and substr(tac.file_name, #{correctStartIndex}) = substr(correctTac.file_name, #{startIndex})
        set tac.exam_uploader_id = -tac.exam_uploader_id,
            tac.student_id = -tac.student_id
        where tac.exam_uploader_id = #{examUploaderId}
          and correctTac.answer_card_id is null;
    </update>

    <select id="getCardRepeatGroup" resultType="java.lang.Integer">
        select count(distinct tac.exam_uploader_id, tac.answer_card_code)
        from t_exam_uploader teu, t_answer_card tac
        where
        <choose>
            <when test="newExamUploaderBO.schoolId > 0">
                teu.exam_uploader_id in <foreach collection="examUploaderIds" separator="," item="item" open="(" close=")">#{item}</foreach>
            </when>
            <otherwise>
                teu.exam_id = #{newExamUploaderBO.examId} and teu.paper_id = #{newExamUploaderBO.paperId} and teu.upload_status in (2, 3, 4, 5, 6)
            </otherwise>
        </choose>
        and tac.exam_uploader_id = teu.exam_uploader_id
        and tac.student_id in <foreach collection="studentIds" separator="," item="item" open="(" close=")">#{item}</foreach>
        and error_code >> 2 &amp; 1 = 1;
    </select>

    <select id="getAnswerCardVOList" parameterType="map" resultType="com.dongni.exam.newcard.bean.VO.AnswerCardVO">
        select
            answer_card_id  answerCardId,
            task_id         taskId,
            modify_status   modifyStatus,
            task_status     taskStatus
        from t_answer_card
        where answer_card_id in (<foreach collection="answerCardIds" separator="," item="item">#{item}</foreach>);
    </select>


    <update id="updateSyncAnswerCardCode" parameterType="com.dongni.exam.plan.bean.vo.UpdateResultCardVO">
        update t_answer_card tac
        left join t_answer_card tac2
            on tac2.exam_uploader_id = tac.exam_uploader_id
            and tac2.student_id = tac.student_id and tac2.page_number = 1
        set tac.answer_card_code = tac2.answer_card_code
        where tac.exam_uploader_id = #{examUploaderId};
    </update>

    <select id="getExceptionExamUploaderIds" resultType="java.lang.Long">
        select distinct teu.exam_uploader_id
        from t_exam_uploader teu, t_answer_card tac
        where teu.exam_id = #{examId} and teu.paper_id = #{paperId} and teu.upload_status in (2, 3, 4, 5,6)
            and tac.exam_uploader_id = teu.exam_uploader_id
            <if test="errorCode != null and errorCode > 0">
              and tac.error_code >> (#{errorCode} - 1) &amp; 1 = 1
            </if>;
    </select>

    <select id="getAnswerCardVOListByExamUploaderId" resultType="com.dongni.exam.newcard.bean.VO.AnswerCardVO">
        select
            answer_card_id      answerCardId,
            correct_param       correctParam,
            correct_status      correctStatus,
            file_path           filePath,
            exam_id             examId,
            exam_uploader_id    examUploaderId
        from t_answer_card
        where exam_uploader_id = #{examUploaderId} and correct_status = 0;
    </select>

    <select id="getCompleteExamUploaderIds" resultType="java.lang.Long">
        select exam_uploader_id
        from t_exam_uploader
        where exam_id = #{examId} and upload_status = 7;
    </select>

    <update id="updateCorrectPath" parameterType="com.dongni.exam.newcard.bean.VO.AnswerCardVO">
        update t_answer_card
        set correct_status= #{correctStatus},
            correct_path = #{correctPath}
        where answer_card_id = #{answerCardId};
    </update>

    <select id="getExamUploaderClassIds" resultType="java.lang.Long" parameterType="map">
        select /*+INL_JOIN(teu, tac, ter)*/
            distinct ter.class_id
        from t_exam_uploader teu, t_answer_card tac, t_exam_result ter
        where teu.exam_uploader_id = #{examUploaderId}
            and tac.exam_uploader_id = teu.exam_uploader_id and tac.page_number = 1
            and ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id and ter.student_id = tac.student_id
    </select>

    <select id="getWaitingCardList" parameterType="map" resultType="com.dongni.exam.newcard.bean.DTO.RecCardDTO">
        select
            answer_card_id                                              answerCardId,
            exam_uploader_id                                            examUploaderId,
            answer_card_code                                            answerCardCode,
            modifier_id                                                 userId,
            modifier_name                                               userName,
            batch_id                                                    batchId,
            template_type                                               templateType,
            student_id                                                  studentId,
            task_status                                                 taskStatus,
            file_name                                                   fileName,
            error_code >> 1 &amp; 1                                         cardLose,
            error_code >> 10 &amp; 1                                        uploadGroupExp,
            error_code &amp; b'00000000000000001111111111111111' > 0        needErrorExp,
            error_code is null                                          emptyErrorCode,
            modify_status                                               modifyStatus
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
            and task_id is null
            order by batch_id, answer_card_code
         limit #{size};
    </select>

    <update id="updateAppendExamUploaderToNormalOfFile" parameterType="map">
        update t_exam_uploader_file
            set exam_uploader_id = #{examUploaderId}
        where exam_uploader_id in
              (<foreach collection="appendExamUploaderIds" item="item" separator=",">#{item}</foreach>);
    </update>

    <update id="updateStudentIdByRecognitionId">
        update /*+INL_JOIN(tr, trc, trce, ter, tac)*/
            t_recognition tr, t_recognition_card trc,
            t_recognition_card_ext trce,t_answer_card tac,
            t_exam_result ter
        set tac.student_id = trce.relative_student_id,
            tac.class_id = ter.class_id,
            tac.student_exam_num = ter.student_exam_num,
            tac.student_num = ter.student_num
        where tr.recognition_id = #{recognitionId}
          and trc.recognition_id = tr.recognition_id
          and trc.student_id in (<foreach collection="studentIds" item="item" separator=",">#{item}</foreach>)
          and trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
          and ter.exam_id = tr.exam_id and ter.paper_id = tr.paper_id and ter.student_id = trce.relative_student_id
          and tac.answer_card_id = trc.answer_card_id;
    </update>

    <select id="getEducationExpStatistics" resultType="java.util.Map" parameterType="map">
        select
        <foreach collection="errorCodes" item="item" separator=",">
            count(distinct if((error_code >> (#{item.errorCode} - 1)) &amp; 1 = 1, exam_uploader_id + answer_card_code, null)) as #{item.key}
        </foreach>
        from t_answer_card
        where exam_uploader_id in (<foreach collection="examUploaderIds" separator="," item="item">#{item}</foreach>);
    </select>

    <select id="getAnswerCardStudentIds" resultType="java.lang.Long">
        select distinct student_id
        from t_exam_uploader teu, t_answer_card tac
        where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
            and tac.exam_uploader_id = teu.exam_uploader_id
            and tac.student_id in (<foreach collection="studentIds" item="item" separator=",">#{item}</foreach>)
    </select>
</mapper>
