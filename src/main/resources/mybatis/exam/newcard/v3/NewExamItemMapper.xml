<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.newcard.dao.NewExamItemDao">
    <update id="rollbackExamItems" parameterType="map">
        update t_exam_item
            set read_status = read_status + 10
        where exam_id = #{examId} and paper_id = #{paperId}
            and question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
            and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
            and read_status in (0, 1);
    </update>

    <delete id="deleteExamItemByCancelExamUploader" parameterType="map">
        delete tei.* from  t_exam_result ter
        join t_exam_item tei on tei.exam_id =  #{examUploaderBo.examId} and tei.paper_id = #{examUploaderBo.paperId}
        and tei.question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
        and tei.read_status in (10, 11)
        and tei.student_id = ter.student_id
        where ter.exam_id = #{examUploaderBo.examId} and ter.paper_id = #{examUploaderBo.paperId} and ter.school_id = #{examUploaderBo.schoolId}
        <if test="classIdList != null and classIdList.size() > 0">
            and ter.class_id in (<foreach collection="classIdList" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
    </delete>

    <delete id="deleteExamItemsByExamIdAndPaperIdAndStudentIds" parameterType="map">
        delete from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId}
        and question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
        and student_id in (<foreach collection="studentIds" item="item" separator=",">#{item}</foreach>);
    </delete>

    <select id="getStudentWithoutCardIds" parameterType="map" resultType="java.lang.Long">
        select tei.student_id from t_exam_result ter
        join t_exam_item tei on tei.exam_id = ter.exam_id
            and tei.paper_id = ter.paper_id
            and tei.student_id = ter.student_id
            and tei.question_number = #{questionNumber}
            and tei.read_status in (10, 11)
        left join (
            select distinct tac.student_id
            from t_exam_uploader teu,t_answer_card tac
            where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
            <if test="schoolId != null and schoolId > 0">
                and teu.school_id = #{schoolId}
            </if>
            and teu.upload_status in (2, 3, 4, 5, 6)
            and tac.exam_uploader_id = teu.exam_uploader_id
        ) tac on tac.student_id = tei.student_id
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
        <if test="schoolId != null and schoolId > 0">
            and ter.school_id = #{schoolId}
        </if>
        <if test="classIdList != null and classIdList.size() > 0">
            and ter.class_id in (<foreach collection="classIdList" separator="," item="item">#{item}</foreach>)
        </if>
        and tac.student_id is null;
    </select>

    <select id="getItemSaveImgUrls" parameterType="map" resultType="java.lang.String">
        select save_file_url from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId}
        and question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
        and student_id in (<foreach collection="studentIds" item="item" separator=",">#{item}</foreach>)
        and read_type = 2;
    </select>

    <select id="getStudentExamItemByExamUploader" parameterType="map" resultType="com.dongni.exam.plan.bean.dto.StudentCardDTO">
        select
               student_id       studentId
        from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId} and question_number = #{questionNumber}
            and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
            and read_status in (0, 1);
    </select>

    <select id="getStudentIdsByExamAndPaperAndStudentIds" resultType="java.lang.Long" parameterType="map">
        select student_id
        from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId}
          and question_number = #{questionNumber}
          and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
          and read_status in (0, 1);
    </select>

    <select id="getQuestionNumbers" resultType="java.lang.Long" parameterType="map">
        select distinct question_number
        from t_exam_item force index (`t_exam_item_student_id_exam_id_paper_id_index`)
        where exam_id = #{examId} and paper_id = #{paperId}
            and student_id in (select distinct student_id from t_answer_card where exam_uploader_id = #{examUploaderId});
    </select>
    <select id="getQuestionNumberBOList" parameterType="map"
            resultType="com.dongni.exam.health.check.bean.bo.ExamItemQuestionNumberBO">
        select /*+INL_JOIN(teu, tac, tei)*/
               question_number   questionNumber,
               count(1)          itemCount
        from t_exam_uploader teu, t_answer_card tac, t_exam_item tei
        where teu.exam_uploader_id = #{examUploaderId}
            and tac.exam_uploader_id = teu.exam_uploader_id and tac.page_number = 1
            and tei.exam_id = #{examId} and tei.paper_id = #{paperId} and tei.student_id = tac.student_id
            and tei.question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
            group by question_number;
    </select>

    <update id="incrementExamItemReadStatus" parameterType="map">
        update t_exam_item
            set read_status = 10 + read_status
        where exam_id = #{examId} and paper_id = #{paperId}
        and question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
        and student_id in (<foreach collection="studentIds" item="item" separator=",">#{item}</foreach>);
    </update>

    <update id="batchUpdateObjectiveItems" parameterType="map">
        <foreach collection="itemList" separator=";" item="item">
            update t_exam_item
                set recognition_value = #{item.recognitionValue},
                    error_code = 0
                    <if test="answer == 1">
                        , finally_score = #{item.finallyScore},
                        read_status = #{item.readStatus}
                    </if>
            where exam_item_id = #{item.examItemId}
        </foreach>
    </update>

    <select id="getStudentEIList" resultType="com.dongni.exam.newcard.bean.DTO.StudentEIDTO">
        select
            student_id                                          studentId,
            count(if(error_code >> 17 &amp; 1 = 1, 1, null ))   suspectProblem,
            count(if(error_code >> 19 &amp; 1 = 1, 1, null ))   mulChoice,
            count(if(error_code >> 20 &amp; 1 = 1, 1, null ))   lowConfident,
            count(if(error_code >> 21 &amp; 1 = 1, 1, null ))   cardBlank
        from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId}
        and question_number in (<foreach collection="questionNumbers" separator="," item="item">#{item}</foreach>)
        and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
        group by student_id;
    </select>

    <update id="updateStudentExamItemNoRelative" parameterType="map">
        update t_exam_item
        set exam_id = -exam_id,
            paper_id = -paper_id,
            student_id = -student_id
        where exam_id = #{examId} and paper_id = #{paperId}
            and question_number in (<foreach collection="qns" item="item" separator=",">#{item}</foreach>)
            and student_id in (<foreach collection="studentIdList" item="item" separator=",">#{item}</foreach>);
    </update>

    <update id="updateStudentIdWithMisMatch" parameterType="map">
        update t_exam_item tei, t_exam_item_mismatch teim
            set tei.student_id = -teim.correct_student_id
        where tei.exam_id = #{examId} and tei.paper_id = #{paperId}
            and teim.exam_id = #{examId} and teim.paper_id = #{paperId}
            and teim.student_id = tei.student_id and teim.question_number = tei.question_number;
     </update>

    <update id="updateExamItemStudentId2Positive">
        update t_exam_item
            set student_id = -student_id
        where exam_id = #{examId} and paper_id = #{paperId}
          and question_number in (<foreach collection="questionNumbers" separator="," item="item">#{item}</foreach>)
          and student_id &lt; 0;
    </update>

    <select id="getExamUploaderExamItemList" parameterType="map"
            resultType="com.dongni.exam.newcard.bean.DTO.ExamItemErrorCodeDTO">
        select /*+INL_JOIN(teu, tac, tei)*/
            tac.error_code >> 17 &amp; 1         suspectProblem,
            tac.error_code >> 19 &amp; 1         mulChoice,
            tac.error_code >> 20 &amp; 1         lowConfident,
            tac.error_code >> 21 &amp; 1         cardBlank,
            tac.low_confident_info           lowConfidentInfo,
            tei.exam_item_id                 examItemId,
            tei.recognition_value            recognitionValue,
            tei.student_id                   studentId,
            tei.question_number              questionNumber
        from t_exam_uploader teu, t_answer_card tac, t_exam_item tei
        where teu.exam_uploader_id = #{examUploaderId}
            and tac.exam_uploader_id = teu.exam_uploader_id and tac.page_number = 1 and tac.error_code &amp; b'11111111111111110000000000000000' > 1
            and tei.exam_id = teu.exam_id and tei.paper_id = teu.paper_id and tei.student_id = tac.student_id
            and tei.question_number in (<foreach collection="qns" item="item" separator=",">#{item}</foreach>);
    </select>

    <update id="batchUpdateExamItemsErrorCode" parameterType="map">
        <foreach collection="list" item="item" separator=";">
            update t_exam_item
                set error_code = b#{item.errorCode}
            where exam_item_id = #{item.examItemId}
        </foreach>
    </update>

    <update id="updateMisMatchExamId" parameterType="map">
        update t_exam_item_mismatch
            set exam_id = -exam_id
        where exam_uploader_id = #{examUploaderId};
    </update>

    <select id="getExamItemInvalid" resultType="com.dongni.exam.item.bean.entity.ExamItem">
        select
               tei.exam_item_id         examItemId,
               tei.question_number      questionNumber,
               tei.student_id           studentId
        from  t_exam_item tei, t_exam_item tei2
        where tei.exam_id = #{examId} and tei.paper_id = #{paperId}
          and tei2.exam_id = #{examId} and tei2.paper_id = #{paperId}
          and tei.student_id = -tei2.student_id and tei.question_number = tei2.question_number
          and tei.student_id > 0;
    </select>

    <update id="updateExamItemInvalid" parameterType="map">
        update  t_exam_item tei, t_exam_item tei2
        set tei.exam_id = -tei.exam_id,
            tei.paper_id = -tei.paper_id
        where tei.exam_id = #{examId} and tei.paper_id = #{paperId}
          and tei2.exam_id = #{examId} and tei2.paper_id = #{paperId}
          and tei.student_id = -tei2.student_id and tei.question_number = tei2.question_number
          and tei.student_id > 0;
    </update>

    <update id="updateReadStatus" parameterType="map">
        update t_exam_item tei
        set tei.read_status = tei.read_status - 10
        <if test="readStatus == 10">
            , tei.finally_score = 0
        </if>
        where tei.exam_id = #{examId} and tei.paper_id = #{paperId}
        and tei.question_number in
        (<foreach collection="qns" item="item" separator=",">#{item}</foreach>)
        and tei.student_id in
        (<foreach collection="stuIds" item="item" separator=",">#{item}</foreach>)
        and tei.read_status = #{readStatus};
    </update>

    <select id="getIntelligencePRIds" resultType="com.dongni.exam.newcard.bean.DTO.CoursePaperReadDTO" parameterType="map">
        select distinct
            tpr.paper_read_id  paperReadId,
            tpr.course_name    courseName
        from t_paper_read tpr, t_exam_item_intelligence teii
        where tpr.paper_read_id = teii.paper_read_id
          and tpr.course_name in (<foreach collection="courseNames" item="name" separator=",">#{name}</foreach>);
    </select>

<!--    <select id="getIntelligencePRIds" resultType="java.lang.Long">-->
<!--        select distinct paper_read_id from t_exam_item_intelligence-->
<!--        where paper_read_id &lt; #{paperReadId}-->
<!--        order by paper_read_id desc;-->
<!--    </select>-->

    <select id="getExamItemListByPaperReadId" resultType="com.dongni.exam.newcard.bean.IntelligenceItem">
        select tei.save_file_url        url,
               teii.recognition_value   rv,
               tei.exam_item_id       examItemId,
               tei.finally_score      finallyScore,
               tei.score_value        score
        from t_exam_item_intelligence teii, t_exam_item tei
        where teii.paper_read_id = #{paperReadId}
          and teii.exam_item_id = tei.exam_item_id;
    </select>

    <select id="getIntelligenceExamCourse" resultType="com.dongni.exam.newcard.bean.IntelligenceExamCourse">
        select course_name,
               exam_id,
               paper_id,
               course_id,
               paper_read_id
        from t_paper_read
        where paper_read_id in (<foreach collection="paperReadIds" separator="," item="id">#{id}</foreach>);
    </select>

    <select id="getExamItemListByEPQIds" resultType="com.dongni.exam.mark.ai.bean.dto.ExamItemDTO">
        select
            exam_item_id    examItemId,
            exam_id         examId,
            paper_id        paperId,
            student_id      studentId,
            save_file_url   saveFileUrl,
            question_number questionNumber
        from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId}
            and question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
            and read_status in (0, 1);
    </select>

    <select id="getExamItemListByIds" resultType="com.dongni.exam.mark.ai.bean.dto.ExamItemDTO">
        select
            exam_item_id    examItemId,
            exam_id         examId,
            paper_id        paperId,
            student_id      studentId,
            save_file_url   saveFileUrl,
            question_number questionNumber
        from t_exam_item
        where exam_item_id in (<foreach collection="examItemIds" separator="," item="item">#{item}</foreach>);
    </select>

    <update id="updateCardExamItemStatus">
        update t_exam_item
        set read_status = #{status}
        where student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
          and paper_id = #{paperId}
          and exam_id = #{examId};
    </update>

    <select id="getOneObjectiveExamItem" resultType="java.lang.Integer">
        select 1 from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId}
          and question_number in (<foreach collection="objectiveQns" item="item" separator=",">#{item}</foreach>)
          and read_status = 0
        limit 1;
    </select>

    <update id="updateExamItemStudentIdToNegative">
        update t_exam_item tei, t_recognition tr, t_recognition_card trc, t_recognition_card_ext trce
        set tei.student_id = -trce.relative_student_id,
            tei.modify_date_time = NOW()
        where tr.recognition_id = #{recognitionId}
          and trc.recognition_id = tr.recognition_id
          and trc.student_id in (<foreach collection="studentIds" item="item" separator=",">#{item}</foreach>)
          and trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
          and tei.exam_id = tr.exam_id and tei.paper_id = tr.paper_id and tei.student_id = trc.student_id
          and tei.question_number in (<foreach collection="qns" item="item" separator=",">#{item}</foreach>);
    </update>

    <update id="updateExamItemStudentIdToPositive">
        update t_exam_item tei, t_recognition tr, t_exam_result ter
            set tei.student_id = -tei.student_id,
                tei.class_id = ter.class_id,
                tei.school_id = ter.school_id,
                tei.modify_date_time = NOW()
        where tr.recognition_id = #{recognitionId}
          and tei.exam_id = tr.exam_id and tei.paper_id = tr.paper_id and tei.question_number in (<foreach collection="qns" item="item" separator=",">#{item}</foreach>)
          and tei.student_id in (<foreach collection="studentIds" item="item" separator=",">-#{item}</foreach>)
          and ter.exam_id = tei.exam_id and ter.paper_id = tei.paper_id and ter.student_id = -tei.student_id;
    </update>

</mapper>
