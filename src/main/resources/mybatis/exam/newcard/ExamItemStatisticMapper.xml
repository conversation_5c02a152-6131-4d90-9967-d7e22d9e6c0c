<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.card.common.ExamItemStatisticMapper">
    <!-- 查询客观题目有识别为空的学生 --> <!-- TODO sharding9 外部根据examUploaderId查询examId -->
    <select id="selectMultiObjectiveResult" resultType="map">
        select
        /*+INL_JOIN(teu, tac, ter, tei)*/
        ter.student_id studentId,
        ter.student_name studentName,
        ter.student_num studentNum,
        ter.student_exam_num studentExamNum,
        ter.class_name className,
        teu.exam_uploader_id   examUploaderId,
        ter.school_id schoolId,
        tac.template_type       templateType,
        tac.template_code       templateCode,
        sum(tei.finally_score) finallyScore,
        count(IF(tei.recognition_value='',TRUE,NULL)) blankCount,
        count(tei.exam_item_id) totalCount,
        <if test="params.resultStatus == 1">
            sum(if(tei.finally_score = 0,0,if(tei.finally_score = tqs.score_value,1,0.5))) / count(tei.finally_score) correctRate,
        </if>
        sum(tei.finally_score)/sum(tqs.score_value) scoreRate
        from t_exam_uploader teu,
            t_answer_card tac,
            t_exam_result ter,
            t_exam_item tei,
            t_question_structure tqs
        where <include refid="joinTablesCondition"></include>
        and tqs.paper_id = tei.paper_id and tqs.question_number = tei.question_number
        group by ter.student_id
        order by ${sortField} ${sortType},ter.student_id
        <if test="params.pageSize !=null and params.currentIndex != null">
            limit #{params.currentIndex, jdbcType=INTEGER},#{params.pageSize, jdbcType=INTEGER}
        </if>;
    </select>

    <!-- 查询客观题目有识别为空的学生 --> <!-- TODO sharding9 外部根据examUploaderId查询examId -->
    <select id="selectMultiQuestionObjectiveResult" resultType="map">
        select
        /*+INL_JOIN(teu, tac, ter, tei)*/
        tei.question_number                 questionNumber,
        tei.structure_number                structureNumber,
        teu.exam_uploader_id                examUploaderId,
        count(if (tei.recognition_value = '', TRUE, NULL ) ) blankCount,
        count(distinct tei.student_id) totalCount,
        <if test="params.resultStatus == 1">
            sum(if(tei.finally_score = 0,0,if(tei.finally_score = tqs.score_value,1,0.5))) / count(tei.finally_score) correctRate,
        </if>
        sum(tei.finally_score)/sum(tqs.score_value) scoreRate
        from t_exam_uploader teu,
            t_answer_card tac,
            t_exam_result ter,
            t_exam_item tei,
            t_question_structure tqs
        where <include refid="joinTablesCondition"></include>
        and tqs.paper_id = tei.paper_id and tqs.question_number = tei.question_number
        group by tei.question_number
        ORDER BY ${sortField} ${sortType}
        <if test="params.pageSize !=null and params.currentIndex != null">
            limit #{params.currentIndex, jdbcType=INTEGER},#{params.pageSize, jdbcType=INTEGER}
        </if>;
    </select>

    <!-- 查询主观题目 --> <!-- TODO sharding9 外部根据examUploaderId查询examId-->
    <select id="selectMultiQuestionSubjectiveResult" resultType="map">
        select
               /*+INL_JOIN(teu, tac, ter, tei)*/
               tei.question_number questionNumber,
               tei.structure_number structureNumber,
               count(distinct tei.student_id) totalCount
        from t_exam_uploader teu, t_answer_card tac, t_exam_result ter, t_exam_item tei
        where <include refid="joinTablesCondition"></include>
        group by tei.question_number;
    </select>


    <sql id="joinTablesCondition">
        teu.exam_uploader_id in <foreach collection="examUploaderIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        and tac.exam_uploader_id = teu.exam_uploader_id and tac.page_number = 1
        <if test="params.templateCode != null and params.templateCode != ''">
            and tac.template_code = #{params.templateCode}
        </if>
        <if test="params.studentIdList != null and params.studentIdList.size >0">
            and tac.student_id in
            <foreach collection="params.studentIdList" item="studentId" open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>
        <if test="params.classIds != null">
            and tac.class_id in <foreach collection="params.classIds" item="classId" open="(" close=")" separator=","> #{classId}</foreach>
        </if>
        and ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id
        <if test="params.scanType != null and params.scanType == 1">
            and ter.school_id = teu.school_id
        </if>
        and ter.student_id = tac.student_id
        <if test="params.resultStatus == 1">
            and ter.result_status = #{params.resultStatus}
        </if>
        <if test="params.searchValue != null and params.searchValue != ''">
            and (ter.student_name like concat('%',#{params.searchValue},'%') OR ter.student_exam_num like concat('%',#{params.searchValue},'%') )
        </if>
        and tei.exam_id = teu.exam_id and tei.paper_id = teu.paper_id
        <if test="questionNumbers != null and questionNumbers.size > 0">
            and tei.question_number in <foreach collection="questionNumbers" item="item" separator="," open="(" close=")">#{item}</foreach>
        </if>
        <if test="questionNumber != null and questionNumber != ''">
            and tei.question_number = #{questionNumber}
        </if>
        and tei.student_id = ter.student_id

    </sql>
    <!-- 查询客观题目的学生总数2 --> <!-- TODO sharding9 外部根据examUploaderId查询examId -->
    <select id="selectMultiObjectiveResultCountWithNull" resultType="int">
        select count(1) from (
        select /*+INL_JOIN(teu, tac, ter, tqs, tei)*/
        count(IF(tei.recognition_value='',TRUE,NULL)) blankCount
        from t_exam_uploader teu,t_answer_card tac,t_exam_result ter,t_exam_item tei
        where <include refid="joinTablesCondition"></include>
        and (tac.error_code  >> 21 &amp; 1 = 1) and tac.error_code > 0
        group by ter.student_id
        ) s where blankCount > 0 ;
    </select>

    <select id="selectMultiObjectiveResultWithNull" parameterType="map" resultType="map">
        select * from (
            select /*+INL_JOIN(teu, tac, ter, tei)*/
            ter.student_id studentId,
            ter.student_name studentName,
            ter.student_num studentNum,
            ter.student_exam_num studentExamNum,
            ter.class_name className,
            ter.school_id  schoolId,
            teu.exam_uploader_id   examUploaderId,
            sum(tei.finally_score) finallyScore,
            count(IF(tei.recognition_value='',TRUE,NULL)) blankCount,
            count(tei.exam_item_id) totalCount,
            <if test="params.resultStatus == 1">
                sum(if(tei.finally_score = 0,0,if(tei.finally_score = tqs.score_value,1,0.5))) / count(tei.finally_score) correctRate,
            </if>
            sum(tei.finally_score)/sum(tqs.score_value) scoreRate
            from t_exam_uploader teu,t_answer_card tac,t_exam_result ter,
                 t_exam_item tei, t_question_structure tqs
            where <include refid="joinTablesCondition"></include>
            and tqs.paper_id = tei.paper_id and tqs.question_number = tei.question_number
            and (tac.error_code  >> 21 &amp; 1 = 1) and tac.error_code > 0
            group by ter.student_id
        ) s where blankCount > 0 ORDER BY ${sortField} ${sortType}, studentId asc
        <if test="params.pageSize !=null and params.currentIndex != null">
            limit #{params.currentIndex, jdbcType=INTEGER},#{params.pageSize, jdbcType=INTEGER}
        </if>;
    </select>

    <select id="selectMultiQuestionObjectiveResultCountWithNull" resultType="int">
        select count(1) from (
        select /*+INL_JOIN(teu, tac, ter, tei)*/
        count(IF(tei.recognition_value='',TRUE,NULL)) blankCount
        from t_exam_uploader teu,t_answer_card tac,t_exam_result ter,t_exam_item tei
        where <include refid="joinTablesCondition"></include>
        and (tac.error_code  >> 21 &amp; 1 = 1)
        group by tei.question_number
        ) s where s.blankCount > 0
    </select>

    <select id="selectMultiQuestionObjectiveResultWithNull" parameterType="map" resultType="map">
        select * from (
        select /*+INL_JOIN(teu, tac, ter, tei)*/
        tei.question_number     questionNumber,
        tei.structure_number    structureNumber,
        count(IF(tei.recognition_value='',TRUE,NULL)) blankCount,
        count(tei.exam_item_id) totalCount,
        <if test="params.resultStatus == 1">
            sum(if(tei.finally_score = 0,0,if(tei.finally_score = tqs.score_value,1,0.5))) / count(tei.finally_score) correctRate,
        </if>
        sum(tei.finally_score)/sum(tqs.score_value) scoreRate
        from t_exam_uploader teu,t_answer_card tac,t_exam_result ter,
             t_exam_item tei,t_question_structure tqs
        where <include refid="joinTablesCondition"></include>
        and tqs.paper_id = tei.paper_id and tqs.question_number = tei.question_number
        and (tac.error_code  >> 21 &amp; 1 = 1) and tac.error_code > 0
        group by tei.question_number
        ) s where blankCount > 0 ORDER BY ${sortField} ${sortType}
        <if test="params.pageSize !=null and params.currentIndex != null">
            limit #{params.currentIndex, jdbcType=INTEGER},#{params.pageSize, jdbcType=INTEGER}
        </if>;
    </select>

    <select id="selectMultiQuestionSubjectiveResultDetail" resultType="map">
        select
        /*+INL_JOIN(teu, tac, ter, tqs, tei)*/
        distinct
        tei.question_number questionNumber,
        tei.structure_number structureNumber,
        tei.exam_item_id examItemId,
        tei.save_file_url saveFileUrl,
        tei.read_status readStatus,
        tei.finally_score finallyScore,
        tqs.score_value scoreValue,
        ter.student_id studentId,
        ter.student_name studentName,
        ter.student_num studentNum,
        ter.student_exam_num studentExamNum,
        tac.error_type_code errorTypeCode
        from t_exam_uploader teu,t_answer_card tac,t_exam_result ter, t_exam_item tei, t_question_structure tqs
        where <include refid="joinTablesCondition"></include>
        and tqs.paper_id = tei.paper_id and tqs.question_number = tei.question_number
        <if test="params.pageSize !=null and params.currentIndex != null">
            limit #{params.currentIndex, jdbcType=INTEGER},#{params.pageSize, jdbcType=INTEGER}
        </if>;
    </select>

    <select id="selectMultiQuestionSubjectiveResultDetailCount" parameterType="map" resultType="int">
        select
        /*+INL_JOIN(teu, tac, ter, tei)*/
        count(distinct tei.exam_item_id)
        from t_exam_uploader teu,t_answer_card tac,t_exam_result ter,t_exam_item tei
        where <include refid="joinTablesCondition"></include>
    </select>

    <select id="getAbsentTypeAnswerCardCodes" parameterType="map" resultType="string">
        select tmp.answer_card_code
        from (
            select  /*+INL_JOIN(teu,tac,tei)*/ distinct tac.student_id, tac.answer_card_code, tei.question_number from t_exam_uploader teu
            inner join t_answer_card tac on tac.exam_uploader_id = teu.exam_uploader_id and tac.error_code >> 6 &amp; 1 = 1
            left join t_exam_item tei on tei.exam_id = #{examId} and tei.paper_id = #{paperId}
                and tei.question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
                and tei.student_id = tac.student_id
                and (length(tei.recognition_value) &lt; 1 or tei.recognition_value is null)
            where teu.exam_uploader_id in (<foreach collection="examUploaderIds" item="item" separator=",">#{item}</foreach>)
        ) tmp
        group by tmp.answer_card_code
        <if test="absentType != null and absentType == 1">
            having count(1) = #{numberSize}
        </if>
        <if test="absentType != null and absentType == 2">
            having count(1) &lt; #{numberSize}
        </if>
    </select>

    <!-- 查询学生 --> <!-- TODO sharding9 外部根据examUploaderId查询examId -->
    <select id="selectStudentDetailResult" resultType="map">
        select
        /*+INL_JOIN(teu, tac, ter, tei)*/
        ter.student_id studentId,
        ter.student_name studentName,
        ter.student_num studentNum,
        ter.student_exam_num studentExamNum,
        ter.class_name className,
        teu.exam_uploader_id   examUploaderId,
        ter.school_id schoolId,
        tac.template_type       templateType,
        tac.template_code       templateCode,
        0 finallyScore,
        0 blankCount,
        0 totalCount,
        <if test="params.resultStatus == 1">
            0 correctRate,
        </if>
        0 scoreRate
        from t_exam_uploader teu,
        t_answer_card tac,
        t_exam_result ter
        where
        teu.exam_uploader_id in <foreach collection="examUploaderIds" item="item" separator="," open="(" close=")">#{item}</foreach>
        and tac.exam_uploader_id = teu.exam_uploader_id and tac.page_number = 1
        <if test="params.templateCode != null and params.templateCode != ''">
            and tac.template_code = #{params.templateCode}
        </if>
        <if test="params.studentIdList != null and params.studentIdList.size >0">
            and tac.student_id in
            <foreach collection="params.studentIdList" item="studentId" open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>
        <if test="params.classIds != null">
            and tac.class_id in <foreach collection="params.classIds" item="classId" open="(" close=")" separator=","> #{classId}</foreach>
        </if>
        and ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id and ter.school_id = teu.school_id
        and ter.student_id = tac.student_id
        <if test="params.resultStatus == 1">
            and ter.result_status = #{params.resultStatus}
        </if>
        <if test="params.searchValue != null and params.searchValue != ''">
            and (ter.student_name like concat('%',#{params.searchValue},'%') OR ter.student_exam_num like concat('%',#{params.searchValue},'%') )
        </if>
        group by ter.student_id
        order by ${sortField} ${sortType},ter.student_id
        <if test="params.pageSize !=null and params.currentIndex != null">
            limit #{params.currentIndex, jdbcType=INTEGER},#{params.pageSize, jdbcType=INTEGER}
        </if>;
    </select>
</mapper>