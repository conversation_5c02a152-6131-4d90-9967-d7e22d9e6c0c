<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="KnowledgeGraspCleanMapper">

    <!-- 获取考试 -->
    <select id="getExam" parameterType="map" resultType="map">
        SELECT
        exam_id id,
        grade_type gradeType,
        start_date date
        FROM t_exam
        WHERE exam_id = #{examId}
    </select>

    <!-- 获取试卷对应的考试 -->
    <select id="getExamIdByPaper" parameterType="map" resultType="long">
        SELECT DISTINCT exam_id
        FROM t_exam_paper
        WHERE paper_id = #{paperId}
    </select>

    <!-- 获取学生考试的知识点统计总数 -->
    <select id="getExamStudentKnowledgeCount" parameterType="map" resultType="java.lang.String">
        SELECT knowledge_id
        FROM t_exam_student_knowledge
        WHERE exam_id = #{examId}  group by knowledge_id
    </select>

    <!-- 从年级获取学生考试的知识点统计总数 -->
    <select id="getExamStudentKnowledgeCountFromGrade" parameterType="map" resultType="string">
        SELECT knowledge_id
        FROM t_exam_grade_knowledge
        WHERE exam_id = #{examId}  group by knowledge_id
    </select>


    <!-- 获取学生考试的知识点统计 -->
    <select id="getExamStudentKnowledgeByKnowledgeId" parameterType="map" resultType="map">
        SELECT
        /*+USE_INDEX(tsks,logic),USE_INDEX(tesk,t_exam_know_stu)*/
        tsks.student_knowledge_statistics_id id,
        tsks.grade_id gradeId,
        tsks.class_id classId,
        tsks.student_id studentId,
        tsks.course_id courseId,
        tsks.knowledge_id knowledgeId,
        tsks.easy_score_sum                  easySum,
        tsks.middle_score_sum                middleSum,
        tsks.difficulty_score_sum            difficultySum,
        tsks.easy_check_count                easyCount,
        tsks.middle_check_count              middleCount,
        tsks.difficulty_check_count          difficultyCount,
        tsks.easy_score_rate                 easyRate,
        tsks.middle_score_rate               middleRate,
        tsks.difficulty_score_rate           difficultyRate,
        tsks.last_check_time                 lastCheckTime
        FROM t_student_knowledge_statistics tsks
        INNER JOIN t_exam_student_knowledge tesk ON tsks.student_id = tesk.student_id AND tsks.knowledge_id = tesk.knowledge_id
        WHERE tesk.exam_id = #{examId} and tesk.knowledge_id = #{knowledgeId}
            AND tesk.student_id IN
            <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
                #{studentId}
            </foreach>

    </select>

    <!-- 更新综合知识点掌握度 -->
    <insert id="saveStudentKnowledge" parameterType="map">
        INSERT INTO t_student_knowledge_statistics(
        student_knowledge_statistics_id,
        grade_id,
        class_id,
        student_id,
        course_id,
        knowledge_id,
        knowledge_grasp,
        easy_grasp,
        middle_grasp,
        difficulty_grasp,
        check_count,
        easy_score_sum,
        middle_score_sum,
        difficulty_score_sum,
        easy_check_count,
        middle_check_count,
        difficulty_check_count,
        easy_score_rate,
        middle_score_rate,
        difficulty_score_rate,
        last_check_time,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="sks" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.gradeId},
            #{item.classId},
            #{item.studentId},
            #{item.courseId},
            #{item.knowledgeId},
            #{item.knowledgeGrasp},
            #{item.easyGrasp},
            #{item.middleGrasp},
            #{item.difficultyGrasp},
            #{item.checkTotal},
            #{item.easySum},
            #{item.middleSum},
            #{item.difficultySum},
            #{item.easyCount},
            #{item.middleCount},
            #{item.difficultyCount},
            #{item.easyRate},
            #{item.middleRate},
            #{item.difficultyRate},
            #{item.lastCheckTime},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        knowledge_grasp=VALUES(knowledge_grasp),
        easy_grasp=VALUES(easy_grasp),
        middle_grasp=VALUES(middle_grasp),
        difficulty_grasp=VALUES(difficulty_grasp),
        check_count=VALUES(check_count),
        easy_score_sum=VALUES(easy_score_sum),
        middle_score_sum=VALUES(middle_score_sum),
        difficulty_score_sum=VALUES(difficulty_score_sum),
        easy_check_count=VALUES(easy_check_count),
        middle_check_count=VALUES(middle_check_count),
        difficulty_check_count=VALUES(difficulty_check_count),
        easy_score_rate=VALUES(easy_score_rate),
        middle_score_rate=VALUES(middle_score_rate),
        difficulty_score_rate=VALUES(difficulty_score_rate),
        last_check_time=VALUES(last_check_time),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 删除学生考试知识点 -->
    <delete id="deleteExamStudentKnowledge" parameterType="map">
        DELETE FROM t_exam_student_knowledge
        WHERE exam_id = #{examId}
          AND paper_id = #{paperId}
          AND class_id = #{classId}
    </delete>

    <!-- 获取班级考试的知识点统计总数 -->
    <select id="getExamClassKnowledgeCount" parameterType="map" resultType="int">
        SELECT COUNT(1)
        FROM t_class_knowledge_statistics tsks
        INNER JOIN t_exam_class_knowledge tesk ON tsks.class_id = tesk.class_id AND tsks.knowledge_id = tesk.knowledge_id
        WHERE tesk.exam_id = #{examId}
    </select>

    <!-- 获取班级考试的知识点统计 -->
    <select id="getExamClassKnowledge" parameterType="map" resultType="map">
        SELECT
        tsks.class_knowledge_statistics_id id,
        tsks.grade_id gradeId,
        tsks.class_id classId,
        tsks.course_id courseId,
        tsks.knowledge_id knowledgeId,
        tsks.easy_score_sum                  easySum,
        tsks.middle_score_sum                middleSum,
        tsks.difficulty_score_sum            difficultySum,
        tsks.easy_check_count                easyCount,
        tsks.middle_check_count              middleCount,
        tsks.difficulty_check_count          difficultyCount,
        tsks.easy_score_rate                 easyRate,
        tsks.middle_score_rate               middleRate,
        tsks.difficulty_score_rate           difficultyRate,
        tsks.last_check_time                 lastCheckTime
        FROM t_class_knowledge_statistics tsks
        INNER JOIN t_exam_class_knowledge tesk ON tsks.class_id = tesk.class_id AND tsks.knowledge_id = tesk.knowledge_id
        WHERE tesk.exam_id = #{examId}
        LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </select>

    <!-- 更新综合知识点掌握度 -->
    <insert id="saveClassKnowledge" parameterType="map">
        INSERT INTO t_class_knowledge_statistics(
        class_knowledge_statistics_id,
        grade_id,
        class_id,
        course_id,
        knowledge_id,
        knowledge_grasp,
        easy_grasp,
        middle_grasp,
        difficulty_grasp,
        check_count,
        easy_score_sum,
        middle_score_sum,
        difficulty_score_sum,
        easy_check_count,
        middle_check_count,
        difficulty_check_count,
        easy_score_rate,
        middle_score_rate,
        difficulty_score_rate,
        last_check_time,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="sks" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.gradeId},
            #{item.classId},
            #{item.courseId},
            #{item.knowledgeId},
            #{item.knowledgeGrasp},
            #{item.easyGrasp},
            #{item.middleGrasp},
            #{item.difficultyGrasp},
            #{item.checkTotal},
            #{item.easySum},
            #{item.middleSum},
            #{item.difficultySum},
            #{item.easyCount},
            #{item.middleCount},
            #{item.difficultyCount},
            #{item.easyRate},
            #{item.middleRate},
            #{item.difficultyRate},
            #{item.lastCheckTime},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        knowledge_grasp=VALUES(knowledge_grasp),
        easy_grasp=VALUES(easy_grasp),
        middle_grasp=VALUES(middle_grasp),
        difficulty_grasp=VALUES(difficulty_grasp),
        check_count=VALUES(check_count),
        easy_score_sum=VALUES(easy_score_sum),
        middle_score_sum=VALUES(middle_score_sum),
        difficulty_score_sum=VALUES(difficulty_score_sum),
        easy_check_count=VALUES(easy_check_count),
        middle_check_count=VALUES(middle_check_count),
        difficulty_check_count=VALUES(difficulty_check_count),
        easy_score_rate=VALUES(easy_score_rate),
        middle_score_rate=VALUES(middle_score_rate),
        difficulty_score_rate=VALUES(difficulty_score_rate),
        last_check_time=VALUES(last_check_time),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 删除班级考试知识点 -->
    <delete id="deleteExamClassKnowledge" parameterType="map">
        DELETE FROM t_exam_class_knowledge WHERE exam_id = #{examId}
    </delete>


    <!-- 获取年级考试的知识点统计总数 -->
    <select id="getExamGradeKnowledgeCount" parameterType="map" resultType="int">
        SELECT COUNT(1)
        FROM t_grade_knowledge_statistics tsks
        INNER JOIN t_exam_grade_knowledge tesk ON tsks.grade_id = tesk.grade_id AND tsks.knowledge_id = tesk.knowledge_id
        WHERE tesk.exam_id = #{examId}
    </select>

    <!-- 获取年级考试的知识点统计 -->
    <select id="getExamGradeKnowledge" parameterType="map" resultType="map">
        SELECT
        tsks.grade_knowledge_statistics_id id,
        tsks.grade_id gradeId,
        tsks.course_id courseId,
        tsks.knowledge_id knowledgeId,
        tsks.easy_score_sum                  easySum,
        tsks.middle_score_sum                middleSum,
        tsks.difficulty_score_sum            difficultySum,
        tsks.easy_check_count                easyCount,
        tsks.middle_check_count              middleCount,
        tsks.difficulty_check_count          difficultyCount,
        tsks.easy_score_rate                 easyRate,
        tsks.middle_score_rate               middleRate,
        tsks.difficulty_score_rate           difficultyRate,
        tsks.last_check_time                 lastCheckTime
        FROM t_grade_knowledge_statistics tsks
        INNER JOIN t_exam_grade_knowledge tesk ON tsks.grade_id = tesk.grade_id AND tsks.knowledge_id = tesk.knowledge_id
        WHERE tesk.exam_id = #{examId}
        LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </select>

    <!-- 更新综合知识点掌握度 -->
    <insert id="saveGradeKnowledge" parameterType="map">
        INSERT INTO t_grade_knowledge_statistics(
        grade_knowledge_statistics_id,
        grade_id,
        course_id,
        knowledge_id,
        knowledge_grasp,
        easy_grasp,
        middle_grasp,
        difficulty_grasp,
        check_count,
        easy_score_sum,
        middle_score_sum,
        difficulty_score_sum,
        easy_check_count,
        middle_check_count,
        difficulty_check_count,
        easy_score_rate,
        middle_score_rate,
        difficulty_score_rate,
        last_check_time,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="sks" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.gradeId},
            #{item.courseId},
            #{item.knowledgeId},
            #{item.knowledgeGrasp},
            #{item.easyGrasp},
            #{item.middleGrasp},
            #{item.difficultyGrasp},
            #{item.checkTotal},
            #{item.easySum},
            #{item.middleSum},
            #{item.difficultySum},
            #{item.easyCount},
            #{item.middleCount},
            #{item.difficultyCount},
            #{item.easyRate},
            #{item.middleRate},
            #{item.difficultyRate},
            #{item.lastCheckTime},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        knowledge_grasp=VALUES(knowledge_grasp),
        easy_grasp=VALUES(easy_grasp),
        middle_grasp=VALUES(middle_grasp),
        difficulty_grasp=VALUES(difficulty_grasp),
        check_count=VALUES(check_count),
        easy_score_sum=VALUES(easy_score_sum),
        middle_score_sum=VALUES(middle_score_sum),
        difficulty_score_sum=VALUES(difficulty_score_sum),
        easy_check_count=VALUES(easy_check_count),
        middle_check_count=VALUES(middle_check_count),
        difficulty_check_count=VALUES(difficulty_check_count),
        easy_score_rate=VALUES(easy_score_rate),
        middle_score_rate=VALUES(middle_score_rate),
        difficulty_score_rate=VALUES(difficulty_score_rate),
        last_check_time=VALUES(last_check_time),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 删除年级考试知识点 -->
    <delete id="deleteExamGradeKnowledge" parameterType="map">
        DELETE FROM t_exam_grade_knowledge WHERE exam_id = #{examId}
    </delete>


    <!-- 获取区域考试的知识点统计总数 -->
    <select id="getExamAreaKnowledgeCount" parameterType="map" resultType="int">
        SELECT COUNT(1)
        FROM t_area_knowledge_statistics tsks
        INNER JOIN t_exam_area_knowledge tesk ON tsks.area_id = tesk.area_id AND tsks.knowledge_id = tesk.knowledge_id
        WHERE tesk.exam_id = #{examId}
    </select>

    <!-- 获取区域考试的知识点统计 -->
    <select id="getExamAreaKnowledge" parameterType="map" resultType="map">
        SELECT
        tsks.area_knowledge_statistics_id id,
        tsks.area_id areaId,
        tsks.course_id courseId,
        tsks.knowledge_id knowledgeId,
        tsks.easy_score_sum                  easySum,
        tsks.middle_score_sum                middleSum,
        tsks.difficulty_score_sum            difficultySum,
        tsks.easy_check_count                easyCount,
        tsks.middle_check_count              middleCount,
        tsks.difficulty_check_count          difficultyCount,
        tsks.easy_score_rate                 easyRate,
        tsks.middle_score_rate               middleRate,
        tsks.difficulty_score_rate           difficultyRate,
        tsks.last_check_time                 lastCheckTime
        FROM t_area_knowledge_statistics tsks
        INNER JOIN t_exam_area_knowledge tesk ON tsks.area_id = tesk.area_id AND tsks.knowledge_id = tesk.knowledge_id
        WHERE tesk.exam_id = #{examId}
        LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </select>

    <!-- 更新综合知识点掌握度 -->
    <insert id="saveAreaKnowledge" parameterType="map">
        INSERT INTO t_area_knowledge_statistics(
        area_knowledge_statistics_id,
        area_id,
        course_id,
        knowledge_id,
        knowledge_grasp,
        easy_grasp,
        middle_grasp,
        difficulty_grasp,
        check_count,
        easy_score_sum,
        middle_score_sum,
        difficulty_score_sum,
        easy_check_count,
        middle_check_count,
        difficulty_check_count,
        easy_score_rate,
        middle_score_rate,
        difficulty_score_rate,
        last_check_time,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="sks" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.areaId},
            #{item.courseId},
            #{item.knowledgeId},
            #{item.knowledgeGrasp},
            #{item.easyGrasp},
            #{item.middleGrasp},
            #{item.difficultyGrasp},
            #{item.checkTotal},
            #{item.easySum},
            #{item.middleSum},
            #{item.difficultySum},
            #{item.easyCount},
            #{item.middleCount},
            #{item.difficultyCount},
            #{item.easyRate},
            #{item.middleRate},
            #{item.difficultyRate},
            #{item.lastCheckTime},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        knowledge_grasp=VALUES(knowledge_grasp),
        easy_grasp=VALUES(easy_grasp),
        middle_grasp=VALUES(middle_grasp),
        difficulty_grasp=VALUES(difficulty_grasp),
        check_count=VALUES(check_count),
        easy_score_sum=VALUES(easy_score_sum),
        middle_score_sum=VALUES(middle_score_sum),
        difficulty_score_sum=VALUES(difficulty_score_sum),
        easy_check_count=VALUES(easy_check_count),
        middle_check_count=VALUES(middle_check_count),
        difficulty_check_count=VALUES(difficulty_check_count),
        easy_score_rate=VALUES(easy_score_rate),
        middle_score_rate=VALUES(middle_score_rate),
        difficulty_score_rate=VALUES(difficulty_score_rate),
        last_check_time=VALUES(last_check_time),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 删除区域考试知识点 -->
    <delete id="deleteExamAreaKnowledge" parameterType="map">
        DELETE FROM t_exam_area_knowledge WHERE exam_id = #{examId}
    </delete>

</mapper>