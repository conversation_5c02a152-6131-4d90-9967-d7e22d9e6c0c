<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="HallRuleMapper">

    <!-- 获取配置方案 -->
    <select id="getHallGlobalRule" parameterType="map" resultType="map">
        SELECT
        rule_code ruleCode,
        rule_name ruleName,
        rule_desc ruleDesc,
        rule_type ruleType,
        rule_status ruleStatus
        FROM t_hall_global_rule
        WHERE rule_code = #{ruleCode}
    </select>

    <!-- 获取配置方案 -->
    <select id="getHallRule" parameterType="map" resultType="map">
        SELECT
        hall_id hallId,
        rule_code ruleCode,
        rule_json ruleJson,
        rule_type ruleType
        FROM t_hall_rule
        WHERE rule_type = #{ruleType}
        AND hall_id = #{hallId}
    </select>


    <!-- 保存考务配置 -->
    <insert id="saveHallRule" parameterType="map">
        INSERT INTO t_hall_rule(
            hall_id,
            rule_code,
            rule_json,
            rule_type,
            creator_id,creator_name,create_date_time,
            modifier_id,modifier_name,modify_date_time
        )VALUES (
            #{hallId},
            #{ruleCode},
            #{ruleJson},
            #{ruleType},
            #{userId},#{userName},#{currentTime},
            #{userId},#{userName},#{currentTime}
        )ON DUPLICATE KEY UPDATE
          rule_json=VALUES(rule_json),
          rule_code=VALUES(rule_code),
          modifier_id=VALUES(modifier_id),
          modifier_name=VALUES(modifier_name),
          modify_date_time=VALUES(modify_date_time)
    </insert>


</mapper>