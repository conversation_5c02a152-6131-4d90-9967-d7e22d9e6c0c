<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamStatTemplateMapper">

  <select id="getExamStatTemplateListByQuery" parameterType="com.dongni.analysis.view.report.bean.dto.ExamStatTemplateQueryDTO"
    resultType="com.dongni.analysis.view.report.bean.ExamStatTemplateVO">
    select
        exam_stat_template_id examStatTemplateId,
        exam_stat_template_name examStatTemplateName,
        modify_date_time modifyDateTime
    from t_exam_stat_template
    where grade_id = #{gradeId}
      and exam_stat_template_type = #{examStatTemplateType}
    <if test="stage != null">
      and stage = #{stage}
    </if>
    <if test="creatorId != null">
      and creator_id = #{creatorId}
    </if>
    and deleted = 0
    order by create_date_time desc, examStatTemplateId desc
  </select>

  <select id="getExamStatTemplateIdById" parameterType="long" resultType="long">
    select exam_stat_template_id
    from t_exam_stat_template
    where exam_stat_template_id = #{examStatTemplateId} and deleted = 0
  </select>

  <insert id="insertExamStatTemplate" parameterType="com.dongni.analysis.view.report.bean.dto.ExamStatTemplateInsertDTO"
    useGeneratedKeys="true" keyColumn="exam_stat_template_id" keyProperty="examStatTemplateId">
    insert into t_exam_stat_template (
        exam_stat_template_name,
        exam_stat_template_type,
        grade_id,
        stage,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time)
    values (
        #{examStatTemplateName},
        #{examStatTemplateType},
        #{gradeId},
        #{stage},
        #{userId},
        #{userName},
        now(),
        #{userId},
        #{userName},
        now()
    )
  </insert>

  <update id="updateExamStatTemplateById" parameterType="com.dongni.analysis.view.report.bean.dto.ExamStatTemplateUpdateDTO">
    update t_exam_stat_template
    set exam_stat_template_name = #{examStatTemplateName},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = now()
      where exam_stat_template_id = #{examStatTemplateId}
  </update>

  <update id="deleteExamStatTemplateById" parameterType="com.dongni.analysis.view.report.bean.dto.ExamStatTemplateUpdateDTO">
    update t_exam_stat_template
    set deleted = 1,
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = now()
    where exam_stat_template_id = #{examStatTemplateId}
  </update>

</mapper>
