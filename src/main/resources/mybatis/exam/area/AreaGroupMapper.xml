<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="AreaGroupMapper">

    <resultMap id="AreaGroup" type="map">
        <id column="area_group_id"  property="areaGroupId"/>
        <result column="area_id"  property="areaId"/>
        <result column="course_id"  property="courseId"/>
        <result column="exam_id"  property="examId"/>
        <result column="stat_id"  property="statId"/>
        <result column="group_name"  property="groupName"/>
        <collection property="areaGroupSchool" javaType="list" ofType="map">
            <result column="school_id" property="schoolId"/>
            <result column="school_name" property="schoolName"/>
        </collection>
    </resultMap>

    <!-- 新增区域报告 -->
    <insert id="insertAreaGroup" parameterType="map" useGeneratedKeys="true" keyColumn="area_group_id" keyProperty="areaGroupId">
        INSERT INTO t_area_group(
        area_id,
        course_id,
        exam_id,
        stat_id,
        group_name,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )VALUES
            (
            #{areaId},
            #{courseId},
            #{examId},
            #{statId},
            #{groupName},
            #{userId},#{userName},#{currentTime},
            #{userId},#{userName},#{currentTime}
            )
        ON DUPLICATE KEY UPDATE
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 新增区域报告 -->
    <insert id="insertAreaGroupSchool" parameterType="map" >
        INSERT INTO t_area_group_school(
            area_group_id,
            school_id,
            school_name,
            creator_id,creator_name,create_date_time,
            modifier_id,modifier_name,modify_date_time
        )VALUES
        <foreach collection="areaGroupSchool" item="item" separator=",">
            (
                #{areaGroupId},
                #{item.schoolId},
                #{item.schoolName},
                #{userId},#{userName},#{currentTime},
                #{userId},#{userName},#{currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            modifier_id=VALUES(modifier_id),
            modifier_name=VALUES(modifier_name),
            modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 获取区域报告自定义分组 -->
    <select id="getAreaGroup" parameterType="map" resultMap="AreaGroup">
        SELECT
            tag.area_group_id ,
            tag.area_id ,
            tag.course_id ,
            tag.exam_id ,
            tag.stat_id,
            tag.group_name,
            tags.school_id,
            tags.school_name
        FROM t_area_group tag, t_area_group_school tags
        WHERE tag.area_group_id = tags.area_group_id
              and area_id = #{areaId}
              and course_id=#{courseId}
              and exam_id = #{examId}
              and stat_id = #{statId}
    </select>

    <!-- 获取区域"全部"分组的areaGroupId -->
    <select id="getAllAreaGroupId" parameterType="map" resultType="long">
        SELECT area_group_id
        FROM t_area_group
        WHERE area_id = #{areaId}
        <if test="userType == 14" >
            AND course_id= 0
        </if>
        AND exam_id = #{examId}
        AND stat_id = #{statId}
        AND group_name = '全部'
        limit 1
    </select>

    <delete id="deleteAreaGroup" parameterType="map">
        DELETE t1, t2 FROM t_area_group t1, t_area_group_school t2
        WHERE t1.area_group_id = t2.area_group_id
              AND t1.area_group_id = #{areaGroupId}
    </delete>

    <!-- 获取考试区域以及子区域 -->
    <select id="getExamAreaByAreaCode" parameterType="map" resultType="long">
        SELECT
        area_id areaId
        FROM t_exam_area
        WHERE exam_id=#{examId} AND area_code like concat('%',#{areaCode},'%')
    </select>

    <!-- 获取考试报告指标配置 -->
    <select id="getExamStatIndex" parameterType="map" resultType="map">
        SELECT
            index_name indexName,
            index_code indexCode,
            index_type_code indexTypeCode,
            index_type indexType,
            default_show_status defaultShowStatus,
            group_index groupIndex
        FROM t_exam_stat_index where index_status = 1
    </select>

    <!-- 获取分组学校id -->
    <select id="getGroupSchool" parameterType="map" resultType="long">
        SELECT
            school_id
        FROM t_area_group_school where area_group_id = #{areaGroupId}
    </select>

    <select id="getDefaultStatAreaGroupIds" parameterType="map" resultType="long">
        select tar.area_group_id
        from t_area_group tar
        inner join
            (SELECT area_id, course_id, min(create_date_time) create_date_time
            FROM t_area_group
            WHERE exam_id = #{examId}
            and stat_id = 0
            and group_name = '全部'
            group by area_id, course_id
                ) t on tar.area_id = t.area_id and tar.course_id = t.course_id and tar.create_date_time = t.create_date_time
        where tar.exam_id = #{examId}
          and tar.stat_id = 0
          and tar.group_name = '全部'
    </select>

    <insert id="addSchoolToDefaultStatAreaGroup" parameterType="map">
        INSERT INTO t_area_group_school(
        area_group_id,
        school_id,
        school_name,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )VALUES
        <foreach collection="schoolList" item="item" separator=",">
            (
            #{item.areaGroupId},
            #{item.schoolId},
            #{item.schoolName},
            #{userId},#{userName},#{currentTime},
            #{userId},#{userName},#{currentTime}
            )
        </foreach>
    </insert>
    
    <delete id="removeSchoolFromDefaultStatAreaGroup" parameterType="map">
        delete
        from t_area_group_school
        where area_group_id in (
            select tar.area_group_id
            from t_area_group tar
            inner join (
                SELECT area_id, course_id, min(create_date_time) create_date_time
                FROM t_area_group
                WHERE exam_id = #{examId}
                and stat_id = 0
                and group_name = '全部'
                group by area_id, course_id) t
                on tar.area_id = t.area_id and tar.course_id = t.course_id and tar.create_date_time = t.create_date_time
            where tar.exam_id = #{examId}
              and tar.stat_id = 0
              and tar.group_name = '全部'
            ) and school_id in
            <foreach collection="schoolIds" item="schoolId" open="(" separator="," close=")">
                #{schoolId}
            </foreach>
    </delete>
</mapper>