<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.card.common.ExamCommonMapper">

    <select id="getExamUploader" resultType="com.dongni.exam.card.common.bean.ExamUploader" parameterType="long">
        select exam_uploader_id examUploaderId, exam_id examId, paper_id paperId
        from t_exam_uploader
        where exam_uploader_id = #{examUploaderId}
    </select>


    <select id="getExamUploaderIds" parameterType="long" resultType="long">
        SELECT exam_uploader_id examUploaderId
        FROM t_exam_uploader
        WHERE exam_id = #{examId}
          AND paper_id = #{paperId};
    </select>

    <select id="getStudentIdsByUploaderId" resultType="java.lang.Long" parameterType="long">
        select distinct student_id
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
          and student_id > 0
    </select>


    <delete id="deleteExamUploaderFile" parameterType="long">
        delete
        from t_exam_uploader_file
        where exam_uploader_id = #{examUploaderId}
    </delete>

    <delete id="deleteExamItem">
        delete
        from t_exam_item
        where exam_id = #{examId}
        and paper_id = #{paperId}
        and student_id in
        <foreach collection="studentIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </delete>

    <delete id="deleteAnswerCard" parameterType="long">
        delete
        from t_answer_card
        where exam_uploader_id = #{examUploaderId}
    </delete>

    <select id="getQuestionNumber" resultType="java.lang.Integer">
        select question_number
        from t_question_structure
        where paper_id = #{paperId}
          and read_type = #{readType};
    </select>

    <select id="getUnfinishedUploader" resultType="java.lang.Integer">
        select 1
        FROM t_exam_uploader
        WHERE exam_id = #{examId}
          AND paper_id = #{paperId}
          AND (upload_status > 2 and upload_status &lt; 7)
          limit 1
    </select>

    <select id="getUnfinishedClassUploader" resultType="java.lang.Integer">
        select 1
        FROM t_exam_uploader teu, t_exam_uploader_class teuc
        WHERE teu.exam_id = #{examId}
          AND teu.paper_id = #{paperId}
          AND (teu.upload_status > 2 and teu.upload_status &lt; 7)
          AND teuc.exam_uploader_id = teu.exam_uploader_id and teuc.class_id = #{classId}
        limit 1;
    </select>

    <select id="getUnfinishedClassCard" resultType="java.lang.Integer">
        select 1
        from t_exam_uploader teu,
             t_answer_card tac
        where teu.exam_id = #{examId}
          and teu.paper_id = #{paperId}
          and tac.class_id = #{classId}
          and (teu.upload_status > 2 and teu.upload_status &lt; 7)
          and teu.exam_uploader_id = tac.exam_uploader_id
        limit 1;
    </select>
</mapper>