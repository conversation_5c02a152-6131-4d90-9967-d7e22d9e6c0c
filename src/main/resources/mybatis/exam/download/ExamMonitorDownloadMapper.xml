<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamMonitorDownloadMapper">

    <select id="getExamMonitorCount" parameterType="map" resultType="int">
        SELECT
        count(1)
        FROM t_exam_monitor_download temd
        WHERE temd.exam_id = #{examId}
        AND temd.creator_id = #{userId}
        <if test="hiddenAnswerCardExportTask != null and hiddenAnswerCardExportTask == true">
            and not exists(
            select 1
            from t_exam_answer_card_export teace
            where teace.exam_id = #{examId}
              and teace.creator_id = #{userId}
              and teace.exam_monitor_download_id = temd.exam_monitor_download_id
            )
        </if>
    </select>

    <select id="getExamMonitorList" parameterType="map" resultType="map">
        SELECT
        temd.exam_monitor_download_id examMonitorDownloadId,
        temd.exam_id examId,
        temd.paper_id paperId,
        temd.status,
        temd.file_name fileName,
        temd.file_url fileUrl,
        temd.fail_message failMessage,
        temd.creator_id creatorId,
        temd.creator_name creatorName,
        temd.create_date_time createDateTime,
        temd.modifier_id modifierId,
        temd.modifier_name modifierName,
        temd.modify_date_time modifyDateTime
        FROM t_exam_monitor_download temd
        WHERE temd.exam_id = #{examId}
        AND temd.creator_id = #{userId}
        <if test="hiddenAnswerCardExportTask != null and hiddenAnswerCardExportTask == true">
            and not exists(
            select 1
            from t_exam_answer_card_export teace
            where teace.exam_id = #{examId}
            and teace.creator_id = #{userId}
            and teace.exam_monitor_download_id = temd.exam_monitor_download_id
            )
        </if>
        ORDER BY temd.create_date_time DESC
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <insert id="insertExamMonitorDownload" parameterType="map" useGeneratedKeys="true" keyColumn="exam_monitor_download_id" keyProperty="examMonitorDownloadId">
        INSERT INTO t_exam_monitor_download
        (
            `exam_id`,
            `paper_id` ,
            `status`,
            `file_name` ,
            `file_url`,
            `creator_id` ,
            `creator_name`,
            `create_date_time`,
            `modifier_id`,
            `modifier_name`,
            `modify_date_time`
        )
        VALUES
            (
                #{examId},
                #{paperId},
                #{status},
                #{fileName},
                #{fileUrl},
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime}
            )
    </insert>

    <update id="updateExamMonitorDownload" parameterType="map">
        UPDATE t_exam_monitor_download
        <set>
            <if test="fileUrl != null and fileUrl!=''">
                file_url = #{fileUrl},
            </if>
            <if test="status != null and status!=''">
                status = #{status},
            </if>
            <if test="failMessage != null and failMessage!=''">
                fail_message = #{failMessage},
            </if>
        </set>
        WHERE exam_monitor_download_id =#{examMonitorDownloadId}
    </update>

    <select id="getExamMonitorDownloadById" parameterType="map" resultType="map">
        SELECT
            exam_monitor_download_id examMonitorDownloadId,
            exam_id examId,
            paper_id paperId,
            status,
            file_name fileName,
            file_url fileUrl,
            fail_message failMessage,
            creator_id creatorId,
            creator_name creatorName,
            create_date_time createDateTime,
            modifier_id modifierId,
            modifier_name modifierName,
            modify_date_time modifyDateTime
        FROM t_exam_monitor_download
        WHERE exam_monitor_download_id = #{examMonitorDownloadId}
    </select>

    <update id="update" parameterType="map">
        UPDATE t_exam_monitor_download
        SET status = #{status},
            file_url = #{fileUrl},
            fail_message = #{failMessage},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE exam_monitor_download_id = #{examMonitorDownloadId}
    </update>
</mapper>