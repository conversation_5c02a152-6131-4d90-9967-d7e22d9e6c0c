<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamClassStatCompareMapper">

    <!-- 获取考试基本信息 -->
    <select id="getExam" parameterType="map" resultType="map">
        SELECT
            te.`exam_id` examId,
            te.`exam_name` examName,
            te.`exam_type` examType,
            te.`grade_type` gradeType,
            te.`grade_year` gradeYear,
            te.`exam_status` examStatus,
            te.`start_date` startDate,
            te.`end_date` endDate,
        count(1) courseCount,
        UNIX_TIMESTAMP(te.end_date) sortKey,
        GROUP_CONCAT(DISTINCT tecs.course_name SEPARATOR ',') courseName,
        GROUP_CONCAT(DISTINCT tec.class_name SEPARATOR ',') className
        FROM t_exam te
        INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id
        INNER JOIN t_exam_course tecs ON te.exam_id = tecs.exam_id
        WHERE te.exam_id = #{examId}
        GROUP BY te.exam_id
    </select>

    <!-- 获取考试基本信息 -->
    <select id="getExamsByIds" parameterType="map" resultType="map">
        SELECT
            te.`exam_id` examId,
            te.`exam_name` examName,
            te.`exam_type` examType,
            te.`grade_type` gradeType,
            te.`grade_year` gradeYear,
            te.`exam_status` examStatus,
            te.`start_date` startDate,
            te.`end_date` endDate,
            count(1) courseCount,
            UNIX_TIMESTAMP(te.end_date) sortKey,
            GROUP_CONCAT(DISTINCT tecs.course_name SEPARATOR ',') courseName,
            GROUP_CONCAT(DISTINCT tec.class_name SEPARATOR ',') className
        FROM t_exam te
        INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id
        INNER JOIN t_exam_course tecs ON te.exam_id = tecs.exam_id
        WHERE te.exam_id IN
        <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
            #{examId}
        </foreach>
        GROUP BY te.exam_id
    </select>

    <!-- 获取可选的所有考试ID -->
    <select id="getOptionalExamId" parameterType="map" resultType="long">
        SELECT te.exam_id
        FROM t_exam te
        INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id
        INNER JOIN t_exam_course tecs ON te.exam_id = tecs.exam_id
        <if test="userType ==8 or userType ==14">
            INNER JOIN t_exam_area tea ON te.exam_id = tea.exam_id
        </if>
        WHERE te.exam_status > 1
        <if test="classId !=null and classId !=''">
            AND tec.class_id = #{classId}
        </if>
        AND te.exam_type != 8
        <if test="examType != null and examType != ''">
            AND te.exam_type = #{examType}
        </if>
        <if test="schoolId !=null and schoolId !=''">
            AND tec.school_id IN
          <foreach collection="schoolIds" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
        </if>
        <if test="userType == 13">
            AND te.creator_id = #{userId}
            AND te.exam_type IN(7,10)
        </if>
        <if test="userType ==8 or userType ==14">
            AND tea.area_id = #{areaId}
            AND te.exam_type = 10
        </if>
        <if test="courseId != 0 and courseId != -1">
            AND tecs.course_id = #{courseId}
        </if>
        <if test="gradeId !=null and gradeId !=''">
            AND tec.grade_id = #{gradeId}
        </if>
        <if test="gradeType !=null and gradeType !=''">
            AND te.grade_type = #{gradeType}
        </if>
        <if test="startDate !=null and startDate !=''">
            AND te.start_date &gt;= #{startDate}
        </if>
        <if test="endDate !=null and endDate !=''">
            AND te.start_date &lt;= #{endDate}
        </if>
        <if test="search !=null and search !=''">
            AND te.exam_name like CONCAT('%',#{search},'%')
        </if>
        GROUP BY te.exam_id
        <if test="courseId == 0">
            HAVING count( DISTINCT tecs.course_id ) > 1
        </if>
    </select>

    <!-- 根据班主任班级获取考试列表 -->
    <select id="getExamIdByHeadClass" resultType="long" parameterType="map">
        SELECT te.exam_id
        FROM t_exam te
        inner join t_exam_class tec ON te.exam_id = tec.exam_id
        WHERE te.exam_status > 1
        and te.exam_type != 8
        <if test="examType != null and examType != ''">
            and te.exam_type = #{examType}
        </if>
        and tec.class_id = #{classId}
        and tec.school_id = #{schoolId}
        <if test="search !=null and search !=''">
            AND te.exam_name like CONCAT('%',#{search},'%')
        </if>
    </select>

    <!-- 根据执教班级+执教课程获取考试列表 -->
    <select id="getExamIdByTeachingClass" resultType="long" parameterType="map">
        SELECT te.exam_id
        FROM t_exam te
        inner join t_exam_paper tep ON te.exam_id = tep.exam_id
        inner join t_exam_class_paper tecp on tep.exam_id = tecp.exam_id and tep.paper_id = tecp.paper_id
        WHERE te.exam_status > 1
        and te.exam_type != 8
        <if test="examType != null and examType != ''">
            and te.exam_type = #{examType}
        </if>
        and tep.course_id = #{courseId}
        and tecp.class_id = #{classId}
        and tecp.school_id = #{schoolId}
        <if test="search !=null and search !=''">
            AND te.exam_name like CONCAT('%',#{search},'%')
        </if>
    </select>

    <!-- 获取可选的所有考试报告 -->
    <select id="getOptionalExamStat" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_name examName,
            te.start_date startDate,
            te.end_date endDate,
            te.exam_type examType,
            te.grade_type gradeType,
            UNIX_TIMESTAMP(te.end_date) sortKey,
            GROUP_CONCAT(DISTINCT tecs.course_name SEPARATOR ',') courseName,
            GROUP_CONCAT(DISTINCT tec.class_name SEPARATOR ',') className
        FROM t_exam te
        INNER JOIN t_exam_course tecs ON te.exam_id = tecs.exam_id
        INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id
        WHERE te.exam_id IN
        <foreach collection="examIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY te.exam_id
        <choose>
            <when test="sortType !=null and sortType !=''">
                ORDER BY te.start_date ${sortType}
            </when>
            <otherwise>
                ORDER BY te.start_date DESC,te.create_date_time DESC
            </otherwise>
        </choose>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 获取考试关联的课程 -->
    <select id="getExamCourse" parameterType="list" resultType="map">
        SELECT
        tec.exam_id examId,
        tec.course_id courseId,
        tec.course_name courseName
        FROM t_exam_course tec
        WHERE tec.exam_id IN
        <foreach collection="examIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 获取班级课程错题总数 -->
    <select id="getWrongClassItemCount" parameterType="map" resultType="long">
        SELECT COUNT(DISTINCT tcwt.question_id)
        FROM t_wrong_class_item tcwt
        WHERE tcwt.class_id = #{classId}
        <if test="courseId != null and courseId != ''">
            AND tcwt.course_id = #{courseId}
        </if>
        <if test="startDate != null">
            AND tcwt.modify_date_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND tcwt.modify_date_time &lt; #{endDate}
        </if>
    </select>

    <select id="getClassWrongQuestionIdsByIds" parameterType="map" resultType="string">
        select distinct question_id
        from t_wrong_class_item
        where class_id = #{classId}
        and question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </select>

</mapper>