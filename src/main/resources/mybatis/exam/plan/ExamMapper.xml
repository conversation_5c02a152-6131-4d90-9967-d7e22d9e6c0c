<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamMapper">

    <!-- 获取考试基本信息 -->
    <select id="getExam" parameterType="map" resultType="map">
        SELECT
        `exam_id` examId,
        `exam_name` examName,
        `exam_type` examType,
        correct_mode correctMode,
        entry_type entryType,
        stage stage,
        `grade_type` gradeType,
        `grade_year` gradeYear,
        `exam_status` examStatus,
        homework_type homeworType,
        auto_publish autoPublish,
        `start_date` startDate,
        `end_date`     endDate,
        `create_date_time` createDateTime,
        `creator_id` creatorId,
        `creator_name` creatorName
        FROM t_exam
        WHERE exam_id = #{examId}
    </select>

    <select id="findMarkByClassExam" parameterType="map" resultType="map">
        select te.exam_id examId, tecp.class_id classId
        from t_exam te
        inner join t_exam_class_paper tecp on te.exam_id = tecp.exam_id
        where te.exam_id in
        <foreach collection="examIds" item="examId" separator="," open="(" close=")">
            #{examId}
        </foreach>
        and te.correct_mode = 0
        and exam_class_paper_status > 10;
    </select>

    <!-- 获取考试是否为总分导入模式 -->
    <select id="getTotalScoreImportExamByExamIds" parameterType="list" resultType="map">
        SELECT exam_id examId FROM t_exam  WHERE exam_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND entry_type = 3;
    </select>


    <!-- 获取考试基本信息 -->
    <select id="getExamMark" parameterType="map" resultType="com.dongni.exam.common.mark.vo.ExamVO">
        SELECT
            `exam_id` examId,
            `exam_name` examName,
            `exam_type` examType,
            correct_mode correctMode,
            entry_type entryType,
            stage stage,
            `grade_type` gradeType,
            `grade_year` gradeYear,
            `exam_status` examStatus,
            homework_type homeworType,
            auto_publish autoPublish,
            `start_date` startDate,
            `end_date`     endDate,
            `create_date_time` createDateTime,
            `creator_id` creatorId,
            `creator_name` creatorName
        FROM t_exam
        WHERE exam_id = #{examId}
    </select>

    <!-- 获取考试基本信息 -->
    <select id="getExamInfo" parameterType="map" resultType="map">
        SELECT
            `exam_id` examId,
            `exam_name` examName,
            `exam_type` examType,
            `correct_mode` correctMode,
            `entry_type` entryType,
            `start_date` startDate,
            `end_date` endDate,
            `creator_id` creatorId,
            `creator_name` creatorName,
            exam_status examStatus,
            auto_publish autoPublish
        FROM t_exam
        WHERE exam_id = #{examId}
    </select>

    <update id="updateMode" parameterType="map">
        update t_exam
        set correct_mode = #{correctMode},
            modifier_id = #{userId},
            modifier_name =#{userName},
            modify_date_time = #{currentTime}
        where exam_id = #{examId}
    </update>

    <select id="getExamBaseInfo" parameterType="map" resultType="map">
        SELECT exam_id           examId,
               exam_name         examName,
               exam_type         examType,
               correct_mode      correctMode,
               entry_type        entryType,
               grade_type        gradeType,
               grade_year        gradeYear,
               stage,
               exam_status       examStatus,
               start_date        startDate,
               end_date          endDate,
               creator_id        creatorId,
               creator_name      creatorName
        FROM t_exam
        WHERE exam_id = #{examId}
    </select>

    <select id="getExamsBaseInfo" parameterType="map" resultType="map">
        SELECT exam_id           examId,
               exam_name         examName,
               exam_type         examType,
               correct_mode      correctMode,
               entry_type        entryType,
               grade_type        gradeType,
               grade_year        gradeYear,
               exam_status       examStatus,
               start_date        startDate,
               end_date          endDate,
               creator_id        creatorId
        FROM t_exam
        WHERE exam_id IN
        <foreach collection="examIdList" item="examId" open="(" close=")" separator=",">
            #{examId}
        </foreach>
    </select>

    <select id="checkExamHavingResult" parameterType="long" resultType="long">
        select exam_result_id
        from t_exam_result
        where exam_id = #{examId}
        limit 1
    </select>

    <select id="getUnionExamIdHasNotResult" resultType="long">
        select te.exam_id
        from t_exam te
        where te.exam_type in (7, 10)
          and te.exam_status = 1
          and te.entry_type = 1
          and not exists (select 1 from t_exam_result ter where ter.exam_id = te.exam_id)
    </select>

    <!-- 获取考试基本信息 -->
    <select id="getAbsentStudent" parameterType="map" resultType="map">
        SELECT
            ter.student_id studentId,
            ter.student_exam_num studentExamNum,
            ter.student_name studentName,
            tes.school_name schoolName,
            tecp.class_name className,
            tes.school_id schoolId,
            tecp.class_id classId
        FROM t_exam_result ter
        INNER JOIN t_exam_school tes ON tes.exam_id = ter.exam_id and ter.school_id = tes.school_id
        INNER JOIN t_exam_class_paper tecp ON tecp.exam_id = ter.exam_id and tecp.class_id = ter.class_id  and ter.paper_id = tecp.paper_id
        WHERE ter.exam_id = #{examId}
        AND ter.paper_id = #{paperId}
        AND (ter.result_status = 1 or ter.uploaded_status in
        <foreach collection="stuUploadStatusList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>)
        <if test="classId != null and classId !=''">
            AND ter.class_id = #{classId}
        </if>
        <if test="schoolId != null and schoolId !=''">
            AND ter.school_id = #{schoolId}
        </if>
        GROUP BY ter.student_id
    </select>

    <!-- 获取缺考考生信息，多科目多班级 -->
    <select id="getAbsentStudentByClassAndPaper" parameterType="map" resultType="map">
        SELECT
            ter.student_id studentId,
            ter.student_num studentNum ,
            ter.student_name studentName,
            tes.school_name schoolName,
            tecp.class_name className,
            tes.school_id schoolId,
            tecp.class_id classId,
            tecp.paper_id paperId
        FROM t_exam_result ter
        INNER JOIN t_exam_school tes ON tes.exam_id = ter.exam_id and ter.school_id = tes.school_id
        INNER JOIN t_exam_class_paper tecp ON tecp.exam_id = ter.exam_id and tecp.class_id = ter.class_id  and ter.paper_id = tecp.paper_id
        WHERE ter.exam_id = #{examId}
        <if test="paperIdList != null and paperIdList.size > 0">
            AND ter.paper_id IN (
                <foreach collection="paperIdList" item="paperId" separator=",">
                    #{paperId}
                </foreach>
            )
        </if>
        AND ter.result_status = 1
        <if test="classIdList != null and classIdList.size > 0">
            AND ter.class_id IN (
                <foreach collection="classIdList" item="classId" separator=",">
                    #{classId}
                </foreach>
            )
        </if>
        <if test="schoolIdList != null and schoolIdList.size > 0">
            AND ter.school_id IN (
                <foreach collection="schoolIdList" item="schoolId" separator=",">
                    #{schoolId}
                </foreach>
            )
        </if>
    </select>

    <!--获取考试学生-->
    <select id="getResultStudent" resultType="map" parameterType="map">
        SELECT
        ter.student_id studentId,
        ter.student_name studentName,
        ter.student_exam_num studentExamNum,
        tes.school_name schoolName,
        tecp.class_name className,
        tes.school_id schoolId,
        tecp.class_id classId
        FROM t_exam_result ter
        INNER JOIN t_exam_school tes ON tes.exam_id = ter.exam_id and ter.school_id = tes.school_id
        INNER JOIN t_exam_class_paper tecp ON tecp.exam_id = ter.exam_id and tecp.class_id = ter.class_id  and ter.paper_id = tecp.paper_id
        WHERE ter.exam_id=#{examId}
        AND ter.paper_id=#{paperId}
        <if test="schoolId != null and schoolId !=''">
            AND ter.school_id = #{schoolId}
        </if>
        GROUP BY ter.student_id
    </select>

    <!--获取这场考试有item--> <!-- TODO sharding7 checkExamId -->
    <select id="getResultItemStudent" resultType="map" parameterType="map">
        SELECT
        tes.student_name studentName,
        tes.student_exam_num studentExamNum,
        tes2.school_name schoolName,
        tecp.class_name className,
        tes2.school_id schoolId,
        tecp.class_id classId
        FROM t_exam_item tei
        INNER JOIN t_exam_student tes ON tes.exam_id = tei.exam_id AND tes.student_id = tei.student_id
        INNER JOIN t_exam_school tes2 ON tes2.exam_id = tes.exam_id and tes.school_id = tes2.school_id
        INNER JOIN t_exam_class_paper tecp ON tecp.exam_id = tei.exam_id and tecp.class_id = tei.class_id  and tei.paper_id = tecp.paper_id
        WHERE tei.exam_id=#{examId}
        AND tei.paper_id=#{paperId}
        <if test="schoolId != null and schoolId != ''">
            AND tei.school_id=#{schoolId}
        </if>
        GROUP BY tei.student_id
    </select>

    <!-- 新增考试 -->
    <insert id="insertExam" parameterType="map" useGeneratedKeys="true" keyProperty="examId">
        INSERT INTO t_exam(
        `exam_name`,
        `exam_type`,
        `grade_type`,
        `correct_mode`,
        `entry_type`,
        `stage`,
        `grade_year`,
        <if test="version != null and version != ''">
            `version`,
        </if>
        `exam_status`,
        `start_date`,
        `end_date`,
        <if test="homeworkType != null and homeworkType != ''">
            `homework_type`,
        </if>
        <if test="autoPublish != null and autoPublish != ''">
            `auto_publish`,
        </if>
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        (
        #{examName},
        #{examType},
        #{gradeType},
        #{correctMode},
        #{entryType},
        #{stage},
        #{gradeYear},
        <if test="version != null and version != ''">
            #{version},
        </if>
        #{examStatus},
        #{startDate},
        #{endDate},
        <if test="homeworkType != null and homeworkType != ''">
            #{homeworkType},
        </if>
        <if test="autoPublish != null and autoPublish != ''">
            #{autoPublish},
        </if>
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        );
    </insert>

    <update id="updateExam" parameterType="map">
        UPDATE `t_exam`
        SET
        <if test="examName !=null and examName !=''">
        `exam_name` = #{examName},
        </if>
        <if test="examType !=null and examType !=''">
        `exam_type` = #{examType},
        </if>
        <if test="examStatus !=null and examStatus !=''">
        `exam_status` = #{examStatus},
        </if>
        <if test="startDate !=null">
        `start_date` = #{startDate},
        </if>
        <if test="endDate !=null">
        `end_date` = #{endDate},
        </if>
         `modifier_id` = #{userId},
         `modifier_name` = #{userName},
         `modify_date_time` = #{currentTime}
        WHERE exam_id = #{examId}
    </update>

    <insert id="insertExamClass" parameterType="map">
        INSERT INTO `t_exam_class` (
            `exam_id`,
            `school_id`,
            `grade_id`,
            `class_id`,
            `class_name`,
            `class_status`,
            `class_type`,
            `correct_mode`,
            `arts_science`,
            `creator_id`,
            `creator_name`,
            `create_date_time`,
            `modifier_id`,
            `modifier_name`,
            `modify_date_time`
        )
        VALUES
        <foreach collection="examClass" separator="," item="item">
            (
                #{examId},
                #{item.schoolId},
                #{item.gradeId},
                #{item.classId},
                #{item.className},
                #{item.classStatus},
                #{item.classType},
                #{item.correctMode},
                #{item.artsScience},
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime}
            )
        </foreach>
    </insert>

    <insert id="insertExamSchool" parameterType="map">
        INSERT INTO `t_exam_school` (
            `exam_id`,
            `area_id`,
            `school_id`,
            `school_name`,
            `grade_id`,
            `grade_type`,
            `grade_name`,
            `creator_id`,
            `creator_name`,
            `create_date_time`,
            `modifier_id`,
            `modifier_name`,
            `modify_date_time`
            )
            VALUES
            <foreach collection="examGrade" item="item" separator=",">
            (
                #{examId},
                #{item.areaId},
                #{item.schoolId},
                #{item.schoolName},
                #{item.gradeId},
                #{item.gradeType},
                #{item.gradeName},
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime}
            )
            </foreach>
    </insert>

    <insert id="insertExamCourse" parameterType="map">
        replace INTO t_exam_course(
        `exam_id`,
        `course_id`,
        `course_name`,
        `exam_course_status`,
        `arts_science`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="examCourse" separator="," item="item">
            (
                #{examId},
                #{item.courseId},
                #{item.courseName},
                #{item.examCourseStatus},
                #{item.artsScience},
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime}
        )
        </foreach>
    </insert>

    <insert id="insertExamPaper" parameterType="list" useGeneratedKeys="true" keyProperty="examPaperId">
        INSERT INTO t_exam_paper(
        `exam_id`,
        `course_id`,
        `course_name`,
        `paper_id`,
        `paper_name`,
        `full_mark`,
        `arts_science`,
        `exam_paper_status`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
        #{item.examId},
        #{item.courseId},
        #{item.courseName},
        #{item.paperId},
        #{item.paperName},
        #{item.fullMark},
        #{item.artsScience},
        #{item.examPaperStatus},
        #{item.userId},
        #{item.userName},
        #{item.currentTime},
        #{item.userId},
        #{item.userName},
        #{item.currentTime}
        )
        </foreach>
    </insert>

    <insert id="insertExamSchoolPaper" parameterType="map">
        INSERT INTO t_exam_school_paper (
            `exam_id`,
            `school_id`,
            `paper_id`,
            `exam_school_paper_status`,
            `creator_id`,
            `creator_name`,
            `create_date_time`,
            `modifier_id`,
            `modifier_name`,
            `modify_date_time`
        ) SELECT
            tes.exam_id,
            tes.school_id,
            tep.paper_id,
            tep.exam_paper_status,
            tep.`creator_id`,
            tep.`creator_name`,
            tep.`create_date_time`,
            tep.`modifier_id`,
            tep.`modifier_name`,
            tep.`modify_date_time`
        FROM
            t_exam_school tes
        INNER JOIN t_exam_paper tep ON tes.exam_id = tep.exam_id
        LEFT JOIN t_exam_school_paper tesp ON tes.exam_id = tesp.exam_id
        AND tep.paper_id = tesp.paper_id
        WHERE
            tes.exam_id = #{examId}
        AND tesp.exam_school_paper_id IS NULL
    </insert>

    <insert id="insertExamStudent" parameterType="map">
        INSERT INTO t_exam_student(
        `exam_id`,
        `school_id`,
        `class_id`,
        `class_name`,
        `student_id`,
        `student_name`,
        `student_num`,
        `student_exam_num`,
        `course_selection_group_id`,
        `foreign_course_id`,
        `arts_science`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="examStudent" separator="," item="item">
        (
        #{examId},
        #{item.schoolId},
        #{item.classId},
        #{item.className},
        #{item.studentId},
        #{item.studentName},
        #{item.studentNum},
        #{item.studentExamNum},
        #{item.courseSelectionGroupId},
        #{item.foreignCourseId},
        #{item.artsScience},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
        </foreach>
    </insert>

    <insert id="insertExamClassPaper" parameterType="map">
        INSERT INTO t_exam_class_paper (
        exam_id,
        paper_id,
        school_id,
        grade_id,
        class_id,
        class_name,
        class_type,
        arts_science,
        exam_class_paper_status,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="examClassPaper" separator="," item="item">
            (
            #{examId},
            #{item.paperId},
            #{item.schoolId},
            #{item.gradeId},
            #{item.classId},
            #{item.className},
            #{item.classType},
            #{item.artsScience},
            #{item.examClassPaperStatus},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
    </insert>


    <insert id="insertExamResult" parameterType="map">
        INSERT INTO `t_exam_result` (
        `exam_id`,
        `course_id`,
        `paper_id`,
        `school_id`,
        `class_id`,
        `class_name`,
        `student_id`,
        `student_num`,
        `student_name`,
        `student_exam_num`,
        `student_name_pinyin`,
        `result_status`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="examResult" item="item" separator=",">
        (
        #{examId},
        #{item.courseId},
        #{item.paperId},
        #{item.schoolId},
        #{item.classId},
        #{item.className},
        #{item.studentId},
        #{item.studentNum},
        #{item.studentName},
        #{item.studentExamNum},
        #{item.studentNamePinyin},
        #{item.resultStatus},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
        </foreach>
    </insert>

    <insert id="insertUnionExamResult" parameterType="map">
        INSERT INTO `t_exam_result` (
        `exam_id`,
        `course_id`,
        `paper_id`,
        `school_id`,
        `class_id`,
        `class_name`,
        `student_id`,
        `student_num`,
        `student_name`,
        `result_status`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="examStudent" item="student" separator=",">
            <foreach collection="unionExamCourse" item="item" separator=",">
                (
                #{examId},
                #{item.courseId},
                #{item.paperId},
                #{student.schoolId},
                #{student.classId},
                #{student.className},
                #{student.studentId},
                #{student.studentNum},
                #{student.studentName},
                0,
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime}
                )
            </foreach>
        </foreach>
    </insert>


    <!--初始化联考答题卡扫描员-->
    <insert id="insertExamUnionClassPaper" parameterType="map">
        INSERT INTO `t_exam_class_paper` (
        `exam_id`,
        `paper_id`,
        `school_id`,
        `grade_id`,
        `class_id`,
        `class_name`,
        `class_type`,
        `arts_science`,
        `exam_class_paper_status`,
        `creator_id`,`creator_name`,`create_date_time`,
        `modifier_id`,`modifier_name`,`modify_date_time`
        )
        VALUES
        <foreach collection="examClass" item="class" separator=",">
            <foreach collection="unionExamCourse" item="examCourse" separator=",">
                (
                #{examId},
                #{examCourse.paperId},
                #{class.schoolId},
                #{class.gradeId},
                #{class.classId},
                #{class.className},
                #{class.classType},
                #{class.artsScience},
                #{examCourse.examPaperStatus},
                #{userId}, #{userName},#{currentTime},
                #{userId},#{userName},#{currentTime}
                )
            </foreach>
        </foreach>
    </insert>

    <!--考试详情-->
    <select id="getExamDetail" parameterType="map" resultType="map">
        SELECT
        exam_id examId,
        exam_name examName,
        exam_type examType,
        correct_mode correctMode,
        entry_type entryType,
        exam_group_id examGroupId,
        escalation_id escalationId,
        auto_publish autoPublish,
        grade_type gradeType,
        grade_year gradeYear,
        version,
        ifnull(scan_type, 1) scanType,
        stage      stage,
        exam_status examStatus,
        start_date startDate,
        end_date endDate,
        homework_type homeworkType,
        creator_id creatorId,
        creator_name creatorName,
        create_date_time createDateTime
        FROM t_exam WHERE exam_id = #{examId}
    </select>

    <!-- 获取考试课程 -->
    <select id="getExamCourse" parameterType="map" resultType="map">
        SELECT
        `exam_course_id` examCourseId,
        `exam_id` examId,
        `course_id` courseId,
        `course_name` courseName,
        `exam_course_status` examCourseStatus,
        `arts_science` artsScience
        FROM t_exam_course
        WHERE exam_id = #{examId}
    </select>

    <select id="getExamCourseIdsByExistUploader" parameterType="map" resultType="long">
        SELECT tec.course_id
        FROM t_exam_course tec
        inner join t_exam_worker tew on tec.exam_id = tew.exam_id and tec.course_id = tew.course_id
        WHERE tec.exam_id = #{examId} and tew.worker_type = 3
        group by tec.course_id
    </select>

    <!-- 获取考试数量 -->
    <select id="getExamCount" parameterType="map" resultType="int">
        SELECT COUNT(1)
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="courseId !=null and courseId !=''">
            INNER JOIN t_exam_course tec ON te.exam_id = tec.exam_id
        </if>
        <if test="classId !=null and classId !=''">
            INNER JOIN t_exam_class tecc ON te.exam_id = tecc.exam_id
        </if>
        WHERE tes.grade_id = #{gradeId}
        <if test="courseId !=null and courseId !=''">
            AND tec.course_id = #{courseId}
            AND tec.arts_science = #{artsScience}
        </if>
        <if test="classId != null and classId !=''">
            AND tecc.class_id = #{classId}
        </if>
        <if test="examType != null and examType !=''">
            AND te.exam_type = #{examType}
        </if>
        <if test="examStatus != null and examStatus !=''">
            AND te.exam_status = #{examStatus}
        </if>
    </select>
    <!-- 获取考试 -->
    <select id="getExamList" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.grade_type gradeType,
        te.grade_year gradeYear,
        te.exam_status examStatus,
        te.start_date startDate,
        te.end_date endDate,
        te.creator_id creatorId,
        te.creator_name creatorName,
        tes.school_id schoolId,
        tes.grade_id gradeId
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="courseId !=null and courseId !=''">
            INNER JOIN t_exam_course tec ON te.exam_id = tec.exam_id
        </if>
        <if test="classId !=null and classId !=''">
            INNER JOIN t_exam_class tecc ON te.exam_id = tecc.exam_id
        </if>
        WHERE tes.grade_id = #{gradeId}
        <if test="courseId !=null and courseId !=''">
            AND tec.course_id = #{courseId}
            AND tec.arts_science = #{artsScience}
        </if>
        <if test="classId != null and classId !=''">
            AND tecc.class_id = #{classId}
        </if>
        <if test="examType != null and examType !=''">
            AND te.exam_type = #{examType}
        </if>
        <if test="examStatus != null and examStatus !=''">
            AND te.exam_status = #{examStatus}
        </if>
        ORDER BY te.create_date_time desc
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

   <select id="getExamResultCount" parameterType="map" resultType="int">
       SELECT count(1) FROM t_exam_result
       WHERE exam_id = #{examId} AND result_status != 1
   </select>

   <delete id="deleteExam">
       DELETE te,tes FROM t_exam te
       LEFT JOIN  t_exam_school tes ON te.exam_id=tes.exam_id
       WHERE te.exam_id = #{examId}
   </delete>

   <delete id="deleteExamCourse">
       DELETE FROM t_exam_course
       WHERE exam_id = #{examId}
   </delete>

   <delete id="deleteExamPaper">
       DELETE FROM t_exam_paper
       WHERE exam_id = #{examId}
   </delete>

    <delete id="deleteExamSchoolPaper">
        DELETE FROM t_exam_school_paper
        WHERE exam_id = #{examId}
    </delete>

   <delete id="deleteExamClass">
       DELETE FROM t_exam_class
       WHERE exam_id = #{examId}
   </delete>

   <delete id="deleteExamStudent">
       DELETE FROM t_exam_student
       WHERE exam_id = #{examId}
   </delete>


   <delete id="deleteExamResult">
       DELETE FROM t_exam_result
       WHERE exam_id = #{examId}
   </delete>

    <!-- 获取考试学生结果 -->
    <select id="getExamResult" parameterType="map" resultType="map">
        SELECT
        `exam_result_id` examResultId,
        `exam_id` examId,
        `course_id` courseId,
        `paper_id` paperId,
        `school_id` schoolId,
        `class_id` classId,
        `class_name` className,
        `student_id` evaluationStudentId,
        `student_name` evaluationStudentName,
        `student_num` studentNum,
        `result_status` resultStatus
        FROM t_exam_result
        WHERE exam_id = #{examId}
        <if test="classId != null and classId !=''">
            AND class_id = #{classId}
        </if>
    </select>

    <!-- 初始化学生自评和学生互评的数据 -->
    <insert id="insertExamResultEvaluation" parameterType="list">
        INSERT INTO t_exam_result_evaluation(
        exam_result_id,
        student_id,
        student_name,
        evaluation_student_id,
        evaluation_student_name,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.examResultId},
            #{item.studentId},
            #{item.studentName},
            #{item.evaluationStudentId},
            #{item.evaluationStudentName},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
    </insert>

    <!-- TODO sharding7 checkExamId -->
    <insert id="insertExamItem" parameterType="map" useGeneratedKeys="true">
        INSERT INTO `t_exam_item` (
        `exam_id`,
        `school_id`,
        `class_id`,
        `student_id`,
        `paper_id`,
        `course_id`,
        `question_number`,
        `structure_number`,
        `read_type`,
        `read_status`,
        `recognition_value`,
        `score_value`,
        `finally_score`,
        `save_file_url`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="examItem" item="item" separator=",">
            (
            #{examId},
            #{item.schoolId},
            #{item.classId},
            #{item.studentId},
            #{item.paperId},
            #{item.courseId},
            #{item.questionNumber},
            #{item.structureNumber},
            #{item.readType},
            0,
            "",
            #{item.scoreValue},
            0,
            NULL,
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>

    </insert>

    <!-- 批量插入考试标签 -->
    <insert id="batchInsertExamTag" parameterType="list" keyProperty="examTagId" useGeneratedKeys="true">
        INSERT INTO t_exam_tag(
        tag_id,
        tag_name,
        tag_desc,
        exam_id,
        school_id,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.tagId},
            #{item.tagName},
            #{item.tagDesc},
            #{item.examId},
            #{item.schoolId},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
    </insert>
     <!-- 批量插入考试标签 -->
    <insert id="batchInsertExamStudentTag" parameterType="list">
        INSERT INTO t_exam_student_tag(
        exam_tag_id,
        exam_id,
        student_id,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.examTagId},
            #{item.examId},
            #{item.studentId},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
    </insert>

    <!-- 插入考试区域 -->
    <insert id="insertExamArea" parameterType="map">
        INSERT t_exam_area (
        exam_id,
        area_id,
        parent_area_id,
        parent_area_code,
        area_code,
        area_name,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        ) VALUES
        <foreach collection="examArea" item="item" separator=",">
            (
            #{examId},
            #{item.areaId},
            #{item.parentAreaId},
            #{item.parentAreaCode},
            #{item.areaCode},
            #{item.areaName},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
    </insert>

    <!-- 获取考试区域根结点 -->
    <select id="getExamSchoolAreaId" parameterType="map" resultType="Long">
        SELECT
        tes.area_id areaId
        FROM
        t_exam_school tes
        WHERE
        exam_id = #{examId};
    </select>

    <select id="getExamSchoolInfoById" parameterType="map" resultType="map">
        SELECT
            exam_school_id examSchoolId,
            school_id schoolId,
            area_id areaId
        FROM
            t_exam_school
        WHERE
            exam_id = #{examId}
    </select>

    <update id="updateExamSchoolInfoById" parameterType="map">
        UPDATE t_exam_school
        SET
            area_id = #{areaId},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        WHERE exam_school_id = #{examSchoolId}
    </update>

    <!-- 获取考试Item --> <!-- TODO sharding7 checkExamId -->
    <select id="getExamItemStatus" parameterType="map" resultType="int">
        SELECT 1
        FROM t_exam_item
        WHERE exam_id = #{examId}
        LIMIT 1
    </select>

    <!-- 根据ID获取考试详情 -->
    <select id="getExamInId" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_name examName,
            te.exam_type examType,
            te.grade_type gradeType,
            te.grade_year gradeYear,
            te.exam_status examStatus,
            te.start_date startDate,
            te.end_date endDate,
            te.creator_id creatorId,
            te.creator_name creatorName
        FROM t_exam te
        WHERE exam_id IN
        <foreach collection="examIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 获取区域最新考试列表 -->
    <select id="getExamListByArea" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.grade_type gradeType,
        te.grade_year gradeYear,
        te.start_date startDate,
        te.end_date endDate,
        te.exam_status examStatus
        FROM t_exam te
        INNER JOIN t_exam_paper tep ON te.exam_id = tep.exam_id
        INNER JOIN t_exam_area tea ON te.exam_id = tea.exam_id
        WHERE tea.area_id = #{areaId}
        AND te.exam_status &gt; 1
        AND te.exam_type IN (10)
        <if test="gradeYear != null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="gradeType != null and gradeType !=''">
            AND te.grade_type = #{gradeType}
        </if>
        GROUP BY te.exam_id
        ORDER BY te.start_date DESC
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 插入examTeacher -->
    <insert id="insertExamTeacher" parameterType="map">
        INSERT t_exam_teacher (
        exam_id,
        class_id,
        class_name,
        course_id,
        course_name,
        teacher_id,
        teacher_name,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        ) VALUES
        <foreach collection="examTeacher" item="item" separator=",">
            (
            #{examId},
            #{item.classId},
            #{item.className},
            #{item.courseId},
            #{item.courseName},
            #{item.teacherId},
            #{item.teacherName},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
        on duplicate key update
        modifier_id = VALUES(modifier_id),
        modifier_name = VALUES(modifier_name),
        modify_date_time = VALUES(modify_date_time)
    </insert>

    <!--  删除这场考试的所有t_exam_teacher数据  -->
    <delete id="deleteExamTeacherAll" parameterType="map">
        DELETE FROM t_exam_teacher WHERE exam_id = #{examId}
    </delete>

    <!-- 查询考试试卷 -->
    <select id="getExamPaper" parameterType="map" resultType="map">
        SELECT
          exam_paper_id examPaperId,
          exam_id examId,
          course_id courseId,
          course_name courseName,
          paper_id paperId,
          paper_name paperName,
          full_mark fullMark,
          arts_science artsScience,
          exam_paper_status examPaperStatus
        FROM t_exam_paper
        WHERE exam_id = #{examId}
        AND paper_id = #{paperId}
    </select>

    <!-- 查询考试试卷 -->
    <select id="getExamPaperList" parameterType="map" resultType="map">
        SELECT
          exam_paper_id examPaperId,
          exam_id examId,
          course_id courseId,
          course_name courseName,
          paper_id paperId,
          paper_name paperName,
          full_mark fullMark,
          arts_science artsScience,
          exam_paper_status examPaperStatus
        FROM t_exam_paper
        WHERE exam_id = #{examId}
        AND course_id = #{courseId}
    </select>

    <!-- 查询课程已经公布成绩的考试 -->
    <select id="getExamByCourseIdCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_course tep ON te.exam_id = tep.exam_id
        WHERE tep.course_id = #{courseId}
        AND tes.exam_school_status = 3
        ORDER BY te.start_date DESC
    </select>

    <!-- 查询课程已经公布成绩的考试 -->
    <select id="getExamByCourseId" parameterType="map" resultType="map">
        SELECT
          te.exam_id examId,
          te.exam_name examName,
          te.start_date statDate,
          te.end_date endDate,
          te.exam_type examType,
          tep.course_id courseId,
          tep.course_name courseName,
          tes.school_id schoolId,
          tes.exam_school_status examStatus
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_course tep ON te.exam_id = tep.exam_id
        WHERE tep.course_id = #{courseId}
        AND tes.exam_school_status = 3
        ORDER BY te.start_date DESC
        <if test="currentIndex != null and pageSize != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!-- 根据courseId查询考试成绩 -->
    <select id="getExamResultByCourseId" parameterType="map" resultType="map">
        SELECT
          exam_result_id examResultId,
          exam_id examId,
          course_id courseId,
          paper_id paperId,
          school_id schoolId,
          class_id classId,
          class_name className,
          student_id studentId,
          student_name studentName,
          result_status resultStatus
        FROM t_exam_result
        WHERE exam_id = #{examId}
        AND course_id = #{courseId}
    </select>

    <select id="getExamToPushNotice" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT
        te.exam_id examId,
        te.start_date startDate,
        tes.school_id schoolId
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE tes.exam_school_status = 3
        AND tes.push_status = 0
        AND te.exam_type NOT IN (11, 12)
        <if test="startDateTime != null">
            AND tes.modify_date_time &gt;= #{startDateTime}
        </if>
        <if test="endDateTime != null">
            AND tes.modify_date_time &lt; #{endDateTime}
        </if>
        <if test="examId != null">
            AND te.exam_id = #{examId}
        </if>
        <if test="schoolId != null">
            AND tes.school_id = #{schoolId}
        </if>
    </select>

    <update id="updateExamPushStatus" parameterType="java.util.Map">
        UPDATE t_exam_school
        SET
        push_status = 1,
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE exam_id = #{examId}
        AND school_id = #{schoolId}
    </update>

    <!-- 获取考试结果推送的对象集合 -->
    <select id="getExamResultPushStudent" parameterType="map" resultType="map">
        SELECT tes.exam_id              examId,
            tes.student_id             studentId,
            tesc.school_id           schoolId,
            tesc.school_name           schoolName,
            tesc.grade_name            gradeName
        FROM t_exam_student tes
            INNER JOIN t_exam_school tesc ON tesc.exam_id = tes.exam_id AND tes.school_id = tesc.school_id
        WHERE tes.exam_id = #{examId}
        AND tesc.school_id = #{schoolId}
    </select>
    <!--通过学生id查找相关学生简单信息-->
    <select id="getStudentSimpleInfoList" parameterType="map" resultType="map">
        SELECT
        tes.student_id studentId,
        tes.student_name studentName
        FROM t_exam_student tes
        WHERE tes.exam_id = #{examId} and  tes.student_id IN
        <foreach collection="studentIdList" item="item" open="(" separator="," close=")">
            #{item.studentId}
        </foreach>
    </select>

    <!-- 获取考试学校信息 -->
    <select id="getExamSchoolInfo" parameterType="map" resultType="map">
        SELECT
        te.`exam_id` examId,
        te.`exam_name` examName,
        te.`exam_type` examType,
        te.`grade_type` gradeType,
        te.`grade_year` gradeYear,
        te.`exam_status` examStatus,
        te.`start_date` startDate,
        te.`end_date` endDate,
        tes.`school_id` schoolId,
        tes.`school_name` schoolName,
        tes.`grade_id` gradeId
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE te.exam_id = #{examId}
    </select>

    <!-- 获取考试班级 -->
    <select id="getExamClass" parameterType="map" resultType="map">
        SELECT
        `exam_id` examId,
        `class_id` classId,
        `school_id` schoolId,
        `class_name` className
        FROM t_exam_class
        WHERE exam_id = #{examId}
    </select>

<!--  查询试卷关联的所有考试  -->
    <select id="getExamListByPaperId" parameterType="map" resultType="map">
        SELECT
            exam_id
        FROM t_exam_paper
        WHERE paper_id = #{paperId}
        GROUP BY exam_id
    </select>

<!--  更新item的questionNumber TODO sharding7 checkExamId -->
    <update id="updateExamItemQuestionNumber" parameterType="map">
        UPDATE t_exam_item
        SET question_number = #{newQuestionNumber},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE exam_id = #{examId}
        AND paper_id = #{paperId}
        AND question_number = #{oldQuestionNumber}
    </update>



    <select id="getExamStudentList" parameterType="map" resultType="map">
        select
        student_id studentId,
        class_id classId,
        school_id schoolId,
        student_name studentName
        from t_exam_result
        where exam_id = #{examId}
        <if test="classId != null">
            AND class_id = #{classId}
        </if>
        and result_status=1
    </select>

    <select id="getExamClassPaper" parameterType="map" resultType="map">
        select
        tecp.exam_id examId,
        tecp.class_id classId,
        tecp.class_name className
        from t_exam_class_paper tecp
        inner join t_exam_paper tep on tecp.exam_id = tep.exam_id and tecp.paper_id = tep.paper_id
        where tep.exam_id = #{examId}
        AND tecp.class_id IN
        <foreach collection="classIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tep.course_id IN
        <foreach collection="courseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getExamExistUploaderByCourse" parameterType="map" resultType="map">
        select
        exam_id examId,
        school_id schoolId,
        worker_type workerType,
        teacher_id teacherId,
        user_id userId,
        worker_name workerName,
        worker_phone workerPhone
        from t_exam_worker
        where exam_id = #{examId} and course_id = #{examWorkerCourseId} and worker_type = 3
    </select>

    <!-- 获取考试班级 -->
    <select id="getExamClassId" parameterType="map" resultType="string">
        SELECT class_id
        FROM t_exam_class
        WHERE exam_id = #{examId}
    </select>

    <!-- 获取在 t_exam_result 中但不在 t_exam_student 中的学生 -->
    <select id="getStudentIdNeedUpdate" parameterType="map" resultType="long">
        select student_id  studentId
        from t_exam_result
        where exam_id = #{examId} and student_id not in (select student_id from t_exam_student where exam_id = #{examId})
        group by student_id;
    </select>

    <update id="updateExamStatus" parameterType="map">
        UPDATE
            t_exam
        SET
            exam_status = #{examStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE
            exam_id = #{examId}
    </update>

    <update id="updateExamSchoolStatus" parameterType="map">
        UPDATE t_exam_school
        SET exam_school_status = #{examStatus},
            modifier_id        = #{userId},
            modifier_name      = #{userName},
            modify_date_time   = #{currentTime}
        WHERE exam_id = #{examId}
    </update>

    <!--撤销公布时调用，将按班级+自动公布的考试置为非自动公布-->
    <update id="closeExamAutoPublish" parameterType="map">
        UPDATE
            t_exam
        SET
            auto_publish = 0,
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE
            exam_id = #{examId}
    </update>

    <update id="updateExamStatusForStudyGuide" parameterType="map">
        update t_exam
        set exam_status = #{updateExamStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
          and exam_status != #{updateExamStatus}
    </update>

    <select id="getExamByExamPaperId" parameterType="map" resultType="map">
        select te.exam_id examId,
               te.exam_type examType
        from t_exam te
                 inner join t_exam_paper tep on te.exam_id = tep.exam_id
        where tep.exam_paper_id = #{examPaperId} limit 1
    </select>

    <select id="getExamAnswerCardSingle" parameterType="map" resultType="map">
        select teu.exam_id          examId,
               teu.exam_uploader_id examUploaderId,
               tac.answer_card_id   answerCardId
        from t_exam_uploader teu
                 inner join t_answer_card tac
                            on teu.exam_id = tac.exam_id and teu.exam_uploader_id = tac.exam_uploader_id
        where teu.exam_id = #{examId} limit 1
    </select>

    <select id="getRegionExamByRegionId" parameterType="long" resultType="com.dongni.exam.bean.bo.ExamNameBO">
        select t.* from (
        select te.exam_id examId,
               te.exam_name examName,
               te.exam_type examType,
               taem.area_id areaId,
               taem.area_name areaName,
               te.start_date startDate
        from t_area_exam_mapping taem
        inner join t_exam te on taem.exam_id = te.exam_id
        where taem.area_id = #{areaId}
          and te.exam_type in (7, 11)
        and exists(select 1 from t_exam_stat tes where te.exam_id = tes.exam_id and tes.deleted = 0)
        group by te.exam_id
        union all
        select te.exam_id examId,
               te.exam_name examName,
               te.exam_type examType,
               tea.area_id areaId,
               tea.area_name areaName,
               te.start_date startDate
        from t_exam te
        inner join t_exam_area tea on te.exam_id = tea.exam_id
        where te.exam_id in (
            select exam_id from t_exam_area where area_id = #{areaId}
        )
        and te.exam_type = 10
        and exists(select 1 from t_exam_stat tes where te.exam_id = tes.exam_id and tes.deleted = 0)
        group by te.exam_id
        having min(area_id) = #{areaId}
        ) t
        order by t.startDate desc, t.examId desc
    </select>

    <select id="getExamIdsByStartDateAndEndDate" parameterType="map" resultType="long">
        select exam_id
        from t_exam
        where grade_type in (1,2,3,4,5,6,7,8,9)
          and exam_type not in (7,10,11)
          and exam_status > 1
          and create_date_time >= #{startDate}
          and create_date_time &lt; #{endDate}
        order by create_date_time desc
    </select>

    <select id="getExamIdsByExamType" parameterType="map" resultType="long">
        select exam_id
        from t_exam
        where exam_type = #{examType}
        order by create_date_time desc
    </select>

    <!--判断是否还有阅卷未完成的考试-->
    <select id="countUnReadComplete" parameterType="long" resultType="long">
        SELECT exam_id
        FROM t_exam_paper
        WHERE exam_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND exam_paper_status &lt; 20
        GROUP BY exam_id
        HAVING COUNT(1) &gt; 0
    </select>

    <!--判断是否还有阅卷未完成的考试-->
    <select id="getUnReadCompleteExamName" parameterType="long" resultType="String">
        SELECT
            te.exam_name
        FROM
        t_exam te
        JOIN t_exam_paper tep ON te.exam_id = tep.exam_id
        WHERE te.exam_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tep.exam_paper_status &lt; 20
        GROUP BY te.exam_id
        HAVING COUNT(1) &gt; 0
    </select>

    <!--判断是否还有阅卷完成但未满一个月的考试-->
    <select id="countReadComplete" parameterType="long" resultType="long">
        SELECT exam_id
        FROM t_exam_paper
        WHERE exam_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND modify_date_time &gt;= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        GROUP BY exam_id
        HAVING COUNT(1) &gt; 0
    </select>

    <!--判断是否还有阅卷完成但未满一个月的考试-->
    <select id="getReadCompleteExamName" parameterType="long" resultType="String">
        SELECT
            te.exam_name
        FROM
            t_exam te
        JOIN t_exam_paper tep ON te.exam_id = tep.exam_id
        WHERE te.exam_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tep.modify_date_time &gt;= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        GROUP BY te.exam_id
        HAVING COUNT(1) &gt; 0
    </select>
    <select id="getHomeworkExamIdsByCreateDateTime" parameterType="map" resultType="long">
        select te.exam_id
        from t_exam te
                 inner join t_exam_school tes on te.exam_id = tes.exam_id
        where te.exam_type = #{examType}
          and tes.school_id = #{schoolId}
          and te.create_date_time >= #{createDateTime}
        order by te.create_date_time desc
    </select>

    <!--根据examId批量获取考试-->
    <select id="getByExamIds" parameterType="long" resultType="map">
        SELECT te.exam_id examId,
               te.exam_name examName,
               te.start_date startDate,
               te.exam_type examType
        FROM t_exam te
        WHERE te.exam_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getExamIdsCreateByOwn" parameterType="map" resultType="long">
        SELECT exam_id
        FROM t_exam
        WHERE exam_id IN
        <foreach collection="examIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND creator_id = #{userId}
    </select>

    <select id="getExamInfoForVacuum" parameterType="long" resultType="map">
        SELECT exam_id          examId,
               modify_date_time modifyDateTime
        FROM t_exam
        WHERE exam_id = #{examId}
    </select>

    <!--  根据年级获取状态是已完成的、非异步联考的考试数量  -->
    <select id="getExamCountByGradeForWrongBook" parameterType="map" resultType="int">
        select count(1)
        from t_exam te
        inner join t_exam_school tes on te.exam_id = tes.exam_id
        where tes.school_id = #{schoolId} and tes.grade_id = #{gradeId} and te.exam_type != 11 and te.exam_status in (2,3)
    </select>

    <!--  根据年级获取状态是已完成的、非异步联考的考试  -->
    <select id="getExamListByGradeForWrongBook" parameterType="map" resultType="map">
        select
            te.exam_id examId,
            te.exam_name examName,
            te.exam_type examType,
            te.start_date startDate
        from t_exam te
        inner join t_exam_school tes on te.exam_id = tes.exam_id
        where tes.school_id = #{schoolId} and tes.grade_id = #{gradeId} and te.exam_type != 11 and te.exam_status in (2,3)
        order by te.start_date desc, te.exam_id desc
        limit #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
    </select>

    <select id="getExamIdBySchUserFilter" parameterType="map" resultType="long">
        select distinct te.exam_id
        from t_exam te
        inner join t_exam_school tes on te.exam_id = tes.exam_id
        <if test="courseIds != null">
            inner join t_exam_course tec on te.exam_id = tec.exam_id and tec.course_id in
            <foreach collection="courseIds" item="courseId" open="(" close=")" separator=",">
                #{courseId}
            </foreach>
        </if>
        where te.exam_id = #{examId}
          and tes.school_id = #{schoolId}
        <if test="gradeIds != null ">
            and tes.grade_id in
            <foreach collection="gradeIds" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
    </select>

    <select id="getExamOfIndividualWrongBook" parameterType="com.dongni.tiku.individual.wrong.book.bean.dto.ExamQueryDTO"
            resultType="com.dongni.tiku.individual.wrong.book.bean.vo.ExamSelectionVO">
        SELECT te.exam_id examId,
               te.exam_name examName,
               te.start_date startDate,
               te.exam_type examType,
               tep.paper_id paperId,
               tep.paper_name paperName,
               tep.course_id courseId,
               tep.course_name courseName
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_paper tep ON te.exam_id = tep.exam_id
        WHERE te.exam_type IN
        <foreach collection="examTypes" item="examType" open="(" separator="," close=")">
            #{examType}
        </foreach>
        AND tep.course_id IN
        <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
        AND tes.grade_id = #{gradeId}
        AND te.start_date BETWEEN #{startTime} AND #{endTime}
        AND te.exam_status IN (2, 3)
        ORDER BY te.start_date DESC
    </select>
</mapper>
