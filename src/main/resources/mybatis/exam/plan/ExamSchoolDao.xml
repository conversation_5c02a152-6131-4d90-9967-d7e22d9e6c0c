<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.newmark.dao.IExamSchoolDao">
    
    <insert id="insert" parameterType="com.dongni.newmark.bean.entity.ExamSchool">
        insert into t_exam_school (
            exam_id,
            area_id,
            school_id,
            school_name,
            grade_id,
            grade_type,
            grade_name,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        values
            (
            #{examId},
            #{areaId},
            #{schoolId},
            #{schoolName},
            #{gradeId},
            #{gradeType},
            #{gradeName},
            #{creatorId},
            #{creatorName},
            #{createDateTime},
            #{modifierId},
            #{modifierName},
            #{modifyDateTime}
            )
    </insert>
    <update id="updateStatus">
        update t_exam_school
        set exam_school_status = #{status},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
    </update>

    <select id="findByExamId" resultType="com.dongni.newmark.bean.entity.ExamSchool">
        select *
        from t_exam_school
        where exam_id = #{examId}
    </select>
</mapper>