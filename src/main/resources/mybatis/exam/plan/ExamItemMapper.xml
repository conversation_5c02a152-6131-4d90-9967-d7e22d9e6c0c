<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamItemMapper">

    <!-- 获取item的文件路径，用于删除使用 --> <!-- TODO sharding7 checkExamId -->
    <select id="getExamItemFilePath" parameterType="map" resultType="string">
        SELECT save_file_url
        FROM t_exam_item
        WHERE exam_id = #{examId}
          AND paper_id = #{paperId}
          AND school_id = #{schoolId}
          AND student_id = #{studentId}
          AND save_file_url IS NOT NULL
    </select>
    <!-- 获取item data--> <!-- TODO sharding7 checkExamId -->
    <select id="getExamItemList" parameterType="map" resultType="map">
        SELECT
               tei.`exam_item_id` examItemId,
               tei.`exam_id` examId,
               tei.`school_id` schoolId,
               tei.`class_id`  classId,
               tei.`student_id` studentId,
               tei.`paper_id` paperId,
               tei.`course_id` courseId,
               tei.`question_number` questionNumber,
               tei.`structure_number` structureNumber,
               tei.`read_type` readType,
               tei.`read_status` readStatus,
               tei.`recognition_value` recognitionValue,
               tei.`score_value` scoreValue,
               tei.`save_file_url` saveFileUrl,
               tei.finally_score finallyScore
        FROM t_exam_item tei
        WHERE tei.exam_id = #{examId}
          AND tei.paper_id = #{paperId}
          AND tei.student_id = #{studentId}
        ORDER BY tei.question_number
    </select>

    <!-- 删除item 考试工具箱-修正成绩-设为缺考 -->
    <delete id="deleteExamItem" parameterType="map">
        DELETE t1
        FROM t_exam_item t1
        LEFT JOIN t_paper_mark_record t2 ON t1.exam_item_id = t2.exam_item_id
        WHERE t1.exam_id = #{examId}
          AND t1.paper_id = #{paperId}
          AND t1.school_id = #{schoolId}
          AND t1.student_id = #{studentId}
    </delete>

    <!-- 备份学生item 考试工具箱-修正成绩-零分学生处理-全部设为缺考 -->
    <insert id="insertExamItemMarkAbsentByStuIds" parameterType="map">
        replace into t_exam_item_mark_absent(
        exam_item_id,exam_id,school_id,
        class_id,student_id,paper_id,
        course_id,question_number,structure_number,
        read_type,read_status,recognition_value,
        score_value,finally_score,save_file_url,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )
        select
        exam_item_id,exam_id,school_id,
        class_id,student_id,paper_id,
        course_id,question_number,structure_number,
        read_type,read_status,recognition_value,
        score_value,finally_score,save_file_url,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        from t_exam_item
        where exam_id = #{examId}
        and paper_id = #{paperId}
        <if test="schoolId != null and schoolId != ''">
            and school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            and class_id = #{classId}
        </if>
        and student_id in
        <foreach collection="studentIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </insert>

    <!-- 删除item 考试工具箱-修正成绩-零分学生处理-全部设为缺考 -->
    <delete id="deleteExamItemByStuIds" parameterType="map">
        DELETE t1
        FROM t_exam_item t1
        LEFT JOIN t_paper_mark_record t2 ON t1.exam_item_id = t2.exam_item_id
        WHERE t1.exam_id = #{examId}
          AND t1.paper_id = #{paperId}
        <if test="schoolId != null and schoolId != ''">
            and t1.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            and t1.class_id = #{classId}
        </if>
        AND t1.student_id in
        <foreach collection="studentIds" item="studentId" separator="," open="(" close=")">
            #{studentId}
        </foreach>
    </delete>

    <!-- 删除item 考试工具箱-修正成绩-设为缺考 TODO sharding9-->
    <delete id="batchDeleteExamItem" parameterType="map">
        DELETE FROM t_exam_item
        WHERE exam_id = #{examId}
          AND paper_id = #{paperId}
          AND school_id = #{schoolId}
          AND student_id IN
          <foreach collection="studentIds" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
    </delete>

    <!-- 备份学生item,防止误删，后期对垃圾数据清理 --> <!-- TODO sharding7 checkExamId -->
    <insert id="insertExamItemMarkAbsent" parameterType="map">
        REPLACE INTO t_exam_item_mark_absent(
            exam_item_id,exam_id,school_id,
            class_id,student_id,paper_id,
            course_id,question_number,structure_number,
            read_type,read_status,recognition_value,
            score_value,finally_score,save_file_url,
            creator_id,creator_name,create_date_time,
            modifier_id,modifier_name,modify_date_time
        )
        SELECT
            exam_item_id,exam_id,school_id,
            class_id,student_id,paper_id,
            course_id,question_number,structure_number,
            read_type,read_status,recognition_value,
            score_value,finally_score,save_file_url,
            creator_id,creator_name,create_date_time,
            modifier_id,modifier_name,modify_date_time
        FROM t_exam_item
        WHERE exam_id = #{examId}
          AND paper_id = #{paperId}
          AND student_id = #{studentId}
    </insert>

    <!-- 备份学生item,防止误删，后期对垃圾数据清理 TODO sharding9-->
    <insert id="batchInsertExamItemMarkAbsent" parameterType="map">
        REPLACE INTO t_exam_item_mark_absent(
            exam_item_id,exam_id,school_id,
            class_id,student_id,paper_id,
            course_id,question_number,structure_number,
            read_type,read_status,recognition_value,
            score_value,finally_score,save_file_url,
            creator_id,creator_name,create_date_time,
            modifier_id,modifier_name,modify_date_time
        )
        SELECT
            exam_item_id,exam_id,school_id,
            class_id,student_id,paper_id,
            course_id,question_number,structure_number,
            read_type,read_status,recognition_value,
            score_value,finally_score,save_file_url,
            creator_id,creator_name,create_date_time,
            modifier_id,modifier_name,modify_date_time
        FROM t_exam_item
        WHERE exam_id = #{examId}
          AND paper_id = #{paperId}
          AND student_id IN
          <foreach collection="studentIds" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
    </insert>

    <!--发布考试 成绩导入模式用 ================================================================ start -->
    <delete id="deleteExamItemForScoreImport" parameterType="map">
        DELETE FROM t_exam_item
        WHERE exam_id = #{examId}
        <if test="paperId != null and paperId != ''">
            AND paper_id = #{paperId}
        </if>
    </delete>

    <insert id="insertExamItemForScoreImport" parameterType="map">
        INSERT INTO `t_exam_item` (
        `exam_id`,
        `school_id`,
        `class_id`,
        `student_id`,
        `paper_id`,
        `course_id`,
        `question_number`,
        `structure_number`,
        `read_type`,
        `read_status`,
        `recognition_value`,
        `score_value`,
        `finally_score`,
        `save_file_url`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="list" separator="," item="item">
            (
            #{item.examId},
            #{item.schoolId},
            #{item.classId},
            #{item.studentId},
            #{item.paperId},
            #{item.courseId},
            #{item.questionNumber},
            #{item.structureNumber},
            #{item.readType},
            #{item.readStatus},
            #{item.recognitionValue},
            #{item.scoreValue},
            #{item.finallyScore},
            #{item.saveFileUrl},
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
    </insert>
    <!--发布考试 成绩导入模式用 ================================================================ end -->

    <delete id="deleteExamItemForDeclare" parameterType="map">
        DELETE tei
        FROM t_absent_declaration tad
        JOIN t_absent_declaration_student tads
            ON tads.absent_declaration_id = tad.absent_declaration_id
        JOIN t_absent_declaration_course tadc
            ON tadc.absent_declaration_id = tads.absent_declaration_id
            AND tadc.student_id = tads.student_id
        JOIN t_exam_result ter
            ON ter.exam_id = tadc.exam_id
            AND ter.student_id = tadc.student_id
            AND ter.course_id = tadc.course_id
        JOIN t_exam_item tei
            ON tei.exam_id = tadc.exam_id
            AND tei.student_id = tadc.student_id
            AND tei.paper_id = ter.paper_id
        WHERE tad.absent_declaration_id = #{absentDeclarationId}
    </delete>

    <update id="updateQuestionNumber" parameterType="map">
        UPDATE t_exam_item
        SET question_number = #{newQuestionNumber},
            read_type = #{readType},
            modifier_id = #{userId},
            modifier_name = #{userName} ,
            modify_date_time = #{currentTime}
        WHERE paper_id = #{paperId} AND question_number = #{oldQuestionNumber}
            AND exam_id IN
        <foreach collection="examIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateItemScore" parameterType="map">
        update t_exam_item
        set finally_score = #{readScore},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{modifyTime}
        where exam_item_id = #{examItemId}
    </update>

    <update id="updateRecord" parameterType="map">
        update t_paper_mark_record
        set read_score = #{readScore},
            point_detail = #{pointDetail},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{modifyTime}
        where read_record_id = #{readRecordId}
    </update>

    <select id="findStuIdByItemId" parameterType="map" resultType="long">
        select student_Id
        from t_exam_item
        where exam_item_id = #{examItemId};
    </select>

    <select id="findAnswerCardByItemId" parameterType="map" resultType="map">
        select distinct tac.exam_uploader_id examUploaderId,
               tac.student_id studentId,
               tac.answer_card_code answerCardCode
        from t_exam_item tei, t_answer_card tac ,t_exam_uploader teu
        where tei.exam_item_id = #{examItemId}
          and tei.exam_id=teu.exam_id and tei.paper_id=teu.paper_id
          and teu.exam_uploader_id=tac.exam_uploader_id and tei.student_id=tac.student_id;
    </select>

    <select id="findByStuId" parameterType="map" resultType="string">
        select group_concat(exam_item_id)
        from t_exam_item
        where exam_id =#{examId}
        and paper_id = #{paperId}
        and student_id = #{studentId}
        and question_number in
        <foreach collection="qns" item="qn" separator="," open="(" close=")">
            #{qn}
        </foreach>
        order by question_number ;
    </select>

    <select id="findRecordsByUserId" parameterType="map" resultType="map">
        select paper_read_id paperReadId,
               modifier_id modifierId,
               modifier_name modifierName,
               exam_item_id examResultItemId,
               modify_date_time modifyDateTime
        from t_paper_mark_record
        where paper_read_id in
        <foreach collection="paperReadIds" item="paperReadId" separator="," open="(" close=")">
            #{paperReadId}
        </foreach>
        and modifier_id = #{userId}
        and choice_status = 1
        order by modify_date_time desc
    </select>

    <select id="findIdByItemId" parameterType="String" resultType="long">
        select read_record_id
        from t_paper_mark_record
        where exam_item_id = #{examItemId} and choice_status=1
    </select>


    <!--删除学校中途不参与考试的学生item-->
    <delete id="deleteExamItemForSchoolAbsent" parameterType="map">
        DELETE FROM t_exam_item
        WHERE exam_id = #{examId}
        AND paper_id = #{paperId}
        AND school_id = #{schoolId}
    </delete>

    <!--获取学生小题分-->
    <select id="getStudentExamItemListForWrongBook" parameterType="map" resultType="map">
        SELECT
            tei.exam_id                 examId,
            tei.school_id               schoolId,
            tei.class_id                classId,
            tei.student_id              studentId,
            tei.paper_id                paperId,
            tei.course_id               courseId,
            tei.question_number         questionNumber,
            tei.structure_number        structureNumber,
            tei.read_type               readType,
            tei.recognition_value       recognitionValue,
            tqs.score_value             scoreValue,
            tei.finally_score           finallyScore,
            tqs.question_id             questionId,
            tqs.course_name             courseName,
            tqs.question_type           questionType,
            tqs.question_type_name      questionTypeName,
            tqs.correct_answer          correctAnswer
        FROM
            t_question_structure tqs
        JOIN t_exam_item tei ON tqs.question_number = tei.question_number AND tqs.paper_id = tei.paper_id
        WHERE
            tei.exam_id = #{examId} AND tqs.paper_id = #{paperId}
          <if test="studentId != null and studentId != ''">
              AND tei.student_id = #{studentId}
          </if>
          <if test="courseId != null and courseId != ''">
              AND tei.course_id = #{courseId}
          </if>
          <if test="questionId != null and questionId != ''">
              AND tqs.question_id = #{questionId}
          </if>
          <if test="questionIdList != null and questionIdList.size() > 0">
              AND tqs.question_id IN
              <foreach collection="questionIdList" item="item" separator="," open="(" close=")">
                  #{item}
              </foreach>
          </if>
        ORDER BY tei.question_number
    </select>

    <delete id="deleteByAnswerCardCodes" parameterType="map">
        DELETE
            tei.*
        FROM t_exam_item tei,
            t_question_structure tqs
        WHERE tei.exam_id = #{examId}
        AND tei.paper_id = #{paperId}
        AND tei.question_number = tqs.question_number
        AND tqs.paper_id = #{paperId}
        AND tei.student_id IN
        (
        SELECT t.student_id
        FROM t_answer_card t
        WHERE t.exam_uploader_id = #{examUploaderId} and t.student_id !=0
        <if test="batchId != null and batchId != ''">
            and t.batch_id = #{batchId}
        </if>
        <if test="answerCardCodeList != null and answerCardCodeList.size()>0 ">
            and t.answer_card_code IN
            <foreach collection="answerCardCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        )
    </delete>

    <update id="updateExamItemScoreValue" parameterType="map">
        update t_exam_item
            set finally_score = #{finallyScore}
            <if test="readStatus != null and readStatus != ''">
                ,read_status = #{readStatus}
            </if>
        where exam_item_id = #{examItemId}
    </update>


    <select id="isAllNeedReadStatus" parameterType="map" resultType="int">
        SELECT count(1) FROM t_exam_item tei, t_answer_card tac, t_exam_uploader teu
        WHERE tei.exam_id = #{examId} AND tei.paper_id = #{paperId}
          AND teu.exam_uploader_id = #{examUploaderId}
          AND tac.exam_uploader_id = teu.exam_uploader_id
          AND tei.student_id = tac.student_id
          AND tei.read_type = 2 AND tei.read_status = 1
    </select>
    <select id="getQuestionValues" parameterType="map" resultType="map">
        SELECT
             recognition_value          recognitionValue,
             question_number            questionNumber
        FROM t_exam_item
        WHERE exam_id = #{examId} AND paper_id = #{paperId} AND school_id = #{schoolId}
          AND student_id = #{studentId} AND read_type = 1;
    </select>

    <select id="getStudentItemStatistic" parameterType="map" resultType="map">
        SELECT
            Max(c) maxItemCount,
            Min(c) minItemCount
        FROM (
             SELECT /*+INL_JOIN(teu,tac,tei)*/ COUNT(1) c
             FROM t_exam_uploader teu
             INNER JOIN t_answer_card tac ON
                 tac.exam_uploader_id = teu.exam_uploader_id AND tac.page_number = 1
             INNER JOIN t_exam_item tei ON
                 tei.exam_id = teu.exam_id AND tei.paper_id = teu.paper_id
                     AND tei.question_number in <foreach collection="questionNumbers" item="item" open="(" separator="," close=")">#{item}</foreach>
                      AND tei.student_id = tac.student_id
             WHERE teu.exam_uploader_id = #{examUploaderId}
             GROUP BY tei.student_id
             ) tmp;
    </select>

    <select id="getExamIdListGtForVacuum" parameterType="map" resultType="long">
        SELECT exam_id examId
        FROM t_exam_item
        WHERE exam_id > #{examId}
        GROUP BY exam_id
        ORDER BY exam_id
        LIMIT #{limit}
    </select>

    <select id="getExamItemMaxModifyForVacuum" parameterType="map" resultType="map">
        SELECT
            exam_id               examId,
            MAX(modify_date_time) lastModifyDateTime
        FROM t_exam_item
        WHERE exam_id = #{examId}
    </select>

    <select id="getExamItemSaveFileUrlCountForVacuum" parameterType="map" resultType="int">
        SELECT COUNT(DISTINCT(save_file_url))
        FROM t_exam_item
        WHERE exam_id = #{examId}
        AND save_file_url IS NOT NULL
        AND save_file_url &lt;> ''
        <if test="answerCardDirPathList != null and answerCardDirPathList.size() > 0">
            <foreach collection="answerCardDirPathList" item="answerCardDirPath">
                AND save_file_url NOT LIKE CONCAT(#{answerCardDirPath}, '%')
            </foreach>
        </if>
    </select>

    <select id="getExamItemSaveFileUrlListForVacuum" parameterType="map" resultType="string">
        SELECT DISTINCT(save_file_url) saveFileUrl
        FROM t_exam_item
        WHERE exam_id = #{examId}
        AND save_file_url IS NOT NULL
        AND save_file_url &lt;> ''
        <if test="answerCardDirPathList != null and answerCardDirPathList.size() > 0">
            <foreach collection="answerCardDirPathList" item="answerCardDirPath">
                AND save_file_url NOT LIKE CONCAT(#{answerCardDirPath}, '%')
            </foreach>
        </if>
    </select>

    <select id="getExamItemUrlByIds" parameterType="map" resultType="map">
        SELECT exam_item_id  examItemId,
               ifnull(save_file_url, '') saveFileUrl
        FROM t_exam_item
        WHERE exam_item_id in
        <foreach collection="examItemIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="hasExamItem" parameterType="long" resultType="long">
        SELECT exam_item_id
        FROM t_exam_item
        WHERE exam_id IN
        <foreach collection="list" item="examId" open="(" close=")" separator=",">
            #{examId}
        </foreach>
        LIMIT 1
    </select>

    <update id="rollbackExamItemReadStatus" parameterType="map">
        update t_exam_item tei, t_exam_result ter
            set read_status = read_status + 10
        where tei.exam_id = #{examId} and tei.paper_id = #{paperId}
            and tei.question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach> )
            and ter.exam_id = #{examId} and ter.paper_id = #{paperId} and ter.school_id = #{schoolId}
            and tei.student_id = ter.student_id and tei.read_status in (0, 1)
            <if test="classIdList != null and classIdList.size > 0">
                and ter.class_id in(<foreach collection="classIdList" item="item" separator=",">#{item}</foreach>)
            </if>;
    </update>


    <delete id="removeExamItemStudentAbsent" parameterType="map">
        delete from t_exam_item
        where exam_id = #{examId} and paper_id = #{paperId}
            and question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
            and student_id in (<foreach collection="studentIdList" item="item" separator=",">#{item}</foreach>)
            and read_status in (10, 11);
    </delete>

    <delete id="deleteErrorStudentItems" parameterType="map">
        delete tei.* from  t_exam_result ter
            join t_exam_item tei on tei.exam_id =  #{examId} and tei.paper_id = #{paperId}
                and tei.question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
                and tei.read_status in (10, 11)
                and tei.student_id = ter.student_id
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId} and ter.school_id = #{schoolId}
        <if test="classIdList != null and classIdList.size() > 0">
            and ter.class_id in (<foreach collection="classIdList" index="index" item="item" separator=",">#{item}</foreach>)
        </if>
    </delete>

    <select id="getAIItems" parameterType="map" resultType="com.dongni.exam.mark.ai.bean.ExamItem">
        select
               tei.exam_item_id  examItemId,
               tei.save_file_url url,
                tpr.paper_read_id paperReadId
        from t_paper_read tpr, t_exam_item tei, t_ai_mark tam
        where tpr.paper_read_id = #{paperReadId}
          and tei.exam_id = tpr.exam_id and tei.paper_id = tpr.paper_id and tei.question_number = tpr.question_number
          and tam.paper_read_id = tpr.paper_read_id and tam.exam_item_id = tei.exam_item_id and tam.status = 0;
    </select>

    <select id="getSchoolIdByExamItemId" parameterType="long" resultType="long">
        select school_id
        from t_exam_item
        where exam_item_id = #{examItemId}
    </select>

    <select id="getByExamItemId" parameterType="long" resultType="map">
        select exam_item_id examItemId,
               exam_id examId,
               paper_id paperId,
               course_id courserId,
               question_number questionNumber,
               structure_number structureNumber,
               recognition_value recognitionValue,
               read_type readType,
               score_value scoreValue,
               finally_score finallyScore,
               save_file_url saveFileUrl
        from t_exam_item
        where exam_item_id = #{0}
    </select>

    <select id="getByExamItemLogicId" parameterType="map" resultType="map">
        select exam_item_id examItemId,
               exam_id examId,
               paper_id paperId,
               course_id courserId,
               question_number questionNumber,
               structure_number structureNumber,
               recognition_value recognitionValue,
               read_type readType,
               score_value scoreValue,
               finally_score finallyScore,
               save_file_url saveFileUrl
        from t_exam_item
        where exam_id = #{examId}
            AND paper_id = #{paperId}
            AND question_number = #{questionNumber}
            AND student_id = #{studentId}
    </select>
</mapper>
