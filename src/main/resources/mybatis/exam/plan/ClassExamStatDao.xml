<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.newmark.dao.IClassExamStatDao">

    <insert id="batchInsert">
        insert into t_class_exam_stat (
            exam_id,
            class_id,
            stat_status,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        values
        <foreach collection="classExamStats" separator="," item="item">
            (
            #{item.examId},
            #{item.classId},
            #{item.statStatus},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createDateTime},
            #{item.modifierId},
            #{item.modifierName},
            #{item.modifyDateTime}
            )
        </foreach>
        on duplicate key update
        modifier_id = values(modifier_id),
        modifier_name = values(modifier_name),
        modify_date_time = values(modify_date_time)
    </insert>

    <update id="updateStatStatus">
        update t_class_exam_stat
        set stat_status = #{statStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
          and class_id in
        <foreach collection="classIds" item="classId" separator="," open="(" close=")">
            #{classId}
        </foreach>
    </update>

    <select id="getClassStatStatus" resultType="com.dongni.newmark.bean.dto.ClassStatStatusDTO">
        select
            class_id classId,
            stat_status statStatus
        from t_class_exam_stat
        where exam_id = #{examId}
        and class_id in
        <foreach collection="classIds" item="classId" separator="," open="(" close=")">
            #{classId}
        </foreach>
    </select>

    <select id="getOnePublishedClassExamStatId" resultType="long">
        select class_exam_stat_id
        from t_class_exam_stat
        where exam_id = #{examId}
          and stat_status = 1
        limit 1
    </select>

</mapper>