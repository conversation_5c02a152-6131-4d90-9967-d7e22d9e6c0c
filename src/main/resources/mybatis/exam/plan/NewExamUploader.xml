<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.plan.dao.NewExamUploaderDao">
    <delete id="deleteExamUploaderByIds" parameterType="map">
        delete from t_exam_uploader
        where exam_uploader_id in (<foreach collection="idList" item="item" separator=",">#{item}</foreach> );
    </delete>
    <select id="getMinUploadStatusExamUploaderByUploader" resultType="com.dongni.exam.plan.bean.vo.ExamUploaderVO">
        select
               exam_uploader_id examUploaderId,
               exam_id          examId,
               paper_id         paperId,
               school_id        schoolId,
               uploader_id      uploaderId,
               uploader_name    uploaderName,
               upload_status    uploadStatus,
               default_template_code  templateCode,
               template_type        templateType
        from t_exam_uploader
        where exam_id = #{examId}
          and paper_id = #{paperId}
          and uploader_id = #{uploaderId}
          order by upload_status asc
        limit 1;
    </select>

    <insert id="insertExamUploader" parameterType="com.dongni.exam.plan.bean.dto.ExamUploaderDTO"
            useGeneratedKeys="true" keyColumn="exam_uploader_id" keyProperty="examUploaderId">
        INSERT INTO t_exam_uploader(
            exam_uploader_name,
            exam_id,
            school_id,
            paper_id,
            upload_type,
            upload_status,
            uploader_id,
            uploader_name,
            template_type,
            default_template_code,
            client_upload_status,
            objectitem_recotype,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES (
                     #{uploaderName},
                     #{examId},
                     #{schoolId},
                     #{paperId},
                     #{uploadType},
                     #{uploadStatus},
                     #{uploaderId},
                     #{uploaderName},
                     #{templateType},
                     #{defaultTemplateCode},
                     0,
                     1,
                     #{userId},
                     #{userName},
                     NOW(),
                     #{userId},
                     #{userName},
                     NOW()
                 )
    </insert>

    <select id="getNormalExamUploaderByScanner" parameterType="com.dongni.exam.plan.bean.dto.ExamUploaderDTO"
        resultType="com.dongni.exam.plan.bean.vo.ExamUploaderVO">
        select default_template_code  defaultTemplateCode,
               default_template_code  templateCode,
               template_type templateType
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId}
            and uploader_id = #{uploaderId} and school_id = #{schoolId}
            and upload_type = #{uploadType}
    </select>

    <update id="updateExamUploaderOfAnswerCardPath" parameterType="com.dongni.exam.plan.bean.vo.ExamUploaderVO">
        update t_exam_uploader
            set answer_card_path = #{answerCardPath}
            <if test="checkTemplateStatus != null and checkTemplateStatus == 0">
                , check_template_status = 0
            </if>
        where exam_uploader_id = #{examUploaderId}
    </update>

    <update id="updateUploadStatus" parameterType="map">
        update t_exam_uploader
            set upload_status = 3
        where exam_uploader_id = #{examUploaderId};
    </update>

    <select id="getExamUploaderByExamUploaderId" parameterType="map" resultType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO">
        select teu.exam_uploader_id          examUploaderId,
               teu.exam_uploader_name        examUploaderName,
               teu.upload_type               uploadType,
               teu.exam_id                   examId,
               teu.paper_id                  paperId,
               teu.school_id                 schoolId,
               teu.uploader_id               uploaderId,
               teu.uploader_name             uploaderName,
               group_concat(teuc.class_id order by teuc.class_id separator ',')   classIds,
               group_concat(teuc.class_name order by teuc.class_id separator ',') classNames,
               group_concat(teucs.school_id) careSchoolIds,
               teu.answer_card_path          answerCardPath
        from t_exam_uploader teu
        left join t_exam_uploader_class teuc on teuc.exam_uploader_id = teu.exam_uploader_id
        left join t_exam_uploader_care_school teucs on teucs.exam_id = teu.exam_id and teucs.uploader_id = teu.uploader_id
        where teu.exam_uploader_id = #{examUploaderId}
        group by teu.exam_uploader_id;
    </select>

    <select id="getExamUploaderCountByExamIdAndUploaderId" parameterType="map" resultType="int">
        select count(1) from t_exam_uploader where exam_id = #{examId} and uploader_id = #{uploaderId};
    </select>

    <select id="getExamUploaderByGetUploaderProgressVO" parameterType="com.dongni.exam.plan.bean.vo.GetUploaderProgressVO"
            resultType="com.dongni.exam.plan.bean.vo.ExamUploaderProgressVO">
        select
               teu.exam_id                                                                         examId,
               teu.paper_id                                                                        paperId,
               tep.paper_name                                                                      paperName,
               teu.uploader_id                                                                     uploaderId,
               teu.uploader_name                                                                   uploaderName,
               group_concat(distinct teu.exam_uploader_id order by teu.exam_uploader_id desc separator ',')  examUploaderIds,
               group_concat(distinct teu.upload_status order by teu.exam_uploader_id desc separator ',')     uploadStatusList,
               count(1)                                                                            count,
               min(teu.upload_status)                                                              minUploadStatus,
               max(teu.upload_status)                                                              maxUploadStatus
        from t_exam_uploader teu, t_exam_paper tep
        where teu.exam_id = #{examId}
            and tep.exam_id = teu.exam_id and tep.paper_id = teu.paper_id and tep.course_id = #{courseId}
        group by teu.uploader_id, teu.paper_id
        order by teu.uploader_id, teu.paper_id;
    </select>

    <select id="getExamUploaderProgressList" parameterType="map" resultType="com.dongni.exam.plan.bean.vo.ExamUploaderProgressVO">
        select
             teu.exam_id                                                                       examId,
             teu.paper_id                                                                      paperId,
             tep.paper_name                                                                    paperName,
             teu.uploader_id                                                                   uploaderId,
             teu.uploader_name                                                                 uploaderName,
             teu.default_template_code                                                         defaultTemplateCode,
             teu.template_type                                                                 templateType,
             count(if(teu.upload_status &lt; 7 and tac.answer_card_id is not null, 1, null))      progressingExamUploaderCardCount,
             count(tac.answer_card_id)                                                         uploadAnswerCardCount,
             count(if(tac.error_code &amp; b'00000000000000001111111111111111', 1, null))          exceptionCount,
             count(if(teu.upload_status  &lt; 7
                    and (tac.error_code &amp; b'00000000000000001111111111111111' = 0)
                    and tac.error_code &amp; b'11111111110101110000000000000000' <![CDATA[<>]]> 0, 1, null))        maybeExceptionCount,
             sum(tub.card_exception_num)                                                       cardExceptionNum
        from t_exam_uploader teu
        inner join t_exam_paper tep on tep.exam_id = teu.exam_id and tep.paper_id = teu.paper_id
        left join t_upload_batch tub on tub.exam_uploader_id = teu.exam_uploader_id
        left join t_answer_card tac on tac.exam_uploader_id = teu.exam_uploader_id and tac.batch_id = tub.batch_id
        where teu.exam_id = #{examId}
          and teu.paper_id in (<foreach collection="list" item="item" separator=",">#{item.paperId}</foreach>)
          and teu.uploader_id in (<foreach collection="list" item="item" separator=",">#{item.uploaderId}</foreach>)
        group by teu.paper_id, teu.uploader_id
        order by teu.uploader_id, teu.paper_id;
    </select>

    <select id="getUploaderStats" parameterType="map" resultType="com.dongni.exam.plan.bean.vo.PaperCardVO">
        select
            teu.paper_id                                                                      paperId,
            tep.course_id                                                                     courseId,
            tep.course_name                                                                   courseName,
            tep.paper_name                                                                    paperName,
            count(if(tac.error_code &amp; b'00000000000000001111111111111111', 1, null))          exceptionCount,
            count(if(teu.upload_status  &lt; 7
                    and (tac.error_code &amp; b'00000000000000001111111111111111' = 0)
                    and tac.error_code &amp; b'11111111110101110000000000000000' <![CDATA[<>]]> 0, 1, null))          maybeExceptionCount
        from t_exam_uploader teu,
             t_answer_card tac,
             t_exam_paper tep
        where teu.exam_id = #{examId}
          <if test="uploaderId > 0">
              and teu.uploader_id = #{uploaderId}
          </if>
          and teu.upload_status in (2, 3, 4, 5, 6)
          and teu.exam_uploader_id = tac.exam_uploader_id
          and tep.exam_id = teu.exam_id and tep.paper_id = teu.paper_id
          <if test="courseId != null and courseId > 0">
              and tep.course_id = #{courseId}
          </if>
        group by teu.paper_id;
    </select>

    <select id="getUploaderStatsByCareSchools" parameterType="map" resultType="com.dongni.exam.plan.bean.vo.UploaderStatsVO">
        select
            count(distinct ter.student_id)                                              totalStudentCount,
            count(if(ter.uploaded_status != 0 and ter.result_status = 0, 1, null))      normalStudentCount,
            count(if(ter.result_status = 1, 1, null))                                   absentStudentCount,
            count(if(ter.uploaded_status = 0, 1, null))                                 unuploadedStudentCount,
            count(distinct ter.school_id)                                               totalSchoolCount
        from t_exam_result ter, t_exam_uploader_care_school teucs, t_exam_paper tep
        where tep.exam_id = #{examId}
            <if test="courseId != null and courseId > 0">and tep.course_id = #{courseId}</if>
            and ter.exam_id = tep.exam_id and ter.paper_id = tep.paper_id
            and teucs.exam_id = #{examId} and teucs.school_id = ter.school_id
            and teucs.uploader_id = #{uploaderId};
    </select>

    <select id="getCompletedExamUploaderGroupByUploaderIdCount" resultType="java.lang.Integer">
        select count(distinct uploader_id) from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and upload_status = 7;
    </select>

    <select id="getCompletedExamUploaderGroupByUploaderId"
            resultType="com.dongni.exam.plan.bean.vo.NewExamUploaderVO">
        select
               teu.exam_id                      examId,
               tep.paper_id                     paperId,
               teu.exam_uploader_name           examUploaderName,
               teu.uploader_id                  uploaderId,
               tep.paper_name                   paperName,
               group_concat(distinct upload_status separator ',') uploadStatusList
        from t_exam_uploader teu, t_exam_paper tep
        where teu.exam_id = #{examId} and teu.paper_id = #{paperId} and teu.upload_status = 7
            and tep.exam_id = #{examId} and tep.paper_id = #{paperId}
        group by uploader_id
        order by uploader_id
        limit #{currentIndex}, #{pageSize};
    </select>

    <select id="getCompletedExamUploaderGroupBySchoolIdCount" resultType="java.lang.Integer">
        select count(1) from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and upload_status = 7
        group by school_id;
    </select>

    <select id="getCompletedExamUploaderGroupBySchoolId"
            resultType="com.dongni.exam.plan.bean.vo.NewExamUploaderVO">
        select
            exam_id                      examId,
            paper_id                     paperId,
            exam_uploader_name           examUploaderName,
            uploader_id                  uploaderId
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and upload_status = 7
        group by school_id
        order by school_id
        limit #{currentIndex}, #{pageSize};
    </select>

    <select id="getExamUploaderListByUploaderId" resultType="com.dongni.exam.plan.bean.dto.ExamUploaderDTO">
        select
                exam_uploader_id            examUploaderId,
                uploader_id                 uploaderId,
                upload_type                 uploadType,
                upload_status               uploadStatus,
                default_template_code       defaultTemplateCode,
                template_type               templateType,
                default_template_code       templateCode
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and uploader_id = #{uploaderId};
    </select>

    <select id="getExamUploaderListBySchoolId" resultType="com.dongni.exam.plan.bean.dto.ExamUploaderDTO">
        select
            exam_uploader_id            examUploaderId,
            uploader_id                 uploaderId,
            upload_type                 uploadType,
            upload_status               uploadStatus
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and school_id = #{schoolId};
    </select>

    <select id="getProcessingUploaderList" parameterType="com.dongni.exam.plan.bean.vo.ExamPaperVO"
            resultType="com.dongni.exam.plan.bean.vo.ExamUploaderVO">
        select
              exam_uploader_id      examUploaderId,
              school_id             schoolId,
              upload_status         uploadStatus,
              exam_uploader_name    examUploaderName
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId}
            and upload_status in (2, 3, 4, 5, 6, 7)
            <if test="all == 0 and scanType == 3">
                and uploader_id = #{userId}
            </if>;
    </select>

    <select id="getExamUploaderList" resultType="com.dongni.exam.plan.bean.vo.ExamUploaderVO"  parameterType="com.dongni.exam.plan.bean.vo.ExamPaperVO">
        select
            exam_uploader_id      examUploaderId,
            school_id             schoolId,
            exam_uploader_name    examUploaderName
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId}
    </select>

    <select id="getExamUploaderByUploader" parameterType="map" resultType="com.dongni.exam.plan.bean.vo.ExamUploaderCoursePaperVO">
        select
               teu.exam_uploader_id     examUploaderid,
               teu.paper_id             paperId,
               tep.course_id            courseId,
               tep.course_name          courseName,
               tep.paper_name           paperName
        from t_exam_uploader teu, t_exam_paper tep
        where teu.exam_id = #{examId} and teu.uploader_id = #{uploaderId}
            and tep.exam_id = teu.exam_id and tep.paper_id = teu.paper_id
            and teu.upload_status in (2, 3, 4, 5, 6);
    </select>

    <select id="getProcessExamUploaderIds" resultType="java.lang.Long" parameterType="map">
        select exam_uploader_id
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and upload_status in (2, 3, 4, 5, 6);
    </select>

    <select id="getCompletedExamUploaderCount" resultType="java.lang.Integer">
        select count(1)
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and upload_status in (7);
    </select>

    <select id="getSchoolScanStatusStatistic" parameterType="com.dongni.exam.plan.bean.bo.ExamCourseBO"
            resultType="com.dongni.exam.plan.bean.dto.ExamUploaderScanStatusDTO">
        select tes.exam_id              examId,
               tes.school_name          schoolName,
               tep.paper_id             paperId,
               tep.paper_name           paperName,
               tes.school_id            schoolId,
               group_concat(teu.exam_uploader_id order by exam_uploader_id separator ',') examUploaderIds,
               group_concat(teu.upload_status order by exam_uploader_id separator ',')   uploadStatus
        from t_exam_school tes
        join t_exam_paper tep on tep.exam_id = tes.exam_id
        join t_exam_school_paper tecp on tecp.exam_id = tes.exam_id and tecp.school_id = tes.school_id and tecp.paper_id = tep.paper_id
        left join t_exam_uploader teu on teu.exam_id = tes.exam_id and teu.paper_id = tep.paper_id and teu.school_id = tes.school_id
        where tes.exam_id = #{examId}
        <if test="courseId != null and courseId > 0">
            and tep.course_id = #{courseId}
        </if>
        <if test="schoolIds != null and schoolIds.size() > 0">
            and tes.school_id in (<foreach collection="schoolIds" separator="," item="item">#{item}</foreach>)
        </if>
        group by  tep.paper_id, tes.school_id;
    </select>

    <select id="getClassScanStatusStatistic" parameterType="com.dongni.exam.plan.bean.bo.ExamCourseBO"
            resultType="com.dongni.exam.plan.bean.dto.ExamUploaderScanStatusDTO">
        select
            tec.class_id                                                                             classId,
            tec.class_name                                                                           className,
            group_concat(teuc.exam_uploader_id order by teuc.exam_uploader_id separator ',')         examUploaderIds,
            group_concat(teuc.upload_status order by teuc.exam_uploader_id separator ',')            uploadStatus
        from t_exam_class tec
        left join (
            select teuc.class_id, teu.exam_uploader_id, teu.upload_status
            from t_exam_uploader teu, t_exam_uploader_class teuc
            where teu.exam_id = #{examId} and teu.exam_uploader_id = teuc.exam_uploader_id
        ) teuc on teuc.class_id = tec.class_id
        where tec.exam_id = #{examId}
        group by tec.class_id
    </select>

    <select id="getClassExamUploaderByExamIdAndPaperIds" resultType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO">
        select
            teu.exam_uploader_id                            examUploaderId,
            teu.exam_uploader_name                          examUploaderName,
            group_concat(teuc.class_id separator ',')       classIds,
            teu.upload_status                               uploadStatus,
            teu.upload_type                                 uploadType
        from t_exam_uploader teu
        left join t_exam_uploader_class teuc on teuc.exam_uploader_id = teu.exam_uploader_id
        where teu.exam_id = #{examId}
            <if test="paperIds != null and paperIds.size() > 0">
                and teu.paper_id in (<foreach collection="paperIds" separator="," item="item">#{item}</foreach>)
            </if>
        group by teu.exam_uploader_id desc;
    </select>

    <select id="getProcessingExamUploaderIdsIn5Days" resultType="java.lang.Long">
        select teu.exam_uploader_id from t_exam_uploader teu, t_answer_card tac
        where teu.upload_status in (2, 3, 4, 5, 6) and teu.modify_date_time > SUBDATE(NOW(), interval 5 day)
          and tac.exam_uploader_id = teu.exam_uploader_id
        group by teu.exam_uploader_id
        having count(1) > 0;
    </select>

    <select id="getExamUploaderListByIds" resultType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO">
        select exam_uploader_id             examUploaderId,
               upload_status                uploadStatus
        from t_exam_uploader
        where exam_uploader_id in (<foreach collection="examUploaderIds" separator="," item="item">#{item}</foreach>);
    </select>

    <select id="getUploaderExamUploaderList" resultType="com.dongni.exam.plan.bean.vo.ExamUploaderProgressVO">
        select
            teu.exam_id                                                                             examId,
            teu.paper_id                                                                            paperId,
            tep.paper_name                                                                          paperName,
            teu.uploader_id                                                                         uploaderId,
            teu.uploader_name                                                                       uploaderName,
            teu.default_template_code                                                               defaultTemplateCode,
            teu.template_type                                                                       templateType,
            group_concat(teu.exam_uploader_id order by teu.exam_uploader_id desc separator ',')     examUploaderIds,
            group_concat(teu.upload_status order by teu.exam_uploader_id desc separator ',')        uploadStatusList
        from t_exam_uploader teu
        inner join t_exam_paper tep on tep.exam_id = teu.exam_id and tep.paper_id = teu.paper_id
        where teu.exam_id = #{examId}
        and teu.paper_id in (<foreach collection="list" item="item" separator=",">#{item.paperId}</foreach>)
        and teu.uploader_id in (<foreach collection="list" item="item" separator=",">#{item.uploaderId}</foreach>)
        group by teu.paper_id, teu.uploader_id
        order by teu.uploader_id, teu.paper_id;
    </select>

    <select id="getEasyExamUploaderByExamUploaderId"
            resultType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO">
        select
            exam_uploader_id            examUploaderId,
            upload_status               uploadStatus,
            exam_id                     examId,
            paper_id                    paperId,
            school_id                   schoolId,
            answer_card_path            answerCardPath,
            check_template_status       checkTemplateStatus,
            upload_type                 uploadType
        from t_exam_uploader
        where exam_uploader_id = #{examUploaderId};
    </select>

    <update id="batchUpdateTemplates" parameterType="map">
        <foreach collection="cardTemplates" item="item" separator=";">
            update t_exam_uploader
            set template_type = #{item.templateType},
                default_template_code = #{item.templateCode},
                modify_date_time = NOW()
            where exam_uploader_id = #{item.examUploaderId}
        </foreach>
    </update>

    <update id="handleClassExamUploaderUploadStatus">
        update t_exam_uploader_class
        set upload_status = #{uploadStatus},
            modify_date_time = NOW()
        where exam_uploader_id = #{examUploaderId}
    </update>

    <update id="handleUploadStatus">
        update t_exam_uploader
        set upload_status = #{uploadStatus},
            modify_date_time = NOW()
        where exam_uploader_id = #{examUploaderId}
    </update>

    <select id="getHistoryExamUploaders" resultType="com.dongni.exam.plan.bean.vo.NewExamUploaderVO">
        select distinct
               teu.exam_uploader_id     examUploaderId,
               teu.exam_uploader_name   examUploaderName
        from t_exam_uploader teu
        <if test="type == 2">
            , t_exam_uploader_class teuc
        </if>
        where exam_id = #{examUploaderBO.examId} and paper_id = #{examUploaderBO.paperId}
        <if test="type == 1">
            and school_id = #{examUploaderBO.schoolId}
        </if>    
        <if test="type == 2">
            and school_id = #{examUploaderBO.schoolId}  
            and teuc.exam_uploader_id = teu.exam_uploader_id
            <if test="examUploaderBO.classIdList != null and examUploaderBO.classIdList.size() > 0">
                and teuc.class_id in <foreach collection="examUploaderBO.classIdList" item="item" separator="," open="(" close=")">#{item}</foreach>
            </if>
        </if>
        <if test="type == 3">
            and teu.uploader_id = #{examUploaderBO.uploaderId}
        </if>;
    </select>

    <select id="getMixExamUploaderListByExamId" resultType="com.dongni.exam.plan.bean.vo.NewExamUploaderVO" parameterType="map">
        select
            exam_id                 examId,
            uploader_id             uploaderId,
            exam_uploader_name      examUploaderName,
            exam_uploader_id        examUploaderId,
            upload_type             uploadType,
            upload_status           uploadStatus,
            paper_id                paperId
        from t_exam_uploader
        where exam_id = #{examId}
        order by uploader_id, paper_id;
    </select>

    <select id="getExamUploaderExpInfo" parameterType="map" resultType="com.dongni.exam.plan.bean.bo.ExamUploaderExpBO">
        select
            teu.exam_uploader_id                    examUploaderId,
            count(if(tac.error_code &amp; b'00000000000000001111111111111111' > 0, 1, null))
                                                    expCardCount,
            count(distinct tac.answer_card_code )   codeCount,
            count(distinct if(tac.student_id != 0, tac.student_id, null))   studentCount,
            teu.check_template_status               checkTemplateStatus
        from t_exam_uploader teu, t_answer_card tac
        where teu.exam_uploader_id in
            <foreach collection="scanningExamUploaderIds" item="item" separator="," open="(" close=")">#{item}</foreach>
            and teu.exam_uploader_id = tac.exam_uploader_id
            group by teu.exam_uploader_id;
    </select>


    <select id="getRecognizedPaperIds" resultType="java.lang.Long">
        select distinct paper_id
        from t_exam_uploader teu, t_answer_card tac
        where teu.exam_id = #{examId}
          and tac.exam_uploader_id = teu.exam_uploader_id and tac.student_id != 0;
    </select>

    <select id="getProgressingExamUploaderCount" resultType="java.lang.Integer">
        select count(1)
        from t_exam_uploader
        where exam_id = #{examId}
          and paper_id = #{paperId}
          and upload_status in (2, 3, 4, 5, 6)
    </select>

    <select id="getEducationExamUploaderIds" resultType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO">
        <choose>
          <when test="startDateStr != null and startDateStr != '' and endDateStr != null and endDateStr != ''">
              select
                teu.exam_uploader_id examUploaderId
              from t_exam_uploader teu
              where teu.exam_id = #{examId} and teu.paper_id = #{paperId} and teu.upload_status in (2, 3, 4, 5, 6)
                and teu.create_date_time between #{startDateStr} and #{endDateStr}
          </when>
          <otherwise>
              select
                teu.exam_uploader_id examUploaderId
              from t_exam_uploader teu, t_exam_uploader_class teuc
              where teu.exam_id = #{examId} and teu.paper_id = #{paperId} and teu.upload_status in (2, 3, 4, 5, 6)
              and teuc.exam_uploader_id = teu.exam_uploader_id
              <choose>
                  <when test="classIdList != null and classIdList.size() > 0">
                      and teuc.class_id in
                      <foreach collection="classIdList" item="item" separator="," open="(" close=")">#{item}</foreach>
                  </when>
                  <otherwise>
                      and teuc.class_id = #{classId}
                  </otherwise>
              </choose>
          </otherwise>
        </choose>
    </select>

    <select id="getProgressExamUploader" resultType="com.dongni.exam.plan.bean.dto.ExamUploaderDTO">
        select
            exam_uploader_id    examUploaderId,
            exam_id             examId,
            paper_id            paperId,
            exam_uploader_name  examUploaderName,
            answer_card_path    answerCardPath
        from t_exam_uploader
        where exam_id = #{examId} and paper_id = #{paperId} and uploader_id = #{uploaderId} and upload_status in (2, 3, 4, 5, 6)
        limit 1;
    </select>

    <select id="getEduMaterialsUnFinishedTasks" resultType="java.lang.Integer">
        select count(1)
        from t_exam_uploader
        where uploader_id = #{userId}
          and upload_type = 4 and recognized_status = 0;
    </select>

    <update id="updateRecognizedStatus" parameterType="map">
        update t_exam_uploader
        set recognized_status = #{status}
        where exam_uploader_id in (<foreach collection="examUploaderIds" item="item" separator=",">#{item}</foreach>);
    </update>
</mapper>
