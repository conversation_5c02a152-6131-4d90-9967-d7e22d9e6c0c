<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamListMapper">


    <!-- 作为考试创建人可以查看的考试ID -->
    <select id="getExamIdForCreator" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="classIds != null">
            INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id AND tec.class_id IN
            <foreach collection="classIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec2 ON te.exam_id = tec2.exam_id AND tec2.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tes.school_id = #{schoolId}
        AND te.creator_id = #{userId}
        <if test="gradeIds!= null">
            AND tes.grade_id IN
            <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examType!=null">
            AND te.exam_type = #{examType}
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <select id="getExamIdForConsultant" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="classIds != null">
            INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id AND tec.class_id IN
            <foreach collection="classIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec2 ON te.exam_id = tec2.exam_id AND tec2.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tes.school_id = #{schoolId}
        <if test="gradeIds!= null">
            AND tes.grade_id IN
            <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examType!=null">
            AND te.exam_type = #{examType}
        </if>
        and exists (
        select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 作为阅卷老师可以查看的考试ID -->
    <select id="getExamIdForPaperRead" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_paper_read tpr
                 INNER JOIN t_paper_read_teacher prt ON tpr.paper_read_id = prt.paper_read_id
                 INNER JOIN t_exam te ON tpr.exam_id = te.exam_id
                 INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE prt.teacher_id = #{teacherId}
          AND tes.school_id = #{schoolId}
    </select>

    <!--获取班级关联考试-->
    <select id="getExamByClassId" parameterType="map" resultType="long">
        select distinct te.exam_id
        from t_exam_class_paper tecp,
        t_exam_paper tep,
        t_exam te
        where tecp.exam_id = tep.exam_id
        and tecp.paper_id = tep.paper_id
        and tep.exam_id = te.exam_id
        and tecp.school_id = #{schoolId} and
        <foreach collection="courseClass" item="item" open="(" close=")" separator="or">
            tep.course_id = #{item.courseId} and tecp.class_id in
            <foreach collection="item.classIds" item="classId" open="(" close=")" separator=",">
                #{classId}
            </foreach>
        </foreach>
    </select>

    <!-- 作为任课老师可以查看的考试ID-->
    <select id="getExamIdForClassTeacher" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        inner join t_exam_teacher tet on te.exam_id = tet.exam_id
        WHERE (
        <foreach collection="classCourse" item="item" separator="OR">
            (tet.class_id = #{item.classId} AND tet.course_id = #{item.courseId})
        </foreach>
        )
        <if test="classId != null and classId != ''">
            AND tet.class_id = #{classId}
        </if>
        <if test="examType != null">
            AND te.exam_type = #{examType}
        </if>
        <if test="gradeIds != null">
            and te.exam_id in (
                select tes.exam_id from t_exam_school tes
                where tes.grade_id IN
                <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                    #{s}
                </foreach>
            )
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 作为任课老师可以查看的考试ID-->
    <select id="getExamIdForClassPaper" parameterType="map" resultType="long">
        SELECT DISTINCT tep.exam_id
        FROM t_exam_paper tep
        inner join t_exam_class_paper tecp on tep.exam_id = tecp.exam_id and tep.paper_id = tecp.paper_id
        inner join t_exam_school tes on tecp.exam_id = tes.exam_id and tecp.school_id = tes.school_id
        inner join t_exam te on te.exam_id = tes.exam_id
        WHERE (
        <foreach collection="classCourse" item="item" separator="OR">
            (tecp.class_id = #{item.classId} AND tep.course_id = #{item.courseId})
        </foreach>
        )
        <if test="classId != null and classId != ''">
            AND tecp.class_id = #{classId}
        </if>
        <if test="gradeIds!= null">
            AND tes.grade_id IN
            <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examType!=null">
            AND te.exam_type = #{examType}
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 作为班主任可以查看的考试ID-->
    <select id="getExamIdForClassHeader" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        inner join t_exam_teacher tet on te.exam_id = tet.exam_id
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec ON tet.exam_id = tec.exam_id AND tec.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tet.class_id IN
        <foreach collection="headerClass" item="item" open="(" separator="," close=")">
            #{item.classId}
        </foreach>
          and tet.course_id = 0
        <if test="classId != null and classId != ''">
            AND tet.class_id = #{classId}
        </if>
        <if test="examType != null">
            AND te.exam_type = #{examType}
        </if>
        <if test="gradeIds != null">
            and te.exam_id in (
            select tes.exam_id from t_exam_school tes
            where tes.grade_id IN
            <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
            )
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 作为班主任可以查看的考试ID-->
    <select id="getExamIdForClass" parameterType="map" resultType="long">
        SELECT DISTINCT tec.exam_id
        FROM t_exam_class tec
            inner join t_exam_school tes on tec.exam_id = tes.exam_id and tec.school_id = tes.school_id
            inner join t_exam te on te.exam_id = tec.exam_id
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec2 ON tec.exam_id = tec2.exam_id AND tec2.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tec.class_id IN
        <foreach collection="headerClass" item="item" open="(" separator="," close=")">
            #{item.classId}
        </foreach>
        <if test="classId != null and classId != ''">
            AND tec.class_id = #{classId}
        </if>
        <if test="gradeIds!= null">
            AND tes.grade_id IN
            <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examType!=null">
            AND te.exam_type = #{examType}
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <select id="getExamIdByIdAndCourseCount" parameterType="map" resultType="long">
        select te.exam_id
        from t_exam te
        inner join t_exam_course tec on te.exam_id = tec.exam_id
        where te.exam_id in
        <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by te.exam_id
        <if test="examCourseCount == 1">
            having count(course_id) = 1
        </if>
        <if test="examCourseCount == 2">
            having count(course_id) > 1
        </if>
    </select>

    <!-- 获取考试列表,通过考试ID -->
    <select id="getExamById" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.entry_type entryType,
        te.grade_type gradeType,
        te.grade_year gradeYear,
        te.version,
        tes.exam_school_status examStatus,
        te.start_date startDate,
        te.end_date endDate,
        te.creator_id creatorId,
        te.creator_name creatorName,
        te.correct_mode correctMode,
        tes.school_id schoolId,
        tec.course_name courseName,
        tec.course_id courseId,
        tes.grade_id gradeId
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_course tec ON te.exam_id=tec.exam_id
        WHERE te.exam_id IN
        <foreach collection="examIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="examStatus != null">
            AND tes.exam_school_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="search != null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>

        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()> 0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="courseIds != null">
            AND tec.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
        GROUP BY te.exam_id
        ORDER BY te.start_date DESC,te.exam_id DESC
        <if test="pageSize !=null and currentIndex !=null">
            LIMIT #{currentIndex,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 获取考试id列表,通过考试ID -->
    <select id="getExamIdsById" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_course tec ON te.exam_id = tec.exam_id
        WHERE te.exam_id IN
        <foreach collection="examIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="examStatus != null">
            AND tes.exam_school_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search != null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="courseIds != null">
            AND tec.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 获取考试列表,通过考试ID -->
    <select id="getExamByIdCount" parameterType="map" resultType="int">
        SELECT count(DISTINCT te.exam_id)
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_course tec ON te.exam_id = tec.exam_id
        WHERE te.exam_id IN
        <foreach collection="examIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="examStatus != null">
            AND tes.exam_school_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search != null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="courseIds != null">
            AND tec.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>


    <!-- 通过学校 获取考试列表 -->
    <select id="getExamBySchool" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.entry_type entryType,
        te.grade_type gradeType,
        te.grade_year gradeYear,
        te.version,
        tes.exam_school_status examStatus,
        te.start_date startDate,
        te.end_date endDate,
        te.creator_id creatorId,
        te.creator_name creatorName,
        te.correct_mode correctMode,
        tes.school_id schoolId,
        tes.grade_id gradeId
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE tes.school_id = #{schoolId} and te.exam_id IN
        <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
            #{examId}
        </foreach>
        GROUP BY te.exam_id
        ORDER BY te.start_date DESC,te.exam_id DESC
        <if test="pageSize !=null and currentIndex !=null">
            LIMIT #{currentIndex,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 通过学校 获取考试id列表 -->
    <select id="getExamIdsBySchool" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="classId != null and classId != ''">
            INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id AND tes.school_id = tec.school_id AND tec.class_id = #{classId}
        </if>
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec2 ON te.exam_id = tec2.exam_id AND tec2.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tes.school_id = #{schoolId}
        <if test="examIds != null">
            and te.exam_id IN
            <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        <!-- 年级 -->
        <if test="gradeIds != null ">
            AND tes.grade_id IN
            <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <!-- 学段 -->
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <!-- 学年 -->
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <!-- 考试状态 -->
        <if test="examStatus != null">
            AND tes.exam_school_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <!-- 搜索条件 -->
        <if test="search !=null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <!-- 考试类型 -->
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <!-- 未毕业 -->
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <!-- 已毕业 -->
        <if test="graduateGradeIdList !=null and graduateGradeIdList.size()>0">
            AND tes.grade_id IN
            <foreach collection="graduateGradeIdList" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <!-- 校本录题筛选项 -->
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!-- 单科目、多科目筛选项 -->
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 通过学校 获取考试列表 -->
    <select id="getExamBySchoolCount" parameterType="map" resultType="int">
        SELECT count(DISTINCT te.exam_id)
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="classId != null and classId != ''">
            INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id AND tes.school_id = tec.school_id AND tec.class_id = #{classId}
        </if>
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec2 ON te.exam_id = tec2.exam_id AND tec2.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tes.school_id = #{schoolId}
        <if test="examIds != null">
            and te.exam_id IN
            <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        <if test="gradeIds != null ">
            AND tes.grade_id IN
            <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="examStatus != null">
            AND tes.exam_school_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search !=null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="graduateGradeIdList !=null and graduateGradeIdList.size()>0">
            AND tes.grade_id IN
            <foreach collection="graduateGradeIdList" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 通过联考员 获取考试列表 -->
    <select id="getExamByExaminer" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.entry_type entryType,
        te.grade_type gradeType,
        te.grade_year gradeYear,
        te.version,
        te.exam_status examStatus,
        te.start_date startDate,
        te.end_date endDate,
        te.correct_mode correctMode,
        te.creator_id creatorId,
        te.creator_name creatorName
        FROM t_exam te
        JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE te.creator_id = #{userId}
        <if test="gradeType !=null and gradeType !=''">
            AND te.grade_type = #{gradeType}
        </if>
        <if test="examStatus != null">
            AND te.exam_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search !=null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="gradeIdList != null and gradeIdList.size()>0">
            AND tes.grade_id IN
            <foreach collection="gradeIdList" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
        GROUP BY te.exam_id
        ORDER BY te.start_date DESC,te.exam_id DESC
        <if test="pageSize !=null and currentIndex !=null">
            LIMIT #{currentIndex,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 通过联考员 获取考试id列表 -->
    <select id="getExamIdsByExaminer" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE te.creator_id = #{userId}
        <if test="gradeType !=null and gradeType !=''">
            AND te.grade_type = #{gradeType}
        </if>
        <if test="examStatus != null">
            AND te.exam_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search !=null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="gradeIdList != null and gradeIdList.size()>0">
            AND tes.grade_id IN
            <foreach collection="gradeIdList" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 通过联考员 获取考试列表 -->
    <select id="getExamByExaminerCount" parameterType="map" resultType="int">
        SELECT count(DISTINCT te.exam_id)
        FROM t_exam te
        JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE te.creator_id = #{userId}
        <if test="gradeType !=null and gradeType !=''">
            AND te.grade_type = #{gradeType}
        </if>
        <if test="examStatus != null">
            AND te.exam_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search !=null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="gradeIdList != null and gradeIdList.size()>0">
            AND tes.grade_id IN
            <foreach collection="gradeIdList" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>


    <!-- 获取考试列表,通过课程负责人|年级负责人 -->
    <select id="getExamPlanByDirector" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.entry_type entryType,
        te.grade_type gradeType,
        te.grade_year gradeYear,
        te.version,
        tes.exam_school_status examStatus,
        te.start_date startDate,
        te.end_date endDate,
        te.correct_mode correctMode,
        te.creator_id creatorId,
        te.creator_name creatorName,
        tes.school_id schoolId,
        tes.grade_id gradeId
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        WHERE tes.school_id = #{schoolId} and tes.grade_id IN
        <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
            #{s}
        </foreach>
        and te.exam_id IN
        <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
            #{examId}
        </foreach>
        GROUP BY te.exam_id
        ORDER BY te.start_date DESC,te.exam_id DESC
        <if test="pageSize !=null and currentIndex !=null">
            LIMIT #{currentIndex,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 获取考试id列表,通过课程负责人|年级负责人 -->
    <select id="getExamIdsByDirector" parameterType="map" resultType="long">
        SELECT DISTINCT te.exam_id
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="classId != null and classId != ''">
            INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id AND tes.school_id = tec.school_id AND tec.class_id = #{classId}
        </if>
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec2 ON te.exam_id = tec2.exam_id AND tec2.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tes.school_id = #{schoolId} and tes.grade_id IN
        <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
            #{s}
        </foreach>
        <if test="examIds != null">
            and te.exam_id IN
            <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        <if test="examStatus != null">
            AND tes.exam_school_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search !=null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <!-- 获取考试列表,通过课程负责人|年级负责人 -->
    <select id="getExamByDirectorCount" parameterType="map" resultType="int">
        SELECT count(DISTINCT te.exam_id)
        FROM t_exam te
        INNER JOIN t_exam_school tes ON te.exam_id = tes.exam_id
        <if test="classId != null and classId != ''">
            INNER JOIN t_exam_class tec ON te.exam_id = tec.exam_id AND tes.school_id = tec.school_id AND tec.class_id = #{classId}
        </if>
        <if test="courseIds != null">
            INNER JOIN t_exam_course tec2 ON te.exam_id = tec2.exam_id AND tec2.course_id IN
            <foreach collection="courseIds" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        WHERE tes.school_id = #{schoolId} and tes.grade_id IN
        <foreach collection="gradeIds" item="s" open="(" close=")" separator=",">
            #{s}
        </foreach>
        <if test="examIds != null">
            and te.exam_id IN
            <foreach collection="examIds" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        <if test="examStatus != null">
            AND tes.exam_school_status IN
            <foreach collection="examStatus" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="search !=null ">
            AND te.exam_name LIKE concat('%',#{search},'%')
        </if>
        <if test="stage !=null ">
            AND te.stage = #{stage}
        </if>
        <if test="gradeYear !=null and gradeYear !=''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="examTypes != null">
            AND te.exam_type IN
            <foreach collection="examTypes" item="s" open="(" close=")" separator=",">
                #{s}
            </foreach>
        </if>
        <if test="notGraduateGradeId !=null and notGraduateGradeId.size()>0">
            AND tes.grade_id IN
            <foreach collection="notGraduateGradeId" item="gradeId" open="(" close=")" separator=",">
                #{gradeId}
            </foreach>
        </if>
        <if test="examIdList !=null and examIdList.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="examIdListByCourseCount !=null and examIdListByCourseCount.size()>0">
            AND te.exam_id IN
            <foreach collection="examIdListByCourseCount" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
        and exists (
            select 1 from t_exam_stat tes1 where tes1.exam_id = te.exam_id and tes1.stat_id = 0
        )
    </select>

    <select id="getSchoolIdByExaminerId" parameterType="map" resultType="map">
        SELECT school_id schoolId
        FROM t_exam_school
        WHERE creator_id = #{userId}
    </select>

    <select id="getSchoolExamByArea" parameterType="map" resultType="map">
        select
            te.exam_id examId,
            te.exam_name examName,
            te.exam_type examType,
            te.start_date startDate,
            tes.school_id schoolId,
            tes.school_name schoolName
        from t_exam te
        inner join t_exam_school tes on te.exam_id = tes.exam_id
        where te.exam_status > 1
          and te.exam_type in
        <foreach collection="examTypes" item="examType" separator="," open="(" close=")">
            #{examType}
        </foreach>
          and tes.school_id in
        <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
            #{schoolId}
        </foreach>
    </select>

    <select id="getUnionExamIdsByArea" parameterType="map" resultType="long">
        select te.exam_id
        from t_exam te
        inner join t_exam_area tea on te.exam_id = tea.exam_id
        where te.exam_id in (
            select exam_id from t_exam_area where area_id = #{areaId}
        )
        and te.exam_status > 1
        and te.exam_type = 10
        group by te.exam_id
        having min(area_id) = #{areaId}
    </select>

    <select id="getUnionExamByArea" parameterType="list" resultType="map">
        select
            te.exam_id examId,
            te.exam_name examName,
            te.exam_type examType,
            te.start_date startDate,
            tes.school_id schoolId,
            tes.school_name schoolName
        from t_exam te
        inner join t_exam_school tes on te.exam_id = tes.exam_id
        where te.exam_id in
        <foreach collection="list" item="examId" separator="," open="(" close=")">
            #{examId}
        </foreach>
    </select>

    <select id="getExamListBySchoolAndDate" parameterType="map" resultType="map">
        select l.* from (
            select
                te.exam_id examId,
                te.exam_name examName,
                te.exam_type examType,
                te.start_date startDate
            from t_exam te
            inner join t_exam_school tes on te.exam_id = tes.exam_id
            where te.exam_status in (2,3)
              and te.exam_type != 11
              and te.start_date >= #{startDate}
              and te.start_date &lt;= #{endDate}
              and tes.school_id = #{schoolId}
            union all
            select
                te.exam_id examId,
                te.exam_name examName,
                te.exam_type examType,
                te.start_date startDate
            from t_exam te
            inner join t_exam_school tes on te.exam_id = tes.exam_id
            where te.exam_status = 3
              and te.exam_type = 11
              and te.start_date >= #{startDate}
              and te.start_date &lt;= #{endDate}
              and tes.school_id = #{schoolId}
        ) l
        order by l.startDate desc, l.examId desc
    </select>

    <select id="getExamListBySchoolAndExamIds" parameterType="map" resultType="map">
        select l.* from (
            select
                te.exam_id examId,
                te.exam_name examName,
                te.exam_type examType,
                te.start_date startDate
            from t_exam te
            inner join t_exam_school tes on te.exam_id = tes.exam_id
            where te.exam_id in
            <foreach collection="examIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
              and te.exam_status in (2,3)
              and te.exam_type != 11
              and tes.school_id = #{schoolId}
            union all
            select
                te.exam_id examId,
                te.exam_name examName,
                te.exam_type examType,
                te.start_date startDate
            from t_exam te
            inner join t_exam_school tes on te.exam_id = tes.exam_id
            where te.exam_id in
            <foreach collection="examIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
              and te.exam_status = 3
              and te.exam_type = 11
              and tes.school_id = #{schoolId}
        ) l
        order by l.startDate desc, l.examId desc
    </select>

    <select id="getExamCountBySchoolGradeAndCourseAndStartDate" resultType="int"
      parameterType="com.dongni.tiku.wrong.book.bean.param.WrongBookEffectivenessExamQuery">
        select count(distinct te.exam_id)
        from t_exam te
        inner join t_exam_school tes on te.exam_id = tes.exam_id
        inner join t_exam_school_paper tesp on tes.exam_id = tesp.exam_id and tes.school_id = tesp.school_id
        inner join t_exam_paper tep on tesp.exam_id = tep.exam_id and tesp.paper_id = tep.paper_id
        where te.exam_status > 1
          and te.exam_type in
          <foreach collection="examTypes" item="examType" separator="," open="(" close=")">
            #{examType}
          </foreach>
          and te.start_date >= #{startDate}
          and te.start_date &lt;= #{endDate}
          and tes.school_id = #{schoolId}
          and tes.grade_id = #{gradeId}
          and tep.course_id in
        <foreach collection="courseIds" open="(" close=")" item="courseId" separator=",">
            #{courseId}
        </foreach>
    </select>

    <select id="getExamListBySchoolGradeAndCourseAndStartDate" resultType="com.dongni.tiku.wrong.book.bean.vo.WrongBookExamVO"
      parameterType="com.dongni.tiku.wrong.book.bean.param.WrongBookEffectivenessExamQuery">
        select
            te.exam_id examId,
            te.exam_name examName,
            te.start_date examDate
        from t_exam te
        inner join t_exam_school tes on te.exam_id = tes.exam_id
        inner join t_exam_school_paper tesp on tes.exam_id = tesp.exam_id and tes.school_id = tesp.school_id
        inner join t_exam_paper tep on tesp.exam_id = tep.exam_id and tesp.paper_id = tep.paper_id
        where te.exam_status > 1
          and te.exam_type in
        <foreach collection="examTypes" item="examType" separator="," open="(" close=")">
            #{examType}
        </foreach>
          and te.start_date >= #{startDate}
          and te.start_date &lt;= #{endDate}
          and tes.school_id = #{schoolId}
          and tes.grade_id = #{gradeId}
          and tep.course_id in
        <foreach collection="courseIds" open="(" close=")" item="courseId" separator=",">
            #{courseId}
        </foreach>
        group by te.exam_id
    </select>

</mapper>