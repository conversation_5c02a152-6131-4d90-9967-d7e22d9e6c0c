<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.plan.dao.NewExamResultDao">

    <select id="getExamResultCountByExamUploaderBO" parameterType="com.dongni.exam.plan.bean.ExamUploaderBO" resultType="int">
        select count(1) from t_exam_result ter
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
            <if test="schoolId != null and schoolId != 0">
                and school_id = #{schoolId}
            </if>
            <if test="classIdList != null and classIdList.size() > 0">
                and class_id in (<foreach collection="classIdList" separator="," item="item">#{item}</foreach>)
            </if>
            <if test="searchValue != null and searchValue != ''">
                and (
                    ter.class_name like concat('%',#{searchValue},'%')
                    or ter.student_name like concat('%',#{searchValue},'%')
                    or ter.student_name_pinyin like concat('%',#{searchValue},'%')
                    or ter.student_exam_num like concat('%',#{searchValue},'%')
                )
            </if>
            <if test="classId != null and classId != ''">
                and ter.class_id = #{classId}
            </if>
    </select>

    <select id="getExamSchoolCount" parameterType="com.dongni.exam.plan.bean.vo.SchoolProgressVO" resultType="int">
        select count(1) from (
            select
                distinct
                ter.school_id
                <if test="schoolId != null and schoolId > 0">
                    , ter.class_id
                </if>
            from t_exam_school tes
            join t_exam_paper tep on tep.exam_id = tes.exam_id
            join t_exam_result ter on ter.exam_id = tes.exam_id and ter.paper_id = tep.paper_id and ter.school_id = tes.school_id
            where tes.exam_id = #{examId}
            <if test="schoolId != null and schoolId > 0">and tes.school_id = #{schoolId}</if>
            <if test="courseId != null and courseId > 0">
              and tep.course_id = #{courseId}
            </if>
            <if test="uploadCourseIdList != null and uploadCourseIdList.size() > 0">
                and tep.course_id in (<foreach collection="uploadCourseIdList" item="item" separator=",">#{item}</foreach>)
            </if>
            group by ter.school_id
            <if test="schoolId != null and schoolId > 0">
                , ter.class_id
            </if>
        )  tmp;
    </select>

    <select id="getSchoolProgressStat2" parameterType="com.dongni.exam.plan.bean.vo.SchoolProgressVO"
            resultType="com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO">
        select
        tes.school_id                                       schoolId,
        tes.school_name                                     schoolName,
        <if test="schoolId != null and schoolId > 0">
            ter.class_id                                        classId,
            ter.class_name                                      className,
        </if>
        tep.course_id                                       courseId,
        tep.course_name                                     courseName,
        tep.paper_id                                        paperId,
        tep.paper_name                                      paperName,
        count(1)                                                                     attendStudentCount,
        count(if(ter.result_status = 0 and ter.uploaded_status in (1,2), 1, null))   normalStudentCount,
        count(if(ter.result_status = 1, 1, null))                                    absentStudentCount,
        count(if(ter.uploaded_status = 0, 1, null))                                  unuploadedStudentCount
        from t_exam_school tes
        inner join t_exam_paper tep on tep.exam_id = tes.exam_id
        join t_exam_result ter on tes.exam_id = ter.exam_id and tes.school_id = ter.school_id
        and tep.paper_id = ter.paper_id
        where tes.exam_id = #{examId}
        <if test="courseId != null and courseId > 0">
            and tep.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId > 0">
            and tes.school_id = #{schoolId}
        </if>
        <if test="schoolIds != null and schoolIds.size() > 0">
            and tes.school_id in (<foreach collection="schoolIds" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="classIds != null and classIds.size() > 0">
            and ter.class_id in (<foreach collection="classIds" item="item" separator=",">#{item}</foreach>)
        </if>
        <if test="uploadCourseIdList != null and uploadCourseIdList.size() > 0">
            and tep.course_id in (<foreach collection="uploadCourseIdList" item="item" separator=",">#{item}</foreach>)
        </if>
        group by ter.paper_id, ter.school_id
        <if test="schoolId != null and schoolId > 0">
            , ter.class_id
        </if>
        order by
        ter.school_id, ter.paper_id
        <if test="schoolId != null and schoolId > 0">
            , ter.class_id asc
        </if>
        <if test="currentIndex >= 0 and pageSize > 0">
            limit #{currentIndex}, #{pageSize}
        </if>;
    </select>

    <select id="getSchoolProgressStat" parameterType="com.dongni.exam.plan.bean.vo.SchoolProgressVO"
            resultType="com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO">
        select
            tes.school_id                                       schoolId,
            tes.school_name                                     schoolName,
            <if test="schoolId != null and schoolId > 0">
            ter.class_id                                        classId,
            ter.class_name                                      className,
            </if>
            tep.course_id                                       courseId,
            tep.course_name                                     courseName,
            tep.paper_id                                        paperId,
            tep.paper_name                                      paperName,
            count(1)                                                                    attendStudentCount,
            count(if(ter.result_status = 0 and ter.uploaded_status in (1,2), 1, null))   normalStudentCount,
            count(if(ter.result_status = 1, 1, null))                                   absentStudentCount,
            count(if(ter.uploaded_status = 0, 1, null))                                 unuploadedStudentCount
        from t_exam_school tes
        inner join t_exam_paper tep on tep.exam_id = tes.exam_id
        join t_exam_result ter on tes.exam_id = ter.exam_id and tes.school_id = ter.school_id
            and tep.paper_id = ter.paper_id
        where tes.exam_id = #{examId}
              <if test="courseId != null and courseId > 0">
                  and tep.course_id = #{courseId}
              </if>
              <if test="schoolId != null and schoolId > 0">
                  and tes.school_id = #{schoolId}
              </if>
              <if test="uploadCourseIdList != null and uploadCourseIdList.size() > 0">
                  and tep.course_id in (<foreach collection="uploadCourseIdList" item="item" separator=",">#{item}</foreach>)
              </if>
            group by ter.paper_id, ter.school_id
            <if test="schoolId != null and schoolId > 0">
                , ter.class_id
            </if>
            order by
            <if test="schoolIds != null and schoolIds.size() > 0">
                case tes.school_id
                 <foreach collection="schoolIds"  item="item" index="index"> WHEN #{item} then #{index}</foreach>
                else tes.school_id end,
            </if>
            ter.school_id, ter.paper_id
            <if test="schoolId != null and schoolId > 0">
                , ter.class_id asc
            </if>
            <if test="currentIndex >= 0 and pageSize > 0">
                limit #{currentIndex}, #{pageSize}
            </if>;
    </select>

    <select id="getSchoolPaperProgressStatCount" parameterType="com.dongni.exam.plan.bean.vo.SchoolProgressVO" resultType="int">
            select count(distinct tep.paper_id, tes.school_id)
            from t_exam_school tes
            inner join t_exam_paper tep on tep.exam_id = tes.exam_id
            join t_exam_result ter on ter.exam_id = tes.exam_id and ter.paper_id = tep.paper_id and ter.school_id = tes.school_id
            where tes.exam_id = #{examId}
            <if test="courseId != null and courseId > 0">and tep.course_id = #{courseId}</if>
            <if test="schoolId != null and schoolId > 0">and tes.school_id = #{schoolId}</if>
    </select>

    <select id="getPaperProgressStat"  resultType="com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO" parameterType="com.dongni.exam.plan.bean.vo.SchoolProgressVO">
        select
            #{schoolId}                                         id,
            <if test="schoolId > 0">
            tes.school_name                                     schoolName,
            </if>
            tep.course_id                                       courseId,
            tep.course_name                                     courseName,
            tep.paper_id                                        paperId,
            tep.paper_name                                      paperName
        from t_exam_result ter
        inner join t_exam_paper tep on tep.exam_id = ter.exam_id and tep.paper_id = ter.paper_id
            <if test="courseId != null and courseId > 0">
                and tep.course_id = #{courseId}
            </if>
        inner join t_exam_school tes on tes.exam_id = ter.exam_id and tes.school_id = ter.school_id
        where ter.exam_id = #{examId}
          <if test="schoolId != null and schoolId > 0">
              and ter.school_id = #{schoolId}
          </if>
        group by ter.paper_id
        order by ter.paper_id;
    </select>

    <select id="getPaperProgressStatInfo" resultType="com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO"
            parameterType="com.dongni.exam.plan.bean.vo.SchoolProgressVO">
        select
            count(1)                                                                     attendStudentCount,
            count(if(ter.result_status = 0 and ter.uploaded_status in (1,2), 1, null))   normalStudentCount,
            count(if(ter.result_status = 1, 1, null))                                    absentStudentCount,
            count(if(ter.uploaded_status = 0, 1, null))                                  unuploadedStudentCount
        from t_exam_result ter
        inner join t_exam_paper tep on tep.exam_id = ter.exam_id and tep.paper_id = ter.paper_id
        where ter.exam_id = #{examId}
        <if test="schoolId != null and schoolId > 0">
            and ter.school_id = #{schoolId}
        </if>
        <if test="courseId != null and courseId > 0">
            and tep.course_id = #{courseId}
        </if>
        group by ter.paper_id
        order by ter.paper_id;
    </select>

    <select id="getPaperSchoolStatDetail" resultType="com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO">
        select
            school_id           schoolId,
            paper_id            paperId,
            class_id            classId,
            count(1)                                                                     attendStudentCount,
            count(if(ter.result_status = 0 and ter.uploaded_status in (1,2), 1, null))   normalStudentCount,
            count(if(ter.result_status = 1, 1, null))                                    absentStudentCount,
            count(if(ter.uploaded_status = 0, 1, null))                                  unuploadedStudentCount
        from t_exam_result ter
        where exam_id = #{examId} and
              <if test="type == 1">
                  (paper_id, school_id ) in (<foreach collection="list" item="item" separator=",">(#{item.paperId}, #{item.schoolId})</foreach>)
              </if>
              <if test="type == 2">
                  (paper_id, school_id, class_id ) in (<foreach collection="list" item="item" separator=",">(#{item.paperId}, #{item.schoolId}, #{item.classId})</foreach>)
              </if>
        group by ter.paper_id, ter.school_id
        <if test="type == 2">
            ,ter.class_id
        </if>;
    </select>

    <select id="getScopeStudentStatVO" parameterType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO" resultType="com.dongni.exam.plan.bean.vo.StudentStatVO">
        select
            count(if(ter.result_status = 0 and ter.uploaded_status in (1,2), 1, null))      normalStudentCount,
            count(if(ter.result_status = 1, 1, null))                                       absentStudentCount,
            count(if(ter.uploaded_status = 0, 1, null))                                     unuploadedStudentCount,
            tep.paper_id                                                                    paperId,
            tep.paper_name                                                                  paperName
        from t_exam_result ter, t_exam_paper tep
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
            <if test="careSchoolIdList != null and careSchoolIdList.size() > 0">
                and ter.school_id in (<foreach collection="careSchoolIdList" item="item" separator=",">#{item}</foreach>)
            </if>
            <if test="schoolId != null and schoolId > 0">
                and ter.school_id = #{schoolId}
            </if>
            <if test="classIdList != null and classIdList.size() > 0">
                and ter.class_id in (<foreach collection="classIdList" item="item" separator=",">#{item}</foreach>)
            </if>
            and tep.exam_id = #{examId} and tep.paper_id = #{paperId}
    </select>

    <select id="getExamStudentResultCountByExamResultVO" parameterType="com.dongni.exam.plan.bean.vo.GetExamResultVO" resultType="java.lang.Integer">
        select count(1)
        from t_exam_result ter
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
            <if test="schoolId != null and schoolId > 0">
                and ter.school_id = #{schoolId}
            </if>
            <if test="classId != null and classId > 0">
                and ter.class_id = #{classId}
            </if>
            <if test="careSchoolIdList != null and careSchoolIdList.size() > 0">
                and ter.school_id in (<foreach collection="careSchoolIdList" item="schoolId" separator=",">#{schoolId}</foreach>)
            </if>
            <if test="classIdList != null and classIdList.size() > 0">
                and ter.class_id in (<foreach collection="classIdList" item="classId" separator=",">#{classId}</foreach>)
            </if>
            <if test="resultStatus != null and resultStatus > -1">
                and ter.result_status = #{resultStatus}
            </if>
            <if test="searchValue != null and searchValue != ''">
                and (ter.student_name like concat('%', #{searchValue}, '%')
                    or ter.student_exam_num like concat('%', #{searchValue}, '%')
                    )
            </if>
            <if test="studentType != null and studentType == 1">
                and ter.result_status = 0 and ter.uploaded_status != 0
            </if>
            <if test="studentType != null and studentType == 2">
                and ter.result_status = 1
            </if>
            <if test="studentType != null and studentType == 3">
                and ter.uploaded_status = 0
            </if>
            <if test="answerCardStatus != null and answerCardStatus == 0">
                and ter.uploaded_status = 0
            </if>
            <if test="answerCardStatus != null and answerCardStatus == 1">
                and ter.uploaded_status != 0
            </if>
            <if test="answerCardStatus != null and answerCardStatus == 2">
                and ter.uploaded_status != 0 and ter.result_status = 1
            </if>
    </select>

    <select id="getExamStudentResultByExamResultVO" parameterType="com.dongni.exam.plan.bean.vo.GetExamResultVO"  resultType="com.dongni.exam.plan.bean.vo.ExamStudentResultVO">
        select
            ter.exam_id                 examId,
            ter.paper_id                paperId,
            ter.school_id               schoolId,
            tes.school_name             schoolName,
            ter.student_exam_num        studentExamNum,
            ter.student_name            studentName,
            ter.student_id              studentId,
            ter.school_id               schoolId,
            tes.school_name             schoolName,
            ter.class_id                classId,
            ter.class_name              className,
            case
            when ter.uploaded_status = 1 and ter.result_status = 1  then '缺诊已上传'
            when ter.uploaded_status in (1, 2) and ter.result_status = 0  then '已上传'
            else '未上传'
            end as                      resultStatusName,
            ter.result_status           resultStatus,
            ter.uploaded_status         uploadedStatus
            <if test="sortField != null and sortType != null">
            , tacb.cardCount              cardCount
            </if>
        from t_exam_result ter
        inner join t_exam_school tes  on tes.exam_id = #{examId} and tes.school_id = ter.school_id
        <if test="sortField != null and sortType != null">
        left join (
            select tacb.student_id, count(1) cardCount
            from t_exam_uploader teu, t_answer_card_recycle tacr, t_answer_card_bak tacb
            where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
            and tacr.exam_uploader_id = teu.exam_uploader_id and tacr.delete_code in (7, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28)
            and tacb.recycle_id = tacr.recycle_id
            group by tacb.student_id
        ) tacb on tacb.student_id = ter.student_id
        </if>
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
            <if test="schoolId != null and schoolId > 0">
                and ter.school_id = #{schoolId}
            </if>
            <if test="classId != null and classId > 0">
                and ter.class_id = #{classId}
            </if>
            <if test="careSchoolIdList != null and careSchoolIdList.size() > 0">
                and ter.school_id in (<foreach collection="careSchoolIdList" item="schoolId" separator=",">#{schoolId}</foreach>)
            </if>
            <if test="classIdList != null and classIdList.size() > 0">
                and ter.class_id in (<foreach collection="classIdList" item="classId" separator=",">#{classId}</foreach>)
            </if>
            <if test="resultStatus != null and resultStatus > -1">
                and ter.result_status = #{resultStatus}
            </if>
            <if test="searchValue != null and searchValue != ''">
                and (ter.student_name like concat('%', #{searchValue}, '%')
                or ter.student_exam_num like concat('%', #{searchValue}, '%')
                )
            </if>
            <if test="studentType != null and studentType == 1">
                and ter.result_status = 0 and ter.uploaded_status != 0
            </if>
            <if test="studentType != null and studentType == 2">
                and ter.result_status = 1
            </if>
            <if test="studentType != null and studentType == 3">
                and ter.uploaded_status = 0
            </if>
            <if test="answerCardStatus != null and answerCardStatus == 0">
                and ter.uploaded_status = 0
            </if>
            <if test="answerCardStatus != null and answerCardStatus == 1">
                and ter.uploaded_status != 0
            </if>
            <if test="answerCardStatus != null and answerCardStatus == 2">
                and ter.uploaded_status != 0 and ter.result_status = 1
            </if>
            order by ter.school_id, ter.class_id, ter.student_id
            <if test="sortField != null and sortField != '' and sortType != null and sortType != ''">
                , ${sortField} ${sortType}
            </if>
            <if test="currentIndex >=0  and pageSize > 0">
                limit #{currentIndex}, #{pageSize};
            </if>
    </select>


    <update id="updateUploadedStatus" parameterType="map">
        update t_exam_result ter
            set ter.uploaded_status = #{uploadedStatus},
                ter.modifier_name = 'fat-service',
                ter.modify_date_time = NOW()
        where ter.exam_id = #{examId}
          and ter.paper_id = #{paperId}
          and ter.student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>);
    </update>

    <select id="getExamStudentInfo" parameterType="map" resultType="com.dongni.exam.plan.bean.vo.ExamStudentResultVO">
        select
             student_id         studentId,
             student_num        studentNum,
             student_exam_num   studentExamNum,
             student_name       studentName
        from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
            <if test="schoolId != null and schoolId > 0">
                and school_id = #{schoolId}
            </if>
            and student_exam_num = #{studentExamNum};
    </select>

    <select id="getStudentCount" parameterType="map" resultType="java.lang.Integer">
        select count(1) from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
         and school_id in (<foreach collection="schoolIds" separator="," item="item">#{item}</foreach>)
    </select>


    <update id="updateExamResultStatusByExamAndPaperAndStudentIds" parameterType="map">
        update t_exam_result
            set result_status = #{resultStatus},
                modifier_name = 'fat-service',
                modify_date_time = NOW()
        where exam_id = #{examId} and paper_id = #{paperId}
            and student_id in (<foreach collection="resultStudentIds" separator="," item="item">#{item}</foreach>);
    </update>

    <update id="updateUploadedStatusExamIdAndPaperIdAndSchoolAndStudents" parameterType="map">
        update t_exam_result
        set uploaded_status = #{uploadedStatus}
        where exam_id = #{examId} and paper_id = #{paperId}
          <if test="studentIds != null and studentIds.size() > 0">
              and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>);
          </if>
          <if test="schoolIds != null and schoolIds.size() > 0">
              and school_id in (<foreach collection="schoolIds" separator="," item="item">#{item}</foreach>);
          </if>
    </update>

    <update id="updateExamResultStatus">
        update t_exam_result
            set result_status = #{resultStatus},
                modify_date_time = NOW()
        where exam_id = #{examId} and paper_id = #{paperId}
          and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>);
    </update>

    <select id="downloadAbsentStudentList" parameterType="com.dongni.exam.plan.bean.bo.NewExamUploaderBO"
            resultType="com.dongni.exam.card.bean.vo.AbsentStudentVO">
            select
                    tes.school_id                                                            schoolId,
                    tes.school_name                                                          schoolName,
                    ter.exam_id                                                              examId,
                    ter.paper_id                                                             paperId,
                    ter.class_name                                                           className,
                    ter.student_id                                                           studentId,
                    ter.result_status                                                        resultStatus,
                    ter.uploaded_status                                                      uploadedStatus,
                    ter.student_num                                                          studentNum,
                    ter.student_exam_num                                                     studentExamNum,
                    ter.student_name                                                         studentName,
                    tep.course_id                                                            courseId,
                    tep.course_name                                                          courseName,
                    tep.paper_name                                                           paperName,
                    case
                    when ter.uploaded_status = 1 and ter.result_status = 1  then '缺诊已上传'
                    when ter.uploaded_status in (1, 2) and ter.result_status = 0  then '已上传'
                    else '未上传'
                    end as                                                                   resultStatusName
            from t_exam_result ter, t_exam_paper tep, t_exam_school tes
            where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
                <if test="schoolId != null and schoolId > 0">
                    and ter.school_id = #{schoolId}
                </if>
                <if test="careSchoolIdList != null and careSchoolIdList.size() > 0">
                    and ter.school_id in (<foreach collection="careSchoolIdList" item="schoolId" separator=",">#{schoolId}</foreach>)
                </if>
                <if test="classIdList != null and classIdList.size() > 0">
                    and ter.class_id in (<foreach collection="classIdList" item="classId" separator=",">#{classId}</foreach>)
                </if>
                and (ter.result_status = 1 or (ter.result_status = 0 and ter.uploaded_status = 0))
                and tep.exam_id = ter.exam_id and tep.paper_id = ter.paper_id
                and tes.exam_id = ter.exam_id and tes.school_id = ter.school_id
                order by ter.school_id, ter.paper_id, ter.class_id, ter.result_status desc, ter.uploaded_status desc;
    </select>

    <select id="getStudentAnswerCardStatus" parameterType="com.dongni.exam.plan.bean.vo.ExamCardStatusVO"
            resultType="com.dongni.exam.card.bean.vo.AbsentStudentVO">
        select
            tes.school_id                                                            schoolId,
            tes.school_name                                                          schoolName,
            ter.class_name                                                           className,
            ter.class_id                                                             classId,
            ter.exam_id                                                              examId,
            ter.paper_id                                                             paperId,
            ter.student_id                                                           studentId,
            ter.result_status                                                        resultStatus,
            ter.uploaded_status                                                      uploadedStatus,
            ter.student_num                                                          studentNum,
            ter.student_exam_num                                                     studentExamNum,
            ter.student_name                                                         studentName,
            tep.course_id                                                            courseId,
            tep.course_name                                                          courseName,
            tep.paper_name                                                           paperName,
            case
            when ter.uploaded_status = 1 and ter.result_status = 1  then '缺诊已上传'
            when ter.uploaded_status in (1, 2) and ter.result_status = 0  then '已上传'
            else '未上传'
            end as                                                                   resultStatusName
        from t_exam_result ter, t_exam_paper tep, t_exam_school tes
        where ter.exam_id = #{examId}
            and ter.paper_id in (<foreach collection="paperIds" separator="," item="item">#{item}</foreach>)
            and ter.school_id in (<foreach collection="schoolIds" separator="," item="item">#{item}</foreach>)
            <if test="careSchoolIdList != null and careSchoolIdList.size() > 0">
                and ter.school_id in (<foreach collection="careSchoolIdList" separator="," item="item">#{item}</foreach>)
            </if>
            and tep.exam_id = ter.exam_id and tep.paper_id = ter.paper_id
            and tes.exam_id = ter.exam_id and tes.school_id = ter.school_id
            <choose>
                <when test="uploadedStatus > -1 and resultStatus > 0">
                    and (ter.uploaded_status = #{uploadedStatus} or ter.result_status = #{resultStatus})
                </when>
                <otherwise>
                    <choose>
                        <when test="uploadedStatus > -1">
                            and ter.uploaded_status = #{uploadedStatus}
                        </when>
                        <otherwise>
                            and ter.result_status = #{resultStatus}
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
            order by ter.school_id, ter.course_id, ter.paper_id,
                     ter.class_id, ter.result_status desc, ter.uploaded_status desc;
    </select>

    <select id="getUploadedPaperSchoolIds" resultType="com.dongni.exam.plan.bean.dto.PaperSchoolDTO" parameterType="com.dongni.exam.plan.bean.vo.ExamCardStatusVO">
        select distinct
            ter.school_id   schoolId,
            ter.paper_id    paperId
        from t_exam_result ter
        <if test="courseId != null and courseId > 0">, t_exam_paper tep</if>
        where ter.exam_id = #{examId}
            <if test="courseId != null and courseId > 0">
                and tep.exam_id = #{examId} and tep.course_id = #{courseId} and tep.paper_id = ter.paper_id
            </if>
            and ter.uploaded_status = 1;
    </select>

    <select id="getExamStudentStatByExamIdAndCourseId" parameterType="map"
            resultType="com.dongni.exam.plan.bean.vo.UploaderStatsVO">
        select
            count(ter.exam_result_id)                                                       totalStudentCount,
            count(if(ter.uploaded_status in (1,2) and ter.result_status = 0, 1, null))      normalStudentCount,
            count(if(ter.result_status = 1, 1, null))                                       absentStudentCount,
            count(if(ter.uploaded_status = 0, 1, null))                                     unuploadedStudentCount,
            count(distinct ter.school_id)                                                   totalSchoolCount
        from t_exam_result ter, t_exam_paper tep
        where ter.exam_id = #{examId} and ter.paper_id = tep.paper_id
            and tep.exam_id = #{examId} and tep.course_id = #{courseId}
    </select>

    <select id="getExamStudentInfoById" resultType="java.util.Map">
        select
            ter.student_id          studentId,
            ter.student_num         studentNum,
            ter.student_exam_num    studentExamNum,
            ter.student_name        studentName,
            ter.class_name          className,
            ter.exam_result_id      examResultId,
            ter.result_status       resultStatus
        from t_exam_result ter
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId} and ter.student_id = #{studentId};
    </select>

    <select id="getUnuploadedStudentCount" resultType="java.lang.Integer">
        select count(1)
        from t_exam_result ter
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
        <if test="schoolId != null and schoolId > 0">
            and ter.school_id = #{schoolId}
        </if>
        <if test="careSchoolIdList != null and careSchoolIdList.size() > 0">
            and ter.school_id in (<foreach collection="careSchoolIdList" item="schoolId" separator=",">#{schoolId}</foreach>)
        </if>
        <if test="classIdList != null and classIdList.size() > 0">
            and ter.class_id in (<foreach collection="classIdList" item="classId" separator=",">#{classId}</foreach>)
        </if>
        and uploaded_status = 0;
    </select>

    <select id="getExamPaperStudents" parameterType="map" resultType="com.dongni.exam.plan.bean.dto.StudentCardDTO">
        select
            ter.exam_id                             examId,
            ter.paper_id                            paperId,
            ter.student_id                          studentId,
            tac.resultStatus                        tacResultStatus,
            if(tac.student_id is not null, 1, 0)    tacCard,
            if(tacb.student_id is not null, 1, 0)   tacbCard
        from t_exam_result ter
        left join
        (
            select
                distinct tac.student_id,
                tac.error_code >> 6 &amp; 1 = 1    resultStatus
            from t_exam_uploader teu, t_answer_card tac
            where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
                and tac.exam_uploader_id = teu.exam_uploader_id
                and (tac.error_code <![CDATA[&]]> b'00000000000000001111111111111111' = 0 or tac.error_code >> 6 &amp; 1 = 1)
        ) tac on tac.student_id = ter.student_id
        left join
        (
            select distinct
                tacb.student_id
            from t_exam_uploader teu, t_answer_card_recycle tacr, t_answer_card_bak tacb
            where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
                and tacr.exam_uploader_id = teu.exam_uploader_id and tacr.delete_code in (7, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29)
                and tacb.recycle_id = tacr.recycle_id
        ) tacb on tacb.student_id = ter.student_id
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId};
    </select>

    <select id="getUpgradeExamIds" parameterType="map" resultType="java.lang.Long">
        select exam_id
        from  t_exam te
        where te.create_date_time between
                  SUBDATE(NOW(), INTERVAL #{end} day) and SUBDATE(NOW(), INTERVAL #{start} day)
        order by te.exam_id desc;
    </select>

    <select id="getMaxExamId" resultType="java.lang.Long">
        select exam_id from t_exam
        order by exam_id desc limit 1;
    </select>

    <select id="getNewUpgradeExamIds" resultType="java.lang.Long">
        select exam_id from t_exam
        where exam_id &lt; #{examId} order by exam_id desc limit 200;
    </select>

    <select id="getUploadedStatusCount" resultType="java.lang.Integer" parameterType="map">
        select count(1) from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId} and uploaded_status = 1;
    </select>

    <select id="getQuestionList" resultType="com.dongni.exam.plan.bean.dto.QuestionPageNumberDTO">
        select page_number pageNumber,
               question_number questionNumber
        from t_question_structure
        where paper_id = #{paperId};
    </select>

    <select id="getStudentIdsByClassIds" resultType="java.lang.Long" parameterType="map">
        select student_id
        from t_exam_result
        where class_id in (<foreach collection="classIds" item="classId" separator="," >#{classId}</foreach>)
            and exam_id = #{examId} and paper_id = #{paperId};
    </select>

    <select id="getWrongCardRelativeStudents" resultType="com.dongni.exam.plan.bean.vo.ExamStudentResultVO">
        select student_id           studentId,
               student_num          studentNum,
               student_exam_num     studentExamNum,
               student_name         studentName,
               class_name           className
        from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
            and student_id in (<foreach collection="studentIds" item="studentId" separator="," >#{studentId}</foreach>)
    </select>

    <select id="getSchoolProgressStatSchoolIds" resultType="java.lang.Long">
        select
               <choose>
                   <when test="schoolId != null and schoolId > 0">
                       ter.class_id                                       classId
                   </when>
                   <otherwise>
                       ter.school_id                                       schoolId
                   </otherwise>
               </choose>
        from t_exam_school tes
        inner join t_exam_paper tep on tep.exam_id = tes.exam_id
        join t_exam_result ter on tes.exam_id = ter.exam_id and tes.school_id = ter.school_id
        and tep.paper_id = ter.paper_id
        where tes.exam_id = #{examId}
        <if test="courseId != null and courseId > 0">
            and tep.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId > 0">
            and tes.school_id = #{schoolId}
        </if>
        <if test="uploadCourseIdList != null and uploadCourseIdList.size() > 0">
            and tep.course_id in (<foreach collection="uploadCourseIdList" item="item" separator=",">#{item}</foreach>)
        </if>
        group by ter.school_id
        <if test="schoolId != null and schoolId > 0">
            , ter.class_id
        </if>
        order by
        <if test="schoolIds != null and schoolIds.size() > 0">
            case tes.school_id
            <foreach collection="schoolIds"  item="item" index="index"> WHEN #{item} then #{index}</foreach>
            else tes.school_id end,
        </if>
        ter.school_id, ter.paper_id
        <if test="schoolId != null and schoolId > 0">
            , ter.class_id asc
        </if>
        <if test="currentIndex >= 0 and pageSize > 0">
            limit #{currentIndex}, #{pageSize}
        </if>;
    </select>

    <select id="getMissCardStudentIds" resultType="java.lang.Long" parameterType="map">
        select ter.student_id
        from t_exam_result ter
        left join (
            select tac.student_id
            from t_exam_uploader teu, t_answer_card tac
            where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
            <if test="schoolId != null and schoolId > 0">
              and teu.school_id = #{schoolId}
            </if>
            and tac.exam_uploader_id = teu.exam_uploader_id and tac.page_number = 1
        ) tac on tac.student_id = ter.student_id
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
            <if test="schoolId != null and schoolId > 0">
                and ter.school_id = #{schoolId}
            </if>
            and ter.result_status = 0 and ter.uploaded_status = 1
            and tac.student_id is null;
    </select>

    <select id="getNewSchoolProgressStat" parameterType="map" resultType="com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO">
        select
              paper_id                                                              paperId,
              school_id                                                             schoolId,
              count(1)                                                              attendStudentCount,
              count(if(result_status = 0 and uploaded_status in (1, 2), 1, null))   normalStudentCount,
              count(if(result_status = 1, 1, null))                                 absentStudentCount,
              count(if(result_status = 0 and uploaded_status = 0, 1, null))         unuploadedStudentCount
        from t_exam_result
        where exam_id = #{examId} and paper_id in (<foreach collection="paperIds" item="paperId" separator=",">#{paperId}</foreach>)
            group by course_id, paper_id, school_id
            order by course_id, paper_id, school_id;
    </select>

    <select id="getNewClassProgressStat" parameterType="map" resultType="com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO">
        select
            school_id                                                             schoolId,
            class_id                                                              classId,
            class_name                                                            className,
            paper_id                                                              paperId,
            count(1)                                                              attendStudentCount,
            count(if(result_status = 0 and uploaded_status in (1, 2), 1, null))   normalStudentCount,
            count(if(result_status = 1, 1, null))                                 absentStudentCount,
            count(if(result_status = 0 and uploaded_status = 0, 1, null))         unuploadedStudentCount
        from t_exam_result
        where exam_id = #{examId} and school_id = #{schoolId}
            and paper_id in (<foreach collection="paperIds" item="paperId" separator=",">#{paperId}</foreach>)
            group by class_id, course_id, paper_id
            order by class_id, course_id, paper_id;
    </select>

    <select id="getExamResultStudentsByExamIdAndPaperIdAndStuIds"
            resultType="com.dongni.exam.newcard.bean.ExamResultStudentVO">
        select
                student_id          studentId,
                student_name        studentName,
                class_id            classId,
                class_name          className,
                student_num         studentNum,
                student_exam_num    studentExamNum
        from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
            and student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>);
    </select>

    <select id="getExamResultCount" resultType="java.lang.Integer">
        select count(1)
        from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
        <if test="searchValue != null and searchValue != ''">
            and (student_name like concat('%', #{searchValue}, '%')
            or student_exam_num like concat('%', #{searchValue}, '%')
            or student_name_pinyin like concat('%', #{searchValue}, '%'))
        </if>
    </select>

    <select id="getExamResultList" resultType="com.dongni.exam.recognition.bean.vo.RecognitionStudentVO">
        select
            student_id          studentId,
            student_name        studentName,
            student_exam_num    studentExamNum,
            student_num         studentNum,
            class_name          className,
            class_id            classId
        from t_exam_result
        where exam_id = #{examId} and paper_id = #{paperId}
        <if test="searchValue != null and searchValue != ''">
            and (student_name like concat('%', #{searchValue}, '%')
            or student_exam_num like concat('%', #{searchValue}, '%')
            or student_name_pinyin like concat('%', #{searchValue}, '%'))
        </if>
        order by student_id
        <if test="pageSize != null and pageSize > 0">
            limit #{currentIndex}, #{pageSize};
        </if>
    </select>
</mapper>
