<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamStudentTagMapper">

    <!-- 获取考试学生列表 -->
    <select id="getStudent" parameterType="map" resultType="map">
        SELECT
        ts.exam_student_id examStudentId,
        ts.student_id studentId,
        ts.student_num studentNum,
        ts.student_name studentName,
        ts.student_exam_num studentExamNum,
        ts.course_selection_group_id courseSelectionGroupId,
        ts.foreign_course_id foreignCourseId,
        tec.class_id classId,
        tec.class_name className,
        tes.school_name schoolName,
        tes.school_id schoolId,
        tes.grade_id gradeId,
        tes.grade_type gradeType,
        tes.grade_name gradeName,
        <if test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
            t.exam_id is null isStuExamNumNotRepeat,
        </if>
        ts.student_exam_num REGEXP '^[0-9]+$' isStuExamNumNormal,
        GROUP_CONCAT(tst.exam_tag_id ORDER BY tst.exam_tag_id ASC SEPARATOR ',') examTagId,
        GROUP_CONCAT(tg.tag_name ORDER BY tst.exam_tag_id ASC SEPARATOR ',') tagName
        FROM t_exam_school tes
        INNER JOIN t_exam_class tec ON tes.exam_id = tec.exam_id AND tes.school_id = tec.school_id
        INNER JOIN t_exam_student ts ON tec.exam_id = ts.exam_id AND tec.class_id = ts.class_id
        <if test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
            left join (
            select exam_id, student_exam_num
            from t_exam_student
            where exam_id = #{examId}
            group by student_exam_num
            having count(student_exam_num) > 1
            ) t on ts.exam_id = t.exam_id and ts.student_exam_num = t.student_exam_num
        </if>
        LEFT JOIN t_exam_student_tag tst
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            force index (t_exam_student_tag_exam_tag_id_index)
        </if>
            ON ts.exam_id = tst.exam_id AND ts.student_id = tst.student_id
        LEFT JOIN t_exam_tag tg ON tst.exam_tag_id = tg.exam_tag_id
        WHERE tes.exam_id = #{examId}
        <if test="schoolId != null and schoolId != 0">
            AND tes.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            AND tec.class_id = #{classId}
        </if>
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            AND tst.exam_tag_id IN
            <foreach collection="examTagIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_exam_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            )
        </if>
        GROUP BY ts.student_id
        ORDER BY
        <choose>
            <when test="sortField != null and sortType != null">
                ${sortField} ${sortType}
            </when>
            <when test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
                <!-- 混扫时默认排序：考号重复 > 考号不正常的（包含非数字或者为空的）> 其他的 -->
                isStuExamNumNotRepeat, isStuExamNumNormal, if(isStuExamNumNotRepeat, 1, ts.student_exam_num),
                tes.school_id, tec.class_id,ts.student_id
            </when>
            <otherwise>
                <!-- 考号不正常的（包含非数字或者为空的）放在前面，正常的放后面 -->
                isStuExamNumNormal, tes.school_id, tec.class_id, ts.student_id
            </otherwise>
        </choose>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 获取考试学生列表 导出参考学生专用 -->
    <select id="getStudentForExport" parameterType="map" resultType="map">
        SELECT
        ts.student_num studentNum,
        ts.student_name studentName,
        ts.student_exam_num studentExamNum,
        ts.course_selection_group_id courseSelectionGroupId,
        ts.foreign_course_id foreignCourseId,
        ts.class_name className,
        ts.school_id schoolId,
        <if test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
            t.exam_id is null isStuExamNumNotRepeat,
        </if>
        ts.student_exam_num REGEXP '^[0-9]+$' isStuExamNumNormal,
        GROUP_CONCAT(tg.tag_name ORDER BY tst.exam_tag_id ASC SEPARATOR ',') tagName
        FROM t_exam_student ts
        <if test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
            left join (
            select exam_id, student_exam_num
            from t_exam_student
            where exam_id = #{examId}
            group by student_exam_num
            having count(student_exam_num) > 1
            ) t on ts.exam_id = t.exam_id and ts.student_exam_num = t.student_exam_num
        </if>
        LEFT JOIN t_exam_student_tag tst
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            force index (t_exam_student_tag_exam_tag_id_index)
        </if>
            ON ts.exam_id = tst.exam_id AND ts.student_id = tst.student_id
        LEFT JOIN t_exam_tag tg ON tst.exam_tag_id = tg.exam_tag_id
        WHERE ts.exam_id = #{examId}
        <if test="schoolId != null and schoolId != 0">
            AND ts.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            AND ts.class_id = #{classId}
        </if>
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            AND tst.exam_tag_id IN
            <foreach collection="examTagIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_exam_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            )
        </if>
        GROUP BY ts.student_id
        ORDER BY
        <choose>
            <when test="sortField != null and sortType != null">
                ${sortField} ${sortType}
            </when>
            <when test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
                <!-- 混扫时默认排序：考号重复 > 考号不正常的（包含非数字或者为空的）> 其他的 -->
                isStuExamNumNotRepeat, isStuExamNumNormal, if(isStuExamNumNotRepeat, 1, ts.student_exam_num),
                ts.school_id, ts.class_id,ts.student_id
            </when>
            <otherwise>
                <!-- 考号不正常的（包含非数字或者为空的）放在前面，正常的放后面 -->
                isStuExamNumNormal, ts.school_id, ts.class_id, ts.student_id
            </otherwise>
        </choose>
    </select>

    <!-- 获取学生列表总数 -->
    <select id="getStudentCount" parameterType="map" resultType="int">
        SELECT
        count(DISTINCT ts.exam_student_id)
        FROM t_exam_student ts
        INNER JOIN t_exam_class tec ON tec.exam_id = ts.exam_id AND tec.class_id = ts.class_id
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            LEFT JOIN t_exam_student_tag tst force index (t_exam_student_tag_exam_tag_id_index) ON ts.exam_id = tst.exam_id AND ts.student_id = tst.student_id
        </if>
        WHERE tec.exam_id = #{examId}
        <if test="schoolId != null and schoolId != 0">
            AND tec.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            AND tec.class_id = #{classId}
        </if>
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            AND tst.exam_tag_id IN
            <foreach collection="examTagIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_exam_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            )
        </if>
    </select>


    <!-- 获取考试学生列表 -->
    <select id="getStudentByPaper" parameterType="map" resultType="map">
        SELECT /*+ INL_JOIN(tes, testu, tsr, tst, tg) */
        testu.exam_student_id examStudentId,
        testu.student_id      studentId,
        tsr.student_num       studentNum,
        tsr.student_name      studentName,
        tsr.student_exam_num  studentExamNum,
        tsr.paper_id          paperId,
        tsr.class_id          classId,
        tsr.class_name        className,
        tes.school_name       schoolName,
        tes.school_id         schoolId,
        tes.grade_id          gradeId,
        tes.grade_type        gradeType,
        tes.grade_name        gradeName,
        tsr.student_exam_num REGEXP '^[0-9]+$' isStuExamNumNormal,
        GROUP_CONCAT(tst.exam_tag_id ORDER BY tst.exam_tag_id ASC SEPARATOR ',') examTagId,
        GROUP_CONCAT(tg.tag_name ORDER BY tst.exam_tag_id ASC SEPARATOR ',')     tagName
        FROM t_exam_school tes
        INNER JOIN t_exam_student testu ON tes.exam_id = testu.exam_id AND tes.school_id = testu.school_id
        INNER JOIN t_exam_result tsr ON testu.exam_id = tsr.exam_id AND testu.student_id = tsr.student_id
        LEFT JOIN t_exam_student_tag tst
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            force index (t_exam_student_tag_exam_tag_id_index)
        </if>
            ON testu.exam_id = tst.exam_id AND testu.student_id = tst.student_id
        LEFT JOIN t_exam_tag tg ON tst.exam_tag_id = tg.exam_tag_id
        WHERE tes.exam_id = #{examId}
        <if test="schoolId != null and schoolId != 0">
            AND tes.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            AND testu.class_id = #{classId}
        </if>
        AND tsr.paper_id = #{paperId}
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            AND tst.exam_tag_id IN
            <foreach collection="examTagIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( tsr.student_exam_num LIKE #{search}
            OR tsr.student_name LIKE #{search}
            )
        </if>
        GROUP BY tsr.student_id
        ORDER BY
        <choose>
            <when test="sortField != null and sortType != null">
                ${sortField} ${sortType}
            </when>
            <otherwise>
                <!-- 考号不正常的（包含非数字或者为空的）放在前面，正常的放后面 -->
                isStuExamNumNormal, tes.school_id, testu.class_id, tsr.student_id
            </otherwise>
        </choose>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 获取考试学生列表 -->
    <select id="getMultiSchoolScanStudentByPaper" parameterType="map" resultType="map">
        SELECT /*+ INL_JOIN(t_exam_result, tes, testu, tsr, tst, tg) */
        testu.exam_student_id examStudentId,
        testu.student_id      studentId,
        tsr.student_num       studentNum,
        tsr.student_name      studentName,
        tsr.student_exam_num  studentExamNum,
        tsr.paper_id          paperId,
        tsr.class_id          classId,
        tsr.class_name        className,
        tes.school_name       schoolName,
        tes.school_id         schoolId,
        tes.grade_id          gradeId,
        tes.grade_type        gradeType,
        tes.grade_name        gradeName,
        t.exam_id is null isStuExamNumNotRepeat,
        tsr.student_exam_num REGEXP '^[0-9]+$' isStuExamNumNormal,
        GROUP_CONCAT(tst.exam_tag_id ORDER BY tst.exam_tag_id ASC SEPARATOR ',') examTagId,
        GROUP_CONCAT(tg.tag_name ORDER BY tst.exam_tag_id ASC SEPARATOR ',')     tagName
        FROM t_exam_school tes
        INNER JOIN t_exam_student testu ON tes.exam_id = testu.exam_id AND tes.school_id = testu.school_id
        INNER JOIN t_exam_result tsr ON testu.exam_id = tsr.exam_id AND testu.student_id = tsr.student_id
        left join (
            select exam_id, student_exam_num
            from t_exam_result
            where exam_id = #{examId}
            and paper_id = #{paperId}
            group by student_exam_num
            having count(student_exam_num) > 1
        ) t on tsr.exam_id = t.exam_id and tsr.student_exam_num = t.student_exam_num
        LEFT JOIN t_exam_student_tag tst
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            force index (t_exam_student_tag_exam_tag_id_index)
        </if>
        ON testu.exam_id = tst.exam_id AND testu.student_id = tst.student_id
        LEFT JOIN t_exam_tag tg ON tst.exam_tag_id = tg.exam_tag_id
        WHERE tes.exam_id = #{examId}
        <if test="schoolId != null and schoolId != 0">
            AND tes.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            AND testu.class_id = #{classId}
        </if>
        AND tsr.paper_id = #{paperId}
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            AND tst.exam_tag_id IN
            <foreach collection="examTagIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( tsr.student_exam_num LIKE #{search}
            OR tsr.student_name LIKE #{search}
            )
        </if>
        GROUP BY tsr.student_id
        ORDER BY
        <choose>
            <when test="sortField != null and sortType != null">
                ${sortField} ${sortType}
            </when>
            <otherwise>
                <!-- 混扫时默认排序：考号重复 > 考号不正常的（包含非数字或者为空的）> 其他的 -->
                isStuExamNumNotRepeat, isStuExamNumNormal, if(isStuExamNumNotRepeat, 1, tsr.student_exam_num),
                tes.school_id, testu.class_id, tsr.student_id
            </otherwise>
        </choose>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 获取考试学生列表 导出参考学生专用 -->
    <select id="getStudentByPaperForExport" parameterType="map" resultType="map">
        SELECT
        tsr.school_id         schoolId,
        tsr.student_num       studentNum,
        tsr.student_name      studentName,
        tsr.student_exam_num  studentExamNum,
        tsr.class_name        className,
        <if test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
            t.exam_id is null isStuExamNumNotRepeat,
        </if>
        tsr.student_exam_num REGEXP '^[0-9]+$' isStuExamNumNormal,
        GROUP_CONCAT(tg.tag_name ORDER BY tst.exam_tag_id ASC SEPARATOR ',')     tagName
        FROM t_exam_result tsr
        <if test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
            left join (
            select exam_id, student_exam_num
            from t_exam_result
            where exam_id = #{examId}
            and paper_id = #{paperId}
            group by student_exam_num
            having count(student_exam_num) > 1
            ) t on tsr.exam_id = t.exam_id and tsr.student_exam_num = t.student_exam_num
        </if>
        LEFT JOIN t_exam_student_tag tst
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            force index (t_exam_student_tag_exam_tag_id_index)
        </if>
            ON tsr.exam_id = tst.exam_id AND tsr.student_id = tst.student_id
        LEFT JOIN t_exam_tag tg ON tst.exam_tag_id = tg.exam_tag_id
        WHERE tsr.exam_id = #{examId} AND tsr.paper_id = #{paperId}
        <if test="schoolId != null and schoolId != 0">
            AND tsr.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            AND tsr.class_id = #{classId}
        </if>
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            AND tst.exam_tag_id IN
            <foreach collection="examTagIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( tsr.student_exam_num LIKE #{search}
            OR tsr.student_name LIKE #{search}
            )
        </if>
        GROUP BY tsr.student_id
        ORDER BY
        <choose>
            <when test="sortField != null and sortType != null">
                ${sortField} ${sortType}
            </when>
            <when test="useMultiSchoolScanType != null and useMultiSchoolScanType == true">
                <!-- 混扫时默认排序：考号重复 > 考号不正常的（包含非数字或者为空的）> 其他的 -->
                isStuExamNumNotRepeat, isStuExamNumNormal, if(isStuExamNumNotRepeat, 1, tsr.student_exam_num),
                tsr.school_id, tsr.class_id, tsr.student_id
            </when>
            <otherwise>
                <!-- 考号不正常的（包含非数字或者为空的）放在前面，正常的放后面 -->
                isStuExamNumNormal, tsr.school_id, tsr.class_id, tsr.student_id
            </otherwise>
        </choose>
    </select>


    <!-- 获取考试学生列表总数 -->
    <select id="getStudentCountByPaper" parameterType="map" resultType="int">
        SELECT
        COUNT(DISTINCT tsr.student_id)
        FROM t_exam_result tsr
        INNER JOIN t_exam_student testu ON testu.exam_id = tsr.exam_id AND testu.student_id = tsr.student_id
        INNER JOIN t_exam_school tes ON tsr.exam_id = tes.exam_id AND tsr.school_id = tes.school_id
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            LEFT JOIN t_exam_student_tag tst force index (t_exam_student_tag_exam_tag_id_index)
                ON tsr.exam_id = tst.exam_id AND tsr.student_id = tst.student_id
        </if>
        WHERE tsr.exam_id = #{examId}
        <if test="schoolId != null and schoolId != 0">
            AND tsr.school_id = #{schoolId}
        </if>
        <if test="classId != null and classId != ''">
            AND tsr.class_id = #{classId}
        </if>
        AND tsr.paper_id = #{paperId}
        <if test="examTagIdList != null and examTagIdList.size() > 0">
            AND tst.exam_tag_id IN
            <foreach collection="examTagIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( tsr.student_exam_num LIKE #{search}
            OR tsr.student_name LIKE #{search}
            )
        </if>
    </select>


    <!-- 查询学生关联的标签 -->
    <select id="getStudentExamTag" parameterType="map" resultType="map">
        SELECT
        test.exam_tag_id examTagId,
        tet.school_id examTagSchoolId,
        tet.tag_name tagName,
        tet.tag_desc tagDesc
        FROM t_exam_student_tag test
        LEFT JOIN t_exam_tag tet
            ON test.exam_tag_id = tet.exam_tag_id
        WHERE test.student_id = #{studentId}
          AND test.exam_id = #{examId}
    </select>


    <!-- 自由组合学生与标签 -->
    <select id="batchGetStudentExamTag" parameterType="map" resultType="map">
        SELECT

        tes.exam_id examId,
        tes.student_id studentId,
        tg.exam_tag_id examTagId,
        tg.tag_name tagName,
        "${userId}"      userId,
        "${userName}"    userName,
        "${currentTime}" currentTime
        FROM t_exam_student tes
        INNER JOIN t_exam_tag tg ON tes.exam_id = tg.exam_id
        WHERE tes.exam_id = #{examId}
        AND tes.exam_student_id IN
        <foreach collection="examStudentIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tg.exam_tag_id IN
        <foreach collection="examTagIdList" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
    </select>


    <!-- 给学生关联标签 -->
    <insert id="batchInsertStudentExamTag" parameterType="list">
        INSERT INTO t_exam_student_tag(
        exam_tag_id,
        exam_id,
        student_id,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.examTagId},
            #{item.examId},
            #{item.studentId},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 删除学生标签的关联 适用于考试工具箱-删除学生 -->
    <delete id="deleteStudentExamTagByStudentId" parameterType="map">
        DELETE FROM t_exam_student_tag
        WHERE exam_id = #{examId} AND student_id = #{studentId}
    </delete>

    <!-- 删除学生标签的关联 -->
    <delete id="deleteStudentExamTag" parameterType="map">
        DELETE FROM t_exam_student_tag
        WHERE exam_id = #{examId}
                AND student_id = #{studentId}
        AND exam_tag_id = #{examTagId}
    </delete>

    <!-- 删除学生标签的关联 删除考试学生不存在的学生考试标签 -->
    <delete id="deleteStudentExamTagNotExistExamStudent" parameterType="map">
        DELETE test
        FROM t_exam_student_tag test
        LEFT JOIN t_exam_student tes
            ON tes.exam_id = test.exam_id
            AND tes.student_id = test.student_id
        WHERE test.exam_id = #{examId}
            AND tes.exam_student_id IS NULL
    </delete>

    <delete id="deleteExamStudentTagByExamTag" parameterType="map">
        delete test
        from t_exam_tag tet, t_exam_student_tag test
        where tet.exam_tag_id = test.exam_tag_id
          and tet.exam_id = #{examId}
          <if test="schoolId != null">
              and tet.school_id = #{schoolId}
          </if>
          and tet.tag_id is not null
    </delete>

    <delete id="deleteExamTag" parameterType="map">
        delete from t_exam_tag
        where exam_id = #{examId}
        <if test="schoolId != null">
            and school_id = #{schoolId}
        </if>
          and tag_id is not null
    </delete>

    <insert id="insertExamStudentTag" parameterType="map">
        INSERT INTO t_exam_student_tag(
        exam_tag_id,
        exam_id,
        student_id,
        creator_id, creator_name, create_date_time,
        modifier_id, modifier_name, modify_date_time
        )VALUES
        <foreach collection="examStudentTags" item="item" separator=",">
            (
            #{item.examTagId},
            #{item.examId},
            #{item.studentId},
            #{userId}, #{userName}, now(),
            #{userId}, #{userName}, now()
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <delete id="deleteStudentExamTagByStudentIds" parameterType="map">
        DELETE FROM t_exam_student_tag
        WHERE exam_id = #{examId} AND student_id IN
            <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
                #{studentId}
            </foreach>
    </delete>
</mapper>
