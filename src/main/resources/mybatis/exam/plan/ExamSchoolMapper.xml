<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamSchoolMapper">

    <!-- 学校大考数量 -->
    <select id="getExamList" parameterType="map" resultType="map">
         SELECT
        tes.school_id schoolId,
        COUNT(tecs.exam_course_id) total
        FROM  t_exam te
        JOIN t_exam_course tecs ON tecs.exam_id = te.exam_id
        JOIN t_exam_school tes ON tes.exam_id = te.exam_id
        JOIN (
            SELECT tecc.exam_id
            FROM t_exam_course tecc
            GROUP BY tecc.exam_id HAVING COUNT(tecc.course_id) > 1
        ) temp ON temp.exam_id = te.exam_id
        WHERE te.start_date >= #{startDate}
        AND te.start_date &lt;= #{endDate}
        AND te.exam_type IN (3,4,7)
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
        GROUP BY tes.school_id
    </select>


    <!-- 学校月考数量 -->
    <select id="getMonthList" parameterType="map" resultType="map">
        SELECT
        tes.school_id schoolId,
        COUNT(tecs.exam_course_id) total
        FROM  t_exam te
        JOIN t_exam_course tecs ON tecs.exam_id = te.exam_id
        JOIN t_exam_school tes ON tes.exam_id = te.exam_id
        JOIN (
            SELECT tecc.exam_id
            FROM t_exam_course tecc
            GROUP BY tecc.exam_id HAVING COUNT(tecc.course_id) > 1
        ) temp ON temp.exam_id = te.exam_id
        WHERE te.start_date >= #{startDate}
        AND te.start_date &lt;= #{endDate}
        AND te.exam_type NOT IN (3,4,7)
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
        GROUP BY tes.school_id
    </select>


    <!-- 学校周测数量 -->
    <select id="getWeekList" parameterType="map" resultType="map">
        SELECT
        tes.school_id schoolId,
        COUNT(tes.exam_id) total
        FROM t_exam te
        JOIN t_exam_school tes ON tes.exam_id = te.exam_id
        JOIN (
        SELECT tecc.exam_id
        FROM t_exam_course tecc
        GROUP BY tecc.exam_id HAVING COUNT(tecc.course_id) = 1
        ) temp ON temp.exam_id = te.exam_id
        WHERE te.start_date >= #{startDate}
        AND te.start_date &lt;= #{endDate}
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
        GROUP BY tes.school_id
    </select>

    <!--获取指定考试开始时间在指定范围内的学校id-->
    <select id="getSchoolIdListByStartDate" parameterType="map" resultType="long">
        SELECT
        DISTINCT tes.school_id schoolId
        FROM t_exam te
        INNER JOIN t_exam_school tes ON tes.exam_id = te.exam_id
        WHERE te.start_date >= #{startDate}
        AND te.start_date &lt;= #{endDate}
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
        ORDER BY tes.school_id
    </select>


    <!-- 获取月均考试次数(完成) -->
    <select id="getExamAvg" parameterType="map" resultType="double">
        SELECT COUNT(*)/COUNT(DISTINCT tes.school_id) "月均考试次数"
        FROM  t_exam te
        left JOIN t_exam_school tes
        ON te.exam_id = tes.exam_id
        WHERE te.end_date >= #{startDate}
        AND te.end_date &lt;= #{endDate}
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
    </select>

    <!-- 获取月活学校总数 -->
    <select id="getSchoolCount" parameterType="map" resultType="int">
        SELECT COUNT(DISTINCT tes.school_id) "月活学校"
        FROM t_exam_school tes
        JOIN t_exam te
        ON te.exam_id = tes.exam_id
        WHERE te.end_date >=  #{startDate}
        AND te.end_date &lt;= #{endDate}
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
    </select>


    <!-- 获取周活学校总数 -->
    <select id="getSchoolWeekCount" parameterType="map" resultType="int">
        SELECT COUNT(*) "周活学校"
        FROM (
        SELECT DISTINCT tes.school_id
        FROM t_exam_school tes
        JOIN t_exam te
        ON te.exam_id = tes.exam_id
        WHERE te.end_date >= #{startDate}
        AND te.end_date &lt;= #{endDate}
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
        GROUP BY tes.school_id HAVING COUNT(*) >= 4
        ) t
    </select>

    <!--获取月活学校的schoolId列表 -->
    <select id="selectSchoolIdByMonth" parameterType="map" resultType="long">
        SELECT DISTINCT tes.school_id schoolId
        FROM t_exam_school tes
        JOIN t_exam te
        ON te.exam_id = tes.exam_id
        WHERE te.end_date >= #{startDate}
        AND te.end_date &lt;= #{endDate}
        AND tes.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
    </select>

    <!-- 新增考试学校 -->
    <insert id="insertExamSchool" parameterType="map">
        INSERT INTO t_exam_school(
        `exam_id`,
        `area_id`,
        `school_id`,
        `school_name`,
        `grade_id`,
        `grade_type`,
        `grade_name`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        (
            #{examId},
            #{areaId},
            #{schoolId},
            #{schoolName},
            #{gradeId},
            #{gradeType},
            #{gradeName},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
        )
    </insert>

    <delete id="deleteExamSchool" parameterType="map">
      DELETE FROM t_exam_school WHERE exam_school_id = #{examSchoolId}
    </delete>

    <!--获取学校的考试列表-->
    <select id="selectSchoolExamList" parameterType="map" resultType="map">
        SELECT
        exam_school_id examSchoolId,
        exam_id examId,
        school_id schoolId,
        exam_school_status examSchoolStatus
        FROM `t_exam_school`
        WHERE school_id=#{schoolId}
        <if test="examIdList!=null and examIdList.size()>0">
            AND exam_id IN
            <foreach collection="examIdList" item="examId" open="(" close=")" separator=",">
                #{examId}
            </foreach>
        </if>
    </select>

    <select id="getExamSchool" parameterType="map" resultType="map">
       SELECT
       `exam_school_id` examSchoolId,
       `exam_id` examId,
       `area_id` areaId,
       `school_id` schoolId,
       `school_name` schoolName,
       `grade_id` gradeId,
       `grade_type` gradeType,
       `grade_name` gradeName
       FROM t_exam_school
       WHERE exam_id = #{examId} ORDER BY school_id
    </select>

    <select id="getExamSchoolMark" parameterType="map" resultType="com.dongni.exam.common.mark.vo.ExamSchoolVO">
        SELECT
            `exam_school_id` examSchoolId,
            `exam_id` examId,
            `area_id` areaId,
            `school_id` schoolId,
            `school_name` schoolName,
            `grade_id` gradeId,
            `grade_type` gradeType,
            `grade_name` gradeName
        FROM t_exam_school
        WHERE exam_id = #{examId} ORDER BY school_id
    </select>

    <select id="getExamSchoolBySchoolId" parameterType="map" resultType="map">
        SELECT
            tes.exam_id        examId,
            tes.area_id        areaId,
            te.exam_name       examName,
            te.exam_type       examType,
            te.exam_status     examStatus,
            tes.exam_school_id examSchoolId,
            tes.exam_school_status examSchoolStatus,
            tes.school_id      schoolId,
            tes.school_name    schoolName,
            tes.grade_id       gradeId,
            tes.grade_type     gradeType,
            tes.grade_name     gradeName
        FROM t_exam_school tes
        JOIN t_exam te
            ON te.exam_id = tes.exam_id
        WHERE tes.exam_id = #{examId}
            AND tes.school_id = #{schoolId}
    </select>

    <select id="getSchoolAnswerCard" parameterType="map" resultType="string">
        SELECT
        (SELECT school_name FROM t_exam_school tes WHERE exam_id =  #{examId}  AND tes.school_id = teu.school_id) schoolName
        FROM t_exam_uploader teu
        WHERE teu.school_id IN
        <foreach collection="schoolIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND teu.upload_type = 1
        AND teu.exam_id = #{examId}
        GROUP BY school_id HAVING min(upload_status) > #{uploadStatus}
    </select>

    <!--获取考试信息-->
    <select id="getExamInfoByExamPaperId" resultType="map" parameterType="map">
        SELECT exam_id examId,
               course_name courseName,
               paper_id paperId,
               paper_name paperName,
               course_id courseId
        FROM t_exam_paper
        WHERE exam_paper_id = #{examPaperId}
    </select>

    <!-- 获取学校试卷 -->
    <select id="getExamSchoolUpload" parameterType="map" resultType="map">
        SELECT
        teu.exam_uploader_id examUploaderId,
        teu.answer_card_path answerCardPath,
        tes.school_id schoolId,
        tes.school_name schoolName,
        IFNULL(teu.upload_status,2) uploadStatus,
        #{paperId} paperId,
        #{examId} examId,
        #{courseName} courseName,
        #{paperName} paperName,
        derived.totalStudent totalStudent,
        SUM((SELECT COUNT(1) FROM t_answer_card tac WHERE tac.exam_uploader_id = teu.exam_uploader_id and tac.exam_id = #{examId}))  uploadCount,
        tew.teacher_id teacherId,
        tew.user_id workerUserId,
        tew.teacher_id relativeId
        FROM t_exam_school tes
        LEFT JOIN t_exam_uploader teu ON tes.exam_id = teu.exam_id AND tes.school_id = teu.school_id AND teu.upload_type IN (1, 2) AND teu.paper_id = #{paperId}
        LEFT JOIN t_exam_worker tew ON tes.exam_id = tew.exam_id AND tes.school_id = tew.school_id AND tew.worker_type = #{workerType} AND tew.paper_id = #{paperId} AND tew.course_id = #{courseId}
        INNER JOIN (
        SELECT exam_id examId,
               paper_id paperId,
               school_id schoolId,
               COUNT(1) totalStudent
        FROM t_exam_result
        WHERE exam_id = #{examId} AND paper_id = #{paperId}
        GROUP BY exam_id,paper_id,school_id
        ) derived ON derived.schoolId = tes.school_id
        WHERE tes.exam_id = #{examId}
        <if test="teacherId != null and teacherId !=''">
            AND tew.teacher_id = #{teacherId}
        </if>
        <if test="search !=null and search !=''">
            AND tes.school_name LIKE CONCAT('%',#{search},'%')
        </if>
        GROUP BY tes.school_id
    </select>

    <!--选取最大的statId-->
    <select id="getSchoolStat" parameterType="map" resultType="map">
        SELECT
            test.exam_id examId,
            test.stat_id statId,
            test.school_id schoolId,
            tes.school_name schoolName,
            tes.grade_name gradeName
        FROM t_school_exam_stat test
        INNER JOIN t_exam_school tes ON test.exam_id = tes.exam_id AND test.school_id = tes.school_id
        WHERE test.exam_id = #{examId}
        AND test.deleted = 0
        AND test.is_display = 1
        AND test.school_id = #{schoolId}
        <if test="statStatus != null">
            AND test.stat_status = #{statStatus}
        </if>
        ORDER BY stat_id DESC
        limit 1
    </select>

    <!-- 查询明细ID -->
    <select id="getExamSchoolStatStatus" parameterType="map" resultType="long">
        SELECT
            school_id schoolId
        FROM t_school_exam_stat
        WHERE exam_id = #{examId}
              AND deleted = 0
              AND is_display = 1
              AND stat_status = 1
        <if test="schoolId != null">
            AND school_id  = #{schoolId}
        </if>
    </select>

    <!-- 获取考试基本信息 -->
    <select id="getExamSchoolStatusInfo" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_name examName,
            te.exam_type examType,
            te.grade_type gradeType,
            te.grade_year gradeYear,
            te.start_date startDate,
            te.end_date endDate,
            tes.exam_school_status examStatus,
            te.correct_mode correctMode,
            te.auto_publish autoPublish
        FROM t_exam te INNER JOIN t_exam_school tes on tes.exam_id = te.exam_id
        WHERE te.exam_id = #{examId} and tes.school_id = #{schoolId}
    </select>

  <!-- 获取学校试卷最大状态 -->
    <select id="getExamSchoolPaperStatus" parameterType="map" resultType="map">
        SELECT
            exam_id examId,
            MAX(exam_school_paper_status) examSchoolPaperStatus
        FROM t_exam_school_paper
        WHERE exam_id  IN
        <foreach collection="examIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and school_id = #{schoolId}
        GROUP BY exam_id
    </select>

    <!-- 更新考试學校状态 -->
    <update id="updateExamSchoolStatus" parameterType="map">
        UPDATE t_exam_school
        SET exam_school_status = #{examSchoolStatus},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE exam_id = #{examId}
        <if test="schoolIdList != null">
            AND school_id IN
            <foreach collection="schoolIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <!-- 更新成绩公布状态 -->
    <update id="updateExamSchoolStatStatus" parameterType="map">
        UPDATE t_school_exam_stat
        SET stat_status = #{statStatus},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE exam_id = #{examId}
        AND deleted = 0
        AND is_display = 1
        AND stat_status = 0
    </update>

    <!-- 更新考试學校状态 -->
    <update id="updateExamSchoolStatusBySchoolId" parameterType="map">
        UPDATE t_exam_school
        SET exam_school_status = #{examSchoolStatus},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE exam_id = #{examId}
        <if test="schoolId != null">
            AND school_id = #{schoolId}
        </if>
        AND exam_school_status > 1
    </update>

        <!-- 更新考试學校状态 -->
    <update id="updateExamSchoolStatusBySchoolIdList" parameterType="map">
        UPDATE t_exam_school
        SET exam_school_status = #{examSchoolStatus},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE exam_id = #{examId}
        AND school_id IN
            <foreach collection="schoolIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        AND exam_school_status > 1
    </update>

    <!--获取学校年级下进行中的考试-->
    <select id="getExamIdBySchoolAndGradeId" parameterType="map" resultType="long">
        SELECT exam_id examId
        FROM t_exam_school
        WHERE school_id = #{schoolId}
        AND grade_id = #{gradeId}
        AND exam_school_status =1
    </select>

    <!-- 获取考试 -->
    <select id="getExam" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.correct_mode correctMode,
        te.grade_type gradeType,
        te.grade_year gradeYear,
        te.exam_status examStatus,
        te.auto_publish autoPublish
        FROM t_exam te
        WHERE te.exam_id = #{examId}
    </select>

    <!-- 更新成绩自动公布值 -->
    <update id="updateExamAutoPublish" parameterType="map">
        UPDATE t_exam
        SET auto_publish = #{autoPublish},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE exam_id = #{examId}
    </update>

    <!-- 获取指定时间内未完成的考试 -->
    <select id="getExamIdByByTime" parameterType="map" resultType="long">
        SELECT t1.exam_id
        FROM t_exam t1
        INNER JOIN t_exam_paper t2 ON t1.exam_id = t2.exam_id
        WHERE t1.correct_mode = 1
        AND exam_paper_status = 15
        AND t1.start_date &lt; #{time}
        UNION
        SELECT t1.exam_id
        FROM t_exam t1
        INNER JOIN t_exam_class t2 ON t1.exam_id = t2.exam_id
        WHERE t1.correct_mode = 0 AND t1.exam_type != 8
        AND t2.class_status = 15
        AND t1.start_date &lt; #{time}
    </select>

    <!--获取学校最近的考试时间-->
    <select id="getLatestExamDatetime" parameterType="list" resultType="map">
        SELECT
        /*+ USE_INDEX(tes, schoolIdx) */
            tes.school_id schoolId,
            MIN( tes.create_date_time ) firstExamTime,
            MAX(tes.create_date_time ) latestExamTime
        FROM
            t_exam_school tes
        <if test="list != null and list.size() > 0">
            WHERE
            tes.school_id IN
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY tes.school_id
    </select>

    <!--获取学校的年度考试数量-->
    <select id="countSchoolExamForYear" parameterType="map" resultType="map">
        SELECT
            tes.school_id schoolId,
            COUNT(*) schoolExamNumber
        FROM
            t_exam_school tes
        WHERE
            <if test="schoolIdList != null and schoolIdList.size() > 0">
                tes.school_id IN
                <foreach collection="schoolIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            AND (tes.create_date_time BETWEEN #{startDateTime}
            AND #{endDateTime})
        GROUP BY tes.school_id
    </select>

    <!--成绩导入 start-->
    <insert id="insertExamSchoolForScoreImport" parameterType="map">
        INSERT INTO t_exam_school(
            `exam_id`,
            `area_id`,
            `school_id`,
            `school_name`,
            `grade_id`,
            `grade_type`,
            `grade_name`,
            `exam_school_status`,
            `creator_id`,
            `creator_name`,
            `create_date_time`,
            `modifier_id`,
            `modifier_name`,
            `modify_date_time`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.examId},
            #{item.areaId},
            #{item.schoolId},
            #{item.schoolName},
            #{item.gradeId},
            #{item.gradeType},
            #{item.gradeName},
            #{item.examSchoolStatus},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
    </insert>
    <!--成绩导入 end-->

    <!--根据schoolId和gradeId查询考试 新版错题本考试选择 gradeId非必选-->
    <select id="getExamBySchoolIdAndGradeId" parameterType="map" resultType="map">
        SELECT
            te.exam_id      examId,
            te.exam_name    examName,
            te.exam_type    examType,
            tes.exam_school_status  examStatus,
            te.start_date   examDate,
            te.start_date   startDate,
            tes.school_id   schoolId
        FROM
            t_exam_school tes
        JOIN t_exam te ON tes.exam_id = te.exam_id
        WHERE
            tes.school_id = #{schoolId}
          <if test="gradeId != null">
            AND tes.grade_id = #{gradeId}
          </if>
        <if test="startDate != null and startDate != ''">
            AND te.start_date BETWEEN #{startDate} AND NOW()
        </if>
        <if test="search != null and search != ''">
            AND te.exam_name LIKE CONCAT('%', #{search}, '%')
        </if>
          AND te.exam_type NOT IN ( 8)
        ORDER BY te.start_date DESC, te.exam_id DESC
        <if test="currentIndex != null and pageSize != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <select id="countExamBySchoolIdAndGradeId" parameterType="map" resultType="int">
        SELECT
            COUNT(1)
        FROM
            t_exam_school tes
                JOIN t_exam te ON tes.exam_id = te.exam_id
        WHERE
            tes.school_id = #{schoolId}
          <if test="gradeId != null">
              AND tes.grade_id = #{gradeId}
          </if>
        <if test="startDate != null and startDate != ''">
            AND te.start_date BETWEEN #{startDate} AND NOW()
        </if>
        <if test="search != null and search != ''">
            AND te.exam_name LIKE CONCAT('%', #{search}, '%')
        </if>
          AND te.exam_type NOT IN (8)
    </select>

    <update id="closeExamAllStat" parameterType="map">
        update t_exam_stat tes, t_school_exam_stat tses
        set tes.stat_status = 0,
            tses.stat_status = 0,
            tes.modifier_id = #{userId},
            tes.modifier_name = #{userName},
            tes.modify_date_time = #{currentTime},
            tses.modifier_id = #{userId},
            tses.modifier_name = #{userName},
            tses.modify_date_time = #{currentTime}
        where tes.exam_id = #{examId}
        and tses.exam_id = #{examId}
        and tes.stat_id = tses.stat_id
        and tes.deleted = 0
    </update>

    <update id="updateExamSchoolStatusForStudyGuide" parameterType="map">
        update t_exam_school
        set exam_school_status = #{updateExamStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
          and exam_school_status != #{updateExamStatus}
    </update>

    <update id="updateExamStatStatusForStudyGuide" parameterType="map">
        update t_exam_stat
        set stat_status = #{updateStatStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
          and stat_id = 0
          and stat_status != #{updateStatStatus}
    </update>

    <update id="updateExamSchoolStatStatusForStudyGuide" parameterType="map">
        update t_school_exam_stat
        set stat_status = #{updateExamStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
          and stat_id = 0
          and stat_status != #{updateStatStatus}
    </update>

    <select id="getUnionAndAreaExamSchool" parameterType="map" resultType="map">
        SELECT
            te.exam_id          eaxmId,
            te.exam_name        examName,
            te.exam_type        examType,
            tesp.school_id      schoolId,
            tesp.paper_id       paperId
        FROM
            t_exam  te
        JOIN t_exam_school_paper tesp ON te.exam_id = tesp.exam_id
        WHERE
        te.exam_type IN (7,10,11)
        <if test="paperId != null and paperId.size() > 0">
            AND tesp.paper_id IN
            <foreach collection="paperId" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getSchoolExamStatList" parameterType="map" resultType="map">
        SELECT school_id schoolId,
               stat_status statStatus
        FROM t_school_exam_stat
        WHERE exam_id = #{examId}
            AND deleted = 0
            AND is_display = 1
            AND school_id != 0
    </select>
</mapper>
