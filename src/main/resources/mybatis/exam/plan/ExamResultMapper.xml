<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamResultMapper">

    <!--修改t_exam_result考试学生对应所在班级信息-->
    <update id="updateExamStudentResult" parameterType="map">
        update t_exam_result
        set class_id = #{classId},
            class_name = #{className},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
        and student_id = #{studentId}
        and paper_id = #{paperId}
    </update>

    <!--修改t_exam_result考试学生对应所在班级信息-->
    <update id="updateExamStudentResultBatch" parameterType="map">
        update t_exam_result
        set class_id = #{classId},
            class_name = #{className},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = now()
        where exam_id = #{examId}
        and student_id IN
        <foreach item="item" collection="studentIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and paper_id IN
        <foreach item="item" collection="paperIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 获取考试学生结果 -->
    <select id="getExamResult" parameterType="map" resultType="map">
        SELECT
        `exam_result_id` examResultId,
        `exam_id` examId,
        `course_id` courseId,
        `paper_id` paperId,
        `school_id` schoolId,
        `class_id` classId,
        `class_name` className,
        `student_id` studentId,
        `student_num` studentNum,
        `student_name` studentName,
        `result_status` resultStatus
        FROM t_exam_result
        WHERE exam_id = #{examId}
        <if test="paperId!=null">
            and paper_id = #{paperId}
        </if>
        <if test="schoolIds!=null">
            and school_id in
            <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
                #{schoolId}
            </foreach>
        </if>
        <if test="classIds!=null">
            and class_id in
            <foreach collection="classIds" item="classId" separator="," open="(" close=")">
                #{classId}
            </foreach>
        </if>
        <if test="resultStatus!=null">
            and result_status = #{resultStatus}
        </if>
        <if test="studentIds!=null">
            and student_id in
            <foreach collection="studentIds" item="studentId" separator="," open="(" close=")">
                #{studentId}
            </foreach>
        </if>
    </select>

    <!-- 统计参考学生数量 -->
    <select id="statSchoolReferenceStuCnt" parameterType="map" resultType="map">
        SELECT
            `school_id` schoolId,
            count(1) referenceCount
        FROM t_exam_result
        WHERE exam_id = #{examId}
          and paper_id = #{paperId}
        group by school_id
    </select>

    <!-- 统计实际参考学生数量 -->
    <select id="statSchoolActualStuCnt" parameterType="map" resultType="map">
        SELECT `school_id` schoolId,
               count(1)    actualCount
        FROM t_exam_result
        WHERE exam_id = #{examId}
          and paper_id = #{paperId}
          and result_status = 0
          and uploaded_status = 1
        group by school_id
    </select>

    <update id="updateExamResult" parameterType="map">
        UPDATE `t_exam_result`
        SET
        <if test="resultStatus !=null and resultStatus !=''">
        `result_status` = #{resultStatus},
        </if>
         `modifier_id` = #{userId},
         `modifier_name` = #{userName},
         `modify_date_time` = #{currentTime}
        WHERE exam_result_id = #{examResultId}
    </update>

    <update id="updateExamResultRecover" parameterType="map">
        update t_exam_result
            set result_status = #{resultStatus}
        where exam_id = #{examId} and paper_id = #{paperId}
            and student_id in <foreach collection="examResultStudentIds" item="item" separator="," open="(" close=")">#{item}</foreach>
    </update>
    <!--获取某场考试某个科目某个学生的试卷id-->
    <select id="getStudentExamPaperInfo" parameterType="map" resultType="map">
        SELECT tes.paper_id    paperId,
               te.exam_name    examName,
               tec.course_name courseName
        FROM t_exam_result tes
        JOIN t_exam te ON te.exam_id = tes.exam_id
        JOIN t_exam_course tec ON tec.exam_id = te.exam_id AND tec.course_id = tes.course_id
        WHERE tes.exam_id = #{examId}
          AND tes.course_id = #{courseId}
          AND tes.student_id = #{studentId}
        LIMIT 1
    </select>


    <!--获取学生参加的考试-->
    <select id="getExamStudent" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.start_date startDate,
        te.end_date endDate,
        te.correct_mode correctMode,
        tesc.exam_school_status examStatus,
        tes.student_id studentId,
        tes.student_num studentNum,
        tes.student_name studentName
        FROM t_exam te
        INNER JOIN t_exam_student tes ON  te.exam_id = tes.exam_id AND tes.student_id = #{studentId}
        INNER JOIN t_exam_school tesc ON  tesc.exam_id = te.exam_id AND tesc.school_id= #{schoolId}
        INNER JOIN  t_exam_course tec ON tec.exam_id = te.exam_id and tec.exam_course_status = #{readComplete}
        INNER JOIN t_school_exam_stat tses ON tses.exam_id = te.exam_id
                    AND tses.school_id = #{schoolId} and tses.deleted = 0 and tses.stat_status = 1 AND tses.is_display = 1
        WHERE te.correct_mode = 1
        AND tes.student_id = #{studentId}
        AND te.exam_type IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="startDate != null">
            AND te.start_date >= #{startDate}
        </if>
        GROUP BY te.exam_id
    </select>

    <!--获取学生参加的考试(按班级) -->
    <select id="getExamStudentByClass" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.start_date startDate,
        te.end_date endDate,
        te.exam_status examStatus,
        te.correct_mode correctMode,
        te.homework_type homeworkType,
        te.auto_publish autoPublish,
        tes.student_id studentId,
        tec.class_status classStatus,
        tes.student_num studentNum,
        tes.student_name studentName,
        tec.correct_mode classCorrectMode
        FROM t_exam te, t_exam_student tes, t_exam_class tec
        WHERE te.exam_id = tes.exam_id AND tes.exam_id = tec.exam_id AND tec.class_id = tes.class_id
        AND te.correct_mode = 0
        AND tes.student_id = #{studentId}
        AND tec.class_status = #{classStatus}
        AND te.exam_type IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="startDate != null">
            AND te.start_date >= #{startDate}
        </if>
    </select>

    <!--获取学生参加的考试-->
    <select id="getExamStudentCount" parameterType="map" resultType="int">
        SELECT
            count(*)
        FROM t_exam te, t_exam_student tes, t_exam_school tesc
        WHERE te.exam_id = tes.exam_id AND tesc.exam_id = te.exam_id AND tesc.school_id = tes.school_id
              AND tes.student_id = #{studentId}
              AND tesc.exam_school_status = 3
              AND te.exam_type IN
              <foreach collection="examType" open="(" close=")" separator="," item="item">
                  #{item}
              </foreach>
    </select>


    <!--获取学生参加的考试-->
    <select id="getExamStudentInfo" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.start_date startDate,
        te.end_date endDate,
        te.correct_mode correctMode,
        te.exam_status examStatus,
        te.stage stage,
        tes.school_id schoolId,
        tes.class_id classId,
        tes.class_name className,
        tes.student_id studentId,
        tes.student_num studentNum,
        tes.student_name studentName,
        tesc.comment examComment
        FROM t_exam te
        INNER JOIN  t_exam_student tes ON te.exam_id = tes.exam_id
        LEFT JOIN  t_exam_student_comment tesc ON tesc.exam_student_id = tes.exam_student_id
        WHERE te.exam_id = #{examId}
        AND tes.student_id = #{studentId}
    </select>

    <!--获取考试学生信息-->
    <select id="getExamStudentInfoList" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_name examName,
            te.exam_type examType,
            te.start_date startDate,
            te.end_date endDate,
            te.correct_mode correctMode,
            te.exam_status examStatus,
            tes.school_id schoolId,
            tes.class_id classId,
            tes.class_name className,
            tes.student_id studentId,
            tes.student_num studentNum,
            tes.student_name studentName
        FROM t_exam te
            INNER JOIN  t_exam_student tes ON te.exam_id = tes.exam_id
        WHERE te.exam_id = #{examId}
          AND tes.student_id IN
            <foreach collection="studentIdList" item="studentId" open="(" separator="," close=")">
                  #{studentId}
            </foreach>
    </select>

    <!--获取学生参加的考试-->
    <select id="getStudentExamInfo" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_type examType,
            te.start_date startDate
        FROM t_exam te
            INNER JOIN  t_exam_student tes ON te.exam_id = tes.exam_id
        WHERE  tes.student_id = #{studentId}
        GROUP BY te.exam_id
        ORDER BY startDate DESC
        limit #{limitCount}
    </select>

    <!--获取学生参加的考试-->
    <select id="getExamStartDate" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_type examType,
            te.start_date startDate
        FROM t_exam te WHERE te.exam_id = #{examId}
    </select>

    <!--获取学生参加的考试-->
    <select id="getAllStudentExamAsc" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_type examType,
            te.start_date startDate
        FROM t_exam te
            INNER JOIN t_exam_student tes ON te.exam_id = tes.exam_id
            INNER JOIN t_school_exam_stat tses ON te.exam_id=tses.exam_id AND tses.school_id =#{schoolId}
        WHERE  tes.student_id = #{studentId} AND tses.deleted=0 AND tses.stat_status=1 AND tses.is_display=1
        GROUP BY te.exam_id
        ORDER BY startDate
    </select>

    <!--获取学生参加的考试-->
    <select id="getAllStudentExamAscForWrongBook" parameterType="map" resultType="map">
        SELECT
            te.exam_id examId,
            te.exam_type examType,
            te.start_date startDate,
            te.exam_status examStatus
        FROM t_exam te
                 INNER JOIN t_exam_student tes ON te.exam_id = tes.exam_id
                 INNER JOIN t_school_exam_stat tses ON te.exam_id=tses.exam_id AND tses.school_id =#{schoolId}
        WHERE  tes.student_id = #{studentId} AND tses.deleted=0 AND tses.is_display=1
        GROUP BY te.exam_id
        ORDER BY startDate,te.exam_id
    </select>

    <!--获取考试的科目信息-->
    <select id="getExamCourse" parameterType="map" resultType="map">
        SELECT exam_id examId,
               course_id courseId,
               arts_science artsScience
               FROM t_exam_course WHERE exam_id IN
        <foreach collection="examIds" item="examId" separator="," open="(" close=")">
            #{examId}
        </foreach>
    </select>

    <!--获取考试的科目信息-->
    <select id="getAbsentStudent" parameterType="map" resultType="map">
        SELECT
        ter.exam_id examId,
        ter.course_id courseId,
        tec.course_name courseName,
        ter.school_id schoolId,
        tes.school_name schoolName,
        ter.class_id classId,
        ter.class_name className,
        ter.student_id studentId,
        ter.student_name studentName,
        ter.student_num studentNum
        FROM t_exam_result ter
        INNER JOIN t_exam_course tec ON ter.exam_id = tec.exam_id AND ter.course_id = tec.course_id
        INNER JOIN t_exam_school tes ON ter.exam_id = tes.exam_id AND ter.school_id = tes.school_id
        WHERE ter.exam_id=#{examId} AND ter.result_status = 1
        AND ter.course_id IN
        <foreach collection="courseIds" item="courseId" separator="," open="(" close=")">
            #{courseId}
        </foreach>
        AND ter.school_id IN
        <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
            #{schoolId}
        </foreach>
        AND ter.class_id IN
        <foreach collection="classIds" item="classId" separator="," open="(" close=")">
            #{classId}
        </foreach>
        <if test="search != null and search != ''">
            AND (
            ter.student_name LIKE concat('%', #{search}, '%')
            OR ter.student_num LIKE concat('%', #{search}, '%')
            )
        </if>
    </select>

    <!--获取某场考试某个科目某个学生的试卷id--> <!-- TODO sharding7 checkExamId 需确认 -->
    <select id="getStudentExamPaperByCourseId" parameterType="map" resultType="map">
        SELECT tei.paper_id    paperId,
               te.exam_name    examName
        FROM t_exam te
        JOIN t_exam_item tei ON te.exam_id = tei.exam_id
        WHERE te.exam_id = #{examId}
          AND tei.course_id = #{courseId}
          AND tei.student_id = #{studentId}
        LIMIT 1
    </select>

    <!--获取学生参与的区域考试-->
    <select id="getStudentAreaExam" parameterType="map" resultType="Long">
        SELECT te.exam_id
        FROM t_exam te
        JOIN t_exam_student tes ON te.exam_id = tes.exam_id
        WHERE te.exam_type = #{examType} AND tes.student_id = #{studentId}
    </select>

    <!--查询学生考试最近两场考试 -->
    <select id="getStudentExamLatest" parameterType="map" resultType="map">
        SELECT
        DISTINCT te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.start_date startDate,
        te.end_date endDate,
        te.correct_mode correctMode,
        tes.student_id studentId,
        tec.class_status classStatus,
        tes.student_num studentNum,
        tes.student_name studentName
        FROM t_exam te
        INNER JOIN t_exam_student tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_class tec ON tes.exam_id = tec.exam_id AND tec.class_id = tes.class_id
        INNER JOIN t_school_exam_stat tses ON tses.exam_id = te.exam_id
        AND tses.school_id = tec.school_id AND tses.deleted = 0 AND tses.stat_status = 1 AND tses.is_display = 1
        WHERE tes.student_id = #{studentId}
        AND tec.class_status = #{classStatus}
        AND te.exam_type IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        ORDER BY te.start_date DESC
        limit 2
    </select>

    <!-- 查询学生所有考试根据课程 -->
    <select id="getStudentExam" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.start_date startDate,
        te.end_date endDate,
        te.correct_mode correctMode,
        tes.student_id studentId,
        tes.student_num studentNum,
        tes.student_name studentName,
        tec.course_id courseId,
        tesc.exam_school_status examStatus
        FROM t_exam te
        INNER JOIN t_exam_student tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_school tesc ON  tesc.exam_id = te.exam_id AND tesc.school_id= #{schoolId}
        INNER JOIN t_exam_course tec ON tec.exam_id = te.exam_id AND tec.exam_course_status = #{readComplete}
        INNER JOIN t_school_exam_stat tses ON tses.exam_id = te.exam_id
        AND tses.school_id = #{schoolId} AND tses.deleted = 0 AND tses.stat_status = 1 AND tses.is_display = 1
        WHERE te.correct_mode = 1
        AND tes.student_id = #{studentId}
        AND te.exam_type IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="startDate != null">
            AND te.start_date >= #{startDate}
        </if>
        GROUP BY te.exam_id,tec.course_id
    </select>

    <select id="getStudentExamByClass" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.start_date startDate,
        te.end_date endDate,
        te.correct_mode correctMode,
        tes.student_id studentId,
        tes.student_num studentNum,
        tes.student_name studentName,
        tecl.class_status classStatus,
        teco.course_id courseId,
        tesc.exam_school_status examStatus,
        te.auto_publish autoPublish
        FROM t_exam te
        INNER JOIN t_exam_school tesc ON  tesc.exam_id = te.exam_id AND tesc.school_id= #{schoolId}
        INNER JOIN t_exam_course teco ON te.exam_id = teco.exam_id
        INNER JOIN t_exam_student tes ON te.exam_id = tes.exam_id
        INNER JOIN t_exam_class tecl ON te.exam_id = tecl.exam_id AND
        tes.class_id = tecl.class_id
        WHERE
        te.correct_mode=0
        AND tes.student_id = #{studentId}
        AND tecl.class_status = #{classStatus}
        AND te.exam_type IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="startDate != null">
            AND te.start_date >= #{startDate}
        </if>
        GROUP BY te.exam_id, teco.course_id
    </select>


    <select id="getExamStudentResultClass" parameterType="map" resultType="long">
        SELECT DISTINCT class_id
        FROM t_exam_result
        WHERE exam_id = #{examId}
        and paper_id = #{paperId}
        AND school_id = #{schoolId}
    </select>


    <!-- 查询报告公布情况按试题 -->
    <select id="getExamStatPublishByQuestion" parameterType="map" resultType="map">
        SELECT
        tses.deleted deleted,
        tses.stat_status statStatus,
        tses.is_display isDisplay
        FROM t_exam te
        INNER JOIN  t_exam_course tec ON tec.exam_id = te.exam_id
        INNER JOIN t_school_exam_stat tses ON tses.exam_id = te.exam_id
        WHERE tses.school_id = #{schoolId}
        AND tec.exam_course_status = #{readComplete}
        AND te.exam_id = #{examId}
        AND te.correct_mode = #{correctMode}
        AND tses.stat_id = #{statId}
        limit 1
    </select>

    <!-- 查询报告公布情况按班级 -->
    <select id="getExamStatPublishByClass" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_status examStatus,
        te.homework_type homeworkType,
        tec.class_status classStatus
        FROM t_exam te
        INNER JOIN t_exam_class tec ON tec.exam_id = te.exam_id
        WHERE te.exam_id = #{examId}
        AND te.correct_mode = #{correctMode}
        AND tec.class_id = #{classId}
        limit 1
    </select>

    <select id="getPublishExamIn" parameterType="map" resultType="map">
        SELECT exam_id examId
        FROM t_school_exam_stat
        WHERE school_id = #{schoolId}
        AND deleted = 0
        AND stat_status = 1
        AND is_display = 1
        AND exam_id IN
        <foreach collection="examIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <!--获取考生信息-->
    <select id="getExamResultListByExamUploaderInfo" parameterType="map" resultType="map">
        SELECT
        student_id   studentId,
        student_name studentName,
        student_num  studentNum,
        class_id     classId,
        school_id    schoolId,
        paper_id     paperId
        FROM t_exam_result
        WHERE exam_id = #{examId}
        AND school_id = #{schoolId}
        AND paper_id = #{paperId}
        <if test="classIdList != null and classIdList.size() > 0">
            AND class_id IN
            <foreach collection="classIdList" item="classId" open="(" close=")" separator=",">
                #{classId}
            </foreach>
        </if>
    </select>

    <select id="getExamResultListByExamUploaderCompleted" parameterType="map" resultType="map">
        SELECT /*+INL_JOIN(teu,tac,ter,tei)*/
            ter.student_id   studentId,
            ter.student_name studentName,
            ter.student_num  studentNum,
            ter.class_id     classId,
            ter.school_id    schoolId,
            ter.paper_id     paperId
        FROM t_exam_uploader teu
        JOIN t_answer_card tac
            ON tac.`exam_uploader_id` = teu.`exam_uploader_id`
            <if test="classIdList != null and classIdList.size() > 0">
                AND tac.class_id IN
                <foreach collection="classIdList" item="classId" open="(" close=")" separator=",">
                    #{classId}
                </foreach>
            </if>
        JOIN t_question_structure tqs on tqs.paper_id = teu.paper_id
        JOIN t_exam_item tei ON tei.`exam_id` = teu.`exam_id` AND tei.`paper_id` = teu.`paper_id` AND tei.`student_id` = tac.`student_id` and tei.question_number = tqs.question_number
        JOIN t_exam_result ter ON ter.`exam_id` = teu.`exam_id` AND ter.`paper_id` = teu.`paper_id` AND ter.`student_id` = tac.`student_id`
            <if test="classIdList != null and classIdList.size() > 0">
                AND ter.class_id IN
                <foreach collection="classIdList" item="classId" open="(" close=")" separator=",">
                    #{classId}
                </foreach>
            </if>
        WHERE teu.exam_id = #{examId}
            AND teu.school_id = #{schoolId}
            AND teu.paper_id = #{paperId}
            AND teu.upload_status = #{examUploadStatusProcessingComplete}
        GROUP BY tei.`student_id`;
    </select>

    <!--发布考试 成绩导入模式用 ================================================================ start -->
    <delete id="deleteExamResultForScoreImport" parameterType="map">
        DELETE FROM t_exam_result
        WHERE exam_id = #{examId}
        <if test="courseId != null and courseId != '' and paperId != null and paperId != ''">
            AND course_id = #{courseId}
            AND paper_id = #{paperId}
        </if>
    </delete>


    <insert id="insertExamResultForScoreImport" parameterType="map">
        INSERT INTO `t_exam_result` (
        `exam_id`,
        `course_id`,
        `paper_id`,
        `school_id`,
        `class_id`,
        `class_name`,
        `student_id`,
        `student_num`,
        `student_name`,
        `student_exam_num`,
        `student_name_pinyin`,
        `result_status`,
        `uploaded_status`,
        `creator_id`,
        `creator_name`,
        `create_date_time`,
        `modifier_id`,
        `modifier_name`,
        `modify_date_time`
        )
        VALUES
        <foreach collection="list" separator="," item="item">
            (
            #{item.examId},
            #{item.courseId},
            #{item.paperId},
            #{item.schoolId},
            #{item.classId},
            #{item.className},
            #{item.studentId},
            #{item.studentNum},
            #{item.studentName},
            #{item.studentExamNum},
            #{item.studentNamePinyin},
            #{item.resultStatus},
            2,
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
    </insert>

    <select id="selectExamResultForScoreImport" parameterType="map" resultType="map">
        SELECT
            `exam_id`       examId,
            `course_id`     courseId,
            `paper_id`      paperId,
            `school_id`     schoolId,
            `class_id`      classId,
            `class_name`    className,
            `student_id`    studentId,
            `student_num`   studentNum,
            `student_name`  studentName,
            `result_status` resultStatus
        FROM t_exam_result
        WHERE exam_id = #{examId}
    </select>

    <select id="selectStudentRepeatPaper" parameterType="map" resultType="map">
        SELECT
            ter.exam_id			examId,
            ter.course_id		courseId,
            ter.paper_id		paperId,
            ter.class_id		classId

        FROM
            t_exam_result AS ter
        WHERE
            ter.exam_id = #{examId}
          AND ter.course_id IN
        <foreach collection="courseIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
          AND ter.student_id = #{studentId}
          AND ter.paper_id != #{paperId}

        UNION ALL

        SELECT
            tei.exam_id			examId,
            tei.course_id		courseId,
            tei.paper_id		paperId,
            tei.class_id		classId
        FROM
            t_exam_item tei
        WHERE
            tei.exam_id = #{examId}
          AND tei.course_id IN
            <foreach collection="courseIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
          AND tei.student_id = #{studentId}
          AND tei.paper_id != #{paperId}
    </select>
    <!--发布考试 成绩导入模式用 ================================================================ end -->

    <select id="getAbsentStudentList" parameterType="map" resultType="map">
        SELECT
            ter.exam_id      examId,
            ter.student_id   studentId,
            ter.student_name studentName,
            ter.student_num  studentNum,
            ter.school_id    schoolId,
            tes.school_name  schoolName,
            ter.class_id     classId,
            ter.class_name   className,
            ter.course_id    courseId,
            tec.course_name  courseName
        FROM t_exam_result ter
        JOIN t_exam_school tes
            ON tes.`exam_id` = ter.`exam_id`
            AND tes.`school_id` = ter.`school_id`
        JOIN t_exam_course tec
            ON tec.`exam_id` = ter.`exam_id`
            AND tec.`course_id` = ter.`course_id`
        WHERE ter.`exam_id` = #{examId}
          AND ter.result_status = #{resultStatusAbsent}
        GROUP BY ter.student_id, ter.course_id
    </select>

    <!--获取某个学校缺考学生名单-->
    <select id="getExamAbsentStudentList" parameterType="map" resultType="map">
        SELECT
            ter.exam_id         examId,
            ter.course_id       courseId,
            ter.paper_id        paperId,
            ter.school_id       schoolId,
            ter.class_id        classId,
            ter.class_name      className,
            ter.student_id      studentId,
            ter.student_num     studentNum,
            ter.student_name    studentName,
            ter.result_status   resultStatus
        FROM
            t_exam_result ter
        WHERE
            ter.exam_id = #{examId}
          AND ter.school_id = #{schoolId}
          AND (ter.result_status = #{resultStatusAbsent} OR ter.uploaded_status = #{uploadedStatus})
    </select>

    <!--获取某个学校多场考试的缺考学生名单-->
    <select id="getMultiExamAbsentStudentList" parameterType="map" resultType="map">
        SELECT
            ter.exam_id         examId,
            ter.course_id       courseId,
            ter.paper_id        paperId,
            ter.school_id       schoolId,
            ter.class_id        classId,
            ter.class_name      className,
            ter.student_id      studentId,
            ter.student_num     studentNum,
            ter.student_name    studentName,
            ter.result_status   resultStatus
        FROM
            t_exam_result ter
        WHERE
            ter.exam_id IN
            <foreach collection="examIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
          AND ter.school_id = #{schoolId}
          AND (ter.result_status = #{resultStatusAbsent} OR ter.uploaded_status = #{uploadedStatus})
    </select>

    <select id="getStudentExamResultList" parameterType="map" resultType="map">
        SELECT
            ter.exam_id       examId,
            ter.student_id    studentId,
            ter.student_name  studentName,
            ter.student_num   studentNum,
            ter.school_id     schoolId,
            tes.school_name   schoolName,
            ter.class_id      classId,
            ter.class_name    className,
            ter.course_id     courseId,
            tec.course_name   courseName,
            ter.result_status resultStatus
        FROM t_exam_result ter
        JOIN t_exam_school tes
            ON tes.`exam_id` = ter.`exam_id`
            AND tes.`school_id` = ter.`school_id`
        JOIN t_exam_course tec
            ON tec.`exam_id` = ter.`exam_id`
            AND tec.`course_id` = ter.`course_id`
        WHERE ter.`exam_id` = #{examId}
          AND ter.school_id = #{schoolId}
          AND ter.student_id = #{studentId}
        GROUP BY ter.course_id
    </select>

    <!-- 获取参考人数和实际人数 -->
    <select id="getStudentCount" parameterType="map" resultType="map">
        <choose>
            <when test="examId != null and paperId != null">
                SELECT
                    count(student_id) referenceCount,
                    count(if(result_status = 0 and uploaded_status = 1,student_id, null)) actualCount
                FROM t_exam_result
                WHERE exam_id = #{examId} AND paper_id = #{paperId}
            </when>
            <otherwise>
                SELECT
                    count(distinct student_id)                               referenceCount,
                    count(distinct if(result_status = 0 and uploaded_status = 1, student_id, null)) actualCount
                FROM t_exam_result r
                inner join t_paper_read pr on r.exam_id = pr.exam_id and r.paper_id = pr.paper_id
                WHERE pr.read_block_id = #{readBlockId}
                group by pr.read_block_id;
            </otherwise>
        </choose>

    </select>

    <select id="countStudent" resultType="long">
        select count(1)
        from t_exam_result
        where exam_id = #{examId}
        and paper_id = #{paperId};
    </select>

    <select id="selectStudentExamResultList4ReadIntelligent" parameterType="map" resultType="map">
        SELECT
            `exam_id`       examId,
            `course_id`     courseId,
            `paper_id`      paperId,
            `school_id`     schoolId,
            `class_id`      classId,
            `class_name`    className,
            `student_id`    studentId,
            `student_num`   studentNum,
            `student_name`  studentName,
            `result_status` resultStatus
        FROM t_exam_result
        WHERE exam_id = #{examId} AND paper_id = #{paperId}  AND result_status = 0
    </select>

    <!--通过唯一键获取考生信息-->
    <select id="getExamResultByLogic" parameterType="map" resultType="map">
        SELECT
            `exam_id`       examId,
            `course_id`     courseId,
            `paper_id`      paperId,
            `school_id`     schoolId,
            `class_id`      classId,
            `class_name`    className,
            `student_id`    studentId,
            `student_num`   studentNum,
            `student_name`  studentName,
            `result_status` resultStatus,
            `uploaded_status` uploadedStatus
        FROM t_exam_result
        WHERE exam_id = #{examId} AND paper_id = #{paperId}  AND student_id = #{studentId}
    </select>

    <select id="getExamStudentResultById" parameterType="map" resultType="map">
        select
            paper_id paperId,
            result_status resultStatus
        from t_exam_result
        where exam_id = #{examId}
        and student_id = #{studentId}
        <if test="paperId != null">
            AND paper_id = #{paperId}
        </if>
    </select>

    <select id="getExamResultListByLogic" parameterType="map" resultType="map">
        SELECT
            `exam_id`       examId,
            `course_id`     courseId,
            `paper_id`      paperId,
            `school_id`     schoolId,
            `class_id`      classId,
            `class_name`    className,
            `student_id`    studentId,
            `student_num`   studentNum,
            `student_name`  studentName,
            `result_status` resultStatus,
            `uploaded_status` uploadedStatus
        FROM t_exam_result
        WHERE exam_id = #{examId} AND school_id = #{schoolId}
        <if test="paperIdList != null and paperIdList.size()>0">
            AND paper_id IN
            <foreach collection="paperIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!--更新学生参考状态-->
    <update id="updateExamResultStatus" parameterType="map">
        UPDATE
            t_exam_result
        SET
            result_status = #{examResultStatus},
            modifier_id =#{userId},
            modifier_name =#{userName},
            modify_date_time = #{currentTime}
        WHERE
            exam_id = #{examId}
        AND school_id = #{schoolId}
        AND paper_id = #{paperId}
    </update>

    <update id="updateExamResultStatusByAnswerCardCodes" parameterType="map">
        UPDATE t_exam_result a
        SET a.result_status=#{examResultStatus},
        a.modifier_id =#{userId},
        a.modify_date_time = #{currentTime}
        WHERE a.exam_id = #{examId}
        AND a.paper_id = #{paperId}
        AND a.student_id IN (SELECT b.student_id
        FROM t_answer_card b
        WHERE b.exam_uploader_id = #{examUploaderId}
        and b.student_id !=0
        <if test="batchId != null and batchId != ''">
            and b.batch_id = #{batchId}
        </if>
        <if test="answerCardCodeList != null and answerCardCodeList.size()>0 ">
            and b.answer_card_code IN
            <foreach collection="answerCardCodeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        )
    </update>

    <update id="updateExamResultException" parameterType="map">
        update t_exam_result
            set result_status = #{resultStatus},
            modifier_id = #{userId},
            modify_date_time = #{currentTime}
            where exam_id = #{examId} and paper_id = #{paperId}
            and student_id in
            <foreach collection="studentIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </update>

    <select id="getAbsentStudentIds" parameterType="map" resultType="long">
        select student_id from t_exam_result t
        where t.exam_id = #{examId} and t.paper_id = #{paperId}
        and t.student_id in
        <foreach collection="studentIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
         and t.result_status = 1;
    </select>

    <!--选科异常详情页试用，获取学生的考试科目信息-->
    <select id="getStudentRealResult" parameterType="map" resultType="map">
        SELECT te.stage,
               ter.student_id studentId,
               ter.student_name studentName,
               ter.course_id courseId,
               tec.course_name courseName,
               ter.paper_id paperId,
               ter.student_num studentNum,
               ter.student_exam_num studentExamNum,
               IF(ter.result_status = 0 AND ter.uploaded_status != 0, 0, 1) resultStatus,
               SUM(IFNULL(tei.finally_score, 0)) finallyScore
        FROM t_exam_result ter
        INNER JOIN t_exam te ON ter.exam_id = te.exam_id
        INNER JOIN t_exam_course tec ON ter.exam_id = tec.exam_id AND ter.course_id = tec.course_id
        LEFT JOIN t_exam_item tei ON ter.exam_id = tei.exam_id AND ter.student_id = tei.student_id AND ter.paper_id = tei.paper_id
        WHERE ter.exam_id = #{examId}
            <if test="studentId != null and studentId != ''">
                AND ter.student_id = #{studentId}
            </if>
            <if test="search != null and search != ''">
                <bind name="searchLike" value="'%' + search + '%'"/>
                AND (ter.student_name LIKE #{searchLike} OR ter.student_num LIKE #{searchLike})
            </if>
        GROUP BY ter.student_id, ter.student_name, ter.course_id, tec.course_name,
                 ter.paper_id, ter.student_num, ter.result_status
        ORDER by ter.course_id
    </select>

    <select id="getExamResultByExamIdsAndPaperIds" parameterType="map" resultType="map">
        SELECT
            ter.exam_id                     examId,
            te.exam_name                    examName,
            ter.school_id                   schoolId,
            ter.student_id                  studentId,
            ter.student_name                studentName,
            ter.student_num                 studentNum,
            ter.class_id                    classId,
            ter.class_name                  className,
            ter.result_status               resultStatus,
            ter.student_id                  studentId,
            ter.paper_id                    paperId,
            ter.course_id                   courseId,
            tep.paper_name					paperName,
            tep.course_name					courseName,
            tesp.exam_school_paper_status   examSchoolPaperStatus
        FROM
        t_exam_result ter
        JOIN t_exam te ON te.exam_id = ter.exam_id
        JOIN t_exam_school_paper tesp ON ter.exam_id = tesp.exam_id AND ter.paper_id = tesp.paper_id AND ter.school_id = tesp.school_id
        JOIN t_exam_paper tep ON tesp.paper_id = tep.paper_id AND tesp.exam_id = tep.exam_id
        WHERE
        ter.school_id = #{schoolId}
        AND ( ter.exam_id, ter.paper_id ) IN (
        <foreach collection="list" item="item" separator=",">
            (#{item.examId}, #{item.paperId})
        </foreach>
        )

    </select>

    <select id="getAttendSchoolClass" parameterType="map" resultType="map">
        SELECT
            tes.school_id            schoolId,
            tes.school_name          schoolName,
            tecp.class_id            classId,
            tecp.class_name          className
        FROM t_exam_school_paper tesp
            INNER JOIN t_exam_school tes ON tes.exam_id = tesp.exam_id AND tes.school_id = tesp.school_id
            INNER JOIN t_exam_class_paper  tecp ON tecp.exam_id = tesp.exam_id AND tecp.school_id = tesp.school_id
                AND tecp.paper_id = tesp.paper_id
        WHERE tesp.exam_id = #{examId} AND tesp.paper_id = #{paperId}
        ORDER BY  tesp.school_id, tecp.class_id;
    </select>


    <select id="getStudentList" parameterType="map" resultType="map">
        select *
        from (
            select
            tmp.student_id studentId,
            tmp.student_name studentName,
            tmp.student_num studentNum,
            tmp.student_exam_num studentExamNum,
            tmp.school_name schoolName,
            tmp.school_id schoolId,
            tmp.class_id classId,
            tmp.class_name className,
            tmp.result_status resultStatus,
            (case
            when tmp.tacStudentId is not null then 1
            when tmp.tacbStudentId is not null then 2
            else 0 end
            ) answerCardStatus
        from (
            select
            ter.*,
            tes.school_name,
            ter.student_id studentId,
            tac.student_id tacStudentId,
            tacb.student_id tacbStudentId
            from t_exam_result ter
            inner join t_exam_school tes on tes.exam_id = ter.exam_id and tes.school_id = ter.school_id
            left join (
                select distinct tac.student_id
                from t_exam_uploader teu, t_answer_card tac
                where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
                    <if test="schoolId != null and schoolId != ''">and teu.school_id = #{schoolId}</if>
                and tac.exam_uploader_id = teu.exam_uploader_id
            ) tac on ter.student_id = tac.student_id
            left join (
                select distinct tacb.student_id
                from t_exam_uploader teu, t_answer_card_recycle tacr, t_answer_card_bak tacb
                where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
                  <if test="schoolId != null and schoolId != ''">and teu.school_id = #{schoolId}</if>
                and tacr.exam_uploader_id = teu.exam_uploader_id and tacr.delete_code in (7, 17, 18, 19, 20, 21, 22, 23, 24, 25,26, 27, 28)
                and tacb.recycle_id = tacr.recycle_id
            ) tacb on tacb.student_id = ter.student_id
            where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
                <include refid="ExportStudentFilters"></include>
                group by ter.student_id
            ) tmp
        ) a
        where 1 = 1
        <if test="answerCardStatus != null and answerCardStatus != ''">
            and a.answerCardStatus = #{answerCardStatus}
        </if>
        order by a.schoolId, a.classId, a.studentId
        <if test="pageSize !=null and currentIndex != null">
            limit #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <sql id="ExportStudentFilters">
        <if test="schoolId != null and schoolId != ''">
            and ter.school_id = #{schoolId}
        </if>
        <if test="resultStatus != null and resultStatus != ''">
            and ter.result_status = #{resultStatus}
        </if>
        <if test="classes != null and classes.size > 0">
            and ter.class_id in (<foreach collection="classes" item="item" separator=",">#{item.classId}</foreach>)
        </if>
        <if test="classId != null and classId != ''">
            and ter.class_id = #{classId}
        </if>

        <if test="searchValue != null and searchValue != ''">
            and (ter.student_name like concat('%', #{searchValue}, '%') or ter.student_exam_num like concat('%', #{searchValue}, '%'))
        </if>
    </sql>

    <select id="getStudentStatusCount"  parameterType="map"  resultType="int">
        select count(1)
        from (
            select (case
            when tacStudentId is not null then 1
            when tacbStudentId is not null then 2
            else 0 end
            ) answerCardStatus
            from (
                select
                ter.student_id studentId,
                tac.student_id tacStudentId,
                tacb.student_id tacbStudentId
                from t_exam_result ter
                left join (
                    select distinct tac.student_id
                    from t_exam_uploader teu, t_answer_card tac
                    where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
                    <if test="schoolId != null and schoolId != ''">and teu.school_id = #{schoolId}</if>
                    and tac.exam_uploader_id = teu.exam_uploader_id
                ) tac on ter.student_id = tac.student_id
                left join (
                    select distinct tacb.student_id
                    from t_exam_uploader teu, t_answer_card_recycle tacr, t_answer_card_bak tacb
                    where teu.exam_id = #{examId} and teu.paper_id = #{paperId}
                      <if test="schoolId != null and schoolId != ''">and teu.school_id = #{schoolId}</if>
                        and tacr.exam_uploader_id = teu.exam_uploader_id and tacr.delete_code in (7, 17, 18, 19, 20, 21, 22, 23, 24, 25,26, 27, 28)
                        and tacb.recycle_id = tacr.recycle_id
                ) tacb on tacb.student_id = ter.student_id
                where ter.exam_id = #{examId} and ter.paper_id = #{paperId}
                    <include refid="ExportStudentFilters"></include>
                    group by ter.student_id
            ) tmp
        ) a
        where 1 = 1
        <if test="answerCardStatus != null and answerCardStatus != ''">
            and a.answerCardStatus = #{answerCardStatus}
        </if>
    </select>

    <select id="getToolsAbsentStudentIds" parameterType="map" resultType="long">
        SELECT
            distinct ter.student_id
        FROM t_exam_result ter, t_exam_uploader teu, t_answer_card_bak tacb
        WHERE  teu.exam_id = ter.exam_id AND teu.paper_id = ter.paper_id AND teu.school_id = ter.school_id
          AND tacb.exam_uploader_id = teu.exam_uploader_id
          AND ter.exam_id = #{examId} AND ter.paper_id = #{paperId}  AND ter.result_status = 1
          <if test="schoolId != null and schoolId != ''">
              AND ter.school_id = #{schoolId}
          </if>;
    </select>

    <select id="getStudentSchoolInfo" parameterType="map" resultType="map">
        SELECT
               ter.school_id            schoolId,
               ter.student_id           studentId,
               ter.class_id             classId,
               ter.exam_id              examId,
               ter.paper_id             paperId
        FROM t_exam_result  ter
        WHERE ter.exam_id = #{examId} AND ter.paper_id = #{paperId}
            AND ter.student_id in
            <foreach collection="studentIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>


    <select id="getAppendStudent" parameterType="map" resultType="map">
        SELECT
                ter.student_id          studentId,
                1                       resultStatus
        FROM t_exam_result ter
        INNER JOIN t_exam_uploader teu ON ter.exam_id = teu.exam_id
                                        AND ter.paper_id = teu.paper_id
                                        AND ter.school_id = teu.school_id
                                        AND teu.upload_type in (11, 12)
                                        AND teu.upload_status <![CDATA[<]]> 7
        INNER JOIN t_answer_card tac ON tac.exam_uploader_id = teu.exam_uploader_id
                                        AND tac.student_id = ter.student_id
        WHERE ter.exam_id = #{examId} AND ter.paper_id = #{paperId}
            AND ter.school_id IN
            <foreach collection="completedSchoolIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND ter.student_id IN
            <foreach collection="studentIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="getProcessingStudent" parameterType="map" resultType="map">
        SELECT
                ter.student_id              studentId,
                0                           resultStatus
        FROM t_exam_result ter
        WHERE ter.exam_id = #{examId} AND ter.paper_id = #{paperId}
                AND ter.school_id IN
                <foreach collection="schoolIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND ter.student_id IN
                <foreach collection="studentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND ter.result_status = 1;
    </select>

    <select id="getAbsentStudentCount" parameterType="map" resultType="int">
        SELECT COUNT(*) FROM (
            SELECT
            ter.student_name                               studentName,
            ter.student_id                                 studentId,
            ter.student_num                                studentNum,
            ter.school_id                                  schoolId,
            tep.paper_id                                   paperId,
            tecp.course_id                                 courseId,
            tecp.course_name                               courseName,
            ter.class_id                                   classId,
            ter.class_name                                 className,
            tes.school_name                                schoolName,
            IF(tacb.student_id != 0, '是', '否')            isExistsCard,
            ter.result_status                              resultStatus
        FROM t_exam_result ter
        INNER JOIN t_exam_paper tep ON ter.exam_id = tep.exam_id AND ter.paper_id = tep.paper_id
        INNER JOIN t_exam_course tecp ON tecp.exam_id= tep.exam_id AND tecp.course_id = tep.course_id
        INNER JOIN t_exam_school tes ON tes.school_id = ter.school_id AND tes.exam_id = ter.exam_id
        LEFT JOIN t_exam_uploader teu ON ter.exam_id = teu.exam_id AND ter.paper_id = teu.paper_id AND ter.school_id = teu.school_id
        LEFT JOIN t_answer_card_recycle tacr ON tacr.exam_uploader_id = teu.exam_uploader_id
        LEFT JOIN t_answer_card_bak tacb ON tacb.recycle_id = tacr.recycle_id
            AND tacb.student_id = ter.student_id AND tacr.delete_code in (7, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28)
        WHERE teu.exam_id = #{examId} AND teu.paper_id = #{paperId}
        AND teu.school_id = #{schoolId}
        <if test="classes != null and classes.size > 0">
            AND ter.class_id IN (
                <foreach collection="classes" item="item" separator=",">
                    #{item.classId}
                </foreach>
            )
        </if>
        GROUP BY ter.student_id
        ) tmp
        WHERE tmp.resultStatus = 1;
    </select>

    <select id="exportAbsentStudent" parameterType="map" resultType="map">
        SELECT * FROM (
             SELECT
                 ter.student_name                               studentName,
                 ter.student_id                                 studentId,
                 ter.student_num                                studentNum,
                 ter.student_exam_num                           studentExamNum,
                 ter.school_id                                  schoolId,
                 tep.paper_id                                   paperId,
                 tecp.course_id                                 courseId,
                 tecp.course_name                               courseName,
                 ter.class_id                                   classId,
                 ter.class_name                                 className,
                 tes.school_name                                schoolName,
                 IF(max(tacb.student_id) != 0, '是', '否')       isExistsCard,
                 ter.result_status                              resultStatus
             FROM t_exam_result ter
                      INNER JOIN t_exam_paper tep ON ter.exam_id = tep.exam_id AND ter.paper_id = tep.paper_id
                      INNER JOIN t_exam_course tecp ON tecp.exam_id= tep.exam_id AND tecp.course_id = tep.course_id
                      INNER JOIN t_exam_school tes ON tes.school_id = ter.school_id AND tes.exam_id = ter.exam_id
                      LEFT JOIN t_exam_uploader teu ON ter.exam_id = teu.exam_id AND ter.paper_id = teu.paper_id AND ter.school_id = teu.school_id
                      LEFT JOIN t_answer_card_recycle tacr ON tacr.exam_uploader_id = teu.exam_uploader_id
                      LEFT JOIN t_answer_card_bak tacb ON tacb.recycle_id = tacr.recycle_id
                             AND tacb.student_id = ter.student_id AND tacr.delete_code in (7, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28)
             WHERE teu.exam_id = #{examId} AND teu.paper_id = #{paperId}
             <include refid="ExportStudentFilters"></include>
             GROUP BY ter.student_id
        ) tmp
        WHERE tmp.resultStatus = 1

        ORDER BY tmp.schoolId, tmp.classId;
    </select>

    <select id="getExamPaperInfo" parameterType="map" resultType="map">
        SELECT
               tep.paper_name       paperName,
               tes.school_name      schoolName,
               te.exam_name         examName
        FROM t_exam_school tes, t_exam_paper tep, t_exam te
        WHERE tep.exam_id = #{examId}  AND tep.paper_id = #{paperId}
            AND tes.exam_id = tep.exam_id AND tes.school_id = #{schoolId}
            AND te.exam_id = tep.exam_id
    </select>

    <update id="updateAbsentForModifyToOtherStudent" parameterType="map">
        UPDATE t_exam_result
            SET result_status = 1
        WHERE exam_id = #{examId} AND paper_id = #{paperId} AND school_id = #{schoolId}
              AND student_id = #{oldStudentId}
    </update>

    <select id="getExamUploaderAttendStudentCount" parameterType="map" resultType="int">
        SELECT COUNT(1) FROM t_exam_result
        WHERE exam_id = #{examId} AND paper_id = #{paperId} AND school_id = #{schoolId};
    </select>

    <select id="getWrongBookStudentResult" parameterType="map" resultType="map">
        SELECT
            ter.exam_id examId,
            ter.paper_id paperId,
            ter.school_id schoolId,
            ter.class_id classId,
            ter.class_name className,
            ter.student_id studentId,
            ter.student_num studentNum,
            ter.student_name studentName,
            ter.result_status resultStatus
        FROM
            t_exam_result ter
        JOIN t_exam_school tes ON ter.exam_id = tes.exam_id AND ter.school_id = tes.school_id
        WHERE
            ter.school_id = #{schoolId}
          AND tes.grade_id = #{gradeId}
          AND ter.student_id = #{studentId}
          AND ter.result_status = 0
          AND ter.exam_id IN
        <foreach collection="examIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="updateStudentResult" parameterType="map">
        update t_exam_result
            set result_status = #{resultStatus}
        where exam_id = #{examId} and paper_id = #{paperId}
            and student_id = #{studentId}
    </select>

    <select id="getExamResultStudent" parameterType="map" resultType="map">
        select student_num studentNum
        from t_exam_result ter, t_exam_uploader teu
        where ter.exam_id = teu.exam_id and ter.paper_id = teu.paper_id
            and ter.student_id = #{studentId}
            and teu.exam_uploader_id = #{examUploaderId};
    </select>

    <select id="getSchoolCourseInfo" parameterType="map" resultType="map">
        select
               concat( tes.school_name , '-' , tep.course_name) schoolCourseName,
               NOW()   timeName
        from t_exam_paper tep, t_exam_school tes
        where tes.exam_id = #{examId} and tes.school_id = #{schoolId}
          and tep.exam_id = tes.exam_id and tep.paper_id = #{paperId};
    </select>

    <update id="rollbackExamResultStatus"  parameterType="map">
        update t_exam_result
            set result_status = 0
        where exam_id = #{examId} and paper_id = #{paperId} and school_id = #{schoolId}
            <if test="absentStudentIds != null and absentStudentIds.size > 0">
                and student_id not in (<foreach collection="absentStudentIds" item="item" separator=",">#{item}</foreach>)
            </if>
    </update>

    <select id="getExamItemStudents" parameterType="map" resultType="map">
        select
            ter.student_id   studentId,
            ter.student_name studentName,
            ter.student_num  studentNum,
            ter.class_id     classId,
            ter.school_id    schoolId,
            ter.paper_id     paperId
        from t_exam_result ter, t_exam_item tei
        where ter.exam_id = #{examId} and ter.paper_id = #{paperId} and ter.school_id = #{schoolId}
            <if test="classIdList != null and classIdList.size() > 0">
                and ter.class_id in
                <foreach collection="classIdList" item="classId" open="(" close=")" separator=",">
                    #{classId}
                </foreach>
            </if>
            and tei.exam_id = ter.exam_id and tei.paper_id = ter.paper_id
            and tei.question_number in (<foreach collection="questionNumbers" item="item" separator=",">#{item}</foreach>)
            and tei.student_id = ter.student_id
            group by tei.student_id
            having count(1) = #{minQNCount};
    </select>

    <select id="getExistsStuResultByCourseAndStuList" parameterType="map" resultType="map">
        select tep.course_id courseId,
               tep.paper_id paperId,
               ter.student_id studentId
        from t_exam_paper tep
        inner join t_exam_result ter on tep.exam_id = ter.exam_id and tep.paper_id = ter.paper_id
        where tep.exam_id = #{examId}
          and tep.course_id in
        <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
          and ter.student_id in
        <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </select>
</mapper>