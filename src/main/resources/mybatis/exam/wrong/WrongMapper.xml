<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="WrongMapper">


    <!--根据student_id、question_id、question_number查出wrong_item_id-->
    <select id="selectWrongItemIdByUniqueKey" parameterType="map" resultType="Long">
        SELECT wrong_item_id
        FROM t_wrong_item
        WHERE student_id=#{studentId} AND question_id=#{questionId} AND question_number=#{questionNumber}
    </select>

    <!--批量插入错题的标签-->
    <insert id="batchInsertItemTag">
        INSERT INTO t_wrong_tag_item
        (
            student_id,
            wrong_item_id,
            wrong_tag_id,
            tag_name,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        VALUES
        <foreach collection="tagList" item="tag" separator=",">
            (
            #{tag.studentId},
            #{tag.wrongItemId},
            #{tag.wrongTagId},
            #{tag.tagName},
            #{tag.userId},
            #{tag.userName},
            #{tag.createDateTime},
            #{tag.userId},
            #{tag.userName},
            #{tag.modifyDateTime}
            )
        </foreach>
    </insert>

    <!--删除指定错题的所有标签-->
    <delete id="deleteItemAllTagByUniqueKey" parameterType="map">
        DELETE twti
        FROM t_wrong_tag_item twti
        JOIN t_wrong_item twi ON twti.wrong_item_id = twi.wrong_item_id
        WHERE twi.question_id=#{questionId} AND twi.student_id=#{studentId} AND twi.question_number=#{questionNumber}
    </delete>

    <!--查询指定课程的所有标签-->
    <select id="selectCourseAllTag" parameterType="map" resultType="map">
        SELECT
        wrong_tag_id wrongTagId,
        course_name courseName,
        tag_name tagName,
        tag_sort tagSort
        FROM t_wrong_tag
        WHERE course_id=#{courseId}
        ORDER BY tag_sort
    </select>

    <!-- 根据试题查询错题明细 -->
    <select id="selectWrongTagItems" parameterType="map" resultType="map">
        SELECT
        twti.wrong_tag_item_id wrongTagItemId,
        twti.wrong_item_id wrongItemId,
        twti.wrong_tag_id wrongTagId,
        twti.tag_name tagName
        FROM t_wrong_tag_item twti
        WHERE twti.wrong_item_id IN
        <foreach collection="wrongItemIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <!-- 根据错题Id查询试卷 -->
    <select id="selectWrongPaperId" parameterType="map" resultType="map">
        SELECT
            twi.paper_id paperId
        FROM t_wrong_item twi
        WHERE twi.question_Id = #{Id} and twi.student_Id = #{studentId}
    </select>

    <!--查询指定错题的所有标签-->
    <select id="selectItemAllTagByUniqueKey" parameterType="map" resultType="map">
        SELECT
            twti.wrong_tag_item_id wrongTagItemId,
            twti.tag_name tagName,
            twti.wrong_tag_id wrongTagId,
            twti.wrong_item_id wrongItemId
        FROM t_wrong_tag_item twti
        JOIN t_wrong_item twi
        ON twi.wrong_item_id = twti.wrong_item_id
        WHERE twi.student_id =#{studentId} AND twi.question_id=#{questionId} AND twi.question_number=#{questionNumber}
    </select>


    <!-- 查询学生课程错题数量 -->
    <select id="selectStudentWrongItemTotalCount" parameterType="map" resultType="map">
        SELECT
          course_id courseId,
          count(DISTINCT question_id) totalCount
        FROM t_wrong_item
        WHERE student_id = #{studentId}
        AND wrong_item_status = 1
        GROUP BY course_id;
    </select>


    <!-- 查询学生课程错题订正数量 -->
    <select id="selectStudentWrongItemExerciseCount" parameterType="map" resultType="map">
        SELECT
            course_id courseId,
            count(DISTINCT question_id) exerciseCount
        FROM t_wrong_item twi
        WHERE EXISTS(SELECT twei.wrong_exercise_item_id FROM t_wrong_exercise_item twei WHERE twei.wrong_item_id = twi.wrong_item_id)
        AND twi.student_id = #{studentId}
        AND twi.wrong_item_status = 1
        GROUP BY course_id;
    </select>

    <!-- 查询学生本周课程错题新增数量 -->
    <select id="selectStudentWrongItemAddCount" parameterType="map" resultType="map">
        SELECT
          course_id courseId,
          count(DISTINCT question_id) addCount
        FROM t_wrong_item
        WHERE student_id = #{studentId}
        AND wrong_item_status = 1
        AND create_date_time &gt; #{startDate}
        AND create_date_time &lt; #{endDate}
        GROUP BY course_id;
    </select>

    <!-- 查询学生本周课程错题消灭数量 -->
    <select id="selectStudentWrongItemDelCount" parameterType="map" resultType="map">
        SELECT
          course_id courseId,
          count(DISTINCT question_id) delCount
        FROM t_wrong_item
        WHERE student_id = #{studentId}
        AND wrong_item_status = 0
        AND create_date_time &gt; #{startDate}
        AND create_date_time &lt; #{endDate}
        GROUP BY course_id;
    </select>

    <!-- 查询学生错题明细数量 -->
    <select id="selectStudentWrongItemCount" parameterType="map" resultType="int">
        SELECT /*+INL_JOIN(twei)*/ count(1) FROM (
            SELECT twi.question_id
            FROM t_wrong_item twi
            INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
            <if test="sourceType != null and sourceType == 1">
            INNER JOIN t_exam te ON te.exam_id = twei.source_id
            </if>
            <if test="sourceType != null and sourceType == 2">
            INNER JOIN t_exercise_plan tep ON tep.exercise_plan_id = twei.source_id
            </if>
            <if test="knowledgeIds != null">
                INNER JOIN t_question_knowledge tqk ON twi.question_id = tqk.question_id
            </if>
            <if test="leftDifficulty != null and rightDifficulty != null">
                INNER JOIN t_question_difficulty tqd ON twi.question_id = tqd.question_id
            </if>
            WHERE twi.student_id = #{studentId}
            AND twi.course_id = #{courseId}
            AND twi.wrong_item_status = #{wrongItemStatus}
            <if test="sourceType != null and sourceType != ''">
                AND twei.source_type = #{sourceType}
            </if>
            <if test="sourceId != null and sourceId != ''">
                AND twei.source_id = #{sourceId}
            </if>
            <if test="startDate != null and startDate != '' and sourceType != null and sourceType == 1">
                AND te.start_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' and sourceType != null and sourceType == 1">
                AND te.start_date &lt;= #{endDate}
            </if>
            <if test="questionType != null and questionType != ''">
                AND twi.question_type = #{questionType}
            </if>
            <if test="knowledgeIds != null">
                AND tqk.knowledge_id IN
                <foreach collection="knowledgeIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="leftDifficulty != null and rightDifficulty != null">
                AND tqd.difficulty &gt; #{leftDifficulty}
                AND tqd.difficulty &lt; #{rightDifficulty}
            </if>
            GROUP BY twi.question_id
        ) t
    </select>

    <!-- 查询学生错题明细列表 -->
    <select id="selectWrongItemByQuestionId" parameterType="map" resultType="map">
        SELECT
          twi.wrong_item_id wrongItemId,
          twei.source_id sourceId,
          twei.source_type sourceType,
          twi.course_id courseId,
          twi.paper_id paperId,
          twi.question_id questionId,
          twi.question_number questionNumber,
          twi.structure_number structureNumber,
          twi.wrong_item_status wrongItemStatus,
          twi.exam_item_id examItemId
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        WHERE twi.student_id = #{studentId}
        AND twi.question_id = #{questionId}
        <if test="sourceType != null and sourceType != ''">
            AND twei.source_type = #{sourceType}
        </if>
        <if test="wrongItemId != null and wrongItemId != ''">
            AND twi.wrong_item_id = #{wrongItemId}
        </if>
        group by twi.question_id
    </select>

    <!-- 根据当前科目显示所有错因 -->
    <select id="selectWrongTagNames" parameterType="map" resultType="map">
        SELECT tag_name tagName
        FROM t_wrong_tag
        WHERE course_id = #{courseId}
    </select>

    <!-- 查询错题标签 -->
    <select id="selectStudentWrongItems" parameterType="map" resultType="map">
        SELECT
        /*+INL_JOIN(twei)*/
        twi.wrong_item_id wrongItemId,
        twi.student_id studentId,
        twei.source_type sourceType,
        twei.source_id sourceId,
        twei.wrong_type wrongType,
        twk.knowledge_id knowledgeId,
        <if test="sourceType != null and sourceType == 1">
            te.exam_name sourceName,
            te.exam_id examId,
            twei.revise_status reviseStatus,
        </if>
        <if test="sourceType != null and sourceType == 2">
            tep.exercise_plan_name sourceName,
            tep.exercise_plan_id exercisePlanId,
        </if>
        twi.question_id questionId,
        twi.question_number questionNumber,
        twi.structure_number structureNumber
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        LEFT JOIN t_question_knowledge twk ON twi.question_id = twk.question_id
        <if test="sourceType != null and sourceType == 1">
            INNER JOIN t_exam te ON te.exam_id = twei.source_id
            INNER JOIN t_exam_item tei ON tei.exam_item_id = twei.source_item_id
        </if>
        <if test="sourceType != null and sourceType == 2">
            INNER JOIN t_exercise_plan tep ON tep.exercise_plan_id = twei.source_id
            INNER JOIN t_exercise_result_item teri ON teri.exercise_result_item_id = twei.source_item_id
        </if>

        <if test="leftDifficulty != null and rightDifficulty != null">
            INNER JOIN t_question_difficulty tqd ON twi.question_id = tqd.question_id
        </if>
        WHERE twi.student_id = #{studentId}
        AND twi.course_id = #{courseId}
        AND twi.wrong_item_status = #{wrongItemStatus}
        <if test="wrongType != null and wrongType != ''">
            AND twei.wrong_type = #{wrongType}
        </if>
        <if test="sourceType != null and sourceType != ''">
            AND twei.source_type = #{sourceType}
        </if>
        <if test="sourceId != null and sourceId != ''">
            AND twei.source_id = #{sourceId}
        </if>
        <if test="startDate != null and startDate != '' and sourceType != null and sourceType == 1">
            AND te.start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != '' and sourceType != null and sourceType == 1">
            AND te.start_date &lt;= #{endDate}
        </if>
        <if test="questionType != null and questionType != ''">
            AND twi.question_type = #{questionType}
        </if>
        <if test="leftDifficulty != null and rightDifficulty != null">
            AND tqd.difficulty &gt; #{leftDifficulty}
            AND tqd.difficulty &lt; #{rightDifficulty}
        </if>
        <if test="knowledgeIds != null and knowledgeIds.size() != 0">
            AND twk.knowledge_id in
            <foreach collection="knowledgeIds" item="knowledgeId" open="(" close=")" separator=",">
                #{knowledgeId}
            </foreach>
        </if>
        <if test="sourceType != null and sourceType == 1">
            <if test="examTypes != null and examTypes.size() != 0">
                AND te.exam_type IN
                <foreach collection="examTypes" item="examType" open="(" separator="," close=")">
                    #{examType}
                </foreach>
            </if>
        </if>
        GROUP BY twi.question_id
    </select>

    <!-- 查询错题 -->
    <select id="getStudentWrongItemsByKnowledgeIds" parameterType="map" resultType="map">
        SELECT
        /*+INL_JOIN(twei)*/
        twk.knowledge_id knowledgeId,
        twi.question_id questionId
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        LEFT JOIN t_question_knowledge twk ON twi.question_id = twk.question_id
        INNER JOIN t_exam te ON te.exam_id = twei.source_id
        INNER JOIN t_exam_item tei ON tei.exam_item_id = twei.source_item_id
        WHERE twi.student_id = #{studentId}
        AND twi.course_id = #{courseId}
        AND twi.wrong_item_status = 1
        and twk.knowledge_id in
        <foreach collection="knowledgeList" item="item" open="(" close=")" separator=",">
            #{item.knowledgeId}
        </foreach>
        <if test="wrongType != null and wrongType != ''">
            AND twei.wrong_type = #{wrongType}
        </if>
        AND twei.source_type = 1
        GROUP BY twk.knowledge_id, twi.question_id
    </select>

    <!-- 查询错题练习数量 -->
    <select id="selectWrongExerciseCount" parameterType="map" resultType="map">
        SELECT
          twei.wrong_item_id wrongItemId,
          COUNT(1) totalCount,
          COUNT(twei.result_type = 1 OR NULL) rightCount
        FROM t_wrong_exercise_item twei
        WHERE twei.wrong_item_id IN
        <foreach collection="wrongItemIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY twei.wrong_item_id
    </select>

    <!-- TODO sharding8 无法确定examId，错题本无法确定examId-->
    <select id="selectExamItems" parameterType="list" resultType="map">
        SELECT
          exam_item_id examItemId,
          school_id schoolId,
          class_id classId,
          finally_score finallyScore,
          score_value scoreValue,
          recognition_value recognitionValue,
          save_file_url saveFileUrl,
          read_type readType
        FROM t_exam_item
        WHERE exam_item_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectExerciseItems" parameterType="list" resultType="map">
        SELECT
        exercise_result_item_id exerciseResultItemId,
        finally_score finallyScore,
        score_value scoreValue,
        save_file_url saveFileUrl,
        read_type readType
        FROM t_exercise_result_item
        WHERE exercise_result_item_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!-- 查询得分 -->
    <select id="selectWrongExamItemsSource" parameterType="map" resultType="map">
        SELECT
            twi.wrong_item_id wrongItemId,
            twei.source_type sourceType,
            twei.source_item_id sourceItemId
        FROM t_wrong_item twi
            INNER JOIN t_wrong_exam_item twei ON twi.wrong_item_id = twei.wrong_item_id
            INNER JOIN t_exam_item tei ON tei.exam_item_id = twei.source_item_id
        WHERE twi.wrong_item_id IN
            <foreach collection="wrongItemIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND twei.source_type = 1
        UNION
        SELECT
            twi.wrong_item_id wrongItemId,
            twei.source_type sourceType,
            twei.source_item_id sourceItemId
        FROM t_wrong_item twi
            INNER JOIN t_wrong_exam_item twei ON twi.wrong_item_id = twei.wrong_item_id
            INNER JOIN t_exercise_result_item teri ON teri.exercise_result_item_id = twei.source_item_id
        WHERE twi.wrong_item_id IN
            <foreach collection="wrongItemIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            AND twei.source_type = 2
    </select>



    <!-- 更新错题明细 -->
    <update id="updateWrongItem" parameterType="map">
        UPDATE t_wrong_item
        SET
            <if test="sourceType != null and sourceType != ''">
              source_type = #{sourceType},
            </if>
            wrong_item_status = #{wrongItemStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE student_id = #{studentId}
        AND question_id = #{questionId}
    </update>

    <!-- 获取类型的ID集合 -->
    <select id="selectExerciseIds" parameterType="map" resultType="map">
        SELECT /*+INL_JOIN(twei)*/
          twi.question_id questionId,
          twi.wrong_item_id wrongItemId
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twi.wrong_item_id = twei.wrong_item_id
        <if test="sourceType != null and sourceType == 1">
            INNER JOIN t_exam te ON te.exam_id = twei.source_id
        </if>
        <if test="sourceType != null and sourceType == 2">
            INNER JOIN t_exercise_plan tep ON tep.exercise_plan_id = twei.source_id
        </if>
        WHERE twi.student_id = #{studentId} AND twi.course_id = #{courseId} AND twi.wrong_item_status = #{wrongItemStatus}
        <if test="readType != null and readType != ''">
            AND twi.read_type = #{readType}
        </if>
        <if test="wrongType != null and wrongType != '' and wrongType != 0">
            AND twei.wrong_type = #{wrongType}
        </if>
        <if test="wrongType != null and wrongType != '' and wrongType == 0">
            AND twei.wrong_type IN (3,4,5)
        </if>
        <if test="startDate != null and startDate != ''">
            AND twi.create_date_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND twi.create_date_time &lt;= #{endDate}
        </if>
        GROUP BY twi.question_id
    </select>

    <!-- 保存练习记录 -->
    <insert id="insertWrongExerciseItem" parameterType="map">
        INSERT INTO t_wrong_exercise_item (
          wrong_item_id,
          current_answer,
          correct_answer,
          result_type,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{wrongItemId},
          #{currentAnswer},
          #{correctAnswer},
          #{resultType},
          #{userId},
          #{userName},
          #{currentTime},
          #{userId},
          #{userName},
          #{currentTime}
        )
    </insert>

    <!-- 查询知识点 -->
    <select id="selectWrongKnowledge" parameterType="map" resultType="String">
        SELECT  /*+INL_JOIN(tqk, twi, twei)*/ DISTINCT
        tqk.knowledge_id
        FROM t_wrong_item twi
        INNER JOIN t_question_knowledge tqk ON twi.question_id = tqk.question_id
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        <if test="sourceType != null and sourceType == 1">
            INNER JOIN t_exam te ON te.exam_id = twei.source_id
        </if>
        <if test="sourceType != null and sourceType == 2">
            INNER JOIN t_exercise_plan tep ON tep.exercise_plan_id = twei.source_id
        </if>
        WHERE twi.student_id = #{studentId}
            AND twi.course_id = #{courseId}
        <if test="parentId != null and parentId != ''">
            AND tqk.parent_id = #{parentId}
        </if>
        <if test="wrongType != null and wrongType != ''">
            AND twei.wrong_type = #{wrongType}
        </if>
        <if test="startDate != null and startDate != ''">
            AND start_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND end_date &lt;= #{endDate}
        </if>
    </select>

    <!-- 查询标签 -->
    <select id="selectWrongTag" parameterType="map" resultType="map">
        SELECT
          wrong_tag_id wrongTagId,
          1 tagType,
          tag_name tagName
        FROM t_wrong_tag_item
        WHERE student_id = #{studentId}
        <if test="tagName!=null and tagName!=''">
            AND tag_name = #{tagName}
        </if>
        <if test="tagNameList!=null and tagNameList.size()>0">
            AND tag_name IN
            <foreach collection="tagNameList" item="tagName" separator="," open="(" close=")">
                #{tagName}
            </foreach>
        </if>
        GROUP BY tagName
    </select>

    <!--根据wrong_item_id进行分组，得到每一题的标签数-->
    <select id="selectTagCountGroupByWrongItemId" parameterType="map" resultType="map">
        SELECT wrong_item_id wrongItemId,count(*) tagCount
        FROM t_wrong_tag_item
        <where>
            <if test="wrongItemIdList!=null and wrongItemIdList.size()>0">
              wrong_item_id IN
                <foreach collection="wrongItemIdList" item="wrongItemId" open="(" close=")" separator=",">
                    #{wrongItemId}
                </foreach>
            </if>
        </where>
        GROUP BY wrong_item_id
    </select>

    <!--获取错题文件路径-->
    <select id="getWrongItemFileURL" parameterType="map" resultType="map">
      SELECT
        wrong_item_id wrongItemId,
        source_id sourceId,
        source_type sourceType,
        save_file_url fileURL,
        revise_status reviseStatus
        FROM t_wrong_exam_item
        <where>
            <if test="wrongItemId!=null">
                wrong_item_id=#{wrongItemId}
            </if>
            <if test="sourceId!=null">
                AND source_id=#{sourceId}
            </if>
            <if test="sourceType!=null">
                AND  source_type=#{sourceType}
            </if>
        </where>
    </select>

    <!--修改错题图片路径及订正状态-->
    <update id="updateReviseStatusOrFileURL" parameterType="map">
        UPDATE t_wrong_exam_item
        <set>
            <if test="reviseStatus != null">
                revise_status = #{reviseStatus},
            </if>
            <if test="fileURL != null and fileURL != ''">
                save_file_url = #{fileURL},
            </if>
            modifier_id=#{userId},
            modifier_name=#{userName},
            modify_date_time=#{currentTime}
        </set>
        WHERE   wrong_item_id=#{wrongItemId}
                AND source_id=#{sourceId}
                AND  source_type=#{sourceType}
    </update>

    <!--修改错题订正状态-->
    <update id="updateReviseStatus" parameterType="map">
        UPDATE t_wrong_exam_item
        SET revise_status = #{reviseStatus},
        modifier_id=#{userId},
        modifier_name=#{userName},
        modify_date_time=#{currentTime}
        <where>
                source_type=#{sourceType}
                AND wrong_item_id=#{wrongItemId}
                AND source_id =#{sourceId}
        </where>

    </update>

    <!--获取学生的所有错题标签-->
    <select id="selectStudentWrongItemTagList" parameterType="map" resultType="map">
        SELECT
        tag_name tagName,
        1 tagType
        FROM t_wrong_tag_item
        WHERE student_id=#{studentId}
        GROUP BY tag_name
    </select>

    <!-- 获取错题明细标签 -->
    <select id="selectWrongItemTag" parameterType="map" resultType="map">
        SELECT
          wrong_tag_item_id wrongTagItemId,
          wrong_item_id wrongItemId,
          wrong_tag_id wrongTagId,
          tag_name tagName
        FROM t_wrong_tag_item twti
        WHERE twti.wrong_item_id = #{wrongItemId}
    </select>


    <!-- 插入标签 -->
    <insert id="insertWrongTag" parameterType="map" useGeneratedKeys="true" keyColumn="wrong_tag_id" keyProperty="wrongTagId">
        INSERT INTO t_wrong_tag (
          student_id,
          tag_name,
          tag_type,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{studentId},
          #{tagName},
          #{tagType},
          #{userId},
          #{userName},
          #{currentTime},
          #{userId},
          #{userName},
          #{currentTime}
        )ON DUPLICATE KEY UPDATE
        tag_type=VALUES(tag_type),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 根据标签名称查询标签 -->
    <select id="selectWrongTagItemByName" parameterType="map" resultType="map">
        SELECT
          wrong_item_id wrongItemId,
          tag_name tagName
        FROM t_wrong_tag_item
        WHERE wrong_item_id = #{wrongItemId}
        <if test="tagName!=null and tagName!=''">
            AND tag_name = #{tagName}
        </if>
        <if test="tagNameList!=null and tagNameList.size()>0">
            AND tag_name in
            <foreach collection="tagNameList" item="tagName" separator="," open="(" close=")">
                #{tagName}
            </foreach>
        </if>
        <if test="studentId!=null">
            AND student_id=#{studentId}
        </if>

    </select>


    <!-- 批量新增错题明细标签 -->
    <insert id="batchInsertWrongTagItem" parameterType="map">
        INSERT INTO t_wrong_tag_item (
          student_id,
          wrong_item_id,
          wrong_tag_id,
          tag_name,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES
        <foreach collection="wrongTagList" item="wrongTag" separator=",">
            (
            #{wrongTag.studentId},
            #{wrongTag.wrongItemId},
            #{wrongTag.wrongTagId},
            #{wrongTag.tagName},
            #{wrongTag.userId},
            #{wrongTag.userName},
            #{wrongTag.currentTime},
            #{wrongTag.userId},
            #{wrongTag.userName},
            #{wrongTag.currentTime}
            )
        </foreach>
    </insert>

    <!-- 新增错题明细标签 -->
    <insert id="insertWrongTagItem" parameterType="map">
        INSERT INTO t_wrong_tag_item (
          student_id,
          wrong_item_id,
          wrong_tag_id,
          tag_name,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{studentId},
          #{wrongItemId},
          #{wrongTagId},
          #{tagName},
          #{userId},
          #{userName},
          #{currentTime},
          #{userId},
          #{userName},
          #{currentTime}
        )ON DUPLICATE KEY UPDATE
        wrong_item_id=VALUES(wrong_item_id),
        wrong_tag_id=VALUES(wrong_tag_id),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!--删除学生的错题标签-->
    <delete id="deleteStudentWrongItemTag" parameterType="map">
        DELETE FROM t_wrong_tag_item
        WHERE student_id=#{studentId}
        AND tag_name IN
        <foreach collection="tagNameList" item="tagName" separator="," open="(" close=")">
            #{tagName}
        </foreach>
    </delete>

    <!-- 删除错题明细标签 -->
    <delete id="deleteWrongTagItem" parameterType="map">
        DELETE FROM t_wrong_tag_item
        <where>
            <if test="wrongTagItemId != null">
                wrong_tag_item_id = #{wrongTagItemId}
            </if>
            <if test="wrongTagItemIdList!=null and wrongTagItemIdList.size()>0">
                wrong_tag_item_id IN
                <foreach collection="wrongTagItemIdList" item="wrongTagItemId" separator="," open="(" close=")">
                    #{wrongTagItemId}
                </foreach>
            </if>
        </where>

    </delete>

    <!-- 查询回收站课程 -->
    <select id="selectTrashCourse" parameterType="map" resultType="long">
        SELECT
          course_id courseId
        FROM t_wrong_item
        WHERE wrong_item_status = 0
        AND student_id = #{studentId}
        <if test="sourceType != null and sourceType != ''">
            AND source_type = #{sourceType}
        </if>
        GROUP BY course_id
    </select>

    <!-- 获取学生错题数量 可限制时间 -->
    <select id="getWrongTopicCount" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(DISTINCT twi.question_id)
        FROM t_wrong_item twi
        WHERE twi.student_id = #{studentId}
          AND twi.wrong_item_status = 1
        <if test="sourceType != null and sourceType != ''">
            AND source_type = #{sourceType}
        </if>
        <if test="courseId != null and courseId != ''">
            AND twi.course_id = #{courseId}
        </if>
        <if test="startDate != null">
            AND twi.modify_date_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND twi.modify_date_time &lt; #{endDate}
        </if>
    </select>

    <insert id="saveStudentWrongExplain" parameterType="map">
        insert into t_wrong_student_question_explain (
            student_id,
            course_id,
            question_id,
            source_type,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES (
            #{studentId},
            #{courseId},
            #{questionId},
            #{sourceType},
            #{userId},
            #{userName},
            now(),
            #{userId},
            #{userName},
            now()
            )
        on duplicate key update
        modifier_id = values(modifier_id),
        modifier_name = values(modifier_name),
        modify_date_time = values(modify_date_time)
    </insert>

    <select id="getWrongStudentExplainCountDetail" resultType="com.dongni.analysis.view.myclass.bean.WrongExplainStudentDetailVO"
      parameterType="map">
        select
            count(distinct twsqe.question_id) wrongExplainCount,
            count(distinct if(twsqe.modify_date_time >= #{startDate} and twsqe.modify_date_time &lt; #{endDate}, twsqe.question_id, null))
                thisWeekWrongExplainCount
        from t_wrong_item twi
        inner join t_wrong_student_question_explain twsqe on twi.student_id = twsqe.student_id and twi.question_id = twsqe.question_id
        where twi.student_id = #{studentId}
        and twi.wrong_item_status = 1
        <if test="courseId != null and courseId != ''">
            and twi.course_id = #{courseId}
        </if>
    </select>

    <select id="getClassWrongExplainCountDetail" resultType="com.dongni.analysis.view.myclass.bean.WrongExplainCountDetailVO"
      parameterType="map">
        select
            count(distinct twi.question_id) wrongExplainCount,
            count(distinct twi.student_id, twi.question_id) wrongStudentCount,
            count(distinct if(twsqe.modify_date_time >= #{startDate} and twsqe.modify_date_time &lt; #{endDate},
                twi.question_id, null)) thisWeekWrongExplainCount,
            count(distinct if(twsqe.modify_date_time >= #{startDate} and twsqe.modify_date_time &lt; #{endDate},
                CONCAT(twi.student_id, '-', twi.question_id), null)) thisWeekWrongStudentCount
        from t_wrong_item twi
        inner join t_wrong_student_question_explain twsqe on twi.student_id = twsqe.student_id and twi.question_id = twsqe.question_id
        where twi.student_id in
        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
            #{studentId}
        </foreach>
        and twi.wrong_item_status = 1
        <if test="courseId != null and courseId != ''">
            and twi.course_id = #{courseId}
        </if>
    </select>

    <select id="getStudentsWrongExplainDetail" resultType="com.dongni.analysis.view.myclass.bean.WrongExplainStudentDetailVO"
      parameterType="map">
        select
            twi.student_id studentId,
            count(distinct twi.question_id) wrongQuestionCount,
            count(distinct twsqe.question_id) wrongExplainCount,
            count(distinct if(twi.modify_date_time >= #{startDate} and twi.modify_date_time &lt; #{endDate}, twi.question_id, null))
                thisWeekWrongQuestionCount,
            count(distinct if(twsqe.modify_date_time >= #{startDate} and twsqe.modify_date_time &lt; #{endDate}, twsqe.question_id, null))
                thisWeekWrongExplainCount
        from t_wrong_item twi
        left join t_wrong_student_question_explain twsqe on twi.student_id = twsqe.student_id and twi.question_id = twsqe.question_id
        where twi.student_id in
        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
            #{studentId}
        </foreach>
        and twi.wrong_item_status = 1
        <if test="courseId != null and courseId != ''">
            and twi.course_id = #{courseId}
        </if>
        group by twi.student_id
    </select>

    <select id="getWrongStudentsQuestionExplain" resultType="map" parameterType="map" >
        select distinct
            twi.student_id studentId,
            twi.question_id questionId
        from t_wrong_item twi
        inner join t_wrong_student_question_explain twsqe on twi.student_id = twsqe.student_id and twi.question_id = twsqe.question_id
        where twi.student_id in
        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
            #{studentId}
        </foreach>
        and twi.question_id in
        <foreach collection="questionIds" item="questionId" open="(" close=")" separator=",">
            #{questionId}
        </foreach>
        and twi.wrong_item_status = 1
    </select>

    <select id="getWrongStudentsQuestionExplainByIds" resultType="map" parameterType="map" >
        select distinct
            twi.student_id studentId,
            twi.question_id questionId
        from t_wrong_item twi
        inner join t_wrong_student_question_explain twsqe on twi.student_id = twsqe.student_id and twi.question_id = twsqe.question_id
        where twi.student_id in
        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
            #{studentId}
        </foreach>
        and twi.question_id in
        <foreach collection="questionIds" item="questionId" open="(" close=")" separator=",">
            #{questionId}
        </foreach>
        and twi.wrong_item_status = 1
    </select>

    <select id="getStudentsQuestionExplainByIds" resultType="map" parameterType="map" >
        select distinct
            twi.student_id studentId,
            twi.question_id questionId
        from t_wrong_item twi
        inner join t_wrong_student_question_explain twsqe on twi.student_id = twsqe.student_id and twi.question_id = twsqe.question_id
        where twi.student_id in
        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
            #{studentId}
        </foreach>
        and twi.question_id in
        <foreach collection="questionIds" item="questionId" open="(" close=")" separator=",">
            #{questionId}
        </foreach>
        and twi.wrong_item_status = 1
        and twsqe.source_type = 1
        union
        select distinct
            student_id studentId,
            question_id questionId
        from t_wrong_student_question_explain
        where student_id in
        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
            #{studentId}
        </foreach>
        and question_id in
        <foreach collection="questionIds" item="questionId" open="(" close=")" separator=",">
            #{questionId}
        </foreach>
        and source_type = 2
    </select>

    <select id="getDestroyWrongTopic" parameterType="java.util.Map" resultType="long">
        SELECT
            COUNT(DISTINCT twi.question_id) destroyCount,
            twi.course_id                   courseId
        FROM t_wrong_item twi
        WHERE twi.student_id = #{studentId}
          AND twi.course_id = #{courseId}
          AND twi.wrong_item_status = 0
          <if test="sourceType != null and sourceType != ''">
              AND source_type = #{sourceType}
          </if>
          AND twi.modify_date_time &gt;= #{startDate}
          AND twi.modify_date_time > #{endDate}

    </select>

    <!-- 查询学生考试的错题数 -->
    <select id="getStudentExamWrongCount" parameterType="map" resultType="int">
        SELECT /*+INL_JOIN(twei)*/ COUNT(DISTINCT twi.question_id)
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        INNER JOIN t_exam te ON twei.source_id = te.exam_id
        INNER JOIN t_exam_school tes ON tes.exam_id = te.exam_id
        WHERE twi.student_id = #{studentId}
        AND twi.course_id = #{courseId}
        AND te.grade_type = #{gradeType}
        AND te.grade_year = #{gradeYear}
        AND twei.modify_date_time &gt;= #{startDate}
        AND twei.modify_date_time &lt;= #{endDate}
        AND twei.source_type = #{sourceType}
        AND tes.school_id = #{schoolId}
        AND tes.exam_school_status = #{examSchoolStatus}
    </select>

    <!-- 查询学生所有考试根据课程 -->
    <select id="getStudentWrongExamCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_exam te
        INNER JOIN t_exam_school tesc ON tesc.exam_id = te.exam_id
        INNER JOIN t_exam_result ter ON ter.exam_id = te.exam_id AND ter.school_id = tesc.school_id
        WHERE ter.student_id = #{studentId}
        AND ter.course_id = #{courseId}
        AND te.grade_type = #{gradeType}
        AND te.grade_year = #{gradeYear}
        AND tesc.exam_school_status != #{examSchoolStatus}
        AND te.exam_type NOT IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
              #{item}
        </foreach>
        ORDER BY
            te.create_date_time DESC
    </select>
    <!-- 查询学生所有考试根据课程 -->
    <select id="getStudentWrongExam" parameterType="map" resultType="map">
        SELECT /*+ INL_JOIN(te, tesc, tec, ter, tses) */
	      te.exam_id examId,
	      te.exam_name examName,
	      te.exam_type examType,
	      te.start_date startDate,
	      te.correct_mode correctMode,
	      tesc.exam_school_status examStatus,
          ter.course_id courseId,
	      ter.student_id studentId,
	      ter.student_num studentNum,
	      ter.student_name studentName
        FROM t_exam te
        INNER JOIN t_exam_school tesc ON tesc.exam_id = te.exam_id AND tesc.school_id = #{schoolId}
        INNER JOIN t_exam_course tec ON tec.exam_id = te.exam_id and tec.exam_course_status = #{readComplete}
        INNER JOIN t_exam_result ter ON ter.exam_id = te.exam_id AND ter.school_id = tesc.school_id
        INNER JOIN t_school_exam_stat tses ON tses.exam_id = te.exam_id
              AND tses.school_id = #{schoolId} and tses.deleted = 0 and tses.stat_status = 1 AND tses.is_display = 1
        WHERE te.correct_mode = 1
        AND ter.student_id = #{studentId}
        AND te.grade_type = #{gradeType}
        AND te.grade_year = #{gradeYear}
        AND te.exam_type NOT IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY te.exam_id,ter.course_id
    </select>

    <select id="getStudentWrongExamByClass" parameterType="map" resultType="map">
        SELECT
        te.exam_id examId,
        te.exam_name examName,
        te.exam_type examType,
        te.start_date startDate,
        te.correct_mode correctMode,
        tesc.exam_school_status examStatus,
        ter.course_id courseId,
        ter.student_id studentId,
        ter.student_num studentNum,
        ter.student_name studentName
        FROM t_exam te
        INNER JOIN t_exam_result ter ON ter.exam_id = te.exam_id
        INNER JOIN t_exam_school tesc ON tesc.exam_id = te.exam_id AND tesc.school_id = #{schoolId} AND ter.school_id = tesc.school_id
        INNER JOIN t_exam_class tecl ON ter.exam_id = tecl.exam_id AND ter.class_id = tecl.class_id
        WHERE
        te.correct_mode = 0
        AND ter.student_id = #{studentId}
        AND te.grade_type = #{gradeType}
        AND te.grade_year = #{gradeYear}
        AND tecl.class_status = #{classStatus}
        AND te.exam_type NOT IN
        <foreach collection="examType" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY te.exam_id,ter.course_id
    </select>


    <!-- 查询学生考试的错题数 -->
    <select id="getStudentExamCount" parameterType="map" resultType="int">
        SELECT COUNT(DISTINCT twi.question_id)
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        WHERE twi.student_id = #{studentId}
        AND twi.course_id = #{courseId}
        AND twei.source_id = #{examId}
        AND twei.source_type = #{sourceType}
    </select>

    <!-- 查询学生考试的错题数 -->
    <select id="getStudentExamCountIn" parameterType="map" resultType="map">
        SELECT
        twei.source_id examId,
        twi.course_id courseId,
        COUNT(DISTINCT twi.question_id) wrongItemCount
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        WHERE twi.student_id = #{studentId}
        AND twei.source_type = #{sourceType}
        <if test="courseIds != null">
            AND twi.course_id IN
            <foreach collection="courseIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY twei.source_id, twi.course_id
    </select>



     <!-- 查询学生包含错题的考试 -->
    <select id="getStudentExamExistWrong" parameterType="map" resultType="long">
        SELECT
        twei.source_id
        FROM
        t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        WHERE
        twi.student_id = #{studentId}
        AND twi.course_id = #{courseId}
        AND twei.source_id in
        <foreach collection="examIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND twei.source_type = #{sourceType}
        GROUP BY twei.source_id
    </select>

    <select id="getStudentExamExistWrongIn" parameterType="map" resultType="long">
        SELECT
        twei.source_id
        FROM
        t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        WHERE
        twi.student_id = #{studentId}
        AND twi.course_id IN
        <foreach collection="courseIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND twei.source_id IN
        <foreach collection="examIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND twei.source_type = #{sourceType}
        GROUP BY twei.source_id
    </select>

    <!-- 查询学生包含错题的考试 -->
    <select id="getStudentExamExistWrongByExam" parameterType="map" resultType="long">
        SELECT
        twei.source_id
        FROM
        t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twei.wrong_item_id = twi.wrong_item_id
        WHERE
        twi.student_id = #{studentId}
        AND twei.source_type = #{sourceType}
        GROUP BY twei.source_id
    </select>

    <!-- 获取错题readType-->
    <select id="getWrongItemReadType" parameterType="map" resultType="int">
        SELECT
            read_type
        FROM t_wrong_item
        WHERE wrong_item_id = #{wrongItemId}
    </select>



    <!-- 查询学生错题明细列表 -->
    <select id="getStudentExamWrongItem" parameterType="map" resultType="map">
       SELECT
          /*+INL_JOIN(twei)*/
          twi.student_id studentId,
          twi.course_id courseId,
          twei.source_type sourceType,
          te.exam_id examId,
          te.exam_name examName,
          te.exam_type examType,
          twi.question_id questionId,
          twi.question_number questionNumber,
          twi.structure_number structureNumber,
          te.start_date startDate,
          te.end_date endDate,#
          te.create_date_time createDateTime,
          tqd.difficulty
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twi.wrong_item_id = twei.wrong_item_id
        INNER JOIN t_exam te ON twei.source_id = te.exam_id
        INNER JOIN t_exam_school tes ON tes.exam_id = te.exam_id
        LEFT JOIN t_question_difficulty tqd ON twi.question_id = tqd.question_id
        WHERE twi.student_id = #{studentId}
        AND twi.course_id = #{courseId}
        AND twei.source_type = #{sourceType}
        <if test="gradeType != null and gradeType != ''">
            AND te.grade_type = #{gradeType}
        </if>
        <if test="gradeYear != null and gradeYear != ''">
            AND te.grade_year = #{gradeYear}
        </if>
        <if test="examId != null and examId != ''">
            AND te.exam_id = #{examId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND twei.modify_date_time &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND twei.modify_date_time &lt;= #{endDate}
        </if>
        <if test="examSchoolStatus != null and examSchoolStatus != ''">
            AND tes.exam_school_status = #{examSchoolStatus}
        </if>
        AND tes.school_id = #{schoolId}
        GROUP BY twi.question_id
        ORDER BY te.create_date_time DESC,twi.question_number ASC
    </select>

    <select id="getStudentExamWrongItemByIds" parameterType="map" resultType="map">
       SELECT
          twi.student_id studentId,
          twi.course_id courseId,
          twei.source_type sourceType,
          te.exam_id examId,
          te.exam_name examName,
          te.exam_type examType,
          twi.question_id questionId,
          twi.question_number questionNumber,
          twi.structure_number structureNumber,
          te.start_date startDate,
          te.end_date endDate,
          te.create_date_time createDateTime,
          twi.wrong_item_id wrongItemId,
          tqd.difficulty
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei ON twi.wrong_item_id = twei.wrong_item_id
        INNER JOIN t_exam te ON twei.source_id = te.exam_id
        INNER JOIN t_exam_school tes ON tes.exam_id = te.exam_id
        LEFT JOIN t_question_difficulty tqd ON twi.question_id = tqd.question_id
        WHERE twi.wrong_item_id IN
            <foreach collection="list" item="wrongItemId" open="(" separator="," close=")">
                #{wrongItemId}
            </foreach>
        GROUP BY twi.wrong_item_id
    </select>

    <!-- 保存错题文档 -->
    <insert id="insertWrongDocument" parameterType="map" keyProperty="wrongDocumentId" useGeneratedKeys="true">
        INSERT INTO t_wrong_document (
          document_name,
          status,
          link,
          finish_time,
          type,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{documentName},
          #{status},
          #{link},
          #{finishTime},
          #{type},
          #{userId},
          #{userName},
          #{currentTime},
          #{userId},
          #{userName},
          #{currentTime}
        )
    </insert>

    <!-- 查询错题文档列表 -->
    <select id="getExamDocumentCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_wrong_document twd
        WHERE twd.creator_id = #{userId}
        <if test="type != null and type != ''">
            AND twd.type = #{type}
        </if>
        <if test="nowTime != null and nowTime != ''">
            AND twd.modify_date_time > #{nowTime}
        </if>
    </select>

    <!-- 查询错题文档列表 -->
    <select id="getExamDocument" parameterType="map" resultType="map">
        SELECT
          twd.wrong_document_id wrongDocumentId,
          twd.document_name documentName,
          twd.status,
          twd.link,
          twd.type,
          twd.finish_time finishTime
        FROM t_wrong_document twd
        WHERE twd.creator_id = #{userId}
        <if test="type != null and type != ''">
            AND twd.type = #{type}
        </if>
        <if test="nowTime != null and nowTime != ''">
            AND twd.modify_date_time &gt; #{nowTime}
        </if>
        ORDER BY twd.create_date_time desc
        <if test="pageNo != null and pageSize != null">
            limit #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!-- 更新文档生成状态 -->
    <update id="updateWrongDocument" parameterType="map">
        UPDATE t_wrong_document
         SET status = #{status},
         link = #{link},
         finish_time = #{finishTime},
         modifier_id = #{userId},
         modifier_name = #{userName},
         modify_date_time = #{currentTime}
        WHERE wrong_document_id = #{wrongDocumentId}
            AND status IN (0, 2)
    </update>

    <update id="resetWrongDocumentStatus" parameterType="map">
        UPDATE t_wrong_document
        SET status = #{status},
            link = #{link},
            finish_time = #{finishTime},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE wrong_document_id = #{wrongDocumentId}
    </update>

    <update id="batchUpdateExamWrongDocumentFailed" parameterType="map">
        UPDATE t_wrong_document
        SET status = #{status},
            finish_time = #{finishTime},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE wrong_document_id IN
            <foreach collection="wrongDocumentIds" item="wrongDocumentId" open="(" separator="," close=")">
                #{wrongDocumentId}
            </foreach>
            AND status IN (0, 2)
    </update>

    <!-- 获取错题明细标签 -->
    <select id="selectWrongExerciseFinish" parameterType="map" resultType="int">
        select count(*)
        from (
                 SELECT DISTINCT twi.wrong_item_id
                              FROM t_wrong_exercise_item twei, t_wrong_item twi
                              WHERE twi.wrong_item_id = twei.wrong_item_id and twi.student_id = #{studentId}) as tb

    </select>


    <!-- 插入记录         t_wrong_tag -->
    <insert id="initWrongTag" parameterType="map">
        INSERT INTO t_wrong_tag (
        tag_name,
        course_id,
        course_name,
        tag_sort,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.tagName},
            #{item.courseId},
            #{item.courseName},
            #{item.tagSort},
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )

        </foreach>
        ON DUPLICATE KEY UPDATE
        modifier_id = VALUES(modifier_id),
        modifier_name = VALUES(modifier_name),
        modify_date_time = VALUES(modify_date_time)

    </insert>

    <!-- TODO sharding7 checkExamId -->
    <select id="getStudentExamCourse" parameterType="map" resultType="long">
        SELECT
        course_id courseId
        FROM t_exam_item
        WHERE exam_id = #{examId} AND student_id = #{studentId}
        GROUP BY course_id
    </select>

    <select id="getExamDocumentById" parameterType="map" resultType="map">
        SELECT
          twd.wrong_document_id wrongDocumentId,
          twd.document_name documentName,
          twd.status,
          twd.link,
          twd.type,
          twd.finish_time finishTime
        FROM t_wrong_document twd
        WHERE twd.wrong_document_id = #{wrongDocumentId}
        AND status = 1
    </select>

    <select id="getExamDocumentInfoById" parameterType="long" resultType="map">
        SELECT
          twd.wrong_document_id wrongDocumentId,
          twd.document_name documentName,
          twd.status,
          twd.link,
          twd.type,
          twd.finish_time finishTime
        FROM t_wrong_document twd
        WHERE twd.wrong_document_id = #{0}
    </select>

    <select id="getExamStudent" parameterType="map" resultType="map">
        select
        school_id schoolId,
        student_name studentName
        from  t_exam_student
        where exam_id in
        <foreach collection="examIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and student_id = #{studentId}
         limit 1
    </select>

    <!--获取考试的错题-->
    <select id="getExamWrongItem" parameterType="map" resultType="map">
        SELECT
            twi.student_id          studentId,
            tei.course_id           courseId,
            twi.paper_id            paperId,
            twi.wrong_item_status   wrongItemStatus,
            twi.question_type       questionType
        FROM
            t_wrong_item AS twi
        JOIN t_exam_item AS tei ON twi.exam_item_id = tei.exam_item_id
        WHERE
            tei.exam_id = #{examId}

          <if test="studentIdList != null and studentIdList.size() >0">
              AND tei.student_id IN
              <foreach collection="studentIdList" item="item" separator="," open="(" close=")">
                  #{item}
              </foreach>
          </if>

            AND tei.paper_id IN
            <foreach collection="paperIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>

          AND twi.wrong_item_status =1;
    </select>

    <select id="getMultiExamWrongItemList" parameterType="map" resultType="map">
        SELECT
        te.exam_id              examId,
        te.exam_name            examName,
        twi.student_id          studentId,
        tei.course_id           courseId,
        twi.paper_id            paperId,
        twi.wrong_item_status   wrongItemStatus,
        twi.question_type       questionType
        FROM
        t_wrong_item twi
        JOIN t_exam_item tei ON twi.exam_item_id = tei.exam_item_id
        JOIN t_exam te ON tei.exam_id = te.exam_id
        WHERE
        tei.school_id = #{schoolId}
        AND (tei.exam_id, tei.paper_id) IN
        (
        <foreach collection="list" item="item" separator=",">
            ( #{item.examId}, #{item.paperId})
        </foreach>
        )
        AND twi.wrong_item_status =1;
    </select>

    <!--获取多场考试的错题-->
    <select id="getStudentMultiExamWrongItemList" parameterType="map" resultType="map">
        SELECT
        te.exam_id              examId,
        te.exam_name            examName,
        twi.student_id          studentId,
        tei.course_id           courseId,
        twi.paper_id            paperId,
        twi.wrong_item_status   wrongItemStatus,
        twi.question_type       questionType
        FROM
        t_wrong_item twi
        JOIN t_exam_item tei ON twi.exam_item_id = tei.exam_item_id
        JOIN t_exam te ON tei.exam_id = te.exam_id
        WHERE
        (tei.exam_id, tei.paper_id, tei.school_id) IN
            (
                <foreach collection="examStudentParamsList" item="item" separator=",">
                   ( #{item.examId}, #{item.paperId}, #{item.schoolId})
                </foreach>
            )
        AND twi.wrong_item_status =1;
    </select>

    <!--根据wrongItemId列表查询关联的examId-->
    <select id="getExamItemByWrongItemIds" parameterType="map" resultType="map">
        SELECT twei.wrong_item_id wrongItemId,
               tei.exam_item_id examItemId,
               tei.score_value scoreValue,
               tei.finally_score finallyScore
        FROM t_wrong_exam_item twei
        INNER JOIN t_exam_item tei ON twei.source_item_id = tei.exam_item_id
        WHERE twei.source_type = 1
        AND twei.wrong_item_id IN
        <foreach collection="wrongItemIds" item="wrongItemId" separator="," open="(" close=")">
            #{wrongItemId}
        </foreach>
    </select>

    <!--根据wrongItemId列表查询关联的examId-->
    <select id="getExamItemByWrongItemIds2" parameterType="map" resultType="map">
        SELECT twi.wrong_item_id wrongItemId,
               tei.exam_item_id examItemId,
               tei.finally_score finallyScore,
               tei.paper_id paperId,
               tei.question_number questionNumber
        FROM t_wrong_item twi
        INNER JOIN t_exam_item tei ON twi.exam_item_id = tei.exam_item_id
        WHERE twi.wrong_item_id IN
        <foreach collection="wrongItemIds" item="wrongItemId" separator="," open="(" close=")">
            #{wrongItemId}
        </foreach>
    </select>

    <!--根据wrongDocumentId删除记录-->
    <delete id="deleteWrongDocument" parameterType="map">
        DELETE FROM t_wrong_document
        WHERE wrong_document_id = #{wrongDocumentId}
    </delete>

    <!--根据id获取生成状态-->
    <select id="checkWrongDocument" parameterType="long" resultType="map">
        SELECT wrong_document_id wrongDocumentId,
               `status`,
               link
        FROM t_wrong_document
        WHERE wrong_document_id IN
        <foreach collection="list" item="wrongDocumentId" open="(" separator="," close=")">
            #{wrongDocumentId}
        </foreach>
    </select>

    <select id="countStudentWrong" parameterType="map" resultType="long">
        SELECT /*+ INL_JOIN(twi, twei, te, tes) */ COUNT(DISTINCT tqd.question_id)
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei
            ON twei.wrong_item_id = twi.wrong_item_id
        INNER JOIN t_exam te
            ON te.exam_id = twei.source_id
        INNER JOIN t_exam_school tes
            ON tes.exam_id = te.exam_id
        INNER JOIN t_question_difficulty tqd
                ON twi.question_id = tqd.question_id
        WHERE twi.student_id = #{studentId}
            AND twi.course_id = #{courseId}
            AND tes.school_id = #{schoolId}
            AND twei.source_type = 1
            AND te.exam_type NOT IN (12)
            AND tes.exam_school_status = 3
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND te.start_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="vipVisibilityDate != null">
                AND te.start_date >= #{vipVisibilityDate}
            </if>
            <if test="difficultyLeft != null and difficultyRight != null">
                <choose>
                    <when test="difficultyLeft == 0">
                        AND tqd.difficulty >= 0 AND tqd.difficulty &lt;= #{difficultyRight}
                    </when>
                    <otherwise>
                        AND tqd.difficulty > #{difficultyLeft} AND tqd.difficulty &lt;= #{difficultyRight}
                    </otherwise>
                </choose>
            </if>
    </select>

    <select id="countStudentWrongGroupByCourseId" parameterType="map" resultType="map">
        SELECT /*+ INL_JOIN(twi, twei, te, tes) */
            twi.course_id courseId,
            COUNT(DISTINCT tqd.question_id) count
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei
            ON twei.wrong_item_id = twi.wrong_item_id
        INNER JOIN t_exam te
            ON te.exam_id = twei.source_id
        INNER JOIN t_exam_school tes
            ON tes.exam_id = te.exam_id
        INNER JOIN t_question_difficulty tqd
                ON twi.question_id = tqd.question_id
        WHERE twi.student_id = #{studentId}
            AND tes.school_id = #{schoolId}
            AND twei.source_type = 1
            AND tes.exam_school_status = 3
            AND te.exam_type NOT IN (12)
        GROUP BY twi.course_id
    </select>

    <select id="getStudentWrong" parameterType="map" resultType="map">
        SELECT /*+ INL_JOIN(twi, twei, te, tes, tqd) */
            twi.wrong_item_id wrongItemId,
            twi.question_id questionId,
            twi.question_number questionNumber,
            twi.exam_item_id examItemId,
            te.exam_id examId,
            te.exam_name examName,
            te.start_date startDate,
            tqd.difficulty
        FROM t_wrong_item twi
        INNER JOIN t_wrong_exam_item twei
            ON twei.wrong_item_id = twi.wrong_item_id
        INNER JOIN t_exam te
            ON te.exam_id = twei.source_id
        INNER JOIN t_exam_school tes
            ON tes.exam_id = te.exam_id
        INNER JOIN t_question_difficulty tqd
            ON twi.question_id = tqd.question_id
        WHERE twi.student_id = #{studentId}
            AND twi.course_id = #{courseId}
            AND tes.school_id = #{schoolId}
            AND twei.source_type = 1
            AND te.exam_type NOT IN (12)
            AND tes.exam_school_status = 3
            <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
                AND te.start_date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="vipVisibilityDate != null">
                AND te.start_date >= #{vipVisibilityDate}
            </if>
            <if test="difficultyLeft != null and difficultyRight != null">
                <choose>
                    <when test="difficultyLeft == 0">
                        AND tqd.difficulty >= 0 AND tqd.difficulty &lt;= #{difficultyRight}
                    </when>
                    <otherwise>
                        AND tqd.difficulty > #{difficultyLeft} AND tqd.difficulty &lt;= #{difficultyRight}
                    </otherwise>
                </choose>
            </if>
    </select>

    <select id="getWrongItemDetail" parameterType="map" resultType="map">
        SELECT student_id studentId,
               wrong_item_id wrongItemId,
               exam_item_id examItemId,
               question_id questionId,
               question_number questionNumber
        FROM t_wrong_item twi
        WHERE student_id = #{studentId}
            AND wrong_item_id = #{wrongItemId}
    </select>


    <select id="getMarkWrongItemDetail" parameterType="map" resultType="map">
        SELECT student_id studentId,
               student_question_mark_wrong_item_id wrongItemId,
               question_id questionId,
               structure_number structureNumber
        FROM t_student_question_mark_wrong_item
        WHERE student_id = #{studentId}
        AND student_question_mark_wrong_item_id = #{studentQuestionMarkWrongItemId}
    </select>

    <delete id="deleteWrongDocumentById" parameterType="long">
        DELETE FROM t_wrong_document
        WHERE wrong_document_id = #{0}
    </delete>

    <select id="getWrongDocumentById" parameterType="long" resultType="map">
        SELECT wrong_document_id wrongDocumentId,
               document_name documentName,
               status,
               link,
               type,
               finish_time finishTime
        FROM t_wrong_document
        WHERE wrong_document_id = #{0}
    </select>
</mapper>