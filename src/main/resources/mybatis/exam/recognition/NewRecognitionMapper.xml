<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.recognition.dao.RecognitionDao">
    <select id="getPaperStructureModifyingCount" parameterType="map" resultType="int">
        select count(1) from t_recognition
        where exam_id = #{examId} and paper_id = #{paperId}
            and recognition_type in (3, 7, 8) and recognition_status = 0;
    </select>

    <update id="updateFailure2Normal" parameterType="map">
        update t_recognition_template
        set recognition_template_status = 4
        where recognition_id = #{recognitionId};
    </update>

    <update id="updateRecognitionStatus">
        update t_recognition
        set recognition_status = #{status}
        where recognition_id = #{recognitionId};
    </update>

    <select id="getRecognitionStudents" resultType="java.util.Map" parameterType="map">
        select student_id   studentId
        from t_recognition_student
        where recognition_id = #{recognitionId}
    </select>
    <select id="getRecognitionTemplates"
            resultType="com.dongni.exam.recognition.bean.dto.RecognitionTemplateDTO">
        select trt.template_code            templateCode,
               trt.template_type            templateType,
               trt.recognition_template_id  recognitionTemplateId,
               tr.recognition_id            rocognitionId,
               tr.recognition_type          recognitionType
        from t_recognition_template trt, t_recognition tr
        where trt.recognition_template_id in
              (<foreach collection="recognitionTemplateIds" separator="," item="item">#{item}</foreach>)
            and tr.recognition_id = trt.recognition_id;
    </select>

    <select id="getRecognition" resultType="com.dongni.exam.recognition.bean.vo.RecognitionVO">
        select recognition_id,
               exam_id,
               paper_id,
               recognition_status
        from t_recognition
        where recognition_id = #{recognitionId}
    </select>

    <select id="getOtherProgressingRecognitionCount" resultType="java.lang.Integer">
        select count(1)
        from t_recognition
        where exam_id = #{examId}
          and paper_id = #{paperId}
          and recognition_id != recognition_id
          and recognition_status = 0;
    </select>

    <select id="getRecognitionQns" resultType="java.lang.Integer">
        select question_number
        from t_recognition_question
        where recognition_id = #{recognitionId};
    </select>
</mapper>