<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dongni.exam.recognition.dao.RecognitionStudentDao">
    <insert id="insertRecognitionCardExtList">
        insert into t_recognition_card_ext
        (bar_num,
         filling_num,
         hand_num,
         hand_name,
         qr_num,
         num_status,
         create_date_time,
         recognition_card_id,
         recognition_id,
         relative_student_id,
         repeat_num
         ) values
         <foreach collection="cards" separator="," item="item">
            (
            #{item.barNum},
            #{item.fillingNum},
            #{item.handNum},
            #{item.handName},
            #{item.qrNum},
            #{item.numStatus},
            now(),
            #{item.recognitionCardId},
            #{item.recognitionId},
            #{item.relativeStudentId},
            #{item.repeatNum}
            )
         </foreach>
        on duplicate key update
        bar_num = values(bar_num),
        filling_num = values(filling_num),
        hand_num = values(hand_num),
        hand_name = values(hand_name),
        qr_num = values(qr_num),
        relative_student_id = values(relative_student_id),
        num_status = values(num_status),
        repeat_num = values(repeat_num);
    </insert>

    <select id="getRecognitionStudents" resultType="com.dongni.exam.recognition.bean.vo.RecognitionStudentVO">
        select trs.student_id                   studentId,
               trs.student_name                 studentName,
               ter.student_exam_num             studentExamNum,
               tr.recognition_id                recognitionId,
               tr.exam_id                       examId,
               tr.paper_id                      paperId,
               trce.filling_num                 fillingNum,
               trce.bar_num                     barNum,
               trce.hand_num                    handNum,
               trce.hand_name                   handName,
               trce.qr_num                      qrNum,
               trce.num_status                  numStatus,
               trce.relative_student_id         relativeStudentId,
               trce.repeat_num                  repeatNum,
               ter.class_id                     classId,
               ter.class_name                   className
        from t_recognition tr
        join t_recognition_student trs on trs.recognition_id = tr.recognition_id
        join t_exam_result ter on ter.exam_id = trs.exam_id and ter.paper_id = trs.paper_id and ter.student_id = trs.student_id
        join t_recognition_card trc on trc.recognition_id = trs.recognition_id and trc.student_id = trs.student_id
                <if test="recognitionTemplateId != null and recognitionTemplateId > 0">
                    and trc.recognition_template_id = #{recognitionTemplateId}
                </if>
        join t_recognition_card_ext trce on trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
                <if test="numStatus != null and numStatus != 0">
                    and trce.num_status = #{numStatus}
                </if>
        where tr.recognition_id = #{recognitionId}
        <if test="searchValue != null and searchValue != ''">
            and (ter.student_name like concat('%', #{searchValue}, '%')
            or ter.student_exam_num like concat('%', #{searchValue}, '%')
            or ter.student_name_pinyin like concat('%', #{searchValue}, '%'))
        </if>
        group by trc.student_id
        order by trce.repeat_num desc, trc.student_id
        <if test="pageSize != null and pageSize > 0">
            limit #{currentIndex}, #{pageSize};
        </if>
    </select>

    <select id="getRecognitionStudentCount" resultType="java.lang.Integer">
        select count(distinct trs.student_id)
        from t_recognition tr
        join t_recognition_student trs on trs.recognition_id = tr.recognition_id
        join t_exam_result ter on ter.exam_id = trs.exam_id and ter.paper_id = trs.paper_id and ter.student_id = trs.student_id
        join t_recognition_card trc on trc.recognition_id = trs.recognition_id and trc.student_id = trs.student_id
        <if test="recognitionTemplateId != null and recognitionTemplateId > 0">
            and trc.recognition_template_id = #{recognitionTemplateId}
        </if>
        join t_recognition_card_ext trce on trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
        <if test="numStatus != null and numStatus != 0">
            and trce.num_status = #{numStatus}
        </if>
        where tr.recognition_id = #{recognitionId}
            <if test="searchValue != null and searchValue != ''">
                and (ter.student_name like concat('%', #{searchValue}, '%')
                    or ter.student_exam_num like concat('%', #{searchValue}, '%')
                    or ter.student_name_pinyin like concat('%', #{searchValue}, '%'))
            </if>
    </select>

    <select id="getRecognitionStudentCards" resultType="com.dongni.exam.recognition.bean.vo.RecognitionStudentVO">
        select
               trc.recognition_card_id,
               trc.recognition_id,
               trc.student_id,
               trce.relative_student_id
        from t_recognition_card trc, t_recognition_card_ext trce
        where trc.recognition_id = #{recognitionId}
        <if test="studentId > 0"> and trc.student_id = #{studentId}</if>
        and trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id;
    </select>

    <select id="getRepeatStudentIds" resultType="java.lang.Long">
        select distinct relative_student_id
        from t_recognition_card_ext
        where recognition_id = #{recognitionId} and repeat_num > 1;
    </select>

    <select id="getRepeatNum" resultType="java.lang.Integer">
        select count(distinct trc.student_id)
        from t_recognition_card_ext trce, t_recognition_card trc
        where trce.recognition_id = #{recognitionId} and trce.relative_student_id = #{relativeStudentId}
            and trc.recognition_id = trce.recognition_id and trc.recognition_card_id = trce.recognition_card_id
    </select>

    <select id="getExamResultList" resultType="com.dongni.exam.plan.bean.bo.ExamResultBO">
        select
            ter.exam_id             examId,
            ter.paper_id            paperId,
            ter.student_id          studentId,
            ter.student_exam_num    studentExamNum,
            ter.student_name        studentName
        from t_recognition tr, t_exam_result ter
        where tr.recognition_id = #{recognitionId}
            and ter.exam_id = tr.exam_id  and ter.paper_id = tr.paper_id;
    </select>

    <select id="getRecognitionStudentCardList"
            resultType="com.dongni.exam.recognition.bean.vo.RecognitionStudentVO">
        select
            trs.student_id                   studentId,
            trs.student_name                 studentName,
            tr.recognition_id                recognitionId,
            tr.exam_id                       examId,
            tr.paper_id                      paperId,
            trc.recognition_card_id          recognitionCardId
            <if test="joinExt > 0">
            ,
            trce.filling_num                 fillingNum,
            trce.bar_num                     barNum,
            trce.hand_num                    handNum,
            trce.hand_name                   handName,
            trce.qr_num                      qrNum,
            trce.num_status                  numStatus,
            trce.relative_student_id         relativeStudentId,
            trce.repeat_num                  repeatNum
            </if>
        from t_recognition tr
        join t_recognition_student trs on trs.recognition_id = tr.recognition_id
        join t_recognition_card trc on trc.recognition_id = trs.recognition_id and trc.student_id = trs.student_id
        <if test="joinExt > 0">
            join t_recognition_card_ext trce on trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
        </if>
        where tr.recognition_id = #{recognitionId}
    </select>

    <select id="getModifyStudentIds" resultType="java.lang.Long">
        select distinct trc.student_id
        from  t_recognition_card trc, t_recognition_card_ext trce
        where trc.recognition_id = #{recognitionId}
            and trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
            and trce.relative_student_id != trc.student_id;
    </select>

    <select id="getNotMatchCount" resultType="java.lang.Integer">
        select count(distinct trc.student_id)
        from t_recognition_card trc
        left join t_answer_card tac on tac.answer_card_id = trc.answer_card_id
        where trc.recognition_id = #{recognitionId}
            and trc.student_id != tac.student_id;
    </select>

    <select id="getCardStudentIds" resultType="java.lang.Long">
        select distinct trc.student_id
        from t_recognition_card trc
        where trc.recognition_id = #{recognitionId};
    </select>

    <select id="getRelativeStudentIds" resultType="java.lang.Long">
        select distinct relative_student_id
        from t_recognition_card_ext trce
        where trce.recognition_id = #{recognitionId} and relative_student_id > 0
            <if test="relativeStudentIds != null and relativeStudentIds.size() > 0">
                and relative_student_id in (<foreach collection="relativeStudentIds" separator="," item="item">#{item}</foreach>)
            </if>;
    </select>

    <select id="getRelativeStudentIdsByNormalStudentIds" resultType="java.lang.Long">
        select distinct relative_student_id
        from  t_recognition_card trc, t_recognition_card_ext trce
        where trc.recognition_id = #{recognitionId}
          and trc.student_id in (<foreach collection="studentIds" separator="," item="item">#{item}</foreach>)
          and trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
    </select>

    <select id="getRecognitionTemplateStuExp"
            resultType="com.dongni.exam.recognition.bean.vo.RecognitionTemplateStuExpVO">
        select trc.recognition_template_id          recognitionTemplateId,
               count(distinct trc.student_id)       expStuExamNumCount
        from t_recognition_card trc, t_recognition_card_ext trce
        where trc.recognition_id = #{recognitionId}
          and trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id
          and trce.num_status = -1
        group by trc.recognition_template_id;
    </select>

    <update id="updateRelativeStudentId">
        update t_recognition_card_ext
        set relative_student_id = #{toStudentId}
        where recognition_id = #{recognitionId}
          and recognition_card_id in (<foreach collection="recognitionCardIds" separator="," item="item">#{item}</foreach>);
    </update>

    <update id="updateRepeatNum">
        update t_recognition_card_ext
        set repeat_num = #{repeatNum}
            <if test="repeatNum == 1">
                ,num_status = 1
            </if>
            <if test="repeatNum > 1">
                ,num_status = -1
            </if>
        where recognition_id = #{recognitionId} and relative_student_id = #{relativeStudentId};
    </update>

    <update id="renewRelativeStudentId">
        update t_recognition_card_ext trce, t_recognition_card trc
        set trce.num_status = 0,
            trce.bar_num = '0',
            trce.filling_num = '0',
            trce.hand_num = '0',
            trce.hand_num = '',
            trce.qr_num = '0',
            trce.relative_student_id = trc.student_id
        where trc.recognition_id = #{recognitionId} and trc.recognition_template_id = #{recognitionTemplateId}
          and trce.recognition_id = trc.recognition_id and trce.recognition_card_id = trc.recognition_card_id;
    </update>
</mapper>