<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="YiqiOtoAreaMapper">

    <!-- 查询一起作业课程 -->
    <select id="select" parameterType="map" resultType="map">
        SELECT yiqi_oto_area_id          yiqiOtoAreaId,
               yiqi_oto_area_name        yiqiOtoAreaName,
               parent_yiqi_oto_area_id   parentYiqiOtoAreaId,
               parent_yiqi_oto_area_name parentYiqiOtoAreaName,
               yiqi_oto_level            yiqiOtoLevel,
               dn_area_id                dnAreaId
        FROM t_yiqi_oto_area
        <where>
            <if test="yiqiOtoAreaId != null and yiqiOtoAreaId != ''">
                AND yiqi_oto_area_id = #{yiqiOtoAreaId}
            </if>
            <if test="withoutDnAreaIdIsNull != null and withoutDnAreaIdIsNull == true">
                AND dn_area_id IS NOT NULL
            </if>
        </where>
    </select>

</mapper>