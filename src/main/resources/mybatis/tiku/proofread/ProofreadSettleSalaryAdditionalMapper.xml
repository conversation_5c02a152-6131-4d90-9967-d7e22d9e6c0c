<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ProofreadSettleSalaryAdditionalMapper">
    <!--插入附加金额列表-->
    <insert id="insertAdditional" parameterType="map">
        INSERT INTO t_proofread_settle_salary_additional
        (
         proofread_settle_salary_id,
         name,
         additional_money,
         comment,
         creator_id,
         creator_name,
         create_date_time,
         modifier_id,
         modifier_name,
         modify_date_time
        ) VALUES
         <foreach collection="list" item="item" separator=",">
             (#{item.proofreadSettleSalaryId},
             #{item.name},
             #{item.additionalMoney},
             #{item.comment},
             #{item.userId},
             #{item.userName},
             #{item.currentTime},
             #{item.userId},
             #{item.userName},
             #{item.currentTime})
         </foreach>
    </insert>

    <!--根据工资单id删除附加金额-->
    <delete id="deleteAdditional" parameterType="map">
        DELETE FROM t_proofread_settle_salary_additional
        WHERE proofread_settle_salary_id = #{proofreadSettleSalaryId}
    </delete>

    <!--根据工资单id获取附加金额-->
    <select id="getAdditional" parameterType="map" resultType="map">
        SELECT name,
               additional_money additionalMoney,
               comment
        FROM t_proofread_settle_salary_additional
        WHERE proofread_settle_salary_id = #{proofreadSettleSalaryId}
    </select>
</mapper>