<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="WrongBookTeacherAssignmentMapper">

    <!--新增错题本选择试题分配老师记录-->
    <insert id="insertWrongBookTeacherAssignment" parameterType="map">
        INSERT INTO t_wrong_book_teacher_assignment (
            wrong_book_id,
            wrong_book_paper_id,
            assignment_teacher_id,
            assignment_teacher_name,
            assignment_status,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES (
            #{wrongBookId},
            #{wrongBookPaperId},
            #{assignmentTeacherId},
            #{assignmentTeacherName},
            #{assignmentStatus},
            #{userId}, #{userName}, #{currentTime},
            #{userId}, #{userName}, #{currentTime}
        )
    </insert>

    <update id="updateWrongBookTeacherAssignmentStatus" parameterType="map">
        UPDATE
            t_wrong_book_teacher_assignment
        SET
            assignment_status = #{assignmentStatus},
            `modifier_id` = #{userId},
            `modifier_name` = #{userName},
            `modify_date_time` = #{currentTime}
        WHERE
            wrong_book_paper_id = #{wrongBookPaperId}
    </update>

    <!--删除错题本选择试题分配老师记录-->
    <delete id="deleteWrongBookTeacherAssignment" parameterType="map">
        DELETE FROM t_wrong_book_teacher_assignment WHERE wrong_book_paper_id = #{wrongBookPaperId}
    </delete>

    <!--获取选择类题的老师-->
    <select id="getWrongBookTeacherAssignmentByWrongBookPaperId" parameterType="map" resultType="map">
        SELECT
            wrong_book_id           wrongBookId,
            wrong_book_paper_id     wrongBookPaperId,
            assignment_teacher_id              assignmentTeacherId,
            assignment_teacher_name            assignmentTeacherName,
            assignment_status       assignmentStatus
        FROM
            t_wrong_book_teacher_assignment
        WHERE wrong_book_paper_id = #{wrongBookPaperId}
    </select>

    <select id="getWrongBookTeacherAssignmentList" parameterType="com.dongni.tiku.wrong.book.bean.param.WrongBookTeacherAssignmentParam"
            resultType="com.dongni.tiku.wrong.book.bean.dto.WrongBookTeacherAssignmentDTO">
        SELECT
            wrong_book_id           wrongBookId,
            wrong_book_paper_id     wrongBookPaperId,
            assignment_teacher_id              assignmentTeacherId,
            assignment_teacher_name            assignmentTeacherName,
            assignment_status       assignmentStatus
        FROM
            t_wrong_book_teacher_assignment
        <where>
            assignment_status = #{assignmentStatus}
        <if test="wrongBookPaperId != null and wrongBookPaperId != ''">
            AND wrong_book_paper_id = #{wrongBookPaperId}
        </if>
        </where>
    </select>
</mapper>