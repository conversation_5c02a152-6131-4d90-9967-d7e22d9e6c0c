<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="TikuCommonMapper">

    <!-- 查询题型映射关系 -->
    <select id="selectQuestionTypeRelation" parameterType="map" resultType="map">
        SELECT
            tqtr.question_type questionType,
            tqt.question_type_name questionTypeName,
            tqt.read_type readType,
            tqt.part_type partType,
            tqur.unit_type unitType,
        <if test="belongType == 1">
            tyqt.course_id courseId,
            tyqt.yiqi_question_type relativeType,
            tyqt.yiqi_question_subtype relativeSubtype
        </if>
        <if test="belongType == 2">
            tjqt.course_id courseId,
            tjqt.jyeoo_question_type relativeType
        </if>
        <if test="belongType == 4">
            txqt.xkw_course_id     xkwCourseId,
            txqt.xkw_course_name   xkwCourseName,
            txqt.xkw_question_type xkwQuestionType,
            txqt.xkw_question_type_name xkwQuestionTypeName,
            txqt.xkw_question_type_parent xkwQuestionTypeParent,
            txqt.xkw_question_type_objective xkwQuestionTypeObjective,
            txqt.xkw_question_type_ordinal xkwQuestionTypeOrdinal,
            txqt.course_id courseId,
            txqt.course_name courseName,
            txqt.xkw_question_type relativeType
        </if>
        FROM t_question_type_relation tqtr
        INNER JOIN t_question_type tqt ON tqt.question_type = tqtr.question_type
        INNER JOIN t_question_unit_relation tqur ON tqur.question_type = tqtr.question_type AND tqur.default = 1
        <if test="belongType == 1">
            INNER JOIN t_yiqi_question_type tyqt ON tqtr.relative_id = tyqt.yiqi_question_type_id
        </if>
        <if test="belongType == 2">
            INNER JOIN t_jyeoo_question_type tjqt ON tqtr.relative_id = tjqt.jyeoo_question_type_id
        </if>
        <if test="belongType == 4">
            INNER JOIN t_xkw_question_type txqt ON txqt.xkw_question_type_id = tqtr.relative_id
        </if>
        WHERE tqtr.belong_type = #{belongType}
        <if test="belongType == 1 and courseIds != null">
            AND tyqt.course_id IN
            <foreach collection="courseIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="belongType == 2 and courseIds != null">
            AND tjqt.course_id IN
            <foreach collection="courseIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="belongType == 4 and courseIds != null">
            AND txqt.course_id IN
            <foreach collection="courseIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="belongType == 1">
            ORDER BY tyqt.yiqi_question_subtype ASC
        </if>
    </select>

    <!-- 查询题型映射关系 - 学科网 -->
    <select id="selectQuestionTypeRelationXkwOuter" parameterType="map" resultType="map">
        SELECT
            txqt.xkw_question_type_id        xkwQuestionTypeId,
            txqt.course_id                   xkwQuestionTypeCourseId,
            txqt.course_name                 xkwQuestionTypeCourseName,
            txqt.xkw_course_id               xkwQuestionTypeXkwCourseId,
            txqt.xkw_course_name             xkwQuestionTypeXkwCourseName,
            txqt.xkw_question_type           xkwQuestionType,
            txqt.xkw_question_type_name      xkwQuestionTypeName,
            txqt.xkw_question_type_objective xkwQuestionTypeObjective,
            txqt.xkw_question_type_parent    xkwQuestionTypeParent,
            txqt.xkw_question_type_ordinal   xkwQuestionTypeOrdinal,
            tqtr.question_type_relation_id   relationQuestionTypeId,
            tqtr.question_type               relationQuestionType,
            tqtr.question_type_name          relationQuestionTypeName,
            tqt.question_type_id             questionTypeId,
            tqt.question_type                questionType,
            tqt.question_type_name           questionTypeName,
            tqtc.question_type_course_id     questionTypeCourseId,
            tqur.qustion_unit_relation_id    questionUnitRelationId,
            tqur.unit_type                   questionUnitRelationUnitType,
            tcut.card_unit_type_id           cardUnitTypeId,
            tcut.unit_type                   unitType,
            tcut.unit_type_name              unitTypeName,
            tcut.read_type                   readType,
            tcut.part_type                   partType
        FROM t_xkw_question_type txqt
        LEFT JOIN t_question_type_relation tqtr ON tqtr.relative_id = txqt.xkw_question_type_id AND belong_type = #{belongTypeXuekewang}
        LEFT JOIN t_question_type tqt ON tqt.question_type = tqtr.question_type AND tqt.creation_type = 0
        LEFT JOIN t_question_type_course tqtc ON tqtc.question_type = tqt.question_type AND tqtc.course_id = txqt.course_id
        LEFT JOIN t_question_unit_relation tqur ON tqur.question_type = tqt.question_type AND tqur.default = 1
        LEFT JOIN t_card_unit_type tcut ON tcut.unit_type = tqur.unit_type
        <where>
            <if test="courseIds != null">
                AND txqt.course_id IN
                <foreach collection="courseIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 查询题型映射关系 五三进校 -->
    <select id="selectQuestionTypeRelationWusanInsideOuter" parameterType="map" resultType="map">
        SELECT
        twqt.wusan_question_type_id      wusanQuestionTypeId,
        twqt.course_id                   wusanQuestionTypeCourseId,
        twqt.course_name                 wusanQuestionTypeCourseName,
        twqt.wusan_level_code            wusanQuestionTypeLevelCode,
        twqt.wusan_level                 wusanQuestionTypeLevel,
        twqt.wusan_subject_code          wusanQuestionTypeSubjectCode,
        twqt.wusan_subject               wusanQuestionTypeSubject,
        twqt.wusan_question_type         wusanQuestionType,
        twqt.wusan_question_type_name    wusanQuestionTypeName,
        tqtr.question_type_relation_id   relationQuestionTypeId,
        tqtr.question_type               relationQuestionType,
        tqtr.question_type_name          relationQuestionTypeName,
        tqt.question_type_id             questionTypeId,
        tqt.question_type                questionType,
        tqt.question_type_name           questionTypeName,
        tqtc.question_type_course_id     questionTypeCourseId,
        tqur.qustion_unit_relation_id    questionUnitRelationId,
        tqur.unit_type                   questionUnitRelationUnitType,
        tcut.card_unit_type_id           cardUnitTypeId,
        tcut.unit_type                   unitType,
        tcut.unit_type_name              unitTypeName,
        tcut.read_type                   readType,
        tcut.part_type                   partType
        FROM t_wusan_question_type twqt
        LEFT JOIN t_question_type_relation tqtr ON tqtr.relative_id = twqt.wusan_question_type_id AND belong_type = #{belongTypeWusanInside}
        LEFT JOIN t_question_type tqt ON tqt.question_type = tqtr.question_type AND tqt.creation_type = 0
        LEFT JOIN t_question_type_course tqtc ON tqtc.question_type = tqt.question_type AND tqtc.course_id = twqt.course_id
        LEFT JOIN t_question_unit_relation tqur ON tqur.question_type = tqt.question_type AND tqur.default = 1
        LEFT JOIN t_card_unit_type tcut ON tcut.unit_type = tqur.unit_type
        <where>
            <if test="courseIds != null">
                AND twqt.course_id IN
                <foreach collection="courseIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!--新增课程题型-->
    <insert id="insertCourseQuestionType" parameterType="map">
        INSERT INTO t_question_type_course
        (
        question_type,
        question_type_name,
        course_id,
        course_name,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES(
         #{questionType},
         #{questionTypeName},
         #{courseId},
         #{courseName},
         #{userId},
         #{userName},
         #{createTime},
         #{userId},
         #{userName},
         #{modifyTime}
        )
        ON DUPLICATE KEY UPDATE
        modifier_id = VALUES (modifier_id),
        modifier_name = VALUES (modifier_name),
        modify_date_time = VALUES (modify_date_time)
    </insert>

    <!-- 插入题型归组 -->
    <insert id="insertQuestionUnit" parameterType="map">
        INSERT INTO t_question_unit_relation (
          question_type,
          question_type_name,
          unit_type,
          `default`,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{questionType},
          #{questionTypeName},
          #{unitType},
          #{default},
          #{userId},
          #{userName},
          #{createTime},
          #{userId},
          #{userName},
          #{modifyTime}
        )
        ON DUPLICATE KEY UPDATE
        modifier_id = VALUES (modifier_id),
        modifier_name = VALUES (modifier_name),
        modify_date_time = VALUES (modify_date_time)
    </insert>

    <!-- 查询题型 -->
    <select id="selectQuestionType" parameterType="map" resultType="map">
        SELECT
        tqt.question_type questionType,
        tqt.question_type_name questionTypeName,
        tqt.read_type readType,
        tqt.part_type partType,
        tqur.unit_type unitType
        FROM t_question_type tqt
        INNER JOIN t_question_unit_relation tqur ON tqt.question_type = tqur.question_type AND tqur.default = 1
        <if test="courseIds != null">
            INNER JOIN t_question_type_course tqtc ON tqt.question_type = tqtc.question_type
            WHERE tqtc.course_id IN
            <foreach collection="courseIds" item="courseId" open="(" close=")" separator=",">
                #{courseId}
            </foreach>
        </if>
        GROUP BY tqt.question_type
        ORDER BY tqt.question_type
    </select>

    <!--  根据名称查询课程题型  -->
    <select id="selectCourseQuestionTypeByName" parameterType="map" resultType="map">
        SELECT
            question_type_course_id questionTypeCourseId,
            question_type questionType,
            question_type_name questionTypeName,
            course_id courseId,
            course_name courseName
        FROM t_question_type_course
        WHERE course_id = #{courseId}
        AND question_type_name = #{questionTypeName}
    </select>

    <!-- 查询题型 -->
    <select id="selectCourseQuestionType" parameterType="map" resultType="map">
        SELECT
        tqtc.question_type_course_id questionTypeCourseId,
        tqtc.question_type questionType,
        tqtc.question_type_name questionTypeName,
        tqtc.create_date_time createTime,
        tqtc.modify_date_time modifyTime,
        tqt.read_type readType
        FROM t_question_type_course tqtc
        INNER JOIN t_question_type tqt ON tqt.question_type = tqtc.question_type
        <where>
            <if test="courseId != null and courseId != ''">
                tqtc.course_id = #{courseId}
            </if>
            <if test="questionTypeCourseId != null and questionTypeCourseId != ''">
                tqtc.question_type_course_id = #{questionTypeCourseId}
            </if>
        </where>

    </select>
    <!--新增课程题型-->
    <insert id="batchInsertCourseQuestionType" parameterType="list" useGeneratedKeys="true" keyColumn="question_type_course_id" keyProperty="questionTypeCourseId">
        INSERT INTO t_question_type_course
        (
            question_type,
            question_type_name,
            course_id,
            course_name,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )VALUES
        <foreach collection="list" separator="," item="item">
            (
            #{item.questionType},
            #{item.questionTypeName},
            #{item.courseId},
            #{item.courseName},
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        modifier_id = VALUES (modifier_id),
        modifier_name = VALUES (modifier_name),
        modify_date_time = VALUES (modify_date_time)
    </insert>

    <!--新增题型-->
    <insert id="insertQuestionType" parameterType="map" useGeneratedKeys="true" keyColumn="question_type_id" keyProperty="questionTypeId">
        INSERT INTO t_question_type
        (
        question_type,
        question_type_name,
        read_type,
        part_type,
        creation_type,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        SELECT
        max(question_type)+1,
        #{questionTypeName},
        #{readType},
        #{partType},
        #{creationType},
        #{userId},
        #{userName},
        now(),
        #{userId},
        #{userName},
        now()
        FROM t_question_type
    </insert>

    <!-- 查询课程的unitType -->
    <select id="selectCardUnitTypeByCourse" parameterType="map" resultType="int">
        SELECT
            tqur.unit_type
        FROM t_question_unit_relation tqur
        INNER JOIN t_question_type_course tqtc ON tqur.question_type = tqtc.question_type
        WHERE tqur.default = 1
        <if test="courseIds != null">
            AND tqtc.course_id IN
            <foreach collection="courseIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY tqur.unit_type
    </select>

    <!-- 查询题型单元 -->
    <select id="selectCardUnitType" parameterType="map" resultType="map">
        SELECT
          tcut.unit_type unitType,
          tcut.unit_type_name unitTypeName,
          tcut.read_type readType,
          tcut.part_type partType,
          tcut.recognition_type recognitionType,
          tcut.sort sort,
          tcut.resizable resizable,
          tqt.question_type questionType,
          tqt.question_type_name questionTypeName,
          tqt.read_type questionTypeReadType,
          tqt.part_type questionTypePartType
        FROM t_card_unit_type tcut
        INNER JOIN t_question_type tqt ON tcut.default_question_type  = tqt.question_type
        <if test="unitTypes != null">
            WHERE tcut.unit_type IN
            <foreach collection="unitTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY tcut.sort ASC
    </select>

    <!-- 获取题型单元 -->
    <select id="selectQuestionUnitRelation" parameterType="map" resultType="map">
        SELECT
          question_type questionType,
          question_type_name questionTypeName,
          unit_type unitType,
          `default` `default`
        FROM t_question_unit_relation
        <if test="questionTypes != null and questionTypes.size > 0">
            WHERE question_type IN
            <foreach collection="questionTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <!--通过yiqiQuestionType yiqiQuestionSubtype courseId 获取 dongniQuestionType dongniQuestionTypeName-->
    <select id="getQuestionTypeByYiqi" parameterType="map" resultType="map">
        SELECT tqtr.`question_type`      questionType,
               tqtr.`question_type_name` questionTypeName,
               tqur.unit_type unitType
        FROM t_yiqi_question_type tyqt
        JOIN t_question_type_relation tqtr
          ON tyqt.`yiqi_question_type_id` = tqtr.relative_id
          AND belong_type = 1
        JOIN t_question_unit_relation tqur
          ON tqur.question_type = tqtr.question_type
         AND tqur.`default` = 1
        WHERE tyqt.`yiqi_question_type` = #{yiqiQuestionType}
          AND tyqt.`yiqi_question_subtype` = #{yiqiQuestionSubtype}
          AND course_id = #{courseId};
    </select>

    <!-- 获取题型信息 -->
    <select id="getYiqiQuestionTypes" parameterType="map" resultType="map">
        SELECT tyqt.course_id              courseId,
               tyqt.course_name            courseName,
               tqtr.`question_type`        questionType,
               tqtr.`question_type_name`   questionTypeName,
               tyqt.yiqi_question_type     yiqiQuestionType,
               tyqt.yiqi_question_subtype  yiqiQuestionSubtype,
               tyqt.yiqi_question_sync_type yiqiQuestionSyncType,
               tqt.read_type               readType,
               tqt.part_type               partType,
               tqur.unit_type              unitType
        FROM t_yiqi_question_type tyqt
        JOIN t_question_type_relation tqtr
          ON tqtr.`belong_type` = 1
         AND tqtr.`relative_id` = tyqt.`yiqi_question_type_id`
        JOIN t_question_type tqt
          ON  tqt.question_type = tqtr.question_type
        JOIN t_question_unit_relation tqur
          ON tqur.question_type = tqtr.question_type
         AND tqur.`default` = 1
        <if test="courseId != null and courseId != ''">
            AND course_id = #{courseId}
        </if>
        GROUP BY tyqt.course_id, tqtr.`question_type`
    </select>

    <!--  根据课程获取一起作业题型  -->
    <select id="getYiqiQuestionTypeByCourse" parameterType="map" resultType="map">
        SELECT
            yiqi_question_type_id yiqiQuestionTypeId,
            yiqi_question_type yiqiQuestionType,
            yiqi_question_type_name yiqiQuestionTypeName,
            yiqi_question_sync_type yiqiQuestionSyncType,
            unit_type unitType
        FROM t_yiqi_question_type
        WHERE course_id = #{courseId}
    </select>

    <!-- 删除答题卡 -->
    <delete id="deleteCourseType" parameterType="map">
        DELETE FROM t_question_type_course
        WHERE
        <if test="questionTypeCourseId != null and questionTypeCourseId != ''">
             question_type_course_id = #{questionTypeCourseId}
        </if>
        <if test="courseId != null and courseId != ''">
             course_id = #{courseId}
        </if>
     </delete>

    <!--根据题型名称获取题型-->
    <select id="getQuestionByName" parameterType="map" resultType="map">
        SELECT
            question_type_id questionTypeId,
            question_type questionType,
            question_type_name questionTypeName
        FROM t_question_type
        WHERE question_type_name = #{questionTypeName}
        LIMIT 1
    </select>

    <!--根据题型名称获取题型-->
    <select id="getQuestionByQuestionTypeId" parameterType="map" resultType="map">
        SELECT
            question_type_id questionTypeId,
            question_type questionType
        FROM t_question_type
        WHERE question_type_id = #{questionTypeId}
    </select>


    <!--根据题型类型 获取题型所属单元-->
    <select id="getQuestionUnitTypeByType" parameterType="map" resultType="map">
        SELECT
        question_type questionType,
        unit_type unitType
        FROM t_question_unit_relation
        WHERE question_type IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and `default` = 1
        group by question_type
    </select>


    <!--根据题型类型 获取题型所属单元-->
    <select id="getQuestionTypeByUnitType" parameterType="map" resultType="map">
        SELECT
        question_type questionType,
        unit_type unitType
        FROM t_question_unit_relation
        WHERE unit_type IN
        <foreach collection="unitTypeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and `default` = 1
        group by unit_type
    </select>

    <!-- 获取课程关联关系 -->
    <select id="getCourseRelation" parameterType="map" resultType="map">
        SELECT
          course_id courseId,
          course_name courseName,
          stage stage,
          relative_key relativeKey,
          belong_type belongType
        FROM t_course_relation
        WHERE belong_type = #{belongType}
    </select>

<!--  根据懂你题型获取一起作业题型  -->
    <select id="getYiqiQuestionTypeByDn" parameterType="map" resultType="map">
        SELECT
             tqtc.course_id              courseId,
             tqtc.course_name            courseName,
             tqt.`question_type`        questionType,
             tqt.`question_type_name`   questionTypeName,
             tyqt.yiqi_question_type     yiqiQuestionType,
             tyqt.yiqi_question_subtype  yiqiQuestionSubtype,
             tyqt.yiqi_question_sync_type yiqiQuestionSyncType,
             tqt.read_type               readType,
             tqt.part_type               partType,
             tqur.unit_type              unitType
        FROM	t_question_type tqt
        INNER JOIN t_question_unit_relation tqur ON tqt.question_type = tqur.question_type AND tqur.`default` = 1
        INNER JOIN t_question_type_course tqtc ON tqt.question_type = tqtc.question_type
        LEFT JOIN t_yiqi_question_type tyqt ON tqur.unit_type = tyqt.unit_type AND tqtc.course_id = tyqt.course_id
        WHERE tqtc.course_id = #{courseId}
        GROUP BY tqt.question_type
    </select>

<!--  获取默认解答题  -->
    <select id="getDefaultSolving" parameterType="map" resultType="map">
        SELECT
            tqtc.question_type questionType,
            tqtc.question_type_name questionTypeName,
            tcut.unit_type unitType
        FROM t_question_type_course tqtc
        INNER JOIN t_question_unit_relation tqur  ON tqur.question_type = tqtc.question_type
        INNER JOIN t_card_unit_type tcut ON tqur.unit_type = tcut.unit_type AND tqur.`default` = 1
        WHERE tqtc.course_id = #{courseId}
        AND tcut.unit_type = 5
        LIMIT 1
    </select>

    <select id="getUnitTypeByQuestionType" resultType="int" parameterType="map">
        select unit_type from t_question_unit_relation where question_type=#{questionType} and `default`=1 limit 1
    </select>

    <select id="getUnitTypeByQuestionTypes" resultType="map" parameterType="map">
        select unit_type unitType, question_type questionType
        from t_question_unit_relation
        where question_type IN
        <foreach collection="questionTypes" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and `default` = 1
    </select>

    <select id="getRecognitionTypeByUnitType" resultType="int" parameterType="map">
       select recognition_type recognitionType from t_card_unit_type where unit_type=#{unitType} limit 1
    </select>


    <!--查询wrongQuestionType-->
    <select id="getWrongQuestionTypeByQuestionType" parameterType="map" resultType="map">
        SELECT
            twqt.wrong_question_type                wrongQuestionType,
            twqt.wrong_question_type_name           wrongQuestionTypeName,
            twqt.unit_type                          unitType,
            twqt.unit_type_name                     unitTypeName,
            tqur.question_type                      questionType,
            tqur.question_type_name                 questionTypeName
        FROM
            t_question_unit_relation tqur
        JOIN t_card_unit_type tcut ON tqur.unit_type = tcut.unit_type AND tqur.`default` = 1
        JOIN t_wrong_question_type_config twqt ON tcut.unit_type = twqt.unit_type
        WHERE
            tqur.question_type IN
        <foreach collection="questionTypeSet" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!--获取推荐数量列表-->
    <select id="getWrongRecommendNumberList" parameterType="map" resultType="map">
        SELECT
            wrong_question_type                     wrongQuestionType,
            wrong_question_type_name                wrongQuestionTypeName,
            wrong_question_recommend_number         wrongQuestionRecommendNumber,
            wrong_question_recommend_number_max     wrongQuestionRecommendNumberMax
        FROM
            t_wrong_question_type_config
        GROUP BY wrong_question_type
    </select>

    <!--查询听力题questionType-->
    <select id="getListenQuestionType" parameterType="map" resultType="Integer">
        SELECT
            question_type questionType
        FROM
            t_question_unit_relation
        WHERE
            question_type_name LIKE '%听%'
          AND `default` = 1;
    </select>

    <select id="getQuestionUnitTypeInfoByType" parameterType="map" resultType="map">
        SELECT
        tqur.question_type  questionType,
        tqur.unit_type      unitType,
        tcut.unit_type_name unitTypeName
        FROM t_question_unit_relation tqur
        JOIN t_card_unit_type tcut ON tqur.unit_type = tcut.unit_type AND tqur.`default` = 1
        WHERE tqur.question_type IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by tqur.question_type
    </select>

    <!--查询推题数量,对于极个别的questionType-->
    <select id="getQuestionTypeRecommendNumberList" parameterType="map" resultType="map">
        SELECT
            question_type                           questionType,
            question_type_name                      questionTypeName,
            wrong_question_recommend_number         wrongQuestionRecommendNumber,
            wrong_question_recommend_number_max     wrongQuestionRecommendNumberMax
        FROM
            t_wrong_question_type_number_config
        <where>
            <if test="questionType != null and questionType != ''">
                question_type = #{questionType}
            </if>
        </where>
    </select>

    <!--根据课程id返回题型信息-举一反三使用-->
    <select id="getQuestionTypeInfoByCourseId" parameterType="map" resultType="map">
        SELECT
             tqtc.course_id              courseId,
             tqtc.course_name            courseName,
             tqt.`question_type`        questionType,
             tqt.`question_type_name`   questionTypeName,
             tqt.read_type               readType,
             tqt.part_type               partType,
             tqur.unit_type              unitType
        FROM	t_question_type tqt
        INNER JOIN t_question_unit_relation tqur ON tqt.question_type = tqur.question_type AND tqur.`default` = 1
        INNER JOIN t_question_type_course tqtc ON tqt.question_type = tqtc.question_type
        WHERE tqtc.course_id = #{courseId}
        GROUP BY tqt.question_type
    </select>

    <!--根据懂你的题型和课程获取其他平台对应的题型-->
    <select id="getXkwQuestionTypeByDn" parameterType="map" resultType="map">
        SELECT txqt.xkw_question_type_id xkwQuestionTypeId,
               txqt.xkw_course_id xkwCourseId,
               txqt.xkw_course_name xkwCourseName,
               txqt.xkw_question_type xkwQuestionType,
               txqt.xkw_question_type_name xkwQuestionTypeName
        FROM t_xkw_question_type txqt
        INNER JOIN t_question_type_relation tqtr ON txqt.xkw_question_type_id = tqtr.relative_id AND tqtr.belong_type = 4
        INNER JOIN t_question_type tqt ON tqtr.question_type = tqt.question_type
        INNER JOIN t_question_type_course tqtc ON tqt.question_type = tqtc.question_type AND tqtc.course_id = txqt.course_id
        WHERE txqt.course_id = #{courseId}
            AND tqt.question_type = #{questionType}
        ORDER BY txqt.xkw_question_type_ordinal
        LIMIT 1
    </select>
</mapper>
