<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="JyeooBookMapper">

    <!-- 教材-章节 -->
    <resultMap id="BookCategory" type="map" extends="JyeooCommonMapper.Book">
        <collection property="category" javaType="list" resultMap="JyeooCommonMapper.Category"/>
    </resultMap>

    <!-- 教材-章节-知识点 -->
    <resultMap id="BookCategoryKnowledge" type="map" extends="JyeooCommonMapper.Book">
        <collection property="category" javaType="list" resultMap="CategoryKnowledge"/>
    </resultMap>

    <!-- 章节-知识点 -->
    <resultMap id="CategoryKnowledge" type="map" extends="JyeooCommonMapper.Category">
        <collection property="knowledge" javaType="list" ofType="map">
            <id column="jyeoo_knowledge_id" property="knowledgeId"/>
            <result column="knowledge_no" property="knowledgeNo"/>
            <result column="knowledge_name" property="knowledgeName"/>
        </collection>
    </resultMap>



    <!-- 更新区域 -->
    <update id="updateRegion" parameterType="map">
        UPDATE t_area
        SET sn_name = #{SName}, area_no = #{ID}
        WHERE area_id = #{AD}
    </update>

    <!-- 更新区域 -->
    <update id="updateOtherRegion" parameterType="map">
        UPDATE t_area
        SET sn_name = #{SName}, area_no = #{ID}
        WHERE area_no IS NULL
        AND area_name = #{SName}
    </update>

    <!-- 更新区域 -->
    <update id="updateOtherRegion2" parameterType="map">
        UPDATE t_area
        SET sn_name = #{SName}, area_no = #{ID}
        WHERE area_no IS NULL
        AND area_name LIKE CONCAT(#{SName2},'%')
    </update>


    <!-- 获取教材版本 -->
    <select id="getBookEdition" parameterType="map" resultType="map">
        SELECT
        tb.edition_type editionType,
        tb.edition_name editionName
        FROM t_jyeoo_book tb
        WHERE tb.course_id = #{courseId}
        GROUP BY tb.edition_type
    </select>

    <!-- 获取教材-章节 -->
    <select id="getBookCategory" parameterType="map" resultMap="BookCategory">
        SELECT
        tb.jyeoo_book_id,
        tb.book_code,
        tb.book_name,
        tb.book_desc,
        tb.course_id,
        tb.course_code,
        tb.grade_type,
        tb.term_type,
        tb.edition_type,
        tb.grade_name,
        tb.term_name,
        tb.edition_name,
        tc.jyeoo_category_id,
        tc.category_code,
        tc.category_name,
        tc.category_desc,
        tc.category_seq,
        tc.node_id,
        tc.parent_node_id,
        tc.node_sort,
        tc.node_type,
        tc.node_level
        FROM t_jyeoo_book tb
        LEFT JOIN t_jyeoo_category tc ON tb.jyeoo_book_id = tc.jyeoo_book_id
        WHERE tb.course_id = #{courseId}
        <if test="editionType != null and editionType != ''">
            AND tb.edition_type = #{editionType}
        </if>
    </select>


    <!-- 获取教材-章节 -->
    <select id="getBookCategoryKnowledge" parameterType="map" resultMap="BookCategoryKnowledge">
        SELECT
        tb.jyeoo_book_id,
        tb.book_code,
        tb.book_name,
        tb.book_desc,
        tb.course_id,
        tb.course_code,
        tb.grade_type,
        tb.term_type,
        tb.edition_type,
        tb.grade_name,
        tb.term_name,
        tb.edition_name,
        tc.jyeoo_category_id,
        tc.category_code,
        tc.category_name,
        tc.category_desc,
        tc.category_seq,
        tc.node_id,
        tc.parent_node_id,
        tc.node_sort,
        tc.node_type,
        tc.node_level,
        tkc.jyeoo_knowledge_id,
        tkc.knowledge_no,
        tkc.knowledge_name
        FROM t_jyeoo_book tb
        LEFT JOIN t_jyeoo_category tc ON tb.jyeoo_book_id = tc.jyeoo_book_id
        LEFT JOIN t_jyeoo_knowledge_category tkc ON tc.jyeoo_category_id = tkc.jyeoo_category_id
        WHERE tb.course_id = #{courseId}
        <if test="editionType != null and editionType != ''">
            AND tb.edition_type = #{editionType}
        </if>
    </select>



    <!-- 更新t_book 课程ID -->
    <update id="updateBook">
    UPDATE t_book tb, t_course tc
    SET tb.course_id = tc.course_id
    WHERE tb.course_code = tc.course_code
    </update>

    <!--更新t_category 课程ID -->
    <update id="updateCategory">
        UPDATE t_category tcb, t_course tc
    SET tcb.course_id = tc.course_id
    WHERE tcb.course_code = tc.course_code
    </update>


    <!-- 更新t_knowledge_category 教材 category_id -->
    <update id="updateKnowledge">
        UPDATE t_category tcb, t_knowledge_category tkc
    SET tkc.category_id = tcb.category_id
    WHERE tcb.category_code = tkc.category_code
    </update>


    <!-- 更新t_knowledge 课程course_id -->
    <update id="updateKnowledgeCourse">
        UPDATE t_course tc, t_knowledge tk
        SET tk.course_id = tc.course_id
        WHERE tk.course_code = tc.course_code
    </update>


    <!-- 更新知识点的解析 -->
    <update id="updateKnowledgeDesc">
        UPDATE t_knowledge
        SET knowledge_describle = #{Desc}
        WHERE knowledge_no = #{No}
        AND course_code = #{courseCode}
    </update>


    <!-- 获取教材-章节 -->
    <select id="exportBookCategory" parameterType="map" resultType="map">
        SELECT
        tb.book_name bookName,
        tb.grade_name gradeName,
        tb.term_name termName,
        tb.edition_name editionName,
        tc.category_name categoryName
        FROM t_book tb
        LEFT JOIN t_category tc ON tb.book_id = tc.book_id
        ORDER BY tb.course_id ASC
    </select>

</mapper>
