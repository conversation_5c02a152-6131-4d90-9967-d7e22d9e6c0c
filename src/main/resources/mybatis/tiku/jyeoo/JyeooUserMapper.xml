<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="JyeooUserMapper">

    <!-- 查询菁优网用户 -->
    <select id="getJyeooUserInUserIds" parameterType="map" resultType="long">
        SELECT user_id
        FROM t_jyeoo_user
        WHERE user_id IN
        <foreach collection="userIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND `status` = 1
    </select>

    <!-- 查询菁优网用户 -->
    <select id="getJyeooUser" parameterType="map" resultType="map">
        SELECT
        name,
        role,
        sex,
        id  jyeooUserId,
        pwd jyeooUserPassword
        FROM t_jyeoo_user
        WHERE user_id = #{userId}
        AND `status` = 1
    </select>

    <!-- 获取JYEOO 用户密码 -->
    <select id="getPwd" parameterType="string" resultType="string">
         SELECT pwd
         FROM t_jyeoo_user
         WHERE user_id = #{userId}
         AND `status` = 1
    </select>

    <!-- 注册JYEOO 用户 -->
    <insert id="insertJyeooUser" parameterType="map">
        INSERT INTO t_jyeoo_user(
        user_id,
        `id`,
        `name`,
        `pwd`,
        school_id,
        school_name,
        creator_id, creator_name,create_date_time,
        modifier_id, modifier_name,modify_date_time
        )
        VALUES(
        #{dongniUserId},
        #{UserID},
        #{UserID},
        #{UserPwd},
        #{schoolId},
        #{schoolName},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
    </insert>

</mapper>