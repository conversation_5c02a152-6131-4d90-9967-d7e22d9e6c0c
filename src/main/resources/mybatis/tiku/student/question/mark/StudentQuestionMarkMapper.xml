<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="StudentQuestionMarkMapper">
    <insert id="insertStudentQuestionMark"
            parameterType="com.dongni.tiku.student.question.mark.bean.entity.StudentQuestionMarkEntity"
            useGeneratedKeys="true" keyProperty="studentQuestionMarkId" keyColumn="student_question_mark_id">
        INSERT INTO t_student_question_mark(student_id, student_name,
                                            school_id, source_type,
                                            source_id, name, remark,
                                            creator_id, creator_name, create_date_time,
                                            modifier_id, modifier_name, modify_date_time)
        VALUE (#{studentId}, #{studentName},
               #{schoolId}, #{sourceType},
               #{sourceId}, #{name}, #{remark},
               #{creatorId}, #{creatorName}, #{createDateTime},
               #{modifierId}, #{modifierName}, #{modifyDateTime})
    </insert>

    <resultMap id="wrongQuestionGroupMap" type="com.dongni.exam.wrong.bean.vo.WrongQuestionGroupVO">
        <id property="groupId" column="student_question_mark_id"/>
        <result property="groupName" column="name"/>
        <collection property="wrongQuestionItems" ofType="com.dongni.exam.wrong.bean.vo.WrongQuestionItemVO"
                    resultMap="wrongQuestionItemMap"/>
    </resultMap>

    <resultMap id="wrongQuestionItemMap" type="com.dongni.exam.wrong.bean.vo.WrongQuestionItemVO">
        <!-- 共有字段 -->
        <result property="wrongItemQuestionFrom" column="wrong_item_question_from"/>
        <result property="belongType" column="belong_type"/>
        <result property="questionId" column="question_id"/>
        <result property="courseId" column="course_id"/>
        <result property="difficulty" column="difficulty"/>
        <result property="createDateTime" column="create_date_time"/>

        <!-- 其他错题字段 -->
        <result property="studentQuestionMarkId" column="student_question_mark_id"/>
        <result property="studentQuestionMarkName" column="name"/>
        <result property="studentQuestionMarkItemId" column="student_question_mark_item_id"/>
        <result property="studentQuestionMarkWrongItemId" column="student_question_mark_wrong_item_id"/>
    </resultMap>

    <select id="getStudentQuestionMarkAndItemByStudent"
            parameterType="map"
            resultMap="wrongQuestionGroupMap">
        select mark.name,
               wrongItem.course_id courseId,
               wrongItem.structure_number,
               wrongItem.difficulty,
               wrongItem.course_id,
               wrongItem.question_id,
               wrongItem.belong_type,
               wrongItem.student_question_mark_id,
               wrongItem.student_question_mark_item_id,
               wrongItem.source_question_id,
               wrongItem.student_question_mark_wrong_item_id,
               wrongItem.create_date_time
        FROM t_student_question_mark mark
        INNER JOIN t_student_question_mark_wrong_item wrongItem on mark.student_question_mark_id = wrongItem.student_question_mark_id
        WHERE mark.student_id = #{studentId}
        <if test="difficultyLeft != null and difficultyRight != null">
            <choose>
                <when test="difficultyLeft == 0">
                    AND wrongItem.difficulty >= 0 AND wrongItem.difficulty &lt;= #{difficultyRight}
                </when>
                <otherwise>
                    AND wrongItem.difficulty > #{difficultyLeft} AND wrongItem.difficulty &lt;= #{difficultyRight}
                </otherwise>
            </choose>
        </if>
        <if test="startDate != null and startDate != '' and endDate != null and endDate != ''">
            AND wrongItem.create_date_time BETWEEN #{startDate} AND #{endDate}
        </if>
        AND wrongItem.course_id = #{courseId}
        ORDER BY mark.create_date_time DESC
    </select>


    <delete id="deleteStudentQuestionMarkById" parameterType="java.lang.Long">
        DELETE FROM t_student_question_mark
        WHERE student_question_mark_id = #{studentQuestionMarkId}
    </delete>

    <!-- 更新标记状态 -->
    <update id="updateMarkStatus">
        UPDATE t_student_question_mark_item
        SET
            mark_status = #{markStatus},
            mark_time = #{markTime},
            modify_date_time = NOW()
        WHERE student_question_mark_item_id = #{itemId}
    </update>

    <select id="getStudentQuestionMark" parameterType="long"
            resultType="com.dongni.tiku.student.question.mark.bean.entity.StudentQuestionMarkEntity">
        SELECT student_question_mark_id studentQuestionMarkId,
               student_id studentId,
               student_name studentName,
               school_id schoolId,
               source_type sourceType,
               source_id sourceId,
               name,
               remark
        FROM t_student_question_mark
        WHERE student_question_mark_id = #{0}
    </select>

    <update id="addAccessNum" parameterType="long">
        UPDATE t_student_question_mark
        SET access_num = access_num + 1
        WHERE student_question_mark_id = #{studentQuestionMarkId}
    </update>

    <select id="getStudentQuestionMarkEffectiveness" parameterType="long"
            resultType="com.dongni.tiku.student.question.mark.bean.dto.StudentQuestionMarkEffectivenessDTO">
        SELECT tsqm.student_id studentId,
               tsqm.source_id sourceId,
               tsqm.source_type sourceType,
               tsqm.remark,
               tsqm.access_num accessNum,
               tsqm.create_date_time createDateTime,
               tsqm.student_question_mark_id studentQuestionMarkId
        FROM t_wrong_book twb
        INNER JOIN t_wrong_book_student_file twbsf ON twb.wrong_book_id = twbsf.wrong_book_id
        INNER JOIN t_student_question_mark tsqm
            ON tsqm.student_id = twbsf.student_id
            AND tsqm.source_id = twbsf.wrong_book_id
            AND tsqm.source_type IN (1, 2)
        WHERE twb.wrong_book_id IN
            <foreach collection="list" item="wrongBookId" open="(" separator="," close=")">
                #{wrongBookId}
            </foreach>
    </select>
</mapper>