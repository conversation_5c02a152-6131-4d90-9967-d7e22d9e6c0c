<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="StudentQuestionMarkItemMapper">
    <insert id="insertStudentQuestionMarkItem"
            parameterType="com.dongni.tiku.student.question.mark.bean.entity.StudentQuestionMarkItemEntity">
        INSERT INTO t_student_question_mark_item(
        student_question_mark_id, question_source_type,
        source_question_id, question_id, belong_type, course_id,
        structure_number, group_type, difficulty, mark_status,
        creator_id, creator_name, create_date_time,
        modifier_id, modifier_name, modify_date_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.studentQuestionMarkId}, #{item.questionSourceType},
            #{item.sourceQuestionId}, #{item.questionId}, #{item.belongType}, #{item.courseId},
            #{item.structureNumber}, #{item.groupType}, #{item.difficulty}, 1,
            #{item.creatorId}, #{item.creatorName}, #{item.createDateTime},
            #{item.modifierId}, #{item.modifierName}, #{item.modifyDateTime})
        </foreach>
    </insert>

    <select id="getSimilarQuestions" parameterType="com.dongni.tiku.student.question.mark.bean.param.SimilarQuestionParam"
            resultType="com.dongni.tiku.student.question.mark.bean.dto.StudentQuestionMarkItemSimpleDTO">
        SELECT
            tsqmwi.belong_type belongType,
            tsqmwi.question_id questionId,
            tsqmwi.difficulty difficulty,
            tsqmwi.source_question_id sourceQuestionId,
            tsqm.student_question_mark_id studentQuestionMarkId,
            tsqm.name studentQuestionMarkName,
            tsqmwi.student_question_mark_wrong_item_id studentQuestionMarkWrongItemId,
            tsqmwi.student_question_mark_item_id studentQuestionMarkItemId,
            tsqmwi.create_date_time createDateTime
        FROM
            t_student_question_mark_wrong_item tsqmwi
            INNER JOIN t_student_question_mark tsqm
                ON tsqm.student_question_mark_id = tsqmwi.student_question_mark_id
        WHERE
            tsqmwi.student_id = #{studentId}
          AND tsqmwi.source_question_id IN
            <foreach collection="questionIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>

    <resultMap id="BaseResultMap" type="com.dongni.tiku.student.question.mark.bean.entity.StudentQuestionMarkItemEntity">
        <id column="student_question_mark_item_id" property="studentQuestionMarkItemId" jdbcType="BIGINT"/>
        <result column="student_question_mark_id" property="studentQuestionMarkId" jdbcType="BIGINT"/>
        <result column="question_source_type" property="questionSourceType" jdbcType="TINYINT"/>
        <result column="source_question_id" property="sourceQuestionId" jdbcType="VARCHAR"/>
        <result column="question_id" property="questionId" jdbcType="VARCHAR"/>
        <result column="belong_type" property="belongType" jdbcType="TINYINT"/>
        <result column="course_id" property="courseId" jdbcType="BIGINT"/>
        <result column="structure_number" property="structureNumber" jdbcType="VARCHAR"/>
        <result column="group_type" property="groupType" jdbcType="TINYINT"/>
        <result column="difficulty" property="difficulty" jdbcType="DECIMAL"/>
        <result column="mark_status" property="markStatus" jdbcType="TINYINT"/>
        <result column="mark_time" property="markTime" jdbcType="TIMESTAMP"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="creator_name" property="creatorName" jdbcType="VARCHAR"/>
        <result column="create_date_time" property="createDateTime" jdbcType="TIMESTAMP"/>
        <result column="modifier_id" property="modifierId" jdbcType="BIGINT"/>
        <result column="modifier_name" property="modifierName" jdbcType="VARCHAR"/>
        <result column="modify_date_time" property="modifyDateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <select id="selectStudentQuestionMarkItemById" parameterType="map" resultMap="BaseResultMap">
        SELECT
            student_question_mark_item_id,
            student_question_mark_id,
            question_source_type,
            source_question_id,
            question_id,
            belong_type,
            course_id,
            structure_number,
            group_type,
            difficulty,
            mark_status,
            mark_time,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        FROM t_student_question_mark_item
        WHERE student_question_mark_id = #{studentQuestionMarkId}
        <if test="groupType != null">
            AND group_type = #{groupType}
        </if>
    </select>


    <select id="selectStudentQuestionMarkItemByStudent" parameterType="map" resultMap="BaseResultMap">
        SELECT
        student_question_mark_item_id,
        student_question_mark_id,
        question_source_type,
        source_question_id,
        question_id,
        belong_type,
        course_id,
        structure_number,
        group_type,
        difficulty,
        mark_status,
        mark_time,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        FROM t_student_question_mark_item
        WHERE student_question_mark_id = #{studentQuestionMarkId}
        <if test="groupType != null">
            AND group_type = #{groupType}
        </if>
    </select>

    <update id="batchUpdateMarkStatusByIds" parameterType="map">
        UPDATE t_student_question_mark_item
        SET
        mark_status = #{markStatus},
        mark_time = NOW()
        WHERE student_question_mark_item_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


    <select id="getMarkIemByIds" parameterType="map" resultType="com.dongni.tiku.student.question.mark.bean.entity.StudentQuestionMarkItemEntity">
        select
        student_question_mark_id studentQuestionMarkId,
        student_question_mark_item_id studentQuestionMarkItemId,
        source_question_id sourceQuestionId,
        question_id questionId,
        question_source_type questionSourceType,
        belong_type belongType,
        structure_number structureNumber,
        course_id courseId,
        difficulty
        from t_student_question_mark_item
        WHERE student_question_mark_item_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>



    <select id="getMarkWrongItemDetail" parameterType="map" resultType="map">
        SELECT student_id studentId,
               belong_type belongType,
               course_id courseId,
               student_question_mark_wrong_item_id wrongItemId,
               question_id questionId,
               structure_number structureNumber
        FROM t_student_question_mark_wrong_item
        WHERE student_id = #{studentId}
          AND student_question_mark_wrong_item_id = #{studentQuestionMarkWrongItemId}
    </select>

    <select id="getMarkItemDetail" parameterType="map" resultType="map">
        SELECT
               student_question_mark_item_id ItemId,
               question_id questionId,
               structure_number structureNumber,
               course_id courseId,
               belong_type belongType
        FROM t_student_question_mark_item
        WHERE  student_question_mark_item_id = #{studentQuestionMarkItemId}
    </select>

</mapper>