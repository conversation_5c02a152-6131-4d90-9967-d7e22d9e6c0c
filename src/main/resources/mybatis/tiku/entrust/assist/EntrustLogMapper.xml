<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="EntrustLogMapper">

    <!--日志表-->

    <!--插入 返回entrustLogId-->
    <insert id="insertEntrustLog" parameterType="map"
            useGeneratedKeys="true" keyColumn="entrust_log_id" keyProperty="entrustLogId">
        INSERT INTO t_entrust_log (
            entrust_id,
            log_level,
            entrust_status, entrust_status_name,
            entrust_status_old, entrust_status_name_old,
            operation_name,
            comment,
            primary_params_json,
            person_in_charge_user_id, person_in_charge_user_name, person_in_charge_user_type,
            start_timestamp_second, timeout_seconds, warning_seconds,
            finished_timestamp_second, is_timeout, cost_seconds,
            skip_second_of_day_start, skip_second_of_day_end,
            timeout_timestamp_second, warning_timestamp_second,
            creator_id, creator_name, create_date_time,
            modifier_id, modifier_name, modify_date_time
        ) VALUES (
            #{entrustId},
            #{logLevel},
            #{entrustStatus}, #{entrustStatusName},
            #{entrustStatusOld}, #{entrustStatusNameOld},
            #{operationName},
            #{comment},
            #{primaryParamsJson},
            #{personInChargeUserId}, #{personInChargeUserName}, #{personInChargeUserType},
            #{startTimestampSecond}, #{timeoutSeconds}, #{warningSeconds},
            #{finishedTimestampSecond}, #{isTimeout}, #{costSeconds},
            #{skipSecondOfDayStart}, #{skipSecondOfDayEnd},
            #{timeoutTimestampSecond}, #{warningTimestampSecond},
            #{userId}, #{userName}, #{currentTime},
            #{userId}, #{userName}, #{currentTime}
        )
    </insert>

    <select id="getEntrustLogCount" parameterType="map" resultType="long">
        SELECT COUNT(*) `count`
        FROM t_entrust_log
        <where>
            <if test="entrustId != null and entrustId != ''">
                AND entrust_id = #{entrustId}
            </if>
            <if test="logLevel != null and logLevel != ''">
                AND log_level = #{logLevel}
            </if>
            <if test="entrustLogId != null and entrustLogId != ''">
                AND entrust_log_id = #{entrustLogId}
            </if>
        </where>
    </select>

    <select id="getEntrustLog" parameterType="map" resultType="map">
        SELECT
            entrust_log_id             entrustLogId,
            entrust_id                 entrustId,
            log_level                  logLevel,
            entrust_status             entrustStatus,
            entrust_status_name        entrustStatusName,
            entrust_status_old         entrustStatusOld,
            entrust_status_name_old    entrustStatusNameOld,
            operation_name             operationName,
            `comment`                  `comment`,
            <if test="queryPrimaryParamsJson != null and queryPrimaryParamsJson == true">
                primary_params_json        primaryParamsJson,
            </if>
            person_in_charge_user_id   personInChargeUserId,
            person_in_charge_user_name personInChargeUserName,
            person_in_charge_user_type personInChargeUserType,
            start_timestamp_second     startTimestampSecond,
            timeout_seconds            timeoutSeconds,
            warning_seconds            warningSeconds,
            finished_timestamp_second  finishedTimestampSecond,
            is_timeout                 isTimeout,
            cost_seconds               costSeconds,
            skip_second_of_day_start   skipSecondOfDayStart,
            skip_second_of_day_end     skipSecondOfDayEnd,
            timeout_timestamp_second   timeoutTimestampSecond,
            warning_timestamp_second   warningTimestampSecond,
            creator_id                 creatorId,
            creator_name               creatorName,
            create_date_time           createDateTime,
            modifier_id                modifierId,
            modifier_name              modifierName,
            modify_date_time           modifyDateTime
        FROM t_entrust_log
        <where>
            <if test="entrustId != null and entrustId != ''">
                AND entrust_id = #{entrustId}
            </if>
            <if test="logLevel != null and logLevel != ''">
                AND log_level = #{logLevel}
            </if>
            <if test="entrustLogId != null and entrustLogId != ''">
                AND entrust_log_id = #{entrustLogId}
            </if>
        </where>
        ORDER BY entrust_log_id
        <if test="pageSize !=null and currentIndex !=null">
            LIMIT #{currentIndex,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
        </if>
    </select>

    <!--状态结束时更新/更换了录题人员视为该负责人员的状态已经完结 完成时间 是否超时 消耗时间-->
    <update id="updateForFinished" parameterType="map">
        UPDATE t_entrust_log
        SET finished_timestamp_second = #{finishedTimestampSecond},
            is_timeout                = #{isTimeout},
            cost_seconds              = #{costSeconds},
            <if test="comment != null and comment != ''">
                comment = #{comment},
            </if>
            modifier_id               = #{userId},
            modifier_name             = #{userName},
            modify_date_time          = #{currentTime}
        WHERE entrust_log_id = #{entrustLogId}
    </update>

    <select id="getEntrustLogForStatistics" parameterType="map" resultType="map">
        SELECT
               te.entrust_id                        entrustId,
               te.school_id                         schoolId,
               te.school_name                       schoolName,
               te.create_date_time                  entrustCreateDateTime,
               tel.entrust_log_id                   entrustLogId,
               tel.entrust_status                   entrustStatus,
               tel.start_timestamp_second           startTimestampSecond,
               tel.timeout_timestamp_second         timeoutTimestampSecond,
               tel.finished_timestamp_second        finishedTimestampSecond,
               tel.is_timeout                       isTimout,
               tel.timeout_seconds                  timeoutSeconds,
               tel.cost_seconds                     costSeconds,
               tel.skip_second_of_day_start         skipSecondOfDayStart,
               tel.skip_second_of_day_end           skipSecondOfDayEnd,
               tel.creator_id                       creatorId,
               tel.creator_name                     creatorName,
               tel.create_date_time                 createDateTime,
               UNIX_TIMESTAMP(tel.create_date_time) createTimestampSecond
        FROM t_entrust te
        JOIN t_entrust_log tel
            ON tel.entrust_id = te.entrust_id
           AND tel.timeout_seconds > 0
        <where>
            <if test="entrustId != null">
                AND tel.entrust_id = #{entrustId}
            </if>
            <if test="schoolIdList != null and schoolIdList.size() > 0">
                AND te.school_id IN
                <foreach collection="schoolIdList" item="schoolId" open="(" close=")" separator=",">
                    #{schoolId}
                </foreach>
            </if>
            <if test="startTime != null and startTime != ''">
                AND te.create_date_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND te.create_date_time &lt;= #{endTime}
            </if>
        </where>
    </select>

</mapper>
