<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="EntrustQueryMapper">

    <resultMap id="entrustDetail" type="map">
        <id     column="entrustId"            property="entrustId"/>
        <result column="entrustPaperType"     property="entrustPaperType"/>
        <result column="entrustBusinessType"  property="entrustBusinessType"/>
        <result column="entrustStatus"        property="entrustStatus"/>
        <result column="entrustStatusOld"     property="entrustStatusOld"/>
        <result column="entrustLevel"         property="entrustLevel"/>
        <result column="entrusterUserId"      property="entrusterUserId"/>
        <result column="entrusterUserName"    property="entrusterUserName"/>
        <result column="paperMarkType"        property="paperMarkType"/>
        <result column="answerPaperId"        property="answerPaperId"/>
        <result column="paperPaperId"         property="paperPaperId"/>
        <result column="paperYear"            property="paperYear"/>
        <result column="fullMark"             property="fullMark"/>
        <result column="areaIds"              property="areaIds"/>
        <result column="areaId"               property="areaId"/>
        <result column="schoolId"             property="schoolId"/>
        <result column="schoolName"           property="schoolName"/>
        <result column="stage"                property="stage"/>
        <result column="gradeType"            property="gradeType"/>
        <result column="gradeName"            property="gradeName"/>
        <result column="courseId"             property="courseId"/>
        <result column="courseName"           property="courseName"/>
        <result column="paperType"            property="paperType"/>
        <result column="comment"              property="comment"/>
        <result column="docRequireType"       property="docRequireType"/>
        <result column="docExplainType"       property="docExplainType"/>
        <result column="docCopyableType"      property="docCopyableType"/>
        <result column="yiqiOtoAreaId"        property="yiqiOtoAreaId"/>
        <result column="startTimestampSecond" property="startTimestampSecond"/>
        <result column="entrustMarkStatus"    property="entrustMarkStatus"/>
        <result column="creatorId"            property="creatorId"/>
        <result column="creatorName"          property="creatorName"/>
        <result column="createDateTime"       property="createDateTime"/>
        <result column="modifierId"           property="modifierId"/>
        <result column="modifierName"         property="modifierName"/>
        <result column="modifyDateTime"       property="modifyDateTime"/>
        <result column="spotCheckStatus"      property="spotCheckStatus"/>
        <result column="submitVersion"        property="submitVersion"/>
        <collection property="files" javaType="list" ofType="map">
            <id     column="entrustUploadId"    property="entrustUploadId"/>
            <result column="fileUrl"            property="fileUrl"/>
            <result column="fileName"           property="fileName"/>
            <result column="fileCreateDateTime" property="createDateTime"/>
        </collection>
    </resultMap>

    <!-- 委托记录 获取委托录题详细信息 -->
    <select id="getEntrustDetail" parameterType="map" resultMap="entrustDetail">
        SELECT
        te.entrust_id             entrustId,
        te.entrust_paper_type     entrustPaperType,
        te.entrust_business_type  entrustBusinessType,
        te.entrust_status         entrustStatus,
        te.entrust_status_old     entrustStatusOld,
        te.entrust_level          entrustLevel,
        te.entruster_user_id      entrusterUserId,
        te.entruster_user_name    entrusterUserName,
        te.paper_mark_type        paperMarkType,
        te.answer_paper_id        answerPaperId,
        te.paper_paper_id         paperPaperId,
        te.paper_year             paperYear,
        te.full_mark              fullMark,
        te.area_ids               areaIds,
        te.area_id                areaId,
        te.school_id              schoolId,
        te.school_name            schoolName,
        te.stage                  stage,
        te.grade_type             gradeType,
        te.grade_name             gradeName,
        te.course_id              courseId,
        te.course_name            courseName,
        te.paper_type             paperType,
        te.comment                `comment`,
        te.doc_require_type       docRequireType,
        te.doc_explain_type       docExplainType,
        te.doc_copyable_type      docCopyableType,
        te.yiqi_oto_area_id       yiqiOtoAreaId,
        te.start_timestamp_second startTimestampSecond,
        te.entrust_mark_status    entrustMarkStatus,
        te.creator_id             creatorId,
        te.creator_name           creatorName,
        te.create_date_time       createDateTime,
        te.modifier_id            modifierId,
        te.modifier_name          modifierName,
        te.modify_date_time       modifyDateTime,
        te.spot_check_status      spotCheckStatus,
        te.submit_version         submitVersion,
        teu.entrust_upload_id     entrustUploadId,
        teu.file_url              fileUrl,
        teu.file_name             fileName,
        teu.create_date_time      fileCreateDateTime
        FROM t_entrust te
        LEFT JOIN t_entrust_upload teu
            ON teu.entrust_id = te.entrust_id
           AND teu.file_status = #{fileNotDeleted}
        WHERE te.entrust_status != 0
        <if test="entrustId != null and entrustId != ''">
            AND te.entrust_id = #{entrustId}
        </if>
        <if test="answerPaperId != null and answerPaperId != ''">
            AND te.answer_paper_id = #{answerPaperId}
        </if>
    </select>

    <!--  获取自动指派中的委托数量  -->
    <select id="getEntrustAutoAppointCount" parameterType="map" resultType="long">
        SELECT course_id
        FROM t_entrust
        WHERE entrust_status = #{entrustStatus}
        <if test="entrustUrgencyList != null and entrustUrgencyList.size() > 0">
            AND entrust_urgency IN
            <foreach collection="entrustUrgencyList" item="entrustUrgency" open="(" separator="," close=")">
                #{entrustUrgency}
            </foreach>
        </if>
    </select>

    <!--  获取自动指派中的委托id-->
    <select id="getEntrustAutoAppoint" parameterType="map" resultType="map">
        SELECT entrust_id entrustId,
               entrust_urgency entrustUrgency,
               entrust_mark_status entrustMarkStatus,
               answer_paper_id answerPaperId,
               deliver_type deliverType,
               course_id courseId
        FROM t_entrust
        WHERE entrust_status = #{entrustStatus}
        <if test="entrustUrgencyList != null and entrustUrgencyList.size() > 0">
            AND entrust_urgency IN
            <foreach collection="entrustUrgencyList" item="entrustUrgency" open="(" separator="," close=")">
                #{entrustUrgency}
            </foreach>
        </if>
    </select>

    <!-- 查询entrust  params: [entrustId] [answerPaperId] [entrustStatusList]-->
    <select id="getEntrust" parameterType="map" resultType="map">
        SELECT
        entrust_id             entrustId,
        entrust_paper_type     entrustPaperType,
        entrust_business_type  entrustBusinessType,
        paper_mark_type        paperMarkType,
        entrust_status         entrustStatus,
        entrust_status_old     entrustStatusOld,
        entrust_level          entrustLevel,
        entruster_user_id      entrusterUserId,
        entruster_user_name    entrusterUserName,
        answer_paper_id        answerPaperId,
        paper_paper_id         paperPaperId,
        paper_year             paperYear,
        full_mark              fullMark,
        area_ids               areaIds,
        area_id                areaId,
        school_id              schoolId,
        school_name            schoolName,
        stage                  stage,
        grade_type             gradeType,
        grade_name             gradeName,
        course_id              courseId,
        course_name            courseName,
        paper_type             paperType,
        `comment`              `comment`,
        doc_require_type       docRequireType,
        doc_explain_type       docExplainType,
        doc_copyable_type      docCopyableType,
        start_timestamp_second startTimestampSecond,
        spot_check_status      spotCheckStatus,
        submit_version         submitVersion,
        entrust_mark_status    entrustMarkStatus,
        entrust_urgency        entrustUrgency,
        deliver_type           deliverType,
        wrong_book_level       wrongBookLevel,
        creator_id             creatorId,
        creator_name           creatorName,
        create_date_time       createDateTime,
        modifier_id            modifierId,
        modifier_name          modifierName,
        modify_date_time       modifyDateTime,
        temp_old_entrust_id    tempOldEntrustId
        FROM t_entrust
        <where>
            <if test="entrustId != null">
                AND entrust_id = #{entrustId}
            </if>
            <if test="entrustIdList != null and entrustIdList.size() > 0">
                AND entrust_id IN
                <foreach collection="entrustIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="answerPaperId != null">
                AND answer_paper_id = #{answerPaperId}
            </if>
            <if test="paperMarkType != null and paperMarkType != ''">
                AND paper_mark_type = #{paperMarkType}
            </if>
            <if test="entrustStatusList != null">
                AND entrust_status IN
                <foreach collection="entrustStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!--  根据试卷ID获取委托  -->
    <select id="getEntrustByPaperId" parameterType="map" resultType="map">
        SELECT
            entrust_id entrustId,
            entrust_status entrustStatus
        FROM t_entrust
        WHERE answer_paper_id = #{paperId}
    </select>

    <!--委托人委托列表-->
    <select id="getEntrustListForEntruster" parameterType="map" resultType="map">
        SELECT
            te.entrust_id                   entrustId,
            te.entrust_status               entrustStatus,
            te.entrust_status_old           entrustStatusOld,
            te.course_id                    courseId,
            te.course_name                  courseName,
            te.entrust_business_type        entrustBusinessType,
            te.entruster_user_id            entrusterUserId,
            te.entruster_user_name          entrusterUserName,
            te.entrust_mark_status          entrustMarkStatus,
            te.create_date_time             createDateTime,
            te.entrust_urgency              entrustUrgency,
            te.overtime_return              overtimeReturn,
            te.deliver_type                 deliverType,
            te.wrong_book_level             wrongBookLevel
        FROM t_entrust te
        WHERE te.entruster_user_id = #{entrusterUserId}
            AND te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="search != null and search != ''">
            AND te.entrust_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
        </if>
        ORDER BY te.entrust_id DESC
        <if test="sortField != null and sortType != null">
            , te.${sortField} ${sortType}
        </if>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--委托人委托列表总数-->
    <select id="getEntrustListForEntrusterCount" parameterType="map" resultType="int">
        SELECT COUNT(*) `count`
        FROM t_entrust te
        WHERE te.entruster_user_id = #{entrusterUserId}
        AND te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="search != null and search != ''">
            AND te.entrust_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
        </if>
    </select>

    <!--录题人员的列表-->
    <select id="getEntrustListForTypist" parameterType="map" resultType="map">
        SELECT
        te.entrust_id                   entrustId,
        te.entrust_status               entrustStatus,
        te.entrust_status_old           entrustStatusOld,
        te.school_id                    schoolId,
        te.course_id                    courseId,
        te.course_name                  courseName,
        te.entrust_business_type        entrustBusinessType,
        te.entrust_mark_status          entrustMarkStatus,
        tet.timeout_timestamp_second    timeoutTimestampSecond,
        tel.timeout_timestamp_second    requireFinishTimestampSecond,
        te.create_date_time             createDateTime,
        te.entrust_urgency              entrustUrgency,
        te.overtime_return              overtimeReturn,
        te.deliver_type                 deliverType,
        te.wrong_book_level             wrongBookLevel,
        <!--需求: 按加急字段升序排序，当委托是不校对且无加急时放在无加急的后面-->
        IF(te.entrust_mark_status = 2 AND te.entrust_urgency = 400, 410, te.entrust_urgency) customSortField
        FROM t_entrust te
        JOIN t_entrust_typist tetypist
            ON tetypist.entrust_id = te.entrust_id
            AND tetypist.typist_user_id = #{typistUserId}
            AND tetypist.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_entrust_timeout tet
            ON tet.entrust_id = te.entrust_id
        LEFT JOIN t_entrust_log tel
            ON tel.entrust_log_id = tet.entrust_log_id
        WHERE te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="search != null and search != ''">
            AND te.entrust_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
        </if>
        <if test="entrustUrgency != null">
            AND te.entrust_urgency = #{entrustUrgency}
        </if>
        ORDER BY customSortField, wrong_book_level IS NULL, wrong_book_level
        <if test="sortField != null and sortType != null">
            , te.${sortField} ${sortType}
        </if>, te.entrust_id DESC
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--录题人员的列表数量-->
    <select id="getEntrustListForTypistCount" parameterType="map" resultType="int">
        SELECT COUNT(*) `count`
        FROM t_entrust te
        JOIN t_entrust_typist tetypist
            ON tetypist.entrust_id = te.entrust_id
            AND tetypist.typist_user_id = #{typistUserId}
            AND tetypist.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_entrust_timeout tet
            ON tet.entrust_id = te.entrust_id
        LEFT JOIN t_entrust_log tel
            ON tel.entrust_log_id = tet.entrust_log_id
        WHERE te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="search != null and search != ''">
            AND te.entrust_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
        </if>
        <if test="entrustUrgency != null">
            AND te.entrust_urgency = #{entrustUrgency}
        </if>
    </select>

    <!--题库管理员委托列表 通过先查出来的entrustIdList再查具体信息-->
    <select id="getEntrustListForSubjectAdmin" parameterType="map" resultType="map">
        SELECT
            te.entrust_id                entrustId,
            te.entrust_status            entrustStatus,
            te.entrust_status_old        entrustStatusOld,
            te.school_id                 schoolId,
            te.school_name               schoolName,
            te.course_id                 courseId,
            te.course_name               courseName,
            te.entrust_business_type     entrustBusinessType,
            te.entruster_user_id         entrusterUserId,
            te.entruster_user_name       entrusterUserName,
            te.create_date_time          createDateTime,
            te.entrust_mark_status       entrustMarkStatus,
            te.entrust_urgency           entrustUrgency,
            te.overtime_return           overtimeReturn,
            te.deliver_type              deliverType,
            te.wrong_book_level          wrongBookLevel,
            <!--需求: 按加急字段升序排序，当委托是不校对且无加急时放在无加急的后面-->
            IF(te.entrust_mark_status = 2 AND te.entrust_urgency = 400, 410, te.entrust_urgency) customSortField,
            tet.timeout_timestamp_second timeoutTimestampSecond,
            tetypist.typist_id           typistId,
            tetypist.typist_user_id      typistUserId,
            tetypist.typist_user_name    typistUserName,
            te.proofread_status             proofreadStatus,
            tpt.part_time_proofreader_user_id partTimeProofreaderUserId,
            tpt.part_time_proofreader_user_name partTimeProofreaderUserName
        FROM t_entrust te
        LEFT JOIN t_entrust_timeout tet
          ON tet.entrust_id = te.entrust_id
        LEFT JOIN t_entrust_typist tetypist
          ON tetypist.entrust_id = te.entrust_id
         AND tetypist.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_proofread_task tpt
            ON te.entrust_id = tpt.entrust_id
            AND tpt.task_status != 4
        WHERE te.entrust_id IN
        <foreach collection="entrustIdList" item="entrustId" separator="," open="(" close=")">
            #{entrustId}
        </foreach>
        GROUP BY te.entrust_id
        ORDER BY customSortField, wrong_book_level IS NULL, wrong_book_level
        <if test="sortField != null and sortType != null">
            , te.${sortField} ${sortType}
        </if>, te.entrust_id DESC
    </select>

    <!-- 委托idList -->
    <select id="getEntrustIdListForSubjectAdmin" parameterType="map" resultType="long">
        SELECT te.entrust_id entrustId,
            <!--需求: 按加急字段升序排序，当委托是不校对且无加急时放在无加急的后面-->
            IF(te.entrust_mark_status = 2 AND te.entrust_urgency = 400, 410, te.entrust_urgency) customSortField
        FROM t_entrust te
        LEFT JOIN t_entrust_typist tet
            ON tet.entrust_id = te.entrust_id
            AND tet.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_proofread_task tpt
            ON tpt.entrust_id = te.entrust_id
            AND tpt.task_status != 4
        WHERE te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND te.school_id = #{schoolId}
        </if>
        <if test="search != null and search != '' and searchField != null and searchField != ''">
            <choose>
                <when test='searchField == "partTimeProofreaderUserName"'>
                    AND tpt.part_time_proofreader_user_name = #{search}
                </when>
                <when test='searchField == "entrusterUserName"'>
                    AND te.entruster_user_name = #{search}
                </when>
                <when test='searchField == "typistUserName"'>
                    AND tet.typist_user_name = #{search}
                </when>
                <when test='searchField == "entrustId"'>
                    AND te.entrust_id = #{search}
                </when>
            </choose>
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 1">
            AND te.proofread_status IN (0, 1)
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 2">
            AND te.proofread_status = 2
        </if>
        <if test="entrustUrgency != null and entrustUrgency !=''">
            AND te.entrust_urgency = #{entrustUrgency}
        </if>
        <if test="deliverType != null and deliverType != ''">
            AND te.deliver_type = #{deliverType}
        </if>
        <if test="createDateLeft != null and createDateLeft != '' and createDateRight != null and createDateRight !=''">
            AND te.create_date_time BETWEEN #{createDateLeft} AND #{createDateRight}
        </if>
        ORDER BY customSortField, wrong_book_level IS NULL, wrong_book_level
        <if test="sortField != null and sortType != null">
            , te.${sortField} ${sortType}
        </if>, te.entrust_id DESC
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--题库管理员委托列表总数-->
    <select id="getEntrustListForSubjectAdminCount" parameterType="map" resultType="int">
        SELECT COUNT(*) `count`
        FROM t_entrust te
        LEFT JOIN t_entrust_typist tet
            ON tet.entrust_id = te.entrust_id
            AND tet.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_proofread_task tpt
            ON tpt.entrust_id = te.entrust_id
            AND tpt.task_status != 4
        WHERE te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND te.school_id = #{schoolId}
        </if>
        <if test="search != null and search != '' and searchField != null and searchField != ''">
            <choose>
                <when test='searchField == "partTimeProofreaderUserName"'>
                    AND tpt.part_time_proofreader_user_name = #{search}
                </when>
                <when test='searchField == "entrusterUserName"'>
                    AND te.entruster_user_name = #{search}
                </when>
                <when test='searchField == "typistUserName"'>
                    AND tet.typist_user_name = #{search}
                </when>
                <when test='searchField == "entrustId"'>
                    AND te.entrust_id = #{search}
                </when>
            </choose>
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 1">
            AND te.proofread_status IN (0, 1)
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 2">
            AND te.proofread_status = 2
        </if>
        <if test="entrustUrgency != null and entrustUrgency !=''">
            AND te.entrust_urgency = #{entrustUrgency}
        </if>
        <if test="deliverType != null and deliverType != ''">
            AND te.deliver_type = #{deliverType}
        </if>
        <if test="createDateLeft != null and createDateLeft != '' and createDateRight != null and createDateRight !=''">
            AND te.create_date_time BETWEEN #{createDateLeft} AND #{createDateRight}
        </if>
    </select>


    <!--获取委托的answerPaperId 用于产品顾问/运营人员委托列表模糊搜索使用-->
    <select id="getAnswerPaperId" parameterType="map" resultType="long">
        SELECT te.answer_paper_id answerPaperId
        FROM t_entrust te
        WHERE te.answer_paper_id > 0
          AND te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND te.school_id = #{schoolId}
        </if>
        <if test="schoolIdList != null">
            AND te.school_id IN
            <foreach collection="schoolIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--运营管理人员委托列表 通过先查出来的entrustIdList再查具体信息-->
    <select id="getEntrustListForManager" parameterType="map" resultType="map">
        SELECT
        te.entrust_id                entrustId,
        te.entrust_status            entrustStatus,
        te.entrust_status_old        entrustStatusOld,
        te.school_id                 schoolId,
        te.school_name               schoolName,
        te.course_id                 courseId,
        te.course_name               courseName,
        te.entrust_business_type     entrustBusinessType,
        te.answer_paper_id           answerPaperId,
        te.entruster_user_id         entrusterUserId,
        te.entruster_user_name       entrusterUserName,
        te.create_date_time          createDateTime,
        te.start_timestamp_second    startTimestampSecond,
        te.entrust_mark_status         entrustMarkStatus,
        te.entrust_urgency           entrustUrgency,
        te.overtime_return           overtimeReturn,
        te.deliver_type              deliverType,
        te.wrong_book_level          wrongBookLevel,
        <!--需求: 按加急字段升序排序，当委托是不校对且无加急时放在无加急的后面-->
        IF(te.entrust_mark_status = 2 AND te.entrust_urgency = 400, 410, te.entrust_urgency) customSortField,
        tet.timeout_timestamp_second timeoutTimestampSecond,
        tel.person_in_charge_user_id personInChargeUserId,
        tel.person_in_charge_user_name personInChargeUserName,
        tetypist.typist_id           typistId,
        tetypist.typist_user_id      typistUserId,
        tetypist.typist_user_name    typistUserName,
        te.proofread_status proofreadStatus,
        tpt.part_time_proofreader_user_id partTimeProofreaderUserId,
        tpt.part_time_proofreader_user_name partTimeProofreaderUserName
        FROM t_entrust te
        LEFT JOIN t_entrust_timeout tet
            ON tet.entrust_id = te.entrust_id
        LEFT JOIN t_entrust_log tel
            ON tel.entrust_log_id = tet.entrust_log_id
        LEFT JOIN t_entrust_typist tetypist
            ON tetypist.entrust_id = te.entrust_id
            AND tetypist.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_proofread_task tpt
            ON te.entrust_id = tpt.entrust_id
            AND tpt.task_status != 4
        WHERE te.entrust_id IN
        <foreach collection="entrustIdList" item="entrustId" separator="," open="(" close=")">
            #{entrustId}
        </foreach>
        GROUP BY te.entrust_id
        ORDER BY customSortField, wrong_book_level IS NULL, wrong_book_level
        <if test="sortField != null and sortType != null">
            , te.${sortField} ${sortType}
        </if>, te.entrust_id DESC
    </select>

    <!--运营管理人员委托idList-->
    <select id="getEntrustIdListForManager" parameterType="map" resultType="long">
        SELECT te.entrust_id entrustId,
               <!--需求: 按加急字段升序排序，当委托是不校对且无加急时放在无加急的后面-->
               IF(te.entrust_mark_status = 2 AND te.entrust_urgency = 400, 410, te.entrust_urgency) customSortField
        FROM t_entrust te
        LEFT JOIN t_entrust_typist tet
            ON tet.entrust_id = te.entrust_id
            AND tet.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_proofread_task tpt
            ON tpt.entrust_id = te.entrust_id
            AND tpt.task_status != 4
        WHERE te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND te.school_id = #{schoolId}
        </if>
        <if test="schoolIdList != null">
            AND te.school_id IN
            <foreach collection="schoolIdList" item="schoolIdListItem" separator="," open="(" close=")">
                #{schoolIdListItem}
            </foreach>
        </if>
        <if test="search != null and search != '' and searchField != null and searchField != ''">
            <choose>
                <when test='searchField == "partTimeProofreaderUserName"'>
                    AND tpt.part_time_proofreader_user_name = #{search}
                </when>
                <when test='searchField == "entrusterUserName"'>
                    AND te.entruster_user_name = #{search}
                </when>
                <when test='searchField == "typistUserName"'>
                    AND tet.typist_user_name = #{search}
                </when>
                <when test='searchField == "entrustId"'>
                    AND te.entrust_id = #{search}
                </when>
            </choose>
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 1">
            AND te.proofread_status IN (0, 1)
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 2">
            AND te.proofread_status = 2
        </if>
        <if test="entrustUrgency != null and entrustUrgency !=''">
            AND te.entrust_urgency = #{entrustUrgency}
        </if>
        <if test="deliverType != null and deliverType != ''">
            AND te.deliver_type = #{deliverType}
        </if>
        <if test="startTimeLeft != null and startTimeLeft != '' and startTimeRight != null and startTimeRight !=''">
            AND te.start_timestamp_second BETWEEN #{startTimeLeft} AND #{startTimeRight}
        </if>
        ORDER BY customSortField, wrong_book_level IS NULL, wrong_book_level
        <if test="sortField != null and sortType != null">
            , te.${sortField} ${sortType}
        </if>, te.entrust_id DESC
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--运营管理人员委托列表总数-->
    <select id="getEntrustListForManagerCount" parameterType="map" resultType="int">
        SELECT COUNT(*) `count`
        FROM t_entrust te
        LEFT JOIN t_entrust_typist tet
            ON tet.entrust_id = te.entrust_id
            AND tet.status != #{entrustTypistStatusInvalid}
        LEFT JOIN t_proofread_task tpt
            ON tpt.entrust_id = te.entrust_id
            AND tpt.task_status != 4
        WHERE te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND te.school_id = #{schoolId}
        </if>
        <if test="schoolIdList != null">
            AND te.school_id IN
            <foreach collection="schoolIdList" item="schoolIdListItem" separator="," open="(" close=")">
                #{schoolIdListItem}
            </foreach>
        </if>
        <if test="search != null and search != '' and searchField != null and searchField != ''">
            <choose>
                <when test='searchField == "partTimeProofreaderUserName"'>
                    AND tpt.part_time_proofreader_user_name = #{search}
                </when>
                <when test='searchField == "entrusterUserName"'>
                    AND te.entruster_user_name = #{search}
                </when>
                <when test='searchField == "typistUserName"'>
                    AND tet.typist_user_name = #{search}
                </when>
                <when test='searchField == "entrustId"'>
                    AND te.entrust_id = #{search}
                </when>
            </choose>
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 1">
            AND te.proofread_status IN (0, 1)
        </if>
        <if test="proofreadStatus != null and proofreadStatus == 2">
            AND te.proofread_status = 2
        </if>
        <if test="entrustUrgency != null and entrustUrgency !=''">
            AND te.entrust_urgency = #{entrustUrgency}
        </if>
        <if test="deliverType != null and deliverType != ''">
            AND te.deliver_type = #{deliverType}
        </if>
        <if test="startTimeLeft != null and startTimeLeft != '' and startTimeRight != null and startTimeRight !=''">
            AND te.start_timestamp_second BETWEEN #{startTimeLeft} AND #{startTimeRight}
        </if>
    </select>

    <!-- 校对中获取下一个处理的委托 -->
    <select id="getNextEntrustForManagerForProcessing" parameterType="map" resultType="map">
        SELECT
        te.entrust_id                entrustId,
        te.create_date_time          createDateTime,
        te.start_timestamp_second    startTimestampSecond,
        te.entrust_mark_status       entrustMarkStatus
        FROM t_entrust te
        WHERE te.entrust_status IN
        <foreach collection="queryEntrustStatusList" item="queryEntrustStatus" separator="," open="(" close=")">
            #{queryEntrustStatus}
        </foreach>
        <if test="courseId != null and courseId != ''">
            AND te.course_id = #{courseId}
        </if>
        <if test="schoolId != null and schoolId != ''">
            AND te.school_id = #{schoolId}
        </if>
        <if test="schoolIdList != null">
            AND te.school_id IN
            <foreach collection="schoolIdList" item="schoolIdListItem" separator="," open="(" close=")">
                #{schoolIdListItem}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            AND (
            te.entrust_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            OR te.answer_paper_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            OR te.entruster_user_name LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            <if test="answerPaperIdList != null">
                OR te.answer_paper_id IN
                <foreach collection="answerPaperIdList" item="answerPaperId" separator="," open="(" close=")">
                    #{answerPaperId}
                </foreach>
            </if>
            )
        </if>
        AND (
            entrust_mark_status > #{entrustMarkStatus}
            OR
            (
               <choose>
                   <when test="sortType == 'ASC' or sortType == 'asc'">
                       entrust_mark_status = #{entrustMarkStatus}
                       AND ${sortField} >= #{sortFieldValue}
                       AND entrust_id > #{entrustId}
                   </when>
                   <otherwise>
                       entrust_mark_status = #{entrustMarkStatus}
                       AND ${sortField} &lt;= #{sortFieldValue}
                       AND entrust_id &lt; #{entrustId}
                   </otherwise>
               </choose>
            )
        )
        ORDER BY te.entrust_mark_status, ${sortField} ${sortType}, entrust_id ${sortType}
        LIMIT 1
    </select>

    <select id="getEntrustIdListForPush" parameterType="map" resultType="long">
        SELECT
               entrust_id entrustId
        FROM t_entrust
        WHERE entrust_status = #{entrustStatusYiqiQuestionPushing}
    </select>

    <select id="getEntrustForPullFrom17" parameterType="map" resultType="map">
        SELECT
               entrust_id entrustId
        FROM t_entrust
        WHERE entrust_status IN
        <foreach collection="entrustStatusList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
          AND paper_mark_type = #{paperMarkType}
        <if test="entrustId != null and entrustId != ''">
            AND entrust_id = #{entrustId}
        </if>
    </select>

    <select id="getEntrustForPullFromDongni" parameterType="map" resultType="map">
        SELECT
               entrust_id entrustId
        FROM t_entrust
        WHERE entrust_status = #{entrustStatus}
          AND paper_mark_type != #{notPaperMarkType}
        <if test="entrustId != null and entrustId != ''">
            AND entrust_id = #{entrustId}
        </if>
    </select>


    <!--根据answerPaperId批量获取委托-->
    <select id="getEntrustByPaperIds" parameterType="map" resultType="map">
        SELECT
            entrust_id      entrustId,
            answer_paper_id paperId,
            entrust_status  entrustStatus
        FROM t_entrust
        WHERE
            answer_paper_id IN
        <foreach collection="paperIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!--查询关联委托 目标委托-->
    <select id="getRelationEntrust4Input" parameterType="map" resultType="map">
        SELECT
            entrust_id          entrustId,
            entrust_status      entrustStatus,
            answer_paper_id     answerPaperId,
            paper_paper_id      paperPaperId,
            school_id           schoolId,
            school_name         schoolName,
            stage               stage,
            grade_type          gradeType,
            grade_name          gradeName,
            course_id           courseId,
            course_name         courseName,
            entruster_user_id         entrusterUserId,
            entruster_user_name       entrusterUserName,
            create_date_time          createDateTime,
            start_timestamp_second    startTimestampSecond
        FROM
            t_entrust
        WHERE
            course_id = #{courseId}
          <if test="schoolId != null and schoolId != ''">
              AND school_id = #{schoolId}
          </if>
        AND answer_paper_id IS NULL
        AND entrust_status IN
            <foreach collection="validQueryStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        <if test="search != null and search != ''">
            AND (
            entrust_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            OR answer_paper_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            OR entruster_user_name LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            <if test="answerPaperIdList != null">
                OR answer_paper_id IN
                <foreach collection="answerPaperIdList" item="answerPaperId" separator="," open="(" close=")">
                    #{answerPaperId}
                </foreach>
            </if>
            )
        </if>
    </select>

    <select id="getRelationEntrust4Check" parameterType="map" resultType="map">
        SELECT
            entrust_id                  entrustId,
            entrust_status              entrustStatus,
            answer_paper_id             answerPaperId,
            paper_paper_id              paperPaperId,
            school_id                   schoolId,
            school_name                 schoolName,
            stage                       stage,
            grade_type                  gradeType,
            grade_name                  gradeName,
            course_id                   courseId,
            course_name                 courseName,
            entruster_user_id           entrusterUserId,
            entruster_user_name         entrusterUserName,
            create_date_time            createDateTime,
            start_timestamp_second      startTimestampSecond
        FROM
        t_entrust
        WHERE
        course_id = #{courseId}
        <if test="schoolId != null and schoolId != ''">
            AND school_id = #{schoolId}
        </if>
        AND entrust_status IN
        <foreach collection="validExamPaperQueryStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="search != null and search != ''">
            AND (
            entrust_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            OR answer_paper_id LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            OR entruster_user_name LIKE concat('%',#{search,jdbcType=VARCHAR},'%')
            <if test="answerPaperIdList != null">
                OR answer_paper_id IN
                <foreach collection="answerPaperIdList" item="answerPaperId" separator="," open="(" close=")">
                    #{answerPaperId}
                </foreach>
            </if>
            )
        </if>
    </select>

    <select id="getWrongBookUrgencyNotComplete" resultType="map">
        SELECT entrust_id entrustId,
               answer_paper_id paperId,
               deliver_type deliverType
        FROM t_entrust
        WHERE entrust_urgency = 200
        AND entrust_status != 9
    </select>

    <select id="getWrongBookUrgencyComplete" parameterType="date" resultType="map">
        SELECT entrust_id entrustId,
               answer_paper_id paperId,
               deliver_type deliverType
        FROM t_entrust
        WHERE entrust_urgency = 200
        AND entrust_status = 9
        AND modify_date_time >= #{0}
    </select>

    <select id="checkWrongBookStatusByEntrustIds" parameterType="long" resultType="long">
        SELECT te.entrust_id entrustId
        FROM t_entrust te
        INNER JOIN t_wrong_book_paper twbp
            ON te.answer_paper_id = twbp.paper_id
        INNER JOIN t_wrong_book twb
            ON twbp.wrong_book_id = twb.wrong_book_id
        WHERE te.entrust_id IN
            <foreach collection="list" item="entrustId" open="(" separator="," close=")">
                #{entrustId}
            </foreach>
            AND twb.wrong_book_status = 3
            AND te.answer_paper_id IS NOT NULL
    </select>
</mapper>
