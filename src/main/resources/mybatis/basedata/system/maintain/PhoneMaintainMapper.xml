<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="PhoneMaintainMapper">

    <!-- 获取需数据处理的表 -->
    <select id="getBaseDataNeedEncryptData" parameterType="map" resultType="map">
        SELECT
        cc.`table_name` "table",
        cc.`column_name` "field",
        (SELECT `column_name` FROM INFORMATION_SCHEMA.`KEY_COLUMN_USAGE` WHERE `table_name`=cc.`table_name` AND `constraint_name`='PRIMARY') "id"
        FROM information_schema.`COLUMNS` cc
        WHERE (
        COLUMN_NAME LIKE '%phone'
        OR COLUMN_NAME LIKE '%mobile'
        OR COLUMN_NAME LIKE 'account_name'
        OR COLUMN_NAME LIKE '%identity_card_no')
        AND TABLE_SCHEMA = 'base_data_test'
    </select>


    <!-- 获取执行过的记录 -->
    <select id="getPhoneHistoryCount" parameterType="map" resultType="int">
      SELECT COUNT(*)
      FROM t_phone_history
      WHERE `table_name` = #{table}
      AND field_name = #{field}
    </select>

    <!-- 获取表字段的值-->
    <select id="getTableField" parameterType="map" resultType="map">
        SELECT
        ${id} idv,
        ${field} fd
        FROM ${table}
        WHERE ${field} IS NOT NULL
    </select>

    <!-- 数据备份 -->
    <insert id="insertPhoneHistory2" parameterType="map">
        INSERT INTO t_phone_history(
        `table_name`,
        table_id,
        field_name,
        field_value,
        creator_id,
        creator_name,
        create_date_time
        )
        SELECT
        "${table}",
        ${id},
        "${field}",
        ${field},
        "${userId}",
        "${userName}",
        "${currentTime}"
        FROM ${table}
    </insert>

    <!-- 数据备份 -->
    <insert id="insertPhoneHistory" parameterType="collection">
        INSERT IGNORE INTO t_phone_history(
        `table_name`,
        table_id,
        field_name,
        field_value,
        field_src,
        field_hide,
        creator_id,
        creator_name,
        create_date_time
        )VALUES
        <foreach collection="collection" item="item" separator=",">
            (
            #{item.table},
            #{item.idv},
            #{item.field},
            #{item.fdAes},
            #{item.fdSrc},
            #{item.fd},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
    </insert>

    <!-- 更新表字段的值 -->
    <update id="updateTableField" parameterType="map">
        UPDATE ${table}
        SET
        ${field} = #{fd},
        ${field}_aes = #{fdAes}
        WHERE ${id} = #{idv}
    </update>

    <!-- 更新表字段的值 -->
    <update id="updateTableHiddenField" parameterType="map">
        UPDATE ${table}
        SET
        ${field} = #{fd}
        WHERE ${id} = #{idv}
    </update>

    <!-- 更新字段的值 -->
    <update id="updateTableField2" parameterType="map">
        UPDATE t_phone_history t1, ${table} t2
        SET
        t2.${field} = t1.field_src,
        t2.${field}_aes = t1.field_value
        WHERE t1.`table_name` = "${table}"
        AND t1.table_id = t2.${id}
    </update>

</mapper>