<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="PaperReadAccountMapper">
    <resultMap id="commonResultMap" type="com.dongni.basedata.paper.read.bean.model.PaperReadAccount">
        <id column="paper_read_account_id" property="paperReadAccountId"/>
        <result column="paper_read_group_id" property="paperReadGroupId"/>
        <result column="account_id" property="accountId"/>
        <result column="account_name" property="accountName"/>
        <result column="user_id" property="userId"/>
        <result column="bind_name" property="bindName"/>
        <result column="bind_phone_num" property="bindPhoneNum"/>
        <result column="bind_phone_num_aes" property="bindPhoneNumAes"/>
        <result column="paper_read_account_type" property="paperReadAccountType"/>
    </resultMap>

    <resultMap id="paperReadAccountDtoResultMap" type="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountDto">
        <id column="paper_read_account_id" property="paperReadAccountId"/>
        <result column="paper_read_group_set_id" property="paperReadGroupSetId"/>
        <result column="set_name" property="setName"/>
        <result column="paper_read_group_id" property="paperReadGroupId"/>
        <result column="group_name" property="groupName"/>
        <result column="course_id" property="courseId"/>
        <result column="course_name" property="courseName"/>
        <result column="grade_type" property="gradeType"/>
        <result column="bind_name" property="bindName"/>
        <result column="bind_phone_num" property="bindPhoneNum"/>
        <result column="bind_phone_num_aes" property="bindPhoneNumAes"/>
        <result column="paper_read_account_type" property="paperReadAccountType"/>
        <result column="account_id" property="accountId"/>
        <result column="user_id" property="userId"/>
        <result column="account_name" property="accountName"/>
    </resultMap>

    <resultMap id="paperReadGroupTeacherVOResultMap" type="com.dongni.exam.common.mark.vo.PaperReadGroupTeacherVO">
        <result column="paper_read_account_id" property="paperReadAccountId"/>
        <result column="account_name" property="accountName"/>
        <result column="teacher_id" property="teacherId"/>
        <result column="teacher_name" property="virtualTeacherName"/>
        <result column="bind_name" property="teacherName"/>
        <result column="bind_phone_num_aes" property="teacherPhone"/>
        <result column="paper_read_group_id" property="paperReadGroupId"/>
    </resultMap>

    <resultMap id="VirtualTeacherInfoResultMap" type="com.dongni.basedata.paper.read.bean.bo.VirtualTeacherInfo">
        <result column="teacher_id" property="teacherId"/>
        <result column="account_name" property="accountName"/>
        <result column="paper_read_account_id" property="paperReadAccountId"/>
        <result column="paper_read_account_type" property="paperReadAccountType"/>
    </resultMap>

    <sql id="commonField">
        tpra.paper_read_account_id,
        tpra.paper_read_group_id,
        tpra.account_id,
        tpra.account_name,
        tpra.user_id,
        tpra.bind_name,
        tpra.bind_phone_num,
        tpra.bind_phone_num_aes,
        tpra.paper_read_account_type
    </sql>

    <!--批量插入-->
    <insert id="batchInsert" parameterType="com.dongni.basedata.paper.read.bean.model.PaperReadAccount" useGeneratedKeys="true"
            keyColumn="paper_read_account_id" keyProperty="paperReadAccountId">
        INSERT INTO t_paper_read_account
        (
         paper_read_group_id,
         account_id,
         account_name,
         user_id,
         bind_name,
         bind_phone_num,
         bind_phone_num_aes,
         paper_read_account_type,
         creator_id,
         creator_name,
         create_date_time,
         modifier_id,
         modifier_name,
         modify_date_time
         ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.paperReadGroupId},
            #{item.accountId},
            #{item.accountName},
            #{item.userId},
            #{item.bindName},
            #{item.bindPhoneNum},
            #{item.bindPhoneNumAes},
            #{item.paperReadAccountType},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createDateTime},
            #{item.modifierId},
            #{item.modifierName},
            #{item.modifyDateTime}
            )
        </foreach>
    </insert>

    <!--按paperReadAccountId删除阅卷组真实账号-->
    <delete id="deletePaperReadAccountById" parameterType="long">
        DELETE FROM t_paper_read_account
        WHERE paper_read_account_id = #{paperReadAccountId}
        AND paper_read_account_type = 1
    </delete>

    <!--按paperReadAccountId批量删除阅卷组真实账号-->
    <delete id="batchRemoveAccountReal" parameterType="long">
        DELETE FROM t_paper_read_account
        WHERE paper_read_account_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND paper_read_account_type = 1
    </delete>

    <delete id="deletePaperReadAccount" parameterType="com.dongni.basedata.paper.read.bean.params.DeletePaperReadAccountParam">
        DELETE FROM t_paper_read_account
        WHERE paper_read_group_id = #{paperReadGroupId}
        AND paper_read_account_type = #{paperReadAccountType}
        AND user_id IN
        <foreach collection="userIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!--按paperReadAccountId删除虚拟账号-->
    <delete id="deleteVirtualByPaperReadAccountId" parameterType="long">
        DELETE tpra, su, sa, tt
        FROM t_paper_read_account tpra
        LEFT JOIN sys_user su ON tpra.user_id = su.user_id
        LEFT JOIN sys_account sa ON tpra.account_id = sa.account_id
        LEFT JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        WHERE tpra.paper_read_account_id = #{paperReadAccountId}
        AND paper_read_account_type = 2
    </delete>

    <!--按paperReadAccountId批量删除虚拟账号-->
    <delete id="batchRemoveAccountVirtual" parameterType="long">
        DELETE tpra, su, sa, tt
        FROM t_paper_read_account tpra
        LEFT JOIN sys_user su ON tpra.user_id = su.user_id
        LEFT JOIN sys_account sa ON tpra.account_id = sa.account_id
        LEFT JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        WHERE tpra.paper_read_account_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND paper_read_account_type = 2
    </delete>

    <!--根据ID查询阅卷组信息-->
    <select id="getPaperReadAccountById" parameterType="long" resultMap="paperReadAccountDtoResultMap">
        SELECT
           tprgs.paper_read_group_set_id,
           tprgs.set_name,
           tprgs.grade_type,
           tprg.paper_read_group_id,
           tprg.group_name,
           tprg.course_id,
           tprg.course_name,
           tpra.paper_read_account_id,
           tpra.bind_name,
           tpra.bind_phone_num,
           tpra.bind_phone_num_aes,
           tpra.paper_read_account_type,
           tpra.account_id,
           tpra.user_id,
           tpra.account_name,
           tpra.creator_id,
           tpra.creator_name,
           tpra.create_date_time,
           tpra.modifier_id,
           tpra.modifier_name,
           tpra.modify_date_time
        FROM t_paper_read_group_set tprgs
                 INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
                 INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tpra.paper_read_account_id = #{paperReadAccountId}
    </select>

    <!--获取阅卷组账号信息-->
    <select id="getPaperReadAccountByAccountId" parameterType="long" resultMap="paperReadAccountDtoResultMap">
        SELECT
            tprgs.paper_read_group_set_id,
            tprgs.set_name,
            tprgs.grade_type,
            tprg.paper_read_group_id,
            tprg.group_name,
            tprg.course_id,
            tprg.course_name,
            tpra.paper_read_account_id,
            tpra.bind_name,
            tpra.bind_phone_num,
            tpra.bind_phone_num_aes,
            tpra.paper_read_account_type,
            tpra.account_id,
            tpra.user_id,
            tpra.account_name,
            tpra.creator_id,
            tpra.creator_name,
            tpra.create_date_time,
            tpra.modifier_id,
            tpra.modifier_name,
            tpra.modify_date_time
        FROM t_paper_read_group_set tprgs
                 INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
                 INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tpra.account_id = #{accountId}
    </select>

    <!--获取阅卷组账号简单用户信息-->
    <select id="getPaperReadAccountInfoByAccountId" parameterType="long" resultType="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountSimpleInfoDto">
        SELECT
            account_id          accountId,
            account_name        accountName,
            user_id             userId,
            bind_name           bindName,
            bind_phone_num      bindPhoneNum,
            bind_phone_num_aes  bindPhoneNumAes,
            paper_read_account_type paperReadAccountType
        FROM
            t_paper_read_account
        WHERE
            account_id = #{accountId}
    </select>

    <!--根据accountId获取阅卷组账号简单用户信息-->
    <select id="getPaperReadAccountInfoByAccountIds" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadGroupSetAccountParams"
            resultType="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountSimpleInfoDto">
        SELECT
            tprgs.set_name           setName,
            tprg.group_name          groupName,
            tpra.account_id          accountId,
            tpra.account_name        accountName,
            tpra.user_id             userId,
            tpra.bind_name           bindName,
            tpra.bind_phone_num      bindPhoneNum,
            tpra.bind_phone_num_aes  bindPhoneNumAes,
            tpra.paper_read_account_type    paperReadAccountType,
            tprg.paper_read_group_id        paperReadGroupId
        FROM t_paper_read_group_set tprgs
        INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
        INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE
            tpra.account_id IN
        <foreach collection="accountIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="paperReadGroupSetId != null and paperReadGroupSetId != ''">
            AND tprgs.paper_read_group_set_id = #{paperReadGroupSetId}
        </if>
    </select>

    <!--更新阅卷组账号的姓名和手机号-->
    <update id="updatePaperReadAccountNameAndPhoneNum" parameterType="com.dongni.basedata.paper.read.bean.bo.PaperReadAccountInfoUpdateBo">
        UPDATE
            t_paper_read_account
        SET bind_name = #{bindName},
            bind_phone_num = #{bindPhoneNum},
            bind_phone_num_aes = #{bindPhoneNumAes},
            modifier_id = #{modifierId},
            modifier_name = #{modifierName},
            modify_date_time = NOW()
        WHERE
            paper_read_account_id = #{paperReadAccountId};
    </update>

    <!--获取分页的数量-->
    <select id="countPaperReadAccount" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadAccountPageParams" resultType="int">
        SELECT COUNT(1)
        FROM t_paper_read_group_set tprgs
        INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
        INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tprgs.paper_read_group_set_id = #{paperReadGroupSetId}
        <if test="paperReadGroupId != null">
            AND tprg.paper_read_group_id = #{paperReadGroupId}
        </if>
        <if test="courseId != null">
            AND tprg.course_id = #{courseId}
        </if>
        <if test="groupNameSearch != null and groupNameSearch != ''">
            <bind name="groupNameSearchLike" value="'%' + groupNameSearch + '%'"/>
            AND tprg.group_name LIKE #{groupNameSearchLike}
        </if>
        <if test="accountNameSearch != null and accountNameSearch != ''">
            <bind name="accountNameSearchLike" value="'%' + accountNameSearch + '%'"/>
            AND tpra.account_name LIKE #{accountNameSearchLike}
        </if>
        <if test="paperReadAccountIds != null and paperReadAccountIds.size() > 0">
            AND tpra.paper_read_account_id IN
            <foreach collection="paperReadAccountIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--分页查询-->
    <select id="listPaperReadAccount" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadAccountPageParams"
            resultMap="paperReadAccountDtoResultMap">
        SELECT tprgs.paper_read_group_set_id,
               tprgs.set_name,
               tprgs.grade_type,
               tprg.paper_read_group_id,
               tprg.group_name,
               tprg.course_id,
               tprg.course_name,
               tpra.paper_read_account_id,
               tpra.bind_name,
               tpra.bind_phone_num,
               tpra.bind_phone_num_aes,
               tpra.paper_read_account_type,
               tpra.account_id,
               tpra.user_id,
               tpra.account_name,
               tpra.creator_id,
               tpra.creator_name,
               tpra.create_date_time,
               tpra.modifier_id,
               tpra.modifier_name,
               tpra.modify_date_time
        FROM t_paper_read_group_set tprgs
        INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
        INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tprgs.paper_read_group_set_id = #{paperReadGroupSetId}
        <if test="paperReadGroupId != null">
            AND tprg.paper_read_group_id = #{paperReadGroupId}
        </if>
        <if test="courseId != null">
            AND tprg.course_id = #{courseId}
        </if>
        <if test="groupNameSearch != null and groupNameSearch != ''">
            <bind name="groupNameSearchLike" value="'%' + groupNameSearch + '%'"/>
            AND tprg.group_name LIKE #{groupNameSearchLike}
        </if>
        <if test="accountNameSearch != null and accountNameSearch != ''">
            <bind name="accountNameSearchLike" value="'%' + accountNameSearch + '%'"/>
            AND tpra.account_name LIKE #{accountNameSearchLike}
        </if>
        <if test="paperReadAccountIds != null and paperReadAccountIds.size() > 0">
            AND tpra.paper_read_account_id IN
            <foreach collection="paperReadAccountIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY tpra.paper_read_account_id
        <if test="currentIndex != null and pageSize != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!--根据阅卷组id列表批量查询账号-->
    <select id="getByPaperReadGroupIds" parameterType="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountQueryDto"
            resultMap="commonResultMap">
        SELECT <include refid="commonField"/>
        FROM t_paper_read_account tpra
        WHERE tpra.paper_read_group_id IN
        <foreach collection="paperReadGroupIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="paperReadAccountTypeList != null and paperReadAccountTypeList.size() > 0">
            AND tpra.paper_read_account_type IN
            <foreach collection="paperReadAccountTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--批量重置阅卷组账号绑定的信息-->
    <update id="clearBindInfo" parameterType="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountUpdateDto">
        UPDATE t_paper_read_account
        SET bind_name = NULL,
            bind_phone_num = NULL,
            bind_phone_num_aes = NULL,
            modifier_id = #{modifierId},
            modifier_name = #{modifierName},
            modify_date_time = #{modifyDateTime}
        WHERE paper_read_account_id IN
        <foreach collection="paperReadAccountIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--根据阅卷组id获取其下的老师列表-->
    <select id="listTeacher" parameterType="long" resultMap="paperReadGroupTeacherVOResultMap">
        SELECT tpra.paper_read_account_id,
               tpra.account_name,
               tt.teacher_id,
               tt.teacher_name,
               tpra.bind_name,
               tpra.bind_phone_num_aes
        FROM t_paper_read_account tpra
        INNER JOIN sys_user su ON tpra.user_id = su.user_id
        INNER JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        WHERE tpra.paper_read_group_id = #{paperReadGroupId}
    </select>

    <select id="validatePaperReadRealAccount" parameterType="long" resultType="com.dongni.basedata.paper.read.bean.vo.PaperReadTeacherVO">
        SELECT
            su.account_id             	accountId,
      		tpra.account_name           accountName,
      		su.user_id                  userId,
      		tt.teacher_id               teacherId,
      		tt.teacher_name             teacherName,
      		tpra.paper_read_group_id    paperReadGroupId,
            tpra.user_id                tempUserId,
            tpra.account_id             tempAccountId,
            tpra.paper_read_account_id  paperReadAccountId,
            tpra.bind_name              bindName,
            tpra.bind_phone_num         bindPhoneNum,
      		tprg.group_name             groupName
        FROM t_paper_read_account tpra
        INNER JOIN t_paper_read_group tprg ON tpra.paper_read_group_id = tprg.paper_read_group_id
        LEFT JOIN sys_user su ON tpra.user_id = su.user_id
        LEFT JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        WHERE tpra.paper_read_account_type = 1 AND tpra.paper_read_group_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!--根据阅卷组id获取其下的老师列表-->
    <select id="listTeacherByIds" parameterType="map" resultMap="paperReadGroupTeacherVOResultMap">
        SELECT tpra.paper_read_account_id,
               tpra.account_name,
               tt.teacher_id,
               tt.teacher_name,
               tpra.bind_name,
               tpra.bind_phone_num_aes
        FROM t_paper_read_account tpra
                 INNER JOIN sys_user su ON tpra.user_id = su.user_id
                 INNER JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        WHERE tt.teacher_id in
        <foreach collection="teacherIds" item="teacherId" separator="," open="(" close=")">
            #{teacherId}
        </foreach>
    </select>

    <!--根据阅卷组id获取其下的老师列表-->
    <select id="listTeacherByGroups" parameterType="long" resultMap="paperReadGroupTeacherVOResultMap">
        SELECT tprg.paper_read_group_id,
               tpra.paper_read_account_id,
               tpra.account_name,
               tt.teacher_id,
               tt.teacher_name,
               tpra.bind_name,
               tpra.bind_phone_num_aes
        FROM t_paper_read_group_set tprgs
        INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
        INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        INNER JOIN sys_user su ON tpra.user_id = su.user_id
        INNER JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        WHERE tprg.paper_read_group_id in
        <foreach collection="list" item="groupId" separator="," open="(" close=")">
            #{groupId}
        </foreach>
    </select>

    <select id="getGroupTeacherByAccount" parameterType="map" resultMap="paperReadGroupTeacherVOResultMap">
        SELECT tpra.paper_read_account_id,
        tpra.account_name,
        tt.teacher_id,
        tt.teacher_name,
        tpra.bind_name,
        tpra.paper_read_group_id,
        tprg.group_name
        FROM t_paper_read_account tpra
        INNER JOIN sys_user su ON tpra.user_id = su.user_id
        INNER JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        INNER JOIN t_paper_read_group tprg ON tpra.paper_read_group_id = tprg.paper_read_group_id
        WHERE tpra.paper_read_group_id in
        <foreach collection="paperReadGroupIds" item="paperReadGroupId" separator="," open="(" close=")">
            #{paperReadGroupId}
        </foreach>
    </select>

    <!--根据阅卷组id删除虚拟账号-->
    <delete id="deleteByPaperReadGroupIdVirtual" parameterType="long">
        DELETE tpra, su, sa, tt
        FROM t_paper_read_account tpra
        LEFT JOIN sys_user su ON tpra.user_id = su.user_id
        LEFT JOIN sys_account sa ON tpra.account_id = sa.account_id
        LEFT JOIN t_teacher tt ON tt.teacher_id = su.relative_id AND su.user_type = 3
        WHERE paper_read_group_id = #{paperReadGroupId}
        AND paper_read_account_type = 2
    </delete>

    <!--根据阅卷组id删除真实账号-->
    <delete id="deleteByPaperReadGroupIdReal" parameterType="long">
        DELETE FROM t_paper_read_account
        WHERE paper_read_group_id = #{paperReadGroupId}
        AND paper_read_account_type = 1
    </delete>

    <!--获取阅卷组对应的阅卷账号-->
    <select id="getPaperReadAccountByGroupIds" parameterType="list" resultType="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountExcelExportDto">
        SELECT tprgs.paper_read_group_set_id    paperReadGroupSetId,
               tprgs.set_name                   setName,
               tprgs.grade_type                 gradeType,
               tprg.paper_read_group_id         paperReadGroupId,
               tprg.group_name                  groupName,
               tprg.course_id                   courseId,
               tprg.course_name                 courseName,
               tpra.paper_read_account_id       paperReadAccountId,
               tpra.bind_name                   bindName,
               tpra.bind_phone_num              bindPhoneNum,
               tpra.bind_phone_num_aes          bindPhoneNumAes,
               tpra.paper_read_account_type     paperReadAccountType,
               tpra.account_id                  accountId,
               tpra.account_name                accountName
        FROM t_paper_read_group_set tprgs
                 INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
                 INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tprg.paper_read_group_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!--根据账号Id和用户Id查询阅卷组账号-->
    <select id="getPaperReadAccountByAccountIdAndUserId" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadSysAccountParams" resultMap="paperReadAccountDtoResultMap">
        SELECT
            tprgs.paper_read_group_set_id,
            tprgs.set_name,
            tprgs.grade_type,
            tprg.paper_read_group_id,
            tprg.group_name,
            tprg.course_id,
            tprg.course_name,
            tpra.paper_read_account_id,
            tpra.bind_name,
            tpra.bind_phone_num,
            tpra.bind_phone_num_aes,
            tpra.paper_read_account_type,
            tpra.account_id,
            tpra.account_name,
            tpra.creator_id,
            tpra.creator_name,
            tpra.create_date_time,
            tpra.modifier_id,
            tpra.modifier_name,
            tpra.modify_date_time
        FROM t_paper_read_group_set tprgs
                 INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
                 INNER JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tpra.account_id = #{accountId} AND tpra.user_id = #{userId}
    </select>

    <!--统计联考员下所有虚拟账号-->
    <select id="countPaperReadAccountByExaminer" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadAccountByExaminerParams" resultType="int">
        SELECT
            COUNT(*)
        FROM
            t_paper_read_group_set tprgs
                JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
                JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
                JOIN sys_user su ON su.user_id = tpra.user_id
                JOIN t_teacher tt ON su.relative_id = tt.teacher_id
                JOIN t_school ts ON tt.school_id = ts.school_id
        WHERE
            tprgs.creator_id = #{examinerUserId}
          <if test="courseId!=null">
              AND tprg.course_id = #{courseId}
          </if>
          <if test="gradeType!=null">
              AND tprgs.grade_type = #{gradeType}
          </if>
          <if test="gradeTypeList != null and gradeTypeList.size() > 0">
              AND tprgs.grade_type IN
              <foreach collection="gradeTypeList" item="item" separator="," open="(" close=")">
                  #{item}
              </foreach>
          </if>
        <if test="search != null and search != ''">
            AND (tpra.bind_name LIKE CONCAT('%', #{search},'%') OR tpra.bind_phone_num LIKE CONCAT('%', #{search},'%') OR tpra.account_name LIKE CONCAT('%', #{search},'%') OR tprg.group_name LIKE CONCAT('%', #{search},'%'))
        </if>
    </select>

    <!--获取联考员下所有虚拟账号-->
    <select id="getPaperReadAccountListByExaminer" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadAccountByExaminerParams"
            resultType="com.dongni.basedata.paper.read.bean.vo.PaperReadAccountTeacherInfoVO">
        SELECT
            tprgs.set_name          setName,
            tprgs.grade_type        gradeType,
            tprgs.paper_read_group_set_id   paperReadGroupSetId,
            tprg.paper_read_group_id        paperReadGroupId,
            tprg.group_name         groupName,
            tprg.course_id          courseId,
            tprg.course_name        courseName,
            ts.school_id            schoolId,
            ts.school_name          schoolName,
            tt.teacher_id           teacherId,
            tt.teacher_name         teacherName,
            tt.teacher_phone_aes    teacherPhoneAes,
            tpra.account_name           accountName,
            tpra.user_id                userId,
            tpra.bind_name              bindName,
            tpra.bind_phone_num_aes     bindPhoneNumAes,
            tpra.paper_read_account_type    paperReadAccountType
        FROM
            t_paper_read_group_set tprgs
                JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
                JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
                JOIN sys_user su ON su.user_id = tpra.user_id
                JOIN t_teacher tt ON su.relative_id = tt.teacher_id
                JOIN t_school ts ON tt.school_id = ts.school_id
        WHERE
            tprgs.creator_id = #{examinerUserId}
            <if test="courseId!=null">
                AND tprg.course_id = #{courseId}
            </if>
            <if test="gradeType!=null">
                AND tprgs.grade_type = #{gradeType}
            </if>
            <if test="gradeTypeList != null and gradeTypeList.size() > 0">
                AND tprgs.grade_type IN
                <foreach collection="gradeTypeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="search != null and search != ''">
                AND (tpra.bind_name LIKE CONCAT('%', #{search},'%') OR tpra.bind_phone_num LIKE CONCAT('%', #{search},'%') OR tpra.account_name LIKE CONCAT('%', #{search},'%') OR tprg.group_name LIKE CONCAT('%', #{search},'%'))
            </if>
            <if test="pageSize !=null and currentIndex != null">
                LIMIT #{currentIndex},#{pageSize}
            </if>
    </select>

    <!--根据老师id列表获取简单的虚拟账号信息-->
    <select id="getVirtualTeacherInfoByTeacherIds" parameterType="long" resultMap="VirtualTeacherInfoResultMap">
        SELECT tt.teacher_id,
               tpra.account_name,
               tpra.paper_read_account_id,
               tpra.paper_read_account_type
        FROM t_teacher tt
        INNER JOIN sys_user su ON tt.teacher_id = su.relative_id AND su.user_type = 3
        INNER JOIN t_paper_read_account tpra ON su.user_id = tpra.user_id
        WHERE tt.teacher_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!--根据阅卷组id列表批量查询账号-->
    <select id="getPaperReadAccountInfoByPaperReadGroupIds" parameterType="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountQueryDto"
            resultType="com.dongni.exam.common.mark.vo.PaperReadAccountVO">
        SELECT

            tt.teacher_id                   teacherId,
            tt.teacher_name                 teacherName,
            tpra.paper_read_group_id        paperReadGroupId,
            tpra.account_id                 accountId,
            tpra.account_name               accountName,
            tpra.user_id                    userId,
            tpra.bind_name                  bindName,
            tpra.bind_phone_num_aes         bindPhoneNumAes,
            tpra.paper_read_account_type    paperReadAccountType
        FROM t_paper_read_account tpra
        JOIN sys_user su ON su.user_id = tpra.user_id
        JOIN t_teacher tt ON su.relative_id = tt.teacher_id
        WHERE tpra.paper_read_group_id IN
        <foreach collection="paperReadGroupIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="paperReadAccountTypeList != null and paperReadAccountTypeList.size() > 0">
            AND tpra.paper_read_account_type IN
            <foreach collection="paperReadAccountTypeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listPaperReadTeachers" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadTeacherParam"
            resultType="com.dongni.basedata.paper.read.bean.vo.PaperReadTeacherVO">
        SELECT
        /*+USE_INDEX(tt,teacher_id), USE_INDEX(tpra,idx_user_id)*/
        	tt.teacher_id               teacherId,
        	tt.teacher_name             teacherName,
        	tpra.account_id             accountId,
        	tpra.account_name           accountName,
        	tprg.paper_read_group_id    paperReadGroupId,
        	tprg.group_name             groupName,
            tprg.course_id              courseId,
            tprg.course_name            courseName
        FROM
        	t_teacher tt
        	JOIN sys_user su ON su.relative_id = tt.teacher_id AND su.user_type = 3
        	JOIN t_paper_read_account tpra ON su.user_id = tpra.user_id
        	JOIN t_paper_read_group tprg ON tpra.paper_read_group_id = tprg.paper_read_group_id
        	JOIN t_paper_read_group_set tprgs ON tprg.paper_read_group_set_id = tprgs.paper_read_group_set_id
        WHERE tt.teacher_id IN
         <foreach collection="grpTchIds" item="item" separator="," open="(" close=")">
             #{item}
         </foreach>
         AND tprgs.grade_type = #{gradeType} AND tprgs.creator_id = #{examinerUserId}
         <if test="groupId != null and groupId != ''">
             AND tpra.paper_read_group_id = #{groupId}
         </if>
        <if test="courseId != null and courseId != ''">
            AND tprg.course_id = #{courseId}
        </if>
        <if test="teacher != null and teacher != ''">
            AND (tpra.bind_name LIKE CONCAT('%', #{teacher},'%') OR tpra.bind_phone_num LIKE CONCAT('%', #{teacher},'%') OR tpra.account_name LIKE CONCAT('%', #{teacher},'%') OR tt.teacher_name LIKE CONCAT('%', #{teacher},'%'))
        </if>
    </select>
</mapper>