<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="PaperReadGroupMapper">
    <resultMap id="commonResultMap" type="com.dongni.basedata.paper.read.bean.model.PaperReadGroup">
        <id column="paper_read_group_id" property="paperReadGroupId"/>
        <result column="paper_read_group_set_id" property="paperReadGroupSetId"/>
        <result column="group_name" property="groupName"/>
        <result column="group_code" property="groupCode"/>
        <result column="course_id" property="courseId"/>
        <result column="course_name" property="courseName"/>
        <result column="assist_paper_read_status" property="assistPaperReadStatus"/>
        <result column="creator_id" property="creatorId"/>
        <result column="creator_name" property="creatorName"/>
        <result column="create_date_time" property="createDateTime"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="modifier_name" property="modifierName"/>
        <result column="modify_date_time" property="modifyDateTime"/>
    </resultMap>

    <resultMap id="paperReadGroupDtoResultMap" type="com.dongni.basedata.paper.read.bean.dto.PaperReadGroupDto" extends="commonResultMap">
        <result column="account_count" property="accountCount"/>
        <result column="grade_type" property="gradeType"/>
    </resultMap>

    <resultMap id="paperReadAccountSequenceDtoResultMap" type="com.dongni.basedata.paper.read.bean.dto.PaperReadAccountSequenceDto">
        <result column="paper_read_group_set_id" property="paperReadGroupSetId"/>
        <result column="paper_read_group_id" property="paperReadGroupId"/>
        <result column="group_name" property="groupName"/>
        <result column="set_code" property="setCode"/>
        <result column="group_code" property="groupCode"/>
        <result column="paper_read_account_sequence_id" property="paperReadAccountSequenceId"/>
        <result column="sequence_code" property="sequenceCode"/>
        <result column="current_num" property="currentNum"/>
    </resultMap>

    <resultMap id="paperReadGroupVOResultMap" type="com.dongni.exam.common.mark.vo.PaperReadGroupVO">
        <result column="paper_read_group_id" property="paperReadGroupId"/>
        <result column="group_name" property="groupName"/>
        <result column="course_id" property="courseId"/>
        <result column="course_name" property="courseName"/>
        <result column="grade_type" property="gradeType"/>
        <result column="assist_paper_read_status" property="assistPaperReadStatus"/>
        <result column="account_count" property="accountCount"/>
        <result column="set_name" property="grpCollection"/>
    </resultMap>

    <sql id="commonField">
        tprg.paper_read_group_id,
        tprg.paper_read_group_set_id,
        tprg.group_name,
        tprg.group_code,
        tprg.course_id,
        tprg.course_name,
        tprg.assist_paper_read_status,
        tprg.creator_id,
        tprg.creator_name,
        tprg.create_date_time,
        tprg.modifier_id,
        tprg.modifier_name,
        tprg.modify_date_time
    </sql>

    <!--根据唯一键查询阅卷组-->
    <select id="getByUniqueKey" parameterType="com.dongni.basedata.paper.read.bean.model.PaperReadGroup" resultType="int">
        SELECT COUNT(1)
        FROM t_paper_read_group
        WHERE group_code = #{groupCode}
        AND paper_read_group_set_id = #{paperReadGroupSetId}
    </select>

    <!--插入阅卷组-->
    <insert id="insertPaperReadGroup" parameterType="com.dongni.basedata.paper.read.bean.model.PaperReadGroup"
            useGeneratedKeys="true" keyProperty="paperReadGroupId" keyColumn="paper_read_group_id">
        INSERT INTO t_paper_read_group
        (paper_read_group_set_id,
         group_name,
         group_code,
         course_id,
         course_name,
         assist_paper_read_status,
         creator_id,
         creator_name,
         create_date_time,
         modifier_id,
         modifier_name,
         modify_date_time) VALUES
        (#{paperReadGroupSetId},
         #{groupName},
         #{groupCode},
         #{courseId},
         #{courseName},
         #{assistPaperReadStatus},
         #{creatorId},
         #{creatorName},
         #{createDateTime},
         #{modifierId},
         #{modifierName},
         #{modifyDateTime})
    </insert>

    <!--批量新增阅卷组-->
    <insert id="batchInsertPaperReadGroup" parameterType="com.dongni.basedata.paper.read.bean.model.PaperReadGroup"
            useGeneratedKeys="true" keyProperty="paperReadGroupId">
        INSERT INTO t_paper_read_group
        (paper_read_group_set_id,
         group_name,
         group_code,
         course_id,
         course_name,
         assist_paper_read_status,
         creator_id,
         creator_name,
         create_date_time,
         modifier_id,
         modifier_name,
         modify_date_time) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.paperReadGroupSetId},
            #{item.groupName},
            #{item.groupCode},
            #{item.courseId},
            #{item.courseName},
            #{item.assistPaperReadStatus},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createDateTime},
            #{item.modifierId},
            #{item.modifierName},
            #{item.modifyDateTime}
            )
        </foreach>
    </insert>

    <!--更新阅卷组-->
    <update id="updatePaperReadGroup" parameterType="com.dongni.basedata.paper.read.bean.model.PaperReadGroup">
        UPDATE t_paper_read_group
        SET group_name = #{groupName},
            course_id = #{courseId},
            course_name = #{courseName},
            assist_paper_read_status = #{assistPaperReadStatus},
            modifier_id = #{modifierId},
            modifier_name = #{modifierName},
            modify_date_time = #{modifyDateTime}
        WHERE paper_read_group_id = #{paperReadGroupId}
    </update>

    <!--查询阅卷组数量-->
    <select id="countPaperReadGroup" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadGroupPageParams" resultType="int">
        SELECT COUNT(1)
        FROM t_paper_read_group
        WHERE paper_read_group_set_id = #{paperReadGroupSetId}
        <if test="search != null and search != ''">
            <bind name="searchLike" value="'%' + search + '%'"/>
            AND group_name LIKE #{searchLike}
        </if>
        <if test="courseId != null">
            AND course_id = #{courseId}
        </if>
    </select>

    <!--分页查询阅卷组-->
    <select id="listPaperReadGroup" parameterType="com.dongni.basedata.paper.read.bean.params.PaperReadGroupPageParams"
            resultMap="paperReadGroupDtoResultMap">
        SELECT
        <include refid="commonField"/>,
        tprgs.grade_type,
        COUNT(tpra.paper_read_account_id) account_count
        FROM t_paper_read_group tprg
        INNER JOIN t_paper_read_group_set tprgs ON tprg.paper_read_group_set_id = tprgs.paper_read_group_set_id
        LEFT JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tprg.paper_read_group_set_id = #{paperReadGroupSetId}
        <if test="search != null and search != ''">
            <bind name="searchLike" value="'%' + search + '%'"/>
            AND tprg.group_name LIKE #{searchLike}
        </if>
        <if test="courseId != null">
            AND tprg.course_id = #{courseId}
        </if>
        GROUP BY tprg.paper_read_group_id
        ORDER BY tprg.paper_read_group_id
        <if test="currentIndex != null and pageSize != ''">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!--根据id查询-->
    <select id="getById" parameterType="long" resultMap="commonResultMap">
        SELECT
        <include refid="commonField"/>
        FROM t_paper_read_group tprg
        WHERE tprg.paper_read_group_id = #{0}
    </select>

    <!--根据名称和集合查询阅卷组-->
    <select id="getByGroupNameAndSetId" parameterType="com.dongni.basedata.paper.read.bean.model.PaperReadGroup" resultType="int">
        SELECT COUNT(1)
        FROM t_paper_read_group tprg
        WHERE tprg.group_name = #{groupName}
        AND tprg.paper_read_group_set_id = #{paperReadGroupSetId}
        <if test="paperReadGroupId != null">
            AND tprg.paper_read_group_id != #{paperReadGroupId}
        </if>
    </select>

    <!--根据阅卷组id列表获取合并后的编码规则-->
    <select id="getSequenceCode" parameterType="long"  resultMap="paperReadAccountSequenceDtoResultMap">
        SELECT tprg.paper_read_group_set_id,
               tprg.paper_read_group_id,
               tprg.group_name,
               tprgs.set_code,
               tprg.group_code,
               tpras.paper_read_account_sequence_id,
               tpras.sequence_code,
               tpras.current_num
        FROM t_paper_read_group tprg
        INNER JOIN t_paper_read_group_set tprgs on tprg.paper_read_group_set_id = tprgs.paper_read_group_set_id
        INNER JOIN t_paper_read_account_sequence tpras on tpras.sequence_code = CONCAT(tprgs.set_code, tprg.group_code)
        WHERE tprg.paper_read_group_id IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!--根据联考员userId、courseId、gradeId获取阅卷组列表-->
    <select id="listPaperReadGroupForMark" parameterType="com.dongni.basedata.paper.read.bean.dto.PaperReadGroupQueryDto"
            resultMap="paperReadGroupVOResultMap">
        SELECT tprg.paper_read_group_id,
               tprg.group_name,
               tprg.course_id,
               tprg.course_name,
               tprgs.grade_type,
               tprgs.set_name,
               tprg.assist_paper_read_status,
               COUNT(tpra.paper_read_account_id) account_count
        FROM t_paper_read_group_set tprgs
        INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
        LEFT JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tprgs.creator_id = #{userId}
        <if test="gradeType!=null">
            AND tprgs.grade_type = #{gradeType}
        </if>
        <if test="courseId!=null">
            AND tprg.course_id = #{courseId}
        </if>
        AND tprgs.creator_id = #{userId}
        GROUP BY tprg.paper_read_group_id
    </select>

    <!--根据id批量查询阅卷组列表-->
    <select id="listByIds" parameterType="long" resultMap="paperReadGroupVOResultMap">
        SELECT tprg.paper_read_group_id,
               tprg.group_name,
               tprg.course_id,
               tprg.course_name,
               tprgs.grade_type,
               tprgs.set_name,
               tprg.assist_paper_read_status,
               COUNT(tpra.paper_read_account_id) account_count
        FROM t_paper_read_group_set tprgs
        INNER JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
        LEFT JOIN t_paper_read_account tpra ON tprg.paper_read_group_id = tpra.paper_read_group_id
        WHERE tprg.paper_read_group_id IN
        <foreach collection="paperReadGroupIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="groupName!=null">
            and tprg.group_name like #{groupName}
        </if>
        GROUP BY tprg.paper_read_group_id
    </select>

    <!--删除阅卷组-->
    <delete id="deletePaperReadGroup" parameterType="long">
        DELETE FROM t_paper_read_group
        WHERE paper_read_group_id = #{0}
    </delete>

    <!--根据阅卷组集合id查询底下的阅卷组-->
    <select id="getByPaperReadGroupSetId" parameterType="long" resultMap="commonResultMap">
        SELECT <include refid="commonField"/>
        FROM t_paper_read_group tprg
        WHERE tprg.paper_read_group_set_id = #{0}
    </select>

    <!--根据阅卷组集合下所有阅卷组-->
    <select id="getPaperReadGroupListBySetId" parameterType="long" resultMap="paperReadGroupDtoResultMap">
        SELECT
            tprgs.set_name,
            tprgs.set_code,
            tprgs.grade_type,
            tprg.paper_read_group_id,
            tprg.paper_read_group_set_id,
            tprg.group_name,
            tprg.group_code,
            tprg.course_id,
            tprg.course_name
        FROM
            t_paper_read_group_set tprgs
        JOIN t_paper_read_group tprg ON tprgs.paper_read_group_set_id = tprg.paper_read_group_set_id
        WHERE
            tprgs.paper_read_group_set_id = #{paper_read_group_set_id}
    </select>

    <!--获取比执行id大且修改时间在一个月前的阅卷组-->
    <select id="getByGreaterThanId" parameterType="long" resultMap="commonResultMap">
        SELECT <include refid="commonField"/>
        FROM t_paper_read_group tprg
        WHERE tprg.paper_read_group_id > #{0}
        AND tprg.modify_date_time &lt; DATE_SUB(NOW(), INTERVAL 1 MONTH)
        ORDER BY tprg.paper_read_group_id
        LIMIT 1
    </select>

    <!--更新阅卷组的修改时间-->
    <update id="updateModifyDateTime" parameterType="long">
        UPDATE t_paper_read_group
        SET modifier_id = 1,
            modifier_name = '定时处理',
            modify_date_time = NOW()
        WHERE paper_read_group_id = #{0}
    </update>
</mapper>