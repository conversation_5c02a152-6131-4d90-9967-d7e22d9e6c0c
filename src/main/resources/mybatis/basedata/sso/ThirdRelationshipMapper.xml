<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ThirdRelationshipMapper">

    <select id="getThirdCompanyName"  resultType="map">
        SELECT
            third_company_name thirdCompanyName,
            third_type thirdType
            from t_third_company
            where status = 1

    </select>

    <select id="getThirdCompany" resultType="map">
        SELECT third_company_name thirdCompanyName,
               third_type thirdType,
               secret secret
        FROM t_third_company
        WHERE status = 1
        AND app_id = #{appId}

    </select>

    <select id="getAccountNameAesByAccountId" parameterType="map"  resultType="string">
        SELECT
            account_name_aes
        from sys_account
        where account_id IN
        <foreach item="item" index="index" collection="accountIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAccountInfoByAccountId" parameterType="map"  resultType="map">
        SELECT
        account_name accountName,
        account_name_aes accountNameAes
        from sys_account
        where account_id = #{accountId}
    </select>


    <insert id="insertThirdRelationship" parameterType="map">
        INSERT INTO t_third_relationship(
        third_id,
        third_type,
        account_name,
        account_name_aes,
        `status`,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        VALUES
        <foreach collection="body" item="item" separator=",">
            (
            #{item.thirdId},
            #{thirdType},
            #{item.accountName},
            #{item.accountNameAes},
            1,
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            `account_name` = values(`account_name`),
            `account_name_aes` = values(`account_name_aes`),
            `status` = values(`status`),
            modifier_id = values(modifier_id),
            modifier_name = values(modifier_name),
            modify_date_time = values(modify_date_time)
    </insert>

    <!-- 删除对应关系 -->
    <delete id="deleteAccountRelation" parameterType="map">
        DELETE FROM t_third_relationship
        WHERE account_name_aes IN
        <foreach item="item" index="index" collection="accountNameList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and third_type = #{thirdType}
    </delete>

    <!-- 删除对应关系 -->
    <delete id="deleteCompanyAccountRelation" parameterType="map">
        DELETE FROM t_third_relationship
        WHERE third_type = #{thirdType}
        AND account_name_aes IN
        <foreach collection="body" open="(" item="item" close=")" separator=",">
            #{item.accountNameAes}
        </foreach>
    </delete>

    <!--老师-->
    <select id="getThirdAccountTeacherRelationList"  resultType="map">
        SELECT
        /*+ INL_JOIN(tt, su, sa, ttr) */
            sa.account_id accountId,
            sa.account_name accountName,
            sa.account_name_aes accountNameAes,
            su.user_name userName,
            su.user_type userType,
            '老师' userTypeName,
            ttr.third_id thirdId
        from t_teacher tt
            inner join sys_user su on  tt.teacher_id = su.relative_id and su.user_type = 3
            inner join sys_account sa on sa.account_id = su.account_id
            left join t_third_relationship ttr on ttr.account_name_aes = sa.account_name_aes and ttr.third_type = #{thirdType}
        where tt.school_id = #{schoolId}
        <if test="search != null and search != ''">
            AND (sa.account_name_aes like concat('%',#{searchAes},'%') OR su.user_name like concat('%',#{search},'%'))
        </if>
        order by sa.account_id
    </select>

    <!--学生-->
    <select id="getThirdAccountStudentRelationList"  resultType="map">
        SELECT
            /*+ INL_JOIN(ts, su, sa, ttr) */
            sa.account_id accountId,
            sa.account_name accountName,
            sa.account_name_aes accountNameAes,
            su.user_name userName,
            su.user_type userType,
            '学生' userTypeName,
            ttr.third_id thirdId
        from t_student ts
            inner join sys_user su on  ts.student_id = su.relative_id and su.user_type = 4
            inner join sys_account sa on sa.account_id = su.account_id
            left join t_third_relationship ttr on ttr.account_name_aes = sa.account_name_aes and ttr.third_type = #{thirdType}
        where ts.school_id = #{schoolId}
        <if test="search != null and search != ''">
            AND (sa.account_name_aes like concat('%',#{search},'%') OR su.user_name like concat('%',#{search},'%'))
        </if>
        order by sa.account_id
    </select>

    <!--家长-->
    <select id="getThirdAccountParentRelationList"  resultType="map">
        SELECT
        /*+ INL_JOIN(ts, tps, tp, su, sa, ttr) */
            sa.account_id accountId,
            sa.account_name accountName,
            sa.account_name_aes accountNameAes,
            su.user_name userName,
            su.user_type userType,
            '家长' userTypeName,
            ttr.third_id thirdId
        from t_student ts
            inner join t_parent_student tps on tps.student_id = ts.student_id
            inner join t_parent tp on tp.parent_id = tps.parent_id
            inner join sys_user su on tp.parent_id = su.relative_id and su.user_type = 5
            inner join sys_account sa on sa.account_id = su.account_id
            left join t_third_relationship ttr on ttr.account_name_aes = sa.account_name_aes and ttr.third_type = #{thirdType}
        where ts.school_id = #{schoolId}
        <if test="search != null and search != ''">
            AND (sa.account_name_aes like concat('%',#{search},'%') OR su.user_name like concat('%',#{search},'%'))
        </if>
        order by sa.account_id
    </select>

    <!--校长-->
    <select id="getThirdAccountPrincipalRelationList"  resultType="map">
        SELECT
        /*+ INL_JOIN(tsp, su, sa, ttr) */
            sa.account_id accountId,
            sa.account_name accountName,
            sa.account_name_aes accountNameAes,
            su.user_name userName,
            su.user_type userType,
            '校长' userTypeName,
            ttr.third_id thirdId
        from t_school_principal tsp
            inner join sys_user su on tsp.school_principal_id = su.relative_id and su.user_type = 9
            inner join sys_account sa on sa.account_id = su.account_id
            left join t_third_relationship ttr on ttr.account_name_aes = sa.account_name_aes and ttr.third_type = #{thirdType}
        where tsp.school_id = #{schoolId}
        <if test="search != null and search != ''">
            AND (sa.account_name_aes like concat('%',#{search},'%') OR su.user_name like concat('%',#{search},'%'))
        </if>
        order by sa.account_id
    </select>

    <!--教导主任-->
    <select id="getThirdAccountDirectorRelationList"  resultType="map">
        SELECT
        /*+ INL_JOIN(tsp, su, sa, ttr) */
            sa.account_id accountId,
            sa.account_name accountName,
            sa.account_name_aes accountNameAes,
            su.user_name userName,
            su.user_type userType,
            '教导主任' userTypeName,
            ttr.third_id thirdId
        from t_school_principal tsp
            inner join sys_user su on tsp.school_principal_id = su.relative_id and su.user_type = 10
            inner join sys_account sa on sa.account_id = su.account_id
            left join t_third_relationship ttr on ttr.account_name_aes = sa.account_name_aes and ttr.third_type = #{thirdType}
        where tsp.school_id = #{schoolId}
        <if test="search != null and search != ''">
            AND (sa.account_name_aes like concat('%',#{search},'%') OR su.user_name like concat('%',#{search},'%'))
        </if>
        order by sa.account_id
    </select>

    <!--教务-->
    <select id="getThirdAccountSchoolInstructorRelationList"  resultType="map">
        SELECT
        /*+ INL_JOIN(tsp, su, sa, ttr) */
            sa.account_id accountId,
            sa.account_name accountName,
            sa.account_name_aes accountNameAes,
            su.user_name userName,
            su.user_type userType,
            '教务' userTypeName,
            ttr.third_id thirdId
        from t_school_principal tsp
            inner join sys_user su on tsp.school_principal_id = su.relative_id and su.user_type = 15
            inner join sys_account sa on sa.account_id = su.account_id
            left join t_third_relationship ttr on ttr.account_name_aes = sa.account_name_aes and ttr.third_type = #{thirdType}
        where tsp.school_id = #{schoolId}
        <if test="search != null and search != ''">
            AND (sa.account_name_aes like concat('%',#{search},'%') OR su.user_name like concat('%',#{search},'%'))
        </if>
        order by sa.account_id
    </select>


    <!--更新账号初始密码状态-->
    <update id="updateAccountPasswordStatus" parameterType="map">
        UPDATE sys_account
        SET password_status = 1
        WHERE account_name_aes IN
        <foreach collection="body" item="item" separator="," open="(" close=")">
            #{item.accountNameAes}
        </foreach>
    </update>
</mapper>