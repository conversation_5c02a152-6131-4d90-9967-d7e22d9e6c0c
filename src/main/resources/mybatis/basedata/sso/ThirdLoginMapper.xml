<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ThirdLoginMapper">

    <select id="getAccountName"  resultType="map"  parameterType="map">
        SELECT
            sa.account_id accountId,
            sa.account_name accountName,
            sa.account_name_aes accountNameAes
            from t_third_relationship thr
                inner join sys_account sa on sa.account_name_aes = thr.account_name_aes
            where thr.third_id = #{thirdId}
                  and thr.third_type = #{thirdType}
    </select>

    <!-- 新增账户 -->
    <insert id="insertAccount" parameterType="map" keyColumn="account_id" keyProperty="accountId"
            useGeneratedKeys="true">
        INSERT ignore INTO sys_account(
        account_id,
        account_name,
        account_name_aes,
        password,
        account_status,
        wechat_open_id,
        password_status,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        VALUES (
        #{accountId},
        #{accountName},
        #{accountNameAes},
        #{password},
        #{accountStatus},
        #{weChatOpenId},
        #{passwordStatus},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
    </insert>

    <insert id="insertAccount4Sso" parameterType="map" useGeneratedKeys="true" keyProperty="accountId">
        INSERT INTO sys_account(
        account_id,
        account_name,
        account_name_aes,
        password,
        account_status,
        wechat_open_id,
        password_status,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        VALUES (
        #{accountId},
        #{accountName},
        #{accountNameAes},
        #{password},
        #{accountStatus},
        #{weChatOpenId},
        #{passwordStatus},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
        ON DUPLICATE KEY UPDATE
        account_status = VALUES(account_status),
        modifier_id = VALUES(modifier_id),
        modifier_name = VALUES(modifier_name),
        modify_date_time = VALUES(modify_date_time)
    </insert>


    <!-- 新增角色用户 -->
    <insert id="insertUser" parameterType="map" keyProperty="currentUserId" keyColumn="user_id" useGeneratedKeys="true">
        INSERT ignore INTO sys_user (
            user_name,
            user_status,
            user_type,
            relative_id,
            account_id,
            nickname,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        VALUES (
            #{roleName},
            #{userStatus},
            #{userType},
            #{relativeId},
            #{accountId},
            #{nickname},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
        )
    </insert>

    <!-- 新增角色用户 -->
    <insert id="insertUser4Sso" parameterType="map" keyProperty="currentUserId" keyColumn="user_id" useGeneratedKeys="true">
        INSERT INTO sys_user (
            user_name,
            user_status,
            user_type,
            relative_id,
            account_id,
            nickname,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        VALUES (
        #{roleName},
        #{userStatus},
        #{userType},
        #{relativeId},
        #{accountId},
        #{nickname},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
         ON DUPLICATE KEY UPDATE
         user_status = VALUES(user_status),
         modifier_id = VALUES(modifier_id),
         modifier_name = VALUES(modifier_name),
         modify_date_time = VALUES(modify_date_time)
    </insert>

    <insert id="insertUser4Jwt" parameterType="map" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO sys_user (
            user_name,
            user_status,
            user_type,
            relative_id,
            account_id,
            nickname,
            <if test="thirdPartyId != null">
                third_party_id,
            </if>
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        VALUES (
        #{roleName},
        #{userStatus},
        #{userType},
        #{relativeId},
        #{accountId},
        #{nickname},
        <if test="thirdPartyId != null">
            #{thirdPartyId},
        </if>
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )  ON DUPLICATE KEY UPDATE
            user_name = #{roleName},
            nickname = #{nickname},
            user_status = #{userStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
    </insert>

    <insert id="insertThirdRelationship" parameterType="map">
        INSERT INTO t_third_relationship(
        third_id,
        third_type,
        account_name,
        account_name_aes,
        `status`,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        VALUES
            (
            #{thirdId},
            #{thirdType},
            #{accountName},
            #{accountNameAes},
            1,
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
            )
    </insert>

    <select id="getStudent"  resultType="map"  parameterType="map">
        SELECT
            ts.student_id relativeId,
            ts.student_name roleName,
            ts.student_num studentNum,
            tc.class_name nickname
        from t_student ts
            join t_class_student tcs on tcs.student_id = ts.student_id
            join t_class tc on tc.class_id = tcs.class_id and class_type = 1
        where ts.student_name = #{studentName} and ts.student_num = #{studentNum}
    </select>

    <select id="getParent"  resultType="map"  parameterType="map">
        SELECT
           tp.parent_id parentId,
           tp.parent_name roleName,
           tps.parent_student_id relativeId,
           ts.student_name nickname
        from t_parent tp
            left join t_parent_student tps on tp.parent_id = tps.parent_id
            left join t_student ts on ts.student_id = tps.student_id
        where tp.parent_phone_aes = #{parentPhoneAes}
        limit 1
    </select>

    <insert id="insertParent" parameterType="map" keyProperty="parentId" keyColumn="parent_id" useGeneratedKeys="true">
        INSERT INTO t_parent(
            parent_name,
            parent_phone,
            parent_phone_aes,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )VALUES (
            #{parentName},
            #{parentPhone},
            #{parentPhoneAes},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
        ) ON DUPLICATE KEY UPDATE
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
    </insert>

    <insert id="insertParentStudent" parameterType="map" keyProperty="parentStudentId" keyColumn="parent_student_id" useGeneratedKeys="true">
        INSERT ignore INTO t_parent_student(
            parent_id,
            student_id,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )VALUES(
            #{parentId},
            #{studentId},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
       )
    </insert>

    <insert id="insertParentStudent4Sso" parameterType="map" keyProperty="parentStudentId" useGeneratedKeys="true">
        INSERT INTO t_parent_student(
            parent_id,
            student_id,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )VALUES(
        #{parentId},
        #{studentId},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
        ON DUPLICATE KEY UPDATE
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
    </insert>
    
    <select id="getStudentByParentId" parameterType="map" resultType="map">
        SELECT ts.identity_card_no_aes identityCardNoAes,
               ts.student_phone_aes studentPhoneAes,
               tps.parent_id parentId,
               tps.student_id studentId,
               ts.student_name studentName,
               ts.student_num  studentNum,
               tsc.school_name schoolName,
               tps.parent_student_id parentStudentId
        FROM t_parent_student tps
        INNER JOIN t_student ts ON tps.student_id = ts.student_id
        INNER JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE tps.parent_id = #{parentId}
    </select>

    <delete id="deleteParentStudent" parameterType="map">
        DELETE FROM t_parent_student
        WHERE student_id = #{studentId}
    </delete>

    <delete id="batchDeleteParentStudent" parameterType="map">
        DELETE FROM t_parent_student
        WHERE parent_student_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteUserForParent" parameterType="map">
        DELETE FROM sys_user
        WHERE relative_id = #{parentStudentId}
        AND user_type = 5
    </delete>

    <delete id="batchDeleteUserForParent" parameterType="map">
        DELETE FROM sys_user
        WHERE relative_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
          AND user_type = 5
    </delete>

</mapper>