<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="BaseCourseMapper">

    <!-- 获取非大学课程 -->
    <select id="selectNotUniversityCourses" resultType="map">
        SELECT
            course_id courseId,
            course_name courseName,
            stage stage
        FROM t_course
        WHERE stage != 4 AND course_type=1
        ORDER BY course_sort ASC
    </select>

    <!-- 查询教研员课程 -->
    <select id="selectInstructorCourses" parameterType="map" resultType="map">
      SELECT
          tic.instructor_course_id instructorCourseId,
          tic.instructor_id instructorId,
          tic.course_id courseId,
          tic.stage stage
      FROM
          sys_user su
          INNER JOIN t_instructor_course tic ON su.relative_id = tic.instructor_id
      WHERE
          su.user_id = #{myUserId}
          AND su.user_type = 8
    </select>

</mapper>