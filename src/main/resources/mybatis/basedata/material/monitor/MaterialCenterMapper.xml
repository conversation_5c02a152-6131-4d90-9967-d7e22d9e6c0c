<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="MaterialCenterMapper">

    <!--获取文件列表-->
    <select id="getMaterial" parameterType="map" resultType="map">
        SELECT
        count(DISTINCT tmf.material_file_id) total,
        tm.material_id materialId,
        tm.material_name materialName,
        tmr.material_root_id materialRootId,
        tmr.school_id schoolId,
        tmr.grade_year gradeYear,
        tmr.grade_term gradeTerm,
        group_concat(DISTINCT ts.staff_name ORDER BY ts.staff_id) staffName
        from t_material_root tmr
        INNER JOIN t_material_file tmf ON tmr.material_root_id = tmf.material_root_id
        INNER JOIN t_material tm ON tm.material_id = tmf.material_id
        INNER JOIN t_material_staff tms ON tms.material_id = tm.material_id
        INNER JOIN t_staff ts ON tms.staff_id = ts.staff_id
        WHERE tmr.school_id = #{schoolId} AND tmr.grade_year = #{gradeYear} AND tmr.grade_term=#{gradeTerm} AND tmf.file_status = 1
        <if test="staffId != null and staffId != ''">
            AND tm.`material_id` IN (
            SELECT DISTINCT tms.material_id
            FROM t_material_staff tms
            WHERE tms.`staff_id` = #{staffId}
            )
        </if>
        <if test="search != null and search != ''">
          AND tm.material_name LIKE concat('%',#{search},'%')
        </if>
        GROUP BY tm.material_id
        HAVING total > 0
        ORDER BY tm.modify_date_time desc
    </select>

    <!--获取文件上传的老师-->
    <select id="getMaterialTeacher" parameterType="map" resultType="map">
        SELECT
        material_root_id materialRootId,
        teacher_id teacherId,
        teacher_name teacherName
        FROM t_material_file  WHERE material_root_id = #{materialRootId}
        GROUP BY teacher_id
    </select>

    <!--获取老师文件详情-->
    <select id="getMaterialFile" parameterType="map" resultMap="TeacherMaterialFile">
        SELECT
        tmf.material_file_id materialFileId,
        tmf.material_root_id materialRootId,
        tmf.teacher_id teacherId,
        tmf.teacher_name teacherName,
        tmf.file_url fileUrl,
        tmf.file_name fileName,
        tmf.upload_time uploadTime
        FROM t_material_file tmf
        WHERE tmf.material_root_id = #{materialRootId}
        AND tmf.material_id = #{materialId}
        AND tmf.file_status = 1
        ORDER BY tmf.upload_time
    </select>

    <!--获取所有老师材料明细-->
    <select id="getMaterialTeacherItem" parameterType="map" resultMap="MaterialTeacherItem">
        SELECT
        tmti.material_id materialId,
        tmti.material_name materialName,
        tmti.teacher_id teacherId,
        tmti.teacher_name teacherName,
        tmti.operation_status operationStatus
        from t_material_teacher_item tmti
        INNER JOIN t_material_plan tmp ON tmti.material_plan_id = tmp.material_plan_id
        INNER JOIN t_material tm ON tmti.material_id = tm.material_id
        INNER JOIN t_material_staff tms ON tm.material_id = tms.material_id
        WHERE tmp.grade_year=#{gradeYear}
        AND tmp.grade_term = #{gradeTerm}
        AND tm.school_id = #{schoolId}
        AND tmti.operation_status IN (2,4)
        <if test="staffId != null and staffId !=''">
            AND tms.staff_id = #{staffId}
        </if>
        <if test="search != null and search !=''">
            AND tmti.teacher_name LIKE CONCAT('%',#{search},'%')
        </if>
        GROUP BY tmti.teacher_id,tmti.material_id
    </select>

    <!--获取材料类别-->
    <select id="getMaterialPlanItem" parameterType="map" resultType="map">
        SELECT
        tmpi.material_id materialId,
        tmpi.material_name materialName,
        GROUP_CONCAT(DISTINCT ts.staff_name) staffName
        FROM t_material_plan_item tmpi
        INNER JOIN t_material_plan tmp ON tmpi.material_plan_id = tmp.material_plan_id
        INNER JOIN t_material_staff tms ON tmpi.material_id = tms.material_id
        LEFT JOIN t_staff ts ON ts.staff_id = tms.staff_id
        WHERE tmp.grade_year=#{gradeYear} AND tmp.grade_term = #{gradeTerm} AND tmp.school_id = #{schoolId}
        <if test="staffId != null and staffId !=''">
            AND tms.staff_id = #{staffId}
        </if>
        GROUP BY tmpi.material_id
    </select>

    <resultMap id="MaterialTeacherItem" type="map">
        <id column="teacherId" property="teacherId"/>
        <result column="teacherName" property="teacherName"/>
        <collection property="item"  javaType="list" ofType="map">
            <id column="materialId" property="materialId"/>
            <result column="materialName" property="materialName"/>
            <result column="operationStatus" property="operationStatus"/>
        </collection>
    </resultMap>

    <resultMap id="TeacherMaterialFile" type="map">
        <id column="teacherId" property="teacherId"/>
        <result column="teacherName" property="teacherName"/>
        <collection property="item"  javaType="list" ofType="map">
            <id column="materialFileId" property="materialFileId"/>
            <result column="materialRootId" property="materialRootId"/>
            <result column="fileUrl" property="fileUrl"/>
            <result column="fileName" property="fileName"/>
            <result column="uploadTime" property="uploadTime"/>
        </collection>
    </resultMap>

</mapper>