<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="RollPlanStudentCourseMapper">

    <!-- 保存学籍任务学生成绩 -->
    <insert id="insertRollPlanStudentCourse" parameterType="map" useGeneratedKeys="true">
        INSERT INTO t_roll_plan_student_course (
            roll_plan_id,
            roll_student_id,
            student_name,
            student_code,
            seat_number,
            class_id,
            class_name,
            `written_status`,
            `oral_status`,
            course_id,
            course_name,
            usual_score,
            oral_usual_score,
            midterm_exam_final_score,
            midterm_exam_written_score,
            terminal_exam_final_score,
            terminal_exam_written_score,
            term_written_score,
            term_written_score_level,
            first_term_oral_score,
            first_term_oral_score_level,
            first_term_written_score,
            first_term_written_score_level,
            first_term_final_score,
            first_term_final_score_level,
            year_final_score,
            year_final_score_level,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        VALUES
        <foreach collection="studentCourses" separator="," item="item">
        (
            #{rollPlanId},
            #{item.rollStudentId},
            #{item.studentName},
            #{item.studentCode},
            #{item.seatNumber},
            #{item.classId},
            #{item.className},
            #{item.writtenStatus},
            #{item.oralStatus},
            #{item.courseId},
            #{item.courseName},
            #{item.usualScore},
            #{item.oralUsualScore},
            #{item.midtermExamFinalScore},
            #{item.midtermExamWrittenScore},
            #{item.terminalExamFinalScore},
            #{item.terminalExamWrittenScore},
            #{item.termWrittenScore},
            #{item.termWrittenScoreLevel},
            #{item.firstTermOralScore},
            #{item.firstTermOralScoreLevel},
            #{item.firstTermWrittenScore},
            #{item.firstTermWrittenScoreLevel},
            #{item.firstTermFinalScore},
            #{item.firstTermFinalScoreLevel},
            #{item.yearFinalScore},
            #{item.yearFinalScoreLevel},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
        )
        </foreach>
    </insert>

    <!--获取任务学生数量-->
    <select id="getRollPlanStudentCourseCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_roll_plan_student_course trpsc
        INNER JOIN t_roll_plan_course_teacher trpct
        ON trpsc.roll_plan_id = trpct.roll_plan_id AND trpct.class_id = trpsc.class_id AND trpct.course_id = trpsc.course_id
        WHERE trpct.roll_plan_id = #{rollPlanId}
        <if test="classId != null and classId !=''">
            AND trpct.class_id = #{classId}
        </if>
        <if test="courseId != null and courseId !=''">
            AND trpct.course_id = #{courseId}
        </if>
        <if test="search != null and search !=''">
            AND (trpsc.student_name LIKE CONCAT('%',#{search},'%')
            OR trpsc.student_code LIKE CONCAT('%',#{search},'%'))
        </if>
    </select>


    <!--获取任务学生成绩-->
    <select id="getRollPlanStudentCourse" parameterType="map" resultType="map">
        SELECT
        trp.grade_term gradeTerm,
        trpsc.roll_plan_student_course_id rollPlanStudentCourseId,
        trpsc.roll_plan_id rollPlanId,
        trpsc.course_id courseId,
        trpsc.course_name courseName,
        trpsc.roll_student_id rollStudentId,
        trpsc.student_name studentName,
        trpsc.student_code studentCode,
        trpsc.class_id classId,
        trpsc.class_name className,
        ts.seat_number seatNumber,
        trpsc.oral_attendance_negative_score oralAttendanceNegativeScore,
        trpsc.oral_preview_negative_score oralPreviewNegativeScore,
        trpsc.oral_listen_negative_score oralListenNegativeScore,
        trpsc.oral_note_negative_score oralNoteNegativeScore,
        trpsc.oral_homework_negative_score oralHomeworkNegativeScore,
        trpsc.oral_posture_negative_score oralPostureNegativeScore,
        trpsc.oral_usual_score oralUsualScore,
        trpsc.attendance_negative_score attendanceNegativeScore,
        trpsc.preview_negative_score previewNegativeScore,
        trpsc.listen_negative_score listenNegativeScore,
        trpsc.note_negative_score noteNegativeScore,
        trpsc.homework_negative_score homeworkNegativeScore,
        trpsc.posture_negative_score postureNegativeScore,
        trpsc.usual_score usualScore,
        trpsc.midterm_exam_oral_score midtermExamOralScore,
        trpsc.midterm_exam_written_score midtermExamWrittenScore,
        trpsc.midterm_exam_final_score midtermExamFinalScore,
        trpsc.terminal_exam_oral_score terminalExamOralScore,
        trpsc.terminal_exam_written_score terminalExamWrittenScore,
        trpsc.terminal_exam_final_score terminalExamFinalScore,
        trpsc.term_oral_score termOralScore,
        trpsc.term_oral_score_level termOralScoreLevel,
        trpsc.term_written_score termWrittenScore,
        trpsc.term_written_score_level termWrittenScoreLevel,
        trpsc.term_final_score termFinalScore,
        trpsc.term_final_score_level termFinalScoreLevel,
        trpsc.first_term_oral_score firstTermOralScore,
        trpsc.first_term_oral_score_level firstTermOralScoreLevel,
        trpsc.first_term_written_score firstTermWrittenScore,
        trpsc.first_term_written_score_level firstTermWrittenScoreLevel,
        trpsc.first_term_final_score firstTermFinalScore,
        trpsc.year_oral_score yearOralScore,
        trpsc.year_oral_score_level yearOralScoreLevel,
        trpsc.year_written_score yearWrittenScore,
        trpsc.year_written_score_level yearWrittenScoreLevel,
        trpsc.year_final_score yearFinalScore,
        trpsc.year_final_score_level yearFinalScoreLevel,
        trpsc.resit_oral_score resitOralScore,
        trpsc.resit_oral_score_level resitOralScoreLevel,
        trpsc.resit_written_score resitWrittenScore,
        trpsc.resit_written_score_level resitWrittenScoreLevel,
        trpsc.resit_final_score resitFinalScore,
        trpsc.resit_final_score_level resitFinalScoreLevel,
        trpct.roll_plan_teacher_status rollPlanTeacherStatus,
        trpsc.written_status writtenStatus,
        trpsc.oral_status oralStatus
        FROM t_roll_plan_student_course trpsc
        INNER JOIN t_roll_plan_course_teacher trpct
        ON trpsc.roll_plan_id = trpct.roll_plan_id AND trpct.class_id = trpsc.class_id AND trpct.course_id = trpsc.course_id
        INNER JOIN t_roll_plan trp ON trpsc.roll_plan_id = trp.roll_plan_id
        INNER JOIN t_student ts on ts.school_id=trp.school_id
        INNER JOIN t_roll_student_relative  trsr ON ts.student_id = trsr.student_id
        INNER JOIN t_roll_student trs ON trs.roll_student_id = trsr.roll_student_id and trs.roll_student_id=trpsc.roll_student_id
        WHERE 1=1
        <if test="rollPlanId != null and rollPlanId !=''">
            AND trpsc.roll_plan_id = #{rollPlanId}
        </if>
        <if test="gradeType != null and gradeType !=''">
            AND trp.grade_type = #{gradeType}
        </if>
        <if test="classId != null and classId !=''">
            AND trpsc.class_id = #{classId}
        </if>
        <if test="classIds != null">
            AND trpsc.class_id IN
            <foreach collection="classIds" item="classId" separator="," close=")" open="(">
                #{classId}
            </foreach>
        </if>
        <if test="rollStudentId != null and rollStudentId !=''">
            AND trpsc.roll_student_id = #{rollStudentId}
        </if>
        <if test="courseId != null and courseId !=''">
            AND trpsc.course_id = #{courseId}
        </if>
        <if test="courseIds != null and courseIds !=''">
            AND trpsc.course_id IN
            <foreach collection="courseIdList" item="courseId" open="(" separator="," close=")">
                #{courseId}
            </foreach>
        </if>
        <if test="writtenStatus != null and writtenStatus !=''">
            AND trpsc.written_status = #{writtenStatus}
        </if>
        <if test="oralStatus != null and oralStatus !=''">
            AND trpsc.oral_status = #{oralStatus}
        </if>
        <if test="search != null and search !=''">
            AND (trpsc.student_name LIKE CONCAT('%',#{search},'%')
            OR trpsc.student_code LIKE CONCAT('%',#{search},'%'))
        </if>
        ORDER BY classId,courseId,studentCode
    </select>

    <!--获取学生成绩-->
    <select id="getRollStudentCourseExam" parameterType="map" resultType="map">
        SELECT
            first_term_oral_score firstTermOralScore,
            first_term_oral_score_level             firstTermOralScoreLevel,
            first_term_written_score             firstTermWrittenScore,
            first_term_written_score_level             firstTermWrittenScoreLevel,
            second_term_oral_score             secondTermOralScore,
            second_term_oral_score_level             secondTermOralScoreLevel,
            second_term_written_score             secondTermWrittenScore,
            second_term_written_score_level             secondTermWrittenScoreLevel,
            year_oral_score             yearOralScore,
            year_oral_score_level             yearOralScoreLevel,
            year_written_score             yearWrittenScore,
            year_written_score_level             yearWrittenScoreLevel,
            resit_oral_score             resitOralScore,
            resit_oral_score_level             resitOralScoreLevel,
            resit_written_score             resitWrittenScore,
            resit_written_score_level             resitWrittenScoreLevel
        FROM t_roll_student_exam
        WHERE roll_student_id = #{rollStudentId} and  grade_year = #{gradeYear}
        and course_id in
        <foreach collection="courseIdList" item="courseId" separator="," close=")" open="(">
            #{courseId}
        </foreach>
    </select>

    <!--获取任务学生全科成绩数量-->
    <select id="getRollPlanStudentCourseAllCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_roll_plan_student
        WHERE roll_plan_id = #{rollPlanId}
        <if test="classId != null and classId !=''">
            AND class_id = #{classId}
        </if>
        <if test="classIds != null">
            AND class_id IN
            <foreach collection="classIds" item="classId" separator="," close=")" open="(">
                #{classId}
            </foreach>
        </if>
        <if test="search != null and search !=''">
            AND (student_name LIKE CONCAT('%',#{search},'%')
            OR student_code LIKE CONCAT('%',#{search},'%'))
        </if>
    </select>

    <!--获取任务学生成绩全科-->
    <select id="getRollPlanStudentCourseAll" parameterType="map" resultMap="StudentCourseAll">
        SELECT
        trpsc.roll_plan_student_course_id rollPlanStudentCourseId,
        trpsc.course_id courseId,
        trpsc.course_name courseName,
        trpsc.roll_student_id rollStudentId,
        trpsc.student_name studentName,
        trpsc.student_code studentCode,
        trpsc.class_id classId,
        trpsc.class_name className,
        ts.seat_number seatNumber,
        trpsc.term_written_score termWrittenScore,
        trpsc.first_term_written_score firstTermWrittenScore,
        trpsc.year_written_score yearWrittenScore,
        trpsc.term_oral_score termOralScore,
        trpsc.first_term_oral_score firstTermOralScore,
        trpsc.year_oral_score yearOralScore,
        trpsc.year_oral_score_level yearOralScoreLevel,
        trpsc.year_written_score_level yearWrittenScoreLevel,
        trpct.roll_plan_teacher_status rollPlanTeacherStatus
        FROM t_roll_plan_student_course trpsc
        INNER JOIN
        (SELECT roll_plan_id, roll_student_id FROM t_roll_plan_student trps
        WHERE roll_plan_id = #{rollPlanId}
        <if test="classId != null and classId !=''">
            AND class_id = #{classId}
        </if>
        <if test="classIds != null">
            AND class_id IN
            <foreach collection="classIds" item="classId" separator="," close=")" open="(">
                #{classId}
            </foreach>
        </if>
        <if test="search != null and search !=''">
            AND (student_name LIKE CONCAT('%',#{search},'%')
            OR student_code LIKE CONCAT('%',#{search},'%'))
        </if>
        ORDER BY class_id, student_code
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
        ) t ON t.roll_plan_id = trpsc.roll_plan_id AND t.roll_student_id = trpsc.roll_student_id
        INNER JOIN t_roll_student trs ON trs.roll_student_id = trpsc.roll_student_id
        INNER JOIN t_roll_student_relative  trsr ON trs.roll_student_id = trsr.roll_student_id
        INNER JOIN t_student ts on ts.student_id = trsr.student_id
        INNER JOIN t_roll_plan_course_teacher trpct
        ON trpsc.roll_plan_id = trpct.roll_plan_id AND trpct.class_id = trpsc.class_id AND trpct.course_id = trpsc.course_id
        WHERE trpsc.roll_plan_id = #{rollPlanId}
    </select>

    <!--更新-->
    <update id="updateRollPlanStudentCourse" parameterType="map">
        UPDATE t_roll_plan_student_course
        <set>
            <if test="writtenStatus != null and writtenStatus!=0">
                written_status = #{writtenStatus},
            </if>
            <if test="oralStatus != null and oralStatus!=0">
                oral_status = #{oralStatus},
            </if>

            <if test="attendanceNegativeScore != null and courseType == 1 ">
                attendance_negative_score = #{attendanceNegativeScore},
            </if>
            <if test="previewNegativeScore != null and courseType == 1 ">
                preview_negative_score = #{previewNegativeScore},
            </if>
            <if test="listenNegativeScore != null and courseType == 1 ">
                listen_negative_score = #{listenNegativeScore},
            </if>
            <if test="noteNegativeScore != null and courseType == 1 ">
                note_negative_score = #{noteNegativeScore},
            </if>
            <if test="homeworkNegativeScore != null and courseType == 1 ">
                homework_negative_score = #{homeworkNegativeScore},
            </if>
            <if test="postureNegativeScore != null and courseType == 1">
                posture_negative_score = #{postureNegativeScore},
            </if>
            <if test="usualScore != null and courseType == 1 ">
                usual_score = #{usualScore},
            </if>

            <if test="attendanceNegativeScore != null and courseType == 2 ">
                oral_attendance_negative_score = #{attendanceNegativeScore},
            </if>
            <if test="previewNegativeScore != null and courseType == 2 ">
                oral_preview_negative_score = #{previewNegativeScore},
            </if>
            <if test="listenNegativeScore != null and courseType == 2">
                oral_listen_negative_score = #{listenNegativeScore},
            </if>
            <if test="noteNegativeScore != null and courseType ==  2">
                oral_note_negative_score = #{noteNegativeScore},
            </if>
            <if test="homeworkNegativeScore != null and courseType == 2">
                oral_homework_negative_score = #{homeworkNegativeScore},
            </if>
            <if test="postureNegativeScore != null and courseType == 2">
                oral_posture_negative_score = #{postureNegativeScore},
            </if>
            <if test="usualScore != null and courseType == 2">
                oral_usual_score = #{usualScore},
            </if>


            <if test="midtermExamFinalScore != null">
                midterm_exam_final_score = #{midtermExamFinalScore},
            </if>

            <if test="terminalExamFinalScore != null">
                terminal_exam_final_score = #{terminalExamFinalScore},
            </if>
            <if test="termOralScore != null">
                term_oral_score = #{termOralScore},
            </if>
            <if test="termOralScoreLevel != null">
                term_oral_score_level = #{termOralScoreLevel},
            </if>
            <if test="termWrittenScore != null">
                term_written_score = #{termWrittenScore},
            </if>
            <if test="termWrittenScoreLevel != null">
                term_written_score_level = #{termWrittenScoreLevel},
            </if>
            <if test="termFinalScore != null">
                term_final_score = #{termFinalScore},
            </if>
            <if test="termFinalScoreLevel != null">
                term_final_score_level = #{termFinalScoreLevel},
            </if>

            <if test="firstTermOralScore != null">
                first_term_oral_score = #{firstTermOralScore},
            </if>
            <if test="firstTermOralScoreLevel != null">
                first_term_oral_score_level = #{firstTermOralScoreLevel},
            </if>
            <if test="firstTermWrittenScore != null">
                first_term_written_score = #{firstTermWrittenScore},
            </if>
            <if test="firstTermWrittenScoreLevel != null">
                first_term_written_score_level = #{firstTermWrittenScoreLevel},
            </if>
            <if test="firstTermFinalScore != null">
                first_term_final_score = #{firstTermFinalScore},
            </if>
            <if test="firstTermFinalScoreLevel != null">
                first_term_final_score_level = #{firstTermFinalScoreLevel},
            </if>
            <if test="yearOralScore != null">
                year_oral_score = #{yearOralScore},
            </if>
            <if test="yearOralScoreLevel != null">
                year_oral_score_level = #{yearOralScoreLevel},
            </if>
            <if test="yearWrittenScore != null">
                year_written_score = #{yearWrittenScore},
            </if>
            <if test="yearWrittenScoreLevel != null">
                year_written_score_level = #{yearWrittenScoreLevel},
            </if>
            <if test="yearFinalScore != null">
                year_final_score = #{yearFinalScore},
            </if>
            <if test="yearFinalScoreLevel != null">
                year_final_score_level = #{yearFinalScoreLevel},
            </if>
            <if test="resitFinalScore != null">
                resit_final_score = #{resitFinalScore},
            </if>
            <if test="resitFinalScoreLevel != null">
                resit_final_score_level = #{resitFinalScoreLevel},
            </if>

            <if test="midtermExamOralScore != null">
                midterm_exam_oral_score = #{midtermExamOralScore},
            </if>
            <if test="midtermExamWrittenScore != null">
                midterm_exam_written_score = #{midtermExamWrittenScore},
            </if>

            <if test="terminalExamOralScore != null">
                terminal_exam_oral_score = #{terminalExamOralScore},
            </if>
            <if test="terminalExamWrittenScore != null">
                terminal_exam_written_score = #{terminalExamWrittenScore},
            </if>

            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        </set>
        WHERE roll_plan_student_course_id = #{rollPlanStudentCourseId}
    </update>

    <!--更新学生重考成绩-->
    <update id="updateStudentCourseResit" parameterType="map">
        UPDATE t_roll_student_exam
      <set>
          <if test="courseType == 1 and isNumeric == true ">
              resit_written_score = #{resitScore},
          </if>
          <if test="courseType == 2 and isNumeric == true ">
              resit_oral_score = #{resitScore},
          </if>
          <if test="courseType == 1 and isNumeric == false ">
              resit_written_score_level = #{resitScore},
              resit_written_score = NULL,
          </if>
          <if test="courseType == 2 and isNumeric == false ">
              resit_oral_score_level = #{resitScore},
              resit_oral_score = NULL,
          </if>
          modifier_id = #{userId},
          modifier_name = #{userName},
          modify_date_time = now()
      </set>
        WHERE roll_student_id = #{rollStudentId} and grade_year = #{gradeYear} and course_id = #{courseId}
    </update>

    <!-- 查找学生成绩 -->
    <select id="getRollPlanStudentCourseByStudentCode" parameterType="map" resultType="map">
        SELECT
        roll_plan_student_course_id rollPlanStudentCourseId,
        student_code studentCode,
        roll_plan_id rollPlanId,
        first_term_oral_score firstTermOralScore,
        first_term_written_score firstTermWrittenScore
        FROM t_roll_plan_student_course
        WHERE roll_plan_id = #{rollPlanId}
        AND student_code IN
        <foreach collection="studentCode" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND course_id = #{courseId}
    </select>

    <!-- 查找学生成绩 -->
    <select id="getRollPlanStudent" parameterType="map" resultType="map">
        SELECT
            roll_plan_student_course_id rollPlanStudentCourseId,
            course_id courseId,
            roll_student_id rollStudentId,
            student_code studentCode,
            midterm_exam_oral_score midtermExamOralScore,
            oral_usual_score oralUsualScore,
            usual_score usualScore,
            midterm_exam_written_score             midtermExamWrittenScore,
            terminal_exam_oral_score             terminalExamOralScore,
            terminal_exam_written_score             terminalExamWrittenScore,
            first_term_oral_score firstTermOralScore,
            first_term_written_score             firstTermWrittenScore
        FROM t_roll_plan_student_course
        WHERE roll_plan_id = #{rollPlanId}
    </select>

    <!---->
    <select id="getRollPlanTeacherClass" resultType="map" parameterType="map">
        SELECT
        class_id classId
        FROM t_roll_plan_course_teacher
        WHERE roll_plan_id = #{rollPlanId} AND teacher_id= #{teacherId} AND course_id = #{courseId}
    </select>

    <resultMap id="StudentCourseAll" type="map">
        <id column="rollStudentId" property="rollStudentId"></id>
        <result column="studentCode" property="studentCode"></result>
        <result column="studentName" property="studentName"></result>
        <result column="seatNumber" property="seatNumber"></result>
        <result column="classId" property="classId"></result>
        <result column="className" property="className"></result>
        <collection property="course" javaType="list" ofType="map">
            <id column="courseId" property="courseId"></id>
            <result column="courseName" property="courseName"></result>
            <result column="termWrittenScore" property="termWrittenScore"></result>
            <result column="firstTermWrittenScore" property="firstTermWrittenScore"></result>
            <result column="yearWrittenScore" property="yearWrittenScore"></result>
            <result column="termOralScore" property="termOralScore"></result>
            <result column="firstTermOralScore" property="firstTermOralScore"></result>
            <result column="yearOralScore" property="yearOralScore"></result>
            <result column="yearWrittenScoreLevel" property="yearWrittenScoreLevel"></result>
            <result column="yearOralScoreLevel" property="yearOralScoreLevel"></result>
            <result column="rollPlanTeacherStatus" property="rollPlanTeacherStatus"></result>
        </collection>
    </resultMap>


</mapper>