<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ThirdIdTransferMapper">
    <select id="getSchoolId" parameterType="list" resultType="map">
        SELECT
            school_id targetId,
            third_biz_id thirdBizId
        FROM t_school WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getIdentityCardId" parameterType="list" resultType="map">
        SELECT
        identity_card_id targetId,
        third_biz_id thirdBizId,
        identity_card_no_aes identityCardNoAes
        FROM t_identity_card WHERE identity_card_no_aes IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getIdentityCardIdByIdentityCardNoAes" parameterType="list" resultType="map">
        SELECT
        identity_card_id targetId,
        third_biz_id thirdBizId,
        identity_card_no_aes identityCardNoAes
        FROM t_identity_card WHERE identity_card_no_aes IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getGradeId" parameterType="list" resultType="map">
        SELECT
        grade_id targetId,
        third_biz_id thirdBizId
        FROM t_grade WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getClassId" parameterType="list" resultType="map">
        SELECT
        class_id targetId,
        third_biz_id thirdBizId
        FROM t_class WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getStudentId" parameterType="list" resultType="map">
        SELECT
        student_id targetId,
        third_biz_id thirdBizId
        FROM t_student WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getParentId" parameterType="list" resultType="map">
        SELECT
        parent_id targetId,
        third_biz_id thirdBizId
        FROM t_parent WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getParentStudentId" parameterType="list" resultType="map">
        SELECT
        parent_student_id targetId,
        third_biz_id thirdBizId
        FROM t_parent_student WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getTeacherId" parameterType="list" resultType="map">
        SELECT
        teacher_id targetId,
        third_biz_id thirdBizId
        FROM t_teacher WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getSchoolPrincipalId" parameterType="list" resultType="map">
        SELECT
        school_principal_id targetId,
        third_biz_id thirdBizId
        FROM t_school_principal WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getGradeDirectorId" parameterType="list" resultType="map">
        SELECT
        grade_director_id targetId,
        third_biz_id thirdBizId
        FROM t_grade_director WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getInstructorId" parameterType="list" resultType="map">
        SELECT
        instructor_id targetId,
        third_biz_id thirdBizId
        FROM t_instructor WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getEducationDirectorId" parameterType="list" resultType="map">
        SELECT
        education_director_id targetId,
        third_biz_id thirdBizId
        FROM t_education_director WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAccountId" parameterType="list" resultType="map">
        SELECT
        account_id targetId,
        account_name_aes accountNameAes
        FROM sys_account WHERE account_name_aes IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getCourseId" parameterType="list" resultType="map">
        SELECT
        course_id targetId,
        third_biz_id thirdBizId
        FROM t_course WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="getTagId" parameterType="list" resultType="map">
        SELECT
        tag_id targetId,
        third_biz_id thirdBizId
        FROM t_tag WHERE third_biz_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>