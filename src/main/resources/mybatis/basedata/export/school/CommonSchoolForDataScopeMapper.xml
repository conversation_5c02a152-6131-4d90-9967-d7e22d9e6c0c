<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="CommonSchoolForDataScopeMapper">

    <!-- PRODUCT(1, "product", "产品顾问" -->
    <select id="validSchoolIdsForDataScopeProduct" parameterType="map" resultType="long">
        SELECT DISTINCT tsm.school_id
        FROM sys_user su
        JOIN t_employee te
            ON te.employee_id = su.relative_id
        JOIN t_school_maintain tsm
            ON tsm.employee_id = te.employee_id
            AND tsm.maintain_status = 1
            AND tsm.school_id IN
            <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
                #{schoolId}
            </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- EXAMINER(13, "examiner", "联考员"  获取areaCode -->
    <select id="getExaminerAreaCode" parameterType="map" resultType="string">
        SELECT DISTINCT ta.area_code
        FROM sys_user su
        JOIN t_examiner te
            ON te.examiner_id = su.relative_id
        JOIN t_area ta
            ON ta.area_id = te.area_id
        WHERE su.user_id = #{userId}
        AND su.user_type = #{userType}
    </select>

    <!-- OPERATOR_REVIEWER(18, "operatorReviewer", "运营-审核员" -->
    <select id="validSchoolIdsForDataScopeOperatorReviewer" parameterType="map" resultType="long">
        SELECT DISTINCT tos.school_id
        FROM t_operator_school tos
        WHERE tos.user_id = #{userId}
          AND tos.operating_type = 1
          AND tos.school_id IN
          <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
              #{schoolId}
          </foreach>
    </select>

    <!-- OPERATOR_PROOFREADER(19, "operatorProofreader", "运营-校对员" -->
    <select id="validSchoolIdsForDataScopeOperatorProofreader" parameterType="map" resultType="long">
        SELECT DISTINCT tos.school_id
        FROM t_operator_school tos
        WHERE tos.user_id = #{userId}
          AND tos.operating_type = 2
          AND tos.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
    </select>

    <!-- SCHOOL_ADMIN(2, "schoolAdmin", "数据维护管理员" -->
    <select id="validSchoolIdsForDataScopeSchoolAdmin" parameterType="map" resultType="long">
        SELECT DISTINCT ts.school_id
        FROM sys_user su
        JOIN t_school ts
            ON ts.school_id = su.relative_id
            AND ts.school_id IN
            <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
                #{schoolId}
            </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- SCHOOL_TEACHER(3, "teacher", "老师" -->
    <select id="validSchoolIdsForDataScopeTeacher" parameterType="map" resultType="long">
        SELECT DISTINCT tt.school_id
        FROM sys_user su
        JOIN t_teacher tt
            ON tt.teacher_id = su.relative_id
            AND tt.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- SCHOOL_PRINCIPAL(9, "principal", "校长" -->
    <select id="validSchoolIdsForDataScopePrincipal" parameterType="map" resultType="long">
        SELECT DISTINCT tsp.school_id
        FROM sys_user su
        JOIN t_school_principal tsp
            ON tsp.school_principal_id = su.relative_id
            AND tsp.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- SCHOOL_DIRECTOR(10, "director", "教导主任" -->
    <select id="validSchoolIdsForDataScopeDirector" parameterType="map" resultType="long">
        SELECT DISTINCT tsp.school_id
        FROM sys_user su
        JOIN t_school_principal tsp
            ON tsp.school_principal_id = su.relative_id
            AND tsp.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- SCHOOL_GRADE_DIRECTOR(11, "gradeDirector", "年级主任" -->
    <select id="validSchoolIdsForDataScopeGradeDirector" parameterType="map" resultType="long">
        SELECT DISTINCT tg.school_id
        FROM sys_user su
        JOIN t_grade_director tgd
            ON tgd.grade_director_id = su.relative_id
        JOIN t_grade tg
        ON tgd.grade_id = tg.grade_id
        AND tg.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- SCHOOL_PREPARE_LEADER(12, "prepareLeader", "备课组长" -->
    <select id="validSchoolIdsForDataScopePrepareLeader" parameterType="map" resultType="long">
        SELECT DISTINCT tg.school_id
        FROM sys_user su
        JOIN t_grade_director tgd
            ON tgd.grade_director_id = su.relative_id
        JOIN t_grade tg
        ON tgd.grade_id = tg.grade_id
            AND tg.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- SCHOOL_INSTRUCTOR(15, "schoolInstructor", "教务管理员" -->
    <select id="validSchoolIdsForDataScopeSchoolInstructor" parameterType="map" resultType="long">
        SELECT DISTINCT tsp.school_id
        FROM sys_user su
        JOIN t_school_principal tsp
            ON tsp.school_principal_id = su.relative_id
            AND tsp.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- SCHOOL_COURSE_DIRECTOR(20, "schoolCourseDirector", "教研组长" -->
    <select id="validSchoolIdsForDataScopeSchoolCourseDirector" parameterType="map" resultType="long">
        SELECT DISTINCT tsp.school_id
        FROM sys_user su
        JOIN t_school_principal tsp
            ON tsp.school_principal_id = su.relative_id
            AND tsp.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- STUDENT(4, "student", "学生" -->
    <select id="validSchoolIdsForDataScopeStudent" parameterType="map" resultType="long">
        SELECT DISTINCT ts.school_id
        FROM sys_user su
        JOIN t_student ts
            ON ts.student_id = su.relative_id
            AND ts.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- STUDENT_PARENT(5, "parent", "家长" -->
    <select id="validSchoolIdsForDataScopeParent" parameterType="map" resultType="long">
        SELECT DISTINCT ts.school_id
        FROM sys_user su
        JOIN t_parent_student tps
            ON tps.parent_student_id = su.relative_id
        JOIN t_student ts
            ON ts.student_id = tps.student_id
            AND ts.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- EDUCATION_INSTRUCTOR(8, "instructor", "教研员"  获取areaCode -->
    <select id="getInstructorAreaCode" parameterType="map" resultType="string">
        SELECT DISTINCT ta.area_code
        FROM sys_user su
        JOIN t_instructor ti
            ON ti.instructor_id = su.relative_id
        JOIN t_area ta
            ON ta.area_id = ti.area_id
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- EDUCATION_DIRECTOR(14, "educDirector", "教育局长"  获取areaCode -->
    <select id="getEducDirectorAreaCode" parameterType="map" resultType="string">
        SELECT DISTINCT ta.area_code
        FROM sys_user su
        JOIN t_education_director ted
            ON ted.education_director_id = su.relative_id
        JOIN t_area ta
            ON ta.area_id = ted.area_id
        WHERE su.user_id = #{userId}
          AND su.user_type = #{userType}
    </select>

    <!-- 根据areaCode获取合法的学校 -->
    <select id="validSchoolIdsForDataScopeByAreaCode" parameterType="map" resultType="long">
        SELECT DISTINCT ts.school_id
        FROM t_area ta
        JOIN t_school ts
        ON ts.area_id = ta.area_id
        AND ts.school_id IN
        <foreach collection="schoolIdList" item="schoolId" separator="," open="(" close=")" >
            #{schoolId}
        </foreach>
        WHERE ta.area_code LIKE CONCAT(#{areaCode}, '%')
    </select>

</mapper>
