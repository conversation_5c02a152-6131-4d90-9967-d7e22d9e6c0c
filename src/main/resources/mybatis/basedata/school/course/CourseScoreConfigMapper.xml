<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="CourseScoreConfigMapper">

    <!-- 插入课程分数配置信息 -->
    <insert id="insertConfig" parameterType="map" useGeneratedKeys="true" keyProperty="courseScoreConfigId">
        INSERT INTO t_course_score_config (course_id,
                                           usually_ratio,
                                           exam_ratio,
                                           level_rules,
                                           creator_id,
                                           creator_name,
                                           create_date_time,
                                           modifier_id,
                                           modifier_name,
                                           modify_date_time)
        SELECT #{courseId},
               #{usuallyRatio},
               #{examRatio},
               #{levelRules},
               #{userId},
               #{userName},
               #{currentTime},
               #{userId},
               #{userName},
               #{currentTime}
        FROM dual
        WHERE NOT EXISTS(
                      SELECT * FROM t_course_score_config where course_id = #{courseId}
                )
    </insert>

    <!-- 更改课程分数配置信息  -->
    <update id="updateConfig" parameterType="map">
        UPDATE t_course_score_config
        SET usually_ratio    = #{usuallyRatio},
            exam_ratio       = #{examRatio},
            level_rules      = #{levelRules},
            modifier_id      = #{modifierId},
            modifier_name    = #{modifierName},
            modify_date_time = #{modifyDateTime}
        WHERE course_id = #{courseId}
    </update>

    <!-- 根据课程id 删除课程分数配置信息  -->
    <delete id="deleteConfig" parameterType="map">
        DELETE
        FROM t_course_score_config
        WHERE course_id = #{courseId}
    </delete>

    <!-- 根据课程id 查询课程分数配置信息  -->
    <select id="getConfig" parameterType="map" resultType="map">
        SELECT course_score_config_id courseScoreConfigId,
               course_id              courseId,
               usually_ratio          usuallyRatio,
               exam_ratio             examRatio,
               level_rules            levelRules
        FROM t_course_score_config
        WHERE course_id = #{courseId}
    </select>

</mapper>