<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="SchoolOperationManagementMapper">

    <sql id="BASE_SCHOOL_FIELD">
        ts.school_id schoolId,
        ts.school_name schoolName,
        ts.school_group_name schoolGroupName,
        ts.belong_type belongType,
        ts.school_ascription schoolAscription,
        ts.school_status schoolStatus,
        ts.member_type memberType,
        ts.create_date_time  createDateTime,
        ts.expire_datetime  expireDatetime,
        IF(ts.longitude = 0.0000000,'',ts.longitude) longitude,
        IF(ts.latitude = 0.0000000,'',ts.latitude) latitude,
        IFNULL(DATEDIFF(DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d'), DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 1 DAY),'%Y-%m-%d')), 0) remainingDays,
        tc.company_id companyId,
        IF(ts.dingding_serial_number IS NULL, '', ts.dingding_serial_number) dingdingSerialNumber,
        IF(tc.company_name IS NULL, '', tc.company_name) companyName,
        IF(t.tiku_enable IS NOT NULL , t.tiku_enable, 0) tikuEnable,
        IF(t.vip_enable IS NOT NULL , t.vip_enable, 0) vipEnable,
        IF(t.notebook_enable IS NOT NULL , t.notebook_enable, 0) notebookEnable
    </sql>

    <sql id="MAINTAIN_QUERY">
        <if test="employeeAuthStatus != null and employeeAuthStatus != ''">
            <if test="employeeAuthStatus != 4 and employeeAuthStatus != 5 and employeeAuthStatus != 2">
            INNER JOIN (SELECT
            tsm.school_id
            FROM
            t_school_maintain tsm
            WHERE
            tsm.maintain_status = 1 AND tsm.employee_id = #{employeeId}
                GROUP BY
                tsm.school_id
                ORDER BY tsm.school_id DESC
                ) tsm ON ts.school_id = tsm.school_id
            </if>

            <if test="employeeAuthStatus == 4 || employeeAuthStatus = 5">

            </if>
        </if>
    </sql>

    <sql id="MODULE_QUERY">
        LEFT JOIN (
        SELECT
        tmc.foreign_id,
        MAX( CASE `key` WHEN 'tiku_enable' THEN `status` ELSE 0 END ) AS tiku_enable,
        MAX( CASE `key` WHEN 'vip_enable' THEN `status` ELSE 0 END ) AS vip_enable,
        MAX( CASE `key` WHEN 'notebook_enable' THEN `status` ELSE 0 END ) AS notebook_enable
        FROM
        t_module_config tmc
        WHERE
        tmc.foreign_type = 1
        GROUP BY tmc.foreign_id
        ) AS t ON ts.school_id = t.foreign_id
    </sql>

    <!--查询学校运营管理 - 我的学校-->
    <!-- 查询区域下的学校（包含子区域） -->
    <select id="selectSchoolsByAreaCode" resultType="map" parameterType="map">
        SELECT
            <include refid="BASE_SCHOOL_FIELD"></include>
        FROM
        t_school ts
        <include refid="MAINTAIN_QUERY"></include>
        INNER JOIN t_area ta ON ts.area_id = ta.area_id
        LEFT JOIN t_company tc ON ts.company_id = tc.company_id
        <include refid="MODULE_QUERY"></include>
        <where>
            <if test="schoolIdList != null and schoolIdList.size() > 0">
                AND ts.school_id IN
                <foreach collection="schoolIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="belongType != null and belongType != ''">
                AND ts.belong_type = #{belongType}
            </if>

            <if test="schoolStatus != null and schoolStatus != ''">
                <if test="schoolStatus == 0">
                    AND ts.school_status = #{schoolStatus}
                </if>

                <if test="schoolStatus == 1">
                    AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
                </if>

                <if test="schoolStatus == 2">
                    AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
                </if>

                <if test="schoolStatus == 3">
                    AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
                </if>

            </if>

            <if test="expireDatetime != null and expireDatetime != '' and expireDatetime != null and expireDatetime != ''">
                AND  DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d') &lt;= #{expireDatetime}
            </if>

            <if test="schoolAscription != null and schoolAscription != ''">
                AND ts.school_ascription = #{schoolAscription}
            </if>

            <if test="tikuEnable != null and tikuEnable != ''">
                <if test="tikuEnable == 1">
                    AND t.tiku_enable = 1
                </if>
                <if test="tikuEnable == 0">
                    AND (t.tiku_enable = 0 OR t.tiku_enable IS NULL)
                </if>
            </if>

            <if test="vipEnable != null and vipEnable != ''">
                <if test="vipEnable == 0">
                    AND (t.vip_enable = #{vipEnable} OR t.vip_enable IS NULL )
                </if>

                <if test="vipEnable == 1">
                    AND t.vip_enable = #{vipEnable}
                </if>

                <if test="vipEnable == 2">
                    AND t.vip_enable = #{vipEnable}
                </if>
            </if>

            <if test="notebookEnable != null and notebookEnable != ''">
                <if test="notebookEnable == 0">
                    AND (t.notebook_enable = #{notebookEnable} OR t.notebook_enable IS NULL)
                </if>

                <if test="notebookEnable == 1">
                    AND (t.notebook_enable = #{notebookEnable})
                </if>
            </if>

            <if test="areaCode !=null and areaCode != ''">
                and ta.area_code like concat('',#{areaCode},'%')
            </if>

            <if test="keyword != null and keyword !=''">
                and (ts.school_name like concat('%',#{keyword},'%') OR ts.school_group_name LIKE CONCAT('%',#{keyword},'%'))
            </if>
        </where>



        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 查询区域下的学校总数（包含子区域） -->
    <select id="selectSchoolCountByAreaCode" resultType="long">
        SELECT
        count(*)
        FROM
        t_school ts
        <include refid="MAINTAIN_QUERY"></include>
        INNER JOIN t_area ta ON ts.area_id = ta.area_id
        LEFT JOIN t_company tc ON ts.company_id = tc.company_id
        <include refid="MODULE_QUERY"></include>
        <if test="stage !=null and stage != ''">
            inner join t_school_stage tss on ts.school_id = tss.school_id
        </if>
        <where>
            <if test="belongType != null and belongType != ''">
                AND ts.belong_type = #{belongType}
            </if>

            <if test="expireDatetime != null and expireDatetime != '' and expireDatetime != null and expireDatetime != ''">
                AND  DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d') &lt;= #{expireDatetime}
            </if>

            <if test="schoolStatus != null and schoolStatus != ''">
                <if test="schoolStatus == 0">
                    AND ts.school_status = #{schoolStatus}
                </if>

                <if test="schoolStatus == 1">
                    AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
                </if>

                <if test="schoolStatus == 2">
                    AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
                </if>

                <if test="schoolStatus == 3">
                    AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
                </if>

            </if>

            <if test="schoolAscription != null and schoolAscription != ''">
                AND ts.school_ascription = #{schoolAscription}
            </if>

            <if test="tikuEnable != null and tikuEnable != ''">
                <if test="tikuEnable == 1">
                    AND t.tiku_enable = 1
                </if>
                <if test="tikuEnable == 0">
                    AND (t.tiku_enable = 0 OR t.tiku_enable IS NULL)
                </if>

                <if test="vipEnable == 2">
                    AND t.vip_enable = #{vipEnable}
                </if>
            </if>

            <if test="vipEnable != null and vipEnable != ''">
                <if test="vipEnable == 0">
                    AND (t.vip_enable = #{vipEnable} OR t.vip_enable IS NULL )
                </if>

                <if test="vipEnable == 1">
                    AND t.vip_enable = #{vipEnable}
                </if>

                <if test="vipEnable == 2">
                    AND t.vip_enable = #{vipEnable}
                </if>
            </if>

            <if test="notebookEnable != null and notebookEnable != ''">
                <if test="notebookEnable == 0">
                    AND (t.notebook_enable = #{notebookEnable} OR t.notebook_enable IS NULL)
                </if>

                <if test="notebookEnable == 1">
                    AND (t.notebook_enable = #{notebookEnable})
                </if>
            </if>

            <if test="stage !=null and stage != ''">
                and tss.stage = #{stage}
            </if>

            <if test="areaCode !=null and areaCode != ''">
                and ta.area_code like concat('',#{areaCode},'%')
            </if>

            <if test="keyword != null and keyword !=''">
                and (ts.school_name like concat('%',#{keyword},'%') OR ts.school_group_name LIKE CONCAT('%',#{keyword},'%'))
            </if>
        </where>
    </select>

    <!-- 查询区域下的学校（不包含子区域） -->
    <select id="selectSchoolsByAreaId" resultType="map">
        SELECT
            <include refid="BASE_SCHOOL_FIELD"></include>
        FROM
        t_school ts
        <include refid="MAINTAIN_QUERY"></include>
        INNER JOIN t_area ta ON ts.area_id = ta.area_id
        LEFT JOIN t_company tc ON ts.company_id = tc.company_id
        <include refid="MODULE_QUERY"></include>
        <if test="stage !=null and stage != ''">
            inner join t_school_stage tss on ts.school_id = tss.school_id
        </if>
        where 1=1

        <if test="schoolIdList != null and schoolIdList.size() > 0">
            AND ts.school_id IN
            <foreach collection="schoolIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="belongType != null and belongType != ''">
            AND ts.belong_type = #{belongType}
        </if>

        <if test="expireDatetime != null and expireDatetime != '' and expireDatetime != null and expireDatetime != ''">
            AND  DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d') &lt;= #{expireDatetime}
        </if>

        <if test="schoolStatus != null and schoolStatus != ''">
            <if test="schoolStatus == 0">
                AND ts.school_status = #{schoolStatus}
            </if>

            <if test="schoolStatus == 1">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 2">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 3">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

        </if>

        <if test="schoolAscription != null and schoolAscription != ''">
            AND ts.school_ascription = #{schoolAscription}
        </if>

        <if test="tikuEnable != null and tikuEnable != ''">
            <if test="tikuEnable == 1">
                AND t.tiku_enable = 1
            </if>
            <if test="tikuEnable == 0">
                AND (t.tiku_enable = 0 OR t.tiku_enable IS NULL)
            </if>
        </if>

        <if test="vipEnable != null and vipEnable != ''">
            <if test="vipEnable == 0">
                AND (t.vip_enable = #{vipEnable} OR t.vip_enable IS NULL )
            </if>

            <if test="vipEnable == 1">
                AND t.vip_enable = #{vipEnable}
            </if>

            <if test="vipEnable == 2">
                AND t.vip_enable = #{vipEnable}
            </if>
        </if>

        <if test="notebookEnable != null and notebookEnable != ''">
            <if test="notebookEnable == 0">
                AND (t.notebook_enable = #{notebookEnable} OR t.notebook_enable IS NULL)
            </if>

            <if test="notebookEnable == 1">
                AND (t.notebook_enable = #{notebookEnable})
            </if>
        </if>

        <if test="stage !=null and stage != ''">
            and tss.stage = #{stage}
        </if>
        <if test="areaId != null and areaId != ''">
            and ts.area_id = #{areaId}
        </if>
        <if test="keyword != null and keyword !=''">
            and (ts.school_name like concat('%',#{keyword},'%') OR ts.school_group_name LIKE CONCAT('%',#{keyword},'%'))
        </if>



        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 查询区域下的学校（不包含子区域） -->
    <select id="selectSchoolCountByAreaId" resultType="long">
        SELECT
        count(*)
        FROM
        t_school ts
        <include refid="MAINTAIN_QUERY"></include>
        INNER JOIN t_area ta ON ts.area_id = ta.area_id
        LEFT JOIN t_company tc ON ts.company_id = tc.company_id
        <include refid="MODULE_QUERY"></include>
        <if test="stage !=null and stage != ''">
            inner join t_school_stage tss on ts.school_id = tss.school_id
        </if>
        where 1=1

        <if test="belongType != null and belongType != ''">
            AND ts.belong_type = #{belongType}
        </if>

        <if test="expireDatetime != null and expireDatetime != '' and expireDatetime != null and expireDatetime != ''">
            AND  DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d') &lt;= #{expireDatetime}
        </if>

        <if test="schoolStatus != null and schoolStatus != ''">
            <if test="schoolStatus == 0">
                AND ts.school_status = #{schoolStatus}
            </if>

            <if test="schoolStatus == 1">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 2">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 3">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

        </if>

        <if test="schoolAscription != null and schoolAscription != ''">
            AND ts.school_ascription = #{schoolAscription}
        </if>

        <if test="tikuEnable != null and tikuEnable != ''">
            <if test="tikuEnable == 1">
                AND t.tiku_enable = 1
            </if>
            <if test="tikuEnable == 0">
                AND (t.tiku_enable = 0 OR t.tiku_enable IS NULL)
            </if>
        </if>

        <if test="vipEnable != null and vipEnable != ''">
            <if test="vipEnable == 0">
                AND (t.vip_enable = #{vipEnable} OR t.vip_enable IS NULL )
            </if>

            <if test="vipEnable == 1">
                AND t.vip_enable = #{vipEnable}
            </if>

            <if test="vipEnable == 2">
                AND t.vip_enable = #{vipEnable}
            </if>
        </if>

        <if test="notebookEnable != null and notebookEnable != ''">
            <if test="notebookEnable == 0">
                AND (t.notebook_enable = #{notebookEnable} OR t.notebook_enable IS NULL)
            </if>

            <if test="notebookEnable == 1">
                AND (t.notebook_enable = #{notebookEnable})
            </if>
        </if>

        <if test="stage !=null and stage != ''">
            and tss.stage = #{stage}
        </if>
        <if test="areaId != null and areaId != ''">
            and ts.area_id = #{areaId}
        </if>
        <if test="keyword != null and keyword !=''">
            and (ts.school_name like concat('%',#{keyword},'%') OR ts.school_group_name LIKE CONCAT('%',#{keyword},'%'))
        </if>
    </select>

    <!-- 查询区域下的学校（包含子区域） -->
    <select id="selectSchools" resultType="map">
        SELECT
        <include refid="BASE_SCHOOL_FIELD"></include>
        FROM
        t_school ts
        <include refid="MAINTAIN_QUERY"></include>
        INNER JOIN t_area ta ON ts.area_id = ta.area_id
        LEFT JOIN t_company tc ON ts.company_id = tc.company_id
        <include refid="MODULE_QUERY"></include>
        <if test="stage !=null and stage != ''">
            inner join t_school_stage tss on ts.school_id = tss.school_id
        </if>
        where 1=1
        <if test="areaCodes != null and areaCodes.size() > 0">
            AND (
            <foreach collection="areaCodes" item="areaCode" separator=" OR ">
                ta.area_code LIKE CONCAT(#{areaCode}, '%')
            </foreach>
            )
        </if>
        <if test="schoolIdList != null and schoolIdList.size() > 0">
            AND ts.school_id IN
            <foreach collection="schoolIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="belongType != null and belongType != ''">
            AND ts.belong_type = #{belongType}
        </if>

        <if test="expireDatetime != null and expireDatetime != '' and expireDatetime != null and expireDatetime != ''">
            AND  DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d') &lt;= #{expireDatetime}
        </if>

        <if test="schoolStatus != null and schoolStatus != ''">
            <if test="schoolStatus == 0">
                AND ts.school_status = #{schoolStatus}
            </if>

            <if test="schoolStatus == 1">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 2">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 3">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

        </if>

        <if test="schoolAscription != null and schoolAscription != ''">
            AND ts.school_ascription = #{schoolAscription}
        </if>

        <if test="tikuEnable != null and tikuEnable != ''">
            <if test="tikuEnable == 1">
                AND t.tiku_enable = 1
            </if>
            <if test="tikuEnable == 0">
                AND (t.tiku_enable = 0 OR t.tiku_enable IS NULL)
            </if>
        </if>

        <if test="vipEnable != null and vipEnable != ''">
            <if test="vipEnable == 0">
                AND (t.vip_enable = #{vipEnable} OR t.vip_enable IS NULL )
            </if>

            <if test="vipEnable == 1">
                AND t.vip_enable = #{vipEnable}
            </if>

            <if test="vipEnable == 2">
                AND t.vip_enable = #{vipEnable}
            </if>
        </if>

        <if test="notebookEnable != null and notebookEnable != ''">
            <if test="notebookEnable == 0">
                AND (t.notebook_enable = #{notebookEnable} OR t.notebook_enable IS NULL)
            </if>

            <if test="notebookEnable == 1">
                AND (t.notebook_enable = #{notebookEnable})
            </if>
        </if>

        <if test="stage !=null and stage != ''">
            and tss.stage = #{stage}
        </if>
        <if test="keyword != null and keyword !=''">
            and (ts.school_name like concat('%',#{keyword},'%') OR ts.school_group_name LIKE CONCAT('%',#{keyword},'%'))
        </if>



        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 查询区域下的学校总数（包含子区域） -->
    <select id="selectSchoolCount" resultType="long">
        SELECT
        COUNT(*)
        FROM
        t_school ts
        <include refid="MAINTAIN_QUERY"></include>
        INNER JOIN t_area ta ON ts.area_id = ta.area_id
        LEFT JOIN t_company tc ON ts.company_id = tc.company_id
        <include refid="MODULE_QUERY"></include>
        <if test="stage !=null and stage != ''">
            inner join t_school_stage tss on ts.school_id = tss.school_id
        </if>
        where 1=1

        <if test="belongType != null and belongType != ''">
            AND ts.belong_type = #{belongType}
        </if>

        <if test="expireDatetime != null and expireDatetime != '' and expireDatetime != null and expireDatetime != ''">
            AND  DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d') &lt;= #{expireDatetime}
        </if>

        <if test="schoolStatus != null and schoolStatus != ''">
            <if test="schoolStatus == 0">
                AND ts.school_status = #{schoolStatus}
            </if>

            <if test="schoolStatus == 1">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 2">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

            <if test="schoolStatus == 3">
                AND (ts.school_status = 1 AND ts.member_type = #{schoolStatus})
            </if>

        </if>

        <if test="schoolAscription != null and schoolAscription != ''">
            AND ts.school_ascription = #{schoolAscription}
        </if>

        <if test="tikuEnable != null and tikuEnable != ''">
            <if test="tikuEnable == 1">
                AND t.tiku_enable = 1
            </if>
            <if test="tikuEnable == 0">
                AND (t.tiku_enable = 0 OR t.tiku_enable IS NULL)
            </if>
        </if>

        <if test="vipEnable != null and vipEnable != ''">
            <if test="vipEnable == 0">
                AND (t.vip_enable = #{vipEnable} OR t.vip_enable IS NULL )
            </if>

            <if test="vipEnable == 1">
                AND t.vip_enable = #{vipEnable}
            </if>

            <if test="vipEnable == 2">
                AND t.vip_enable = #{vipEnable}
            </if>
        </if>

        <if test="notebookEnable != null and notebookEnable != ''">
            <if test="notebookEnable == 0">
                AND (t.notebook_enable = #{notebookEnable} OR t.notebook_enable IS NULL)
            </if>

            <if test="notebookEnable == 1">
                AND (t.notebook_enable = #{notebookEnable})
            </if>
        </if>

        <if test="stage !=null and stage != ''">
            and tss.stage = #{stage}
        </if>
        <if test="keyword != null and keyword !=''">
            and (ts.school_name like concat('%',#{keyword},'%') OR ts.school_group_name LIKE CONCAT('%',#{keyword},'%'))
        </if>
    </select>

    <!--查询学校详情-->
    <select id="selectSchoolById" parameterType="map" resultType="map">
        select
            ts.school_id schoolId,
            ts.school_name schoolName,
            ts.school_group_name schoolGroupName,
            ts.school_phone schoolPhone,
            ts.school_phone_aes schoolPhoneAes,
            ts.school_status schoolStatus,
            ts.member_type memberType,
            ts.school_ascription schoolAscription,
            ts.expire_datetime expireDatetime,
            ts.address address,
            ts.belong_type belongType,
            ts.school_property schoolProperty,
            ts.school_clique schoolClique,
            ts.create_date_time  createDateTime,
            ta.area_id areaId,
            ta.area_code areaCode,
            ta.area_name areaName,
            IF(ts.longitude = 0.0000000,'',ts.longitude) longitude,
            IF(ts.latitude = 0.0000000,'',ts.latitude) latitude,
            tc.company_id  companyId,
            tc.company_name companyName
        from t_school ts
                 inner join t_area ta on ts.area_id = ta.area_id
                 LEFT JOIN t_company tc ON ts.company_id = tc.company_id
        where ts.school_id = #{schoolId}
    </select>


    <!--  公司列表  -->
    <select id="pageCompany" resultType="map" parameterType="map">
        SELECT
            tc.company_id companyId,
            tc.company_name companyName
        FROM
            t_company tc
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!--  统计公司列表  -->
    <select id="countCompany" resultType="int" parameterType="map">
        SELECT
        count(*) total
        FROM
        t_company tc
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!--产品顾问列表-->
    <select id="pageMaintain" resultType="map" parameterType="map">
        SELECT
        te.company_id companyId,
        tc.company_name companyName,
        te.employee_id employeeId,
        te.employee_name employeeName,
        te.employee_duty  employeeDuty,
        te.employee_phone  employeePhone,
        te.employee_phone_aes employeePhoneAes
        FROM
        t_employee AS te
        LEFT JOIN t_company tc ON te.company_id = tc.company_id
        LEFT JOIN sys_user AS su ON te.employee_id = su.relative_id
        AND su.user_type = 1
        WHERE
             1 = 1
            <if test="employeeAuthStatus != null and employeeAuthStatus != ''">
                AND te.employee_auth_status = #{employeeAuthStatus}
            </if>

            <if test="keyword != null and keyword != ''">
                AND te.employee_name LIKE CONCAT('%', #{keyword}, '%') OR tc.company_name LIKE CONCAT('%', #{keyword}, '%')
            </if>

            <if test="pageSize !=null and currentIndex != null">
                LIMIT #{currentIndex}, #{pageSize}
            </if>
    </select>

    <!--产品顾问列表统计-->
    <select id="countMaintain" resultType="int" parameterType="map">
        SELECT
        COUNT(*) total
        FROM
        t_employee AS te
        LEFT JOIN t_company tc ON te.company_id = tc.company_id
        LEFT JOIN sys_user AS su ON te.employee_id = su.relative_id
        AND su.user_type = 1
        WHERE
        1 =1
        <if test="employeeAuthStatus != null and employeeAuthStatus != ''">
            AND te.employee_auth_status = #{employeeAuthStatus}
        </if>

        <if test="keyword != null and keyword != ''">
            AND te.employee_name LIKE CONCAT('%', #{keyword}, '%') OR tc.company_name LIKE CONCAT('%', #{keyword}, '%')
        </if>
    </select>

    <sql id="newSchoolField">
        ts.school_id,
        ts.school_name,
        IF(t.review_enable IS NOT NULL , t.review_enable, 0) review_enable,
        IF(t.tiku_enable IS NOT NULL , t.tiku_enable, 0) tiku_enable,
        IF(t.jyeoo_register IS NOT NULL , t.jyeoo_register, 0) jyeoo_register,
        IF(t.xueke_enable IS NOT NULL , t.xueke_enable, 0) xueke_enable,
        IF(t.vip_enable IS NOT NULL , t.vip_enable, 0) vip_enable,
        IF(t.notebook_enable IS NOT NULL , t.notebook_enable, 0) notebook_enable
    </sql>




    <!--主查询-->
    <select id="pageNew" resultType="map" parameterType="map">
        SELECT
        <include refid="newSchoolField"></include>
        FROM
        t_school ts
        INNER JOIN t_area ta ON ts.area_id = ta.area_id
        LEFT JOIN t_company tc ON ts.company_id = tc.company_id

        LEFT JOIN (
        SELECT
        tmc.foreign_id,
        MAX( CASE `key` WHEN 'review_enable' THEN `status` ELSE 0 END ) AS review_enable,
        MAX( CASE `key` WHEN 'tiku_enable' THEN `status` ELSE 0 END ) AS tiku_enable,
        MAX( CASE `key` WHEN 'jyeoo_register' THEN `status` ELSE 0 END ) AS jyeoo_register,
        MAX( CASE `key` WHEN 'xueke_enable' THEN `status` ELSE 0 END ) AS xueke_enable,
        MAX( CASE `key` WHEN 'vip_enable' THEN `status` ELSE 0 END ) AS vip_enable,
        MAX( CASE `key` WHEN 'notebook_enable' THEN `status` ELSE 0 END ) AS notebook_enable
        FROM
        t_module_config tmc
        WHERE
        tmc.foreign_type = 1
        GROUP BY tmc.foreign_id
        ) AS t ON ts.school_id = t.foreign_id
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>


    <!-- 新增学校 -->
    <insert id="insertSchool" parameterType="map" useGeneratedKeys="true" keyProperty="schoolId" keyColumn="school_id">
        INSERT INTO t_school (
        school_name,
        school_group_name,
        stage,
        grade_number,
        school_phone,
        school_phone_aes,
        address,
        school_status,
        member_type,
        area_id,
        belong_type,
        school_property,
        school_ascription,
        expire_datetime,
        <if test="longitude !=null and longitude!=''">
            longitude,
        </if>
        <if test="latitude !=null and latitude!=''">
            latitude,
        </if>
        <if test="companyId != null and companyId != ''">
            company_id,
        </if>
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        ) VALUES (
        #{schoolName},
        #{schoolGroupName},
        #{stage},
        0,
        #{schoolPhone},
        #{schoolPhoneAes},
        #{address},
        #{schoolStatus},
        #{memberType},
        #{areaId},
        #{belongType},
        #{schoolProperty},
        #{schoolAscription},
        #{expireDatetime},
        <if test="longitude !=null and longitude!=''">
            #{longitude},
        </if>
        <if test="latitude !=null and latitude!=''">
            #{latitude},
        </if>
        <if test="companyId != null and companyId != ''">
            #{companyId},
        </if>
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
    </insert>

    <!--更新学校-->
    <update id="updateSchool" parameterType="map">
        UPDATE t_school
        SET
            school_status = #{schoolStatus},
            <if test="memberType != null and memberType != ''">
                member_type = #{memberType},
            </if>
            <if test="companyId != null and companyId != ''">
                company_id = #{companyId},
            </if>
            belong_type = #{belongType},
            school_name = #{schoolName},
            school_group_name = #{schoolGroupName},
            <if test="schoolPhone != null and schoolPhone != ''">
                school_phone = #{schoolPhone},
            </if>

            <if test="schoolPhoneAes != null and schoolPhoneAes != ''">
                school_phone_aes = #{schoolPhoneAes},
            </if>
            address = #{address},
            school_property = #{schoolProperty},

            <if test="longitude != null and longitude != ''">
                longitude = #{longitude},
            </if>

            <if test="longitude != null and longitude != ''">
                latitude = #{latitude},
            </if>

            school_ascription = #{schoolAscription},
            <if test="expireDatetime != null and expireDatetime != ''">
                expire_datetime = #{expireDatetime},
            </if>
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}

        WHERE
            school_id = #{schoolId}
    </update>

    <!--根据学校ID删除当前学校的顾问-->
    <delete id="deleteMaintainBySchoolId" parameterType="map">
        DELETE FROM t_school_maintain WHERE school_id = #{schoolId}
    </delete>

    <!--批量插入顾问-->
    <insert id="batchInsertMaintain" parameterType="map">
        INSERT INTO t_school_maintain (
            school_id,
            company_id,
            company_name,
            employee_id,
            employee_name,
            employee_duty,
            employee_phone,
            employee_phone_aes,
            primary_type,
            maintain_status,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        VALUES
        <foreach collection="maintainList"  item="item" separator="," >
            (
            #{schoolId},
            #{item.companyId},
            #{item.companyName},
            #{item.employeeId},
            #{item.employeeName},
            #{item.employeeDuty},
            #{item.employeePhone},
            #{item.employeePhoneAes},
            #{item.primaryType},
            1,
            #{userId}, #{userName}, #{currentTime},
            #{userId}, #{userName}, #{currentTime}
            )
        </foreach>
    </insert>

    <!--关闭到期的学校-->
    <update id="closeExpireSchool" parameterType="map">
        UPDATE t_school
        SET school_status = 0,
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE
            school_status != 0
            AND DATE_FORMAT(expire_datetime ,'%Y-%m-%d') = DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 1 DAY),'%Y-%m-%d')
    </update>

    <!--查询需要关闭的学校-->
    <select id="selectCloseExpireSchool" parameterType="map" resultType="map">
        SELECT
            school_id schoolId,
            school_status schoolStatus,
            create_date_time    createDateTime,
            expire_datetime     expireDatetime
        FROM
            t_school
        WHERE
            school_status != 0
        AND DATE_FORMAT( expire_datetime, '%Y-%m-%d' ) = DATE_FORMAT(DATE_SUB( NOW(), INTERVAL 1 DAY ),'%Y-%m-%d')
    </select>

    <!--查询批量开通vip的学校-->
    <select id="selectExpireVipList" resultType="map">
        SELECT
            tmc.module_config_id  moduleConfigId,
            tmc.`value`  value
        FROM
            t_module_config tmc
        WHERE
            tmc.`key` = 'vip_enable'
          AND `status` = 2;
    </select>

    <!--关闭vip-->
    <update id="closeSchoolVip" parameterType="list">
        UPDATE t_module_config SET `status` = 0 WHERE module_config_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectDuplicateSchool" resultType="map" parameterType="map">
        SELECT
            ts.area_id areaId,
            a.area_name areaName,
            ts.school_name schoolName
        FROM t_school ts join t_area a on ts.area_id = a.area_id
        WHERE
              ts.area_id = #{areaId}
          AND ts.school_name = #{schoolName}
          <if test="schoolId != null and schoolId != ''">
              AND ts.school_id != #{schoolId}
          </if>
    </select>

    <!--查询申报学校是否重复-->
    <select id="selectDuplicateSchoolByAreaIds" parameterType="map" resultType="map">
        SELECT
        area_id areaId,
        area_name areaName,
        school_name schoolName
        FROM t_school
        WHERE
        area_id in
        <foreach collection="areaIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>>
        AND school_name = #{schoolName}
        <if test="schoolId != null and schoolId != ''">
            AND school_id != #{schoolId}
        </if>
    </select>

    <select id="selectEmployeeByPhone" resultType="map" parameterType="map">
        SELECT
            te.employee_id  employeeId,
            te.company_id  companyId,
            te.employee_name  employeeName,
            te.employee_duty  employeeDuty,
            te.employee_phone  employeePhone,
            te.employee_phone_aes  employeePhoneAes,
            tc.company_name  companyName
        FROM
            t_employee AS te
                INNER JOIN t_company AS tc ON te.company_id = tc.company_id
        WHERE
            te.employee_name = #{employeeName}
          AND te.employee_phone_aes = #{employeePhoneAes}
    </select>

    <select id="selectAreaIdExist" parameterType="map" resultType="map">
        SELECT area_id FROM t_area ta WHERE ta.area_id = #{areaId}
    </select>

    <!--查询重复的账号-->
    <select id="selectDuplicateAccount" parameterType="map" resultType="map">
        SELECT
             sa.account_id
        FROM
             sys_account sa
        WHERE
             sa.account_name_aes = #{schoolPhoneAes} AND sa.account_id NOT IN
        <foreach collection="accountIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!--根据accountNameAes查询账号信息-->
    <select id="getBaseAccountInfoBySchoolId" resultType="map" parameterType="map">
        SELECT
            sa.account_id  accountId,
            sa.account_name  accountName,
            sa.account_name_aes  accountNameAes
        FROM
            sys_account AS sa
                INNER JOIN
            t_school AS ts
            ON
                sa.account_name_aes = ts.school_phone_aes
        WHERE
            ts.school_id = #{schoolId}
    </select>

    <!--根据schoolId查询账号信息-->
    <select id="getBaseAccountBySchoolId" resultType="map" parameterType="map">
        SELECT
            su.user_id userId,
            su.user_name userName,
            sa.account_id  accountId,
            sa.account_name  accountName,
            sa.account_name_aes  accountNameAes
        FROM
            sys_user su

                INNER JOIN sys_account sa ON su.account_id = sa.account_id
        WHERE
            su.user_type = 2
          AND su.relative_id = #{schoolId}
    </select>

    <!--查询学校开通剩余时间-->
    <select id="selectRemainingTime" resultType="map" parameterType="map">
        SELECT
            IFNULL(DATEDIFF(DATE_FORMAT(ts.expire_datetime,'%Y-%m-%d'), DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 1 DAY),'%Y-%m-%d')), 0) remainingDays
        FROM
        t_school ts
        WHERE ts.school_id = #{schoolId}
    </select>
    

    <!--查询学校的所有维护人员-->
    <select id="selectSchoolMaintainList" resultType="map" parameterType="map">
        SELECT
            school_id schoolId,
            employee_id employeeId
        FROM
            t_school_maintain
    </select>

    <!--批量开通vip的学校-->
    <select id="selectBatchOpenSchool" resultType="map">
        SELECT
        tmc.foreign_id schoolId,
        tmc.`value` vipDeadline
        FROM
        t_module_config tmc
        INNER JOIN t_school ts ON ts.school_id = tmc.foreign_id
        WHERE
        tmc.foreign_type = 1
        AND tmc.`status` = 2
        AND tmc.`key` = 'vip_enable'
        AND ts.school_status = 1
          <if test="schoolId != null and schoolId != ''">
              AND tmc.foreign_id = #{schoolId}
          </if>
    </select>

    <!--批量开通vip的学生-->
    <select id="selectBatchOpenStudent" resultType="map" parameterType="list">
        SELECT
            ts.student_id   studentId,
            ts.school_id    schoolId,
            sum.status      status,
            sum.end_time    endTime
        FROM
            t_student ts
        JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        JOIN t_class tc ON tc.class_id = tcs.class_id AND tc.class_type = 1
        JOIN t_grade tg ON tc.grade_id = tg.grade_id AND tg.graduate_status = 0
        LEFT JOIN sys_user_membership `sum` ON ts.student_id = sum.relative_id
        WHERE
        ts.student_status = 1 AND ts.school_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!--更新或者插入学生vip-->
    <insert id="insertBatchOpenStudent" parameterType="map">
        INSERT INTO `sys_user_membership` (
            `start_time`,
            `end_time`,
            `status`,
            `relative_id`,
            `creator_id`,
            `creator_name`,
            `create_date_time`,
            `modifier_id`,
            `modifier_name`,
            `modify_date_time`
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.startTime},
            #{item.endTime},
            1,
            #{item.studentId},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        `status`=VALUES(status),
        end_time=VALUES(end_time),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!--判断是否是懂你的销售-->
    <select id="dongniSales" resultType="map" parameterType="map">
        SELECT
            te.employee_id employeeId,
            te.employee_name employeeName,
            te.employee_phone employeePhone,
            te.employee_duty employeeDuty,
            te.employee_phone_aes employeePhoneAes,
            te.employee_auth_status employeeAuthStatus,
            tc.company_id companyId,
            tc.company_name companyName,
            su.user_id userId
        FROM
            t_employee te
                INNER JOIN sys_user su ON te.employee_id = su.relative_id
                AND su.user_type = #{userType}
                INNER JOIN sys_account sa ON sa.account_id = su.account_id
                INNER JOIN t_company tc ON tc.company_id = te.company_id
        WHERE
            su.user_id = #{userId} AND (tc.company_name LIKE '%懂你%' OR tc.company_name LIKE '%和气聚力%')
    </select>

</mapper>