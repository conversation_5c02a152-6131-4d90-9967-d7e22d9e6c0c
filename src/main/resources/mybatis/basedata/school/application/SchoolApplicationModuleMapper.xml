<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="SchoolApplicationModuleMapper">

    <insert id="batchInsertApplicationModule" parameterType="map">
        INSERT INTO t_school_application_module (
            school_application_id,
            school_name,
            `key`,
            `name`,
            `status`,
            arts_science,
            creator_id,
            creator_name,
            modifier_id,
            modifier_name
        ) VALUES
        <foreach collection="moduleConfigList" item="item" separator=",">
            (
            #{schoolApplicationId},
            #{schoolName},
            #{item.key},
            #{item.name},
            #{item.status},
            0,
            #{userId}, #{userName},
            #{userId}, #{userName}
            )
        </foreach>
    </insert>


    <select id="listSchoolApplicationModule" resultType="map" parameterType="map">
        SELECT
            tsam.`key` `key`,
            tsam.`name` `name`,
            tsam.`status` `status`
        FROM
            t_school_application_module AS tsam
        WHERE tsam.school_application_id = #{schoolApplicationId}
    </select>

    <delete id="deleteModuleBySchoolApplicationId" parameterType="map">
        DELETE FROM t_school_application_module WHERE school_application_id = #{schoolApplicationId}
    </delete>

    <insert id="batchInsertModuleList" parameterType="list">
        INSERT INTO t_school_application_module (
        school_application_id,
        school_name,
        `key`,
        `name`,
        `status`,
        arts_science,
        creator_id,
        creator_name,
        modifier_id,
        modifier_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.schoolApplicationId},
            #{item.schoolName},
            #{item.key},
            #{item.name},
            #{item.status},
            0,
            #{item.userId}, #{item.userName},
            #{item.userId}, #{item.userName}
            )
        </foreach>
    </insert>

    <!--同步学校模块-->
    <insert id="insertSyncSchoolModuleConfig" parameterType="map">
        INSERT INTO t_school_application_module (
        school_application_id,
        school_name,
        `key`,
        `name`,
        `status`,
        arts_science,
        creator_id,
        creator_name,
        modifier_id,
        modifier_name
        ) VALUES
        <foreach collection="moduleConfigList" item="item" separator=",">
            (
            #{schoolApplicationId},
            #{schoolName},
            #{item.key},
            #{item.name},
            #{item.status},
            0,
            1, '同步管理员',
            1, '同步管理员'
            )
        </foreach>
    </insert>
</mapper>