<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="StudentMapper">


    <!-- 获取注册学生数总数-->
    <select id="getRegisterCount" parameterType="map" resultType="int">
        SELECT COUNT(su.user_id) "注册学生数"
        FROM t_student ts
                 INNER JOIN sys_user su ON ts.student_id = su.relative_id AND su.user_type IN (4, 5)
        WHERE su.create_date_time >= #{startDate}
          AND su.create_date_time &lt;= #{endDate}
          AND ts.school_id NOT IN (6, 15, 28, 29, 39, 41, 52, 81)
    </select>

    <!--获取指定的学校的所有学生总数-->
    <select id="getAllStudentCountBySchoolIdList" parameterType="list" resultType="int">
        SELECT COUNT(*)
        FROM t_student tt
        WHERE tt.school_id IN
        <foreach collection="list" item="schoolId" separator="," open="(" close=")">
            #{schoolId}
        </foreach>
    </select>


    <!-- 学生信息 -->
    <resultMap id="Student" type="map" extends="SchoolCommonMapper.Student">
        <result column="class_id" property="classId"/>
        <result column="class_name" property="className"/>
        <result column="class_sort" property="classSort"/>
        <result column="tag_id" property="tagId"/>
        <result column="tag_name" property="tagName"/>
        <result column="student_phone" property="studentPhone"/>
        <result column="student_phone_aes" property="studentPhoneAes"/>
        <result column="student_no" property="studentNo"/>
        <result column="student_num" property="studentNum"/>
        <result column="seat_number" property="seatNumber"/>
        <result column="candidate" property="candidate"/>
        <result column="class_student_id" property="classStudentId"/>
        <result column="course_selection_group_id" property="courseSelectionGroupId"/>
        <result column="course_selection_group_name" property="courseSelectionGroupName"/>
        <result column="foreign_course_id" property="foreignCourseId"/>
        <result column="course_name" property="foreignCourseName"/>
        <result column="arts_science" property="artsScience"/>
    </resultMap>

    <!-- 获取学生用户信息 -->
    <select id="getStudentUser" parameterType="map" resultType="map">
        SELECT
        ts.school_id schoolId,
        ts.student_id studentId,
        ts.student_name studentName,
        su.user_id userId
        FROM t_student ts
        INNER JOIN t_school tc ON ts.school_id = tc.school_id
        INNER JOIN sys_user su ON ts.student_id = su.relative_id AND su.user_type = 4
        WHERE ts.student_id IN
        <foreach collection="studentIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!-- 根据studentIds获取studentId->identityCardNoAes映射表 -->
    <select id="getStudentIdentityCardNoAesByStudentIds" parameterType="list" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.identity_card_no_aes identityCardNoAes
        FROM t_student ts
        WHERE ts.student_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!-- 获取学生账户 -->
    <select id="getStudentAccount" parameterType="map" resultType="string">
        SELECT su.account_id
        FROM sys_user su
        WHERE su.relative_id = #{studentId}
          and su.user_type = 4
    </select>

    <!-- 根据学生ID 获取学生信息 -->
    <select id="getStudentById" parameterType="map" resultType="map">
        SELECT ts.student_id        studentId,
               ts.school_id         schoolId,
               ts.student_num       studentNum,
               ts.student_name      studentName,
               ts.student_phone     studentPhone,
               ts.student_phone_aes studentPhoneAes,
               ts.student_status    studentStatus,
               tsc.school_name      schoolName,
               tsc.area_id          areaId,
               ts.third_party_id    thirdPartyId,
               ts.third_biz_id      thirdStudentId,
               tsc.third_party_id   schoolThirdPartyId
        FROM t_student ts
                 JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE ts.student_id = #{studentId}
    </select>

    <select id="getStudentListByIds" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.school_id schoolId,
        ts.student_num studentNum,
        ts.student_name studentName,
        ts.student_phone studentPhone,
        ts.student_phone_aes studentPhoneAes,
        tsc.school_name schoolName,
        ts.third_biz_id thirdBizId,
        ts.third_party_id thirdPartyId,
        tsc.third_party_id schoolThirdPartyId
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE
        ts.student_id IN
        <foreach collection="studentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="studentStatus != null">
            AND ts.student_status = #{studentStatus}
        </if>
    </select>


    <!--获取学生列表-->
    <select id="getStudent" parameterType="map" resultType="long">
        SELECT /*+INL_JOIN(tsc,tgd,tc,tcs,ts)*/
        ts.student_id FROM
        t_school tsc
        JOIN t_grade tgd ON tgd.school_id = tsc.school_id
        JOIN t_class tc ON tc.grade_id = tgd.grade_id
        JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        join t_student ts ON ts.student_id = tcs.student_id
        LEFT JOIN t_roll_student_relative trsr ON trsr.student_id = ts.student_id
        LEFT JOIN t_roll_student trs ON trs.roll_student_id = trsr.roll_student_id
        LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
        LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        LEFT JOIN sys_user su ON ts.student_id = su.relative_id AND su.user_type = 4
        LEFT JOIN sys_account sa ON su.account_id = sa.account_id
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE
        ts.student_status = '1' AND tgd.graduate_status = 0
        <if test="schoolId != null and schoolId != ''">
            AND tsc.school_id = #{schoolId}
        </if>
        <if test="schoolIds != null and schoolIds.size() > 0">
            AND tsc.school_id in
            <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
                #{schoolId}
            </foreach>
        </if>
        <if test="gradeId != null and gradeId != ''">
            AND tgd.grade_id = #{gradeId}
        </if>
        <if test="gradeId == null or gradeId == ''">
            <if test="stage != null and stage != ''">
                AND tgd.stage = #{stage}
            </if>
        </if>
        <if test="gradeType != null and gradeType != ''">
            AND tgd.grade_type = #{gradeType}
            <if test="graduateStatus!=null and graduateStatus !=''">
                AND tgd.graduate_status = #{graduateStatus}
            </if>
        </if>
        <if test="classId != null and classId != ''">
            AND tc.class_id = #{classId}
        </if>
        <if test="classType != null and classType != ''">
            AND tc.class_type = #{classType}
        </if>
        <if test="tagId != null and tagId != ''">
            AND tg.tag_id = #{tagId}
        </if>
        <if test="foreignCourseId != null and foreignCourseId != ''">
            AND tscs.foreign_course_id = #{foreignCourseId}
        </if>
        <if test="courseSelectionGroupId != null and courseSelectionGroupId != ''">
            AND tscs.course_selection_group_id = #{courseSelectionGroupId}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            OR ts.student_name_pinyin LIKE #{search}
            OR ts.candidate LIKE #{search}
            )
        </if>
        GROUP BY ts.student_id
        <if test="sortField != null and sortType != null">
            ORDER BY ${sortField} ${sortType}
        </if>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--获取学生列表-->
    <select id="getBaseStudentCount"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.bean.req.BaseDataRequest"
            resultType="int">
        SELECT /*+INL_JOIN(tsc,tgd,tc,tcs,ts)*/
        count(distinct ts.student_id) FROM
        t_school tsc
        JOIN t_grade tgd ON tgd.school_id = tsc.school_id
        JOIN t_class tc ON tc.grade_id = tgd.grade_id
        JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        join t_student ts ON ts.student_id = tcs.student_id
        <if test="tagId != null">
            LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
            LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        </if>
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE
        ts.student_status = #{userStatus} AND tgd.graduate_status = 0
        AND tsc.school_id = #{schoolId}
        <if test="gradeId != null">
            AND tgd.grade_id = #{gradeId}
        </if>
        <if test="classId != null">
            AND tc.class_id = #{classId}
        </if>
        <if test="tagId != null">
            AND tg.tag_id = #{tagId}
        </if>
        <if test="foreignCourseId != null and foreignCourseId != ''">
            AND tscs.foreign_course_id = #{foreignCourseId}
        </if>
        <if test="courseSelectionGroupId != null and courseSelectionGroupId != ''">
            AND tscs.course_selection_group_id = #{courseSelectionGroupId}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            OR ts.student_name_pinyin LIKE #{search}
            OR ts.candidate LIKE #{search}
            )
        </if>

    </select>


    <!--获取学生列表-->
    <select id="getBaseStudent"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.bean.req.BaseDataRequest"
            resultType="long">
        SELECT /*+INL_JOIN(tsc,tgd,tc,tcs,ts)*/
        distinct ts.student_id FROM
        t_school tsc
        JOIN t_grade tgd ON tgd.school_id = tsc.school_id
        JOIN t_class tc ON tc.grade_id = tgd.grade_id
        JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        join t_student ts ON ts.student_id = tcs.student_id
        <if test="tagId != null">
            LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
            LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        </if>
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE
        ts.student_status = #{userStatus} AND tgd.graduate_status = 0
        AND tsc.school_id = #{schoolId}
        <if test="gradeId != null">
            AND tgd.grade_id = #{gradeId}
        </if>
        <if test="classId != null">
            AND tc.class_id = #{classId}
        </if>
        <if test="tagId != null">
            AND tg.tag_id = #{tagId}
        </if>
        <if test="foreignCourseId != null and foreignCourseId != ''">
            AND tscs.foreign_course_id = #{foreignCourseId}
        </if>
        <if test="courseSelectionGroupId != null and courseSelectionGroupId != ''">
            AND tscs.course_selection_group_id = #{courseSelectionGroupId}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            OR ts.student_name_pinyin LIKE #{search}
            OR ts.candidate LIKE #{search}
            )
        </if>
        <if test="sortField == null or sortType == null">
            order by ts.student_id desc
        </if>
        <if test="sortField != null and sortType != null">
            ORDER BY ${sortField} ${sortType}
        </if>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--获取学生列表-->
    <select id="getBaseDataStudent"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.bean.req.BaseDataClassStudentRequest"
            resultType="com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataStudent">
        SELECT
        ts.student_id studentId,
        ts.student_num studentNum,
        ts.student_name studentName,
        ts.student_phone studentPhone,
        ts.student_phone_aes studentPhoneAes,
        ts.student_no studentNo,
        ts.seat_number seatNumber,
        ts.school_id schoolId,
        ts.student_status studentStatus,
        ts.candidate candidate
        FROM t_student ts
        WHERE ts.student_id IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>

    <!--获取学生列表-->
    <select id="getBaseDataStudentDetail"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.bean.req.BaseDataClassStudentRequest"
            resultType="com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataStudentDetail">
        SELECT
        ts.student_id studentId,
        trs.student_code studentCode,
        GROUP_CONCAT(DISTINCT tst.tag_id ORDER BY tst.tag_id ASC SEPARATOR ',') tag_id,
        GROUP_CONCAT(DISTINCT tg.tag_name ORDER BY tst.tag_id ASC SEPARATOR ',') tag_name,
        tcsg.course_selection_group_id courseSelectionGroupId,
        tcsg.course_selection_group_name courseSelectionGroupName,
        tscs.foreign_course_id foreignCourseId,
        tco.course_name courseName
        FROM t_student ts
        LEFT JOIN t_roll_student_relative trsr ON trsr.student_id = ts.student_id
        LEFT JOIN t_roll_student trs ON trs.roll_student_id = trsr.roll_student_id
        LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
        LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE ts.student_id IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by ts.student_id
        <if test="sortField != null and sortType != null">
            ORDER BY ts.${sortField} ${sortType}
        </if>
    </select>

    <!--获取学生列表-->
    <select id="getBaseStudentDetail" parameterType="map" resultMap="Student">
        SELECT
        ts.student_id,
        ts.student_num,
        ts.student_name,
        ts.student_phone ,
        ts.student_phone_aes,
        ts.student_no ,
        ts.seat_number ,
        ts.candidate,
        tsc.school_name schoolName,
        tsc.school_id schoolId,
        tcs.class_student_id,
        tc.arts_science,
        trs.student_code studentCode,
        sa.account_name_aes studentAccountNameAes,
        GROUP_CONCAT(DISTINCT tc.class_id ORDER BY tc.class_id ASC SEPARATOR ',') class_id,
        GROUP_CONCAT(DISTINCT tc.class_name ORDER BY tc.class_id ASC SEPARATOR ',') class_name,
        GROUP_CONCAT(DISTINCT tc.class_sort SEPARATOR ',') class_sort,
        GROUP_CONCAT(DISTINCT tst.tag_id ORDER BY tst.tag_id ASC SEPARATOR ',') tag_id,
        GROUP_CONCAT(DISTINCT tg.tag_name ORDER BY tst.tag_id ASC SEPARATOR ',') tag_name,
        tcsg.course_selection_group_id,
        tcsg.course_selection_group_name,
        tscs.foreign_course_id,
        tco.course_name
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        LEFT JOIN t_school tsc ON ts.school_id = tsc.school_id
        LEFT JOIN t_roll_student_relative trsr ON trsr.student_id = ts.student_id
        LEFT JOIN t_roll_student trs ON trs.roll_student_id = trsr.roll_student_id
        LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
        LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        LEFT JOIN sys_user su ON ts.student_id = su.relative_id AND su.user_type = 4
        LEFT JOIN sys_account sa ON su.account_id = sa.account_id
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE ts.student_id IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="classId != null and classId != ''">
            AND tc.class_id = #{classId}
        </if>
        <if test="classType != null and classType != ''">
            AND tc.class_type = #{classType}
        </if>
        <if test="tagId != null and tagId != ''">
            AND tg.tag_id = #{tagId}
        </if>
        GROUP BY ts.student_id
        <if test="sortField != null and sortType != null">
            ORDER BY ${sortField} ${sortType}
        </if>

    </select>


    <select id="getStudentId" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
        LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        WHERE tc.grade_id = #{gradeId} AND ts.student_status = '1'
        <if test="classId != null and classId != ''">
            AND tc.class_id = #{classId}
        </if>
        <if test="tagId != null and tagId != ''">
            AND tg.tag_id = #{tagId}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND (ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search})
        </if>
    </select>

    <!--获取学生班级列表-->
    <select id="getClassStudent" parameterType="map" resultMap="ClassStudentMap">
        SELECT
        ts.student_id,
        ts.student_num,
        ts.student_name,
        tc.class_id,
        tc.class_name,
        tg.tag_name
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
        LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        WHERE tc.grade_id = #{gradeId} AND ts.student_status = '1'
        AND ts.student_id IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY tc.class_type
    </select>

    <select id="getAllStudentClass" parameterType="map" resultMap="ClassStudentMap">
        SELECT
        ts.student_id,
        ts.student_num,
        ts.student_name,
        tc.class_id,
        tc.class_name
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id
        WHERE ts.school_id = #{schoolId}
        <if test="gradeId != null and gradeId != ''">
            AND tg.grade_id = #{gradeId}
        </if>
        <if test="classId != null and classId != ''">
            AND tc.class_id = #{classId}
        </if>
        AND ts.student_status = '1'
        AND tg.graduate_status = 0
        ORDER BY tc.class_type
    </select>

    <resultMap id="ClassStudentMap" type="map">
        <id column="student_id" property="studentId"/>
        <result column="student_num" property="studentNum"/>
        <result column="student_name" property="studentName"/>
        <collection property="classList" javaType="list" ofType="map">
            <id column="class_id" property="classId"/>
            <result column="class_name" property="className"/>
        </collection>
        <collection property="tagList" javaType="list" ofType="map">
            <result column="tag_name" property="tagName"/>
        </collection>
    </resultMap>

    <select id="getClassCourse" parameterType="map" resultMap="classCourseMap">
        SELECT tct.class_id,
               tc.class_name,
               tct.course_id,
               tct.course_name,
               tct.course_remark,
               tco.stage,
               tco.course_sort
        FROM t_class_teacher tct
                 INNER JOIN t_class tc ON tct.class_id = tc.class_id
                 INNER JOIN t_course tco ON tct.course_id = tco.course_id
        WHERE tc.grade_id = #{gradeId}
          AND (course_remark = 2 OR course_remark = 3)
    </select>

    <select id="getStudentClassCourse" parameterType="map" resultMap="classCourseMap">
        SELECT
        tct.class_id,
        tc.class_name,
        tct.course_id,
        tct.course_name,
        tct.course_remark,
        tco.stage,
        tco.course_sort
        FROM t_class_teacher tct
        INNER JOIN t_class tc ON tct.class_id = tc.class_id
        INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id
        INNER JOIN t_course tco ON tct.course_id = tco.course_id
        WHERE tg.school_id = #{schoolId} AND tg.graduate_status = 0
        <if test="gradeId != null and gradeId != ''">
            AND tg.grade_id = #{gradeId}
        </if>
        AND (course_remark =2 OR course_remark = 3)
    </select>

    <resultMap id="classCourseMap" type="map">
        <id column="class_id" property="classId"/>
        <collection property="courseList" javaType="list" ofType="map">
            <id column="course_id" property="courseId"/>
            <result column="class_id" property="classId"/>
            <result column="class_name" property="className"/>
            <result column="course_name" property="courseName"/>
            <result column="course_remark" property="courseRemark"/>
            <result column="stage" property="stage"/>
            <result column="course_sort" property="courseSort"/>
        </collection>
    </resultMap>

    <!-- 获取学生列表总数 -->
    <select id="getStudentCountBySchoolId" parameterType="map" resultType="int">
        SELECT count(DISTINCT ts.student_id)
        FROM t_student ts
        WHERE ts.school_id = #{schoolId}
          and ts.student_status = '1'

    </select>
    <!-- 获取学生列表总数 -->
    <select id="getStudentCountBySchoolIds" parameterType="map" resultType="map">
        SELECT
        count(1) stuNumber,
        ts.school_id schoolId
        FROM t_student ts
        WHERE ts.student_status = '1'
        <if test="schoolIdList != null">
            AND ts.school_id IN
            <foreach collection="schoolIdList" item="schoolId" open="(" close=")" separator=",">
                #{schoolId}
            </foreach>
        </if>
        group by ts.school_id

    </select>

    <!-- 获取学生列表总数 -->
    <select id="getStudentCount" parameterType="map" resultType="int">
        SELECT
        count(DISTINCT ts.student_id) FROM
        t_school tsc
        JOIN t_grade tgd ON tgd.school_id = tsc.school_id
        JOIN t_class tc ON tc.grade_id = tgd.grade_id
        JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        join t_student ts ON ts.student_id = tcs.student_id
        LEFT JOIN t_roll_student_relative trsr ON trsr.student_id = ts.student_id
        LEFT JOIN t_roll_student trs ON trs.roll_student_id = trsr.roll_student_id
        LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
        LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE ts.student_status = '1' AND tgd.graduate_status = 0
        <if test="schoolId != null and schoolId != ''">
            AND tsc.school_id = #{schoolId}
        </if>
        <if test="schoolIds != null and schoolIds.size() > 0">
            AND tsc.school_id in
            <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
                #{schoolId}
            </foreach>
        </if>
        <if test="gradeId != null and gradeId != ''">
            AND tc.grade_id = #{gradeId}
        </if>
        <if test="gradeType != null and gradeType != ''">
            AND tgd.grade_type = #{gradeType}
            <if test="graduateStatus!=null and graduateStatus !=''">
                AND tgd.graduate_status = #{graduateStatus}
            </if>
        </if>
        <if test="classId != null and classId != ''">
            AND tc.class_id = #{classId}
        </if>
        <if test="classType != null and classType != ''">
            AND tc.class_type = #{classType}
        </if>
        <if test="tagId != null and tagId != ''">
            AND tg.tag_id = #{tagId}
        </if>
        <if test="foreignCourseId != null and foreignCourseId != ''">
            AND tscs.foreign_course_id = #{foreignCourseId}
        </if>
        <if test="courseSelectionGroupId != null and courseSelectionGroupId != ''">
            AND tscs.course_selection_group_id = #{courseSelectionGroupId}
        </if>

        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND(
            ts.student_name LIKE #{search}
            OR ts.student_name_pinyin LIKE #{search}
            <if test="scope != 'kaoshi'">
                OR ts.student_num LIKE #{search}
            </if>
            <if test="scope != null and scope =='kaoshi'">
                OR ts.candidate= LIKE #{search}
            </if>
            )
        </if>
    </select>

    <!-- 获取学生详情 -->
    <select id="getStudentDetail" parameterType="map" resultMap="Student">
        SELECT ts.student_id,
               ts.student_num,
               ts.student_name
        FROM t_student ts
        WHERE ts.student_id = #{studentId}
    </select>

    <!-- 获取学生列表总数 -->
    <select id="getBaseAbsenceStudentCount" parameterType="map" resultType="int">
        SELECT /*+INL_JOIN(tsc,tgd,tc,tcsr,ts)*/
        distinct count(ts.student_id)
        FROM t_school tsc
        JOIN t_grade tgd ON tgd.school_id = tsc.school_id
        JOIN t_class tc ON tc.grade_id = tgd.grade_id
        JOIN t_class_student_record tcsr ON tcsr.class_id = tc.class_id
        join t_student ts on ts.student_id = tcsr.student_id
        <if test="tagId != null">
            LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
            LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        </if>
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE tsc.school_id = #{schoolId} and ts.student_status ='0'
        <if test="gradeId != null and gradeId != ''">
            AND tgd.grade_id = #{gradeId}
        </if>
        <if test="tagId != null">
            AND tg.tag_id = #{tagId}
        </if>
        <if test="foreignCourseId != null and foreignCourseId != ''">
            AND tscs.foreign_course_id = #{foreignCourseId}
        </if>
        <if test="courseSelectionGroupId != null and courseSelectionGroupId != ''">
            AND tscs.course_selection_group_id = #{courseSelectionGroupId}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            or ts.candidate like #{search}
            )
        </if>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--获取学生列表-->
    <select id="getBaseAbsenceStudent"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.bean.req.BaseDataRequest"
            resultType="long">
        SELECT /*+INL_JOIN(tsc,tgd,tc,tcsr,ts)*/
        distinct ts.student_id
        FROM t_school tsc
        JOIN t_grade tgd ON tgd.school_id = tsc.school_id
        JOIN t_class tc ON tc.grade_id = tgd.grade_id
        JOIN t_class_student_record tcsr ON tcsr.class_id = tc.class_id
        join t_student ts on ts.student_id = tcsr.student_id
        <if test="tagId != null">
            LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
            LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        </if>
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE tsc.school_id = #{schoolId} and ts.student_status ='0'
        <if test="gradeId != null and gradeId != ''">
            AND tgd.grade_id = #{gradeId}
        </if>
        <if test="tagId != null">
            AND tg.tag_id = #{tagId}
        </if>
        <if test="foreignCourseId != null and foreignCourseId != ''">
            AND tscs.foreign_course_id = #{foreignCourseId}
        </if>
        <if test="courseSelectionGroupId != null and courseSelectionGroupId != ''">
            AND tscs.course_selection_group_id = #{courseSelectionGroupId}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            or ts.candidate like #{search}
            )
        </if>
        order by ts.student_id desc
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--获取休学学生的列表-->
    <select id="getAbsenceStudent" parameterType="map" resultMap="Student">
        SELECT
        ts.student_id,
        ts.student_num,
        ts.student_name,
        ts.student_phone,
        ts.student_phone_aes,
        ts.student_no,
        ts.seat_number,
        GROUP_CONCAT(DISTINCT tst.tag_id ORDER BY tst.tag_id ASC SEPARATOR ',') tag_id,
        GROUP_CONCAT(DISTINCT tg.tag_name ORDER BY tst.tag_id ASC SEPARATOR ',') tag_name,
        tcsg.course_selection_group_id,
        tcsg.course_selection_group_name,
        tscs.foreign_course_id,
        tco.course_name
        FROM t_school tsc
        JOIN t_grade tgd ON tgd.school_id = tsc.school_id
        JOIN t_class tc ON tc.grade_id = tgd.grade_id
        JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        join t_student ts on ts.student_id = tcs.student_id
        LEFT JOIN t_student_tag tst ON ts.student_id = tst.student_id
        LEFT JOIN t_tag tg ON tst.tag_id = tg.tag_id
        LEFT JOIN t_class_student_record tcsr ON tcsr.student_id = ts.student_id
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE tsc.school_id = #{schoolId} and ts.student_status ='0'
        <if test="gradeId != null and gradeId != ''">
            AND tgd.grade_id = #{gradeId}
        </if>
        <if test="gradeId == null or gradeId == ''">
            AND tgd.stage =#{stage}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            )
        </if>
        GROUP BY ts.student_id
        <if test="sortField != null and sortType != null">
            ORDER BY ${sortField} ${sortType}
        </if>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>


    <!-- 获取学生列表总数 -->
    <select id="getAbsenceStudentCount" parameterType="map" resultType="int">
        select count(distinct ts.student_id) from t_student ts
        LEFT JOIN t_class_student_record tcsr ON tcsr.student_id = ts.student_id
        LEFT JOIN t_class tc ON tc.class_id = tcsr.class_id
        where ts.school_id = #{schoolId} AND ts.student_status ='0'
        <if test="gradeId != null">
            AND tc.grade_id = #{gradeId}
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            and (ts.student_num like #{search}
            or ts.student_name like #{search}
            or ts.candidate like #{search}
            )
        </if>
    </select>

    <!-- 更新学生 -->
    <update id="updateUser" parameterType="map">
        update sys_user
        set user_name=#{studentName}
        where relative_id = #{studentId}
          and user_type = #{userType}
    </update>
    <!-- 更新学生 -->

    <update id="updateBaseStudent" parameterType="map">
        UPDATE t_student
        SET student_num = #{studentNum},
        student_name = #{studentName},
        modifier_id = #{userId},
        modifier_name = #{userName},
        <if test="_parameter.containsKey('studentStatus') and studentStatus != null and studentStatus != ''">
            student_status = #{studentStatus},
        </if>
        candidate = #{candidate},
        student_phone = #{studentPhone},
        student_phone_aes = #{studentPhoneAes},
        student_no = #{studentNo},
        seat_number = #{seatNumber},
        modify_date_time = #{currentTime}
        WHERE student_id = #{studentId}
    </update>


    <update id="updateStudent" parameterType="map">
        UPDATE t_student
        SET student_num = #{studentNum},
        student_name = #{studentName},
        modifier_id = #{userId},
        modifier_name = #{userName},
        <if test="_parameter.containsKey('studentStatus') and studentStatus != null and studentStatus != ''">
            student_status = #{studentStatus},
        </if>
        <if test="_parameter.containsKey('candidate') and candidate != null and candidate != ''">
            candidate = #{candidate},
        </if>
        <if test="_parameter.containsKey('studentPhone') and studentPhone !=null and studentPhone != ''">
            student_phone = #{studentPhone},
        </if>
        <if test="_parameter.containsKey('studentPhoneAes') and studentPhoneAes !=null and studentPhoneAes != ''">
            student_phone_aes = #{studentPhoneAes},
        </if>
        <if test="_parameter.containsKey('studentNo') and studentNo!=null and studentNo!=''">
            student_no = #{studentNo},
        </if>
        <if test="_parameter.containsKey('seatNumber') and seatNumber!=null and seatNumber!=''">
            seat_number = #{seatNumber},
        </if>

        modify_date_time = #{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <!-- 更新学生 -->
    <update id="updateStudentForImport" parameterType="map">
        UPDATE t_student
        SET student_num = #{studentNum},
        student_name = #{studentName},
        student_status = 1,
        modifier_id = #{userId},
        modifier_name = #{userName},
        <if test="_parameter.containsKey('studentPhone') and studentPhone!=null and studentPhone!=''">
            student_phone = #{studentPhone},
        </if>
        <if test="_parameter.containsKey('studentPhoneAes') and studentPhoneAes!=null and studentPhoneAes!=''">
            student_phone_aes = #{studentPhoneAes},
        </if>
        <if test="_parameter.containsKey('studentNo') and studentNo!=null and studentNo!=''">
            student_no = #{studentNo},
        </if>
        <if test="_parameter.containsKey('seatNumber') and seatNumber!=null and seatNumber!=''">
            seat_number = #{seatNumber},
        </if>
        <if test="_parameter.containsKey('candidate') and candidate!=null and candidate!=''">
            candidate = #{candidate},
        </if>
        modify_date_time = #{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <!-- 新增学生 -->
    <insert id="insertStudent" parameterType="map" keyProperty="studentId" useGeneratedKeys="true">
        INSERT INTO t_student (student_name,
                               student_num,
                               school_id,
                               creator_id, creator_name, create_date_time,
                               modifier_id, modifier_name, modify_date_time,
                               student_phone,
                               student_phone_aes,
                               student_no, seat_number, candidate,
                               student_status)
        VALUES (#{studentName},
                #{studentNum},
                #{schoolId},
                #{userId}, #{userName}, #{currentTime},
                #{userId}, #{userName}, #{currentTime},
                #{studentPhone},
                #{studentPhoneAes},
                #{studentNo}, #{seatNumber}, #{candidate}, 1)
        ON DUPLICATE KEY UPDATE modifier_id=VALUES(modifier_id),
                                modifier_name=VALUES(modifier_name),
                                modify_date_time=VALUES(modify_date_time)
    </insert>

    <!-- 新增 account -->
    <insert id="insertAccount" parameterType="map" keyProperty="accountId" useGeneratedKeys="true">
        INSERT INTO sys_account(account_name,
                                account_name_aes,
                                password,
                                account_status,
                                password_status,
                                creator_id,
                                creator_name,
                                create_date_time,
                                modifier_id,
                                modifier_name,
                                modify_date_time)
        VALUES (#{studentPhone},
                #{studentPhoneAes},
                #{password},
                1,
                0,
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime})
    </insert>

    <!-- 新增 user -->
    <insert id="insertUser" parameterType="map" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO sys_user(user_name,
                             user_status,
                             user_type,
                             relative_id,
                             account_id,
                             nickname,
                             creator_id,
                             creator_name,
                             create_date_time,
                             modifier_id,
                             modifier_name,
                             modify_date_time)
        VALUES (#{studentName},
                1,
                4,
                #{studentId},
                #{accountId},
                #{className},
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime})
    </insert>

    <!-- 获取 student 用来去重 -->
    <select id="getStudentForExist" parameterType="map" resultMap="Student">
        SELECT
        ts.student_id,
        ts.student_num,
        ts.student_name,
        ts.student_phone studentPhone,
        ts.student_phone_aes studentPhoneAes
        FROM t_student ts
        WHERE ts.school_id = #{schoolId}
        <if test="body != null">
            AND ts.student_num IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.studentNum}
            </foreach>
        </if>
        <if test="studentNum != null and studentNum != ''">
            AND ts.student_num = #{studentNum}
        </if>
        <if test="studentId != null and studentId != ''">
            AND ts.student_id != #{studentId}
        </if>
        <if test="studentStatus != null">
            AND ts.student_status = #{studentStatus}
        </if>
    </select>

    <!-- 获取 student 用来去重 -->
    <select id="getStudentForName" parameterType="map" resultMap="Student">
        SELECT
        ts.student_id,
        ts.student_num,
        ts.student_name,
        ts.student_phone studentPhone,
        ts.student_phone_aes studentPhoneAes
        FROM t_student ts
        WHERE ts.school_id = #{schoolId}
        <if test="body != null">
            AND ts.student_name IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.studentName}
            </foreach>
        </if>
    </select>

    <!-- 获取 student 用来判断是否存在 -->
    <select id="getClassStudentForExist" parameterType="map" resultType="map">
        SELECT
        tcs.class_student_id classStudentId,
        tcs.class_id classId,
        tcs.student_id studentId,
        tc.class_sort classSort,
        tc.class_name className,
        ts.student_name studentName,
        ts.student_num studentNum
        FROM t_class_student tcs
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        INNER JOIN t_student ts ON tcs.student_id = ts.student_id
        WHERE ts.school_id = #{schoolId}
        AND tc.grade_id = #{gradeId}
        AND tc.school_id = #{schoolId}
        <if test="body != null">
            AND ts.student_num IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.studentNum}
            </foreach>
        </if>
        <if test="keyword != null">
            AND tc.class_name IN
            <foreach collection="keyword" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 获取 student 用来判断是否存在 -->
    <select id="getClassStudentForExistByGradeIds" parameterType="map" resultType="map">
        SELECT
        tcs.class_student_id classStudentId,
        tcs.class_id classId,
        tcs.student_id studentId,
        tg.grade_name gradeName,
        tc.class_sort classSort,
        tc.class_name className,
        ts.student_name studentName,
        ts.student_num studentNum
        FROM t_class_student tcs
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id
        INNER JOIN t_student ts ON tcs.student_id = ts.student_id
        WHERE ts.school_id = #{schoolId}
        AND tc.school_id = #{schoolId}
        AND tc.grade_id in
        <foreach collection="gradeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="body != null">
            AND ts.student_num IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.studentNum}
            </foreach>
        </if>
        <if test="keyword != null">
            AND tc.class_name IN
            <foreach collection="keyword" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <!--更新学籍辅号-->
    <update id="updateStudentCode" parameterType="map">
        UPDATE t_roll_student trs,t_roll_student_relative trsr
        SET trs.student_code = #{studentCode}
        WHERE trs.roll_student_id = trsr.roll_student_id
          AND trsr.student_id = #{studentId}
    </update>

    <!--插入学籍辅号-->
    <insert id="insertStudentCode" parameterType="map">
        INSERT INTO t_roll_student (student_name,
                                    student_code,
                                    creator_id, creator_name, create_date_time,
                                    modifier_id, modifier_name, modify_date_time)
        VALUES (#{studentName},
                #{studentCode},
                #{userId}, #{userName}, now(),
                #{userId}, #{userName}, now())
    </insert>

    <!--根据studentCode查询roll_student_id-->
    <select id="getRollStudentId" parameterType="map" resultType="long">
        SELECT roll_student_id
        FROM t_roll_student
        WHERE student_code = #{studentCode}
    </select>

    <!--根据studentCode查询roll_student_id-->
    <select id="getStudentCode" parameterType="map" resultType="int">
        SELECT count(*)
        FROM t_roll_student
        WHERE student_code = #{studentCode}
    </select>

    <!--初始化学生学籍关系表-->
    <insert id="insertRollStudentRelative" parameterType="map">
        INSERT INTO t_roll_student_relative (roll_student_id,
                                             student_id,
                                             creator_id, creator_name, create_date_time,
                                             modifier_id, modifier_name, modify_date_time)
        VALUES (#{rollStudentId},
                #{studentId},
                #{userId}, #{userName}, now(),
                #{userId}, #{userName}, now())
    </insert>

    <!--根据studentId和学籍辅号查询学生是否存在-->
    <select id="getStudentIsExist" parameterType="map" resultType="long">
        SELECT COUNT(trs.roll_student_id)
        FROM t_roll_student trs
                 INNER JOIN t_roll_student_relative trsr ON trs.roll_student_id = trsr.roll_student_id
            AND trsr.student_id = #{studentId}
            AND trs.student_code = #{studentCode}
    </select>

    <!--根据roll_student_id判断是否有学籍辅号-->
    <select id="getStudentCodeIsExist" parameterType="map" resultType="long">
        SELECT COUNT(trs.roll_student_id)
        FROM t_roll_student trs
                 INNER JOIN t_roll_student_relative trsr ON trs.roll_student_id = trsr.roll_student_id
            AND trsr.student_id = #{studentId}
    </select>

    <!-- 删除班级学生 -->
    <delete id="deleteClassStudent" parameterType="map">
        DELETE
        FROM t_class_student
        WHERE student_id IN
        <foreach collection="studentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 删除学生 -->
    <delete id="deleteStudent" parameterType="map">
        DELETE
        FROM t_student
        WHERE student_id IN
        <foreach collection="studentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <!-- 判断新增的学生是否已存在该班级 -->
    <select id="getStuClassType" parameterType="map" resultType="int">
        SELECT
        count(*)
        FROM t_class tc
        INNER JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        INNER JOIN t_student ts ON ts.student_id = tcs.student_id
        <where>
            <if test="studentNum != null and studentNum != ''">
                ts.student_num = #{studentNum}
            </if>
            <if test="studentName != null and studentName != ''">
                AND ts.student_name = #{studentName}
            </if>
            <if test="studentIds != null and studentIds != ''">
                AND ts.student_id IN
                <foreach collection="studentIds" open="(" item="item" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            AND tc.class_type = 1
        </where>
    </select>

    <!--  -->
    <select id="getStudentExist" parameterType="map" resultType="int">
        SELECT count(*)
        FROM t_class_student tcs
                 INNER JOIN t_student ts ON tcs.student_id = ts.student_id
        WHERE ts.school_id = #{schoolId}
          AND ts.student_num = #{studentNum}
          AND tcs.class_id = #{classId}
    </select>

    <!-- 判断学生存在的多个班级是否在同一年级 -->
    <select id="getStudentExistGrade" parameterType="map" resultType="map">
        SELECT DISTINCT tg.grade_id gradeId
        FROM t_class_student tcs
                 INNER JOIN t_class tc ON tcs.class_id = tc.class_id
                 INNER JOIN t_student ts ON tcs.student_id = ts.student_id
                 INNER JOIN t_grade tg ON tg.grade_id = tc.grade_id
        WHERE ts.school_id = #{schoolId}
          AND tg.graduate_status = 0
          AND (ts.student_num = #{studentNum} OR tc.class_id = #{classId})
    </select>

    <!-- 获取 student 用来判断是否能删 -->
    <select id="getClassStudentForDelete" parameterType="map" resultType="int">
        SELECT COUNT(tcs.class_student_id)
        FROM t_class_student tcs
        WHERE tcs.student_id IN
        <foreach collection="studentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <!-- 获取 student 用来判断是否能删 -->
    <select id="getClassStudentForUpdate" parameterType="map" resultType="int">
        SELECT tcs.class_student_id
        FROM t_class_student tcs
        WHERE tcs.student_id in
        <foreach collection="siList" index="index" item="item" separator="," open="(" close=")">
            = #{item}
        </foreach>
    </select>
    <!--用来需要休学学生计数-->
    <select id="getStudentForUpdate" parameterType="map" resultType="int">
        SELECT student_id
        FROM t_student ts
        WHERE student_id in
        <foreach collection="siList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND student_status = 1
    </select>

    <!-- 通过学生姓名和学号判断其它学校是否已存在该学生 -->
    <select id="getSameNumAndName" parameterType="map" resultType="map">
        SELECT ts.student_num studentNum,
        ts.student_name studentName
        FROM t_student ts
        <where>
            <if test="studentNum != null and studentNum != ''">
                ts.student_num = #{studentNum}
            </if>
            <if test="studentName != null and studentName != ''">
                AND ts.student_name = #{studentName}
            </if>
            AND ts.school_id != #{schoolId}
        </where>
    </select>

    <!-- 通过学生学号判断该学校是否已存在该学生 -->
    <select id="getSameNum" parameterType="map" resultType="int">
        SELECT COUNT(1)
        FROM t_student ts
        WHERE ts.student_num = #{studentNum}
          AND ts.school_id = #{schoolId}
          AND ts.student_status = 1
    </select>


    <!-- 通过学生学号判断该学校是否已存在该学生 -->
    <select id="getSeatNumber" parameterType="map" resultType="int">
        SELECT COUNT(1)
        FROM t_student ts
        join t_class_student tcs on ts.student_id = tcs.student_id
        WHERE ts.seat_number = #{seatNumber}
        AND tcs.class_id = #{classId}
        AND ts.student_status = 1
        <if test="studentId != null">
            AND ts.student_id != #{studentId}
        </if>
    </select>

    <!-- 通过学生学号判断该学校是否已存在该学生 -->
    <select id="getSeatNumberList" parameterType="map" resultType="string">
        SELECT seat_number
        FROM t_student ts
        join t_class_student tcs on ts.student_id = tcs.student_id
        WHERE ts.seat_number in
        <foreach collection="seatNumberList" item="seatNumber" open="(" separator="," close=")">
            #{seatNumber}
        </foreach>
        AND tcs.class_id = #{classId}
        AND ts.student_status = 1
        <if test="studentIdList != null and studentIdList.size() > 0">
            AND ts.student_id NOT IN
            <foreach collection="studentIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 通过学生姓名和学号判断其它学校是否已存在该学生 -->
    <select id="getSameNumAndNameByAbsence" parameterType="map" resultType="map">
        SELECT ts.student_id   studentId,
               ts.student_num  studentNum,
               ts.student_name studentName
        FROM t_student ts
        where ts.student_num = #{studentNum}
          AND ts.student_name = #{studentName}
          AND ts.school_id = #{schoolId}
          AND ts.student_status = 0
    </select>

    <!-- 通过学生姓名和学号判断其它学校是否已存在该学生 -->
    <select id="getSameNumAndNameListByAbsence" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.student_num studentNum,
        ts.student_name studentName
        FROM t_student ts
        where
        (ts.student_num,ts.student_name) IN
        (<foreach collection="body" item="item" separator=",">
        (#{item.studentNum}, #{item.studentName})
    </foreach>)
        AND ts.student_status = 0
        AND ts.school_id = #{schoolId}
    </select>

    <!-- 通过学生姓名和学号判断其它学校是否已存在该学生 -->
    <select id="getSameNumAndNameList" parameterType="map" resultType="map">
        SELECT
        tb.student_num studentNum,
        tb.student_name studentName
        FROM (
        SELECT student_num ,
        student_name ,
        school_id,
        CONCAT(student_num,student_name) joinStr
        FROM t_student) tb
        where
        <if test="body != null">
            joinStr IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.joinStr}
            </foreach>
        </if>
        AND tb.school_id != #{schoolId}
    </select>

    <!-- 通过学生姓名和学号判断其它学校是否已存在该学生 -->
    <select id="getSchoolStudentNum" parameterType="long" resultType="string">
        SELECT student_num studentNum
        FROM t_student
        WHERE school_id = #{schoolId}
    </select>

    <!--查询符合条件的学生记录-->
    <select id="getClassStudentIsExist" parameterType="map" resultType="map">
        select tcs.class_student_id from t_class_student tcs
        where tcs.student_id in
        <foreach collection="siList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!--更新学生休学-->
    <update id="updateClassStuStatus" parameterType="String">
        update t_class_student
        set outer_date = now(),student_status=-1
        WHERE student_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--更新学生关联的所有班级-->
    <update id="updateStudentStatus" parameterType="list">
        update t_student
        set student_status = 0
        WHERE student_id IN
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!--更新学生关联的所有班级-->
    <update id="resumeEnrollment" parameterType="map">
        update t_student
        set student_status = 1
        WHERE student_id = #{studentId}
    </update>

    <!--更新学生phone-->
    <update id="updateStudentPhone" parameterType="map">
        update t_student
        set student_phone     = #{accountName},
            student_phone_aes = #{accountNameAes},
            modifier_id       =#{userId},
            modifier_name     = #{userName},
            modify_date_time=#{currentTime}
        WHERE student_id = #{relativeId}
    </update>

    <!-- 批量插入学生 -->
    <insert id="batchInsertStudent" parameterType="list" keyProperty="studentId" useGeneratedKeys="true">
        INSERT INTO t_student(
        student_name,
        student_num,
        school_id,
        student_phone,
        student_phone_aes,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time,
        student_no,seat_number, candidate,student_status
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.studentName},
            #{item.studentNum},
            #{item.schoolId},
            #{item.studentPhone},
            #{item.studentPhoneAes},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.studentNo},#{item.seatNumber},#{item.candidate},1
            )
        </foreach>
    </insert>


    <!-- 根据student_id 更新account表 -->
    <update id="updateAccount" parameterType="map">
        UPDATE sys_account
        SET account_name     = #{studentPhone},
            account_name_aes = #{studentPhoneAes},
            password         = #{password},
            modify_date_time = #{currentTime}
        WHERE account_id in
              (select account_id
               from sys_user su
               where su.relative_id = #{studentId}
                 and su.user_type = 4)
    </update>

    <!-- 插入班级学生 -->
    <insert id="insertClassStudent" parameterType="map">
        INSERT t_class_student (class_id,
                                student_id,
                                inner_date,
                                creator_id, creator_name, create_date_time,
                                modifier_id, modifier_name, modify_date_time)
        VALUES (#{classId},
                #{studentId},
                #{currentTime},
                #{userId}, #{userName}, #{currentTime},
                #{userId}, #{userName}, #{currentTime})
    </insert>

    <!-- 批量插入班级学生 -->
    <insert id="batchInsertClassStudent" parameterType="map">
        INSERT t_class_student(
        class_id,
        student_id,
        inner_date,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.classId},
            #{item.studentId},
            #{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime}
            )
        </foreach>
    </insert>


    <!-- 获取班级，判断是否存 -->
    <select id="getClassForExist" parameterType="map" resultType="map">
        SELECT class_id classId,
        class_name className,
        class_type classType
        FROM t_class
        WHERE school_id = #{schoolId}
        AND grade_id = #{gradeId}
        <if test="keyword != null">
            AND class_name IN
            <foreach collection="keyword" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 获取班级，判断是否存 -->
    <select id="getClassForExistByGradeIds" parameterType="map" resultType="map">
        SELECT tc.class_id classId,
        tg.grade_name gradeName,
        tc.class_name className,
        tc.class_type classType
        FROM t_class tc
        inner join t_grade tg on tc.grade_id = tg.grade_id
        WHERE tc.school_id = #{schoolId}
        AND tc.grade_id in
        <foreach collection="gradeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="keyword != null">
            AND tc.class_name IN
            <foreach collection="keyword" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--通过学生姓名查找相关学生简单信息-->
    <select id="getStudentInfo" parameterType="map" resultType="map">
        SELECT ts.student_name studentName,
               ts.student_num  sutdentNum,
               tc.class_name   className,
               tss.school_name schoolName
        FROM t_student ts
                 LEFT JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 LEFT JOIN t_class tc ON tcs.class_id = tc.class_id
                 LEFT JOIN t_school tss ON tss.school_id = ts.school_id
        WHERE ts.student_name LIKE concat(#{studentName,jdbcType=VARCHAR}, '%')
        ORDER BY ts.school_id
    </select>


    <!-- 获取未注册的菁优网学生 -->
    <select id="getUnRegisterStudent" parameterType="map" resultType="long">
        SELECT DISTINCT (tt1.relative_id)
        FROM (SELECT t2.relative_id relative_id,
                     t2.user_id,
                     t1.jyeoo_user_id
              FROM sys_user_level t,
                   sys_user t2
                       LEFT JOIN t_jyeoo_user t1 ON t2.user_id = t1.user_id
              WHERE t.user_level = 2
                AND t.relative_id = t2.relative_id
                AND t2.user_type IN (5, 4)
                AND t1.jyeoo_user_id IS NULL) tt1
                 LEFT JOIN (SELECT t2.relative_id relative_id,
                                   t2.user_id,
                                   t1.jyeoo_user_id
                            FROM sys_user_level t,
                                 sys_user t2
                                     LEFT JOIN t_jyeoo_user t1 ON t2.user_id = t1.user_id
                            WHERE t.user_level = 2
                              AND t.relative_id = t2.relative_id
                              AND t2.user_type IN (5, 4)
                              AND t1.jyeoo_user_id IS NOT NULL) tt2 ON tt1.relative_id = tt2.relative_id
        WHERE tt2.relative_id IS NULL
    </select>
    <!-- 获取存在并有效的 account -->
    <select id="getAccountForExistByStudentPhone" parameterType="collection" resultType="map">
        SELECT
        sa.account_id accountId,
        sa.account_name accountName,
        sa.account_name_aes accountNameAes
        FROM sys_account sa
        WHERE sa.account_name_aes IN
        <foreach collection="collection" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <!-- 获取 account -->
    <select id="getAccount" parameterType="map" resultType="string">
        SELECT sa.account_id
        FROM sys_account sa
        WHERE sa.account_name_aes = #{studentPhoneAes}
    </select>


    <!--批量获取账号-->
    <select id="getStudentAccountByPhone" parameterType="collection" resultType="map">
        SELECT
        sa.account_id accountId,
        sa.account_name accountName,
        sa.account_name_aes accountNameAes
        FROM sys_account sa
        WHERE sa.account_name_aes IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 批量插入 account -->
    <insert id="batchInsertAccount" parameterType="list" keyProperty="accountId" useGeneratedKeys="true">
        INSERT INTO sys_account(
        account_name,
        account_name_aes,
        password,
        account_status,
        password_status,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.studentPhone},
            #{item.studentPhoneAes},
            #{item.password},
            1,
            0,
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        account_status = 1,
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>


    <!-- 批量插入 user -->
    <insert id="batchInsertUser" parameterType="list" keyProperty="userId" useGeneratedKeys="true">
        INSERT INTO sys_user(
        user_name,
        user_status,
        user_type,
        relative_id,
        account_id,
        nickname,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (#{item.studentName},
            1,
            4,
            #{item.studentId},
            #{item.accountId},
            #{item.className},
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
    </insert>

    <!-- 获取学生id -->
    <select id="getStudentExistInSchool" parameterType="map" resultType="long">
        SELECT student_id studentId
        FROM t_student
        WHERE student_num = #{studentNum}
          AND student_name = #{studentName}
    </select>

    <!-- 获取学生是否已注册 -->
    <select id="getUserForExist" parameterType="map" resultType="int">
        SELECT count(*)
        FROM sys_user
        WHERE relative_id = #{studentId}
    </select>

    <!-- 获取学生所在年级 -->
    <select id="getStudentGradeByNum" parameterType="map" resultType="map">
        SELECT DISTINCT tg.grade_id gradeId, ts.student_num studentNum
        FROM t_class_student tcs
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        INNER JOIN t_student ts ON tcs.student_id = ts.student_id
        INNER JOIN t_grade tg ON tg.grade_id = tc.grade_id
        WHERE ts.school_id = #{schoolId}
        <if test="body != null">
            AND ts.student_num IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.studentNum}
            </foreach>
        </if>
    </select>

    <!-- 获取班级所在年级-->
    <select id="getClassBelong" parameterType="map" resultType="map">
        SELECT
        tg.grade_id gradeId,
        tc.class_name className
        FROM t_class tc
        INNER JOIN t_grade tg ON tg.grade_id = tc.grade_id
        WHERE tg.school_id = #{schoolId}
        AND tg.grade_id = #{gradeId}
        <if test="body != null">
            AND tc.class_name IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.className}
            </foreach>
        </if>
    </select>

    <!-- 获取班级所在年级-->
    <select id="getGradeClassBelong" parameterType="map" resultType="map">
        SELECT
        tg.grade_id gradeId,
        tg.grade_name gradeName,
        tc.class_name className
        FROM t_class tc
        INNER JOIN t_grade tg ON tg.grade_id = tc.grade_id
        WHERE tg.school_id = #{schoolId}
        AND tg.grade_id in
        <foreach collection="gradeIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="body != null">
            AND tc.class_name IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.className}
            </foreach>
        </if>
    </select>

    <!--更新学生关联的所有班级-->
    <update id="updateAbsenceStudentStatus" parameterType="list">
        update t_student
        set student_status = 1
        WHERE student_id IN
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="touchAbsence" parameterType="map">
        update t_student
        set student_status = 0
        WHERE student_id IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <!--更新学生user表中的Nickname-->
    <update id="updateStudentNickName" parameterType="list">
        UPDATE sys_user su
        INNER JOIN t_class_student tcs ON tcs.student_id = su.relative_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id AND tc.class_type = 1
        SET su.nickname = tc.class_name
        WHERE su.user_type = 4
        and su.relative_id IN
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 获取 学生导出数据 -->
    <select id="getStudentExport" parameterType="map" resultType="map">
        SELECT
        tc.class_sort classSort,
        tc.class_name className,
        ts.student_name studentName,
        ts.student_num studentNum,
        trs.student_code studentCode,
        trs.union_code unionCode,
        trs.identity_card_no identityCardNo,
        trs.identity_card_no_aes identityCardNoAes
        FROM t_grade tg
        INNER JOIN t_class tc ON tc.grade_id = tg.grade_id
        INNER JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        INNER JOIN t_student ts ON ts.student_id = tcs.student_id
        LEFT JOIN t_roll_student_relative trsr ON trsr.student_id= ts.student_id
        LEFT JOIN t_roll_student trs ON trs.roll_student_id = trsr.roll_student_id
        WHERE tg.grade_id = #{gradeId}
        <if test="classId != null and classId != ''">
            AND tc.class_id = #{classId}
        </if>
        AND tc.class_type = 1
        AND ts.student_status = 1
        GROUP BY ts.student_num
        ORDER BY tc.class_sort, ts.student_num
    </select>

    <!-- 获取学校 studentId  studentNum 对应关系-->
    <select id="getStudentIdNumMap" parameterType="map" resultMap="Student">
        SELECT
        ts.student_id,
        ts.student_num
        FROM t_student ts
        WHERE ts.school_id = #{schoolId}
        <if test="body != null">
            AND ts.student_num IN
            <foreach collection="body" item="item" open="(" separator="," close=")">
                #{item.studentNum}
            </foreach>
        </if>
    </select>


    <!-- 删除学生学籍信息 -->
    <delete id="deleteRollStudent" parameterType="map">
        DELETE trsr,trs
        FROM t_roll_student_relative trsr, t_roll_student trs
        WHERE trsr.roll_student_id = trs.roll_student_id
        AND trsr.student_id IN
        <foreach collection="studentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 更新学生用户信息userStatus为注销   -->
    <update id="updateUserStatus" parameterType="map">
        update sys_user set user_status =0,
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        where user_id in
        <foreach collection="studentList" index="index" item="item" open="(" separator="," close=")">
            #{item.userId}
        </foreach>
    </update>

    <!-- 更新学生用户信息userStatus为注销   -->
    <update id="updateAccountStatus" parameterType="map">
        update sys_account set account_status =0,
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        where account_id in
        <foreach collection="studentList" index="index" item="item" open="(" separator="," close=")">
            #{item.accountId}
        </foreach>
    </update>


    <select id="getExamVipStudent" resultType="map" parameterType="map">
        SELECT
        relative_id studentId
        FROM sys_user_membership
        WHERE end_time>=#{currentTime} AND status=1 AND relative_id IN
        <foreach collection="studentIds" item="studentId" separator="," open="(" close=")">
            #{studentId}
        </foreach>
    </select>

    <select id="getStudentIn" resultType="map" parameterType="map">
        SELECT
        student_id studentId,
        student_name studentName,
        student_num studentNum,
        CONCAT(student_name,'-',student_num) keyword
        FROM t_student
        WHERE school_id = #{schoolId}
        AND student_status = 1
        AND CONCAT(student_name,'-',student_num) IN
        <foreach collection="keyword" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getUserByStudentIds" resultType="map" parameterType="map">
        SELECT
        su.relative_id studentId,
        sa.account_id accountId
        FROM t_student ts
        INNER JOIN sys_user su ON ts.student_id = su.relative_id
        INNER JOIN sys_account sa ON sa.account_id = su.account_id
        WHERE sa.account_status = 1 AND su.user_status = 1 AND user_type = 4
        AND ts.student_id IN
        <foreach collection="studentIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateAccountName" parameterType="map">
        UPDATE sys_account
        SET account_name     = #{accountName},
            account_name_aes = #{accountNameAes},
            modifier_id      = #{userId},
            modifier_name    = #{userName},
            modify_date_time = #{currentTime}
        WHERE account_id = #{accountId}
    </update>

    <!-- 获取学生列表总数 -->
    <select id="getStudentListCount" parameterType="map" resultType="int">
        SELECT
        count(DISTINCT ts.student_id)
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        WHERE tc.class_id = #{classId} and ts.student_status = '1'
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            )
        </if>
    </select>

    <!-- 获取学生列表 -->
    <select id="getStudentList" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.student_num studentNum,
        ts.student_name studentName,
        tc.class_id classId,
        tc.class_name className
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        WHERE tc.class_id = #{classId} and ts.student_status = '1'
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            )
        </if>
        <if test="sortKey !=null and sortKey =='studentNum'">
            order by ts.student_num
            <if test="sortType !=null and sortType == 'asc'">
                asc
            </if>
            <if test="sortType !=null and sortType == 'desc'">
                desc
            </if>
        </if>
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <select id="getStudentUserList" parameterType="map" resultType="map">
        select
        su.user_id accountUserId,
        su.account_id accountId,
        sa.account_name accountName,
        su.user_id userId,
        su.user_name userName,
        su.user_type userType,
        su.relative_id studentId
        from sys_user su
        inner join sys_account sa on sa.account_id = su.account_id
        where su.relative_id in
        <foreach collection="student" item="item" separator="," open="(" close=")">
            #{item.studentId}
        </foreach>
        and user_type = #{userType} and su.user_status=1
    </select>

    <select id="getStudentParentUser" parameterType="map" resultType="map">
        select
        su.user_id accountUserId,
        su.account_id accountId,
        sa.account_name accountName,
        su.user_name userName,
        su.user_id userId,
        su.user_type userType,
        tps.student_id studentId
        from sys_user su
        inner join sys_account sa on sa.account_id = su.account_id
        inner join t_parent_student tps on su.relative_id = tps.parent_student_id
        where tps.student_id in
        <foreach collection="student" item="item" separator="," open="(" close=")">
            #{item.studentId}
        </foreach>
        and user_type = #{userType} and su.user_status=1
    </select>

    <select id="getUser" parameterType="map" resultType="map">
        select su.user_type        userType,
               sa.account_id       accountId,
               sa.account_name_aes accountNameAes
        from sys_user su
                 inner join sys_account sa on su.account_id = sa.account_id
        where user_id = #{accountUserId}
    </select>

    <!--获取学生基本信息通过Id-->
    <select id="getStudentInfoByStudentId" resultType="map" parameterType="map">
        SELECT ts.student_id                        studentId,
               ts.student_num                       studentNum,
               ifnull(ts.candidate, ts.student_num) studentExamNum,
               ts.student_name                      studentName,
               tg.grade_type                        gradeType,
               tsc.school_name                      schoolName,
               tc.class_id                          classId,
               tc.class_name                        className
        FROM t_student ts
                 INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class tc ON tcs.class_id = tc.class_id
                 LEFT JOIN t_grade tg ON tg.grade_id = tc.grade_id
                 LEFT JOIN t_school tsc ON tsc.school_id = ts.school_id
        WHERE ts.student_id = #{studentId}
          AND tc.class_type = 1
        limit 1
    </select>


    <select id="getAccountForStudentByIdentityCardNoAes" parameterType="map" resultType="map">
        SELECT sa.account_id                accountId,
               sa.account_name              accountName,
               sa.account_name_aes          accountNameAes,
               sa.account_status            accountStatus,
               sa.password_modify_date_time passwordModifyDateTime,
               sa.security_status           securityStatus,
               sa.phone_bind_status         phoneBindStatus,
               su.user_id                   userId,
               su.user_name                 userName,
               su.nickname                  nickname,
               su.user_status               userStatus,
               su.user_type                 userType,
               su.relative_id               relativeId,
               su.account_id                accountId
        FROM sys_user su
                 INNER JOIN sys_account sa ON su.account_id = sa.account_id AND su.user_status = 1
                 INNER JOIN t_student ts ON su.relative_id = ts.student_id AND su.user_type = 4
        WHERE ts.identity_card_no_aes = #{identityCardNoAes}
        limit 1
    </select>

    <select id="getAccountForStudentByStudentNum" parameterType="map" resultType="map">
        SELECT sa.account_id                accountId,
               sa.account_name              accountName,
               sa.account_name_aes          accountNameAes,
               sa.account_status            accountStatus,
               sa.password_modify_date_time passwordModifyDateTime,
               sa.security_status           securityStatus,
               sa.phone_bind_status         phoneBindStatus,
               su.user_id                   userId,
               su.user_name                 userName,
               su.nickname                  nickname,
               su.user_status               userStatus,
               su.user_type                 userType,
               su.relative_id               relativeId,
               su.account_id                accountId,
               ts.school_id                 schoolId
        FROM sys_user su
                 INNER JOIN sys_account sa ON su.account_id = sa.account_id AND su.user_status = 1
                 INNER JOIN t_student ts ON su.relative_id = ts.student_id AND su.user_type = 4
        WHERE ts.student_num = #{studentNum}
        limit 1
    </select>

    <select id="getStudentByIdentityCardNoAes" parameterType="map" resultType="map">
        SELECT student_id studentId,
        school_id schoolId,
        student_num studentNum,
        student_name studentName,
        student_phone studentPhone,
        student_phone_aes studentPhoneAes,
        student_no studentNo,
        student_status studentStatus,
        identity_card_no identityCardNo,
        identity_card_no_aes identityCardNoAes
        FROM t_student
        WHERE identity_card_no_aes IN
        <foreach collection="identityCardNoAesList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getStudentByStudentPhoneAes" parameterType="map" resultType="map">
        SELECT student_id studentId,
        school_id schoolId,
        student_num studentNum,
        student_name studentName,
        student_phone studentPhone,
        student_phone_aes studentPhoneAes,
        student_no studentNo,
        student_status studentStatus,
        identity_card_no identityCardNo,
        identity_card_no_aes identityCardNoAes
        FROM t_student
        WHERE student_phone_aes IN
        <foreach collection="studentPhoneAesList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 查询学生的家长号码 -->
    <select id="getStudentParentPhone" parameterType="map" resultType="map">
        SELECT DISTINCT
        tp.parent_phone parentPhone,
        tp.parent_phone_aes parentPhoneAes,
        ts.student_id studentId
        FROM t_class_student tcs
        INNER JOIN t_student ts ON tcs.student_id = ts.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        INNER JOIN t_parent_student tps ON ts.student_id = tps.student_id
        INNER JOIN t_parent tp ON tps.parent_id = tp.parent_id
        WHERE tcs.class_id in
        <foreach collection="classIds" separator="," open="(" close=")" item="i">
            #{i}
        </foreach>
        <if test="studentIds != null">
            AND ts.student_id not in
            <foreach collection="studentIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getExistUser" parameterType="map" resultType="long">
        select
        relative_id
        from sys_user
        where relative_id in
        <foreach collection="existStu" open="(" close=")" separator="," item="item">
            #{item.studentId}
        </foreach>
        and user_type = #{studentUserType}
    </select>

    <update id="updateUserAccountId" parameterType="map">
        UPDATE sys_user
        SET account_id      = #{accountId},
            modifier_id     =#{userId},
            modifier_name   = #{userName},
            modify_date_time=#{currentTime}
        WHERE relative_id = #{relativeId}
          AND user_status = 1
          AND user_type = 4
          AND account_id = #{oldAccountId}
    </update>

    <select id="getAccountStudentList" parameterType="map" resultType="map">
        select relative_id studentId
        from sys_user
        where account_id = #{accountId}
          and user_type = 4
    </select>

    <select id="getStudentByStudentIdList" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.identity_card_no_aes idCard
        FROM
        t_student ts
        WHERE
        ts.student_id IN
        <foreach collection="studentIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!--获取学生年级信息-->
    <select id="selectStudentGradeInfo" parameterType="map" resultType="map">
        SELECT ts.student_id   studentId,
               tsc.school_id   schoolId,
               tsc.school_name schoolName,
               tsc.area_id     areaId,
               tg.stage        stage,
               tg.end_date     endDate,
               tg.grade_type   gradeType,
               tg.grade_year   gradeYear
        FROM t_student AS ts
                 INNER JOIN t_school AS tsc ON ts.school_id = tsc.school_id
                 INNER JOIN t_class_student AS tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class AS tc ON tcs.class_id = tc.class_id
                 INNER JOIN t_grade AS tg ON tc.grade_id = tg.grade_id
        WHERE ts.student_id = #{studentId}
    </select>

    <!--通过userId获取-->
    <select id="selectStudentGradeInfoByUserId" resultType="map" parameterType="map">
        SELECT ts.student_id       studentId,
               sa.account_name_aes accountNameAes,
               tsc.school_id       schoolId,
               tsc.school_name     schoolName,
               tsc.area_id         areaId,
               tg.stage            stage,
               tg.end_date         endDate,
               tg.grade_type       gradeType,
               tg.grade_year       gradeYear
        FROM t_student AS ts
                 INNER JOIN sys_user AS su ON ts.student_id = su.relative_id AND su.user_type = 4
                 INNER JOIN sys_account sa ON su.account_id = sa.account_id
                 INNER JOIN t_school AS tsc ON ts.school_id = tsc.school_id
                 INNER JOIN t_class_student AS tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class AS tc ON tcs.class_id = tc.class_id
                 INNER JOIN t_grade AS tg ON tc.grade_id = tg.grade_id
        WHERE su.user_id = #{userId}
    </select>


    <select id="selectStudentListForScoreImport" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum,
        ifnull(ts.candidate, ts.student_num) candidate,
        ts.student_status studentStatus,
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        tcs.class_id classId,
        tc.class_name className,
        tg.grade_id gradeId,
        tg.grade_name gradeName,
        tg.grade_type gradeType,
        tg.grade_year gradeYear,
        tg.graduate_status graduateStatus,
        tg.stage stage
        FROM
        t_student ts
        INNER JOIN t_school tsc ON ts.school_id = tsc.school_id
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        AND tc.class_type = 1
        INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id AND tg.graduate_status = 0
        WHERE
        tsc.school_id IN
        <foreach collection="schoolIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getStudentByClassIdsForScoreImport" parameterType="map" resultType="map">
        SELECT
        ts.`school_id` schoolId,
        ts.`school_name` schoolName,
        ts.`area_id` areaId,
        tg.`grade_id` gradeId,
        tg.`grade_name` gradeName,
        tg.`grade_type` gradeType,
        tc.`class_id` classId,
        tc.`class_name` className,
        tc.`class_type` classType,
        tstu.`student_id` studentId,
        tstu.`student_name` studentName,
        tstu.`student_num` studentNum,
        ifnull(tstu.candidate, tstu.student_num) candidate,
        tscs.course_selection_group_id courseSelectionGroupId,
        tscs.foreign_course_id foreignCourseId
        FROM t_class tc
        JOIN t_grade tg ON tg.`grade_id` = tc.`grade_id`
        JOIN t_school ts ON ts.`school_id` = tc.`school_id`
        JOIN t_class_student tcs ON tcs.`class_id` = tc.`class_id`
        JOIN t_student tstu ON tstu.`student_id` = tcs.`student_id`
        left join t_student_course_selection tscs on tstu.student_id = tscs.student_id
        WHERE tc.class_id IN
        <foreach collection="classIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <!--通过schoolId获取学生的第三方信息-->
    <select id="selectStudentThirdInfoBySchoolId" resultType="map" parameterType="map">
        SELECT ts.student_name studentName,
               ts.student_id   studentId,
               ts.third_biz_id thirdBizId
        FROM t_student AS ts
        WHERE ts.school_id = #{schoolId}
          AND ts.third_party_id = #{thirdPartyId}
    </select>

    <select id="selectStudentThirdInfoByStuIds" resultType="map" parameterType="map">
        select
        student_id studentId,
        third_biz_id thirdBizId
        from t_student
        where student_id in
        <foreach collection="studentIds" item="studentId" separator="," open="(" close=")">
            #{studentId}
        </foreach>
        and third_party_id = #{thirdPartyId}
    </select>

    <!--统计错题本学生数量-->
    <select id="countStudentForWrongBook" parameterType="map" resultType="long">
        SELECT
        COUNT(*)
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        JOIN t_class tc ON tcs.class_id = tc.class_id
        AND tc.class_type = 1
        WHERE
        ts.student_id IN
        <foreach collection="studentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="search != null and search != ''">
            AND (ts.student_name LIKE CONCAT(#{search}, '%') OR ts.student_num LIKE CONCAT(#{search}, '%'))
        </if>
    </select>

    <!--获取学生的基本信息-->
    <select id="getStudentForWrongBook" parameterType="map" resultType="map">
        SELECT
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum,
        tc.class_id classId,
        tc.class_name className
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        JOIN t_class tc ON tcs.class_id = tc.class_id
        AND tc.class_type = 1
        WHERE
        ts.student_id IN
        <foreach collection="studentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="search != null and search != ''">
            AND (ts.student_name LIKE CONCAT(#{search}, '%') OR ts.student_num LIKE CONCAT(#{search}, '%'))
        </if>
        <if test="currentIndex != null and pageSize != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!--根据唯一键查询学生信息-->
    <select id="selectStudentListByLogic" parameterType="map" resultType="map">
        SELECT
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        tsc.area_id areaId,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE
        (ts.student_name, ts.student_num) IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.studentName}, #{item.studentNum})
        </foreach>
        <if test="schoolId != null and schoolId != ''">
            AND ts.school_id = #{schoolId}
        </if>
    </select>

    <select id="selectStudentByLogic" parameterType="map" resultType="map">
        SELECT tsc.school_id     schoolId,
               tsc.school_name   schoolName,
               ts.student_id     studentId,
               ts.student_name   studentName,
               ts.student_num    studentNum,
               tsc.school_status schoolStatus,
               sa.account_id     accountId
        FROM t_student ts
                 JOIN t_school tsc ON ts.school_id = tsc.school_id
                 JOIN sys_user su ON ts.student_id = su.relative_id AND su.user_type = 4
                 JOIN sys_account sa ON sa.account_id = su.account_id
        WHERE student_name = #{studentName}
          and student_num = #{studentNum}
    </select>

    <!--查询学生信息-->
    <select id="getStudentByPhoneAndStudentName" parameterType="map" resultType="map">
        SELECT
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum,
        ts.student_phone_aes studentPhoneAes,
        ts.student_phone_aes identityCardNoAes
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE
        (ts.student_name, ts.student_phone_aes) IN

        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.studentName}, #{item.studentPhoneAes})
        </foreach>
    </select>

    <insert id="insertStudentForUnionExamImport" parameterType="list" useGeneratedKeys="true" keyProperty="studentId">
        INSERT INTO t_student(
        school_id,
        student_num,
        student_name,
        candidate,
        student_status,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.schoolId},
            #{item.studentNum},
            #{item.studentName},
            #{item.studentNum},
            1,
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        student_status = VALUES(student_status),
        modifier_id = VALUES(modifier_id),
        modifier_name = VALUES(modifier_name),
        modify_date_time = VALUES(modify_date_time)
    </insert>

    <insert id="insertClassStudentForUnionExamImport" parameterType="list">
        INSERT INTO t_class_student(
        class_id,
        student_id,
        inner_date,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.classId},
            #{item.studentId},
            #{item.innerDate},
            #{item.userId},
            #{item.userName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
    </insert>

    <select id="getStudentListForExamUnion" parameterType="map" resultType="map">
        SELECT tsc.school_id   schoolId,
               tsc.school_name schoolName,
               ts.student_id   studentId,
               ts.student_name studentName,
               ts.student_num  studentNum
        FROM t_student ts
                 JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE ts.school_id = #{schoolId}
    </select>

    <select id="selectStudentForUnionExamImport" parameterType="map" resultType="map">
        SELECT
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE
        (ts.student_name, ts.student_num, ts.school_id) IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.studentName}, #{item.studentNum},#{item.schoolId})
        </foreach>
    </select>

    <!--根据唯一键查询学生-->
    <select id="getStudentListForBatchImport" parameterType="map" resultType="map">
        SELECT
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE
        (ts.student_name, ts.student_num) IN

        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.studentName}, #{item.studentNum})
        </foreach>
        <if test="schoolId != null and schoolId != ''">
            AND ts.school_id = #{schoolId}
        </if>
    </select>

    <select id="getStudentInfoListForBatchImport" parameterType="map" resultType="map">
        SELECT
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_num studentNum,
        ts.student_name studentName ,
        tc.class_id classId,
        tc.grade_id gradeId,
        tc.class_name className,
        tc.class_type classType
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        JOIN t_class tc ON tc.class_id = tcs.class_id
        AND tc.class_type = 1
        JOIN t_grade tg ON tc.grade_id = tg.grade_id AND tg.graduate_status = 0
        WHERE
        ts.school_id = #{schoolId} AND tg.grade_id = #{gradeId}
        AND (ts.student_name, ts.student_num) IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.studentName}, #{item.studentNum})
        </foreach>
    </select>

    <!-- 批量插入学生 -->
    <insert id="batchInsertStudentForBatchImport" parameterType="list" keyProperty="studentId" useGeneratedKeys="true">
        INSERT INTO t_student(
        student_name,
        student_num,
        school_id,
        student_phone,
        student_phone_aes,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time,
        student_no,seat_number,candidate, student_status
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.studentName},
            #{item.studentNum},
            #{item.schoolId},
            #{item.studentPhone},
            #{item.studentPhoneAes},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.studentNo},#{item.seatNumber},#{item.candidate},1
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        student_no=VALUES(student_no),
        seat_number=VALUES(seat_number),
        student_phone=VALUES(student_phone),
        student_phone_aes=VALUES(student_phone_aes),
        student_status=VALUES(student_status),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!--通过学生姓名和学号查询学生信息-->
    <select id="getStudentListByStudentNameAndStudentNum" parameterType="map" resultType="map">
        SELECT
        ts.student_name studentName,
        ts.student_num studentNum
        FROM
        t_student ts
        JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        JOIN t_class tc ON tcs.class_id = tc.class_id AND tc.class_type = 1
        JOIN t_grade tg ON tc.grade_id = tg.grade_id
        WHERE tg.graduate_status = 0
        AND ts.school_id = #{schoolId}
        AND (ts.student_name, ts.student_num) IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            (#{item.studentName}, #{item.studentNum})
        </foreach>

    </select>

    <select id="getEmployee" parameterType="map" resultType="map">
        SELECT tc.company_name       companyName,
               te.employee_name      employeeName,
               te.employee_phone_aes employeePhoneAes
        FROM t_employee te
                 JOIN t_company tc ON te.company_id = tc.company_id
    </select>

    <!--    &lt;!&ndash;获取学生列表&ndash;&gt;-->
    <!--    <select id="getStudentForBatchExport" parameterType="map" resultType="map">-->
    <!--        SELECT-->
    <!--        ts.student_id           studentId,-->
    <!--        ts.student_num          studentNum,-->
    <!--        ts.student_name         studentName,-->
    <!--        ts.student_no           studentNo,-->
    <!--        ts.seat_number          seatNumer,-->
    <!--        tg.grade_name           gradeName,-->
    <!--        sa.account_name_aes     accountNameAes,-->
    <!--        GROUP_CONCAT(DISTINCT tc.class_id SEPARATOR ',') classId,-->
    <!--        GROUP_CONCAT(DISTINCT tc.class_name SEPARATOR ',') className,-->
    <!--        GRO UP_CONCAT(DISTINCT tc.class_sort SEPARATOR ',') classSort,-->
    <!--        tcsg.course_selection_group_id      courseSelectionGroupId,-->
    <!--        tcsg.course_selection_group_name    courseSelectionGroupName,-->
    <!--        tscs.foreign_course_id              foreignCourseId,-->
    <!--        tco.course_name                     courseName-->
    <!--        FROM t_student ts-->
    <!--        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id-->
    <!--        INNER JOIN t_class tc ON tcs.class_id = tc.class_id-->
    <!--        INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id and tg.graduate_status = 0-->
    <!--        LEFT JOIN sys_user su ON ts.student_id = su.relative_id AND su.user_type = 4-->
    <!--        LEFT JOIN sys_account sa ON su.account_id = sa.account_id-->
    <!--        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id-->
    <!--        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id-->
    <!--        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id-->
    <!--        WHERE ts.school_id = #{schoolId} and ts.student_status = '1'-->
    <!--        GROUP BY ts.student_id-->
    <!--        ORDER BY tg.grade_id, tc.class_id-->
    <!--    </select>-->

    <!--获取学生列表-->
    <select id="getStudentForBatchExport" parameterType="map" resultType="map">
        SELECT ts.student_id                                      studentId,
               ts.student_num                                     studentNum,
               ts.student_name                                    studentName,
               ts.student_no                                      studentNo,
               ts.seat_number                                     seatNumer,
               tg.grade_name                                      gradeName,
               GROUP_CONCAT(DISTINCT tc.class_id SEPARATOR ',')   classId,
               GROUP_CONCAT(DISTINCT tc.class_name SEPARATOR ',') className,
               GROUP_CONCAT(DISTINCT tc.class_sort SEPARATOR ',') classSort
        FROM t_student ts
                 INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class tc ON tcs.class_id = tc.class_id
                 INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id and tg.graduate_status = 0
        WHERE ts.school_id = #{schoolId}
          and ts.student_status = '1'
        GROUP BY ts.student_id
        ORDER BY tg.grade_id, tc.class_id
    </select>

    <!--获取学生列表-->
    <select id="getStudentAccountForBatchExport" parameterType="map" resultType="map">
        SELECT ts.student_id       studentId,
               sa.account_name_aes accountNameAes
        FROM t_student ts
                 LEFT JOIN sys_user su ON ts.student_id = su.relative_id AND su.user_type = 4
                 LEFT JOIN sys_account sa ON su.account_id = sa.account_id
        WHERE ts.school_id = #{schoolId}
          and ts.student_status = '1'
    </select>

    <!--获取学生列表-->
    <select id="getStudentCourseForBatchExport" parameterType="map" resultType="map">
        SELECT ts.student_id                    studentId,
               tcsg.course_selection_group_id   courseSelectionGroupId,
               tcsg.course_selection_group_name courseSelectionGroupName,
               tscs.foreign_course_id           foreignCourseId,
               tco.course_name                  courseName
        FROM t_student ts
                 LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
                 LEFT JOIN t_course_selection_group tcsg
                           ON tscs.course_selection_group_id = tcsg.course_selection_group_id
                 LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE ts.school_id = #{schoolId}
          and ts.student_status = '1'
    </select>

    <select id="getStudent4Graduation" parameterType="map" resultType="map">
        SELECT ts.student_id   studentId,
               ts.student_name studentName,
               ts.student_num  studentNum
        FROM t_student ts
                 JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 JOIN t_class tc ON tc.class_id = tcs.class_id AND tc.class_type = 1
        WHERE tc.grade_id = #{gradeId}
    </select>

    <update id="updateStudentNameAndStudentNumber4Graduation" parameterType="map">
        UPDATE
            t_student
        SET student_name    = #{studentName},
            student_num     = #{studentNum},
            modifier_id     =#{userId},
            modifier_name   = #{userName},
            modify_date_time=#{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <update id="batchUpdateStudentForImport" parameterType="map">
        UPDATE
            t_student
        SET student_name    = #{studentName},
            student_num     = #{realStudentNum},
            seat_number     = #{realSeatNumber},
            student_no      = #{realStudentNo},
            modifier_id     =#{userId},
            modifier_name   = #{userName},
            modify_date_time=#{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <update id="batchUpdateSeatNumberForImport" parameterType="map">
        UPDATE
            t_student
        SET seat_number     = #{realSeatNumber},
            modifier_id     =#{userId},
            modifier_name   = #{userName},
            modify_date_time=#{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <select id="getStudentListBySchoolId" parameterType="map" resultType="map">
        SELECT
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum,
        ts.seat_number seatNumber,
        ts.student_no studentNo
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE ts.school_id = #{schoolId}
        <if test="studentNumList != null and studentNumList.size() > 0">
            AND ts.student_num IN
            <foreach collection="studentNumList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="studentNoList != null and studentNoList.size() > 0">
            AND ts.student_no IN
            <foreach collection="studentNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="studentList != null and studentList.size() > 0">
            AND ts.student_num IN
            <foreach collection="studentList" item="item" open="(" separator="," close=")">
                #{item.studentNum}
            </foreach>
        </if>
    </select>

    <select id="getStudentListByGradeId" parameterType="map" resultType="map">
        SELECT tsc.school_id   schoolId,
               tsc.school_name schoolName,
               ts.student_id   studentId,
               ts.student_name studentName,
               ts.student_num  studentNum,
               ts.student_no   studentNo,
               tg.grade_id     gradeId,
               tg.grade_name   gradeName,
               tc.class_id     classId,
               tc.class_name   className
        FROM t_student ts
                 JOIN t_school tsc ON tsc.school_id = ts.school_id
                 JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 JOIN t_class tc ON tc.class_id = tcs.class_id AND tc.class_type = 1
                 JOIN t_grade tg ON tc.grade_id = tg.grade_id AND tg.graduate_status = 0
        WHERE tg.grade_id = #{gradeId}
    </select>

    <select id="getStudentIdsByClassIds" resultType="long" parameterType="map">
        SELECT distinct
        ts.student_id
        FROM t_class_student tcs
        INNER JOIN t_student ts ON ts.student_id = tcs.student_id
        WHERE ts.student_status = 1 and tcs.class_id IN
        <foreach collection="classIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getStudentNameByStuIds" resultType="com.dongni.basedata.export.student.bean.StudentIdNameDTO" parameterType="list">
        select
            student_id studentId,
            student_name studentName
        from t_student
        where student_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getStudentByClassIds" resultType="map" parameterType="map">
        SELECT
        tc.class_id classId,
        tc.class_name className,
        ts.student_id studentId,
        ts.student_name studentName
        FROM t_class_student tcs
        INNER JOIN t_student ts ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tc.class_id = tcs.class_id
        WHERE ts.student_status = 1 and tcs.class_id IN
        <foreach collection="classIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="getStudentSchoolAreaCode" parameterType="map" resultType="string">
        select ta.area_code
        from t_student ts
                 inner join t_school tsc on ts.school_id = tsc.school_id
                 inner join t_area ta on tsc.area_id = ta.area_id
        where ts.student_id = #{studentId}
    </select>

    <!--获取学生年级信息-->
    <select id="getStudentGradeByStudentId" parameterType="map" resultType="map">
        SELECT ts.student_id studentId,
               tg.grade_type gradeType
        FROM t_student ts
                 INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class tc ON tcs.class_id = tc.class_id
                 INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id
        WHERE ts.student_id = #{studentId}
        limit 1
    </select>

    <select id="getStudentNumByIds" parameterType="com.dongni.exam.health.check.bean.bo.StudentUniqueInfoBO"
            resultType="com.dongni.exam.health.check.bean.bo.StudentUniqueInfoBO">
        select *
        from t_student
        where student_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.studentId}
        </foreach>
    </select>

    <!--通过classId和schoolId获取班级-->
    <select id="getStudentListByClassId" parameterType="map" resultType="map">
        SELECT tcs.class_id classId,
        tcs.student_id studentId,
        tg.grade_name gradeName,
        tc.class_sort classSort,
        tc.class_name className,
        ts.student_name studentName,
        ts.student_num studentNum
        FROM t_class_student tcs
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id AND tc.class_type = 1
        INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id
        INNER JOIN t_student ts ON tcs.student_id = ts.student_id
        WHERE ts.school_id = #{schoolId}
        AND tc.school_id = #{schoolId}
        AND tc.class_id = #{classId}
        <if test="search != null and search != ''">
            AND ts.student_name LIKE CONCAT ('%',#{search},'%')
        </if>
    </select>

    <select id="getStudentListBySchoolIdAndClassId" parameterType="map" resultType="map">
        select distinct
        tcs.student_id studentId,
        ts.student_name studentName
        from t_class tc
        inner join t_class_student tcs ON tc.class_id = tcs.class_id
        inner join t_student ts ON tcs.student_id = ts.student_id
        where tc.school_id = #{schoolId}
        and tc.class_id = #{classId}
        and ts.student_status = 1
        <if test="search != null and search != ''">
            and ts.student_name like concat ('%',#{search},'%')
        </if>
    </select>

    <!--获取学生以及班级信息-->
    <select id="getClassStudentDTO" parameterType="com.dongni.basedata.school.student.bean.param.StudentInfoParam"
            resultType="com.dongni.basedata.school.student.bean.dto.ClassStudentDTO">
        SELECT ts.student_id   relativeId,
               ts.student_id   studentId,
               ts.student_num  studentNum,
               ts.student_name studentName,
               ts.school_id    schoolId,
               tc.class_name   className
        FROM t_student ts
                 INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class tc ON tcs.class_id = tc.class_id AND tc.class_type = 1
        WHERE ts.student_name = #{studentName}
          AND ts.student_num = #{studentNum}
          AND ts.school_id = #{schoolId}
    </select>

    <!--获取学生以及班级信息-->
    <select id="getClassStudentInfo" parameterType="com.dongni.basedata.school.student.bean.param.StudentInfoParam"
            resultType="com.dongni.basedata.school.student.bean.dto.ClassStudentDTO">
        SELECT ts.student_id   relativeId,
               ts.student_id   studentId,
               ts.student_num  studentNum,
               ts.student_name studentName,
               tsc.school_id   schoolId,
               tsc.school_name schoolName,
               tc.class_name   className,
               tg.grade_id     gradeId,
               tg.grade_name   gradeName
        FROM t_student ts
                 INNER JOIN t_school tsc ON ts.student_id = tsc.student_id
                 INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class tc ON tcs.class_id = tc.class_id AND tc.class_type = 1
                 INNER JOIN t_grade tg ON tc.grade_id = tg.grade_id
        WHERE ts.student_id = #{studentId}
    </select>

    <update id="convertSyncStudent2InnerStudent" parameterType="list">
        UPDATE t_student ts
        JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        JOIN t_class tc ON tcs.class_id = tc.class_id
        SET
        ts.student_num = CONCAT(ts.student_num, '-', tc.grade_id, '-', 'BY'),
        ts.third_party_id = 0,
        ts.third_biz_id = 0,
        ts.modifier_id = 1,
        ts.modifier_name = '好专业同步毕业处理',
        ts.modify_date_time = NOW()
        WHERE
        tc.grade_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <select id="getAllClassStudentList" parameterType="map"
            resultType="com.dongni.basedata.school.escalation.bean.common.SchoolEscalationStudent">
        SELECT
        ts.student_id studentId,
        ts.student_name studentName,
        ts
        FROM t_student ts
        INNER JOIN t_class_student tcs ON tcs.student_id = ts.student_id
        INNER JOIN t_class tc ON tc.class_id = tcs.class_id
        LEFT JOIN t_student_tag tst ON tst.student_id = ts.student_id
        LEFT JOIN t_grade tg ON tg.grade_id = tc.grade_id
        WHERE
        tc.class_id IN
        <foreach collection="classIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND
        <if test="tagId != null and tagId != ''">
            tst.tag_id = #{tagId}
        </if>
        <if test="gradeId != null and gradeId != ''">
            tg.grade_id = #{gradeId}
        </if>
    </select>


    <select id="getStudentByIds" parameterType="list"
            resultType="com.dongni.basedata.school.escalation.bean.common.Student">
        SELECT
        ts.student_id studentId,
        ts.student_name studentName,
        ts.school_id schoolId,
        ts.student_num studentNum,
        tg.grade_id gradeId,
        tg.grade_name gradeName,
        tg.grade_type gradeType,
        tc.class_id classId,
        tc.class_name className,
        tc.arts_science artsScience,
        tcsg.course_selection_group_id courseSelectionGroupId,
        tcsg.course_selection_group_name courseSelectionGroupName,
        tscs.foreign_course_id foreignCourseId,
        tco.course_name foreignCourseName
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tco.course_id = tscs.foreign_course_id
        LEFT JOIN t_grade tg ON tg.grade_id = tc.grade_id
        WHERE ts.student_id IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and tc.class_type = '1'
    </select>

    <select id="getStudentByGradeIdAndSearch" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.student_num studentNum,
        ts.student_name studentName,
        tc.class_id classId,
        tc.class_name className,
        tcsg.course_selection_group_id courseSelectionGroupId,
        tcsg.course_selection_group_name courseSelectionGroupName,
        tscs.foreign_course_id foreignCourseId,
        tco.course_name foreignCourseName
        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        LEFT JOIN t_student_course_selection tscs ON ts.student_id = tscs.student_id
        LEFT JOIN t_course_selection_group tcsg ON tscs.course_selection_group_id = tcsg.course_selection_group_id
        LEFT JOIN t_course tco ON tscs.foreign_course_id = tco.course_id
        WHERE tc.grade_id = #{gradeId} and ts.student_status = '1'
        AND tc.class_type = '1'
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            )

        </if>

    </select>

    <select id="getDuplicateStudentNameAndNum"
            parameterType="com.dongni.basedata.school.student.bean.param.DuplicateStudentNameAndNumParam"
            resultType="map">
        SELECT ts.student_id        studentId,
               ts.student_num       studentNum,
               ts.student_name      studentName,
               ts.student_phone     studentPhone,
               ts.student_phone_aes studentPhoneAes
        FROM t_student ts
        WHERE ts.student_name = #{studentName}
          AND ts.student_num = #{studentNum}
          AND ts.student_id != #{excludeStudentId}
    </select>

    <select id="getGradeStudentCount" parameterType="map" resultType="int">
        SELECT count(DISTINCT ts.student_id)
        FROM t_student ts
                 INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
                 INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        WHERE tc.grade_id = #{gradeId}
          and ts.student_status = '1'
    </select>


    <select id="getStudentByPinYinNull" resultType="map">
        SELECT student_id   studentId,
               student_name studentName
        FROM t_student
        WHERE student_name_pinyin IS NULL
    </select>

    <select id="getStudentBySchoolIDAndPinYinNull" parameterType="map" resultType="map">
        SELECT student_id   studentId,
               student_name studentName
        FROM t_student
        WHERE school_id = #{schoolId}
          AND student_name_pinyin IS NULL
    </select>

    <update id="updateStudentName" parameterType="map">
        UPDATE
            t_student
        SET student_name_pinyin = #{studentNamePinyin}
        WHERE student_id = #{studentId}
    </update>


    <!--获取学生列表-->
    <select id="getAreaStudentListBySchoolId" parameterType="map" resultType="long">
        SELECT
        distinct ts.student_id studentId
        FROM t_school tsc
        JOIN t_grade tg ON tg.school_id=tsc.school_id
        JOIN t_class tc ON tg.grade_id=tc.grade_id
        JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        JOIN t_student ts ON ts.student_id = tcs.student_id
        WHERE tsc.school_id=#{schoolId}
        <if test="gradeId != null and gradeId != ''">
            AND tg.grade_type = #{gradeId}
        </if>
        AND tg.graduate_status = 0
        AND ts.student_status = '1' AND tc.class_type = 1
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            OR ts.student_name_pinyin LIKE #{search}
            )
        </if>
        ORDER BY tsc.school_status DESC,tsc.school_name ASC, tsc.school_id ASC,tc.class_sort,tc.class_id,ts.student_id
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--获取学生列表-->
    <select id="getStudentListByAreaCode" parameterType="map" resultType="long">
        SELECT
        distinct ts.student_id studentId
        FROM
        t_area ta
        INNER JOIN t_school tsc on tsc.area_id = ta.area_id
        LEFT JOIN t_grade tg ON tg.school_id = tsc.school_id
        INNER JOIN t_class tc ON tg.grade_id=tc.grade_id
        INNER JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        INNER join t_student ts on ts.student_id = tcs.student_id
        WHERE ts.student_status = '1' AND tc.class_type = 1
        and ta.area_code like concat('',#{areaCode},'%')
        <if test="gradeId != null and gradeId != ''">
            AND tg.grade_type = #{gradeId}
        </if>
        AND tg.graduate_status = 0
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            OR ts.student_name_pinyin LIKE #{search}
            )
        </if>
        ORDER BY tsc.school_status DESC,tsc.school_name ASC, tsc.school_id ASC,tc.class_sort,tc.class_id,ts.student_id
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!--获取学生列表-->
    <select id="getStudentListByStudentIds" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        ts.student_num studentNum,
        ts.student_name studentName,
        ts.candidate candidate,
        tc.arts_science artsScience,
        tc.class_id classId,
        tc.class_name className,
        tc.class_sort classSort,
        tc.grade_id gradeId ,
        tsc.school_id schoolId,
        tsc.school_name schoolName,
        tsc.third_party_id thirdPartyId,
        tsc.school_status schoolStatus

        FROM t_student ts
        INNER JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        INNER JOIN t_class tc ON tcs.class_id = tc.class_id
        INNER JOIN t_grade tg ON tg.grade_id = tc.grade_id
        LEFT JOIN t_school tsc ON tsc.school_id = ts.school_id
        WHERE ts.student_status = '1' AND tc.class_type = 1
        <if test="studentIdList != null">
            AND ts.student_id IN
            <foreach collection="studentIdList" item="studentId" open="(" close=")" separator=",">
                #{studentId}
            </foreach>
        </if>
        AND tg.graduate_status = 0
        GROUP BY ts.student_id
        ORDER BY tsc.school_status DESC,tsc.school_name ASC, tsc.school_id ASC,tc.class_sort,tc.class_id,ts.student_id
    </select>

    <!--获取学生列表-->
    <select id="getStudentListBySchoolIdsCount" parameterType="map" resultType="int">
        SELECT /*+INL_JOIN(tsc,ts,tcs,tc,tg)*/
        COUNT( ts.student_id) studentCount
        FROM t_school tsc
        JOIN t_grade tg ON tsc.school_id=tg.school_id
        JOIN t_class tc ON tg.grade_id=tc.grade_id
        JOIN t_class_student tcs ON tcs.class_id = tc.class_id
        JOIN t_student ts ON ts.student_id = tcs.student_id
        WHERE ts.student_status = '1' AND tc.class_type = 1
        <if test="gradeId != null and gradeId != ''">
            AND tg.grade_type = #{gradeId}
        </if>
        <if test="schoolIdList != null">
            AND tsc.school_id IN
            <foreach collection="schoolIdList" item="schoolId" open="(" close=")" separator=",">
                #{schoolId}
            </foreach>
        </if>
        <if test="search != null and search != ''">
            <bind name="search" value="'%'+search+'%'"/>
            AND( ts.student_num LIKE #{search}
            OR ts.student_name LIKE #{search}
            OR ts.student_name_pinyin LIKE #{search}
            )
        </if>
    </select>

    <select id="getStudentInfoListByIds" parameterType="com.dongni.basedata.school.student.bean.param.StudentInfoParam"
            resultType="com.dongni.basedata.school.student.bean.dto.StudentInfoDTO">
        SELECT
        ts.school_id schoolId,
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum,
        ts.student_status studentStatus,
        tc.class_id classId,
        tc.class_name className,
        tg.grade_id gradeId,
        tg.grade_name gradeName,
        tg.stage stage
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        LEFT JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        LEFT JOIN t_class tc ON tc.class_id = tcs.class_id
        LEFT JOIN t_grade tg ON tc.grade_id = tg.grade_id
        WHERE
        ts.student_id IN
        <foreach collection="studentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tc.class_type = 1
    </select>

    <select id="getStudentForWrongBookIntroducedStudent"
            parameterType="com.dongni.basedata.school.student.bean.param.StudentInfoParam"
            resultType="com.dongni.basedata.school.student.bean.dto.StudentInfoDTO">
        SELECT
        ts.school_id AS schoolId,
        tsc.school_name AS schoolName,
        ts.student_id AS studentId,
        ts.student_name AS studentName,
        ts.student_num AS studentNum,
        ts.student_status AS studentStatus,
        tc.class_id AS classId,
        tc.class_name AS className,
        tg.grade_id AS gradeId,
        tg.grade_name AS gradeName,
        tg.stage AS stage
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        JOIN t_class tc ON tc.class_id = tcs.class_id AND tc.class_type = 1
        JOIN t_grade tg ON tc.grade_id = tg.grade_id AND ts.school_id = tg.school_id
        WHERE
        ts.student_id IN
        <foreach collection="studentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        UNION ALL
        SELECT
        ts.school_id AS schoolId,
        tsc.school_name AS schoolName,
        ts.student_id AS studentId,
        ts.student_name AS studentName,
        ts.student_num AS studentNum,
        ts.student_status AS studentStatus,
        tc.class_id AS classId,
        tc.class_name AS className,
        tg.grade_id AS gradeId,
        tg.grade_name AS gradeName,
        tg.stage AS stage
        FROM
        t_student ts
        JOIN t_school tsc ON ts.school_id = tsc.school_id
        LEFT JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        LEFT JOIN t_class tc ON tc.class_id = tcs.class_id AND tc.class_type = 1
        LEFT JOIN t_grade tg ON tc.grade_id = tg.grade_id AND ts.school_id = tg.school_id
        WHERE
        ts.student_id IN
        <foreach collection="studentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tcs.student_id IS NULL
    </select>

    <select id="getStudentByStudentNums" parameterType="map"
            resultType="map">
        SELECT
        ts.school_id schoolId,
        tsc.school_name schoolName,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum,
        ts.student_status studentStatus,
        tc.class_id classId,
        tc.class_name className,
        tg.grade_id gradeId,
        tg.grade_name gradeName,
        tg.stage stage
        FROM
        t_student ts
        LEFT JOIN t_school tsc ON ts.school_id = tsc.school_id
        LEFT JOIN t_class_student tcs ON ts.student_id = tcs.student_id
        LEFT JOIN t_class tc ON tc.class_id = tcs.class_id
        LEFT JOIN t_grade tg ON tc.grade_id = tg.grade_id
        WHERE
        ts.school_id = #{schoolId}
        AND
        ts.student_num IN
        <foreach collection="studentNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tc.class_type = 1
        AND ts.student_status = 1
    </select>

    <select id="getStudentStatusByStudentNum" parameterType="map"
            resultType="map">
        SELECT
        ts.school_id schoolId,
        ts.student_id studentId,
        ts.student_name studentName,
        ts.student_num studentNum,
        ts.student_status studentStatus
        FROM
        t_student ts
        WHERE
        ts.school_id = #{schoolId} AND
        ts.student_num IN
        <foreach collection="studentNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="getStudentStatusBySchoolId" parameterType="map"
            resultType="map">
        SELECT ts.school_id      schoolId,
               tsc.school_name   schoolName,
               ts.student_id     studentId,
               ts.student_name   studentName,
               ts.student_num    studentNum,
               ts.student_status studentStatus
        FROM t_student ts
                 INNER JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE ts.school_id = #{schoolId}
    </select>

    <select id="getStduentBySchoolIdAndStudentNum" parameterType="map"
            resultType="map">
        SELECT ts.school_id      schoolId,
               ts.student_id     studentId,
               ts.student_name   studentName,
               ts.student_num    studentNum,
               ts.student_status studentStatus
        FROM t_student ts
        WHERE ts.school_id = #{schoolId}
          AND ts.student_num = #{studentNum}
    </select>

    <select id="getStduentBySchoolIdAndStudentNumCount" parameterType="map"
            resultType="int">
        SELECT count(*)
        FROM t_student ts
        WHERE ts.school_id = #{schoolId}
          AND ts.student_num = #{studentNum}
    </select>


    <insert id="batchInsertBaseStudent" parameterType="list" keyProperty="studentId" useGeneratedKeys="true">
        INSERT INTO t_student(
        student_name,
        student_num,
        school_id,
        student_phone,
        student_phone_aes,
        creator_id,creator_name,create_date_time,
        modifier_id,modifier_name,modify_date_time,
        student_no,seat_number, candidate,student_status
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.studentName},
            #{item.studentNum},
            #{item.schoolId},
            #{item.studentPhone},
            #{item.studentPhoneAes},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.userId},#{item.userName},#{item.currentTime},
            #{item.studentNo},#{item.seatNumber},#{item.candidate},1
            )
        </foreach>
    </insert>

    <update id="batchUpdateBaseStudentForImport"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.application.bean.InsertStudentBean">
        UPDATE
            t_student
        SET student_name      = #{studentName},
            student_num       = #{studentNum},
            seat_number       = #{seatNumber},
            candidate         = #{candidate},
            student_no        = #{studentNo},
            student_phone     = #{studentPhone},
            student_status    = 1,
            student_phone_aes = #{studentPhoneAes},
            modifier_id       =#{userId},
            modifier_name     = #{userName},
            modify_date_time=#{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <update id="batchUpdateBaseStudentNumForImport"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.application.bean.InsertStudentBean">
        UPDATE
            t_student
        SET student_num     =#{studentNum},
            modifier_id     =#{userId},
            modifier_name   =#{userName},
            modify_date_time=#{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <update id="updateBaseStudentNum"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.application.bean.InsertStudentBean">
        UPDATE
            t_student
        SET student_num =#{studentNum}
        WHERE student_id = #{studentId}
    </update>

    <update id="batchUpdateBaseStudentNoForImport"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.application.bean.InsertStudentBean">
        UPDATE
            t_student
        SET seat_number     = #{seatNumber},
            student_no      = #{studentNo},
            modifier_id     =#{userId},
            modifier_name   = #{userName},
            modify_date_time=#{currentTime}
        WHERE student_id = #{studentId}
    </update>

    <update id="batchUpdateUserAccountId"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.application.bean.InsertStudentBean">
        UPDATE sys_user
        SET account_id      = #{accountId},
            modifier_id     =#{userId},
            modifier_name   = #{userName},
            modify_date_time=#{currentTime}
        WHERE relative_id = #{studentId}
          AND user_status = 1
          AND user_type = 4
    </update>

    <update id="replaceCandidate"
            parameterType="com.dongni.basedata.school.client.schoolClassStructure.application.bean.InsertStudentBean">
        UPDATE t_student
        SET candidate = student_num,
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE
        student_id IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>


    <select id="lockStudents" parameterType="map" resultType="long">

        SELECT student_id FROM t_student WHERE
        student_id IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY student_id FOR UPDATE
    </select>

    <!--获取学生学校id信息-->
    <select id="getStudentSchoolByStudentId" parameterType="map" resultType="long">
        SELECT tsc.school_id schoolId
        FROM t_student ts
                 INNER JOIN t_school tsc ON ts.school_id = tsc.school_id
        WHERE ts.student_id = #{studentId}
    </select>


    <!--获取学生学校id信息-->
    <select id="getStudentNameByStudentId" parameterType="map" resultType="string">
        SELECT ts.student_name studentName
        FROM t_student ts
        WHERE ts.student_id = #{studentId}
    </select>

    <select id="getStudentClassesOpenCourseList" parameterType="map" resultType="map">
        SELECT
            tc.course_id     courseId,
            tc.course_name   courseName,
            tc.course_sort   courseSort,
            tc.stage         stage,
            tc.arts_science  artsScience,
            tc.member_count  memberCount,
            tc.member_str    memberStr
        FROM t_class_student tcs
        INNER JOIN t_class_teacher tct ON tct.class_id = tcs.class_id AND tct.course_open = 1
        INNER JOIN t_course tc ON tc.course_id = tct.course_id
        WHERE tcs.student_id = #{studentId}
        GROUP BY tc.course_id
        ORDER BY tc.stage ASC, tc.course_sort ASC
    </select>

    <!--获取学生列表-->
    <select id="getSchoolByStudentIds" parameterType="map" resultType="map">
        SELECT
        ts.student_id studentId,
        tsc.school_id schoolId,
        tsc.school_name schoolName


        FROM t_student ts
        LEFT JOIN t_school tsc ON tsc.school_id = ts.school_id
        WHERE ts.student_status = '1'
        AND ts.student_id IN
        <foreach collection="studentIdList" item="studentId" open="(" close=")" separator=",">
            #{studentId}
        </foreach>

    </select>


</mapper>

