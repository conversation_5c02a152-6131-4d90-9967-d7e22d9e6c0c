<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="StudyGuidePublisherTeacherCourseMapper">

    <insert id="insertPublisherTeacherCourseList" parameterType="list"
            useGeneratedKeys="true"
            keyProperty="studyGuidePublisherTeacherCourseId"
            keyColumn="study_guide_publisher_teacher_course_id">
        INSERT INTO t_study_guide_publisher_teacher_course (
            study_guide_publisher_id,
            study_guide_publisher_teacher_id,
            stage,
            course_id,
            course_name,
            creator_id, creator_name, create_date_time,
            modifier_id, modifier_name, modify_date_time
        ) VALUES
        <foreach collection="list" item="publisherTeacherCourse" separator=",">
            (
            #{publisherTeacherCourse.studyGuidePublisherId},
            #{publisherTeacherCourse.studyGuidePublisherTeacherId},
            #{publisherTeacherCourse.stage},
            #{publisherTeacherCourse.courseId},
            #{publisherTeacherCourse.courseName},
            #{publisherTeacherCourse.creatorId},
            #{publisherTeacherCourse.creatorName},
            #{publisherTeacherCourse.createDateTime},
            #{publisherTeacherCourse.modifierId},
            #{publisherTeacherCourse.modifierName},
            #{publisherTeacherCourse.modifyDateTime}
            )
        </foreach>
    </insert>

    <update id="updatePublisherTeacherCourse" parameterType="com.dongni.basedata.studyguide.bean.po.StudyGuidePublisherTeacherCoursePO">
        UPDATE t_study_guide_publisher_teacher_course
        SET course_name = #{courseName},
            modifier_id = #{modifierId},
            modifier_name = #{modifierName},
            modify_date_time = #{modifyDateTime}
        WHERE study_guide_publisher_id = #{studyGuidePublisherId}
          AND study_guide_publisher_teacher_id = #{studyGuidePublisherTeacherId}
          AND study_guide_publisher_teacher_course_id = #{studyGuidePublisherTeacherCourseId}
          AND course_name != #{courseName}
    </update>

    <delete id="deleteAllPublisherTeacherCourse"
            parameterType="com.dongni.basedata.studyguide.bean.mybatis.PublisherTeacherCourseDeleteAllQO">
        DELETE FROM t_study_guide_publisher_teacher_course
        WHERE study_guide_publisher_id = #{studyGuidePublisherId}
          AND study_guide_publisher_teacher_id = #{studyGuidePublisherTeacherId}
    </delete>

    <delete id="deletePublisherTeacherCourse"
            parameterType="com.dongni.basedata.studyguide.bean.mybatis.PublisherTeacherCourseDeleteQO">
        DELETE FROM t_study_guide_publisher_teacher_course
        WHERE study_guide_publisher_id = #{studyGuidePublisherId}
          AND study_guide_publisher_teacher_id = #{studyGuidePublisherTeacherId}
          AND study_guide_publisher_teacher_course_id IN
            <foreach collection="studyGuidePublisherTeacherCourseIdList" item="studyGuidePublisherTeacherCourseId"
                     open="(" separator="," close=")">
                #{studyGuidePublisherTeacherCourseId}
            </foreach>
    </delete>

</mapper>
