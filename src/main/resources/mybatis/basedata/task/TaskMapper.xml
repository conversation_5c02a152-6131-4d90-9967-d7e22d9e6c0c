<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="TaskMapper">

    <!-- 根据队列编码查询任务 -->
    <select id="selectWaitingTaskByQueueCode" parameterType="map" resultType="map">
        SELECT
          task_id taskId,
          task_name taskName,
          task_type taskType,
          queue_code queueCode,
          total_step totalStep,
          step step,
          task_status taskStatus
        FROM t_task
        WHERE task_type = #{taskType}
        AND task_status = 1
        AND queue_code = #{queueCode}
        limit 1
    </select>

  <select id="getExecutingTaskIdByExamIdAndStatId" parameterType="map" resultType="long">
    SELECT tt.task_id
    FROM t_task tt
    inner join t_task_analysis tta on tt.task_id = tta.task_id
    where tta.exam_id = #{examId}
      and tta.stat_id = #{statId}
      and tt.task_status = #{taskStatus}
    limit 1
  </select>

    <!-- 新增任务 -->
    <insert id="insertTask" parameterType="map" useGeneratedKeys="true" keyColumn="task_id" keyProperty="taskId">
       INSERT INTO t_task (
        task_name,
        task_type,
        queue_code,
        total_step,
        step,
        task_status,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
       ) VALUES (
        #{taskName},
        #{taskType},
        #{queueCode},
        #{totalStep},
        #{step},
        #{taskStatus},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
       )
   </insert>

  <!-- 新增知识点的统计任务 -->
  <insert id="insertKnowledgeTask" parameterType="map" useGeneratedKeys="true" keyColumn="task_id" keyProperty="taskId">
    INSERT INTO t_task (
      task_name,
      task_type,
      queue_code,
      total_step,
      step,
      task_status,
      retry_count,
      creator_id,
      creator_name,
      create_date_time,
      modifier_id,
      modifier_name,
      modify_date_time
    ) VALUES (
      #{taskName},
      #{taskType},
      #{queueCode},
      #{totalStep},
      #{step},
      #{taskStatus},
      20,
      #{userId},
      #{userName},
      #{currentTime},
      #{userId},
      #{userName},
      #{currentTime}
    )
  </insert>

    <!-- 新增统计任务 -->
    <insert id="insertTaskAnalysis" parameterType="map">
        INSERT INTO t_task_analysis (
          task_id,
          exam_id,
          exam_name,
          stat_id,
          params_json,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{taskId},
          #{examId},
          #{examName},
          #{statId},
          #{paramsJson},
          #{userId},
          #{userName},
          #{currentTime},
          #{userId},
          #{userName},
          #{currentTime}
        )
    </insert>

    <!-- 根据考试报告查询统计任务 -->
    <select id="getTaskInStats" parameterType="map" resultType="map">
        SELECT
          tt.task_id taskId,
          tt.task_name taskName,
          tt.task_status taskStatus,
          tt.total_step totalStep,
          tt.step step,
          tta.exam_id examId,
          tta.stat_id statId,
          tta.create_date_time createDateTime,
          tta.modify_date_time modifyDateTime
        FROM t_task tt
        INNER JOIN t_task_analysis tta ON tt.task_id = tta.task_id
        WHERE tta.exam_id = #{examId}
        AND tta.stat_id IN
        <foreach collection="stats" item="item" open="(" close=")" separator=",">
            #{item.statId}
        </foreach>
        ORDER BY tt.create_date_time DESC, if(tt.task_status in (1, 2), 0, 1), tt.task_id desc
    </select>

    <!-- 查询报告统计任务数量 -->
    <select id="selectTaskAnalysisCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_task tt
        INNER JOIN t_task_analysis tta ON tt.task_id = tta.task_id
        WHERE tta.exam_id = #{examId}
        AND tta.stat_id = #{statId}
    </select>

    <!-- 查询报告统计任务 -->
    <select id="selectTaskAnalysis" parameterType="map" resultType="map">
        SELECT
            tt.task_id taskId,
            tt.task_name taskName,
            tt.task_status taskStatus,
            tt.total_step totalStep,
            tt.step step,
            tta.exam_id examId,
            tta.stat_id statId
        FROM t_task tt
        INNER JOIN t_task_analysis tta ON tt.task_id = tta.task_id
        WHERE tta.exam_id = #{examId}
        AND tta.stat_id = #{statId}
        ORDER BY tt.create_date_time DESC, if(tt.task_status in (1, 2), 0, 1), tt.task_id desc
        <if test="currentIndex != null and pageSize != null">
            LIMIT #{currentIndex}, #{pageSize}
        </if>
    </select>

    <!-- 更新任务状态 -->
    <update id="updateTaskStatus" parameterType="map">
        UPDATE t_task
        SET task_status = #{taskStatus},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE task_id = #{taskId}
    </update>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateTaskStatus" parameterType="map">
        UPDATE t_task
        SET task_status = #{taskStatus},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE task_id in
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId}
        </foreach>
    </update>

    <!-- 更新任务状态在异常的情况下 -->
    <update id="updateTaskStatusByException" parameterType="map">
        UPDATE t_task
        SET task_status = #{taskStatus},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE task_id = #{taskId}
        AND task_status IN (1, 2)
    </update>

    <!--根据taskId获取统计任务信息-->
    <select id="getTaskByTaskId" parameterType="map" resultType="map">
        SELECT task_name taskName,
               total_step totalStep,
               step step,
               task_status taskStatus
        FROM t_task
        WHERE task_id = #{taskId}
    </select>

    <!--  获取统计分析任务列表  -->
    <select id="getTaskList" parameterType="map" resultType="map">
        SELECT
            tt.task_id taskId,
            tt.task_name taskName,
            tt.task_status taskStatus,
            tt.task_type taskType,
            tt.queue_code queueCode,
            tt.modify_date_time modifyDateTime,
            tta.params_json paramsJson,
            count(ttr.task_retry_id) retryCount
        FROM t_task tt
        INNER JOIN t_task_analysis tta ON tt.task_id = tta.task_analysis_id
        LEFT JOIN t_task_retry ttr ON tt.task_id = ttr.task_id
        WHERE task_status IN
        <foreach collection="taskStatusList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY tt.task_id
    </select>

    <select id="getTaskAnalysisDetail" parameterType="map" resultType="map">
        SELECT
            t1.task_id taskId,
            t1.task_name taskName,
            t1.task_type taskType,
            t1.queue_code queueCode,
            t1.task_status taskStatus,
            t1.creator_id creatorId,
            t1.creator_name creatorName,
            t1.create_date_time createDateTime,
            t2.exam_id examId,
            t2.exam_name examName,
            t2.stat_id statId,
            t2.params_json paramsJson
        FROM t_task t1
        INNER JOIN t_task_analysis t2 ON t1.task_id = t2.task_id
        WHERE t1.task_id = #{taskId}
    </select>

    <update id="updateTaskAnalysis" parameterType="map">
        UPDATE t_task
        SET step = #{step},
            task_status = #{taskStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE task_id = #{taskId}
    </update>

<!--  插入任务刷新记录  -->
    <insert id="insertTaskRetry" parameterType="map">
        INSERT INTO t_task_retry(
            task_id,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES (
            #{taskId},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
        )
    </insert>

    <!--  批量插入任务刷新记录  -->
    <insert id="batchInsertTaskRetry" parameterType="map">
        INSERT INTO t_task_retry(
            task_id,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES
        <foreach collection="taskIds" item="item" separator=",">
            (
                #{item},
                #{userId},
                #{userName},
                #{currentTime},
                #{userId},
                #{userName},
                #{currentTime}
            )
        </foreach>

    </insert>

    <!--查询报告统计生成状态-->
    <select id="getStatGeneratorStatus" resultType="map" parameterType="map">
        SELECT
            COUNT(*) completeNum
        FROM
            t_task t,
            t_task_analysis ta
        WHERE
            t.task_id = ta.task_id
          AND t.task_status = 3
          AND ta.exam_id = #{examId}
          AND ta.stat_id = #{statId}
          AND t.queue_code = #{queueCode}
    </select>

  <select id="getExamIdsByTaskCompleteTime" resultType="long" parameterType="map">
    select distinct tta.exam_id
    from t_task tt
    inner join t_task_analysis tta on tt.task_id = tta.task_id
    where tt.task_status in
    <foreach collection="taskStatusList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
      and tt.modify_date_time >= #{completeTime}
      and tta.stat_id = 0
  </select>
</mapper>