<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ThirdStudentMapper">

    <!--获取有效的数据 适用于validate 从中间库到正式库-->
    <select id="getStudentEffective" parameterType="map" resultType="map">
        SELECT
            thti.third_task_item_id  thirdTaskItemId,
            thti.table_name          tableName,
            thti.data_status         dataStatus,
            tts.third_student_id     thirdBizId,
            tts.third_party_id       thirdPartyId,
            tts.third_school_id      thirdSchoolId,
            tts.student_num          studentNum,
            tts.student_name         studentName,
            tts.student_name_pinyin  studentNamePinyin,
            tts.student_phone        studentPhone,
            tts.student_phone_aes    studentPhoneAes,
            tts.student_no           studentNo,
            tts.student_status       studentStatus,
            tts.identity_card_no     identityCardNo,
            tts.identity_card_no_aes identityCardNoAes,
            tts.seat_number          seatNumber,
            tts.deleted              deleted,
            tts.creator_id           creatorId,
            tts.creator_name         creatorName,
            tts.create_date_time     createDateTime,
            tts.modifier_id          modifierId,
            tts.modifier_name        modifierName,
            tts.modify_date_time     modifyDateTime
        FROM t_third_task_item thti
        INNER JOIN t_third_student tts
            ON tts.third_student_id = thti.third_biz_id
            AND tts.deleted = 0
        WHERE thti.third_task_id = #{thirdTaskId}
          AND thti.table_name = 't_third_student'
    </select>

    <!-- 从中间库获取数据同步到生产库 -->
    <select id="getStudent" parameterType="map" resultType="map">
        SELECT
        t.third_student_id                 thirdBizId,
        t.third_party_id            thirdPartyId,
        thti.third_task_item_id               thirdTaskItemId,
        thti.data_status               dataStatus,
        t.third_school_id                thirdSchoolId,
        t.student_num                studentNum,
        t.student_name                studentName,
        t.student_phone                studentPhone,
        t.student_phone_aes                studentPhoneAes,
        t.student_no                studentNo,
        t.student_status                studentStatus,
        t.identity_card_no                identityCardNo,
        t.identity_card_no_aes                identityCardNoAes,
        t.seat_number                seatNumber,
        t.deleted deleted,
        t.creator_id creatorId,
        t.creator_name creatorName,
        t.create_date_time createDateTime,
        t.modifier_id modifierId,
        t.modifier_name modifierName,
        t.modify_date_time modifyDateTime
        FROM t_third_student t
        INNER JOIN t_third_task_item thti ON thti.third_biz_id = t.third_student_id
        WHERE thti.third_task_id = #{thirdTaskId}
        AND thti.table_name = #{tableName}
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>

    <!--获取中间库的所有学生，通过学校ID -->
    <select id="getStudentBySchoolId" parameterType="map" resultType="map">
        SELECT
        t.third_student_id                 thirdBizId,
        t.deleted,
        t.third_party_id            thirdPartyId,
        t.third_school_id                thirdSchoolId,
        t.student_num                studentNum,
        t.student_name                studentName,
        t.student_name_pinyin                studentNamePinyin,
        t.student_phone                studentPhone,
        t.student_phone_aes                studentPhoneAes,
        t.student_no                studentNo,
        t.student_status                studentStatus,
        t.identity_card_no                identityCardNo,
        t.identity_card_no_aes                identityCardNoAes,
        t.seat_number                seatNumber,
        t.third_primary_key thirdPrimaryKey,
        t.creator_id creatorId,
        t.creator_name creatorName,
        t.create_date_time createDateTime,
        t.modifier_id modifierId,
        t.modifier_name modifierName,
        t.modify_date_time modifyDateTime
        FROM t_third_student t
        WHERE
        t.third_party_id = #{thirdPartyId}
        AND third_school_id = #{currentThirdSchoolId}
    </select>

    <!--获取中间库未删除所有学生，通过学校ID -->
    <select id="getStudentBySchoolIdWithDelete" parameterType="map" resultType="map">
        SELECT t.third_student_id     thirdBizId,
               t.deleted,
               t.third_party_id       thirdPartyId,
               t.third_school_id      thirdSchoolId,
               t.student_num          studentNum,
               t.student_name         studentName,
               t.student_phone        studentPhone,
               t.student_phone_aes    studentPhoneAes,
               t.student_no           studentNo,
               t.student_status       studentStatus,
               t.identity_card_no     identityCardNo,
               t.identity_card_no_aes identityCardNoAes,
               t.seat_number          seatNumber,
               t.third_primary_key    thirdPrimaryKey,
               t.creator_id           creatorId,
               t.creator_name         creatorName,
               t.create_date_time     createDateTime,
               t.modifier_id          modifierId,
               t.modifier_name        modifierName,
               t.modify_date_time     modifyDateTime
        FROM t_third_student t
        WHERE t.deleted = 0
          AND t.third_party_id = #{thirdPartyId}
          AND third_school_id = #{currentThirdSchoolId}
    </select>

    <!-- 获取中间库的学校ID，通过第三方的学校ID -->
    <select id="getSchoolIdBySrcSchoolId" parameterType="map" resultType="map">
        SELECT
        third_school_id  thirdSchoolId,
        third_primary_key schoolId
        FROM t_third_school
        WHERE third_party_id = #{thirdPartyId}
        AND third_primary_key IN
        <foreach collection="putianSchoolIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 插入学生信息 -->
    <insert id="insertStudent" parameterType="map" keyProperty="thirdBizId" useGeneratedKeys="true">
        INSERT INTO t_third_student(
        third_student_id,
        third_school_id,
        student_num,
        student_name,
        student_name_pinyin,
        student_phone,
        student_phone_aes,
        student_no,
        student_status,
        identity_card_no,
        identity_card_no_aes,
        seat_number,
        third_party_id,
        third_primary_key,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.thirdBizId},
            #{item.thirdSchoolId},
            #{item.studentNum},
            #{item.studentName},
            #{item.studentNamePinyin},
            #{item.studentPhone},
            #{item.studentPhoneAes},
            #{item.studentNo},
            #{item.studentStatus},
            #{item.identityCardNo},
            #{item.identityCardNoAes},
            #{item.seatNumber},
            #{item.thirdPartyId},
            #{item.thirdPrimaryKey},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createDateTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        third_school_id = VALUES(third_school_id),
        modifier_id = VALUES(modifier_id),
        modifier_name = VALUES(modifier_name),
        modify_date_time = VALUES(modify_date_time)
    </insert>

    <!-- 更新学生 -->
    <update id="updateStudent" parameterType="map">
        UPDATE t_third_student
        SET third_school_id = #{thirdSchoolId},
            student_num = #{studentNum},
            student_name = #{studentName},
            student_name_pinyin = #{studentNamePinyin},
            student_phone = #{studentPhone},
            student_phone_aes = #{studentPhoneAes},
            student_no = #{studentNo},
            student_status = #{studentStatus},
            identity_card_no = #{identityCardNo},
            identity_card_no_aes = #{identityCardNoAes},
            seat_number = #{seatNumber},
            third_party_id = #{thirdPartyId},
            third_primary_key = #{thirdPrimaryKey},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE third_student_id = #{thirdBizId}
    </update>

    <!-- 删除学生 -->
    <delete id="deleteStudent" parameterType="map">
        DELETE FROM t_third_student
        WHERE third_student_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.thirdBizId}
        </foreach>
    </delete>

    <select id="getStudentInStudentIds" resultType="map" parameterType="map">
        SELECT
        third_primary_key studentId,
        third_student_id thirdStudentId
        FROM t_third_student
        WHERE third_party_id = #{thirdPartyId}
        AND deleted = 0
        AND third_primary_key IN
        <foreach collection="studentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getStudentInThirdPrimaryKeyList" resultType="map" parameterType="map">
        SELECT
        third_primary_key thirdPrimaryKey,
        third_student_id  thirdStudentId,
        third_student_id  thirdBizId,
        student_name      studentName
        FROM t_third_student
        WHERE third_party_id = #{thirdPartyId}
        AND deleted = 0
        AND third_primary_key IN
        <foreach collection="thirdPrimaryKeyList" item="thirdPrimaryKey" open="(" separator="," close=")">
            #{thirdPrimaryKey}
        </foreach>
    </select>

    <select id="getStudentWithClassInThirdPrimaryKeyList" resultType="map" parameterType="map">
        SELECT
        tts.third_primary_key thirdPrimaryKey,
        tts.third_student_id  thirdStudentId,
        tts.third_student_id  thirdBizId,
        ttc.class_name        className
        FROM t_third_student tts
        JOIN t_third_class_student ttcs
            ON ttcs.deleted = 0
            AND ttcs.third_student_id = tts.third_student_id
        JOIN t_third_class ttc
            ON ttc.deleted = 0
            AND ttc.third_class_id = ttcs.third_class_id
        WHERE tts.third_party_id = #{thirdPartyId}
        AND tts.deleted = 0
        AND tts.third_primary_key IN
        <foreach collection="thirdPrimaryKeyList" item="thirdPrimaryKey" open="(" separator="," close=")">
            #{thirdPrimaryKey}
        </foreach>
        GROUP BY tts.third_student_id
    </select>

    <select id="getStudentInfoBySchoolStudentNo" parameterType="map" resultType="map">
        SELECT
            third_student_id  thirdStudentId,
            student_no        studentNo
        FROM t_third_student
        WHERE deleted = 0
          AND third_party_id = #{thirdPartyId}
          AND third_school_id = #{thirdSchoolId}
          AND student_no IN
          <foreach collection="studentNoCollection" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
    </select>

    <!--批量获取中间库学生信息-->
    <select id="getThirdStudentListByIds" parameterType="map" resultType="map">
        SELECT
            tts.third_student_id    thirdStudentId,
            tts.third_school_id     thirdSchoolId,
            tts.student_num         studentNum,
            tts.student_name        studentName,
            tts.student_no          studentNo,
            tts.third_primary_key thirdPrimaryKey
        FROM
            t_third_student AS tts
                JOIN t_third_school AS ttsc ON tts.third_school_id = ttsc.third_school_id
        WHERE
            tts.deleted = 0
          AND ttsc.deleted = 0
          AND tts.third_student_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getThirdStudentById" parameterType="map" resultType="map">
        SELECT
            tts.third_student_id    thirdStudentId,
            tts.third_school_id     thirdSchoolId,
            tts.student_num         studentNum,
            tts.student_name        studentName,
            tts.student_no          studentNo,
            tts.third_primary_key   thirdPrimaryKey
        FROM
            t_third_student AS tts
        JOIN t_third_school AS ttsc ON tts.third_school_id = ttsc.third_school_id
        WHERE
            tts.deleted = 0
          AND ttsc.deleted = 0
          AND tts.third_student_id = #{thirdStudentId}
    </select>

    <select id="getByThirdPartyIdAndThirdSchoolId" parameterType="map" resultType="map">
        SELECT
            third_student_id thirdBizId,
            third_primary_key thirdPrimaryKey
        FROM t_third_student
        WHERE third_party_id = #{thirdPartyId} and third_school_id = #{thirdSchoolId} and deleted = 0
    </select>
</mapper>
