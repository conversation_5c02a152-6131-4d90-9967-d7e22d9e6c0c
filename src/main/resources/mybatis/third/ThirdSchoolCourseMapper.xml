<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ThirdSchoolCourseMapper">

    <!--获取有效的数据 适用于validate 从中间库到正式库-->
    <select id="getSchoolCourseEffective" parameterType="map" resultType="map">
        SELECT
            thti.third_task_item_id     thirdTaskItemId,
            thti.table_name             tableName,
            thti.data_status            dataStatus,
            ttsc.third_school_course_id thirdBizId,
            ttsc.third_party_id         thirdPartyId,
            ttsc.third_school_id        thirdSchoolId,
            ttsc.third_course_id        thirdCourseId,
            ttsc.course_name            courseName,
            ttsc.school_course_label_id schoolCourseLabelId,
            ttsc.third_primary_key      thirdPrimaryKey,
            ttsc.creator_id             creatorId,
            ttsc.creator_name           creatorName,
            ttsc.create_date_time       createDateTime,
            ttsc.modifier_id            modifierId,
            ttsc.modifier_name          modifierName,
            ttsc.modify_date_time       modifyDateTime,
            ttc.stage                   stage
        FROM t_third_task_item thti
        INNER JOIN t_third_school_course ttsc ON ttsc.third_school_course_id = thti.third_biz_id AND ttsc.deleted = 0
        INNER JOIN  t_third_course ttc ON ttsc.third_course_id = ttc.third_course_id
        WHERE thti.third_task_id = #{thirdTaskId}
          AND thti.table_name = 't_third_school_course'
    </select>

    <select id="getSchoolCourse" parameterType="map" resultType="map">
        SELECT t.third_school_course_id thirdBizId,
               t.third_party_id thirdPartyId,
               thti.third_task_item_id thirdTaskItemId,
               thti.data_status dataStatus,
               t.third_school_id thirdSchoolId,
               t.third_course_id thirdCourseId,
               t.course_name courseName,
               t.school_course_label_id schoolCourseLabelId,
               t.third_primary_key thirdPrimaryKey,
               t.creator_id creatorId,
               t.creator_name creatorName,
               t.create_date_time createDateTime,
               t.modifier_id modifierId,
               t.modifier_name modifierName,
               t.modify_date_time modifyDateTime
        FROM t_third_school_course t
        INNER JOIN t_third_task_item thti ON thti.third_biz_id = t.third_school_course_id
        WHERE thti.third_task_id = #{thirdTaskId}
        AND thti.table_name = #{tableName}
        <if test="offset != null and pageSize != null">
            ORDER BY t.third_school_course_id
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>

    <select id="getSchoolCourseBySchoolId" parameterType="map" resultType="map">
        SELECT third_school_course_id thirdBizId,
               deleted,
               third_school_id thirdSchoolId,
               third_course_id thirdCourseId,
               course_name courseName,
               school_course_label_id schoolCourseLabelId,
               third_party_id thirdPartyId,
               third_primary_key thirdPrimaryKey,
               creator_id creatorId,
               creator_name creatorName,
               create_date_time createDateTime
        FROM t_third_school_course
        WHERE third_party_id = #{thirdPartyId}
        AND third_school_id = #{currentThirdSchoolId}
    </select>

    <insert id="insertSchoolCourse" parameterType="list" useGeneratedKeys="true" keyColumn="third_school_course_id" keyProperty="thirdBizId">
        INSERT INTO t_third_school_course(
            third_school_course_id,
            third_school_id,
            third_course_id,
            course_name,
            school_course_label_id,
            third_party_id,
            third_primary_key,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.thirdBizId},
            #{item.thirdSchoolId},
            #{item.thirdCourseId},
            #{item.courseName},
            #{item.schoolCourseLabelId},
            #{item.thirdPartyId},
            #{item.thirdPrimaryKey},
            #{item.creatorId},
            #{item.creatorName},
            #{item.createDateTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
    </insert>

    <update id="updateSchoolCourse" parameterType="map">
        UPDATE t_third_school_course
        SET third_school_id = #{thirdSchoolId},
            third_course_id = #{thirdCourseId},
            course_name = #{courseName},
            school_course_label_id = #{schoolCourseLabelId},
            third_party_id = #{thirdPartyId},
            third_primary_key = #{thirdPrimaryKey},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE third_school_course_id = #{thirdBizId}
    </update>

    <delete id="deleteSchoolCourse" parameterType="map">
        DELETE FROM t_third_school_course
        WHERE third_school_course_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item.thirdBizId}
        </foreach>
    </delete>

    <select id="getSchoolCourseInThirdPrimaryKeyList" parameterType="map" resultType="map">
        SELECT
        third_school_course_id  thirdBizId,
        third_school_course_id  thirdSchoolCourseId,
        third_school_id         thirdSchoolId,
        course_name             course_name,
        course_name             courseName,
        third_primary_key       thirdPrimaryKey
        FROM t_third_school_course
        WHERE third_party_id = #{thirdPartyId}
        AND third_primary_key IN
        <foreach collection="thirdPrimaryKeyList" item="thirdPrimaryKey" open="(" separator="," close=")">
            #{thirdPrimaryKey}
        </foreach>
        AND deleted = 0
    </select>
</mapper>