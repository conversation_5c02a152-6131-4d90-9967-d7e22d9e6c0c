<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ThirdMapper">

    <select id="getUnFinishThirdTask" resultType="map" parameterType="map">
        SELECT
            third_task_id thirdTaskId,
            third_task_name thirdTaskName,
            third_party_id thirdPartyId
        FROM t_third_task
        WHERE third_task_status != 2
        limit 1
    </select>

    <select id="getThirdTask" parameterType="map" resultType="map">
        SELECT
            third_task_id thirdTaskId,
            third_party_id thirdPartyId,
            third_task_status thirdTaskStatus,
            sync_params syncParams
        FROM t_third_task
        WHERE third_task_id = #{thirdTaskId}
    </select>

    <insert id="insertThirdTask" parameterType="map" useGeneratedKeys="true" keyColumn="third_task_id" keyProperty="thirdTaskId">
        INSERT INTO t_third_task (
            third_task_name,
            third_task_type,
            third_party_id,
            sync_params,
            third_task_status,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES (
            #{thirdTaskName},
            #{thirdTaskType},
            #{thirdPartyId},
            #{syncParams},
            #{thirdTaskStatus},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
        )
    </insert>

    <!--  根据三方ID查询学校  -->
    <select id="getSchoolInKeys" parameterType="map" resultType="map">
        SELECT
            third_school_id thirdSchoolId,
            third_primary_key thirdPrimaryKey
        FROM t_third_school
        WHERE third_party_id = #{thirdPartyId}
        AND third_primary_key IN
        <foreach collection="thirdPrimaryKeys" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateTaskItem" parameterType="map">
        UPDATE t_third_task_item
        SET data_status = #{dataStatus},
            err_msg = #{errMsg},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE third_task_item_id IN
        <foreach collection="body" item="item" open="(" close=")" separator=",">
            #{item.thirdTaskItemId}
        </foreach>
    </update>

    <!-- 插入更新 -->
    <insert id="insertTaskItem2Update" parameterType="map">
        INSERT INTO t_third_task_item(
        third_task_item_id,
        third_task_id,
        table_name,
        table_name_alias,
        third_biz_id,
        data_status,
        err_msg,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="body" item="item" separator=",">
            (
              #{item.thirdTaskItemId},
              #{thirdTaskId},
              'unknown','unknown',1,
              #{dataStatus},
              #{item.errMsg},
            #{userId},#{userName},#{currentTime},
            #{userId},#{userName},#{currentTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        data_status=VALUES(data_status),
        err_msg=VALUES(err_msg),
        modifier_id=VALUES(modifier_id),
        modifier_name=VALUES(modifier_name),
        modify_date_time=VALUES(modify_date_time)
    </insert>

    <!--  更新第三方同步任务状态  -->
    <update id="updateThirdTask" parameterType="map">
        UPDATE t_third_task
        SET third_task_status = #{thirdTaskStatus},
            modifier_id = #{userId},
            modifier_name = #{userId},
            modify_date_time = #{currentTime}
        WHERE third_task_id = #{thirdTaskId}
    </update>

    <!--  查询任务正常的数据  新增,修改数据,不变数据 -->
    <select id="getThirdTaskItem" parameterType="map" resultType="map">
        SELECT
            third_task_id    thirdTaskId,
            `table_name`     tableName,
            third_biz_id     thirdBizId
        FROM t_third_task_item
        WHERE third_task_id = #{thirdTaskId} and data_status IN (1,2,4)
    </select>

</mapper>