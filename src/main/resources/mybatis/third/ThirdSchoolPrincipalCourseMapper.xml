<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ThirdSchoolPrincipalCourseMapper">

    <select id="getSchoolPrincipalCourseBySchoolId" parameterType="map" resultType="map">
        SELECT
            t.third_school_principal_course_id		thirdBizId,
            t.third_school_principal_id				thirdSchoolPrincipalId,
            t.third_course_id						thirdCourseId,
            t.stage									stage,
            t.third_primary_key 					thirdPrimaryKey,
            t.deleted                               deleted,
            t.creator_id 							creatorId,
            t.creator_name 							creatorName,
            t.create_date_time 						createDateTime,
            t.modifier_id 							modifierId,
            t.modifier_name 						modifierName,
            t.modify_date_time 						modifyDateTime
        FROM
            t_third_school_principal_course AS t
        INNER JOIN t_third_school_principal AS ttsp ON t.third_school_principal_id = ttsp.third_school_principal_id
        WHERE
            t.third_party_id = #{thirdPartyId}
          AND third_school_id = #{currentThirdSchoolId}
    </select>

    <select id="getSchoolPrincipalCourseEffective" parameterType="map" resultType="map">
        SELECT
            thti.third_task_item_id                 thirdTaskItemId,
            thti.table_name                         tableName,
            thti.data_status                        dataStatus,
            ttspc.third_school_principal_course_id  thirdBizId,
            ttspc.third_party_id                    thirdPartyId,
            ttspc.creator_id                        creatorId,
            ttspc.creator_name                      creatorName,
            ttspc.create_date_time                  createDateTime,
            ttspc.modifier_id                       modifierId,
            ttspc.modifier_name                     modifierName,
            ttspc.modify_date_time                  modifyDateTime,
            ttspc.third_school_principal_id         thirdSchoolPrincipalId,
            ttspc.third_course_id                   thirdCourseId,
            ttspc.stage                             stage
        FROM
            t_third_task_item AS thti
                INNER JOIN t_third_school_principal_course AS ttspc ON ttspc.third_school_principal_course_id = thti.third_biz_id
                AND ttspc.deleted = 0
        WHERE
            thti.third_task_id = #{thirdTaskId}
          AND thti.table_name = 't_third_school_principal_course'
    </select>

    <insert id="insertSchoolPrincipalCourse" parameterType="map">
        INSERT INTO t_third_school_principal_course(
        third_school_principal_course_id,
        third_school_principal_id,
        third_course_id,
        stage,
        third_party_id,
        third_primary_key,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.thirdBizId},
            #{item.thirdSchoolPrincipalId},
            #{item.thirdCourseId},
            #{item.stage},
            #{item.thirdPartyId},
            #{item.thirdPrimaryKey},
            #{item.creatorId},
            #{item.creatorName},
            #{item.currentTime},
            #{item.userId},
            #{item.userName},
            #{item.currentTime}
            )
        </foreach>
    </insert>
</mapper>