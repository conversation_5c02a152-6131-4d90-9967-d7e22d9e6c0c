package com.dongni.newmark.bean.entity;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2025/9/12
 * @description: 考试班级报告实体类，对应数据库中的 t_class_exam_stat 表
 */
@Getter
@Setter
public class ClassExamStat {

	/**
	 * 考试Id
	 */
	private Long examId;
	/**
	 * 班级Id
	 */
	private Long classId;
	/**
	 * 报告状态：0未公布  1已公布
	 */
	private Integer statStatus;
	/**
	 * 创建人Id
	 */
	private Long creatorId;
	/**
	 * 创建人
	 */
	private String creatorName;
	/**
	 * 创建时间
	 */
	private Date createDateTime;
	/**
	 * 修改人Id
	 */
	private Long modifierId;
	/**
	 * 修改人
	 */
	private String modifierName;
	/**
	 * 修改时间
	 */
	private Date modifyDateTime;

}

