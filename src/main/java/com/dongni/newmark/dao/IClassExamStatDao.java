package com.dongni.newmark.dao;

import com.dongni.newmark.bean.dto.ClassStatStatusDTO;
import com.dongni.newmark.bean.entity.ClassExamStat;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @author: hzw
 * @date: 2025/9/12
 * @description:
 */
public interface IClassExamStatDao {

    void batchInsert(@Param("classExamStats") List<ClassExamStat> classExamStats);

    void updateStatStatus(@Param("examId") long examId,
                      @Param("classIds") List<Long> classIds,
                      @Param("statStatus") int statStatus,
                      @Param("userId") long userId,
                      @Param("userName") String userName);

    List<ClassStatStatusDTO> getClassStatStatus(@Param("examId") long examId, @Param("classIds") List<Long> classIds);

    Long getOnePublishedClassExamStatId(@Param("examId") long examId);
}
