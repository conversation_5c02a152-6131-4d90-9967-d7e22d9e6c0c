package com.dongni.newmark.manager;

import com.dongni.newmark.bean.dto.ClassStatStatusDTO;
import com.dongni.newmark.bean.entity.ClassExamStat;
import com.dongni.newmark.dao.IClassExamStatDao;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: hzw
 * @date: 2025/9/12
 * @description:
 */
@Component
public class ClassExamStatManagerImpl implements IClassExamStatManager{

    @Autowired
    private IClassExamStatDao classExamStatDao;

    @Override
    public void batchInsert(List<ClassExamStat> classExamStats) {
        classExamStatDao.batchInsert(classExamStats);
    }

    @Override
    public void updateStatStatus(long examId, List<Long> classIds, int statStatus, long userId, String userName) {
        classExamStatDao.updateStatStatus(examId, classIds, statStatus, userId, userName);
    }

    @Override
    public List<ClassStatStatusDTO> getClassStatStatus(long examId, List<Long> classIds) {
        return classExamStatDao.getClassStatStatus(examId, classIds);
    }

    @Override
    public Long getOnePublishedClassExamStatId(long examId) {
        return classExamStatDao.getOnePublishedClassExamStatId(examId);
    }
}
