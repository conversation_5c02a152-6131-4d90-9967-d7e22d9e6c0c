package com.dongni.newmark.manager;

import com.dongni.newmark.bean.ExamGradeVO;
import com.dongni.newmark.bean.entity.Exam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24
 * @desc
 */
public interface IExamManager {

    void insert(Exam exam);

    void updateStatus(long examId, int status, long userId, String userName);

    Exam findById(long examId);

    Long findByPaperAndSchAndGrd(long paperId, long schId, long grdId);

    List<Exam> findBySchAndGrdAndCourse(long schoolId, long grdId, long courseId);

    List<ExamGradeVO> findByIds(List<Long> examIds);
}
