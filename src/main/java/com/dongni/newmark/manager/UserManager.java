package com.dongni.newmark.manager;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.exam.common.mark.vo.SysUserVO;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Component
public class UserManager {

    @Autowired
    private BaseDataRepository baseDataRepository;

    public SysUserVO findByUserId(long userId) {
        return baseDataRepository.selectOne("UserMapper.findByUserId", MapUtil.of("userId", userId));
    }


    public List<SysUserVO> findByUserIds(Collection<Long> userIds) {
        return baseDataRepository.selectList("UserMapper.findByUserIds", MapUtil.of("userIds", userIds));
    }

    public SysUserVO findByRelativeId(long relativeId, int userType) {
        return baseDataRepository.selectOne("UserMapper.findByRelativeId", MapUtil.of("relativeId", relativeId, "userType", userType));
    }

    public List<SysUserVO> findByRelativeIds(Collection<Long> relativeIds, int userType) {
        if (CollectionUtils.isEmpty(relativeIds)) {
            return Collections.emptyList();
        }
        return baseDataRepository.selectList("UserMapper.findByRelativeIds", MapUtil.of("relativeIds", relativeIds, "userType", userType));
    }
}
