package com.dongni.newmark.manager;

import com.dongni.newmark.bean.dto.ClassStatStatusDTO;
import com.dongni.newmark.bean.entity.ClassExamStat;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @desc
 */
public interface IClassExamStatManager {

    void batchInsert(List<ClassExamStat> classExamStats);

    void updateStatStatus(long examId, List<Long> classIds, int statStatus, long userId, String userName);

    List<ClassStatStatusDTO> getClassStatStatus(long examId, List<Long> classIds);

    Long getOnePublishedClassExamStatId(long examId);
}
