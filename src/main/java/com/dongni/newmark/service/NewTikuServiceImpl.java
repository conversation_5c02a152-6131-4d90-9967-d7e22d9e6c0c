package com.dongni.newmark.service;

import com.dongni.exam.common.mark.serivice.exam.ITikuService;
import com.dongni.exam.common.mark.vo.QuestionUnitVO;
import com.dongni.tiku.common.service.TikuCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class NewTikuServiceImpl implements ITikuService {
	@Autowired
	private TikuCommonService tikuCommonService;

	@Override
	public List<QuestionUnitVO> getUnitTypeList(Collection<Long> questionTypes) {
		List<Map<String, Object>> unitTypeByQuestionTypes = tikuCommonService.getUnitTypeByQuestionTypes(questionTypes);
		return unitTypeByQuestionTypes.stream().map(x -> {
			QuestionUnitVO questionUnitVO = new QuestionUnitVO();
			questionUnitVO.setQuestionType(Long.parseLong(x.get("questionType").toString()));
			questionUnitVO.setUnitType(Integer.parseInt(x.get("unitType").toString()));
			return questionUnitVO;
		}).collect(Collectors.toList());
	}
}
