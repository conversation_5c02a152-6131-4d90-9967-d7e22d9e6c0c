package com.dongni.newmark.service;

import com.dongni.analysis.stat.service.ExamStatQueueService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.constant.RequestUtil;
import com.dongni.exam.common.mark.enums.ExamPaperStatus;
import com.dongni.exam.common.mark.serivice.exam.IExamPaperClientService;
import com.dongni.exam.common.mark.serivice.exam.IExamService;
import com.dongni.exam.common.mark.vo.*;
import com.dongni.exam.health.check.ExamStatus;
import com.dongni.exam.maintain.service.ExamCompleteAgainService;
import com.dongni.exam.mark.constant.CommonConstant;
import com.dongni.exam.mark.constant.MarkRedisKey;
import com.dongni.exam.mark.service.handle.ExamMarkCorrectModeHandle;
import com.dongni.exam.mark.util.ExamCheckUtil;
import com.dongni.exam.plan.service.ExamInitService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.newmark.manager.ExamClassManager;
import com.dongni.newmark.manager.ExamCourseManager;
import com.dongni.newmark.manager.ExamManager;
import com.dongni.newmark.manager.ExamSchoolManager;
import com.dongni.newmark.manager.IClassExamStatManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class NewExamServiceImpl implements IExamService {

    private static final Logger log = LoggerFactory.getLogger(NewExamServiceImpl.class);

    @Autowired
    private ExamManager examManager;

    @Autowired
    private ExamSchoolManager examSchoolManager;

    @Autowired
    private ExamCourseManager examCourseManager;

    @Autowired
    private ExamClassManager examClassManager;

    @Autowired
    private ExamRepository commonRepository;

    @Autowired
    private IExamPaperClientService examPaperService;

    @Autowired
    private ExamStatQueueService examStatQueueService;
    @Autowired
    private ExamCompleteAgainService examCompleteAgainService;

    @Autowired
    private ExamInitService examInitService;
    @Autowired
    private ExamService examService;
    @Autowired
    private IClassExamStatManager classExamStatManager;

    @Autowired
    private ExamMarkCorrectModeHandle examMarkCorrectModeHandle;

    @Override
    public ExamVO getExamDetail(long examId) {
        return examManager.findByExamId(examId);
    }

    @Override
    public ExamVO getExamDetailFromMysql(long examId) {
        return examManager.findByExamIdFromMysql(examId);
    }

    @Override
    public void updateExamCorrectMode(long examId, int correctMode, long userId, String userName) {
        examMarkCorrectModeHandle.refreshExamAnswerCardUploader(examId, correctMode, userId, userName);
        examManager.updateCorrectMode(examId, correctMode, userId, userName);
    }

    @Override
    public long countExamStatCourse(long examId) {
        List<Map<String, Object>> examStatCourse = commonRepository.selectList("ExamUnionNewMapper.getStatCourse", examId);
        return examStatCourse.size();
    }

    @Override
    public long countExamStatPaper(long examId) {
        List<Map<String, Object>> examStatPaper = commonRepository.selectList("ExamUnionNewMapper.getStatPaper", examId);

        return examStatPaper.size();
    }

    @Override
    public List<ExamSchoolVO> listExamSchools(long examId) {
        return examSchoolManager.findSchoolsByExamId(examId);
    }

    @Override
    public List<ExamClassVO> listExamClasses(long examId) {
        return examClassManager.findByExamId(examId);
    }

    @Override
    public List<ExamCourseVO> listCourses(long examId) {
        return examCourseManager.findByExamId(examId);
    }

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void finishExam(long examId, long userId, String userName) {
        // 更新状态
        examClassManager.updateStatus(examId, null, null, ExamPaperStatus.Finished.getStatus(), userId, userName);
        ExamVO examDetail = getExamDetail(examId);
        // 判断是否是自动公布，是 更新考试状态为已公布，否则为未公布
        boolean needAutoPublish = examDetail.needAutoPublish();
        if (!needAutoPublish && examService.isStudyGuideHomework(examId)) {
            // 设置的是非自动公布并且是教辅作业，如果此时有部分班级已经是公布状态了，那么还是要将考试的状态更新成已公布
            needAutoPublish = classExamStatManager.getOnePublishedClassExamStatId(examId) != null;
        }
        int status = needAutoPublish ? ExamStatus.Published.getStatus() : ExamStatus.ToPublish.getStatus();
        examSchoolManager.updateSchoolStatus(examId, null, status, userId, userName);

        examManager.updateExamStatus(examId, status, userId, userName);

        int statStatus = DictUtil.getDictValue("statStatus", needAutoPublish ? "statOpen":"statClose");
        Map<String, Object> params = new HashMap<>();
        params.put("examId", examId);
        params.put("userId", userId);
        params.put("userName", userName);
        params.put("currentTime", new Date());
        params.put("statStatus", statStatus);

        // 更新学校报告状态
        commonRepository.update("ExamSchoolMapper.updateExamSchoolStatStatus",params);

        // 刷新考试报告
        log.info("添加统计任务开始:{}", params);
        examStatQueueService.computeAll(params);
        log.info("添加统计任务结束:{}", params);

        // 删除考试相关待办
        examInitService.deleteExamTodoAfterExamComplete(examId);
    }

    @Override
    public void finishCourse(long examId, long courseId, long userId, String userName) {
        examCourseManager.updateCourseStatus(examId, courseId, ExamPaperStatus.Finished.getStatus(), userId, userName);
    }

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void startExam(long examId, long userId, String userName, int status) {
        List<Long> paperIds = examPaperService.listPapers(examId).stream()
                .filter(x -> ExamPaperStatus.isNotFinished(x.getExamPaperStatus()))
                .map(ExamPaperInfoVO::getPaperId).collect(Collectors.toList());
        List<Long> classIds = examPaperService.listClasses(examId, paperIds).stream()
                .filter(x -> ExamPaperStatus.isNotFinished(x.getClassStatus())
                        && ExamPaperStatus.isAfterStructureSplit(x.getClassStatus()))
                .map(PaperClassVO::getClassId).collect(Collectors.toList());
        List<Long> schoolIds = examPaperService.listSchoolCourse(examId, paperIds).stream()
                .filter(x -> ExamPaperStatus.isNotFinished(x.getSchoolPaperStatus()))
                .map(SchoolCourseVO::getSchoolId).collect(Collectors.toList());
        examManager.updateExamStatus(examId, 1, userId, userName);
        examSchoolManager.updateSchoolStatus(examId, schoolIds, 1, userId, userName);
        examClassManager.updateStatus(examId, null, classIds, status, userId, userName);
    }

    @Override
    public void updateExamClassStatus(long examId, List<Long> classIds, int classStatus, long userId, String userName) {
        examClassManager.updateStatus(examId, null, classIds, classStatus, userId, userName);

        // 刷新班级报告
        if (examClassManager.findByExamId(examId).stream().anyMatch(x-> ExamCheckUtil.notFinishMark(x.getClassStatus()))) {
            Map<String, Object> params = new HashMap<>();
            params.put("examId", examId);
            params.put("userId", userId);
            params.put("userName", userName);
            log.info("添加统计任务开始:{}", params);
            examCompleteAgainService.invokeExamCompleteAgain(params);
            log.info("添加统计任务结束:{}", params);
        }
    }

    @Override
    public Map<Long, List<Long>> findMarkByClassExam(List<Long> examIds) {
        return examManager.findMarkByClassExam(examIds);
    }

    @Override
    public List<Long> findSchoolExamByDate(long schoolId, long gradeId, LocalDate startTime, LocalDate endTime) {
        return JedisTemplate.execute(jedis -> {
            String key = MarkRedisKey.getSchoolGradeExamsKey(schoolId, gradeId, startTime, endTime);
            String ids = jedis.get(key);
            if (ids != null) {
                return StringUtils.isBlank(ids) ?
                        Collections.emptyList() :
                        Arrays.stream(ids.split(CommonConstant.COMMA)).map(Long::parseLong).collect(Collectors.toList());
            }
            List<Long> examIds = examManager.findSchoolExamByDate(schoolId, gradeId, startTime, endTime);
            jedis.setex(key, CommonConstant.EXPIRE_TIME_SEC_10, examIds.stream().map(Object::toString).collect(Collectors.joining(CommonConstant.COMMA)));
            return examIds;
        });
    }

    @Override
    public void updateExamStatus(long examId, int examStatus) {
        RequestVO user = RequestUtil.getUser();
        examManager.updateExamStatus(examId, examStatus,
                user.getUserId(), user.getUserName());
    }

    @Override
    public void updateExamSchoolStatus(long examId, int examStatus) {
        RequestVO user = RequestUtil.getUser();
        examManager.updateExamSchoolStatus(examId, examStatus,
                user.getUserId(), user.getUserName());
    }

    @Override
    public void updateExamCourseStatus(long examId, long courseId, int courseStatus) {
        RequestVO user = RequestUtil.getUser();
        examCourseManager.updateCourseStatus(examId, courseId, courseStatus,
                user.getUserId(), user.getUserName());
    }
}
