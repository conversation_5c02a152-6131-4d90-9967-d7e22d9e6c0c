package com.dongni.newmark.service;

import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.basedata.export.clazz.bean.ClassInfoDTO;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.export.school.service.CommonSchoolService;
import com.dongni.basedata.export.student.bean.StudentCourseSelectionDTO;
import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.basedata.export.teacher.bean.ClassTeacherDTO;
import com.dongni.basedata.export.teacher.service.ExamTeacherService;
import com.dongni.basedata.school.classes.bean.ClassHeaderDto;
import com.dongni.basedata.school.classes.bean.ClassHeaderQuery;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataGrade;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataStudent;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataClassTeacherService;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataGradeService;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataStudentService;
import com.dongni.basedata.school.grade.service.impl.SchoolConfigService;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.Pinyin4jUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.exception.DongniException;
import com.dongni.commons.exception.ErrorCode;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.constant.CommonConstant;
import com.dongni.exam.common.mark.constant.MarkConstant;
import com.dongni.exam.common.mark.constant.RequestUtil;
import com.dongni.exam.common.mark.constant.SysDictConstant;
import com.dongni.exam.common.mark.enums.UnitTypeEnum;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolTeacherService;
import com.dongni.exam.common.mark.serivice.basedata.ITodoTaskService;
import com.dongni.exam.common.mark.serivice.exam.IExamCompleteService;
import com.dongni.exam.common.mark.serivice.exam.IExamPaperClientService;
import com.dongni.exam.common.mark.serivice.exam.IExamService;
import com.dongni.exam.common.mark.serivice.exam.IStudyGuideHWService;
import com.dongni.exam.common.mark.serivice.exam.ISysDictService;
import com.dongni.exam.common.mark.serivice.item.IExamItemService;
import com.dongni.exam.common.mark.serivice.mark.IQnMappingClientService;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.AnswerSetting;
import com.dongni.exam.common.mark.vo.ClassTeacherVO;
import com.dongni.exam.common.mark.vo.ExamPaperInfoVO;
import com.dongni.exam.common.mark.vo.ExamVO;
import com.dongni.exam.common.mark.vo.ExtensionQnInfo;
import com.dongni.exam.common.mark.vo.HomeworkCompleteVO;
import com.dongni.exam.common.mark.vo.InsertExamClsVO;
import com.dongni.exam.common.mark.vo.IntelliAnswer;
import com.dongni.exam.common.mark.vo.ItemDetailVO;
import com.dongni.exam.common.mark.vo.ListItemVO;
import com.dongni.exam.common.mark.vo.MarkTodoTaskVO;
import com.dongni.exam.common.mark.vo.QnMappingVO;
import com.dongni.exam.common.mark.vo.QuestionStructureVO;
import com.dongni.exam.common.mark.vo.RequestVO;
import com.dongni.exam.enumeration.AutoPublishEnum;
import com.dongni.exam.enumeration.EntryTypeEnum;
import com.dongni.exam.enumeration.ExamTypeEnum;
import com.dongni.exam.enumeration.HomeworkTypeEnum;
import com.dongni.exam.health.check.ExamPaperStatus;
import com.dongni.exam.health.check.ExamStatus;
import com.dongni.exam.item.bean.dto.PrItemDTO;
import com.dongni.exam.mark.bean.bo.PrIntelligenceInfo;
import com.dongni.exam.mark.bean.entity.PaperRead;
import com.dongni.exam.mark.bean.entity.PaperReadExtension;
import com.dongni.exam.mark.common.enums.CorrectMode;
import com.dongni.exam.mark.manager.IPaperReadExtensionManager;
import com.dongni.exam.mark.mq.producer.IntelliMQProUtil;
import com.dongni.exam.mark.service.ExamUpdateAnswerService;
import com.dongni.exam.mark.service.IIntelligenceService;
import com.dongni.exam.mark.service.IPaperReadArrangeService;
import com.dongni.exam.mark.task.MarkUtil;
import com.dongni.exam.mark.util.EntityBaseUtil;
import com.dongni.exam.materials.service.IEducationMaterialsService;
import com.dongni.exam.newcard.parse.enumeration.CorrectModeEnum;
import com.dongni.exam.newcard.parse.enumeration.ExamResultStatusEnum;
import com.dongni.newmark.bean.entity.ClassExamStat;
import com.dongni.newmark.bean.entity.Exam;
import com.dongni.newmark.bean.entity.ExamClass;
import com.dongni.newmark.bean.entity.ExamClassPaper;
import com.dongni.newmark.bean.entity.ExamCourse;
import com.dongni.newmark.bean.entity.ExamPaper;
import com.dongni.newmark.bean.entity.ExamResult;
import com.dongni.newmark.bean.entity.ExamSchool;
import com.dongni.newmark.bean.entity.ExamSchoolPaper;
import com.dongni.newmark.bean.entity.ExamStudent;
import com.dongni.newmark.bean.entity.ExamTeacher;
import com.dongni.newmark.manager.IClassExamStatManager;
import com.dongni.newmark.manager.IExamClassManager;
import com.dongni.newmark.manager.IExamClassPaperManager;
import com.dongni.newmark.manager.IExamCourseManager;
import com.dongni.newmark.manager.IExamManager;
import com.dongni.newmark.manager.IExamPaperManager;
import com.dongni.newmark.manager.IExamResultManager;
import com.dongni.newmark.manager.IExamSchoolManager;
import com.dongni.newmark.manager.IExamSchoolPaperManager;
import com.dongni.newmark.manager.IExamStudentManager;
import com.dongni.newmark.manager.IExamTeacherManager;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.own.bean.dto.QuestionInfoForIntelliReadDTO;
import com.dongni.tiku.own.service.OwnPaperIntelliReadService;
import com.dongni.tiku.own.service.OwnPaperService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2025/3/20
 * @desc
 */
@Service
public class StudyGuideHWServiceImpl implements IStudyGuideHWService {

    private static final Logger log = LoggerFactory.getLogger(StudyGuideHWServiceImpl.class);

    @Autowired
    private IExamManager examManager;
    @Autowired
    private IExamPaperManager examPaperManager;
    @Autowired
    private IExamSchoolManager examSchoolManager;
    @Autowired
    private IExamCourseManager examCourseManager;
    @Autowired
    private IExamSchoolPaperManager examSchoolPaperManager;
    @Autowired
    private PaperManager paperManager;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private IBaseDataGradeService baseDataGradeService;
    @Autowired
    private CommonSchoolService commonSchoolService;
    @Autowired
    private IQsClientService qsClientService;
    @Autowired
    private ExamUpdateAnswerService examUpdateAnswerService;
    @Autowired
    private IExamStudentManager examStudentManager;
    @Autowired
    private IExamClassManager examClassManager;
    @Autowired
    private IExamClassPaperManager examClassPaperManager;
    @Autowired
    private IBaseDataStudentService baseDataStudentService;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private CommonStudentService commonStudentService;
    @Autowired
    private IExamResultManager examResultManager;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private ExamTeacherService examTeacherService;
    @Autowired
    private IBaseDataClassTeacherService baseDataClassTeacherService;
    @Autowired
    private ExamConfigService examConfigService;
    @Autowired
    private IExamTeacherManager examTeacherManager;
    @Autowired
    private IExamItemService examItemService;
    @Autowired
    private IExamCompleteService examCompleteService;
    @Autowired
    IExamPaperClientService examPaperClientService;
    @Autowired
    private IPaperReadArrangeService paperReadArrangeService;
    @Autowired
    private IIntelligenceService intelligenceService;
    @Autowired
    private IQnMappingClientService qnMappingClientService;
    @Autowired
    private SchoolConfigService schoolConfigService;
    @Autowired
    private IClassExamStatManager classExamStatManager;

    @Autowired
    private IEducationMaterialsService educationMaterialsService;

    @Autowired
    private MarkUtil markUtil;

    @Autowired
    private IExamService examService;

    @Autowired
    private ITodoTaskService todoTaskService;

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private ISchoolTeacherService schoolTeacherService;

    @Autowired
    private IPaperReadExtensionManager paperReadExtensionManager;

    @Autowired
    private IntelliMQProUtil intelliMQProUtil;

    @Autowired
    private IntelliMQProUtil rgtMQProducerService;

    @Autowired
    private OwnPaperIntelliReadService ownPaperIntelliReadService;

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName = "EXAM", name = "createStudyGuideHW", argValueKeys = {"[0]", "[1]"}, waitTime = 0)
    public long createExamAndReturnId(long paperId, long gradeId, long schoolId) {
        // 生成 根据 examId paperId schoolId gradeId courseId
        Long examId = examManager.findByPaperAndSchAndGrd(paperId, schoolId, gradeId);
        if (examId != null) {
            return examId;
        }
        Document paper = paperManager.getPaperSimple(paperId);
        Long courseId = paper.getLong("courseId");
        Map<String, Object> courseInfo = Optional.ofNullable(commonCourseService.getCourseInfoByCourseId(courseId))
                .orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "课程在基础数据中不存在或已被删除！courseId：" + courseId));
        int artsScience = MapUtils.getInteger(courseInfo, "artsScience");
        String courseName = MapUtils.getString(courseInfo, "courseName");
        RequestVO user = RequestUtil.getUser();
        String paperName = paper.getString("paperName");
        BaseDataGrade gradeInfo = baseDataGradeService.getGradeById(gradeId);
        long userId = user.getUserId();
        String userName = user.getUserName();
        Date now = new Date();

        // 创建考试
        examId = createExamAndReturnId(paperName, gradeInfo, schoolId, now);
        // 创建考试科目
        saveExamCourse(examId, courseId, courseName, artsScience, userId, userName, now);
        // 创建考试试卷
        saveExamPaper(examId, paperId, paperName, courseId, courseName, artsScience, userId, userName, now);
        // 初始化试卷的附加答案到考试中
        examUpdateAnswerService.initExamPaperAdditionalAnswer(MapUtil.of("examId", examId, "userId", userId, "userName", userName),
                Collections.singletonList(paper));
        // 创建考试学校
        saveExamSchool(examId, schoolId, gradeInfo, userId, userName, now);
        // 创建考试试卷学校
        saveSchoolPaper(examId, schoolId, paperId, userId, userName, now);

        // 更新试卷状态为被引用
        ownPaperService.updatePaperAndQuestionStatusToUsed(paperId);

        // 初始化阅卷安排
        List<PaperRead> paperReads = paperReadArrangeService.initPaperReadsForHw(examId, paperId, null, user);

        // 初始化智能批改
        initIntelli(paperId, examId, courseId, paperReads, user);

        return examId;
    }

    private void initIntelli(long paperId, Long examId, Long courseId, List<PaperRead> paperReads, RequestVO user) {
        if (paperReads.isEmpty()) {
            return;
        }
        // 如果没有智能批改权限，则返回
        Map<Integer, Integer> unitType2IntelliType = intelligenceService.getUnitType2IntelliType(examId, courseId);
        if (unitType2IntelliType.isEmpty()) {
            return;
        }

        List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(paperId);
        Map<Integer, List<Integer>> markQn2PaperQns = qnMappingList.stream()
                .collect(groupingBy(QnMappingVO::getMarkQn, mapping(QnMappingVO::getPaperQn, toList())));

        List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(paperId);
        // 获取题型
        Map<Integer, Integer> qn2UnitType = questionStructureVOS.stream()
                .collect(toMap(QuestionStructureVO::getQuestionNumber, QuestionStructureVO::getUnitType));
        // 获取有智能批改的题号
        Map<Integer, List<Integer>> intelliMarkQn2PaperQns = questionStructureVOS.stream()
                .filter(x -> unitType2IntelliType.containsKey(x.getUnitType()) && markQn2PaperQns.containsKey(x.getQuestionNumber()))
                .collect(toMap(QuestionStructureVO::getQuestionNumber, x -> markQn2PaperQns.get(x.getQuestionNumber())));
        if (intelliMarkQn2PaperQns.isEmpty()) {
            return;
        }

        // 获取有答案的题目信息
        List<Integer> qns = intelliMarkQn2PaperQns.values().stream().flatMap(Collection::stream).collect(toList());
        Map<Integer, QuestionInfoForIntelliReadDTO> paperQn2QuestionInfo = ownPaperIntelliReadService.getQuestionInfoForIntelliRead(paperId, qns);
        if (paperQn2QuestionInfo.isEmpty()) {
            return;
        }

        List<PaperReadExtension> paperReadExtensionList = paperReads.stream()
                // 筛选出有智能批改权限且有答案的题目
                .filter(paperRead -> {
                    if (!intelliMarkQn2PaperQns.containsKey(paperRead.getQuestionNumber())) {
                        return false;
                    }
                    List<Integer> paperQns = intelliMarkQn2PaperQns.get(paperRead.getQuestionNumber());
                    return paperQns.stream().anyMatch(paperQn2QuestionInfo::containsKey);
                }).map(paperRead -> initPaperReadExtension(
                        paperRead, qn2UnitType.get(paperRead.getQuestionNumber()),
                        intelliMarkQn2PaperQns, paperQn2QuestionInfo, user)
                ).filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (paperReadExtensionList.isEmpty()) {
            return;
        }
        paperReadExtensionManager.batchInsert(paperReadExtensionList);
    }

    private PaperReadExtension initPaperReadExtension(PaperRead paperRead, Integer unitType,
                                                      Map<Integer, List<Integer>> intelliMarkQn2PaperQns,
                                                      Map<Integer, QuestionInfoForIntelliReadDTO> paperQn2QuestionInfo,
                                                      RequestVO user) {
        PaperReadExtension paperReadExtension = new PaperReadExtension();
        paperReadExtension.setPaperReadId(paperRead.getPaperReadId());
        paperReadExtension.setIntelligenceStatus(1);
        paperReadExtension.setIntelliType(2);
        paperReadExtension.setIntelliRgtTime(1);
        List<Integer> paperQns = intelliMarkQn2PaperQns.get(paperRead.getQuestionNumber());
        StringBuilder stemUrl = new StringBuilder();
        StringBuilder answerUrl = new StringBuilder();
        StringBuilder answerSb = new StringBuilder();
        StringBuilder stemSb = new StringBuilder();
        for (Integer paperQn : paperQns) {
            QuestionInfoForIntelliReadDTO questionInfo = paperQn2QuestionInfo.get(paperQn);
            if (questionInfo == null) {
                break;
            }
            Integer subQuestionIndex = questionInfo.getSubQuestionIndex();
            // 当前questionNumber是否为某个题的多个小问中的某个小问
            if (subQuestionIndex != null && subQuestionIndex >= 0) {
                String answer = getAnswer(unitType, questionInfo);
                if (!setAnswer(answer, answerSb)) {
                    break;
                }
            } else {
                // 使用题干图片和答案图片
                if (!setStemAndAnswer(questionInfo, unitType, stemSb, stemUrl, answerSb, answerUrl)) {
                    break;
                }
            }
        }

        if (answerUrl.length() == 0 && answerSb.length() == 0) {
            log.info("答案不存在，paperReadId：{}", paperRead.getPaperReadId());
            return null;
        }

        if (answerSb.length() > 0 || answerUrl.length() > 0) {
            AnswerSetting answerSetting = new AnswerSetting();
            answerSetting.setAnswer(answerSb.toString());
            answerSetting.setUrl(answerUrl.toString());
            IntelliAnswer intelliAnswer = new IntelliAnswer();
            intelliAnswer.setAnswers(Collections.singletonList(answerSetting));
            paperReadExtension.setAnswer(JSONUtil.toJson(intelliAnswer));
        }
        if (stemSb.length() > 0 || stemUrl.length() > 0) {
            ExtensionQnInfo extensionQnInfo = new ExtensionQnInfo();
            extensionQnInfo.setQnDesc(stemSb.toString());
            extensionQnInfo.setQnUrl(stemUrl.toString());
            paperReadExtension.setQuestionInfo(JSONUtil.toJson(extensionQnInfo));
        }
        EntityBaseUtil.setPartBaseInfo(paperReadExtension, user, new Date());
        return paperReadExtension;
    }

    private static String getAnswer(Integer unitType, QuestionInfoForIntelliReadDTO questionInfo) {
        String answer = questionInfo.getAnswer();
        if (StringUtils.isBlank(answer)) {
            return "";
        }
        String explain = questionInfo.getExplain() == null ? "" : questionInfo.getExplain();
        if (UnitTypeEnum.isWordQn(unitType)) {
            return answer.contains("见解析") ? answer + CommonConstant.SEMICOLON + explain : answer;
        }
        return answer + CommonConstant.SEMICOLON + explain;
    }

    /**
     * 优先使用图片，图片不存在则使用文本
     */
    private boolean setStemAndAnswer(QuestionInfoForIntelliReadDTO questionInfo, Integer unitType,
                                  StringBuilder stemSb, StringBuilder stemUrl,
                                  StringBuilder answerSb, StringBuilder answerUrl) {
        try {
            String questionPng = questionInfo.getQuestionPng() == null ? "" : questionInfo.getQuestionPng();
            if (StringUtils.isNotBlank(questionPng) && FileStorageTemplate.exists(questionPng)) {
                stemUrl.append(questionPng).append(CommonConstant.SEMICOLON);
            } else {
                String stem = questionInfo.getStem() == null ? "" : questionInfo.getStem();
                stemSb.append(stem);
            }
            String explainPng = questionInfo.getExplainPng() == null ? "" : questionInfo.getExplainPng();
            if (StringUtils.isNotBlank(explainPng) && FileStorageTemplate.exists(explainPng)) {
                answerUrl.append(explainPng).append(CommonConstant.SEMICOLON);
            } else {
                String answer = getAnswer(unitType, questionInfo);
                if (!setAnswer(answer, answerSb)) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("parse answer url error", e);
            return false;
        }
        return true;
    }

    /**
     * 优先使用答案文本，如果文本存在图片，认为答案为空
     */
    private boolean setAnswer(String answer, StringBuilder answerSb) {
        String answerText = extractTextWithoutImages(answer);
        if (StringUtils.isBlank(answerText)){
            return false;
        }
        answerSb.append(answerText);
        return true;
    }

    /**
     * 去除cdn前缀
     */
    private String splitCdnURL(String url) {
        String cdnUrl = FileStorageTemplate.getCdnUrl() + "/";
        if (url.startsWith(cdnUrl)) {
            return url.replace(cdnUrl, "");
        }
        cdnUrl = cdnUrl.replace("https:", "").replace("http:", "");
        url = url.replace("https:", "").replace("http:", "");
        return url.replace(cdnUrl, "");
    }

    /**
     * 从 HTML 字符串中提取纯文本。
     * <p>
     * 如果 HTML 字符串中包含 img 标签，则直接返回空字符串；
     * 如果字符串为空或解析失败，也返回空字符串；
     * 否则返回提取出的纯文本。
     * </p>
     *
     * @param html HTML 格式字符串
     * @return 提取的纯文本（不含图片），若条件不满足则返回空字符串
     */
    public String extractTextWithoutImages(String html) {
        if (html == null || html.trim().isEmpty()) {
            return "";
        }

        // 将字符串解析为 HTML 文档
        org.jsoup.nodes.Document doc = Jsoup.parse(html);

        // 使用选择器查找所有的 <img> 标签
        Elements images = doc.select("img");

        // 如果存在<img>标签，返回空字符串
        if (!images.isEmpty()){
            return "";
        }

        // 获取纯文本，Jsoup会自动处理标签，并用空格分隔内容
        return doc.text();
    }

    private String preprocessHtml(String html) {
        return html.replace("\\\"", "\"")     // 处理转义的双引号
                .replace("\\'", "'")       // 处理转义的单引号
                .replace("\\\\", "\\");    // 处理转义的反斜杠
    }

    /**
     * 使用Jsoup解析字符串中img标签的图片路径
     *
     * @param answer 包含img标签的字符串
     * @return 图片路径列表，如果不存在则返回空列表
     */
    private List<String> tryParseAnswerUrl(String answer) {
        List<String> imageUrls = new ArrayList<>();

        // 如果输入字符串为空或null，直接返回空列表
        if (answer == null || answer.trim().isEmpty()) {
            return imageUrls;
        }

        try {
            String preprocessHtml = preprocessHtml(answer);
            // 使用Jsoup解析HTML字符串
            org.jsoup.nodes.Document doc = Jsoup.parse(preprocessHtml);
            // 选择所有有src属性的img标签
            Elements imgElements = doc.select("img[src]");
            // 提取每个img标签的src属性值
            for (Element img : imgElements) {
                String src = img.attr("src").trim();
                // 检查src是否不为空
                if (!src.isEmpty()) {
                    imageUrls.add(src);
                }
            }
        } catch (Exception e) {
            // 如果解析失败，记录错误并返回空列表
            log.error("HTML解析失败:{}", e.getMessage());
        }

        return imageUrls;
    }

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void insertClsAndExecuteAfterUpload(InsertExamClsVO insertExamClsVO) {
        List<Long> studentIds = insertExamClsVO.getStudentIds();
        if (CollectionUtils.isEmpty(studentIds)) {
            return;
        }
        long examId = insertExamClsVO.getExamId();
        Exam exam = examManager.findById(examId);
        if (exam.getExamType() != ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode()) {
            throw new DongniException(ErrorCode.USER_EXCEPTION, "非教辅作业不支持此方式添加班级");
        }
        List<ExamSchool> examSchools = examSchoolManager.findByExamId(examId);
        if (examSchools.size() != 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取学校数据出现异常，请联系管理员处理！examId：" + examId);
        }

        List<ExamPaper> examPaperList = examPaperManager.findByExamId(examId);
        if (examPaperList.size() != 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取试卷数据出现异常，请联系管理员处理！examId：" + examId);
        }
        ExamPaper examPaper = examPaperList.get(0);
        long courseId = examPaper.getCourseId();
        // 根据courseId + studentIds从基础数据获取学生所在的班级，优先取教学班
        Map<Long, List<Long>> classId2StudentIds = baseDataStudentService.getClassIdsByStudentIdsAndCourseId(studentIds, courseId);
        if (classId2StudentIds.isEmpty()) {
            // 获取不到班级，可能是基础数据没维护好
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取学生班级信息失败，请联系管理员处理！");
        }

        // 处理学生答题卡第x张提交数据信息.
        educationMaterialsService.handleRecognitionStudents(insertExamClsVO);

        List<Long> classIds = new ArrayList<>(classId2StudentIds.keySet());
        // 班级信息
        Map<Long, ClassInfoDTO> classId2ClassInfo = commonClassService.getClassInfoByIds(classIds).stream()
                .collect(Collectors.toMap(ClassInfoDTO::getClassId, x -> x));
        List<Long> allStudentIds = commonStudentService.getStudentIdsByClassIds(classIds);
        // 调基础数据的接口重新获取所有学生的所属班级
        classId2StudentIds = baseDataStudentService.getClassIdsByStudentIdsAndCourseIdAndClassIds(allStudentIds, courseId, classIds);
        // 学生信息
        Map<Long, BaseDataStudent> stuId2StuInfo = baseDataStudentService.getBaseDataStudents(allStudentIds).stream()
                .collect(Collectors.toMap(BaseDataStudent::getStudentId, x -> x));
        // 学生选科组合信息
        Map<Long, StudentCourseSelectionDTO> stuId2CourseSelection = commonStudentService.getStudentCourseSelectionByStuIds(allStudentIds)
                .stream().collect(Collectors.toMap(StudentCourseSelectionDTO::getStudentId, x -> x));

        List<ExamClass> examClasses = new ArrayList<>(classIds.size());
        List<ExamClassPaper> examClassPapers = new ArrayList<>(classIds.size());
        int insertSize = allStudentIds.size() - studentIds.size();
        List<ExamStudent> insertExamStudents = new ArrayList<>(insertSize);
        List<ExamStudent> scanExamStudents = new ArrayList<>(allStudentIds.size());
        List<ExamResult> insertExamResults = new ArrayList<>(insertSize);
        List<ExamResult> scanExamResults = new ArrayList<>(studentIds.size());
        ExamSchool examSchool = examSchools.get(0);
        long paperId = examPaper.getPaperId();
        RequestVO user = RequestUtil.getUser();
        long userId = user.getUserId();
        String userName = user.getUserName();
        Date now = new Date();
        Set<Long> studentIdSet = new HashSet<>(studentIds);
        classId2StudentIds.forEach((classId, stuIds) -> {
            ClassInfoDTO classInfo = Optional.ofNullable(classId2ClassInfo.get(classId))
                    .orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "班级在基础数据中不存在或已被删除！classId：" + classId));
            examClasses.add(createExamClass(examId, examSchool, classInfo, userId, userName, now));
            examClassPapers.add(createExamClassPaper(examId, paperId, examSchool, classInfo, userId, userName, now));
            stuIds.forEach(studentId -> {
                BaseDataStudent studentInfo = Optional.ofNullable(stuId2StuInfo.get(studentId)).orElseThrow(
                        () -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "学生在基础数据中不存在或已被删除，请联系管理员处理！studentId：" + studentId));
                ExamStudent examStudent = createExamStudent(examId, examSchool, classInfo, studentInfo,
                        stuId2CourseSelection.get(studentId), userId, userName, now);
                ExamResult examResult = createExamResult(examId, courseId, paperId, examSchool, classInfo, studentInfo, userId, userName,
                        now);
                // 识别的方式才需要修改result_status.
                if (studentIdSet.contains(studentId) && insertExamClsVO.getWay() == 0) {
                    scanExamStudents.add(examStudent);
                    examResult.setResultStatus(ExamResultStatusEnum.NORMAL.getCode());
                    scanExamResults.add(examResult);
                } else {
                    insertExamStudents.add(examStudent);
                    insertExamResults.add(examResult);
                }
            });
        });

        List<ExamTeacher> examTeachers = new ArrayList<>(classId2StudentIds.size());
        //任课老师
        List<ClassTeacherDTO> classTeacher = examTeacherService.getCourseTeacher(classIds, Collections.singletonList(courseId));
        examTeachers.addAll(classTeacher.stream().map(x -> createExamTeacher(examId, x, classId2ClassInfo, userId, userName, now))
                .collect(Collectors.toList()));
        //班主任
        ClassHeaderQuery classHeaderQuery = new ClassHeaderQuery();
        classHeaderQuery.setSchoolId(examSchool.getSchoolId());
        classHeaderQuery.setClassIds(classIds);
        List<ClassHeaderDto> classHeader = baseDataClassTeacherService.getClassHeaderList(classHeaderQuery);
        examTeachers.addAll(classHeader.stream().map(x -> createExamTeacher(examId, x, classId2ClassInfo, userId, userName, now))
                .collect(Collectors.toList()));

        // 插入t_exam_class
        examClassManager.batchInsert(examClasses);
        // 插入t_exam_class_paper
        examClassPaperManager.batchInsert(examClassPapers);
        // 插入t_exam_student
        examStudentManager.batchInsert(insertExamStudents);
        // 插入或更新t_exam_student的班级信息
        examStudentManager.batchInsertOrUpdate(scanExamStudents);
        // 插入t_exam_result
        examResultManager.batchInsert(insertExamResults);
        // 插入或更新t_exam_result的班级信息和缺考状态
        examResultManager.batchInsertOrUpdate(scanExamResults);
        //  插入t_exam_teacher
        if (!examTeachers.isEmpty()) {
            examTeacherManager.batchInsert(examTeachers);
        }
        deleteInvalidExamData(examId, courseId, paperId);
        // 插入t_class_exam_stat 教辅作业的班级报告
        classExamStatManager.batchInsert(createClassExamStatList(examClasses, examId, userId, userName, now));

        // 0 表示已经写入item的数据，不为0则不需要判断是否能阅卷完成
        if (insertExamClsVO.getWay() != 0) {
            return;
        }

        // 重置报告配置
        examConfigService.resetExamConfigInfo(MapUtil.of("examId", examId, "userId", userId, "userName", userName));

        // 更新班级状态，并处理阅卷待办
        updateStatusAndExecuteTask(examPaper.getExamPaperId(), classIds, userId, userName);

        // 判断是否有智能批改权限，如果有则发起智能批改
        checkAndStartIntelliRgt(examId, paperId, studentIds);
    }

    /**
     *  更新班级状态，并处理阅卷待办
     * @param examPaperId 考试试卷id
     * @param classIds 班级id列表
     * @param userId 用户id
     * @param userName 用户名
     */
    public void updateStatusAndExecuteTask(long examPaperId, List<Long> classIds, long userId, String userName) {
        ExamPaperInfoVO examPaper = examPaperClientService.getExamPaperInfo(examPaperId);
        long examId = examPaper.getExamId();
        long paperId = examPaper.getPaperId();
        // 校验是否需要更新考试状态到阅卷中或者阅卷完成
        List<Long> notCompleteClassIds = examItemService.getNotCompleteClassIds(examId, paperId, null, null);
        HomeworkCompleteVO homeworkCompleteVO = new HomeworkCompleteVO();
        homeworkCompleteVO.setExamPaperId(examPaperId);
        homeworkCompleteVO.setClsIds(notCompleteClassIds);
        homeworkCompleteVO.setUserId(userId);
        homeworkCompleteVO.setUserName(userName);
        int todoType = getTodoType();
        if (notCompleteClassIds.isEmpty()) {
            homeworkCompleteVO.setClsIds(Collections.emptyList());
            examCompleteService.homeworkComplete(homeworkCompleteVO);
            todoTaskService.deleteMarkTodoTask(examPaperId, 0, Collections.emptyList(), todoType);
            return;
        }
        // 后置处理
        Map<Boolean, List<Long>> isFinish2ClsIds = classIds.stream().collect(Collectors.groupingBy(x -> !notCompleteClassIds.contains(x)));
        List<Long> notFinishedClsIds = isFinish2ClsIds.get(false);
        // 更新阅卷状态到阅卷中 并 刷新报告
        if (notFinishedClsIds != null) {
            examPaperClientService.startHomework(examId, paperId, notFinishedClsIds);
            examCompleteService.hwQnComplete(examId, paperId, notCompleteClassIds);
        }

        // 更新班级状态为已完成
        List<Long> finishedClsIds = isFinish2ClsIds.get(true);
        if (finishedClsIds != null) {
            homeworkCompleteVO.setClsIds(finishedClsIds);
            examCompleteService.homeworkComplete(homeworkCompleteVO);
        }
        // 处理阅卷待办
        addTodoTask(examId, examPaper.getCourseId(), examPaperId, todoType, notFinishedClsIds);
    }

    /**
     * 判断是否有智能批改权限，如果有则发起智能批改
     * @param examId 考试id
     * @param paperId 试卷id
     * @param studentIds 学生id列表
     */
    private void checkAndStartIntelliRgt(long examId, long paperId, List<Long> studentIds) {
        List<PrIntelligenceInfo> prIntelligenceInfos = paperReadExtensionManager.listByExamAndPaper(examId, paperId);
        if (prIntelligenceInfos.isEmpty()) {
            return;
        }

        List<Integer> qns = prIntelligenceInfos.stream().map(PrIntelligenceInfo::getQn).collect(Collectors.toList());
        ListItemVO listItemVO = new ListItemVO();
        listItemVO.setExamId(examId);
        listItemVO.setPaperId(paperId);
        listItemVO.setStudentIds(studentIds);
        listItemVO.setQuestionNumbers(qns);
        List<ItemDetailVO> items = examItemService.getItems(listItemVO);
        if (items.isEmpty()) {
            return;
        }
        Map<Integer, Long> qn2Pr = prIntelligenceInfos.stream()
                .collect(Collectors.toMap(PrIntelligenceInfo::getQn, PrIntelligenceInfo::getPaperReadId));
        Map<Long, List<PrItemDTO>> pr2Items = items.stream().collect(Collectors.groupingBy(x -> qn2Pr.get(x.getQuestionNumber()), Collectors.mapping(x -> {
            PrItemDTO prItemDTO = new PrItemDTO();
            prItemDTO.setPaperReadId(qn2Pr.get(x.getQuestionNumber()));
            prItemDTO.setExamItemId(x.getExamItemId());
            prItemDTO.setUrl(x.getSaveUrl());
            return prItemDTO;
        }, Collectors.toList())));
        pr2Items.forEach((prId, prItems) -> rgtMQProducerService.send(prId, prItems));
    }

    private void addTodoTask(long examId, long courseId, Long examPaperId, int todoType, List<Long> notFinishedClsIds) {
        if (CollectionUtils.isEmpty(notFinishedClsIds)) {
            return;
        }
        List<ClassTeacherVO> classTeacherVOS = schoolTeacherService.listClassTeacher(notFinishedClsIds, Collections.singletonList(courseId));
        if (CollectionUtils.isEmpty(classTeacherVOS)) {
            return;
        }
        // 添加阅卷待办
        Set<Long> addTodoTchs = classTeacherVOS.stream().map(ClassTeacherVO::getTeacherId).collect(Collectors.toSet());

        List<MarkTodoTaskVO> markTodoTaskVOS = new ArrayList<>(addTodoTchs.size());
        ExamVO examDetail = examService.getExamDetail(examId);
        ExamPaperInfoVO examPaperInfo = examPaperClientService.getExamPaperInfo(examPaperId);
        markUtil.addChargeTodoTask(addTodoTchs, examPaperInfo, examDetail, markTodoTaskVOS, CommonConstant.BLANK, 0, null);
        todoTaskService.addMarkTodoTask(markTodoTaskVOS, todoType);
    }

    private Long createExamAndReturnId(String paperName, BaseDataGrade gradeInfo, long schoolId, Date now) {
        Long examId;
        Exam exam = new Exam();
        // 设置考试信息
        exam.setExamName(paperName);
        exam.setExamType(ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode());
        exam.setGradeType(gradeInfo.getGradeType());
        exam.setCorrectMode(CorrectMode.ByCls.getMode());
        exam.setEntryType(EntryTypeEnum.NEW.getValue());
        exam.setStage(gradeInfo.getStage());
        exam.setGradeYear(gradeInfo.getGradeYear());
        exam.setVersion(2.0);
        exam.setExamStatus(ExamStatus.InProgress.getStatus());
        exam.setStartDate(now);
        exam.setEndDate(now);
        exam.setHomeworkType(HomeworkTypeEnum.END_DATE.getValue());
        // 作业默认自动公布
        Document schoolConfig = schoolConfigService.getSchoolConfig(MapUtil.of("schoolId", schoolId));
        exam.setAutoPublish(MapUtils.getInteger(schoolConfig, "studyGuideAutoPublish", AutoPublishEnum.YES.getValue()));
        exam.setCreatorId((long) MarkConstant.SYSTEM_TCH_ID);
        exam.setCreatorName(MarkConstant.SYSTEM_TCH_NAME);
        exam.setCreateDateTime(now);
        exam.setModifierId((long) MarkConstant.SYSTEM_TCH_ID);
        exam.setModifierName(MarkConstant.SYSTEM_TCH_NAME);
        exam.setModifyDateTime(now);
        examManager.insert(exam);
        examId = exam.getExamId();
        return examId;
    }

    private void saveExamCourse(long examId, long courseId, String courseName, int artsScience, long userId, String userName, Date now) {
        ExamCourse examCourse = new ExamCourse();
        examCourse.setExamId(examId);
        examCourse.setCourseId(courseId);
        examCourse.setCourseName(courseName);
        //字典中examCourseStatus只有0（未完成）和1（已完成），但实际上其他地方还会把值改成15、20这种，先还是认为初始状态值为0
        examCourse.setExamCourseStatus(DictUtil.getDictValue("examCourseStatus", "unfinished"));
        examCourse.setArtsScience(artsScience);
        examCourse.setCreatorId(userId);
        examCourse.setCreatorName(userName);
        examCourse.setCreateDateTime(now);
        examCourse.setModifierId(userId);
        examCourse.setModifierName(userName);
        examCourse.setModifyDateTime(now);
        examCourseManager.insert(examCourse);
    }

    private void saveExamPaper(long examId, long paperId, String paperName, long courseId, String courseName, int artsScience, long userId,
                               String userName, Date now) {
        ExamPaper examPaper = new ExamPaper();
        examPaper.setExamId(examId);
        examPaper.setCourseId(courseId);
        examPaper.setCourseName(courseName);
        examPaper.setPaperId(paperId);
        examPaper.setPaperName(paperName);
        examPaper.setFullMark(BigDecimal.valueOf(qsClientService.getFullMark(paperId).getFullMark()));
        examPaper.setArtsScience(artsScience);
        examPaper.setExamPaperStatus(ExamPaperStatus.Marking.getStatus());
        examPaper.setCreatorId(userId);
        examPaper.setCreatorName(userName);
        examPaper.setCreateDateTime(now);
        examPaper.setModifierId(userId);
        examPaper.setModifierName(userName);
        examPaper.setModifyDateTime(now);
        examPaperManager.insert(examPaper);
    }

    private void saveExamSchool(long examId, long schoolId, BaseDataGrade gradeInfo, long userId, String userName, Date now) {
        Map<String, Object> schoolDetail = Optional.ofNullable(commonSchoolService.getSchoolDetail(MapUtil.of("schoolId", schoolId)))
                .orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "学校在基础数据中不存在或已被删除！schoolId：" + schoolId));
        ExamSchool examSchool = new ExamSchool();
        examSchool.setExamId(examId);
        examSchool.setAreaId(MapUtils.getLong(schoolDetail, "areaId"));
        examSchool.setSchoolId(schoolId);
        examSchool.setSchoolName(MapUtils.getString(schoolDetail, "schoolName"));
        examSchool.setGradeId(gradeInfo.getGradeId());
        examSchool.setGradeType(gradeInfo.getGradeType());
        examSchool.setGradeName(gradeInfo.getGradeName());
        examSchool.setCreatorId(userId);
        examSchool.setCreatorName(userName);
        examSchool.setCreateDateTime(now);
        examSchool.setModifierId(userId);
        examSchool.setModifierName(userName);
        examSchool.setModifyDateTime(now);
        examSchoolManager.insert(examSchool);
    }

    private void saveSchoolPaper(long examId, long schoolId, long paperId, long userId, String userName, Date now) {
        ExamSchoolPaper examSchoolPaper = new ExamSchoolPaper();
        examSchoolPaper.setExamId(examId);
        examSchoolPaper.setSchoolId(schoolId);
        examSchoolPaper.setPaperId(paperId);
        examSchoolPaper.setExamSchoolPaperStatus(ExamPaperStatus.Marking.getStatus());
        examSchoolPaper.setCreatorId(userId);
        examSchoolPaper.setCreatorName(userName);
        examSchoolPaper.setCreateDateTime(now);
        examSchoolPaper.setModifierId(userId);
        examSchoolPaper.setModifierName(userName);
        examSchoolPaper.setModifyDateTime(now);
        examSchoolPaperManager.insert(examSchoolPaper);
    }

    private ExamClass createExamClass(long examId, ExamSchool examSchool, ClassInfoDTO classInfo, long userId, String userName, Date now) {
        ExamClass examClass = new ExamClass();
        examClass.setExamId(examId);
        examClass.setSchoolId(examSchool.getSchoolId());
        examClass.setGradeId(examSchool.getGradeId());
        examClass.setClassId(classInfo.getClassId());
        examClass.setClassName(classInfo.getClassName());
        examClass.setClassStatus(ExamPaperStatus.Marking.getStatus());
        examClass.setClassType(classInfo.getClassType());
        examClass.setArtsScience(classInfo.getArtsScience());
        examClass.setCorrectMode(CorrectModeEnum.READ_BY_TEACHER.getValue());
        examClass.setCreatorId(userId);
        examClass.setCreatorName(userName);
        examClass.setCreateDateTime(now);
        examClass.setModifierId(userId);
        examClass.setModifierName(userName);
        examClass.setModifyDateTime(now);
        return examClass;
    }

    private ExamClassPaper createExamClassPaper(long examId, long paperId, ExamSchool examSchool, ClassInfoDTO classInfo, long userId,
                                                String userName, Date now) {
        ExamClassPaper examClassPaper = new ExamClassPaper();
        examClassPaper.setExamId(examId);
        examClassPaper.setPaperId(paperId);
        examClassPaper.setSchoolId(examSchool.getSchoolId());
        examClassPaper.setGradeId(examSchool.getGradeId());
        examClassPaper.setClassId(classInfo.getClassId());
        examClassPaper.setClassName(classInfo.getClassName());
        examClassPaper.setClassType(classInfo.getClassType());
        examClassPaper.setArtsScience(classInfo.getArtsScience());
        examClassPaper.setExamClassPaperStatus(ExamPaperStatus.Marking.getStatus());
        examClassPaper.setCreatorId(userId);
        examClassPaper.setCreatorName(userName);
        examClassPaper.setCreateDateTime(now);
        examClassPaper.setModifierId(userId);
        examClassPaper.setModifierName(userName);
        examClassPaper.setModifyDateTime(now);
        return examClassPaper;
    }

    private List<ClassExamStat> createClassExamStatList(List<ExamClass> examClassList, long examId, long userId, String userName,
      Date now) {
        int statClose = DictUtil.getDictValue("statStatus", "statClose");
        return examClassList.stream().map(x -> {
            ClassExamStat classExamStat = new ClassExamStat();
            classExamStat.setExamId(examId);
            classExamStat.setClassId(x.getClassId());
            classExamStat.setStatStatus(statClose);
            classExamStat.setCreatorId(userId);
            classExamStat.setCreatorName(userName);
            classExamStat.setCreateDateTime(now);
            classExamStat.setModifierId(userId);
            classExamStat.setModifierName(userName);
            classExamStat.setModifyDateTime(now);
            return classExamStat;
        }).collect(toList());
    }

    private ExamStudent createExamStudent(long examId, ExamSchool examSchool, ClassInfoDTO classInfo, BaseDataStudent studentInfo,
                                          StudentCourseSelectionDTO stuCourseSelection, long userId, String userName, Date now) {
        ExamStudent examStudent = new ExamStudent();
        examStudent.setExamId(examId);
        examStudent.setSchoolId(examSchool.getSchoolId());
        examStudent.setClassId(classInfo.getClassId());
        examStudent.setClassName(classInfo.getClassName());
        examStudent.setStudentId(studentInfo.getStudentId());
        examStudent.setStudentName(studentInfo.getStudentName());
        examStudent.setStudentNum(studentInfo.getStudentNum());
        String candidate = studentInfo.getCandidate();
        examStudent.setStudentExamNum(StringUtils.isEmpty(candidate) ? studentInfo.getStudentNum() : candidate);
        if (ObjectUtil.isNotBlank(stuCourseSelection)) {
            examStudent.setCourseSelectionGroupId(stuCourseSelection.getCourseSelectionGroupId());
            examStudent.setForeignCourseId(stuCourseSelection.getForeignCourseId());
        }
        examStudent.setArtsScience(classInfo.getArtsScience());
        examStudent.setCreatorId(userId);
        examStudent.setCreatorName(userName);
        examStudent.setCreateDateTime(now);
        examStudent.setModifierId(userId);
        examStudent.setModifierName(userName);
        examStudent.setModifyDateTime(now);
        return examStudent;
    }

    private ExamResult createExamResult(long examId, long courseId, long paperId, ExamSchool examSchool, ClassInfoDTO classInfo,
                                        BaseDataStudent studentInfo, long userId, String userName, Date now) {
        ExamResult examResult = new ExamResult();
        examResult.setExamId(examId);
        examResult.setCourseId(courseId);
        examResult.setPaperId(paperId);
        examResult.setSchoolId(examSchool.getSchoolId());
        examResult.setClassId(classInfo.getClassId());
        examResult.setClassName(classInfo.getClassName());
        examResult.setStudentId(studentInfo.getStudentId());
        examResult.setStudentNum(studentInfo.getStudentNum());
        examResult.setStudentName(studentInfo.getStudentName());
        String candidate = studentInfo.getCandidate();
        examResult.setStudentExamNum(StringUtils.isEmpty(candidate) ? studentInfo.getStudentNum() : candidate);
        examResult.setStudentNamePinyin(Pinyin4jUtil.chinese2Pinyin(studentInfo.getStudentName()));
        examResult.setResultStatus(ExamResultStatusEnum.ABSENT.getCode());
        examResult.setCreatorId(userId);
        examResult.setCreatorName(userName);
        examResult.setCreateDateTime(now);
        examResult.setModifierId(userId);
        examResult.setModifierName(userName);
        examResult.setModifyDateTime(now);
        return examResult;
    }

    private ExamTeacher createExamTeacher(long examId, ClassTeacherDTO classTeacher, Map<Long, ClassInfoDTO> classId2ClassInfo, long userId,
                                          String userName, Date now) {
        long classId = classTeacher.getClassId();
        ClassInfoDTO classInfo = Optional.ofNullable(classId2ClassInfo.get(classId))
                .orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "班级在基础数据中不存在或已被删除！classId：" + classId));
        ExamTeacher examTeacher = new ExamTeacher();
        examTeacher.setExamId(examId);
        examTeacher.setClassId(classId);
        examTeacher.setClassName(classInfo.getClassName());
        examTeacher.setCourseId(classTeacher.getCourseId());
        examTeacher.setCourseName(classTeacher.getCourseName());
        examTeacher.setTeacherId(classTeacher.getTeacherId());
        examTeacher.setTeacherName(classTeacher.getTeacherName());
        examTeacher.setCreatorId(userId);
        examTeacher.setCreatorName(userName);
        examTeacher.setCreateDateTime(now);
        examTeacher.setModifierId(userId);
        examTeacher.setModifierName(userName);
        examTeacher.setModifyDateTime(now);
        return examTeacher;
    }

    private ExamTeacher createExamTeacher(long examId, ClassHeaderDto classHeader, Map<Long, ClassInfoDTO> classId2ClassInfo, long userId,
                                          String userName, Date now) {
        long classId = classHeader.getClassId();
        ClassInfoDTO classInfo = Optional.ofNullable(classId2ClassInfo.get(classId))
                .orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "班级在基础数据中不存在或已被删除！classId：" + classId));
        ExamTeacher examTeacher = new ExamTeacher();
        examTeacher.setExamId(examId);
        examTeacher.setClassId(classId);
        examTeacher.setClassName(classInfo.getClassName());
        examTeacher.setCourseId(0L);
        examTeacher.setTeacherId(classHeader.getHeaderId());
        examTeacher.setTeacherName(classHeader.getHeaderName());
        examTeacher.setCreatorId(userId);
        examTeacher.setCreatorName(userName);
        examTeacher.setCreateDateTime(now);
        examTeacher.setModifierId(userId);
        examTeacher.setModifierName(userName);
        examTeacher.setModifyDateTime(now);
        return examTeacher;
    }

    private void deleteInvalidExamData(long examId, long courseId, long paperId) {
        examClassManager.deleteInvalidDataByExamStudent(examId);
        examClassPaperManager.deleteInvalidDataByExamResult(examId, paperId);
        examTeacherManager.deleteInvalidClassHeaderByExamClass(examId);
        examTeacherManager.deleteInvalidClassTeacherByExamClassPaper(examId, courseId, paperId);
    }

    private int getTodoType() {
        return sysDictService.getDictValue(SysDictConstant.TODO_TYPE, SysDictConstant.STUDY_GUIDE_CORRECT_TEA);
    }
}
