package com.dongni.newmark.service;

import com.dongni.analysis.view.monitor.service.ExamStatService;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.constant.RequestUtil;
import com.dongni.exam.common.mark.constant.SysDictConstant;
import com.dongni.exam.common.mark.enums.ExamPaperStatus;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolTeacherService;
import com.dongni.exam.common.mark.serivice.basedata.ITodoTaskService;
import com.dongni.exam.common.mark.serivice.exam.*;
import com.dongni.exam.common.mark.serivice.item.IExamItemService;
import com.dongni.exam.common.mark.serivice.mark.IMarkConfigService;
import com.dongni.exam.common.mark.serivice.mark.IPaperReadTaskService;
import com.dongni.exam.common.mark.serivice.mark.ITrialCommonService;
import com.dongni.exam.common.mark.vo.*;
import com.dongni.exam.maintain.service.ExamCompleteAgainService;
import com.dongni.exam.mark.aop.MarkOperateLog;
import com.dongni.exam.mark.manager.IPaperReadTaskManager;
import com.dongni.exam.mark.service.IExamRepeatRecordService;
import com.dongni.exam.mark.util.ExamCheckUtil;
import com.dongni.newmark.manager.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExamCompleteServiceImpl implements IExamCompleteService {

    private static final Logger log = LoggerFactory.getLogger(ExamCompleteServiceImpl.class);

    @Autowired
    private IExamPaperClientService examPaperService;

    @Autowired
    private IExamRepeatRecordService examRepeatRecordService;

    @Autowired
    private IExamService newExamService;

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private ITrialCommonService trialService;

    @Autowired
    private ITodoTaskService todoTaskService;

    @Autowired
    private IExamItemService examItemService;
    @Autowired
    private IExamService examService;

    @Autowired
    private ExamClassPaperManager examClassPaperManager;

    @Autowired
    private ISchoolTeacherService schoolTeacherService;

    @Autowired
    private IExamUploaderService examUploaderService;

    @Autowired
    private ExamCompleteAgainService examCompleteAgainService;

    @Autowired
    private IMarkConfigService markConfigService;

    @Autowired
    private IPaperReadTaskManager paperReadTaskManager;

    @Autowired
    private IPaperReadTaskService paperReadTaskService;

    @Autowired
    private IExamClassPaperManager iExamClassPaperManager;

    @Autowired
    private IExamPaperManager examPaperManager;

    @Autowired
    private IExamSchoolPaperManager examSchoolPaperManager;

    @Autowired
    private IExamCourseManager examCourseManager;

    @Autowired
    private ExamStatService examStatService;

    @Autowired
    private IExamClassManager examClassManager;

    @Override
    @MarkOperateLog(operationMessage = "执行阅卷完成")
    @Transactional(transactionManager = ExamRepository.TRANSACTION)
    public MarkOperateDataVO<Boolean> markComplete(MarkCompleteVO markCompleteVO) {
        long examPaperId = markCompleteVO.getExamPaperId();
        long userId = markCompleteVO.getUserId();
        String userName = markCompleteVO.getUserName();

        // 判断阅卷是否完成
        ExamPaperInfoVO examPaperInfo = examPaperService.getExamPaperInfo(examPaperId);
        long examId = examPaperInfo.getExamId();
        long paperId = examPaperInfo.getPaperId();

        int trialRead = sysDictService.getDictValue(SysDictConstant.EXAM_PAPER_STATUS, SysDictConstant.TRIAL_READ_PAPER);

        long classId = markCompleteVO.getClassId();

        // 是否是班级阅卷
        ExamVO examDetail = examService.getExamDetail(examId);

        // 如果是上传答案触发的，且是按班级阅卷，则不走判断task是否已完成的逻辑
        boolean readByClass = ExamCheckUtil.isReadByClass(examDetail.getCorrectMode());
        boolean readByClsUploadAnswer = !markCompleteVO.isUploadAnswer() || !readByClass;
        MarkOperateDataVO<Boolean> result = new MarkOperateDataVO<>();
        if (readByClsUploadAnswer) {
            boolean existsExamPaperUnFinishedTask = paperReadTaskManager.existsExamPaperUnFinishedTask(examPaperId, classId);
            if (existsExamPaperUnFinishedTask) {
                log.info("examPaperId:{} 的考试科目还有task状态为未阅，禁止阅卷完成", examPaperId);
                result.setData(false);
                result.setMarkOpeRes("还有task状态为未阅");
                return result;
            }
        }
        if (examPaperInfo.getExamPaperStatus() == trialRead) {
            log.info("examPaperId: {}的考试科目开始执行试评完成", examPaperId);
            trialService.closeTrial(examPaperId, userId, userName);
            todoTaskService.deleteMarkTodoTask(examPaperId, markCompleteVO.getClassId(), new ArrayList<>(), 0);
            log.info("examPaperId: {} 的考试科目执行试评完成结束", examPaperId);
            result.setMarkOpeRes("试评");
            result.setData(false);
            return result;
        }
        if (readByClsUploadAnswer) {
            boolean complete = examItemService.markComplete(examId, paperId, classId);
            if (!complete) {
                log.info("examPaperId: {} 的考试科目还有item状态为未阅，禁止阅卷完成", examPaperId);
                result.setMarkOpeRes("还有item状态为未阅");
                result.setData(false);
                return result;
            }
        }

        if (readByClass) {
            List<String> completeClassMsgs = completeClass(examPaperInfo, classId, userId, userName);
            result.setData(completeClassMsgs.isEmpty());
            result.setMarkOpeRes("按班级" + completeClassMsgs);
            return result;
        }

        // 如果有上传中的任务，则不允许阅卷完成
        if (examUploaderService.existNotFinishedUploader(examId, paperId, 0)) {
            result.setMarkOpeRes("有上传中的任务");
            result.setData(false);
            return result;
        }

        examRepeatRecordService.finishRepeatMark(examPaperId, Collections.emptyList());
        // 修改考试试卷状态
        String msg = examPaperService.finishedMark(examPaperId, classId, userId, userName);
        if (StringUtils.isNotBlank(msg)) {
            result.setMarkOpeRes(msg);
            result.setData(false);
            return result;
        }

        // 修改科目状态
        completeExamCourse(examId, examPaperInfo.getCourseId(), userId, userName);

        // 修改考试状态
        completeExam(examId, userId, userName);
        todoTaskService.deleteMarkTodoTask(examPaperId, classId, new ArrayList<>(), 0);
        // 删除阅卷安排待办
        todoTaskService.deleteArrangeTodoTask(examId);
        result.setData(true);
        return result;
    }

    @Override
    @MarkOperateLog(operationMessage = "执行阅卷完成")
    public void homeworkComplete(HomeworkCompleteVO homeworkCompleteVO) {
        long examPaperId = homeworkCompleteVO.getExamPaperId();
        long userId = homeworkCompleteVO.getUserId() < 0 ? 1 : homeworkCompleteVO.getUserId();
        String userName = homeworkCompleteVO.getUserName();
        List<Long> clsIds = homeworkCompleteVO.getClsIds();
        ExamPaperInfoVO examPaperInfo = examPaperService.getExamPaperInfo(examPaperId);
        long examId = examPaperInfo.getExamId();
        int todoType = sysDictService.getDictValue(SysDictConstant.TODO_TYPE, SysDictConstant.STUDY_GUIDE_CORRECT_TEA);
        if (!clsIds.isEmpty()) {
            iExamClassPaperManager.updateStatus(examId, clsIds, ExamPaperStatus.Finished.getStatus(), userId, userName);
            examService.updateExamClassStatus(examId, clsIds, ExamPaperStatus.Finished.getStatus(), userId, userName);
            // 删除已阅完的老师的待办
            delTodoTask(examPaperId, examPaperInfo, examId, todoType);
            // 公布报告
            publishClassExamStat(examId, clsIds, userId, userName);
            return;
        }

        iExamClassPaperManager.updateStatus(examId, null, ExamPaperStatus.Finished.getStatus(), userId, userName);
        examSchoolPaperManager.updateStatus(examId, ExamPaperStatus.Finished.getStatus(), userId, userName);
        examPaperManager.updateStatus(examPaperId, ExamPaperStatus.Finished.getStatus(), userId, userName);
        examCourseManager.updateStatus(examId, ExamPaperStatus.Finished.getStatus(), userId, userName);
        // 公布班级报告
        publishClassExamStat(examId, null, userId, userName);
        examService.finishExam(examId, userId, userName);
        // 删除所有老师的阅卷待办
        todoTaskService.deleteMarkTodoTask(examPaperId, 0, Collections.emptyList(), todoType);
    }

    /**
     * 公布班级报告
     * @param examId 考试id
     * @param classIds 班级id，为空则公布所有班级报告
     * @param userId 用户id
     * @param userName 用户名
     */
    private void publishClassExamStat(Long examId, List<Long> classIds, Long userId, String userName) {
        if (CollectionUtils.isEmpty(classIds)){
            classIds = examClassManager.findClassIds(Collections.singletonList(examId));
        }
        examStatService.autoPublishClassExamStat(examId, classIds, userId, userName);
    }

    private void delTodoTask(long examPaperId, ExamPaperInfoVO examPaperInfo, long examId, int todoType) {
        List<PaperClassVO> allClsList = examClassPaperManager.listExamPaperClass(examId, Collections.singletonList(examPaperInfo.getPaperId()));
        Set<Long> unfinishedClsIds = allClsList
                .stream().filter(x -> ExamCheckUtil.whileFormalMark(x.getClassStatus())).map(PaperClassVO::getClassId).collect(Collectors.toSet());
        List<Long> allClsIds = allClsList.stream().map(PaperClassVO::getClassId).collect(Collectors.toList());
        List<ClassTeacherVO> allTchs = schoolTeacherService.listClassTeacher(allClsIds, Collections.singletonList(examPaperInfo.getCourseId()));
        Set<Long> markingTchs = allTchs.stream().filter(x -> unfinishedClsIds.contains(x.getClassId())).map(ClassTeacherVO::getTeacherId).collect(Collectors.toSet());
        List<Long> delTodoTchs = allTchs.stream().map(ClassTeacherVO::getTeacherId).distinct().filter(x -> !markingTchs.contains(x)).collect(Collectors.toList());
        if (delTodoTchs.isEmpty()) {
            return;
        }
        todoTaskService.deleteMarkTodoTask(examPaperId, 0, delTodoTchs, todoType);
    }

    @Override
    public void qnComplete(MarkCompleteVO markCompleteVO) {
        ExamPaperInfoVO examPaperInfo = examPaperService.getExamPaperInfo(markCompleteVO.getExamPaperId());
        Map<String, Object> params = new HashMap<>();
        params.put("examId", examPaperInfo.getExamId());
        params.put("paperId", examPaperInfo.getPaperId());
        params.put("userId", markCompleteVO.getUserId());
        params.put("userName", markCompleteVO.getUserName());
        log.info("添加按试题统计任务开始:{}", params);
        ExamVO examDetail = examService.getExamDetail(examPaperInfo.getExamId());
        if (ExamCheckUtil.isReadByClass(examDetail.getCorrectMode())) {
            params.put("classId", markCompleteVO.getClassId());
            examCompleteAgainService.invokeExamCompleteAgain(params);
        } else {
            examCompleteAgainService.computeCradOrReadPaper(params);
        }
        log.info("添加按试题统计任务结束:{}", params);
    }

    @Override
    public void hwQnComplete(long examId, long paperId, List<Long> clsIds) {
        Map<String, Object> params = new HashMap<>();
        RequestVO user = RequestUtil.getUser();
        params.put("examId", examId);
        params.put("userId", user.getUserId() < 0 ? 1 : user.getUserId());
        params.put("userName", user.getUserName());
        params.put("currentTime", new Date());

        // 刷新考试报告
        log.info("添加统计任务开始:{}", params);
        examCompleteAgainService.invokeExamCompleteAgain(params);
        log.info("添加统计任务结束:{}", params);
    }

    @Override
    @MarkOperateLog(operationMessage = "执行自动阅卷结束")
    public boolean autoMarkComplete(MarkCompleteVO markCompleteVO) {
        long examPaperId = markCompleteVO.getExamPaperId();
        // 没有task为线下阅卷，自动结束阅卷设置不生效
        if (paperReadTaskService.existPaperReadTask(examPaperId) && !markConfigService.isMarkAutoComplete(examPaperId)) {
            return false;
        }

        return this.markComplete(markCompleteVO).getData();
    }

    /**
     * 按班级阅卷完成
     *
     * @param examPaperInfo 考试试卷
     * @param classId       班级id
     * @param userId        用户id
     * @param userName      用户名
     */
    private List<String> completeClass(ExamPaperInfoVO examPaperInfo, long classId, long userId, String userName) {
        long examId = examPaperInfo.getExamId();
        long paperId = examPaperInfo.getPaperId();
        long examPaperId = examPaperInfo.getExamPaperId();
        List<PaperClassVO> paperClassVOS = examPaperService.listClasses(examId, Collections.singletonList(paperId));
        List<PaperClassVO> markingClses = paperClassVOS.stream()
                .filter(x -> !ExamCheckUtil.beforeMark(x.getClassStatus())).collect(Collectors.toList());
        List<String> msgs = new ArrayList<>();
        if (markingClses.isEmpty()) {
            msgs.add("没有阅卷中的班级");
            return msgs;
        }
        List<Long> classIds;

        // 如果是全量更新，则判断是否还有未完成阅卷的班级，如果有，则过滤这些班级
        if (classId == 0) {
            List<Long> notCompleteClassIds = examItemService.listNotCompleteClassIds(examId, paperId);
            classIds = markingClses.stream().map(PaperClassVO::getClassId).filter(x -> !notCompleteClassIds.contains(x)).collect(Collectors.toList());
        } else {
            classIds = Collections.singletonList(classId);
        }
        if (classIds.isEmpty()) {
            msgs.add("没有已完成的班级");
            return msgs;
        }
        // 删除重新批阅数据
        examRepeatRecordService.finishRepeatMark(examPaperId, classIds);
        examClassPaperManager.updateStatus(examId, paperId, null, classIds, ExamPaperStatus.Finished.getStatus(), userId, userName);
        examService.updateExamClassStatus(examId, classIds, ExamPaperStatus.Finished.getStatus(), userId, userName);

        // 删除阅卷待办
        for (Long id : classIds) {
            todoTaskService.deleteMarkTodoTask(examPaperId, id, null, 0);
        }
        // 如果还有班级阅卷未完成则不处理
        paperClassVOS = examPaperService.listClasses(examId, Collections.singletonList(paperId));
        if (paperClassVOS.stream().anyMatch(x -> ExamCheckUtil.notFinishMark(x.getClassStatus()))) {
            msgs.add("还有班级未完成");
            return msgs;
        }
        // 修改考试试卷状态
        String msg = examPaperService.finishedMark(examPaperId, classId, userId, userName);
        if (StringUtils.isNotBlank(msg)) {
            msgs.add(msg);
            return msgs;
        }
        // 修改科目状态
        completeExamCourse(examId, examPaperInfo.getCourseId(), userId, userName);

        // 修改考试状态
        completeExam(examId, userId, userName);
        return msgs;
    }


    /**
     * 判断考试是否完成 且结束考试
     *
     * @param examId   考试id
     * @param userId   用户id
     * @param userName 用户名
     */
    private void completeExam(long examId, long userId, String userName) {
        if (examPaperService.listPapers(examId).stream().anyMatch(x -> ExamCheckUtil.notFinishMark(x.getExamPaperStatus()))) {
            return;
        }
        if (newExamService.listCourses(examId).stream().anyMatch(x -> ExamCheckUtil.notFinishMark(x.getCourseStatus()))) {
            return;
        }
        examService.finishExam(examId, userId, userName);
    }

    /**
     * 判断考试科目是否完成
     *
     * @param examId   考试id
     * @param courseId 科目ID
     * @param userId   用户id
     * @param userName 用户名
     */
    private void completeExamCourse(long examId, long courseId, long userId, String userName) {
        boolean courseNotFinished = examPaperService.listPapers(examId).stream().anyMatch(x -> x.getCourseId() == courseId && x.getExamPaperStatus() != 20);
        if (courseNotFinished) {
            return;
        }
        examService.finishCourse(examId, courseId, userId, userName);
    }
}
