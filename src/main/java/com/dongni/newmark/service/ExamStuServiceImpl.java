package com.dongni.newmark.service;

import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.serivice.exam.IExamStuService;
import com.dongni.exam.common.mark.vo.ExamPaperInfoVO;
import com.dongni.exam.common.mark.vo.SchoolStatVO;
import com.dongni.exam.common.mark.vo.StudentStatVO;
import com.dongni.exam.common.mark.vo.StudentVO;
import com.dongni.newmark.manager.ExamPaperManager;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ExamStuServiceImpl implements IExamStuService {

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private ExamPaperManager examPaperManager;
    @Override
    public List<StudentVO> listExamPaperStu(long examPaperId, List<Long> schoolIds, List<Long> studentIds) {
        ExamPaperInfoVO examPaperInfoVO = examPaperManager.findByExamPaperId(examPaperId);
        long examId = examPaperInfoVO.getExamId();
        long paperId = examPaperInfoVO.getPaperId();
        List<Map<String, Object>> students = examRepository.selectList("ExamResultMapper.getExamResult",
                MapUtil.of("examId", examId, "paperId", paperId, "schoolIds", schoolIds, "studentIds", studentIds,  "resultStatus", 0));
        return getStudentVOS(students);
    }

    @Override
    public List<StudentVO> listStudents(long examId, long paperId, List<Long> clsIds) {
        List<Map<String, Object>> students = examRepository.selectList("ExamResultMapper.getExamResult",
                MapUtil.of("examId", examId, "paperId", paperId, "classIds", clsIds, "resultStatus", 0));
        return getStudentVOS(students);
    }

    private List<StudentVO> getStudentVOS(List<Map<String, Object>> students) {
        return students.stream().map(x -> {
            StudentVO studentVO = new StudentVO();
            studentVO.setStudentId(MapUtil.getLong(x, "studentId"));
            studentVO.setStudentName(MapUtil.getString(x, "studentName"));
            studentVO.setSchoolId(MapUtil.getLong(x, "schoolId"));
            studentVO.setClassId(MapUtil.getLong(x, "classId"));
            studentVO.setResultStatus(MapUtil.getInt(x, "resultStatus"));
            studentVO.setClassName(MapUtil.getString(x, "className"));
            return studentVO;
        }).collect(Collectors.toList());
    }

    @Override
    public StudentStatVO statStudent(long examId, long paperId) {

        Map<String, Long> params = new HashMap<>();
        params.put("examId", examId);
        params.put("paperId", paperId);
        return getStudentStatVO(params);
    }

    @Override
    public int countStudent(long examId, long paperId) {
        Map<String, Object> params = new HashMap<>();
        params.put("examId", examId);
        params.put("paperId", paperId);
        Long count = examRepository.selectOne("ExamResultMapper.countStudent", params);
        return count.intValue();
    }

    @Override
    public StudentStatVO statStudent(long readBlockId) {

        Map<String, Long> params = new HashMap<>();
        params.put("readBlockId", readBlockId);
        return getStudentStatVO(params);
    }

    @Override
    public List<SchoolStatVO> statSchoolReferenceStuCnt(long examId, long paperId) {
        List<Map<String, Object>> stuStat = examRepository.selectList("ExamResultMapper.statSchoolReferenceStuCnt",
                MapUtil.of("examId", examId, "paperId", paperId));
        return stuStat.stream().map(x -> {
            SchoolStatVO schoolStatVO = new SchoolStatVO();
            schoolStatVO.setSchoolId(MapUtil.getLong(x, "schoolId"));
            schoolStatVO.setStuCount(MapUtil.getInt(x, "referenceCount"));
            return schoolStatVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SchoolStatVO> statSchoolActualStuCnt(long examId, long paperId) {
        List<Map<String, Object>> stuStat = examRepository.selectList("ExamResultMapper.statSchoolActualStuCnt",
                MapUtil.of("examId", examId, "paperId", paperId));
        return stuStat.stream().map(x -> {
            SchoolStatVO schoolStatVO = new SchoolStatVO();
            schoolStatVO.setSchoolId(MapUtil.getLong(x, "schoolId"));
            schoolStatVO.setStuCount(MapUtil.getInt(x, "actualCount"));
            return schoolStatVO;
        }).collect(Collectors.toList());
    }

    private StudentStatVO getStudentStatVO(Map<String, Long> params) {
        Map<String, Long> studentCountMap = examRepository.selectOne("ExamResultMapper.getStudentCount", params);

        StudentStatVO studentStatVO = new StudentStatVO();
        studentStatVO.setActualCount(studentCountMap.get("actualCount").intValue());
        studentStatVO.setReferenceCount(studentCountMap.get("referenceCount").intValue());
        return studentStatVO;
    }

}
