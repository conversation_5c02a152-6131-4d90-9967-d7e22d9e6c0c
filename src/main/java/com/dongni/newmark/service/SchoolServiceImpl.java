package com.dongni.newmark.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolService;
import com.dongni.exam.common.mark.vo.SchoolVO;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SchoolServiceImpl implements ISchoolService {

    @Autowired
    private BaseDataRepository baseDataRepository;

    @Override
    public List<SchoolVO> listByIds(Collection<Long> schoolIds) {
        if (schoolIds.isEmpty()) return new ArrayList<>();
        List<Map<String, Object>> schools = baseDataRepository.selectList("SchoolMapper.findByIds", MapUtil.of("schoolIds", schoolIds));
        return schools.stream().map(x -> {
            SchoolVO schoolVO = new SchoolVO();
            schoolVO.setSchoolId(MapUtil.getLong(x, "schoolId"));
            schoolVO.setSchoolName(MapUtil.getString(x, "schoolName"));
            return schoolVO;
        }).collect(Collectors.toList());
    }

    @Override
    public long findVirtualSchool() {
        return JedisTemplate.execute(jedis -> {
            String key = "BASE:DATA:VIRTUAL:SCHOOL";
            String schoolIdStr = jedis.get("key");
            if (schoolIdStr == null) {
                Long schoolId = baseDataRepository.selectOne("SchoolMapper.findVirtualSchool");
                schoolId = schoolId == null ? 0 : schoolId;
                jedis.set(key, String.valueOf(schoolId));
                return schoolId;
            }
            return Long.parseLong(schoolIdStr);
        });
    }
}
