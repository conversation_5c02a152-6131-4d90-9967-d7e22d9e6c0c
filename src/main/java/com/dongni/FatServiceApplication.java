package com.dongni;

import com.dongni.basedata.log.aspect.RequestLogAspect;
import com.dongni.common.utils.StackTraceUtil;
import com.dongni.commons.mvc.interceptor.UserLoginInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@ServletComponentScan
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(scanBasePackages={"com.dongni"})
@MapperScan(basePackages = {
        "com.dongni.exam.plan.dao","com.dongni.exam.newcard.dao",
        "com.dongni.exam.recognition.dao",
        "com.dongni.exam.mark.dao", "com.dongni.exam.item.dao",
        "com.dongni.exam.mark.ai.composition.dao",
        "com.dongni.exam.mark.ai.dao",
        "com.dongni.newmark.dao",
        "com.dongni.exam.mark.bigmodel.mapper"
})
//@Configuration
//@EnableAutoConfiguration(exclude= {
//        DataSourceAutoConfiguration.class,
//        MongoAutoConfiguration.class, MongoDataAutoConfiguration.class
//})
//@EnableConfigurationProperties
public class FatServiceApplication {

    public static void main(String[] args) {
        //不再使用旧的文件处理方式
        //System.setProperty("servletContextPath", "/home/<USER>/temp-file/");

        UserLoginInterceptor.addNotInterceptorUri("/error");
        
        StackTraceUtil.addClassNameStartWithForPrintOnly("com.dongni");
        StackTraceUtil.addClassNameStartWithForPrintOnly("com.hqjl");
        StackTraceUtil.setPrintProxy(false);
        System.setProperty("druid.mysql.usePingMethod", "false");
        
        RequestLogAspect.addNotAccessLogApi("POST /common/file/exist");
        
        SpringApplication.run(FatServiceApplication.class, args);
    }
}
