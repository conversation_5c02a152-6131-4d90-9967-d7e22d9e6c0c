package com.dongni.basedata.school.config.service;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/27.
 * <p>
 * 学校模块参数配置明细
 */
public interface IModuleConfigService {


    /**
     * 获取模块参数
     *
     * @param params foreignId foreignType key artsScience
     * @return 模块参数集合
     */
    List<Map<String, Object>> getModuleConfig(Map<String, Object> params);

    /**
     * 获取模块参数
     *
     * @param params foreignId foreignType key artsScience, type
     * @return 模块参数集合
     */
    <T> T getModuleConfig(Map<String, Object> params, Class<T> type);


    /**
     * 保存模块参数
     *
     * @param params foreignId foreignType key artsScience name value status
     */
    String saveModuleConfig(Map<String, Object> params);


    /**
     * 获取年级文理分科总人数
     *
     * @param params gradeId
     * @return 各科人数
     */
    List<Map<String, Object>> getGradeTotalStudent(Map<String, Object> params);

    /**
     * 导入目标达成度
     *
     * @param params key name  status gradeId
     * @return
     */
    Map<String, Object> importTargetAgreement(Map<String, Object> params);

    /**
     * 导入(分数段or名次段)指标数据
     *
     * @param params gradeId（导入名次段需要） foreignId foreignName foreignType key name status
     * @return
     */
    Map<String, Object> importSubsection(Map<String, Object> params);

    /**
     * 获取年级课程默认教材
     * @param params foreignId foreignType key
     * @return 年级课程默认教材
     */
    Map<String,Object> getDefaultTextbook(Map<String, Object> params);

    /**
     * 获取年级默认报表配置
     * @param params foreignId foreignType key
     * @return 年级默认报表配置
     */
    Map<String,Object> getDefaultReport(Map<String, Object> params);

    /**
     * 删除年级默认报表配置
     * @param params moduleConfigId
     * @return 删除年级默认报表配置
     */
    int delModuleConfig(Map<String, Object> params);

    /**
     * 批量插入或更新年级默认报表配置
     * @param params moduleConfigId
     * @return 数量
     */
    int batchSaveModuleConfig(Map<String, Object> params);

    /**
     * 获取模块参数
     *
     * @param params list foreignType key artsScience
     * @return 模块参数集合
     */
    List<Map<String, Object>> getAllModuleConfig(Map<String, Object> params);

    String updateModuleConfig(Map<String,Object> parameterMap);

    /**
     * 查询VIP截至日期
     * @param moduleConfig
     * @return
     */
    Map<String, Object> selectVipDeadline(Map<String, Object> moduleConfig);

    /**
     * 获取学校金卷知识点标注配置项(学校不存在||学校知识点标注配置不存在 默认返回公司标注 已跟产品沟通)
     * @param params schoolId
     * @return  knowledgeMarkStatus  学校配置项存在 取t_module_config.status
     *                               学校配置项不存在 默认为公司标注
     */
    Map<String, Object> getSchoolKnowledgeMarkConfig(Map<String, Object> params);

    /**
     * 批量删除学校模块
     * @param params schoolId
     */
    void deleteSchoolModuleConfig(Map<String, Object> params);

    /**
     * 保存学校模块配置
     * @param saveModuleConfigList
     */
    void batchInsertModuleConfig(List<Map<String, Object>> saveModuleConfigList);

    /**
     * 获取学校的个册配置
     *
     * @param schoolIdList 学校id列表
     * @return schoolId key status
     */
    List<Map<String, Object>> getSchoolNotebookConfig(List<Long> schoolIdList);

    /**
     * 获取学校的校本题库配置
     *
     * @param schoolId 学校id
     * @return 配置
     */
    Map<String, Object> getSchoolTikuConfig(long schoolId);

    /**
     * 获取学校打印开通状态
     * @param schoolId 学校ID
     * @return
     */
    Map<String, Object> getPrintingFilingModuleConfig(Long schoolId);
}
