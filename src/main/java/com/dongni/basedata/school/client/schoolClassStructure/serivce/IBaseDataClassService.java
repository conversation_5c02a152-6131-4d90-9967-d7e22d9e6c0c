package com.dongni.basedata.school.client.schoolClassStructure.serivce;

import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataClass;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataClassStudentCount;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataClassTeacherCount;

import java.util.List;
import java.util.Map;

public interface IBaseDataClassService {

    BaseDataClass saveBaseDataClass(BaseDataClass baseDataClass, Map<String, Object> params);

    BaseDataClass updateBaseDataClass(BaseDataClass baseDataClass, Map<String, Object> params);

    List<BaseDataClass> getBaseDataClassByGradeId(Long gradeId);

    List<BaseDataClass> getBaseDataClassByClassIds(List<Long> classIds);

    List<BaseDataClass> getBaseDataClassBySchoolId(Long schoolId);

    List<BaseDataClassStudentCount> getBaseDataClassStudentCountByGradeId(Long gradeId);

    List<BaseDataClassTeacherCount> getBaseDataClassTeacherCountByGradeId(Long gradeId);

    List<BaseDataClassTeacherCount> getBaseDataClassHeaderCountByGradeId(Long gradeId);
}
