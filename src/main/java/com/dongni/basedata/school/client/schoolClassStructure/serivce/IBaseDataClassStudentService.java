package com.dongni.basedata.school.client.schoolClassStructure.serivce;

import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataClassStudent;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataClassStudentCourse;
import com.dongni.basedata.school.client.schoolClassStructure.bean.req.BaseDataRequest;

import java.util.List;

public interface IBaseDataClassStudentService {

    List<BaseDataClassStudent> getBaseDataClassStudentListByStudentIds(List<Long> studentIDs);

    List<BaseDataClassStudent> getBaseDataAbsenceClassStudentListByStudentIds(List<Long> studentIDs);

    List<Long> getAdminClassIdsByStudentIds(List<Long> studentIDs);

    List<BaseDataClassStudent> getBaseDataClassStudentListByClassIds(List<Long> classIds);



    void deleteBaseDataClassStudentByStudentIdsAndClassId(List<Long> studentIDs,Long classId);


}
