package com.dongni.basedata.school.study.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Create by sapluk <br/>
 * time 14:48 2019/01/25 <br/>
 * description: <br/>
 * 学习相关 比如学生课程
 */
@Service
public class StudyService {

    @Autowired
    private BaseDataRepository baseDataRepository;

    /**
     * 查询学生在这班级下所学的课程,这里老师也可以查看，如果是班主任可以查看所有，如果是任课老师只能看任课课程
     *
     * @param params 学生Id:studentId(必选) 如果是任课老师则把 teacherId 带上
     * @return 学生的课程信息
     */
    public List<Map<String, Object>> getStudentClassCourse(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();

        // 是否是班主任进行判断，这里要判断是不是这个学生的班主任
        if (params.get("teacherId") != null && ObjectUtil.isNumeric(params.get("teacherId"))) {
            int count = baseDataRepository.selectOne("StudyMapper.getTeacherStudentClassHead", params);
            if (count > 0) {
                params.remove("teacherId");
            }
        }

        // 数据查询
        return baseDataRepository.selectList("StudyMapper.getStudentClassCourse", params);
    }
}
