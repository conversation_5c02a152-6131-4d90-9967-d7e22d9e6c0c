package com.dongni.basedata.school.grade.service.impl;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

import com.dongni.basedata.bean.BaseDataMongodb;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.enumeration.AutoPublishEnum;
import com.dongni.tiku.common.util.MapUtil;
import com.mongodb.client.MongoCollection;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: hzw
 * @date: 2023/4/21
 */
@Service
public class SchoolConfigService {

	private final MongoCollection<Document> collection;

	// 关闭
	private static final int OFF = 0;
	// 打开
	private static final int ON = 0;

	@Autowired
	public SchoolConfigService(BaseDataMongodb mongodb) {
		collection = mongodb.getMongoDatabase().getCollection("schoolConfig");
	}

	/**
	 * 获取学校配置
	 * @param params schoolId
	 */
	public Document getSchoolConfig(Map<String, Object> params) {
		Verify.of(params)
			.isValidId("schoolId")
			.verify();

		long schoolId = MapUtils.getLong(params, "schoolId");
		Document schoolConfigDoc = collection.find(eq("schoolId", schoolId)).projection(fields(excludeId())).first();
		boolean needInsert = MapUtils.isEmpty(schoolConfigDoc);
		schoolConfigDoc = populateSchoolConfig(schoolConfigDoc, schoolId);
		// 第一次获取为空,保存默认值
		if (needInsert) {
			collection.insertOne(schoolConfigDoc);
		}
		return schoolConfigDoc;
	}

	/**
	 * 更新作业发布默认配置
	 * @param params schoolId scanVersion
	 * scanVersion：
	 */
	public void updateHomework(Map<String, Object> params) {
		Verify.of(params)
			.isValidId("schoolId")
			.isNumeric("scanVersion")
			.verify();

		Bson updateBson = set("scanVersion", params.get("scanVersion"));
		if(params.containsKey("homeworkBackgroundImageUrl")){
			updateBson = combine(updateBson, set("homeworkBackgroundImageUrl", params.get("homeworkBackgroundImageUrl")));
			Document schoolConfig = getSchoolConfig(params);
			if(ObjectUtil.isNotBlank(params.get("homeworkBackgroundImageUrl")) && ObjectUtil.isBlank(schoolConfig.get("imageCreateTime"))){
				//第一次设置背景图时，记录下时间，用于发布作业的时候获取近5张背景图时限制查询的examId的边界
				updateBson = combine(updateBson, set("imageCreateTime", DateUtil.getCurrentDateTime()));
			}
		}
		collection.updateOne(eq("schoolId", MapUtils.getLong(params, "schoolId")), updateBson);
	}

	/**
	 * 更新在线报告AI配置
	 *
	 * @param params schoolId
	 *               summary ai全文总结
	 *               chat ai对话
	 */
	public void updateAiReport(Map<String, Object> params) {
		Verify.of(params)
				.isValidId("schoolId")
				.isInteger("summary")
				.isInteger("chat")
				.verify();

		long schoolId = MapUtil.getLong(params, "schoolId");
		Document update = new Document()
				.append("summary", MapUtil.getInt(params, "summary"))
				.append("chat", MapUtil.getInt(params, "chat"));

		collection.updateOne(eq("schoolId", schoolId), set("aiReport", update));
	}

	/**
	 * 更新线下作业配置
	 * @param params schoolId
	 *               studyGuideAutoPublish 线下作业配置
	 */
	public void updateStudyGuide(Map<String, Object> params) {
		Verify.of(params)
			.isValidId("schoolId")
			.isInteger("studyGuideAutoPublish")
			.verify();

		Bson updateBson = set("studyGuideAutoPublish", params.get("studyGuideAutoPublish"));
		collection.updateOne(eq("schoolId", MapUtils.getLong(params, "schoolId")), updateBson);
	}

	/**
	 * 保存学校配置中的第一次设置作业背景图时间
	 * @param schoolId 学校id
	 */
	public void saveImageCreateTime(Long schoolId, String imageCreateTime) {
		if (ObjectUtil.isBlank(getSchoolConfig(MapUtil.of("schoolId", schoolId)).get("imageCreateTime"))) {
			//第一次设置背景图时，记录下时间，用于发布作业的时候获取近5张背景图时限制查询的examId的边界
			collection.updateOne(eq("schoolId", schoolId), set("imageCreateTime", imageCreateTime));
		}

	}

	/**
	 * <pre>
	 * 补充学校配置缺少的字段
	 * 当新增配置时,用于处理旧数据,为新增字段添加默认值
	 * 1.scanVersion  默认2.0                    -- 不知道是啥页面没有配置
	 * 2.homeworkBackgroundImageUrl  默认""      -- 发布线上作业的默认背景
	 * 3.imageCreateTime 默认""                  -- 第一次设置背景图的时间
	 * 4.aiReport
	 * 		- summary 默认0(关闭)                 -- ai全文总结
	 * 	    - chat 默认0(关闭)                    -- ai对话
	 * </pre>
	 *
	 * @param schoolConfigDoc 数据库查询的出来的schoolConfig
	 * @param schoolId 学校id
	 */
	private Document populateSchoolConfig(Document schoolConfigDoc, Long schoolId) {
		if (schoolId == null) {
			throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "schoolId is null");
		}

		if (MapUtils.isEmpty(schoolConfigDoc)) {
			schoolConfigDoc = new Document();
		}
		schoolConfigDoc.append("schoolId", schoolId);
		schoolConfigDoc.putIfAbsent("scanVersion", 2.0);
		schoolConfigDoc.putIfAbsent("homeworkBackgroundImageUrl", "");
		schoolConfigDoc.putIfAbsent("imageCreateTime", null);

		Document aiReport = Optional.<Document>ofNullable(MapUtil.getCast(schoolConfigDoc, "aiReport"))
				.orElseGet(Document::new);
		aiReport.putIfAbsent("summary", OFF);
		aiReport.putIfAbsent("chat", OFF);
		schoolConfigDoc.put("aiReport", aiReport);

		//线下作业配置
		schoolConfigDoc.putIfAbsent("studyGuideAutoPublish", AutoPublishEnum.YES.getValue());

		return schoolConfigDoc;
	}
}
