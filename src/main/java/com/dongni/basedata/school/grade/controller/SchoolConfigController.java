package com.dongni.basedata.school.grade.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.school.grade.service.impl.SchoolConfigService;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: hzw
 * @date: 2023/4/21
 */
@RestController
@RequestMapping(value = BaseDataConfig.CONTEXT_PATH + "/school/config")
public class SchoolConfigController extends BaseController {

	@Autowired
	private SchoolConfigService schoolConfigService;

	/**
	 * 获取学校配置
	 */
	@GetMapping
	@DongniRequest(operationName = "学校.获取学校配置", remark = {"TODO 权限"})
	public Response getSchoolConfig() {
		return new Response(schoolConfigService.getSchoolConfig(getParameterMap()));
	}

	/**
	 * 更新作业发布默认配置
	 */
	@PostMapping("/update/homework")
	@DongniRequest(operationName = "学校.更新作业发布默认配置", remark = {"TODO 权限"})
	public Response updateHomework() {
		schoolConfigService.updateHomework(getParameterMap());
		return new Response();
	}

	/**
	 * 更新在线报告AI配置
	 */
	@PostMapping("/update/aiReport")
	@DongniRequest(operationName = "学校.更新在线报告AI配置", remark = {"TODO 权限"})
	public Response updateAiReport() {
		schoolConfigService.updateAiReport(getParameterMap());
		return new Response();
	}

	/**
	 * 更新线下作业配置
	 */
	@PostMapping("/update/studyGuide")
	@DongniRequest(operationName = "学校.更新线下作业配置", remark = {"TODO 权限"})
	public Response updateStudyGuide() {
		schoolConfigService.updateStudyGuide(getParameterMap());
		return new Response();
	}
}
