package com.dongni.basedata.school.student.export;

import com.dongni.basedata.school.operator.management.enumerate.SchoolApplicationDropDownFieldEnum;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.*;

import java.util.List;
import java.util.Map;

/**
 * 学生信息修改导出
 * <AUTHOR>
 */
public class BaseDataStudentUpdateImportReport extends ExportExcel {

    private List<Map<String, Object>> data;
    private List<String> fields;
    private List<String> headers;

    public BaseDataStudentUpdateImportReport(String sheetName, List<Map<String,Object>> data, List<String> headers, List<String> fields) {
        super(sheetName);
        this.fields = fields;
        this.data = data;
        this.headers=headers;
    }

    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        CellStyle promptStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        promptStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.index);
        promptStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Row row = sheet.createRow(currentRow);
        for (int i = 0; i < headers.size(); i++) {
            String field = headers.get(i);
            if (field.contains("旧") || field.contains("序号")) {
                ExcelUtil.createCell(row, currentCol++, field, promptStyle);
            } else {
                ExcelUtil.createCell(row, currentCol++, field, headerStyle);
            }

        }
        return ++currentRow;
    }

    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        if (CollectionUtils.isEmpty(data)) {
            return currentRow;
        }

        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle promptStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        promptStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.index);
        promptStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Integer index = 0;
        for (Map<String,Object> item : data){

            int stage = MapUtil.getIntNullable(item, "stage");
            boolean primary = DictUtil.isEquals(stage, "stage", "primary");
            boolean middle = DictUtil.isEquals(stage, "stage", "middle");
            boolean high = DictUtil.isEquals(stage, "stage", "high");

            item.put("index", ++index);
            Row row =sheet.createRow(currentRow);
            Integer fieldIndex = 0;
            for (String field: fields) {
                // 新选考科目
                if (ObjectUtil.isValueEquals(field, "newCourseSelectionGroupName")) {
                    String[] textList = {"无", "物化生", "物化政", "物化地", "物生政", "物生地", "物政地", "史政地", "史化政",
                            "史生地", "史化生", "史生政", "史化地", "物史地", "物化史", "物生史", "物政史", "化生政", "化生地",
                            "化政地", "生政地", "文综", "理综","全科"};
                    ExcelUtil.setHSSFValidation(sheet, textList, currentRow, currentRow, fieldIndex, fieldIndex);
                }

                // 新外语类型
                if (ObjectUtil.isValueEquals(field, "newForeignCourseName")) {
                    if (primary) {
                        String[] textList = {"英语"};
                        ExcelUtil.setHSSFValidation(sheet, textList, currentRow, currentRow, fieldIndex, fieldIndex);
                    } else if (middle) {
                        String[] textList = {"英语", "日语", "俄语"};
                        ExcelUtil.setHSSFValidation(sheet, textList, currentRow, currentRow, fieldIndex, fieldIndex);
                    } else if (high) {
                        String[] textList = {"英语", "日语", "俄语", "法语", "西班牙语", "德语"};
                        ExcelUtil.setHSSFValidation(sheet, textList, currentRow, currentRow, fieldIndex, fieldIndex);
                    } else {
                        String[] textList = {};
                        ExcelUtil.setHSSFValidation(sheet, textList, currentRow-1, currentRow-1, fieldIndex, fieldIndex);
                    }
                }

                if (field.contains("studentName") || field.contains("new")) {
                    ExcelUtil.createCell(row, currentCol++, item.get(field), bodyStyle);
                } else {
                    ExcelUtil.createCell(row, currentCol++, item.get(field), promptStyle);
                }

                fieldIndex++;
            }

            ++currentRow;
            currentCol = 0;
        }

        Row row = sheet.getRow(2);
        if (ObjectUtil.isBlank(row)) {
            row = sheet.createRow(2);
        }
        Integer remarkCol = 12;
        int gap = 2;
        CellStyle remarkStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        remarkStyle.setFillForegroundColor(IndexedColors.LIGHT_CORNFLOWER_BLUE.index);
        remarkStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        ExcelUtil.createCell(row, remarkCol, "提示", remarkStyle);
        ExcelUtil.addMergedRegion(sheet, row.getRowNum(), row.getRowNum(), remarkCol, remarkCol + gap);


        Row remarkRow = sheet.getRow(3);
        if (ObjectUtil.isBlank(remarkRow)) {
            remarkRow = sheet.createRow(3);
        }
        CellStyle contentStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        contentStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.index);
        contentStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
        contentStyle.setAlignment(CellStyle.ALIGN_LEFT);
        contentStyle.setWrapText(true);
        ExcelUtil.createCell(remarkRow, remarkCol, "1.橙色列请勿手动修改以免影响数据导入准确性 \n" +
                "2.绿色列为需要修改的新信息，若不填写则默认不修改该学生此项信息\n" +
                "3.请确保需要修改的新学号、新座位号、新学籍号不重复", contentStyle);
        ExcelUtil.addMergedRegion(sheet, remarkRow.getRowNum(), remarkRow.getRowNum() + 5, remarkCol, remarkCol + gap);

        return currentRow;
    }
}
