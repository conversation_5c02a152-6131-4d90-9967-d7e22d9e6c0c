package com.dongni.basedata.school.escalation.bean;

import lombok.Data;

import java.util.List;

@Data
public class StudentBean {
    private Long studentId;
    private Long classId;
    private Long courseSelectionGroupId;
    private Long foreignCourseId;
    private String studentName;
    private String studentNum;
    private String candidate;
    private String joinStr;
    private Long schoolId;
    private Long gradeId;
    private String currentTime;
    private String studentNamePinyin;
    private String studentPhone;
    private String studentPhoneAes;
    private String studentNo;
    private String seatNumber;
    private Long userId;
    private String userName;
    private List<Object> body;
    private Integer row;
    private Boolean hasStudentNum = true;


}
