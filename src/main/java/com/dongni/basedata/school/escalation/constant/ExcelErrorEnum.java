package com.dongni.basedata.school.escalation.constant;

public enum ExcelErrorEnum {

    SCHOOL_NOT_EMPTY(1, "学校名称不能为空"),
    SCHOOL_NOT_EXIT(2, "学校名称不存在"),
    SCHOOL_NAME_REPEAT(3, "学校名称重复"),
    PHONE_NOT_EMPTY(4, "上报账号不能为空"),
    PHONE_NOT_CORRECT(25, "上报账号格式不正确"),
    TEACHER_NAME_EMPTY(5, "教师姓名不能为空"),
    TEACHER_NAME_REPEAT(6, "教师姓名重复"),
    TEACHER_PHONE_EMPTY(7, "教师账号不能为空"),
    TEACHER_PHONE_REPEAT(8, "教师账号重复"),
    TEACHER_PHONE_NOT_EXIT(9, "教师账号不存在"),
    TEACHER_COURSE_NOT_EXIT(10, "阅卷科目不存在"),
    STUDENT_NAME_NOT_EMPTY(11, "学生姓名不能为空"),
    STUDENT_NAME_REPEAT(12, "学生姓名在班级内重复"),
    SCHOOL_STUDENT_NAME_REPEAT(23, "学生姓名在学校内重复"),
    STUDENT_NAME_NOT_EXIT(13, "学生姓名在班级里不存在"),
    SCHOOL_STUDENT_NAME_NOT_EXIT(28, "学生姓名在学校里不存在"),
    STUDENT_REPEAT(29, "学生在表格中重复"),
    STUDENT_CANDIDATE_NOT_EMPTY(14, "学生考号不能为空"),
    STUDENT_CANDIDATE_NOT_CORRECT(24, "学生考号格式不正确"),
    STUDENT_CANDIDATE_EXIT_WORD(27, "学生考号格式不正确"),
    STUDENT_CANDIDATE_REPEAT(15, "学生考号重复"),
    STUDENT_NO_REPEAT(16, "学生学号重复"),
    STUDENT_NO_NOT_EXIT(17, "学生学号不存在"),
    STUDENT_NO_NOT_EMPTY(26, "学生学号不能为空"),
    STUDENT_IN_EXCEL_REPEAT(28, "学生在表格中重复"),
    STUDENT_CLASS_NOT_EMPTY(18, "学生班级不能为空"),
    COURSE_SELECTION_GROUP_NOT_EXIT(19, "选科组合不存在"),
    COURSE_FOREIGN_COURSE_NOT_EXIT(20, "外语科目不存在"),
    COURSE_NOT_EXIT(21, "科目不存在"),
    TEACHER_NAME_ERROR(22, "教师姓名不匹配");


    private final int type;
    private final String message;

    ExcelErrorEnum(int type, String message) {
        this.type = type;
        this.message = message;
    }

    public int getType() {
        return this.type;
    }

    public String getMessage() {
        return this.message;
    }

}
