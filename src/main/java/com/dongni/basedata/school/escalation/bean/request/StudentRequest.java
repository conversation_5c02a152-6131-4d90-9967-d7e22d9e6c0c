package com.dongni.basedata.school.escalation.bean.request;

import com.dongni.basedata.school.escalation.bean.common.Course;
import com.dongni.commons.entity.BaseRequestParams;
import lombok.Data;

import java.util.List;

@Data
public class StudentRequest extends BaseRequestParams {
    private Long studentId;
    private Long schoolEscalationId;
    private Long schoolId;
    private Long classId;
    private Long gradeId;
    private String studentName;
    private String candidate;
    private Long courseSelectionGroupId;
    private Long foreignCourseId;
    private List<Course> otherCourseList;
    private String examRoomNo;
    private String seatNumber;
}
