package com.dongni.basedata.school.operator.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.dongni.basedata.admin.service.IBaseSchoolService;
import com.dongni.basedata.admin.service.impl.BaseOperatorServiceImpl;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.school.grade.service.impl.GradeServiceImpl;
import com.dongni.basedata.system.account.service.IUserService;
import com.dongni.common.report.excel.ExcelReport;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.common.report.excel.simple.SimpleExcelReport;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.utils.ResourceConfig;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import com.pugwoo.wooutils.json.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> <br/>
 * @date 2020/05/18 <br/>
 * 学校运营人员
 */
@Service
public class SchoolOperatorServiceImpl {
    
    @Autowired
    private BaseDataRepository baseDataRepository;
    
    @Autowired
    private BaseOperatorServiceImpl baseOperatorService;
    
    @Autowired
    private IUserService userService;
    
    @Autowired
    private IBaseSchoolService baseSchoolService;

    @Autowired
    private GradeServiceImpl gradeService;
    
    /**
     * 获取绑定学校列表
     * @param params
     *   - operatingType    审核/校对
     *   - [search]       运营人员姓名 / 学校名称
     *   - [pageNo]
     *   - [pageSize]
     * @return
     *   - count
     *   - schoolOperatorList
     *      - schoolId,      学校id
     *      - schoolName,    学校名称
     *      - userId,        用户id
     *      - userName,      用户名
     *      - accountName,   联系方式
     *      - accountNameAes,
     *      - operatorSchoolId
     *      - createDateTime 开始时间
     */
    public Map<String, Object> getSchoolOperatorList(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("operatingType")
                .verify();
    
        Map<String, Object> queryParams = MapUtil.copy(params, "operatingType", "pageNo", "pageSize", "currentIndex");
        baseOperatorService.getUserType(Integer.parseInt(params.get("operatingType").toString()));
        queryParams.put("userStatusOn", DictUtil.getDictValue("userStatus", "on"));
        queryParams.put("accountStatusOn", DictUtil.getDictValue("accountStatus", "on"));
        
        String listStatement = "SchoolOperatorMapper.getSchoolOperatorList";
        if (!ObjectUtil.isBlank(params.get("search"))) {
            queryParams.put("search", params.get("search").toString().trim());
            listStatement = listStatement + "ForSearch";
        }
        int count = baseDataRepository.selectOne(listStatement + "Count", queryParams);
        if (count == 0) {
            return MapUtil.of("count", count, "schoolOperatorList", Collections.emptyList());
        }
        
        List<Map<String, Object>> schoolOperatorList = baseDataRepository.selectList(listStatement, queryParams);
        //是否经过校验，是则显示解密后的信息
        if (userService.checkUser(params)){
            SensitiveInfoUtil.aesDecrypt(schoolOperatorList,"accountNameAes");
        }
    
        return MapUtil.of("count", count, "schoolOperatorList", schoolOperatorList);
    }
    
//    /**
//     * 获取绑定学校列表
//     * @param params
//     *   - operatingType    审核/校对
//     *   - [search]       运营人员姓名 / 学校名称
//     *   - [pageNo]
//     *   - [pageSize]
//     * @return
//     *   - count
//     *   - schoolOperatorList
//     *      - schoolId,      学校id
//     *      - schoolName,    学校名称
//     *      - userName,      用户名
//     *      - accountName,   联系方式
//     *      - accountNameAes,
//     *      - createDateTime 开始时间
//     */
//    public Map<String, Object> getSchoolOperatorList2(Map<String, Object> params) {
//        Verify.of(params)
//                .isNumeric("operatingType")
//                .verify();
//
//        Map<String, Object> queryParams = MapUtil.copy(params, "operatingType", "pageNo", "pageSize", "currentIndex");
//        baseOperatorService.getUserType(Integer.parseInt(params.get("operatingType").toString()));
//        queryParams.put("userStatusOn", DictUtil.getDictValue("userStatus", "on"));
//        queryParams.put("accountStatusOn", DictUtil.getDictValue("accountStatus", "on"));
//
//        String search = null;
//        String listStatement = "SchoolOperatorMapper.getSchoolOperatorList";
//        if (!ObjectUtil.isBlank("search")) {
//            search = params.get("search").toString().trim();
//            queryParams.put("search", search);
//            listStatement = listStatement + "ForSearch";
//        }
//
//        boolean pageSearch = ObjectUtil.isNumeric("pageNo")
//                && ObjectUtil.isNumeric("pageSize")
//                && ObjectUtil.isNumeric("currentIndex");
//        Object pageNo = params.get("pageNo");
//        Object pageSize = params.get("pageSize");
//        Object currentIndex = params.get("currentIndex");
//
//        // 额外的几个学校   懂你小学 懂你初中 懂你高中 区域专用学校
//        List<Map<String, Object>> extraSchoolInfoList = getExtraSchoolList();
//        List<Long> schoolIdList = extraSchoolInfoList.stream()
//                .map(o -> Long.parseLong(o.get("schoolId").toString()))
//                .collect(toList());
//
//        int extraCount = 0;
//        List<Map<String, Object>> extraSchoolOperatorList = new ArrayList<>();
//
//        if (CollectionUtils.isNotEmpty(schoolIdList)) {
//            Map<String, Object> queryParamsForExtraSchool = MapUtil.copy(params, "operatingType", "pageNo", "pageSize", "currentIndex");
//            queryParamsForExtraSchool.put("schoolIdList", schoolIdList);
//            if (!ObjectUtil.isBlank(search)) {
//                queryParamsForExtraSchool.put("search", search);
//            }
//
//            extraCount = baseDataRepository.selectOne("SchoolOperatorMapper.getOperatorForExtraSchoolCount", queryParamsForExtraSchool);
//            if (extraCount > 0) {
//                List<Map<String, Object>> extraSchoolOperatorInfoList =
//                        baseDataRepository.selectList("SchoolOperatorMapper.getOperatorForExtraSchool", queryParamsForExtraSchool);
//                if (CollectionUtils.isNotEmpty(extraSchoolOperatorInfoList)) {
//                    Map<Long, Map<String, Object>> schoolIdMapExtraSchoolOperatorInfo = extraSchoolOperatorInfoList.stream()
//                            .collect(toMap(o -> Long.parseLong(o.get("schoolId").toString()), o -> o));
//                    for (Map<String, Object> extraSchoolInfo : extraSchoolInfoList) {
//                        Map<String, Object> extraSchoolOperatorInfo =
//                                schoolIdMapExtraSchoolOperatorInfo.get(Long.parseLong(extraSchoolInfo.get("schoolId").toString()));
//                        if (MapUtils.isNotEmpty(extraSchoolOperatorInfo)) {
//                            Map<String, Object> tmp = new HashMap<>(extraSchoolInfo);
//                            MapUtil.copy(extraSchoolInfo, tmp, "schoolName");
//                            extraSchoolOperatorList.add(tmp);
//                        }
//                    }
//                }
//            }
//        }
//
//        int schoolOperatorListSize = baseDataRepository.selectOne(listStatement + "Count", queryParams);
//        int count = extraCount + schoolOperatorListSize;
//
//
//
//        if (count == 0) {
//            return MapUtil.of("count", count, "schoolOperatorList", Collections.emptyList());
//        }
//
//        List<Map<String, Object>> schoolOperatorList = baseDataRepository.selectList(listStatement, queryParams);
//
//        List<Map<String, Object>> allSchoolOperatorList = new ArrayList<>();
//        allSchoolOperatorList.addAll(extraSchoolOperatorList);
//        allSchoolOperatorList.addAll(schoolOperatorList);
//
//        //是否经过校验，是则显示解密后的信息
//        if (userService.checkUser(params)){
//            SensitiveInfoUtil.aesDecrypt(allSchoolOperatorList,"accountNameAes");
//        }
//
//        return MapUtil.of("count", count, "schoolOperatorList", schoolOperatorList);
//    }
//
//    /**
//     * @return [{},{}]
//     *   schoolId
//     *   schoolName
//     */
//    private List<Map<String, Object>> getExtraSchoolList() {
//        List<Map<String, Object>> extraSchoolList = entrustVirtualInfo.getVirtualSchoolInfoList();
//        extraSchoolList.add(MapUtil.of("schoolId", 0, "schoolName", "区域专用学校"));
//        return extraSchoolList;
//    }
    
    /**
     * 绑定
     * @param params
     */
    public void bind(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isValidId("operatorId")
                .isNumeric("schoolId")
                .isNumeric("operatingType")
                .verify();
        
        int operatingType = Integer.parseInt(params.get("operatingType").toString());
        Map<String, Object> operatorDetail =
                baseOperatorService.getDetailWithStatusOnByOperatorIdAndType(MapUtil.of(
                        "operatorId", params.get("operatorId"),
                        "operatingType", operatingType
                ));
        if (MapUtils.isEmpty(operatorDetail)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "运营人员不存在或被注销，请刷新重试");
        }
    
        Map<String, Object> insertParams = new HashMap<>();
        insertParams.put("userId", params.get("userId"));
        insertParams.put("userName", params.get("userName"));
        insertParams.put("schoolId", params.get("schoolId"));
        insertParams.put("operatingType", operatingType);
        insertParams.put("operatorUserId", operatorDetail.get("userId"));
        ParamsUtil.setCurrentTime(insertParams);
        baseDataRepository.insert("SchoolOperatorMapper.bindSchoolOperator", insertParams);
    }
    
    /**
     * 绑定 给导入使用
     * @param params
     *  - userId
     *  - userName
     *  - operatingType
     *  - operatorSchoolList: [{}, ...]
     *      - operatorUserId
     *      - schoolId
     */
    private void bindForImport(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNumeric("operatingType")
                .isNotEmptyCollections("operatorSchoolList")
                .verify();
        ParamsUtil.setCurrentTime(params);
        baseDataRepository.insert("SchoolOperatorMapper.bindForImportSchoolOperator", params);
    }
    
    /**
     * 解绑
     * @param params
     */
    public void unbind(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("schoolId")
                .isNumeric("operatingType")
                .verify();
        int operatingType = Integer.parseInt(params.get("operatingType").toString());
        baseOperatorService.getUserType(operatingType);
        baseDataRepository.delete("SchoolOperatorMapper.unbindSchoolOperator", params);
    }
    
    
    /**
     * 取消关联 用于注销运营账户时使用 其他勿用
     */
    public void unbindByUserIdAndType(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNumeric("operatingType")
                .verify();
        baseDataRepository.delete("SchoolOperatorMapper.unbindByUserIdAndType", params);
    }
    
    /**
     * 解绑 给导入使用
     * @param params
     *  - operatingType
     *  - schoolIdList: [{Long}, ...]
     */
    private void unbindForImport(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("operatingType")
                .isNotEmptyCollections("schoolIdList")
                .verify();
        baseDataRepository.delete("SchoolOperatorMapper.unbindForImportSchoolOperator", params);
    }
    
    /**
     * 修改运营人员的运营类型专用 判断userId维护的学校有没有另外一个运营类型的运营员
     *  运营员userId -> schoolIds -> 另外一个运营类型的数量 只要有一个就不允许修改
     * @param params 运营人员的userId
     * @return true 存在    不允许更改运营人员的运营类型 改了也会报错
     */
    public boolean existOtherTypeOperatorByUserId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .verify();
        Integer exist = baseDataRepository.selectOne("SchoolOperatorMapper.existOtherTypeOperatorByUserId",
                params);
        return exist != null;
    }
    
    /**
     * 更新用户的运营类型 运营人员更换运营类型时调用
     * @param params
     *   - userId    接口请求的userId
     *   - userName  接口请求的userName
     *   - updateUserId     被更新的userId
     *   - operatingType    新的operatingType
     */
    public void updateUserOperatorType(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isValidId("updateUserId")
                .isNumeric("operatingType")
                .verify();
        ParamsUtil.setCurrentTime(params);
        baseDataRepository.update("SchoolOperatorMapper.updateUserOperatorType", params);
    }
    
    /**
     * 获取运营人员信息
     * @param schoolIdList
     * @return
     */
    public List<Map<String, Object>> getOperatorBySchoolIdList(List<Long> schoolIdList) {
        if (CollectionUtils.isEmpty(schoolIdList)) {
            return Collections.emptyList();
        }
        Map<String, Object> queryParams = MapUtil.of("schoolIdList", schoolIdList);
        queryParams.put("userStatusOn", DictUtil.getDictValue("userStatus", "on"));
        queryParams.put("accountStatusOn", DictUtil.getDictValue("accountStatus", "on"));
        return baseDataRepository.selectList("SchoolOperatorMapper.getOperatorBySchoolIdList", queryParams);
    }
    
    /**
     * 获取运营人员负责的schoolIdList
     * @param params userId
     * @return
     */
    public List<Long> getSchoolIdListByUserId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .verify();
        
        return baseDataRepository.selectList("SchoolOperatorMapper.getSchoolIdListByUserId", MapUtil.of(
                "userId", params.get("userId"),
                "userStatusOn", DictUtil.getDictValue("userStatus", "on")
        ));
    }
    
    /**
     * 获取运营人员负责的学校列表
     * @param params userId
     * @return [{}] schoolId schoolName
     */
    public List<Map<String, Object>> getSchoolInfoListByUserId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .verify();
        
        return baseDataRepository.selectList("SchoolOperatorMapper.getSchoolInfoListByUserId", MapUtil.of(
                "userId", params.get("userId"),
                "userStatusOn", DictUtil.getDictValue("userStatus", "on")
        ));
    }
    
    /**
     * 获取运营人员信息
     * @param params schoolId  operatingType
     * @return userId userName userType
     */
    public Map<String, Object> getOperatorInfoBySchoolAndType(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("schoolId")
                .isNumeric("operatingType")
                .verify();
        
        return baseDataRepository.selectOne("SchoolOperatorMapper.getOperatorInfoBySchoolAndType", MapUtil.of(
                "schoolId", params.get("schoolId"),
                "operatingType", params.get("operatingType"),
                "userStatusOn", DictUtil.getDictValue("userStatus", "on")
        ));
    }
    
    /**
     * 导出学校-运营人员关联模板
     * @param params operatingType 运营类型
     * @return
     */
    public String exportForImportSchoolOperation(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("operatingType")
                .verify();
        
        int operatingType = Integer.parseInt(params.get("operatingType").toString());
        String operatingTypeLabel = DictUtil.getDictLabel("operatingType", operatingType);
        SimpleExcelHeader headers = new SimpleExcelHeader(
                Arrays.asList("no", "schoolId", "schoolName", "operatingTypeLabel", "userName", "accountName"),
                Arrays.asList("序号", "学校编号", "学校名称", "运营类型", "负责人", "联系方式"));
    
        Map<String, Object> schoolOperatorMap = getSchoolOperatorList(MapUtil.of("operatingType", operatingType));
        List<Map<String, Object>> schoolOperatorList = (List<Map<String, Object>>) schoolOperatorMap.get("schoolOperatorList");
        for (int i = 0, iLen = schoolOperatorList.size(); i < iLen; i++) {
            Map<String, Object> item = schoolOperatorList.get(i);
            item.put("operatingTypeLabel", operatingTypeLabel);
            item.put("no", i + 1);
        }

        ExcelReport report = new SimpleExcelReport(schoolOperatorList, headers);
        return report.exportToFileStorage("运营" + operatingTypeLabel + "导入模板");
    }
    
    /**
     * 导入学校-运营人员关联模板
     *    序号     序号仅用于提示错误信息
     *    学校id    查询学校是否存在
     *    学校名称  辅助字段 忽略
     *    运营类型  运营类型必须与 operatingType 相对应
     *    负责人    辅助字段 忽略
     *    联系方式  accountNameSrc
     *             如果不提供 原来有维护人员的 移除
     *                       原来没有维护人员的 忽略
     *             如果带* 如134****2360 忽略
     *             正常的账号 -> accountNameAes
     *                       -> sys_account.account_name_aes join sys_user.user_type = 运营类型的userType
     *                       -> userList
     *                       -> isEmpty 错误
     * @param params operatingType -> operatingTypeLabel 必须与文件的运营类型相等
     *                             -> userType 用于查询账号
     * @return {
     *     validatorCount: 0为成功的
     *     validatorList: [String] 错误信息列表
     * }
     */
    @Transactional(value = BaseDataRepository.TRANSACTION, rollbackFor = Exception.class)
    public Map<String, Object> importSchoolOperation(Map<String, Object> params, MultipartFile importFile) {
        // ------------------------------------------------------------------------------------------- 校验基本请求信息
        if (importFile == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "导入文件必须提供");
        }
        Verify.of(params)
                .isNumeric("operatingType")
                .verify();
        
        int operatingType = Integer.parseInt(params.get("operatingType").toString());
        String operatingTypeLabel = DictUtil.getDictLabel("operatingType", operatingType);
        // 运营类型对应的userType
        Integer operatingUserType = null;
        // 审核员
        if (DictUtil.isEquals(operatingType, "operatingType", "reviewer")) {
            operatingUserType = DictUtil.getDictValue("userType", "operatorReviewer");
        }
        // 校对员
        else if (DictUtil.isEquals(operatingType, "operatingType", "proofreader")) {
            operatingUserType = DictUtil.getDictValue("userType", "operatorProofreader");
        }
        // 必须要有userType
        if (operatingUserType == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "可能新增了其他的运营类型，请联系管理员");
        }
        
        // 验证文件名 必须提供 且类型只能是 xls 或 xlsx
        String fileName = importFile.getOriginalFilename();
        if(fileName == null || (!fileName.endsWith("xls") && !fileName.endsWith("xlsx"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "文件名必须提供");
        }
    
        // ------------------------------------------------------------------------------------------- 获取导入文件内容
        // 校验的错误信息
        List<String> validatorList = new ArrayList<>();
        // 校验基本信息并获取表体内容
        Map<String, List<Map<String, String>>> bodyMap = new HashMap<>();
        FileStorageTemplate.local(fileStorage -> {
            File target = new File(fileStorage.getRootPath()
                    + ResourceConfig.getString("importTempSchoolOperator") + fileName);
            InputStream is = null;
            try{
                is = importFile.getInputStream();
                FileUtils.copyInputStreamToFile(is,target);
            }catch (IOException e){
                throw new CommonException(ResponseStatusEnum.FILE_ERROR,e);
            }finally {
                IOUtils.closeQuietly(is);
            }
            
            // 获取文件头
            List<String> header = ExcelUtil.getHeader(target);
            if(CollectionUtils.isEmpty(header)
                    || header.size() != 6
                    || !"序号".equals(header.get(0))
                    || !"学校编号".equals(header.get(1))
                    || !"学校名称".equals(header.get(2))
                    || !"运营类型".equals(header.get(3))
                    || !"负责人".equals(header.get(4))
                    || !"联系方式".equals(header.get(5))){
                validatorList.add("表头信息不完整，从左往右依次排列为：序号、学校编号、学校名称、运营类型、负责人、联系方式");
                return;
            }
            
            // 获取文件体
            List<String> keys = Arrays.asList("no", "schoolId", "schoolName", "operatingTypeLabel", "userName", "accountName");
            List<Map<String, String>> bodyList = ExcelUtil.getBody(target, keys);
            if (CollectionUtils.isEmpty(bodyList)) {
                validatorList.add("表体数据为空，不需要导入");
            }
            bodyMap.put("bodyList", bodyList);
        });
        
        // 如果校验的错误信息不为空 则直接返回
        if (CollectionUtils.isNotEmpty(validatorList)) {
            return MapUtil.of("validatorCount", validatorList.size(), "validatorList", validatorList);
        }
    
        // ------------------------------------------------------------------------------------------- 校验导入内容
        
        // 表体内容
        List<Map<String, String>> bodyList = bodyMap.get("bodyList");
        
        // 学校idList
        List<Long> bodySchoolIdList = bodyList.stream()
                .filter(row -> ObjectUtil.isNotBlank(row.get("schoolId")) && ObjectUtil.isLong(row.get("schoolId").trim()))
                .map(row -> MapUtil.getLong(row.get("schoolId").trim()))
                .collect(toList());
        long schoolIdDistinctCount = bodySchoolIdList.stream().distinct().count();
        if (schoolIdDistinctCount != bodySchoolIdList.size()) {
            validatorList.add("学校编号冲突! 存在重复的学校编号，请勿更改学校编号列");
            return MapUtil.of("validatorCount", validatorList.size(), "validatorList", validatorList);
        }
    
        // 账号加密List 用于查询用户数据  列表为空不能结束 可能是做解绑操作
        Set<String> bodyAccountNameAesSet = new HashSet<>();
        bodyList.forEach(row -> {
            if (ObjectUtil.isBlank(row.get("accountName")) || row.get("accountName").contains("*")) {
                return;
            }
            row.put("accountName", row.get("accountName").trim());
            SensitiveInfoUtil.aesEncrypt(row, "accountName");
            bodyAccountNameAesSet.add(row.get("accountNameAes"));
        });
        
        List<Map<String, Object>> userInfoList;
        if (CollectionUtils.isEmpty(bodyAccountNameAesSet)) {
            userInfoList = new ArrayList<>();
        } else {
            userInfoList = userService.getUserInfoByAccountNameAesAndUserType(MapUtil.of(
                    "accountNameAesList", new ArrayList<>(bodyAccountNameAesSet),
                    "queryUserType", operatingUserType
            ));
        }
        // accountNameAes -> AccountInfo
        Map<String, Map<String, Object>> accountNameAesMapUserInfo = userInfoList.stream()
                .collect(toMap(o -> o.get("accountNameAes").toString(), o -> o));
    
    
        // 获取关联的数据 也就是模板拿到的对应关系
        Map<String, Object> schoolOperatorMap = getSchoolOperatorList(MapUtil.of("operatingType", operatingType));
        List<Map<String, Object>> schoolOperatorList = (List<Map<String, Object>>) schoolOperatorMap.get("schoolOperatorList");
        // schoolId -> SchoolOperator
        Map<Long, Map<String, Object>> schoolIdMapSchoolOperator = schoolOperatorList.stream()
                .collect(toMap(o -> MapUtil.getLong(o, "schoolId"), o -> o));
        
        List<Map<String, Object>> bindInfoList = new ArrayList<>();
        List<Long> unbindSchoolIdList = new ArrayList<>();
        
        // 校验数据合法性 并组装数据
        boolean validatorListNotEmpty = false;
        for (int i = 0, iLen = bodyList.size(); i < iLen; i++) {
            int realRowNo = i + 1;
            Map<String, String> row = bodyList.get(i);
            System.out.println(JSON.toJson(row));
            String no = Optional.ofNullable(row.get("no")).map(String::trim).orElse("");
            String schoolIdString = Optional.ofNullable(row.get("schoolId")).map(String::trim).orElse("");
            String rowLabel = Optional.ofNullable(row.get("operatingTypeLabel")).map(String::trim).orElse("");
            String accountName = row.get("accountName");
            String accountNameAes = row.get("accountNameAes");
            String accountNameSrc = row.get("accountNameSrc");
            Long schoolId = null;
            if (ObjectUtil.isLong(schoolIdString)) {
                schoolId = MapUtil.getLong(schoolIdString);
            }
            
            // 如果提供的联系方式包含**脱敏数据 则直接无视
            if (accountNameAes == null && accountName != null && accountName.contains("*")) {
                continue;
            }
    
            // 如果提供的联系方式为空的，且schoolId非法 无视
            if (accountNameAes == null && schoolId == null) {
                continue;
            }
            
            // 校验联系方式的用户是否存在
            if (accountNameAes != null) {
                Map<String, Object> userInfo = accountNameAesMapUserInfo.get(accountNameAes);
                if (MapUtils.isEmpty(userInfo)) {
                    validatorList.add("第" + realRowNo + "行序号" + no + ",联系方式" + accountNameSrc + "获取不到用户信息");
                    validatorListNotEmpty = true;
                }
            }
    
            // 校验学校是否存在
            Map<String, Object> schoolInfo = schoolIdMapSchoolOperator.get(schoolId);
            if (schoolId != null) {
                if (MapUtils.isEmpty(schoolInfo)) {
                    validatorList.add("第" + realRowNo + "行序号" + no + ",学校编号" + schoolId + "获取不到学校信息");
                    validatorListNotEmpty = true;
                }
            }
            
            // 校验运营类型
            if (!ObjectUtil.isValueEquals(operatingTypeLabel, rowLabel)) {
                validatorList.add("第" + realRowNo + "行序号" + no + ",运营类型错误: " + operatingTypeLabel + "!=" + rowLabel);
                validatorListNotEmpty = true;
            }
            
            // 验证不通过 不需要组装数据了
            if (validatorListNotEmpty) {
                continue;
            }
            
            // 联系方式没有提供的 可能是要解绑的
            if (accountNameAes == null) {
                // 存在 需要解绑
                if (schoolInfo.get("operatorSchoolId") != null) {
                    unbindSchoolIdList.add(schoolId);
                }
            }
            
            // 有提供联系方式
            else {
                Map<String, Object> userInfo = accountNameAesMapUserInfo.get(accountNameAes);
                
                Map<String, Object> schoolOperatorInfo = new HashMap<>();
                schoolOperatorInfo.put("schoolId", schoolId);
                schoolOperatorInfo.put("operatorUserId", userInfo.get("userId"));
                bindInfoList.add(schoolOperatorInfo);
            }
        }
        
        if (CollectionUtils.isNotEmpty(validatorList)) {
            return MapUtil.of("validatorCount", validatorList.size(), "validatorList", validatorList);
        }
    
        // ------------------------------------------------------------------------------------------- 开始更新数据
        // 开始更新数据
        System.out.println("------------------------------------");
    
        Map<String, Object> importParams = MapUtil.of(
                "userId", params.get("userId"),
                "userName", params.get("userName"),
                "operatingType", operatingType,
                "operatorSchoolList", bindInfoList,
                "schoolIdList", unbindSchoolIdList
        );
        if (CollectionUtils.isNotEmpty(bindInfoList)) {
            bindForImport(importParams);
        }
        if (CollectionUtils.isNotEmpty(unbindSchoolIdList)) {
            unbindForImport(importParams);
        }
    
        return MapUtil.of("validatorCount", 0, "validatorList", validatorList);
    }


    /**
     * 学校管理员所负责的学校 可根据开通项筛选
     * @param params schoolSales t_employee.employee_id  顾问/销售ID
     *               [moduleConfig] t_module_config.key   foreign_type为校级 所查询的模块
     *               [schoolName] 学校名称查询
     *               [pageNo]
     *               [pageSize]
     * @return school_id
     *         school_name
     *         areaId
     *         areaCode
     *         gradeList {
     *             gradeName
     *             gradeId
     *         }
     */
    public List<Map<String, Object>> schoolList4Operator(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolSales")
                .verify();

        //获取产品顾问的学校
        params.put("relativeId", params.get("schoolSales"));
        List<Map<String, Object>> result = new ArrayList<>();
        List<Map<String, Object>> schoolList = baseDataRepository.selectList("SchoolOperatorMapper.getOperatorSchool", params);
        Map<Long, List<Map<String, Object>>> school2Grade = new HashMap<>();
        if (CollectionUtil.isNotEmpty(schoolList)) {
            school2Grade = schoolList.stream().collect(Collectors.toMap(
              x -> MapUtils.getLong(x, "schoolId"), x -> MapUtil.getListMap(x, "gradeList")));
        }
        //还没有年级等基础数据的学校，目前用于异步联考
        List<Map<String, Object>> schools = baseDataRepository.selectList("SchoolOperatorMapper.getOperatorSchoolWithoutGrade", params);
        if(CollectionUtils.isNotEmpty(schools)){
            List<Map<String, Object>> defaultGrade = Collections.singletonList(
              MapUtil.of("gradeId", -1, "gradeName", "默认年级", "stage", -1));
            for(Map<String, Object> school : schools){
                school.put("gradeList", school2Grade.getOrDefault(MapUtils.getLong(school, "schoolId"), defaultGrade));
            }
            result.addAll(schools);
        }
        return result;
    }
}
