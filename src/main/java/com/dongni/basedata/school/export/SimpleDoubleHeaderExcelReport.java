package com.dongni.basedata.school.export;

import com.dongni.common.report.excel.*;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by Heweipo on 2017/1/4.
 * <p>
 * 简单报表导出类，所谓简单报表是指：表头表体没有过多的转换
 * 这个类是 ExcelReport 的子类
 * 本身这个类应该要对结果进行分页处理，但是由于数据量不大暂且不需要
 */
public class SimpleDoubleHeaderExcelReport extends ExcelReport {

    /**
     * 报表头名称以及映射关系,因为每个字段的映射是一个Map，因此可以扩展
     */
    protected SimpleExcelHeader headers;

    /**
     * 报表体数据
     */
    protected List<Map<String, Object>> rs;

    protected String header1;
    protected Integer rang;
    protected String[] dropdownOptions;


    /**
     * 简单文件导出构造
     *
     * @param rs      报表体数据 [{"name":"张三","sex":"男"}]
     * @param headers 报表头名称以及映射关系 [{"field":"name","label":"姓名"},{"field":"sex","label":"性别"}]
     */
    public SimpleDoubleHeaderExcelReport(List<Map<String, Object>> rs, SimpleExcelHeader headers, String header1, Integer rang, String[] dropdownOptions, ExcelVersionEnum... version) {
        super((ObjectUtil.isNotBlank(version) && version.length == 1) ? version[0] : ExcelVersionEnum.V_2003);
        this.headers = headers;
        this.rs = rs;
        this.header1 = header1;
        this.rang = rang;
        this.dropdownOptions = dropdownOptions;
    }

    public void create(String file) {
        // 生成文件
        ExcelReport report = this;
        Sheet myself = report.createSheet(file);

        // 文件头
        CellStyle style = ExcelUtil.createHeaderStyle(report);
        CellStyle style1 = ExcelUtil.createHeaderStyle(report);
        style1.setAlignment(HorizontalAlignment.LEFT);
        style1.setVerticalAlignment(VerticalAlignment.TOP);
        style1.setWrapText(true);
        report.generateHeader(sheet -> {
            Row row1 = ExcelUtil.createRow(sheet, 0);
            row1.setHeight((short) 2400);
            ExcelUtil.createCell(row1, 0, header1, style1);
            ExcelUtil.addMergedRegionUnsafe(sheet, 0, 0, 0, rang);
            Row row = ExcelUtil.createRow(sheet, 1);
            int i = 0;
            for (String label : headers.labels) {
                ExcelUtil.createCell(row, i, label, style);
                i++;
            }
        });

        // 文件体
        CellStyle bodyStyle = ExcelUtil.createBodyStyle(report);
        CreationHelper createHelper = myself.getWorkbook().getCreationHelper();
        bodyStyle.setDataFormat(createHelper.createDataFormat().getFormat("@"));
        CellStyle rateStyle = ExcelStyle.getRateStyle(myself.getWorkbook());
        rateStyle.setDataFormat(createHelper.createDataFormat().getFormat("@"));
        report.generateBody(sheet -> {
            Row row;
            for (int i = 0; i < rs.size(); i++) {
                row = ExcelUtil.createRow(sheet, i + 2);
                int j = 0;
                for (String field : headers.fields) {
                    if (SimpleExcelHeader.HEADERS_INDEX.equals(field)) {
                        ExcelUtil.createCell(row, j, i + 1, bodyStyle);
                    } else if (field.toLowerCase().endsWith("rate")) {
                        ExcelUtil.createCell(row, j, ExcelFormatUtil.forMatRate(rs.get(i).get(field)), rateStyle);
                    } else {
                        ExcelUtil.createCell(row, j, rs.get(i).get(field), bodyStyle);
                    }
                    j++;
                }
            }
            if (dropdownOptions != null && dropdownOptions.length > 0) {
                DataValidationHelper validationHelper = sheet.getDataValidationHelper();
                DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(dropdownOptions);

                // 设置下拉框应用的范围，从第三行开始到最后一行
                int firstRow = 2; // Excel行索引从0开始，所以第三行是2
                int lastRow = rs.size() + 2; // 假设我们想要设置100行，可以根据需要调整
                int firstCol = 2; // 第三列（C列）的索引是2
                int lastCol = 2; // 只设置第三列
                CellRangeAddressList addressList = new CellRangeAddressList(firstRow, lastRow, firstCol, lastCol);
                DataValidation validation = validationHelper.createValidation(constraint, addressList);
                // 设置下拉框的显示属性
                validation.setShowErrorBox(true);
                // 将验证添加到工作表
                sheet.addValidationData(validation);
            }
        });

        // 调整列宽
        // 取消自动调整 myself.autoSizeColumn(i);
        myself.setDefaultColumnWidth(15);
    }

    @Override
    public String exportToFileStorage(String fileName) {
        create(fileName);
        return super.exportToFileStorage(fileName);
    }

    @Override
    public String exportToFileStorage(String fileName, boolean isRandomName) {
        create(fileName);
        return super.exportToFileStorage(fileName, isRandomName);
    }

    @Override
    public File exportToLocalPath(String fileName, String path) {
        create(fileName);
        return super.exportToLocalPath(fileName, path);
    }
}
