package com.dongni.basedata.school.batch.validator;

import com.dongni.basedata.school.batch.bean.TeacherExcelDTO;
import com.dongni.basedata.school.teacher.service.impl.TeacherServiceImpl;
import com.dongni.common.report.excel.read.bean.ExcelValidationResult;
import com.dongni.common.report.excel.read.validator.AbstractExcelValidator;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.SpecialCharUtils;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName TeacherExcelValidator
 * @Description 老师数据导入校验器
 * <AUTHOR>
 * @Date 2022/5/18 17:22
 * @Version 1.0.0
 */
@Component
public class BatchTeacherExcelValidator extends AbstractExcelValidator<TeacherExcelDTO> {

    @Autowired
    private TeacherServiceImpl teacherService;


    @Override
    protected Integer excelSheetType() {
        return DictUtil.getDictValue("excelSheetType", "batchTeacher");
    }

    @Override
    protected void bizValidate(List<ExcelValidationResult<TeacherExcelDTO>> totalList, Map<String, Object> params) {

        long schoolId = MapUtil.getLong(params, "schoolId");

        List<Map<String, Object>> teacherLogicList = totalList.stream()
                .map(item -> {
                    Map<String, Object> teacherLogicParams = new HashMap<>();
                    teacherLogicParams.put("teacherPhoneAes", SensitiveInfoUtil.aesEncrypt(item.getData().getTeacherPhone()));
                    return teacherLogicParams;
                })
                .collect(Collectors.toList());

        // 通过手机号和schoolId 查询教师信息
        List<Map<String, Object>> teacherList = teacherService.getTeacherByLogic(MapUtil.of("list", teacherLogicList, "schoolId", schoolId));
        Map<String, Map<String, Object>> teacherPhoneAesMap = teacherList.stream()
                .collect(Collectors.toMap(item -> MapUtil.getString(item, "teacherPhoneAes"), item -> item));


        if (CollectionUtils.isNotEmpty(totalList)) {

            Map<String, List<ExcelValidationResult<TeacherExcelDTO>>> excelTeacherPhoneGroupMap = totalList
                    .stream()
                    .collect(Collectors.groupingBy(item -> SensitiveInfoUtil.aesEncrypt(item.getData().getTeacherPhone())));
            excelTeacherPhoneGroupMap.forEach((teacherPhoneAes, list) -> {
                // 校验excel内手机号重复
                if (list.size() > 1) {
                    List<String> repeatRowNumList = list.stream().map(item -> item.getData().getRowNum()).collect(Collectors.toList());
                    list.forEach(item -> {
                        item.appendErrorMessage("excel编号：第" + repeatRowNumList + "行老师手机号重复");
                    });
                }

                // 校验教师名称是否一致
                Map<String, Object> teacherInfo2Mysql = teacherPhoneAesMap.get(teacherPhoneAes);
                if (MapUtils.isNotEmpty(teacherInfo2Mysql)) {
                    String teacherName2Mysql = MapUtils.getString(teacherInfo2Mysql, "teacherName");
                    list.forEach(item -> {
                        String teacherName2Excel = item.getData().getTeacherName();
                        if (!ObjectUtil.isValueEquals(teacherName2Mysql, teacherName2Excel)) {
                            item.appendErrorMessage(String.format("手机号所属教师与导入教师姓名不一致，导入教师名称：%s, 手机号所属教师名称: %s", teacherName2Excel, teacherName2Mysql));
                        }

                        if (ObjectUtil.isValueEquals(teacherName2Mysql, teacherName2Excel)) {
                            item.appendErrorMessage("教师已存在，无需导入");
                        }
                    });
                }
            });


            // 校验excel内身份证号重复
            Map<String, List<ExcelValidationResult<TeacherExcelDTO>>> excelIdentityCardNoGroupMap = totalList
                    .stream()
                    .filter(item -> ObjectUtil.isNotBlank(item.getData().getIdentityCardNo()))
                    .collect(Collectors.groupingBy(item -> SensitiveInfoUtil.aesEncrypt(item.getData().getIdentityCardNo().toUpperCase())));
            excelIdentityCardNoGroupMap.forEach((identityCardNoAes, list) -> {
                if (list.size() > 1) {
                    List<String> repeatRowNumList = list.stream().map(item -> item.getData().getRowNum()).collect(Collectors.toList());
                    list.forEach(item -> {
                        item.appendErrorMessage("excel编号：第" + repeatRowNumList + "行老师身份证号重复");
                    });
                }
            });

            excelTeacherPhoneGroupMap.clear();
        }
    }


    @Override
    protected void excelItemSpecialFiledValidate(ExcelValidationResult<TeacherExcelDTO> result) {
        String teacherName = result.getData().getTeacherName();
        // 非null判断，防止单元格为空时出现NPE
        if (teacherName != null && SpecialCharUtils.containSpecialCharacters(teacherName)) {
            result.appendErrorMessage("教师名称包含特殊字符串");
        }

        String identityCardNo = result.getData().getIdentityCardNo();
        if (ObjectUtil.isNotBlank(identityCardNo)) {
            if (identityCardNo.length() > 20) {
                result.appendErrorMessage("教师身份证位数大于20");
            }
        }
    }
}
