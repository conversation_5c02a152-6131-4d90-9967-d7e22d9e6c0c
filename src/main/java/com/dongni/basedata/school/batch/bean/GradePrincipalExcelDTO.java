package com.dongni.basedata.school.batch.bean;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.dongni.common.report.excel.annotations.ExcelColumn;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;

/**
 * @ClassName GradePrincipalExcelDTO
 * @Description 年级负责人导入模板实体类
 * <AUTHOR>
 * @Date 2022/5/19 14:27
 * @Version 1.0.0
 */
public class GradePrincipalExcelDTO extends BaseDataExcelDTO {
    @ExcelProperty("序号")
    private Integer index;

    @ExcelProperty("年级名称")
    @NotBlank(message = "年级名称为空")
    @Length(message = "负责人姓名不能超过20位")
    private String gradeName;

    @ExcelProperty("负责人类型")
    @NotBlank(message = "负责人类型为空")
    private String chiefType;

    @ExcelProperty("负责课程")
    private String courseName;

    @ExcelProperty("负责人姓名")
    @NotBlank(message = "负责人姓名为空")
    private String directorName;

    @ExcelProperty("负责人账号")
    @NotBlank(message = "负责人账号为空")
//    @Pattern(regexp = "^[1][3456789][0-9]{9}$", message = "手机号码格式错误")
    private String directorPhone;

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public String getChiefType() {
        return chiefType;
    }

    public void setChiefType(String chiefType) {
        this.chiefType = chiefType;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getDirectorName() {
        return directorName;
    }

    public void setDirectorName(String directorName) {
        this.directorName = directorName;
    }

    public String getDirectorPhone() {
        return directorPhone;
    }

    public void setDirectorPhone(String directorPhone) {
        this.directorPhone = directorPhone;
    }

    @Override
    public String toString() {
        return "GradePrincipalExcelDTO{" +
                "index=" + index +
                ", gradeName='" + gradeName + '\'' +
                ", chiefType='" + chiefType + '\'' +
                ", courseName='" + courseName + '\'' +
                ", directorName='" + directorName + '\'' +
                ", directorPhone='" + directorPhone + '\'' +
                '}';
    }

    public static List<GradePrincipalExcelDTO> convert(List<Document> documentList) {
        if (CollectionUtils.isEmpty(documentList)) {
            return Lists.newArrayList();
        }
        List<GradePrincipalExcelDTO> gradePrincipalExcelDTOList = Lists.newArrayList();
        documentList.forEach(item -> {
            Map<String, Object> data = MapUtil.getMap(item, "data");
            GradePrincipalExcelDTO gradePrincipalExcelDTO = new GradePrincipalExcelDTO();
            BeanUtil.fillBeanWithMapIgnoreCase(data, gradePrincipalExcelDTO,true);
            gradePrincipalExcelDTOList.add(gradePrincipalExcelDTO);
        });

        return gradePrincipalExcelDTOList;
    }

    public static boolean isNullable(GradePrincipalExcelDTO gradePrincipalExcelDTO) {
        if (gradePrincipalExcelDTO.getIndex() == null
                && gradePrincipalExcelDTO.getGradeName() == null
                && gradePrincipalExcelDTO.getChiefType() == null
                && gradePrincipalExcelDTO.getCourseName() == null
                && gradePrincipalExcelDTO.getDirectorName() == null
                && gradePrincipalExcelDTO.getDirectorPhone() == null) {
            return true;
        }
        return false;
    }

}
