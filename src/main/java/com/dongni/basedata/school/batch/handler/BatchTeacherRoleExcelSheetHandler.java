package com.dongni.basedata.school.batch.handler;


import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.school.batch.bean.BaseDataExcelDTO;
import com.dongni.basedata.school.batch.util.BaseDataImportUtil;
import com.dongni.basedata.school.teacher.service.IdentityCardService;
import com.dongni.basedata.school.teacher.service.impl.TeacherServiceImpl;
import com.dongni.basedata.system.account.service.IAccountService;
import com.dongni.basedata.system.account.service.IUserService;
import com.dongni.basedata.system.account.service.impl.AccountServiceImpl;
import com.dongni.common.report.excel.read.handler.ExcelSheetHandler;
import com.dongni.common.server.CurrentServerUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>教师批量导入处理类</p>
 *
 * <AUTHOR>
 * @since 2022/5/24 15:02
 */
@Service
public class BatchTeacherRoleExcelSheetHandler extends ExcelSheetHandler {

    @Autowired
    private TeacherServiceImpl teacherService;
    @Autowired
    private IdentityCardService identityCardService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private IUserService userService;
    @Autowired
    private BaseDataRepository commonRepository;

    @Override
    @SuppressWarnings("unchecked")
    public <T> void fillingCustomParameter(List<T> dataList, Map<String, Object> params) {
        BaseDataImportUtil.fillingCustomParameter((List<? extends BaseDataExcelDTO>) dataList, params);
    }

    @Override
    protected List<Map<String, Object>> fillingData(List<Map<String, Object>> dataList, Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();

        if (CollectionUtils.isEmpty(dataList)) {
            return Lists.newArrayList();
        }


        List<Map<String, Object>> schoolCourseList = teacherService.getSchoolCourse(params);
        Map<String, Map<String, Object>> courseIdToCourseMap = new HashMap<>();
        Map<Integer, Map<String, Map<String, Object>>> schoolCourseStageMap = new HashMap<>();
        for (Map<String, Object> schoolCourse : schoolCourseList) {
            Map<String, Map<String, Object>> schoolCourseMap = schoolCourseStageMap.computeIfAbsent(MapUtil.getInt(schoolCourse, "stage"), k -> new HashMap<>());
            schoolCourseMap.put(schoolCourse.get("courseName").toString(), schoolCourse);
            courseIdToCourseMap.put(schoolCourse.get("courseId").toString(), schoolCourse);
        }
        List<Map<String, Object>> gradeList = commonRepository.selectList("GradeMapper.getGradeBySchoolId", params);
        Map<String, Map<String, Object>> gradeMap = new HashMap<>();
        if (gradeList != null) {
            for (Map<String, Object> grade : gradeList) {
                gradeMap.put(grade.get("gradeName").toString(), grade);
            }
        }

        List<Map<String, Object>> classList = commonRepository.selectList("ClassesMapper.getClassBySchoolId", params);
        Map<Long, Map<String, Map<String, Object>>> gradeIdToClassMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(classList)) {
            for (Map<String, Object> classDetail : classList) {
                gradeIdToClassMap.computeIfAbsent(MapUtil.getLong(classDetail, "gradeId"), k -> new HashMap<>()).put(classDetail.get("className").toString(), classDetail);
            }
        }

        List<Map<String, Object>> accountList = commonRepository.selectList("TeacherMapper.getTeacherRoleBySchoolId", params);
        Map<String, String> accountNameMap = new HashMap<>();
        List<Long> accountIds = new ArrayList<>();
        if (accountList != null) {
            for (Map<String, Object> accountDetail : accountList) {
                accountNameMap.put(accountDetail.get("accountNameAes").toString(), accountDetail.get("accountId").toString());
                accountIds.add(MapUtil.getLong(accountDetail, "accountId"));
            }
        }
        Map<Long, String> accountIdToName = new HashMap<>();
        params.put("accountIds", accountIds);
        List<Map<String, Object>> schoolPrincipalList = commonRepository.selectList("SchoolPrincipalMapper.getPrincipalBySchoolId", params);
        if (schoolPrincipalList != null) {
            for (Map<String, Object> schoolPricipal : schoolPrincipalList) {
                accountIdToName.put(MapUtil.getLong(schoolPricipal, "accountId"), schoolPricipal.get("principalName").toString());
            }
        }
        List<Map<String, Object>> teachers = commonRepository.selectList("TeacherMapper.getTeacherAccountBySchoolId", params);
        if (teachers != null) {
            for (Map<String, Object> teacher : teachers) {
                accountIdToName.put(MapUtil.getLong(teacher, "accountId"), teacher.get("teacherName").toString());
            }
        }
        Map<String, Map<String, Object>> teacherMap = new HashMap<>();

        for (Map<String, Object> teacherDocument : dataList) {

            Map<String, Object> teacherItem = new HashMap<>(teacherDocument);

            String teacherPhone = MapUtil.getString(teacherItem, "teacherPhone");


            String userTypeName = MapUtil.getStringNullable(teacherItem.get("userTypeName"));
            String teacherName = teacherItem.get("teacherName") + "";
            if (StringUtils.isBlank(userTypeName)) {
//                    validator.add(row + "职务未填写");
                Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
            } else {
                if (userTypeName.equals("校长")) {
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    Map<String, Object> role = new HashMap<>();
                    role.put("userType", 9);
                    addRole(teacher, role);
                } else if (userTypeName.equals("教导主任")) {
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    Map<String, Object> role = new HashMap<>();
                    role.put("userType", 10);
                    addRole(teacher, role);
                } else if (userTypeName.equals("教务")) {
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    Map<String, Object> role = new HashMap<>();
                    role.put("userType", 15);
                    addRole(teacher, role);
                } else if (userTypeName.equals("教研组长")) {
                    String stateName = MapUtil.getStringNullable(teacherItem.get("stageName"));
                    if (StringUtils.isBlank(stateName)) {
                        continue;
                    }
                    String courseName = MapUtil.getStringNullable(teacherItem.get("courseName"));
                    if (StringUtils.isBlank(courseName)) {
                        continue;
                    }
                    Integer stage = stateName.equals("大学") ? 4 : stateName.equals("高中") ? 3 : stateName.equals("初中") ? 2 : 1;
                    Map<String, Map<String, Object>> schoolCourseMap = schoolCourseStageMap.get(stage);
                    if (schoolCourseMap == null) {
                        continue;
                    }
                    Map<String, Object> course = schoolCourseMap.get(courseName);
                    if (course == null) {
                        continue;
                    }
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    List<Map<String, Object>> roleList = (List<Map<String, Object>>) teacher.get("roleList");
                    Map<String, Object> role = null;
                    for (Map<String, Object> roleDetail : roleList) {
                        if (MapUtil.getInt(roleDetail, "userType") == 20) {
                            role = roleDetail;
                            break;
                        }

                    }
                    if (role == null) {
                        role = new HashMap<>();
                        role.put("userType", 20);
                        List<Map<String, Object>> courseList = new ArrayList<>();
                        courseList.add(course);
                        role.put("courseList", courseList);
                        roleList.add(role);
                    } else {
                        List<Map<String, Object>> courseList = (List<Map<String, Object>>) role.get("courseList");
                        courseList.add(course);
                    }
                } else if (userTypeName.equals("年级主任")) {
                    String stateName = (String) teacherItem.get("stageName");
                    if (StringUtils.isBlank(stateName)) {
                        continue;
                    }
                    Integer stage = stateName.equals("大学") ? 4 : stateName.equals("高中") ? 3 : stateName.equals("初中") ? 2 : 1;
                    String gradeName = (String) teacherItem.get("gradeName");
                    if (StringUtils.isBlank(gradeName)) {
                        continue;
                    }
                    Map<String, Object> grade = gradeMap.get(gradeName);
                    if (grade == null) {
                        continue;
                    }
                    int stage1 = MapUtil.getInt(grade, "stage");
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    Map<String, Object> role = new HashMap<>();
                    role.put("userType", 11);
                    role.put("gradeId", grade.get("gradeId"));
                    addRole(teacher, role);
                } else if (userTypeName.equals("备课组长")) {
                    String stateName = (String) teacherItem.get("stageName");
                    if (StringUtils.isBlank(stateName)) {
                        continue;
                    }
                    Integer stage = stateName.equals("大学") ? 4 : stateName.equals("高中") ? 3 : stateName.equals("初中") ? 2 : 1;
                    String gradeName = (String) teacherItem.get("gradeName");
                    if (StringUtils.isBlank(gradeName)) {
                        continue;
                    }
                    Map<String, Object> grade = gradeMap.get(gradeName);
                    if (grade == null) {
                        continue;
                    }
                    String courseName = (String) teacherItem.get("courseName");
                    if (StringUtils.isBlank(courseName)) {
                        continue;
                    }
                    Map<String, Map<String, Object>> schoolCourseMap = schoolCourseStageMap.get(stage);
                    if (schoolCourseMap == null) {
                        continue;
                    }
                    Map<String, Object> course = schoolCourseMap.get(courseName);
                    if (course == null) {
                        continue;
                    }
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    Map<String, Object> role = new HashMap<>();
                    role.put("userType", 12);
                    role.put("gradeId", grade.get("gradeId"));
                    role.put("courseId", course.get("courseId"));
                    role.put("courseName", course.get("courseName"));
                    addRole(teacher, role);
                } else if (userTypeName.equals("班主任")) {
                    String stateName = (String) teacherItem.get("stageName");
                    if (StringUtils.isBlank(stateName)) {
                        continue;
                    }
                    Integer stage = stateName.equals("大学") ? 4 : stateName.equals("高中") ? 3 : stateName.equals("初中") ? 2 : 1;
                    String gradeName = (String) teacherItem.get("gradeName");
                    if (StringUtils.isBlank(gradeName)) {
                        continue;
                    }
                    Map<String, Object> grade = gradeMap.get(gradeName);
                    if (grade == null) {
                        continue;
                    }
                    String className = (String) teacherItem.get("className");
                    if (StringUtils.isBlank(className)) {
                        continue;
                    }
                    className = className.replaceAll("，", ",");
                    String[] classSplit = className.split(",");
                    Map<String, Map<String, Object>> classMap = gradeIdToClassMap.get(MapUtil.getLong(grade.get("gradeId")));
                    if (classMap == null) {
                        continue;
                    }
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    Set<String> classNameSet = new HashSet<>();
                    for (String cName : classSplit) {
                        if (classNameSet.contains(cName)) {
                            continue;
                        }
                        classNameSet.add(cName);
                        Map<String, Object> classDetail = classMap.get(cName);
                        if (classDetail == null) {
                            continue;
                        }
                        Map<String, Object> role = new HashMap<>();
                        role.put("userType", 51);
                        role.put("gradeId", grade.get("gradeId"));
                        role.put("classId", classDetail.get("classId"));
                        role.put("className", cName);
                        addRole(teacher, role);
                    }
                } else if (userTypeName.equals("任课教师")) {
                    String stateName = (String) teacherItem.get("stageName");
                    if (StringUtils.isBlank(stateName)) {
                        continue;
                    }
                    Integer stage = stateName.equals("大学") ? 4 : stateName.equals("高中") ? 3 : stateName.equals("初中") ? 2 : 1;
                    String gradeName = (String) teacherItem.get("gradeName");
                    if (StringUtils.isBlank(gradeName)) {
                        continue;
                    }
                    Map<String, Object> grade = gradeMap.get(gradeName);
                    if (grade == null) {
                        continue;
                    }
                    String courseName = (String) teacherItem.get("courseName");
                    if (StringUtils.isBlank(courseName)) {
                        continue;
                    }
                    Map<String, Map<String, Object>> schoolCourseMap = schoolCourseStageMap.get(stage);
                    if (schoolCourseMap == null) {
                        continue;
                    }
                    Map<String, Object> course = schoolCourseMap.get(courseName);
                    if (course == null) {
                        continue;
                    }
                    String className = (String) teacherItem.get("className");
                    if (StringUtils.isBlank(className)) {
                        continue;
                    }
                    className = className.replaceAll("，", ",");
                    String[] classSplit = className.split(",");
                    Map<String, Map<String, Object>> classMap = gradeIdToClassMap.get(MapUtil.getLong(grade.get("gradeId")));
                    if (classMap == null) {
                        continue;
                    }
                    Map<String, Object> teacher = builderTeacher(params, teacherMap, teacherPhone, teacherName);
                    Set<String> classNameSet = new HashSet<>();
                    for (String cName : classSplit) {
                        if (classNameSet.contains(cName)) {
                            continue;
                        }
                        classNameSet.add(cName);
                        Map<String, Object> classDetail = classMap.get(cName);
                        if (classDetail == null) {
                            continue;
                        }
                        Map<String, Object> role = new HashMap<>();
                        role.put("userType", 52);
                        role.put("gradeId", grade.get("gradeId"));
                        role.put("classId", classDetail.get("classId"));
                        role.put("courseId", course.get("courseId"));
                        role.put("courseName", course.get("courseName"));
                        role.put("className", cName);
                        addRole(teacher, role);
                    }
                }

            }
        }


        return new ArrayList<>(teacherMap.values());
    }

    @Override
    public void saveData(List<Map<String, Object>> dataList, Map<String, Object> params) {

        if (dataList != null) {
            dataList.forEach(item -> {
                teacherService.insertTeacherRole(item);
            });
        }
    }

    private static Map<String, Object> builderTeacher
            (Map<String, Object> params, Map<String, Map<String, Object>> teacherMap, String teacherPhone, String
                    teacherName) {
        Map<String, Object> teacher = (Map<String, Object>) teacherMap.get(teacherPhone);
        if (teacher == null) {
            teacher = new HashMap<>(params);
            teacher.put("teacherName", teacherName);
            teacher.put("teacherPhone", teacherPhone);
            teacher.put("roleList", new ArrayList<>());
            teacherMap.put(teacherPhone, teacher);
        }
        return teacher;
    }

    private static void addRole(Map<String, Object> teacher, Map<String, Object> role) {
        List<Map<String, Object>> roleList = (List<Map<String, Object>>) teacher.get("roleList");
        roleList.add(role);
    }
}
