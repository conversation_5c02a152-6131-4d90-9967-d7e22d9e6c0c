package com.dongni.basedata.school.batch.bean;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * @ClassName StudentExcelDTO
 * @Description 学生导入模板实体类
 * <AUTHOR>
 * @Date 2022/5/18 14:08
 * @Version 1.0.0
 */
@Data
public class StudentRoleExcelDTO extends BaseDataExcelDTO {
    @ExcelProperty("序号")
    private Integer index;

    @ExcelProperty(value = "学生姓名", index = 0)
    @NotBlank(message = "学生姓名为空")
    @Length(max = 20, message = "学生姓名最长为20个字符")
    private String studentName;

    @ExcelProperty(value = "年级名称", index = 1)
    @NotBlank(message = "年级名称为空")
    private String gradeName;

    @ExcelProperty(value = "行政班级", index = 2)
    @NotBlank(message = "行政班级为空")
    private String className;

    @ExcelProperty(value = "教学班级", index = 3)
    private String teachingNames;

    @ExcelProperty(value = "学号", index = 4)
    @NotBlank(message = "学号为空")
    @Length(max = 20, message = "学号最长为40个字符")
    private String studentNum;

    @ExcelProperty(value = "默认考号", index = 5)
    private String candidate;

    @ExcelProperty(value = "外语类型", index = 6)
    private String foreignCourseName;

    @ExcelProperty(value = "选考科目", index = 7)
    private String courseSelectionGroupName;

    @ExcelProperty(value = "学生标签", index = 8)
    private String tagNames;

    @ExcelProperty(value = "学籍号", index = 9)
    @Length(max = 20, message = "学籍号最长为20个字符")
    private String studentNo;

    @ExcelProperty(value = "座位号",index = 10)
    @Length(max = 20, message = "座位号最长为20个字符")
    private String seatNumber;

    @ExcelProperty(value = "手机号",index = 11)
    private String studentPhone;


    @ExcelIgnore
    private Integer stage;


    public static List<StudentRoleExcelDTO> convert(List<Document> documentList) {
        if (CollectionUtils.isEmpty(documentList)) {
            return Lists.newArrayList();
        }
        List<StudentRoleExcelDTO> studentExcelDTOList = Lists.newArrayList();
        documentList.forEach(item -> {
            Map<String, Object> data = MapUtil.getMap(item, "data");
            StudentRoleExcelDTO studentExcelDTO = new StudentRoleExcelDTO();
            BeanUtil.fillBeanWithMapIgnoreCase(data, studentExcelDTO, true);
            studentExcelDTOList.add(studentExcelDTO);
        });

        return studentExcelDTOList;
    }

    public static boolean isNullable(StudentRoleExcelDTO studentExcelDTO) {
        if (studentExcelDTO.getIndex() == null && studentExcelDTO.getStudentName() == null && studentExcelDTO.getStudentNum() == null && studentExcelDTO.getGradeName() == null && studentExcelDTO.getClassName() == null
                && studentExcelDTO.getCourseSelectionGroupName() == null && studentExcelDTO.getForeignCourseName() == null && studentExcelDTO.getStudentPhone() == null && studentExcelDTO.getStudentNo() == null
                && studentExcelDTO.getSeatNumber() == null) {
            return true;
        }
        return false;
    }

}
