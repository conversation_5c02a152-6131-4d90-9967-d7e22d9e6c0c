package com.dongni.basedata.school.batch.bean;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 年级模板
 * @date 2022年05月13日
 */

@Data
public class GradeClassExcelDTO extends BaseDataExcelDTO {

    @ExcelProperty("序号")
    private Integer index;

    @ExcelProperty(value = "新建年级（必填）", index = 0)
    @NotBlank(message = "当前年级为空")
    private String gradeTypeName;

    @ExcelProperty(value = "年级名称", index = 1)
    private String gradeName;

    @ExcelProperty(value = "班级名称", index = 2)
    private String className;

    @ExcelProperty(value = "班级类型", index = 3)
    private String classType;

    /**
     * 文理类型
     */
    @ExcelProperty(value = "文理类型", index = 4)
    private String artsScience;


    @ExcelIgnore
    private Integer gradeType;

    @ExcelIgnore
    private Integer stage;

    @ExcelIgnore
    private Long gradeId;


    public static List<GradeClassExcelDTO> convert(List<Document> documentList) {
        if (CollectionUtils.isEmpty(documentList)) {
            return Lists.newArrayList();
        }
        List<GradeClassExcelDTO> gradeDTOList = Lists.newArrayList();
        documentList.forEach(item -> {
            Map<String, Object> data = MapUtil.getMap(item, "data");
            GradeClassExcelDTO gradeExcelDTO = new GradeClassExcelDTO();
            BeanUtil.fillBeanWithMapIgnoreCase(data, gradeExcelDTO, true);
            gradeDTOList.add(gradeExcelDTO);
        });

        return gradeDTOList;
    }

    public static boolean isNullable(GradeClassExcelDTO gradeExcelDTO) {
        if (gradeExcelDTO.getIndex() == null && gradeExcelDTO.getGradeTypeName() == null && StringUtils.isBlank(gradeExcelDTO.getGradeName()) && gradeExcelDTO.getClassName() == null) {
            return true;
        }
        return false;
    }
}
