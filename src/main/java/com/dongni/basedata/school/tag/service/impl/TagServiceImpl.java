package com.dongni.basedata.school.tag.service.impl;

import com.dongni.common.utils.ParamsUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.school.tag.service.ITagService;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Created by Heweipo on 2017/11/1.
 * <p>
 * 标签管理接口，主要是学校标签的 CURD
 */
@Service
public class TagServiceImpl implements ITagService {

    @Autowired
    private BaseDataRepository commonRepository;


    /**
     * 获取所有标签(不分页)
     *
     * @param params schoolId
     * @return 标签列表
     */
    @Override
    public List<Map<String, Object>> getTag(Map<String, Object> params) {
        // 参数校验
        if (params == null || !ObjectUtil.isValidId(params.get("schoolId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        // 数据查询
        List<Map<String, Object>> rs = commonRepository.selectList("TagMapper.getTag", params);

        return rs;
    }

    /**
     * 添加标签
     *
     * @param params schoolId tagName [tagDesc]
     * @return 标签ID
     */
    @Override
    public String insertTag(Map<String, Object> params) {
        // 参数校验
        if (params == null || !ObjectUtil.isValidId(params.get("schoolId"))
                || ObjectUtil.isBlank(params.get("tagName"))
                || !ObjectUtil.isValidUser(params)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        params.put("currentTime", DateUtil.getCurrentDateTime());

        // 查询标签是否已存在
        List<Map<String, Object>> tagList = getTag(params);
        if (CollectionUtils.isNotEmpty(tagList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "标签已存在");
        }

        // 数据操作
        commonRepository.insert("TagMapper.insertTag", params);

        return params.get("tagId").toString();
    }

    /**
     * 删除标签,注意会级联删除学生的关联关系（慎用）
     *
     * @param params tagId
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    @Override
    public void deleteTag(Map<String, Object> params) {
        // 参数校验
        if (params == null || !ObjectUtil.isValidId(params.get("tagId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        // 数据操作
        commonRepository.delete("TagMapper.deleteTagUsed", params);
        commonRepository.delete("TagMapper.deleteTag", params);
    }

    /**
     * 获取标签关联的学生总数
     *
     * @param params tagId
     * @return 总数
     */
    @Override
    public int getTagUsedCount(Map<String, Object> params) {
        // 参数校验
        if (params == null || !ObjectUtil.isValidId(params.get("tagId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        // 数据查询
        Integer count = commonRepository.selectOne("TagMapper.getTagUsedCount", params);
        count = (count == null ? 0 : count);

        return count;
    }

    /**
     * 更新标签
     *
     * @param params tagId tagName [tagDesc]
     */
    @Override
    public void updateTag(Map<String, Object> params) {
        // 参数校验
        if (params == null || !ObjectUtil.isValidId(params.get("tagId"))
                || ObjectUtil.isBlank(params.get("tagName"))
                || !ObjectUtil.isValidUser(params)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        params.put("currentTime", DateUtil.getCurrentDateTime());

        // 查询标签是否已存在
        List<Map<String, Object>> tagList = getTag(params);
        if (CollectionUtils.isNotEmpty(tagList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "标签已存在");
        }

        // 数据操作
        commonRepository.update("TagMapper.updateTag", params);
    }

    @Override
    public List<Map<String, Object>> getTagListForScoreImport(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("schoolIdList")
                .verify();
        return commonRepository.selectList("TagMapper.selectTagListForScoreImport", params);
    }
    
    @Override
    public long insertTagForDeclareTransient(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isNotBlank("tagName")
                .isNotBlank("tagDesc")
                .verify();
        Map<String, Object> tagInfo = commonRepository.selectOne("TagMapper.selectTagForDeclareTransient", params);
        if (MapUtils.isNotEmpty(tagInfo)) {
            return MapUtil.getLong(tagInfo, "tagId");
        }
        ParamsUtil.setCurrentTimeIfAbsent(params);
        commonRepository.insert("TagMapper.insertTagForDeclareTransient", params);
        return MapUtil.getLong(params, "tagId");
    }
}
