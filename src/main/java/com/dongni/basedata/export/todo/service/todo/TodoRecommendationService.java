package com.dongni.basedata.export.todo.service.todo;

import com.dongni.basedata.export.todo.service.CommonTodoService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mongodb.client.model.Filters.*;

/**
 * <p>类题推荐代办</p>
 *
 * <AUTHOR>
 * @since 2022/9/1 16:18
 */
@Service
public class TodoRecommendationService {

    @Autowired
    private CommonTodoService commonTodoService;

    /** todoType.similarRecommendation 1401 类题推荐 */
    private Integer todoType = null;

    private int getTodoType() {
        if (todoType == null) {
            todoType = DictUtil.getDictValue("todoType", "similarRecommendation");
        }
        return todoType;
    }

    public void buildTodo(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recommendationId")
                .isInteger("recommendationTaskType")
                .isValidId("courseId")
                .isNotBlank("courseName")
                .isNotBlank("stage")
                .isValidId("todoUserId")
                .isNotBlank("todoUserName")
                .isValidId("userId")
                .isNotBlank("userName")
                .isNotBlank("businessName")
                .verify();


        Map<String, Object> displayContent = new HashMap<>();
        displayContent.put("courseId", MapUtil.getLong(params, "courseId"));
        displayContent.put("courseName", MapUtil.getString(params, "courseName"));
        displayContent.put("stage", MapUtil.getInt(params, "stage"));
        displayContent.put("createDateTime", System.currentTimeMillis());
        displayContent.put("recommendationId", params.get("recommendationId"));
        displayContent.put("recommendationTaskType", params.get("recommendationTaskType"));
        if (ObjectUtil.isNotBlank(params.get("deadline"))) {
            displayContent.put("deadline", params.get("deadline"));
        }

        Map<String, Object> todoParams = MapUtil.newInstance(8);
        todoParams.put("userId", MapUtil.getLong(params, "userId"));
        todoParams.put("userName", MapUtil.getString(params, "userName"));
        todoParams.put("todoUserId", params.get("todoUserId"));
        todoParams.put("todoUserName", params.get("todoUserName"));
        todoParams.put("todoType", params.get("todoType"));
        todoParams.put("businessId", MapUtil.getLong(params, "recommendationId"));
        todoParams.put("businessName", params.get("businessName"));
        todoParams.put("displayContent", displayContent);
        commonTodoService.insertOne(todoParams);
    }

    /**
     * 标注知识点 删除待办
     * @param params markId
     */
    public void deleteTodo(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("markId")
                .verify();

        Map<String, Object> todoParams = new HashMap<>(4);
        todoParams.put("todoType", getTodoType());
        todoParams.put("businessId",  MapUtil.getLong(params, "markId"));
        commonTodoService.deleteTodoTask(todoParams);
    }

    /**
     * 删除推荐任务
     * @param params recommendationIdList
     */
    public void deleteManyTodo(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("recommendationIdList")
                .verify();

        List<Long> recommendationIdList = MapUtil.getListLong(params, "recommendationIdList");
        Bson delete = and(eq("todoType", getTodoType()), in("displayContent.recommendationId", recommendationIdList));
        commonTodoService.deleteManyTodo(delete);
    }
}
