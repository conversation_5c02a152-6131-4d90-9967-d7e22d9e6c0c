package com.dongni.basedata.export.student.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.student.bean.SeatNumberAndStudentNo;
import com.dongni.basedata.export.student.bean.StudentCourseSelectionDTO;
import com.dongni.basedata.export.student.bean.StudentIdNameDTO;
import com.dongni.basedata.export.student.bean.StudentUpdateInfoDTO;
import com.dongni.basedata.export.tag.service.CommonTagService;
import com.dongni.basedata.school.classes.bean.ClassHeaderQuery;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.batch.BatchDataUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Heweipo on 2018/11/19.
 * <p>
 * 学生公共接口
 */
@Service
public class CommonStudentService {

    @Autowired
    private BaseDataRepository commonRepository;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private CommonTagService commonTagService;


    /**
     * 通过 学校、年级、班级查询学生
     *
     * @param params schoolId [gradeId] [classId] [tagId] [search]
     * @return 学生列表
     */
    public Map<String, Object> getStudentList(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("schoolId").verify();

        Map<String, Object> rs = new HashMap<>();

        // 数据查询
        Integer totalCount = commonRepository.selectOne("CommonStudentMapper.getStudentListCount", params);
        rs.put("totalCount", totalCount);
        if (totalCount == null || totalCount == 0) {
            rs.put("student", null);
            return rs;
        }

        // 查询学生
        List<Map<String, Object>> ls = commonRepository.selectList("CommonStudentMapper.getStudentList", params);
        rs.put("student", ls);

        return rs;
    }

    /**
     * 通过学生id查询学生的可以安全一键更新的信息（学号、姓名、标签、选科信息）
     *
     * @param studentIds 学生ids
     */
    public List<StudentUpdateInfoDTO> getStudentUpdateInfoByIds(List<Long> studentIds) {
        if (studentIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<StudentUpdateInfoDTO> list = new ArrayList<>(studentIds.size());
        BatchDataUtil.execute(studentIds,
                x -> list.addAll(commonRepository.selectList("CommonStudentMapper.getStudentUpdateInfoByIds", x)), 10000);
        return list;
    }

    /**
     * 通过班级id查询学生
     *
     * @param params classIds
     * @return 学生列表, 包括考号信息
     */
    public List<Map<String, Object>> getClassStudent(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isNotBlank("classIds").verify();
        params.put("classIds", params.get("classIds").toString().split(","));
        // 数据查询

        List<Map<String, Object>> list = commonRepository.selectList("CommonStudentMapper.getClassStudent", params);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        } else {
            buiderStudentTag(list);
        }
        return list;
    }

    private void buiderStudentTag(List<Map<String, Object>> list) {
        List<Long> studentIds = list.stream().map(s -> MapUtil.getLong(s, "studentId")).collect(Collectors.toList());
        List<Map<String, Object>> studentTags = commonRepository.selectList("StudentTagMapper.getStudentTagByIds", MapUtil.of("studentIds", studentIds));
        if (CollectionUtils.isNotEmpty(studentTags)) {
            Map<Long, Map<String, String>> result = studentTags.stream()
                    // 过滤掉tagId为空的Map
                    .filter(map -> Optional.ofNullable(map.get("tagId")).map(Object::toString).map(tagId -> !tagId.isEmpty()).orElse(false))
                    .collect(Collectors.groupingBy(
                            map -> (Long) map.get("studentId"), // 分组的key是studentId
                            Collectors.mapping(
                                    map -> new AbstractMap.SimpleEntry<>(
                                            map.get("tagId").toString(),
                                            map.get("tagName").toString()
                                    ), // 将每个Map中的tagId和tagName提取出来
                                    Collectors.reducing(
                                            new AbstractMap.SimpleEntry<>(null, null),
                                            (entry1, entry2) -> new AbstractMap.SimpleEntry<>(
                                                    entry1.getKey() == null ? entry2.getKey() : entry1.getKey() + "," + entry2.getKey(),
                                                    entry1.getValue() == null ? entry2.getValue() : entry1.getValue() + "," + entry2.getValue()
                                            )
                                    )
                            )
                    )).entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                String tagIds = entry.getValue().getKey() + "";
                                String tagNames = entry.getValue().getValue() + "";
                                return new HashMap<String, String>() {{
                                    put("tagId", tagIds);
                                    put("tagName", tagNames);
                                }};
                            }
                    ));
            for (Map<String, Object> student : list) {
                Map<String, String> studentTag = result.get(MapUtil.getLong(student.get("studentId")));
                if (studentTag != null) {
                    student.put("tagId", studentTag.get("tagId"));
                    student.put("tagName", studentTag.get("tagName"));

                }
            }
        }
    }

    /**
     * 通过班级id查询学生
     *
     * @param classIds 班级ids
     * @return 学生列表，包括考号、选科信息
     */
    public List<Map<String, Object>> getClassStudentInfo(List<Long> classIds) {
        if (classIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 数据查询
        List<Map<String, Object>> list = commonRepository.selectList("CommonStudentMapper.getClassStudentInfo", classIds);
        if (list.isEmpty()) {
            return list;
        }
        buiderStudentTag(list);
        return list;
    }

    /**
     * 通过学生ids查询对应学生的考号（使用学号值填充）、选科信息
     *
     * @param studentIds 学生ids
     * @return 学生列表，包括考号（使用学号值填充）、选科信息
     */
    public List<Map<String, Object>> getStuInfoByStuIds(List<Long> studentIds) {
        if (studentIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 数据查询
        return commonRepository.selectList("CommonStudentMapper.getStuInfoByStuIds", studentIds);
    }

    /**
     * 通过学生ids查询对应学生的选科信息
     * @param studentIds 学生ids
     * @return 学生选科信息
     */
    public List<StudentCourseSelectionDTO> getStudentCourseSelectionByStuIds(List<Long> studentIds) {
        if (studentIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<StudentCourseSelectionDTO> studentList = new ArrayList<>(studentIds.size());
        BatchDataUtil.execute(studentIds, subList ->
            studentList.addAll(commonRepository.selectList("CommonStudentMapper.getStudentCourseSelectionByStuIds", subList))
        );
        return studentList;
    }

    /**
     * 通过班级id查询学生
     *
     * @param params classIds
     * @return 学生列表
     */
    public Map<String, Object> getClassStudentMap(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isNotBlank("classIds").verify();
        params.put("classIds", params.get("classIds").toString().split(","));
        // 数据查询
        Map<String, Object> rs = new HashMap<>();
        Integer count = commonRepository.selectOne("CommonStudentMapper.getClassStudentMapCount", params);
        rs.put("totalCount", count);
        if (count == 0) {
            rs.put("list", new ArrayList<>());
            return rs;
        }
        List<Map<String, Object>> list = commonRepository.selectList("CommonStudentMapper.getClassStudent", params);
        if (CollectionUtils.isNotEmpty(list)) {
            buiderStudentTag(list);
        }
        rs.put("list", list);
        return rs;
    }


    /**
     * 通过班级id查询重复的学生
     *
     * @param params classIds
     * @return 重复学生列表
     */
    public List getDuplicateClassStudent(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isNotBlank("classIds").verify();
        params.put("testClassIds", params.get("classIds").toString().split(","));
        // 数据查询
        List<Map<String, Object>> rs = commonRepository.selectList("CommonStudentMapper.getDuplicateClassStudent", params);
        if (CollectionUtils.isEmpty(rs)) {
            return Collections.emptyList();
        }
        List<String> list = new ArrayList<>();
        for (Map<String, Object> r : rs) {
            list.add("学生" + "\t" + r.get("studentName") + "存在多个班级" + "\t" + r.get("className"));
        }
        return list;
    }

    /**
     * 联考通过年级id查询重复的学生
     *
     * @param params gradeIds  removeClassIds
     * @return 重复学生列表
     */
    public List getUnionDuplicateClassStudent(Map<String, Object> params) {
        // 参数校验
        Verify.of(params)
                .isNotBlank("gradeIds")
                .verify();

        params.put("gradeIds", StringUtil.strToList(params.get("gradeIds").toString(), ",", Long.class));
        List<Map<String, Object>> gradeClassList = commonClassService.getGradeClassList(params);
        List<Long> classIdList = gradeClassList.stream().map(m -> Long.valueOf(m.get("classId").toString())).collect(Collectors.toList());
        List<Long> rsClassIdList = new ArrayList<>();
        if (!ObjectUtil.isBlank("removeClassIds")) {
            Set<Long> removeClassSet = new HashSet<>(StringUtil.strToList(params.get("removeClassIds").toString(), ",", Long.class));
            for (Long id : classIdList) {
                if (!removeClassSet.contains(id)) {
                    rsClassIdList.add(id);
                }
            }

        }
        if (rsClassIdList.size() == 0) {
            return Collections.emptyList();
        }
        params.put("classIds", StringUtils.join(rsClassIdList, ","));
        return getDuplicateClassStudent(params);
    }


    /**
     * 通过班级id查询重复的学生
     *
     * @param params classIds
     * @return 重复学生列表
     */
    public List<Map<String, Object>> getDuplicateClassCourseStudent(Map<String, Object> params) {
        // 参数校验
        Verify.of(params)
                .isNotBlank("courseIdList")
                .isNotBlank("classIdList")
                .verify();
        // 数据查询
        List<Long> courseIdList = MapUtil.getListLong(params.get("classIdList"));
        return BatchDataUtil.submit(courseIdList, ids -> {
                    Map<String, Object> queryMap = new HashMap<>(params);
                    queryMap.put("classIdList", ids);
                    return commonRepository.selectList("CommonStudentMapper.getDuplicateClassCourseStudent", queryMap);
                }
                , 500);
    }

    /**
     * 通过年级id查询重复的学生
     *
     * @param params classIds
     * @return 重复学生列表
     */
    public List<Map<String, Object>> getDuplicateGradeCourseStudent(Map<String, Object> params) {
        // 参数校验
        Verify.of(params)
                .isNotBlank("courseIdList")
                .isNotBlank("gradeIdList")
                .verify();
        // 数据查询
        return commonRepository.selectList("CommonStudentMapper.getDuplicateGradeCourseStudent", params);
    }


    /**
     * 获取考试需要基本信息
     */
    public Map<String, Object> getClassStudentCount(Map<String, Object> params) {

        Map<String, Object> result = new HashMap<>();

        if (!ObjectUtil.isBlank(params.get("classIds"))) {
            params.put("classIds", params.get("classIds").toString().split(","));
            List<Map<String, Object>> list = commonRepository.selectList("CommonStudentMapper.getClassStudentCount", params);
            Map<Long, Integer> map = list.stream().collect(Collectors.toMap(item -> Long.valueOf(item.get("classId").toString()), item -> Integer.valueOf(item.get("count").toString())));
            result.put("classIdMapping", map);
        }

        if (!ObjectUtil.isBlank(params.get("schoolIds"))) {
            params.put("schoolIds", params.get("schoolIds").toString().split(","));
            //获取学校地址
            List<Map<String, Object>> list = commonRepository.selectList("CommonAreaMapper.getSchoolAddress", params);
            Map<Long, String> map = list.stream().collect(Collectors.toMap(item -> Long.valueOf(item.get("schoolId").toString()), item -> item.get("address").toString()));
            result.put("schoolIdMapping", map);
        }

        //获取考试发布人电话
        Map<String, Object> teacher = commonRepository.selectOne("CommonTeacherMapper.getTeacherAccountName", params);
        if (MapUtils.isEmpty(teacher)) {
            result.put("teacherPhone", "暂无");
        } else {
            result.put("teacherPhone", teacher.get("teacherPhone"));
        }
        return result;
    }

    /**
     * 获取未注册学生
     *
     * @param params classIds
     * @return 未注册学生
     */
    public List<Map<String, Object>> getUnregisterStu(Map<String, Object> params) {
        Verify.of(params).isNotBlank("classIds").verify();
        String classIds = params.get("classIds").toString();
        Object studentIds = params.get("studentIds");
        if (!ObjectUtil.isBlank(params.get("tagIds"))) {
            List<Map<String, Object>> tagStudentList = commonTagService.getTagStudentList(params);
            StringBuilder sb = new StringBuilder();
            if (CollectionUtils.isNotEmpty(tagStudentList)) {
                tagStudentList.forEach(t -> sb.append(t.get("studentId").toString()).append(","));
                if (!ObjectUtil.isBlank(studentIds)) {
                    studentIds = studentIds.toString() + "," + sb.toString();
                } else {
                    studentIds = sb.toString();
                }
            }
        }
        if (!ObjectUtil.isBlank(studentIds)) {
            params.put("studentIds", studentIds.toString().split(","));
        }
        params.put("classIds", classIds.split(","));
        return commonRepository.selectList("CommonStudentMapper.getUnregisterStu", params);
    }

    /**
     * 获取classId中 且不在studentIds列表中的学生总数
     * 统计-学生学情分层使用
     *
     * @param params - classId  班级id
     *               - [studentIds [Long]] 已经分层的学生idList
     * @return 班级中不在studentIds列表中的学生
     * {studentCount : 123456}
     */
    public Map<String, Object> getStudentCountNotExist(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("classId")
                .verify();

        // studentIds 如果isEmpty则去除该参数
        if (params.get("studentIds") != null) {
            List studentIds = (List) params.get("studentIds");
            if (studentIds.isEmpty()) {
                params.remove("studentIds");
            }
        }
        return MapUtil.of(
                "studentCount", commonRepository.selectOne("CommonStudentMapper.getStudentCountNotExist", params)
        );
    }

    /**
     * 获取classId中 且不在studentIds列表中的学生信息
     * 统计-学生学情分层使用
     *
     * @param params - classId  班级id
     *               - [studentIds [Long]] 已经分层的学生idList
     *               - [search]   查询学生姓名条件
     * @return 班级中不在studentIds列表中的学生
     * [{studentId : 123456, studentName: 德玛西亚},...]
     */
    public Map<String, Object> getStudentNotExist(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("classId")
                .verify();

        // studentIds 如果isEmpty则去除该参数
        if (params.get("studentIds") != null) {
            List studentIds = (List) params.get("studentIds");
            if (studentIds.isEmpty()) {
                params.remove("studentIds");
            }
        }
        return MapUtil.of(
                "students", commonRepository.selectList("CommonStudentMapper.getStudentNotExist", params)
        );
    }


    public Map<String, Object> getStudentNotExistBySchoolId(Map<String, Object> params) {

        // studentIds 如果isEmpty则去除该参数
        if (params.get("studentIds") != null) {
            List studentIds = (List) params.get("studentIds");
            if (studentIds.isEmpty()) {
                params.remove("studentIds");
            }
        }
        return MapUtil.of(
                "students", commonRepository.selectList("CommonStudentMapper.getStudentNotExistBySchoolId", params)
        );
    }

    /**
     * 获取学生信息
     * 获取在行政班且未离班的学生信息/班级信息/年级信息
     *
     * @param params <br/>
     *               - [classId]   班级id <br/>
     *               - [studentId] 学生id <br/>
     * @return students: []
     * - classId
     * - className
     * - studentId
     * - studentName
     * - gradeId 年级id
     */
    public Map<String, Object> getStudentInfo(Map<String, Object> params) {
        return MapUtil.of("students", commonRepository.selectList("CommonStudentMapper.getStudentInfo", params));
    }

    /**
     * 学生跟踪-获取学生信息
     */
    public Map<String, Object> getAreaStudent(Map<String, Object> params) {

        Verify.of(params).isValidId("studentId").verify();
        return commonRepository.selectOne("CommonStudentMapper.getAreaStudent", params);
    }


    /**
     * 通过学生id查找相关学生简单信息
     *
     * @param params studentId
     * @return 学生信息
     */
    public Map<String, Object> getStudent(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isValidId("studentId").verify();
        // 数据查询
        return commonRepository.selectOne("CommonStudentMapper.getStudent", params);
    }

    /**
     * 通过学生id查询学生座位号
     *
     * @param params studentIds
     * @return 学生信息
     */
    public List<Map<String, Object>> getStudentSeat(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isNotBlank("studentIds").verify();
        params.put("studentIdList", StringUtil.strToList(params.get("studentIds").toString(), ",", Long.class));
        // 数据查询
        return commonRepository.selectList("CommonStudentMapper.getStudentSeatNumber", params);
    }

    /**
     * 根据学生获取家长
     *
     * @param params
     * @return
     */
    public List<Map<String, Object>> getParentByStudent(Map<String, Object> params) {
        Verify.of(params)
                .isNotNull("studentList")
                .verify();

        return commonRepository.selectList("CommonStudentMapper.getParentInfo", params);
    }

    /**
     * @Description: 通过学生id查找相关学生信息
     * @Param: studentIdList
     */
    public List getStudentInfoList(Map<String, Object> params) {
        return commonRepository.selectList("CommonStudentMapper.getStudentListById", params);
    }

    /**
     * @Description: 通过学生id查找相关学校信息信息
     * @Param: studentIdList
     */
    public List<Map<String, Object>> getStudentSchoolInfoList(Map<String, Object> params) {
        return commonRepository.selectList("CommonStudentMapper.getStudentSchoolListById", params);
    }

    /**
     * @Description: 通过学生id查找相关学生简单信息
     * @Param: studentIdList
     */
    public List getStudentSimpleInfoList(Map<String, Object> params) {
        return commonRepository.selectList("CommonStudentMapper.getStudentSimpleInfoList", params);
    }


    /**
     * @Description: 获取行政班学生班级信息
     * @Param:
     */
    public List<Map<String, Object>> getStudentClassInfoList(Map<String, Object> params) {
        return commonRepository.selectList("CommonStudentMapper.getStudentClassInfoList", params);
    }

    /**
     * @param params examStudent
     * @return 学生所在所有班
     */
    public List<Map<String, Object>> getStudentClassInfo(Map<String, Object> params) {
        return commonRepository.selectList("CommonStudentMapper.getStudentClassInfo", params);
    }

    /**
     * @param params
     * @return 班级的信息
     */
    public Map<String, Object> getClassGradeInfo(Map<String, Object> params) {
        return commonRepository.selectOne("CommonStudentMapper.getClassGradeInfo", params);
    }

    /**
     * @Description: 通过学生id查找相关学生信息(包括账号)
     * @Param: studentId
     */
    public Map<String, Object> getStudentInfoByList(Map<String, Object> params) {
        return commonRepository.selectOne("CommonStudentMapper.getStudentInfoByStudentId", params);
    }

    /**
     * @Description: 通过家长id查找相关学生id
     * @Param: parentStudentId
     */
    public List getStudentIdByParentId(Map<String, Object> params) {
        return commonRepository.selectList("CommonStudentMapper.getStudentByParentId", params);
    }

    /**
     * 根据学生id查询schoolId
     *
     * @param studentId 学生id
     * @return schoolId or null
     */
    public Long getStudentSchoolId(Long studentId) {
        return commonRepository.selectOne("CommonStudentMapper.getStudentSchoolId", studentId);
    }

    /**
     * 获取学生座位号和学籍号
     *
     * @param studentIds 待查询的学生id列表
     * @return 学生座位号和学籍号
     */
    public List<SeatNumberAndStudentNo> getSeatNumberAndStudentNo(List<Long> studentIds) {
        if (CollectionUtils.isEmpty(studentIds)) {
            return Collections.emptyList();
        }

        List<SeatNumberAndStudentNo> result = new ArrayList<>();

        BatchDataUtil.execute(studentIds, subList -> {
            List<SeatNumberAndStudentNo> items = commonRepository
                    .selectList("CommonStudentMapper.getSeatNumberAndStudentNo", subList);
            if (CollectionUtils.isNotEmpty(items)) {
                result.addAll(items);
            }
        });

        return result;
    }

    /**
     * 根据班级ids获取学生ids
     * @param classIds 班级ids
     */
    public List<Long> getStudentIdsByClassIds(List<Long> classIds) {
        if (classIds.isEmpty()) {
            return Collections.emptyList();
        }
        return commonRepository.selectList("StudentMapper.getStudentIdsByClassIds", MapUtil.of("classIds", classIds));
    }

    /**
     * 根据学生ids获取学生姓名
     */
    public List<StudentIdNameDTO> getStudentNameByStudentIds(List<Long> studentIds) {
        if (studentIds.isEmpty()) {
            return Collections.emptyList();
        }
        return commonRepository.selectList("StudentMapper.getStudentNameByStuIds", studentIds);
    }

    /**
     * 根据thirdPartyId、学生ids获取其对应的thirdBizId
     */
    public List<Map<String, Object>> getStudentThirdInfoByStuIds(Long thirdPartyId, List<Long> studentIds) {
        if (studentIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> studentThirdInfoList = new ArrayList<>(studentIds.size());
        Map<String, Object> params = new HashMap<>(2);
        params.put("thirdPartyId", thirdPartyId);
        BatchDataUtil.execute(studentIds, subList -> {
              params.put("studentIds", subList);
              studentThirdInfoList.addAll(commonRepository.selectList("StudentMapper.selectStudentThirdInfoByStuIds", params));
          }
        );
        return studentThirdInfoList;
    }
}
