package com.dongni.basedata.export.area.service;

import com.dongni.basedata.admin.service.IBaseAreaService;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.system.account.util.AreaTreeUtil;
import com.dongni.common.utils.PathUtils;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.TreeUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.plan.service.ExamCourseCombineService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2018/11/26 21:05
 */
@Service
public class CommonAreaService {

    @Autowired
    private BaseDataRepository commonRepository;
    @Autowired
    private CommonAreaService commonAreaService;
    @Autowired
    private IBaseAreaService baseAreaService;
    @Autowired
    private ExamCourseCombineService examCourseCombineService;

    /**
     * 获取当前区域及所有子区域
     *
     * @param params areaId
     * @return
     */
    public List<Map<String, Object>> getAreaChild(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();

        return commonRepository.selectList("CommonAreaMapper.selectAreaChild", params);

    }

    /**
     * 根据ID获取区域
     *
     * @param params
     * @return
     */
    public Map<String, Object> getArea(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();

        return commonRepository.selectOne("CommonAreaMapper.getArea", params);
    }

    /**
     * 获取所有区域
     *
     * @return
     */
    public List<Map<String, Object>> getAllArea() {
        return commonRepository.selectList("CommonAreaMapper.getAllArea");
    }

    /**
     * 区域及所有节点
     *
     * @param params areaCode
     * @return 区域及所有节点
     */
    public List<Map<String, Object>> getAreaByAreaCode(Map<String, Object> params) {
        Verify.of(params).isNotBlank("areaCode").verify();
        return commonRepository.selectList("CommonAreaMapper.getAreaByAreaCode", params);
    }

    /**
     * 区域及所有节点
     *
     * @param params areaId
     * @return 区域及所有节点
     */
    public List<Map<String, Object>> getAreaByAreaId(Map<String, Object> params) {
        Verify.of(params).isNotBlank("areaId").verify();
        return commonRepository.selectList("CommonAreaMapper.getAreaByAreaId", params);
    }

    /**
     * 查询区域下所有学校
     *
     * @return 学校信息
     */
    public List<Map<String, Object>> getAreaSchool(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("areaCode")
                .isNumeric("stage")
                .verify();
        return commonRepository.selectList("CommonAreaMapper.getAreaSchool", params);
    }

    /**
     * 查询区域下所有学校
     *
     * @return 学校信息
     */
    public Map<String, Object> getAreaSchoolGradeNotStage(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("areaIds")
                .verify();
        Map<String, Object> rs = new HashMap<>();
        Integer totalCount= commonRepository.selectOne("CommonAreaMapper.getAreaSchoolGradeNotStageCount", params);
        rs.put("totalCount", totalCount);
        if (totalCount == null || totalCount == 0){
            rs.put("schoolGradeInfo", Collections.emptyList());
            return rs;
        }
        List<Map<String,Object>> schoolGradeInfoList = commonRepository
                .selectList("CommonAreaMapper.getAreaSchoolGradeNotStage", params);
        schoolGradeInfoList.forEach(item -> item.put("sellType",null));
        //二期处理该需求
        rs.put("schoolGradeInfo", schoolGradeInfoList);
        return rs;
    }

    /**
     * 查询学校上级所有区域并返回区域树
     *
     * @return 区域及学校数据
     */
    public List<Map<String, Object>> getAreaAllSchool(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("school")
                .verify();
        List<Map<String, Object>> school = (List<Map<String, Object>>) params.get("school");
        //区域树   通过areaId关联到区域树
        Set<String> areaId = new HashSet();
        school.forEach(s -> areaId.add(s.get("areaId").toString()));
        //通过考试学校的区域id,找到最低层的区域数据
        params.put("areaId", areaId);
        List<Map<String, Object>> area = commonAreaService.getAreaByAreaId(params);
        //通过areaCode split 查找所有节点数据
        Set<String> areaCode = new HashSet();
        area.forEach(a -> {
            areaCode.add(a.get("areaCode").toString());
            String[] split = a.get("areaCode").toString().split(",");
            for (int i = 0; i < split.length; i++) {
                StringBuilder s = new StringBuilder(split[0]);
                if (i != 0) {
                    for (int z = 1; z < i; z++) {
                        s.append(",").append(split[z]);
                    }
                }
                areaCode.add(s.toString());
            }

        });
        params.put("areaCode", areaCode);
        List<Map<String, Object>> allArea = commonAreaService.getAreaByAreaCode(params);

        Map<Long, List<Map<String, Object>>> areaSchool = school.stream().collect(groupingBy(s -> Long.valueOf(s.get("areaId").toString())));
        for (Map<String, Object> r : allArea) {
            List<Map<String, Object>> list = areaSchool.get(Long.valueOf(r.get("areaId").toString()));
            r.put("school", list);
        }

        return TreeUtil.list2Tree(allArea, "areaId", "parentAreaId", "child");
    }

    /**
     * 查询区域下班级
     *
     * @return 班级信息
     */
    public Map<String, Object> getAreaClass(Map<String, Object> params) {

        Map<String, Object> rs = new HashMap<>();

        List<Long> schoolIds = new ArrayList<>();
        if (ObjectUtil.isValidId(params.get("schoolId"))) {
            schoolIds.add(Long.valueOf(params.get("schoolId").toString()));
        } else {
            List<Map<String, Object>> areaSchool = getAreaSchool(params);
            schoolIds = areaSchool.stream().map(m -> Long.valueOf(m.get("schoolId").toString())).collect(Collectors.toList());
        }

        params.put("schoolIds", schoolIds);
        int count = commonRepository.selectOne("CommonAreaMapper.getAreaClassCount", params);
        if (count == 0) {
            rs.put("totalCount", 0);
            rs.put("classList", Collections.emptyList());
            return rs;
        }

        List<Map<String, Object>> classList = commonRepository.selectList("CommonAreaMapper.getAreaClass", params);
        rs.put("classList", classList);
        rs.put("totalCount", count);
        return rs;
    }

    /**
     * 获取区域下所有节点（包含学校）
     *
     * @param params areaCode
     * @return 所有节点（包含学校）
     */
    public Map<String, Object> getAreaTree(Map<String, Object> params) {
        Verify.of(params).isNotBlank("areaCode").isNumeric("stage").verify();
        //当前区域及所有子区域
        List<Map<String, Object>> area = commonRepository.selectList("CommonAreaMapper.getAllAreaByAreaCode", params);
        params.put("area", area);
        //区域下的所有学校
        List<Map<String, Object>> school = commonRepository.selectList("CommonAreaMapper.getSchoolByArea", params);
        return AreaTreeUtil.assemblyAreaTree(area, school);
    }

    /**
     * 获取区域的最小共同父亲结点
     *
     * @param params areaId
     * @return
     */
    public Map<String, Object> getLastParentArea(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("areaId")
                .verify();

        // 查询所有区域
        List<Map<String, Object>> areaList = commonRepository.selectList("CommonAreaMapper.getAreaByAreaId", params);

        if (CollectionUtils.isEmpty(areaList)) {
            return null;
        }

        if (areaList.size() == 1) {
            return areaList.get(0);
        }

        List<List<String>> parentAreaCodeList = new ArrayList<>();

        // 获取所有父亲结点
        int size = Integer.MAX_VALUE;
        for (Map<String, Object> area : areaList) {
            List<String> parentAreaCode = getParentAreaCode(area);
            parentAreaCodeList.add(parentAreaCode);
            size = Integer.min(size, parentAreaCode.size());
        }

        if (size == Integer.MAX_VALUE) {
            return null;
        }

        // 比较areaCode，取出最小的相同areaCode
        String resultAreaCode = null;
        for (int i = 0; i < size; i++) {
            Set<String> areaCodeSet = new HashSet<>();
            for (int j = 0; j < parentAreaCodeList.size(); j++) {
                String areaCode = parentAreaCodeList.get(j).get(i);
                areaCodeSet.add(areaCode);
            }

            // 取areaCode相同的
            if (areaCodeSet.size() == 0) {
                resultAreaCode = areaCodeSet.iterator().next();
            }
        }

        if (resultAreaCode == null) {
            return null;
        }

        params.put("areaCode", resultAreaCode);
        List<Map<String, Object>> result = commonRepository.selectList("CommonAreaMapper.getAreaByAreaCode", params);

        return result.get(0);

    }

    /**
     * 获取父亲区域（包括本身）
     *
     * @param params areaId
     * @return
     */
    public List<Map<String, Object>> getParentArea(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();

        // 查询所有区域
        params.put("areaId", Collections.singletonList(Long.valueOf(params.get("areaId").toString())));
        List<Map<String, Object>> areaList = commonRepository.selectList("CommonAreaMapper.getAreaByAreaId", params);

        if (CollectionUtils.isEmpty(areaList)) {
            return Collections.emptyList();
        }

        List<String> parentAreaCodeList = getParentAreaCode(areaList.get(0));
        params.put("areaCode", parentAreaCodeList);

        return commonRepository.selectList("CommonAreaMapper.getAreaByAreaCode", params);
    }

    /**
     * 根据areaCode获取所有父亲areaCode（包括本身），areaCode从上往下排序
     *
     * @param params
     * @return
     */
    private List<String> getParentAreaCode(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("areaCode")
                .verify();

        String areaCode = params.get("areaCode").toString();
        List<String> codeList = Arrays.asList(areaCode.split(","));

        List<String> result = new ArrayList<>();
        for (int i = 0; i < codeList.size(); i++) {
            result.add(String.join(",", codeList.subList(0, i + 1)));
        }
        // 添加全国
        result.add("10");

        return result;
    }

    /**
     * 查询区域树
     *
     * @return 区域树
     */
    public List<Map<String, Object>> getCommonAreaTree(Map<String, Object> params) {
        // 设置区域的区域全称
        List<Map<String, Object>> areas = commonRepository.selectList("CommonAreaMapper.selectAreas", params);
        return TreeUtil.list2Tree(areas, "areaId", "parentAreaId", "child");
    }

    /**
     * 查询区域
     *
     * @return 区域
     */
    public List<Map<String, Object>> getCommonAreaList(Map<String, Object> params) {
        Verify.of(params).isNotBlank("areaIds").verify();
        params.put("areaIds",params.get("areaIds").toString().split(","));
        return commonRepository.selectList("CommonAreaMapper.getCommonAreaList", params);
    }

    /**
     * 获取当前区域及所有子区域
     *
     * @param params areaId
     * @return
     */
    public List<Map<String, Object>> getAreaChildTree(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId");
        Map<String, Object> area = getArea(params);
        List<Map<String, Object>> areas = commonRepository.selectList("CommonAreaMapper.getAllAreaByAreaCode", area);
        params.put("area",areas);
        List<Map<String, Object>> areasSchool = commonRepository.selectList("CommonAreaMapper.getAreaSchoolList", params);
        List<Map<String, Object>> list = TreeUtil.list2Tree(areas, "areaId", "parentAreaId", "child");
        computeSchoolCount(areas,areasSchool);
        return list;
    }

    /**
     * 获取区域所属省份
     * @param params areaId
     */
    public Map<String, Object> getAreaProvince(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("areaId")
          .verify();

        Map<String, Object> area = baseAreaService.getArea(params);
        if (MapUtils.isEmpty(area)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "区域数据有误，请联系管理员处理");
        }
        String[] areaCode = area.get("areaCode").toString().split(",");
        //取省份的areaCode
        params.put("areaCode", areaCode[0]);
        Map<String, Object> provinceArea = baseAreaService.getAreaId(params);
        if (MapUtils.isEmpty(provinceArea)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "区域所属省份数据有误，请联系管理员处理");
        }
        return provinceArea;
    }

    /**
     * 获取区域是否有有效的新高考赋分配置
     * @param params areaId
     */
    public Boolean checkGradeScoreChangeEnable(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("areaId")
          .verify();

        Map<String, Object> provinceArea = getAreaProvince(params);
        //根据省份能获取到赋分配置 && 根据省份能获取到赋分课程
        provinceArea.put("scoreLevelType", 1);
        return CollectionUtils.isNotEmpty(examCourseCombineService.getScoreLevelChange(provinceArea)) &&
          CollectionUtils.isNotEmpty(examCourseCombineService.getScoreLevelChangeCourse(provinceArea));
    }

    private void computeSchoolCount(List<Map<String,Object>> areas,List<Map<String,Object>> school){
        if(CollectionUtils.isEmpty(school))return;
        for(Map<String,Object> map : areas){
            long count = school.stream().filter(s -> s.get("areaCode").toString().startsWith(map.get("areaCode").toString())).count();
            map.put("schoolCount",count);
            if(CollectionUtils.isNotEmpty((Collection<?>) map.get("child"))){
                List<Map<String,Object>> child = (List<Map<String, Object>>) map.get("child");
                computeSchoolCount(child,school);
            }
        }

    }

    /**
     * 获取学校区域的最小父亲 areaCode
     * @param area 学校区域area集合
     * @param rs 学校共同的 area 区域结合
     * @return 小父亲 areaCode
     */
    public String getSchoolMinCommonAreaCode(List<Map<String, Object>> area, Set<String> rs) {
        if (CollectionUtils.isEmpty(area)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        String areaCodeSeparator = ",";
        List<String> areaCodes = area.stream().map(x -> x.get("areaCode").toString()).collect(Collectors.toList());
        String commonAreaCode = PathUtils.getMinCommonPath(areaCodes, areaCodeSeparator);
        if (StringUtils.isBlank(commonAreaCode)) {
            //全国的code
            commonAreaCode = "10";
        }else{
            for (String areaCode : areaCodes) {
                while (areaCode.length() > commonAreaCode.length() && areaCode.contains(areaCodeSeparator)) {
                    rs.add(areaCode);
                    areaCode = areaCode.substring(0, areaCode.lastIndexOf(areaCodeSeparator));
                }
            }
        }
        rs.add(commonAreaCode);
        return commonAreaCode;
    }

    public List<Map<String, Object>> getCityArea(Map<String, Object> params) {
        // 设置区域的区域全称
        List<Map<String, Object>> areas = commonRepository.selectList("CommonAreaMapper.getCityArea", params);
        return TreeUtil.list2Tree(areas, "areaId", "parentAreaId", "children");
    }
    
    /**
     * 获取某个id的孩子区域
     * @param params parentAreaId 父级区域id
     * @return 孩子区域
     */
    public List<Map<String, Object>> getAreaListByParentAreaId(Map<String, Object> params) {
        Verify.of(params).isValidId("parentAreaId").verify();
        return commonRepository.selectList("CommonAreaMapper.getAreaListByParentAreaId", params);
    }

    /**
     * 根据areaCode返回区域信息
     *
     * @param areaCodes areaId areaCode areaName
     * @return 区域信息
     */
    public List<Map<String, Object>> getAreaByAreaCodes(Collection<String> areaCodes) {
        if (CollectionUtils.isEmpty(areaCodes)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = MapUtil.of("list", areaCodes);
        return commonRepository.selectList("CommonAreaMapper.getAreaByAreaCodes", params);
    }
}
