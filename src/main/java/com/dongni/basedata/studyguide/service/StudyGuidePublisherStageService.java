package com.dongni.basedata.studyguide.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.studyguide.bean.dto.PublisherStageCdResultDTO;
import com.dongni.basedata.studyguide.bean.dto.PublisherStageDTO;
import com.dongni.basedata.studyguide.bean.param.PublisherStageListGetParam;
import com.dongni.basedata.studyguide.bean.po.StudyGuidePublisherStagePO;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.utils.verify.Verify2;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toMap;


/**
 * 教辅商学段
 * <AUTHOR>
 * @date 2025/03/10
 */
@Service
public class StudyGuidePublisherStageService {
    
    @Autowired
    private BaseDataRepository baseDataRepository;
    
    /**
     * 获取教辅商的学段信息
     */
    public List<PublisherStageDTO> getPublisherStageList(PublisherStageListGetParam publisherStageListGetParam) {
        Verify2.of(publisherStageListGetParam)
                .isValidId(PublisherStageListGetParam::getStudyGuidePublisherId, "获取教辅商学段时教辅商id必须提供")
                .verify();
        return baseDataRepository.selectList("StudyGuidePublisherStageMapper.getPublisherStageListById", publisherStageListGetParam);
        
    }
    
    /**
     * 管理教辅商学段信息
     * @param studyGuidePublisherId 教辅商id
     * @param stageList             新的学段list
     * @param oldPublisherStageList 旧的学段list
     * @return 是否有修改到数据
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    public PublisherStageCdResultDTO managePublisherStage(long studyGuidePublisherId,
                                                          List<Integer> stageList,
                                                          List<PublisherStageDTO> oldPublisherStageList) {
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        Date currentTime = new Date();
        
        Map<Integer, PublisherStageDTO> oldStage2Info = oldPublisherStageList.stream()
                .collect(toMap(PublisherStageDTO::getStage, item -> item));
        
        List<StudyGuidePublisherStagePO> insertStageList = new ArrayList<>();
        for (Integer stage : stageList) {
            PublisherStageDTO oldPublisherStage = oldStage2Info.remove(stage);
            if (oldPublisherStage == null) {
                StudyGuidePublisherStagePO publisherStagePO = new StudyGuidePublisherStagePO();
                publisherStagePO.setStudyGuidePublisherId(studyGuidePublisherId);
                publisherStagePO.setStage(stage);
                publisherStagePO.setCreatorId(dongniUserInfoContext.getUserId());
                publisherStagePO.setCreatorName(dongniUserInfoContext.getUserName());
                publisherStagePO.setCreateDateTime(currentTime);
                publisherStagePO.setModifierId(dongniUserInfoContext.getUserId());
                publisherStagePO.setModifierName(dongniUserInfoContext.getUserName());
                publisherStagePO.setModifyDateTime(currentTime);
                insertStageList.add(publisherStagePO);
            }
        }
        List<PublisherStageDTO> deleteStageList = new ArrayList<>(oldStage2Info.values());
        
        int deleteCount = 0;
        if (CollectionUtils.isNotEmpty(deleteStageList)) {
            Map<String, Object> deleteParams = MapUtil.of(
                    "studyGuidePublisherId", studyGuidePublisherId,
                    "stageList", deleteStageList
            );
            deleteCount = baseDataRepository.delete("StudyGuidePublisherStageMapper.deleteByPublisherStage", deleteParams);
        }
        
        int insertCount = 0;
        if (CollectionUtils.isNotEmpty(insertStageList)) {
            insertCount = baseDataRepository.insert("StudyGuidePublisherStageMapper.insertPublisherStage", insertStageList);
        }
        
        PublisherStageCdResultDTO resultDTO = new PublisherStageCdResultDTO();
        resultDTO.setInsertCount(insertCount);
        resultDTO.setDeleteCount(deleteCount);
        return resultDTO;
    }
}
