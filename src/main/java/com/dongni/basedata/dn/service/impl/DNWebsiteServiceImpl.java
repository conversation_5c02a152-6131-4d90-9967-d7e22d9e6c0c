package com.dongni.basedata.dn.service.impl;

import com.alibaba.fastjson.JSON;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.dn.report.WebsiteMessageReport;
import com.dongni.basedata.dn.service.DNWebsiteService;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020/05/14 09:41
 */
@Service
public class DNWebsiteServiceImpl implements DNWebsiteService {

    private static final Logger log = LogManager.getLogger(DNWebsiteServiceImpl.class);

    @Autowired
    private BaseDataRepository commonRepository;

    @Override
    public void saveMessage(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("name")
                .isNotBlank("mobile")
                .isNotBlank("companyName")
                .verify();

        params.putIfAbsent("userId", 1);
        params.putIfAbsent("userName", 1);
        params.putIfAbsent("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.insert("DNWebsiteMapper.insertCooperationMessage", params);
    }

    @Override
    public Map<String, Object> queryMessage(Map<String, Object> params) {
        params.putIfAbsent("pageNo", 0);
        params.putIfAbsent("pageSize", 20);
        Map<String, Object> result = new HashMap<>();
        long totalCount = commonRepository.selectOne("DNWebsiteMapper.getMessageByLikeCount", params);
        result.put("totalCount", totalCount);
        if (totalCount == 0) {
            result.put("message", null);
        }
        List<Object> data = commonRepository.selectList("DNWebsiteMapper.getMessageByLike", params);
        result.put("messages", data);
        return result;
    }

    @Override
    public ExportExcel exportMessage(Map<String, Object> params) {
        params.remove("pageNo");
        params.remove("pageSize");
        ExportExcel report = null;
        try {
            String[] headers = new String[]{"姓名", "电话", "单位", "省", "市", "合作申请留言", "留言时间"};
            String[] fields = new String[]{"name", "mobile", "companyName", "province", "city", "message", "createDateTime"};
            Object messageObj = queryMessage(params).get("messages");
            if (messageObj != null) {
                List<Map<String, Object>> message = (ArrayList<Map<String, Object>>)messageObj;
                report = new WebsiteMessageReport("合作生请留言信息", message, fields, headers);
            }
        } catch (Exception e) {
            log.error("导出合作生请留言信息失败params{},error{}", JSON.toJSON(params), e.getMessage());
        }
        return report;
    }


}
