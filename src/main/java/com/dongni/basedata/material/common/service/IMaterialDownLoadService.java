package com.dongni.basedata.material.common.service;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/9/21.
 *
 * 材料下载
 */
public interface IMaterialDownLoadService {

    /**
     * 获取老师的多类别文件下载地址(对应老师提交详情的批量下载)
     * @param params materialTeacherItemId 多个使用逗号隔开
     * @return 文件地址
     */
    String getTeacherMultiMaterialDownloadUrl(Map<String, Object> params);

    /**
     * 获取某学年学期多类别文件下载地址（对应文件中心的批量下载）
     * @param params gradeYear gradeTerm  materialId 多个使用逗号隔开 [materialFileId 多个使用逗号隔开]
     * @return 文件地址
     */
    String getRootMultiMaterialDownloadUrl(Map<String, Object> params);

}
