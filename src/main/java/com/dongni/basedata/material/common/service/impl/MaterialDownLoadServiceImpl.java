package com.dongni.basedata.material.common.service.impl;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.material.common.service.IMaterialDownLoadService;
import com.dongni.common.utils.VerifyCodeUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageGet;
import com.dongni.commons.utils.file.CompressFileUtils;
import com.dongni.commons.utils.verify.Verify;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.groupingBy;

/**
 * Created by Heweipo on 2018/9/21.
 *
 * 材料类别下载
 */
@Service
public class MaterialDownLoadServiceImpl implements IMaterialDownLoadService {

    @Autowired
    private BaseDataRepository baseDataRepository;

    /**
     * 获取老师的多类别文件下载地址(对应老师提交详情的批量下载)
     *
     * @param params schoolId gradeYear gradeTerm materialTeacherItemId 多个使用逗号隔开
     * @return 文件地址
     */
    @Override
    public String getTeacherMultiMaterialDownloadUrl(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isNotBlank("gradeYear")
                .isNotBlank("gradeTerm")
                .isNotBlank("materialTeacherItemId")
                .verify();

        String[] ids = params.remove("materialTeacherItemId").toString().split(",");
        params.put("materialTeacherItemIds", ids);

        String prefix = getFileNameForGradeYearTerm(params);

        List<Map<String, Object>> fs = baseDataRepository.selectList("MaterialDownLoadMapper.getMaterialFile", params);
        if (CollectionUtils.isEmpty(fs)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "没有找到上传的文件");
        }
        String teacherName = fs.get(0).get("teacherName").toString();

        Map<Long, List<Map<String, Object>>> gfs = fs.stream().collect(groupingBy(f -> Long.valueOf(f.get("materialTeacherItemId").toString())));
        boolean isMulti = gfs.size() > 1;
        String materialNameStr = "";

        //批量下载
        List<FileStorageGet> fileStorageGets = new ArrayList<>();
        StringBuilder path = new StringBuilder();
        StringBuilder materialName = new StringBuilder();

        materialNameStr = getFileStorageGets(fs, gfs, materialNameStr, fileStorageGets);

        materialName.append(materialNameStr);

        FileStorageTemplate.batchGet(fileStorageGets, fileStorageBatchGet -> {
            File root = fileStorageBatchGet.getRootDir();
            String tempPath = FileStorageTemplate.put(fileStoragePut -> {
                String suffix = "_" + RandomUtils.nextInt(10) + ".zip";
                String filePath = isMulti ? fileStoragePut.getRootPath() + prefix + "-" + teacherName + suffix :
                        fileStoragePut.getRootPath() + prefix + "-" + teacherName + "-" + materialName + suffix;
                File zip = new File(filePath);
                //压缩
                CompressFileUtils.zip(root, zip);
                fileStoragePut.setLocalFile(zip);
                fileStoragePut.setAutoExpire(true);
            });
            path.append(tempPath);
        });

        return path.toString();
    }

    /**
     * 获取某学年学期多类别文件下载地址（对应文件中心的批量下载）
     *
     * @param params gradeYear gradeTerm  materialId 多个使用逗号隔开 [materialFileId 多个使用逗号隔开]
     * @return 文件地址
     */
    @Override
    public String getRootMultiMaterialDownloadUrl(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isNotBlank("gradeYear")
                .isNotBlank("gradeTerm")
                .isNotBlank("materialId")
                .verify();

        // 参数解析
        String[] ids = params.remove("materialId").toString().split(",");
        params.put("materialIds", ids);
        params.put("materialRootId", getMaterialRootForGradeYearTerm(params));

        if (!VerifyCodeUtil.isBlank(params.get("materialFileId"))) {
            params.put("materialFileIds", params.remove("materialFileId").toString().split(","));
        }

        // 定义文件夹
        String prefix = getFileNameForGradeYearTerm(params);

        List<Map<String, Object>> fs = baseDataRepository.selectList("MaterialDownLoadMapper.getMaterialFile", params);
        if (CollectionUtils.isEmpty(fs)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "没有找到上传的文件");
        }

        Map<Long, List<Map<String, Object>>> gfs = fs.stream().collect(groupingBy(f -> Long.valueOf(f.get("materialId").toString())));
        boolean isMulti = gfs.size() > 1;
        String materialNameStr = "";

        //批量下载
        List<FileStorageGet> fileStorageGets = new ArrayList<>();
        StringBuilder path = new StringBuilder();
        StringBuilder materialName = new StringBuilder();

        materialNameStr = getFileStorageGets(fs, gfs, materialNameStr, fileStorageGets);

        materialName.append(materialNameStr);

        FileStorageTemplate.batchGet(fileStorageGets, fileStorageBatchGet -> {
            File root = fileStorageBatchGet.getRootDir();
            String tempPath = FileStorageTemplate.put(fileStoragePut -> {
                String suffix = "_" + RandomUtils.nextInt(10) + ".zip";
                String filePath = isMulti ? fileStoragePut.getRootPath() + prefix + suffix :
                        fileStoragePut.getRootPath() + prefix + "-" + materialName + suffix;
                File zip = new File(filePath);
                //压缩
                CompressFileUtils.zip(root, zip);
                fileStoragePut.setLocalFile(zip);
                fileStoragePut.setAutoExpire(true);
            });
            path.append(tempPath);
        });

        return path.toString();
    }

    public String getFileStorageGets(List<Map<String, Object>> fs,
                                     Map<Long, List<Map<String, Object>>> gfs,
                                     String materialNameStr, List<FileStorageGet> fileStorageGets) {
        for (Long itemId : gfs.keySet()) {
            materialNameStr = gfs.get(itemId).get(0).get("materialName").toString();
            for (Map<String, Object> item : fs) {
                FileStorageGet fileStorageGet = new FileStorageGet();
                fileStorageGet.setFilePath(item.get("fileUrl").toString());
                fileStorageGet.setFileName("(" + item.get("materialFileId") + ")_" + item.get("fileName"));
                fileStorageGets.add(fileStorageGet);
            }
        }
        return materialNameStr;
    }

    /**
     * 根据学年学期转为要求的文件名称
     * @param params gradeYear gradeTerm
     * @return 文件名称
     */
    private String getFileNameForGradeYearTerm(Map<String, Object> params) {
        int gradeYear = Integer.parseInt(params.get("gradeYear").toString());
        int gradeTerm = Integer.parseInt(params.get("gradeTerm").toString());
        return gradeYear + "-" + (gradeYear + 1) + "学年" + (gradeTerm == 1 ? "上学期" : "下学期");
    }

    /**
     * 根据学年学期获取对应根路径ID
     * @param params gradeYear gradeTerm
     * @return materialRootId
     */
    private Long getMaterialRootForGradeYearTerm(Map<String, Object> params) {
        Map<String, Object> rs = baseDataRepository.selectOne("MaterialDownLoadMapper.getMaterialRootId", params);
        if (MapUtils.isEmpty(rs)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "没有找到对应的文件收集任务");
        }
        return Long.valueOf(rs.get("materialRootId").toString());
    }
}

