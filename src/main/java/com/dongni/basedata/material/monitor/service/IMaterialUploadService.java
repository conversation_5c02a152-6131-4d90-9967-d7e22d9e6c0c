package com.dongni.basedata.material.monitor.service;

import java.util.List;
import java.util.Map;

/**
 * Created by scott
 * time: 16:04 2018/9/20
 * description:
 */
public interface IMaterialUploadService {


    /**
     * 获取老师上传资料列表
     * params materialPlanId teacherId [staffId]
     *
     * @return 老师上传资料列表
     */
    List<Map<String, Object>> getMaterialUpload(Map<String, Object> params);


    /**
     * 老师上传资料
     * params materialRootId materialTeacherItemId materialId materialName  teacherId teacherName fileStatus fileUrl fileName
     *
     */
    void insertMaterialFile(Map<String, Object> params);

    /**
     * 删除上传资料
     * params materialFileId
     *
     */
    void deleteMaterialFile(Map<String, Object> parameterMap);

    /**
     * 提交上传资料
     * params materialPlanTeacherId
     *
     */
    void updateMaterialPlanTeacher(Map<String, Object> parameterMap);
}
