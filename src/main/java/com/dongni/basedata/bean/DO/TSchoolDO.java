package com.dongni.basedata.bean.DO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据库表t_school
 *
 * <AUTHOR>
 * @date 2023/4/7 16:54
 */
public class TSchoolDO implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_id
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Long schoolId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.area_id
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Long areaId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_status
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Byte schoolStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.member_type
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Byte memberType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.belong_type
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Byte belongType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.stage
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Byte stage;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.grade_number
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Byte gradeNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_name
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String schoolName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_group_name
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String schoolGroupName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_phone
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String schoolPhone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_phone_aes
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String schoolPhoneAes;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.address
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String address;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_property
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Byte schoolProperty;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.longitude
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private BigDecimal longitude;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.latitude
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private BigDecimal latitude;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.third_party_id
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Long thirdPartyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.third_biz_id
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Long thirdBizId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_ascription
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Byte schoolAscription;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.company_id
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Long companyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.dingding_serial_number
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String dingdingSerialNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.expire_datetime
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Date expireDatetime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.school_sort
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Integer schoolSort;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.creator_id
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Long creatorId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.creator_name
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String creatorName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.create_date_time
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Date createDateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.modifier_id
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Long modifierId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.modifier_name
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private String modifierName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column t_school.modify_date_time
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private Date modifyDateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table t_school
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_id
     *
     * @return the value of t_school.school_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Long getSchoolId() {
        return schoolId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_id
     *
     * @param schoolId the value for t_school.school_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolId(Long schoolId) {
        this.schoolId = schoolId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.area_id
     *
     * @return the value of t_school.area_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Long getAreaId() {
        return areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.area_id
     *
     * @param areaId the value for t_school.area_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_status
     *
     * @return the value of t_school.school_status
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Byte getSchoolStatus() {
        return schoolStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_status
     *
     * @param schoolStatus the value for t_school.school_status
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolStatus(Byte schoolStatus) {
        this.schoolStatus = schoolStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.member_type
     *
     * @return the value of t_school.member_type
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Byte getMemberType() {
        return memberType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.member_type
     *
     * @param memberType the value for t_school.member_type
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setMemberType(Byte memberType) {
        this.memberType = memberType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.belong_type
     *
     * @return the value of t_school.belong_type
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Byte getBelongType() {
        return belongType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.belong_type
     *
     * @param belongType the value for t_school.belong_type
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setBelongType(Byte belongType) {
        this.belongType = belongType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.stage
     *
     * @return the value of t_school.stage
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Byte getStage() {
        return stage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.stage
     *
     * @param stage the value for t_school.stage
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setStage(Byte stage) {
        this.stage = stage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.grade_number
     *
     * @return the value of t_school.grade_number
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Byte getGradeNumber() {
        return gradeNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.grade_number
     *
     * @param gradeNumber the value for t_school.grade_number
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setGradeNumber(Byte gradeNumber) {
        this.gradeNumber = gradeNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_name
     *
     * @return the value of t_school.school_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getSchoolName() {
        return schoolName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_name
     *
     * @param schoolName the value for t_school.school_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolName(String schoolName) {
        this.schoolName = schoolName == null ? null : schoolName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_group_name
     *
     * @return the value of t_school.school_group_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getSchoolGroupName() {
        return schoolGroupName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_group_name
     *
     * @param schoolGroupName the value for t_school.school_group_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolGroupName(String schoolGroupName) {
        this.schoolGroupName = schoolGroupName == null ? null : schoolGroupName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_phone
     *
     * @return the value of t_school.school_phone
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getSchoolPhone() {
        return schoolPhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_phone
     *
     * @param schoolPhone the value for t_school.school_phone
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolPhone(String schoolPhone) {
        this.schoolPhone = schoolPhone == null ? null : schoolPhone.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_phone_aes
     *
     * @return the value of t_school.school_phone_aes
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getSchoolPhoneAes() {
        return schoolPhoneAes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_phone_aes
     *
     * @param schoolPhoneAes the value for t_school.school_phone_aes
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolPhoneAes(String schoolPhoneAes) {
        this.schoolPhoneAes = schoolPhoneAes == null ? null : schoolPhoneAes.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.address
     *
     * @return the value of t_school.address
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.address
     *
     * @param address the value for t_school.address
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_property
     *
     * @return the value of t_school.school_property
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Byte getSchoolProperty() {
        return schoolProperty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_property
     *
     * @param schoolProperty the value for t_school.school_property
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolProperty(Byte schoolProperty) {
        this.schoolProperty = schoolProperty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.longitude
     *
     * @return the value of t_school.longitude
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public BigDecimal getLongitude() {
        return longitude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.longitude
     *
     * @param longitude the value for t_school.longitude
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.latitude
     *
     * @return the value of t_school.latitude
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public BigDecimal getLatitude() {
        return latitude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.latitude
     *
     * @param latitude the value for t_school.latitude
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.third_party_id
     *
     * @return the value of t_school.third_party_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Long getThirdPartyId() {
        return thirdPartyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.third_party_id
     *
     * @param thirdPartyId the value for t_school.third_party_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setThirdPartyId(Long thirdPartyId) {
        this.thirdPartyId = thirdPartyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.third_biz_id
     *
     * @return the value of t_school.third_biz_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Long getThirdBizId() {
        return thirdBizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.third_biz_id
     *
     * @param thirdBizId the value for t_school.third_biz_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setThirdBizId(Long thirdBizId) {
        this.thirdBizId = thirdBizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_ascription
     *
     * @return the value of t_school.school_ascription
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Byte getSchoolAscription() {
        return schoolAscription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_ascription
     *
     * @param schoolAscription the value for t_school.school_ascription
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolAscription(Byte schoolAscription) {
        this.schoolAscription = schoolAscription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.company_id
     *
     * @return the value of t_school.company_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.company_id
     *
     * @param companyId the value for t_school.company_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.dingding_serial_number
     *
     * @return the value of t_school.dingding_serial_number
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getDingdingSerialNumber() {
        return dingdingSerialNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.dingding_serial_number
     *
     * @param dingdingSerialNumber the value for t_school.dingding_serial_number
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setDingdingSerialNumber(String dingdingSerialNumber) {
        this.dingdingSerialNumber = dingdingSerialNumber == null ? null : dingdingSerialNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.expire_datetime
     *
     * @return the value of t_school.expire_datetime
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Date getExpireDatetime() {
        return expireDatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.expire_datetime
     *
     * @param expireDatetime the value for t_school.expire_datetime
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setExpireDatetime(Date expireDatetime) {
        this.expireDatetime = expireDatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.school_sort
     *
     * @return the value of t_school.school_sort
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Integer getSchoolSort() {
        return schoolSort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.school_sort
     *
     * @param schoolSort the value for t_school.school_sort
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setSchoolSort(Integer schoolSort) {
        this.schoolSort = schoolSort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.creator_id
     *
     * @return the value of t_school.creator_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Long getCreatorId() {
        return creatorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.creator_id
     *
     * @param creatorId the value for t_school.creator_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.creator_name
     *
     * @return the value of t_school.creator_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.creator_name
     *
     * @param creatorName the value for t_school.creator_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.create_date_time
     *
     * @return the value of t_school.create_date_time
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Date getCreateDateTime() {
        return createDateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.create_date_time
     *
     * @param createDateTime the value for t_school.create_date_time
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setCreateDateTime(Date createDateTime) {
        this.createDateTime = createDateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.modifier_id
     *
     * @return the value of t_school.modifier_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Long getModifierId() {
        return modifierId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.modifier_id
     *
     * @param modifierId the value for t_school.modifier_id
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setModifierId(Long modifierId) {
        this.modifierId = modifierId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.modifier_name
     *
     * @return the value of t_school.modifier_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public String getModifierName() {
        return modifierName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.modifier_name
     *
     * @param modifierName the value for t_school.modifier_name
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setModifierName(String modifierName) {
        this.modifierName = modifierName == null ? null : modifierName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column t_school.modify_date_time
     *
     * @return the value of t_school.modify_date_time
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public Date getModifyDateTime() {
        return modifyDateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column t_school.modify_date_time
     *
     * @param modifyDateTime the value for t_school.modify_date_time
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    public void setModifyDateTime(Date modifyDateTime) {
        this.modifyDateTime = modifyDateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_school
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", schoolId=").append(schoolId);
        sb.append(", areaId=").append(areaId);
        sb.append(", schoolStatus=").append(schoolStatus);
        sb.append(", memberType=").append(memberType);
        sb.append(", belongType=").append(belongType);
        sb.append(", stage=").append(stage);
        sb.append(", gradeNumber=").append(gradeNumber);
        sb.append(", schoolName=").append(schoolName);
        sb.append(", schoolGroupName=").append(schoolGroupName);
        sb.append(", schoolPhone=").append(schoolPhone);
        sb.append(", schoolPhoneAes=").append(schoolPhoneAes);
        sb.append(", address=").append(address);
        sb.append(", schoolProperty=").append(schoolProperty);
        sb.append(", longitude=").append(longitude);
        sb.append(", latitude=").append(latitude);
        sb.append(", thirdPartyId=").append(thirdPartyId);
        sb.append(", thirdBizId=").append(thirdBizId);
        sb.append(", schoolAscription=").append(schoolAscription);
        sb.append(", companyId=").append(companyId);
        sb.append(", dingdingSerialNumber=").append(dingdingSerialNumber);
        sb.append(", expireDatetime=").append(expireDatetime);
        sb.append(", schoolSort=").append(schoolSort);
        sb.append(", creatorId=").append(creatorId);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", createDateTime=").append(createDateTime);
        sb.append(", modifierId=").append(modifierId);
        sb.append(", modifierName=").append(modifierName);
        sb.append(", modifyDateTime=").append(modifyDateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_school
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TSchoolDO other = (TSchoolDO) that;
        return (this.getSchoolId() == null ? other.getSchoolId() == null : this.getSchoolId().equals(other.getSchoolId()))
                && (this.getAreaId() == null ? other.getAreaId() == null : this.getAreaId().equals(other.getAreaId()))
                && (this.getSchoolStatus() == null ? other.getSchoolStatus() == null : this.getSchoolStatus().equals(other.getSchoolStatus()))
                && (this.getMemberType() == null ? other.getMemberType() == null : this.getMemberType().equals(other.getMemberType()))
                && (this.getBelongType() == null ? other.getBelongType() == null : this.getBelongType().equals(other.getBelongType()))
                && (this.getStage() == null ? other.getStage() == null : this.getStage().equals(other.getStage()))
                && (this.getGradeNumber() == null ? other.getGradeNumber() == null : this.getGradeNumber().equals(other.getGradeNumber()))
                && (this.getSchoolName() == null ? other.getSchoolName() == null : this.getSchoolName().equals(other.getSchoolName()))
                && (this.getSchoolGroupName() == null ? other.getSchoolGroupName() == null : this.getSchoolGroupName().equals(other.getSchoolGroupName()))
                && (this.getSchoolPhone() == null ? other.getSchoolPhone() == null : this.getSchoolPhone().equals(other.getSchoolPhone()))
                && (this.getSchoolPhoneAes() == null ? other.getSchoolPhoneAes() == null : this.getSchoolPhoneAes().equals(other.getSchoolPhoneAes()))
                && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
                && (this.getSchoolProperty() == null ? other.getSchoolProperty() == null : this.getSchoolProperty().equals(other.getSchoolProperty()))
                && (this.getLongitude() == null ? other.getLongitude() == null : this.getLongitude().equals(other.getLongitude()))
                && (this.getLatitude() == null ? other.getLatitude() == null : this.getLatitude().equals(other.getLatitude()))
                && (this.getThirdPartyId() == null ? other.getThirdPartyId() == null : this.getThirdPartyId().equals(other.getThirdPartyId()))
                && (this.getThirdBizId() == null ? other.getThirdBizId() == null : this.getThirdBizId().equals(other.getThirdBizId()))
                && (this.getSchoolAscription() == null ? other.getSchoolAscription() == null : this.getSchoolAscription().equals(other.getSchoolAscription()))
                && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
                && (this.getDingdingSerialNumber() == null ? other.getDingdingSerialNumber() == null : this.getDingdingSerialNumber().equals(other.getDingdingSerialNumber()))
                && (this.getExpireDatetime() == null ? other.getExpireDatetime() == null : this.getExpireDatetime().equals(other.getExpireDatetime()))
                && (this.getSchoolSort() == null ? other.getSchoolSort() == null : this.getSchoolSort().equals(other.getSchoolSort()))
                && (this.getCreatorId() == null ? other.getCreatorId() == null : this.getCreatorId().equals(other.getCreatorId()))
                && (this.getCreatorName() == null ? other.getCreatorName() == null : this.getCreatorName().equals(other.getCreatorName()))
                && (this.getCreateDateTime() == null ? other.getCreateDateTime() == null : this.getCreateDateTime().equals(other.getCreateDateTime()))
                && (this.getModifierId() == null ? other.getModifierId() == null : this.getModifierId().equals(other.getModifierId()))
                && (this.getModifierName() == null ? other.getModifierName() == null : this.getModifierName().equals(other.getModifierName()))
                && (this.getModifyDateTime() == null ? other.getModifyDateTime() == null : this.getModifyDateTime().equals(other.getModifyDateTime()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table t_school
     *
     * @mbg.generated Fri Apr 07 08:53:19 UTC 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSchoolId() == null) ? 0 : getSchoolId().hashCode());
        result = prime * result + ((getAreaId() == null) ? 0 : getAreaId().hashCode());
        result = prime * result + ((getSchoolStatus() == null) ? 0 : getSchoolStatus().hashCode());
        result = prime * result + ((getMemberType() == null) ? 0 : getMemberType().hashCode());
        result = prime * result + ((getBelongType() == null) ? 0 : getBelongType().hashCode());
        result = prime * result + ((getStage() == null) ? 0 : getStage().hashCode());
        result = prime * result + ((getGradeNumber() == null) ? 0 : getGradeNumber().hashCode());
        result = prime * result + ((getSchoolName() == null) ? 0 : getSchoolName().hashCode());
        result = prime * result + ((getSchoolGroupName() == null) ? 0 : getSchoolGroupName().hashCode());
        result = prime * result + ((getSchoolPhone() == null) ? 0 : getSchoolPhone().hashCode());
        result = prime * result + ((getSchoolPhoneAes() == null) ? 0 : getSchoolPhoneAes().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getSchoolProperty() == null) ? 0 : getSchoolProperty().hashCode());
        result = prime * result + ((getLongitude() == null) ? 0 : getLongitude().hashCode());
        result = prime * result + ((getLatitude() == null) ? 0 : getLatitude().hashCode());
        result = prime * result + ((getThirdPartyId() == null) ? 0 : getThirdPartyId().hashCode());
        result = prime * result + ((getThirdBizId() == null) ? 0 : getThirdBizId().hashCode());
        result = prime * result + ((getSchoolAscription() == null) ? 0 : getSchoolAscription().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getDingdingSerialNumber() == null) ? 0 : getDingdingSerialNumber().hashCode());
        result = prime * result + ((getExpireDatetime() == null) ? 0 : getExpireDatetime().hashCode());
        result = prime * result + ((getSchoolSort() == null) ? 0 : getSchoolSort().hashCode());
        result = prime * result + ((getCreatorId() == null) ? 0 : getCreatorId().hashCode());
        result = prime * result + ((getCreatorName() == null) ? 0 : getCreatorName().hashCode());
        result = prime * result + ((getCreateDateTime() == null) ? 0 : getCreateDateTime().hashCode());
        result = prime * result + ((getModifierId() == null) ? 0 : getModifierId().hashCode());
        result = prime * result + ((getModifierName() == null) ? 0 : getModifierName().hashCode());
        result = prime * result + ((getModifyDateTime() == null) ? 0 : getModifyDateTime().hashCode());
        return result;
    }
}
