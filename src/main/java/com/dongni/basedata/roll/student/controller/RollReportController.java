package com.dongni.basedata.roll.student.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.roll.student.service.IRollReportService;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniOperationLog;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/10/12 15:31
 */
@RestController
@RequestMapping(BaseDataConfig.CONTEXT_PATH + "/roll/report")
public class RollReportController extends BaseController {

    @Autowired
    private IRollReportService iRollStudentService;
    @Autowired
    private HttpServletRequest request;


    /**
     * 查询素质报告
     *
     * @return
     */
    @GetMapping("/evaluation")
    @DongniOperationLog
    @DongniRequest(operationName = "学籍.查询素质报告", remark = {"TODO 权限"})
    public Response getEvaluationReport() {
        Map<String, Object> params = getParameterMap();
        params.put("requestUrl", request.getRequestURI());
        return new Response(iRollStudentService.getEvaluationReport(params));
    }

    /**
     * 查询素质报告
     *
     * @return
     */
    @GetMapping("/stuAllEvaluation")
    @DongniOperationLog
    @DongniRequest(operationName = "学籍.获取学生素质报告册", remark = {"TODO 权限"})
    public Response getStuAllEvaluationReport() {
        Map<String, Object> params = getParameterMap();
        params.put("requestUrl", request.getRequestURI());
        return new Response(iRollStudentService.getStuAllEvaluationReport(params));
    }


    /**
     * 查询素质报告，下载专用接口，解密后的信息
     *
     * @return
     */
    @GetMapping("/evaluation/download")
    @DongniRequest(operationName = "学籍.查询素质报告(下载)", remark = {"TODO 权限"})
    public Response getEvaluationReportDownload() {
        return new Response(iRollStudentService.getEvaluationReportDownload(getParameterMap()));
    }

    /**
     * 查询学籍卡片
     *
     * @return
     */
    @GetMapping("/card")
    @DongniRequest(operationName = "学籍.查询学籍卡片", remark = {"TODO 权限"})
    public Response getCardReport() {
        Map<String,Object> params = getParameterMap();
        params.put("requestUrl",request.getRequestURI());
        return new Response(iRollStudentService.getCardReport(params));
    }

    /**
     * 查询学籍卡片，下载专用接口，解密后的信息
     *
     * @return
     */
    @GetMapping("/card/download")
    @DongniRequest(operationName = "学籍.查询学籍卡片(下载)", remark = {"TODO 权限"})
    public Response getCardReportDownload() {
        return new Response(iRollStudentService.getCardReportDownload(getParameterMap()));
    }

}
