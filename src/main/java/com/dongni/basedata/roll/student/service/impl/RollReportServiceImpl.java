package com.dongni.basedata.roll.student.service.impl;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.roll.common.enumeration.RollPlanStatus;
import com.dongni.basedata.roll.common.service.IRollCommonService;
import com.dongni.basedata.roll.plan.service.IRollPlanCourseService;
import com.dongni.basedata.roll.student.service.IRollReportService;
import com.dongni.basedata.roll.student.service.IRollStudentService;
import com.dongni.basedata.school.grade.service.impl.GradeServiceImpl;
import com.dongni.basedata.system.account.service.IUserService;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.GradeUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.wooutils.string.Hash;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2018/10/12 15:45
 */
@Service
public class RollReportServiceImpl implements IRollReportService {
    private final static Logger log = LogManager.getLogger(RollReportServiceImpl.class);

    @Autowired
    private IRollStudentService iRollStudentService;

    @Autowired
    private BaseDataRepository commonRepository;

    @Autowired
    private IUserService userService;

    @Autowired
    private GradeServiceImpl gradeService;

    @Autowired
    private IRollCommonService rollCommonService;


    /**
     * 获取素质报告
     *
     * @param params rollStudentId stage gradeYear gradeTerm gradeType schoolId
     * @return
     */
    @Override
    public Map<String, Object> getEvaluationReport(Map<String, Object> params) {
        if (!ObjectUtil.isValidId(params.get("rollStudentId"))
                || !ObjectUtil.isNumeric(params.get("stage"))
                || !ObjectUtil.isNumeric(params.get("gradeYear"))
                || !ObjectUtil.isNumeric(params.get("gradeTerm"))
                || !ObjectUtil.isValidId(params.get("schoolId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        params.put("allCourse", false);

        // 根据学年学期获取当前学生的年级
        Map<String, Object> grade = commonRepository.selectOne("RollReportMapper.selectStudentGrade", params);
        if (grade == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "当前学生没有年级，无法查看报告");
        }

        // 计算当前学生在学年对应的年级
        int stage = Integer.valueOf(params.get("stage").toString());
        int maxGradeType = 0;
        if (stage == 1) {
            maxGradeType = 6;
        } else if (stage == 2) {
            maxGradeType = 9;
        } else {
            maxGradeType = 12;
        }
        // grade中的gradeYear表示届，减1表示毕业学年。gradeType = 毕业年级 - (届 - 1 - 查询学年)
        int gradeType = maxGradeType - (Integer.valueOf(grade.get("gradeYear").toString()) - 1 - Integer.valueOf(params.get("gradeYear").toString()));
        params.put("gradeType", gradeType);

        Map<String, Object> result = new HashMap<>();

        // 查询素质报告任务
        Map<String, Object> rollPlan = commonRepository.selectOne("RollReportMapper.selectRollPlan", params);
        if (rollPlan == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "素质报告不存在");
        }
        params.put("rollPlanId", rollPlan.get("rollPlanId"));

        int rollPlanStatus = Integer.valueOf(rollPlan.get("rollPlanStatus").toString());
        if (rollPlanStatus == RollPlanStatus.GENERATING.getStatus()) {
            // 进行中，取原始数据

            // 查询学生信息
            Map<String, Object> studentBase = iRollStudentService.getStudentBase(params);
            if (studentBase == null) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "当前学生不存在");
            }
            result.putAll(studentBase);

            // 查询监护人
            params.put("isParent", 1);
            List<Map<String, Object>> studentParent = iRollStudentService.getStudentParent(params);
            //校验是否通过，是则显示解密后的信息
            if (userService.checkUser(params)){
                SensitiveInfoUtil.aesDecrypt(studentParent,"tellPhoneAes");
            }
            result.put("parentList", studentParent);

        } else {
            // 已完成，取已完成的任务中的数据

            // 查询学生信息
            Map<String, Object> studentBase = commonRepository.selectOne("RollReportMapper.selectRollPlanStudent", params);
            if (studentBase == null) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "当前学生不存在");
            }

            if (studentBase.get("nativeAreaCode") != null) {
                // 查询户籍所在地
                studentBase.put("nativeAreaName", iRollStudentService.getRealAreaName(studentBase.get("nativeAreaCode").toString()));
            }

            if (studentBase.get("registerAreaCode") != null) {
                // 查询户口所在地
                studentBase.put("registerAreaName", iRollStudentService.getRealAreaName(studentBase.get("registerAreaCode").toString()));
            }

            result.putAll(studentBase);

            // 查询监护人
            List<Map<String, Object>> studentParent = commonRepository.selectList("RollReportMapper.selectRollPlanParent", params);
            result.put("parentList", studentParent);
        }



        // 查询毕业信息
        List<Map<String, Object>> studentGraduation = iRollStudentService.getStudentGraduation(params);
        if (CollectionUtils.isNotEmpty(studentGraduation)) {
            result.put("graduation", studentGraduation.get(0));
        } else {
            result.put("graduation", new HashMap<>());
        }



        // 查询班主任
        List<Map<String, Object>> studentTeacherList = commonRepository.selectList("RollReportMapper.selectStudentTeacher", params);
        result.put("teacherList", studentTeacherList);

        // 查询学校课程
        result.put("courseList", getSchoolCourseList(params));

        // 查询成绩
        List<Map<String, Object>> studentCourseList = commonRepository.selectList("RollReportMapper.selectStudentCourse", params);
        //考试成绩占比为0%,将其考试成绩为0的置为NULL   防止家长误解
        for (Map<String, Object> studentCourse : studentCourseList) {
            Map<String, Object> creator = commonRepository.selectOne("RollPlanCourseMapper.getRollPlanCourseByLogic", MapUtil.of("rollPlanId",params.get("rollPlanId"),"courseId",studentCourse.get("courseId")));
            //期中笔试成绩
            if (!ObjectUtil.isBlank(creator.get("midtermExamRate")) && "0.00".equals(creator.get("midtermExamRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("midtermExamWrittenScore")) && "0.0".equals(studentCourse.get("midtermExamWrittenScore").toString())){
                studentCourse.put("midtermExamWrittenScore",null);
            }
            //期中笔口试成绩
            if (!ObjectUtil.isBlank(creator.get("midtermOralRate")) && "0.00".equals(creator.get("midtermOralRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("midtermExamOralScore")) && "0.0".equals(studentCourse.get("midtermExamOralScore").toString())){
                studentCourse.put("midtermExamOralScore",null);
            }
            //期末笔试成绩
            if (!ObjectUtil.isBlank(creator.get("terminalExamRate")) && "0.00".equals(creator.get("terminalExamRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("terminalExamWrittenScore")) && "0.0".equals(studentCourse.get("terminalExamWrittenScore").toString())){
                studentCourse.put("terminalExamWrittenScore",null);
            }
            //期末口试成绩
            if (!ObjectUtil.isBlank(creator.get("terminalOralRate")) && "0.00".equals(creator.get("terminalOralRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("terminalExamOralScore")) && "0.0".equals(studentCourse.get("terminalExamOralScore").toString())){
                studentCourse.put("terminalExamOralScore",null);
            }
        }
        result.put("examCourseList", studentCourseList);

        // 查询考勤
        List<Map<String, Object>> studentAttendanceList = commonRepository.selectList("RollReportMapper.selectTermAttendance", params);
        if (CollectionUtils.isNotEmpty(studentAttendanceList)) {
            double totalLesson = Integer.parseInt(studentAttendanceList.get(0).get("totalLesson").toString());
            double sickLesson = 0.0D;
            double leaveLesson = 0.0D;
            double lateLesson = 0.0D;
            double truancyLesson = 0.0D;
            for (int i = 0, size = studentAttendanceList.size(); i < size; i++) {

                sickLesson = sickLesson + Double.parseDouble(studentAttendanceList.get(i).get("sickLesson").toString());
                leaveLesson = leaveLesson + Double.parseDouble(studentAttendanceList.get(i).get("leaveLesson").toString());
                lateLesson = lateLesson + Double.parseDouble(studentAttendanceList.get(i).get("lateLesson").toString());
                truancyLesson = truancyLesson + Double.parseDouble(studentAttendanceList.get(i).get("truancyLesson").toString());
            }
            result.put("attendance", MapUtil.of("totalLesson", totalLesson,
                                                "sickLesson", sickLesson,
                                                "leaveLesson", leaveLesson,
                                                "lateLesson", lateLesson,
                                                "truancyLesson", truancyLesson));
        } else {
            result.put("attendance", null);
        }


        // 查询注册时间
        Map<String, Object> schoolTerm = commonRepository.selectOne("RollReportMapper.selectSchoolTerm", params);
        result.put("schoolTerm", schoolTerm);

        // 查询老师寄语
        Map<String, Object> studentEvaluation = commonRepository.selectOne("RollReportMapper.selectStudentEvaluation", params);
        result.put("evaluation", studentEvaluation);

        // 查询模版
        Map<String, Object> template = commonRepository.selectOne("RollReportMapper.selectSchoolReportTemplate", params);
        if (template != null) {
            try {
                result.put("template", new ObjectMapper().readValue(template.get("value").toString(), Map.class));
            } catch (IOException e) {
                log.error("素质报告模版json解析错误");
            }
        } else {
            result.put("template", new HashMap<>());
        }

        return result;
    }


    /**
     * 获取学生素质报告册（包含学生所经历的所有学期）
     *
     * @param params
     * @return
     */
    @Override
    public Map<String, Object> getStuAllEvaluationReport(Map<String, Object> params) {
        if (!ObjectUtil.isValidId(params.get("studentId"))
                || !ObjectUtil.isNumeric(params.get("rollStudentId"))
                || !ObjectUtil.isNumeric(params.get("gradeId"))
                || !ObjectUtil.isValidId(params.get("schoolId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        params.put("allCourse", false);

        // 根据學生id获取当前学生的年级
        String gradeId = params.get("gradeId").toString();
        Map<String, Object> grade = gradeService.getGradeDetail(gradeId);
        if (grade == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "当前学生没有年级，无法查看报告");
        }
        params.put("stage", grade.get("stage"));

        Map<String, Object> result = new HashMap<>();
        // 查询学生信息
        Map<String, Object> studentBase = iRollStudentService.getStudentBase(params);
        if (studentBase == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "当前学生不存在");
        }
        result.put("studentBaseInfo", studentBase);

        // 查询监护人
        params.put("isParent", 1);
        List<Map<String, Object>> studentParent = iRollStudentService.getStudentParent(params);
        //校验是否通过，是则显示解密后的信息
        if (userService.checkUser(params)){
            SensitiveInfoUtil.aesDecrypt(studentParent,"tellPhoneAes");
        }
        result.put("parentList", studentParent);

        // 查询毕业信息
        List<Map<String, Object>> studentGraduation = iRollStudentService.getStudentGraduation(params);
        if (CollectionUtils.isNotEmpty(studentGraduation)) {
            result.put("graduation", studentGraduation.get(0));
        } else {
            result.put("graduation", new HashMap<>());
        }

        // 查询班主任
        List<Map<String, Object>> studentTeacherList = commonRepository.selectList("RollReportMapper.selectStudentTeacher", params);
        result.put("teacherList", studentTeacherList);


        // 查询模版
        Map<String, Object> template = commonRepository.selectOne("RollReportMapper.selectSchoolReportTemplate", params);
        if (template != null) {
            try {
                result.put("template", new ObjectMapper().readValue(template.get("value").toString(), Map.class));
            } catch (IOException e) {
                log.error("素质报告模版json解析错误");
            }
        } else {
            result.put("template", new HashMap<>());
        }

        List<Map<String, Object>> gradeYears = rollCommonService.getGradeYear(params);
        Map<String, Map<String, Object>> gradeCourseScores = new HashMap<>();
        result.put("gradeCourseScores", gradeCourseScores);

        for(Map<String, Object> gradeYearMap : gradeYears){
            params.put("gradeYear", gradeYearMap.get("gradeYear"));
            params.put("gradeType", gradeYearMap.get("gradeType"));

            Map<String, Object> termCourseScores = new HashMap<>();
            gradeCourseScores.put(gradeYearMap.get("gradeYear").toString(), termCourseScores);

            for(int term=1; term<=2; term++){
                params.put("gradeTerm", term);
                Map<String, Object> rollPlan = commonRepository.selectOne("RollReportMapper.selectRollPlan", params);
                if (rollPlan == null) {
                    continue;
                }

                params.put("rollPlanId", rollPlan.get("rollPlanId"));
                Map<String, Object> termResult = new HashMap<>();
                termCourseScores.put(String.valueOf(term), termResult);

                // 查询学校课程
                termResult.put("courseList", getSchoolCourseList(params));

                // 查询学期 学籍任务成绩
                List<Map<String, Object>> studentCourseList = commonRepository.selectList("RollReportMapper.selectStudentCourse", params);
                //考试成绩占比为0%, 将其考试成绩为0的置为NULL   防止家长误解
                for (Map<String, Object> studentCourse : studentCourseList) {
                    Map<String, Object> creator = commonRepository.selectOne("RollPlanCourseMapper.getRollPlanCourseByLogic", MapUtil.of("rollPlanId",params.get("rollPlanId"),"courseId",studentCourse.get("courseId")));

                    //期中笔试成绩
                    if (!ObjectUtil.isBlank(creator.get("midtermExamRate")) && "0.00".equals(creator.get("midtermExamRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("midtermExamWrittenScore")) && "0.0".equals(studentCourse.get("midtermExamWrittenScore").toString())){
                        studentCourse.put("midtermExamWrittenScore",null);
                    }
                    //期中笔口试成绩
                    if (!ObjectUtil.isBlank(creator.get("midtermOralRate")) && "0.00".equals(creator.get("midtermOralRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("midtermExamOralScore")) && "0.0".equals(studentCourse.get("midtermExamOralScore").toString())){
                        studentCourse.put("midtermExamOralScore",null);
                    }
                    //期末笔试成绩
                    if (!ObjectUtil.isBlank(creator.get("terminalExamRate")) && "0.00".equals(creator.get("terminalExamRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("terminalExamWrittenScore")) && "0.0".equals(studentCourse.get("terminalExamWrittenScore").toString())){
                        studentCourse.put("terminalExamWrittenScore",null);
                    }
                    //期末口试成绩
                    if (!ObjectUtil.isBlank(creator.get("terminalOralRate")) && "0.00".equals(creator.get("terminalOralRate").toString()) && !ObjectUtil.isBlank(studentCourse.get("terminalExamOralScore")) && "0.0".equals(studentCourse.get("terminalExamOralScore").toString())){
                        studentCourse.put("terminalExamOralScore",null);
                    }
                }
                termResult.put("examCourseList", studentCourseList);

                // 查询考勤
                List<Map<String, Object>> studentAttendanceList = commonRepository.selectList("RollReportMapper.selectTermAttendance", params);
                if (CollectionUtils.isNotEmpty(studentAttendanceList)) {
                    double totalLesson = Integer.parseInt(studentAttendanceList.get(0).get("totalLesson").toString());
                    double sickLesson = 0.0D;
                    double leaveLesson = 0.0D;
                    double lateLesson = 0.0D;
                    double truancyLesson = 0.0D;
                    for (int i = 0, size = studentAttendanceList.size(); i < size; i++) {

                        sickLesson = sickLesson + Double.parseDouble(studentAttendanceList.get(i).get("sickLesson").toString());
                        leaveLesson = leaveLesson + Double.parseDouble(studentAttendanceList.get(i).get("leaveLesson").toString());
                        lateLesson = lateLesson + Double.parseDouble(studentAttendanceList.get(i).get("lateLesson").toString());
                        truancyLesson = truancyLesson + Double.parseDouble(studentAttendanceList.get(i).get("truancyLesson").toString());
                    }
                    termResult.put("attendance", MapUtil.of("totalLesson", totalLesson,
                            "sickLesson", sickLesson,
                            "leaveLesson", leaveLesson,
                            "lateLesson", lateLesson,
                            "truancyLesson", truancyLesson));
                } else {
                    termResult.put("attendance", null);
                }

                // 查询注册时间
                Map<String, Object> schoolTerm = commonRepository.selectOne("RollReportMapper.selectSchoolTerm", params);
                termResult.put("schoolTerm", schoolTerm);

                // 查询老师寄语
                Map<String, Object> studentEvaluation = commonRepository.selectOne("RollReportMapper.selectStudentEvaluation", params);
                termResult.put("evaluation", studentEvaluation);
            }
        }
        return result;
    }


    /**
     * 获取学籍卡片
     *
     * @param params rollStudentId stage schoolId
     * @return
     */
    @Override
    public Map<String, Object> getCardReport(Map<String, Object> params) {
        if (!ObjectUtil.isValidId(params.get("rollStudentId"))
                || !ObjectUtil.isNumeric(params.get("stage"))
                || !ObjectUtil.isValidId(params.get("schoolId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        params.put("allCourse", true);

        // 查询学生数据
        params.put("isStageEvaluation", true);
        Map<String, Object> student = iRollStudentService.getStudentInfo(params);
        if (student == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "当前学生不存在");
        }

        // 查询当前学生所在年级
        Map<String, Object> grade = commonRepository.selectOne("RollReportMapper.selectStudentGrade", params);
        if (grade == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "当前学生没有年级");
        }
        params.putAll(grade);
        student.put("stage", grade.get("stage"));

        // 查询学校课程
        student.put("courseList", getSchoolCourseList(params));

        // 获取成绩
        List<Map<String, Object>> studentExamList = getExamCourseList(params);
        student.put("examCourseList", studentExamList);

        // 查询学年
        student.put("gradeYearList", GradeUtil.getGradeYearListByStage(grade));

        // 查询入学成绩
        List<Map<String, Object>> studentInitExamList = commonRepository.selectList("RollReportMapper.selectInitStudentExam", params);
        student.put("initExamCourseList", studentInitExamList);

        // 查询考勤
        List<Map<String, Object>> attendanceList = commonRepository.selectList("RollReportMapper.selectCardAttendance", params);
        student.put("attendanceList", attendanceList);

        // 查询模版
        Map<String, Object> template = commonRepository.selectOne("RollReportMapper.selectCardTemplate", params);
        student.put("template", template);

        return student;
    }

    @Override
    public Map<String, Object> getCardReportDownload(Map<String, Object> parameterMap) {
        Map<String,Object> map = getCardReport(parameterMap);
        //解密身份证
        SensitiveInfoUtil.aesDecrypt(map,"identityCardNoAes");
        //解密家长联系方式
        List<Map<String,Object>> parentList = (List)map.get("parentList");
        SensitiveInfoUtil.aesDecrypt(parentList,"tellPhoneAes");
        return map;
    }

    @Override
    public Map<String, Object> getEvaluationReportDownload(Map<String, Object> parameterMap) {
        Map<String, Object> map = getEvaluationReport(parameterMap);
        //解密家长联系方式
        List<Map<String, Object>> parentList = (List) map.get("parentList");
        SensitiveInfoUtil.aesDecrypt(parentList, "tellPhoneAes");
        return map;
    }

    /**
     * 获取成绩
     *
     * @param params
     * @return
     */
    private List<Map<String, Object>> getExamCourseList(Map<String, Object> params) {
        // 查询成绩
        List<Map<String, Object>> studentExamList = commonRepository.selectList("RollReportMapper.selectStudentExam", params);

        // 查询进行中素质报告的成绩
        List<Map<String, Object>> firstPlanExamCourseList = commonRepository.selectList("RollReportMapper.selectPlanExamCourseByFirst", params);
        List<Map<String, Object>> secondPlanExamCourseList = commonRepository.selectList("RollReportMapper.selectPlanExamCourseBySecond", params);

        // 归并学籍数据和素质报告数据
        Map<String, Map<String, Object>> planExamMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(studentExamList)) {
            //处理成绩和等级
            dealStudentExamList(studentExamList);
            // 学籍归档成绩
            studentExamList.forEach(item -> putItem(planExamMap, item));
        }

        if (CollectionUtils.isNotEmpty(secondPlanExamCourseList)) {
            //处理成绩和等级
            dealSecondPlanExamCourseList(secondPlanExamCourseList);
            // 第二学期素质报告成绩
            secondPlanExamCourseList.forEach(item -> putItem(planExamMap, item));
        }

        if (CollectionUtils.isNotEmpty(firstPlanExamCourseList)) {
            //处理成绩和等级
            dealFirstPlanExamCourseList(firstPlanExamCourseList);
            // 第一学期素质报告成绩
            firstPlanExamCourseList.forEach(item -> putItem(planExamMap, item));
        }

        List<Map<String, Object>> result = new ArrayList<>();
        planExamMap.forEach((key, item) -> result.add(item));

        return result;
    }

    private void dealFirstPlanExamCourseList(List<Map<String, Object>> firstPlanExamCourseList) {
        for (Map<String, Object> map : firstPlanExamCourseList){
            Integer examDisplay = Integer.valueOf(map.get("examDisplay").toString());
            Object oralDisplay = map.get("oralDisplay");
            //显示分数
            if (examDisplay.equals(1)){
                map.put("firstTermWrittenScoreLevel", map.get("firstTermWrittenScore"));
            }
            //显示分数
            if (oralDisplay!=null && oralDisplay.toString().equals("1")){
                map.put("firstTermOralScoreLevel", map.get("firstTermOralScore"));
            }
            if (map.get("resitWrittenScore") != null){
                map.put("resitWrittenScoreLevel", map.get("resitWrittenScore"));
            }
            if (map.get("resitOralScore") != null){
                map.put("resitOralScoreLevel", map.get("resitOralScore"));
            }
            //对于社区服务和社会实践课程 ，成绩都是A
            String courseName = String.valueOf(map.get("courseName"));
            if(courseName.contains("社会实践")){
                map.put("firstTermWrittenScoreLevel", "A");
            }
        }
    }

    private void dealSecondPlanExamCourseList(List<Map<String, Object>> secondPlanExamCourseList) {
        for (Map<String,Object> map : secondPlanExamCourseList){
            Integer examDisplay = Integer.valueOf(map.get("examDisplay").toString());
            Object oralDisplay = map.get("oralDisplay");
            //显示分数
            if (examDisplay.equals(1)){
                map.put("firstTermWrittenScoreLevel", map.get("firstTermWrittenScore"));
                map.put("secondTermWrittenScoreLevel", map.get("secondTermWrittenScore"));
                map.put("yearWrittenScoreLevel", map.get("yearWrittenScore"));
            }
            //显示分数
            if (oralDisplay != null && oralDisplay.toString().equals("1")){
                map.put("firstTermOralScoreLevel",map.get("firstTermOralScore"));
                map.put("secondTermOralScoreLevel",map.get("secondTermOralScore"));
                map.put("yearOralScoreLevel",map.get("yearOralScore"));
            }
            if (map.get("resitWrittenScore") != null){
                map.put("resitWrittenScoreLevel", map.get("resitWrittenScore"));
            }
            if (map.get("resitOralScore") != null){
                map.put("resitOralScoreLevel", map.get("resitOralScore"));
            }
            //对于社区服务和社会实践课程 ，成绩都是A
            String courseName = String.valueOf(map.get("courseName"));
            if(courseName.contains("社会实践")){
                map.put("firstTermWrittenScoreLevel", "A");
                map.put("secondTermWrittenScoreLevel", "A");
                map.put("yearWrittenScoreLevel", "A");
            }
        }
    }

    private void dealStudentExamList(List<Map<String,Object>> studentExamList) {
        for (Map<String,Object> map:studentExamList){
            String firstTermOralScore = map.get("firstTermOralScore")==null?null:map.get("firstTermOralScore").toString();
            if (!ObjectUtil.isBlank(firstTermOralScore) && !firstTermOralScore.equals("-1")){
                map.put("firstTermOralScoreLevel",firstTermOralScore);
            }
            String firstTermWrittenScore = map.get("firstTermWrittenScore")==null?null:map.get("firstTermWrittenScore").toString();
            if (!ObjectUtil.isBlank(firstTermWrittenScore) && !firstTermWrittenScore.equals("-1")){
                map.put("firstTermWrittenScoreLevel",firstTermWrittenScore);
            }
            String secondTermOralScore = map.get("secondTermOralScore")==null?null:map.get("secondTermOralScore").toString();
            if (!ObjectUtil.isBlank(secondTermOralScore) && !secondTermOralScore.equals("-1")){
                map.put("secondTermOralScoreLevel",secondTermOralScore);
            }
            String secondTermWrittenScore = map.get("secondTermWrittenScore")==null?null:map.get("secondTermWrittenScore").toString();
            if (!ObjectUtil.isBlank(secondTermWrittenScore) && !secondTermWrittenScore.equals("-1")){
                map.put("secondTermWrittenScoreLevel",secondTermWrittenScore);
            }
            String yearOralScore = map.get("yearOralScore")==null?null:map.get("yearOralScore").toString();
            if (!ObjectUtil.isBlank(yearOralScore) && !yearOralScore.equals("-1")){
                map.put("yearOralScoreLevel",yearOralScore);
            }
            String yearWrittenScore = map.get("yearWrittenScore")==null?null:map.get("yearWrittenScore").toString();
            if (!ObjectUtil.isBlank(yearWrittenScore) && !yearWrittenScore.equals("-1")){
                map.put("yearWrittenScoreLevel",yearWrittenScore);
            }
            String resitOralScore = map.get("resitOralScore")==null? null:map.get("resitOralScore").toString();
            if (!ObjectUtil.isBlank(resitOralScore) && !resitOralScore.equals("-1")){
                map.put("resitOralScoreLevel",resitOralScore);
            }
            String resitWrittenScore = map.get("resitWrittenScore")==null? null:map.get("resitWrittenScore").toString();
            if (!ObjectUtil.isBlank(resitWrittenScore) && !resitWrittenScore.equals("-1")){
                map.put("resitWrittenScoreLevel",resitWrittenScore);
            }

            String courseName = String.valueOf(map.get("courseName"));
            if(courseName.contains("社会实践")){
                if(!ObjectUtil.isBlank(map.get("firstTermWrittenScoreLevel"))){
                    map.put("firstTermWrittenScoreLevel", "A");
                }
                if(!ObjectUtil.isBlank(map.get("secondTermWrittenScoreLevel"))){
                    map.put("secondTermWrittenScoreLevel", "A");
                }
                if(!ObjectUtil.isBlank(map.get("yearWrittenScoreLevel"))){
                    map.put("yearWrittenScoreLevel", "A");
                }
            }
        }
    }

    private void putItem(Map<String, Map<String, Object>> planExamMap, Map<String, Object> item) {
        String key = item.get("gradeYear") + "-" + item.get("courseId");
        Map<String, Object> yearCourse = planExamMap.get(key);
        if (yearCourse == null) {
            // 当前学年的课程还没有成绩，直接加入
            planExamMap.put(key, item);
        } else {
            // 当前学年的课程已经有成绩，判断是否需要覆盖为空的成绩
            replaceScore(item, yearCourse);
        }
    }

    private void replaceScore(Map<String, Object> item, Map<String, Object> yearCourse) {
        // 第一学期
        yearCourse.putIfAbsent("firstTermOralScore", item.get("firstTermOralScore"));
        yearCourse.putIfAbsent("firstTermOralScoreLevel", item.get("firstTermOralScoreLevel"));
        yearCourse.putIfAbsent("firstTermWrittenScore", item.get("firstTermWrittenScore"));
        yearCourse.putIfAbsent("firstTermWrittenScoreLevel", item.get("firstTermWrittenScoreLevel"));
        yearCourse.putIfAbsent("firstTermFinalScore", item.get("firstTermFinalScore"));
        yearCourse.putIfAbsent("firstTermFinalScoreLevel", item.get("firstTermFinalScoreLevel"));

        // 第二学期
        yearCourse.putIfAbsent("secondTermOralScore", item.get("secondTermOralScore"));
        yearCourse.putIfAbsent("secondTermOralScoreLevel", item.get("secondTermOralScoreLevel"));
        yearCourse.putIfAbsent("secondTermWrittenScore", item.get("secondTermWrittenScore"));
        yearCourse.putIfAbsent("secondTermWrittenScoreLevel", item.get("secondTermWrittenScoreLevel"));
        yearCourse.putIfAbsent("secondTermFinalScore", item.get("secondTermFinalScore"));
        yearCourse.putIfAbsent("secondTermFinalScoreLevel", item.get("secondTermFinalScoreLevel"));

        // 学年
        yearCourse.putIfAbsent("yearOralScore", item.get("yearOralScore"));
        yearCourse.putIfAbsent("yearOralScoreLevel", item.get("yearOralScoreLevel"));
        yearCourse.putIfAbsent("yearWrittenScore", item.get("yearWrittenScore"));
        yearCourse.putIfAbsent("yearWrittenScoreLevel", item.get("yearWrittenScoreLevel"));
        yearCourse.putIfAbsent("yearFinalScore", item.get("yearFinalScore"));
        yearCourse.putIfAbsent("yearFinalScoreLevel", item.get("yearFinalScoreLevel"));

        // 重考
        yearCourse.putIfAbsent("resitOralScore", item.get("resitOralScore"));
        yearCourse.putIfAbsent("resitOralScoreLevel", item.get("resitOralScoreLevel"));
        yearCourse.putIfAbsent("resitWrittenScore", item.get("resitWrittenScore"));
        yearCourse.putIfAbsent("resitWrittenScoreLevel", item.get("resitWrittenScoreLevel"));
        yearCourse.putIfAbsent("resitFinalScore", item.get("resitFinalScore"));
        yearCourse.putIfAbsent("resitFinalScoreLevel", item.get("resitFinalScoreLevel"));
    }

    /**
     * 获取学校课程
     *
     * @param params stage schoolId
     * @return
     */
    private List<Map<String, Object>> getSchoolCourseList(Map<String, Object> params) {

        List<Map<String, Object>> result = new ArrayList<>();
        List<Map<String, Object>> configs = commonRepository.selectList("RollPlanCourseMapper.getRollPlanCourse", params);

        if(Boolean.parseBoolean(params.get("allCourse").toString())){
            //学籍卡片进来的，显示所有课程
            List<Map<String, Object>> schoolCourseList = commonRepository.selectList("RollReportMapper.selectRollSchoolCourse", params);
            if(CollectionUtils.isEmpty(schoolCourseList)){
                schoolCourseList = commonRepository.selectList("RollReportMapper.selectSchoolCourse", params);
            }

            Map<Long, Map<String, Object>> configsMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(configs)){
                configsMap = configs.stream().collect(toMap(s -> Long.valueOf(s.get("courseId").toString()), s -> s));
            }

            if (CollectionUtils.isNotEmpty(schoolCourseList)) {
                // 按课程标签分组
                Map<Long, List<Map<String, Object>>> schoolCourseLabelGroup = schoolCourseList.stream()
                        .collect(groupingBy(item -> Long.valueOf(item.get("schoolCourseLabelId").toString())));

                Map<Long, Map<String, Object>> finalConfigsMap = configsMap;
                // 语数英课程ID
                List baseCourseIds = Arrays.asList(2L, 3L, 4L, 12L, 13L, 14L, 22L, 23L, 24L);

                schoolCourseLabelGroup.forEach((schoolCourseLabelId, courseList) -> {
                    courseList = courseList.stream()
                            .peek(item -> item.put("isBase", baseCourseIds.contains(Long.parseLong(item.get("courseId").toString()))))
                            .peek(item -> item.put("oralDisplay", finalConfigsMap.get(Long.parseLong(item.get("courseId").toString()))==null? null: finalConfigsMap.get(Long.parseLong(item.get("courseId").toString())).get("oralDisplay")))
                            .collect(Collectors.toList());

                    Map<String, Object> labelMap = new HashMap<>();
                    labelMap.put("courseLabelId", schoolCourseLabelId);
                    labelMap.put("courseLabelName", courseList.get(0).get("courseLabelName"));
                    labelMap.put("courseList", courseList);

                    result.add(labelMap);
                });
                return result;
            }
        }else{
            //综合素质册进来
            List<Map<String, Object>> schoolCourseList = commonRepository.selectList("RollReportMapper.selectSchoolCourse", params);
            Map<Long, Map<String, Object>> courseLabelMap = schoolCourseList.stream()
                    .collect(Collectors.toMap(s -> Long.valueOf(s.get("courseId").toString()), s-> s));
            if (CollectionUtils.isNotEmpty(configs)) {
                // 按课程标签分组
                Map<Long, List<Map<String, Object>>> courseMap = configs.stream()
                        .peek(s -> s.put("courseLabelId", courseLabelMap.get(Long.valueOf(s.get("courseId").toString()))==null? 0:courseLabelMap.get(Long.valueOf(s.get("courseId").toString())).get("schoolCourseLabelId")))
                        .peek(s -> s.put("courseLabelName", courseLabelMap.get(Long.valueOf(s.get("courseId").toString()))==null? null: courseLabelMap.get(Long.valueOf(s.get("courseId").toString())).get("courseLabelName")))
                        .collect(groupingBy(s -> Long.valueOf(s.get("courseLabelId").toString())));

                courseMap.forEach((schoolCourseLabelId, courseList) -> {
                    Map<String, Object> labelMap = new HashMap<>();
                    labelMap.put("courseLabelId", schoolCourseLabelId);
                    labelMap.put("courseLabelName", courseList.get(0).get("courseLabelName"));
                    labelMap.put("courseList", courseList);
                    result.add(labelMap);
                });
                return result;
            }
        }
        return result;
    }
}
