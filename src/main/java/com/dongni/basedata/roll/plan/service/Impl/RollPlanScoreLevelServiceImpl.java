package com.dongni.basedata.roll.plan.service.Impl;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.roll.plan.service.IRollPlanScoreLevelService;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.Verify;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by scott
 * time: 17:19 2018/10/10
 * description:学籍任务
 */
@Service
public class RollPlanScoreLevelServiceImpl implements IRollPlanScoreLevelService {

    @Autowired
    private BaseDataRepository commonRepository;

    /**
     * 保存学籍任务成绩配置
     *
     * @return 保存状态
     */

    @Override
    public void updateRollPlanScoreLevel(Map<String, Object> parameterMap) {
        //参数校验
        Verify.of(parameterMap)
                .isValidId("rollPlanScoreLevelId")
                .verify();
        parameterMap.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.update("RollPlanScoreLevelMapper.updateRollPlanScoreLevel", parameterMap);

    }

 /**
     *保存学籍任务课程配置
     *
     * @return 保存状态
     */

    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public void updateRollPlanCourse(Map<String, Object> parameterMap) {
        //参数校验
        Verify.of(parameterMap)
                .isValidId("rollPlanCourseId")
                .verify();
        parameterMap.put("currentTime", DateUtil.getCurrentDateTime());
        Map<String, Object> creator = commonRepository.selectOne("RollPlanCourseMapper.getRollPlanCourseCreator", parameterMap);
        parameterMap.putAll(creator);
        commonRepository.delete("RollPlanCourseMapper.deletePlanCourse", parameterMap);
        commonRepository.insert("RollPlanScoreLevelMapper.updateRollPlanCourse", parameterMap);

    }


    /**
     * 获取学籍任务学生
     *
     * @param parameterMap gradeId [tagId]
     * @return 学籍任务学生
     */
    @Override
    public Map<String, Object> getRollPlanScoreLevel(Map<String, Object> parameterMap) {
        //参数校验
        Verify.of(parameterMap)
                .isValidId("rollPlanId")
                .isNumeric("gradeYear")
                .isNotBlank("gradeTerm")
                .isValidId("schoolId")
                .verify();
        Map<String, Object> rs = new HashMap<>();

        Map<String, Object> rollPlanInfo = commonRepository.selectOne("RollPlanMapper.getRollPlanBase", parameterMap);
        List<Map<String, Object>> scoreLevel = commonRepository.selectList("RollPlanScoreLevelMapper.getRollPlanScoreLevel", parameterMap);
        List<Map<String, Object>> courseInfo = commonRepository.selectList("RollPlanScoreLevelMapper.getRollCourseScoreRate", parameterMap);
        // 查询注册时间
        Map<String, Object> schoolTerm = commonRepository.selectOne("RollReportMapper.selectSchoolTerm", parameterMap);
        if (schoolTerm != null){
            rs.putAll(schoolTerm);
        }
        rs.put("scoreLevel", scoreLevel);
        rs.put("courseInfo", courseInfo);
        rs.putAll(rollPlanInfo);
        return rs;
    }
}
