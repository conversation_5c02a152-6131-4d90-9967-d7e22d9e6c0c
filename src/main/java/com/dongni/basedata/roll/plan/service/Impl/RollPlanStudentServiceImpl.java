package com.dongni.basedata.roll.plan.service.Impl;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.roll.plan.service.IRollPlanStudentService;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.common.report.excel.simple.SimpleExcelReport;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.export.roll.ExportExamRollService;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * Created by scott
 * time: 17:19 2018/10/10
 * description:学籍任务学生
 */
@Service
public class RollPlanStudentServiceImpl implements IRollPlanStudentService {

    @Autowired
    private BaseDataRepository commonRepository;
    @Autowired
    private RollPlanServiceImpl rollPlanService;
    @Autowired
    ExportExamRollService exportExamRollService;

    /**
     * 保存学籍任务学生
     *
     * @param parameterMap userId rollPlanId
     */
    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public void insertRollPlanStudent(Map<String, Object> parameterMap) {
        //参数校验
        Verify.of(parameterMap)
                .isValidId("rollPlanId")
                .isNotBlank("rollStudentIds").verify();
        parameterMap.put("currentTime", DateUtil.getCurrentDateTime());
        parameterMap.put("rollStudentIds", StringUtil.strToList(parameterMap.get("rollStudentIds").toString(),",", Long.class));
        List<Map<String, Object>> students = commonRepository.selectList("RollPlanStudentEvaluationMapper.getAddStudent", parameterMap);
        parameterMap.put("studentIds", students.stream().map(m -> m.get("studentId").toString()).collect(joining(",")));
        parameterMap.put("classIds", students.stream().map(m -> m.get("classId").toString()).collect(toList()));
        parameterMap.putAll(commonRepository.selectOne("RollPlanMapper.getRollPlanBase", parameterMap));
        commonRepository.insert("RollPlanStudentMapper.insertRollPlanStudent", parameterMap);
        parameterMap.put("students", students);

        //获取学籍任务课程
        List<Map<String, Object>> courseList =  commonRepository.selectList("RollPlanCourseMapper.getRollPlanCourse", parameterMap);
        //考试课程总分信息
        Map<Long, Map<Long, Document>> midtermExamIdCourseIdMap = new HashMap<>();
        Map<Long, Map<Long, Document>> terminalExamIdCourseIdMap = new HashMap<>();
        List<Map<String, Object>> studentResult;
        if (CollectionUtils.isNotEmpty(courseList)){
            //考试课程满分集合
            List<Document> midtermExamCourseList = new ArrayList<>();
            List<Document> terminalExamCourseList = new ArrayList<>();
            Set<String> mSet = new HashSet<>();
            Set<String> tSet = new HashSet<>();
            for (Map<String, Object> course : courseList) {
                int midtermExamSource = Integer.valueOf(course.get("midtermExamSource").toString());
                int terminalExamSource = Integer.valueOf(course.get("terminalExamSource").toString());
                if (midtermExamSource == 1){
                    Long midtermExamId = Long.valueOf(course.get("midtermExamId").toString());
                    Document courseMap = new Document();
                    courseMap.put("examId", midtermExamId);
                    courseMap.put("courseId", course.get("courseId"));
                    courseMap.put("fullMark", course.get("midtermExamFullMark"));
                    if (!mSet.contains(course.get("courseId").toString()+"_"+midtermExamId.toString())){
                        midtermExamCourseList.add(courseMap);
                        mSet.add(course.get("courseId").toString()+"_"+midtermExamId.toString());
                    }
                }

                if (terminalExamSource == 1){
                    Long terminalExamId = Long.valueOf(course.get("terminalExamId").toString());
                    Document courseMap = new Document();
                    courseMap.put("examId", terminalExamId);
                    courseMap.put("courseId", course.get("courseId"));
                    courseMap.put("fullMark", course.get("terminalExamFullMark"));
                    if (!tSet.contains(course.get("courseId").toString()+"_"+terminalExamId.toString())){
                        terminalExamCourseList.add(courseMap);
                        tSet.add(course.get("courseId").toString()+"_"+terminalExamId.toString());
                    }
                }

            }
            parameterMap.put("courses", courseList);
            if (CollectionUtils.isNotEmpty(midtermExamCourseList)){
                midtermExamIdCourseIdMap = midtermExamCourseList.stream().collect(groupingBy(m -> Long.valueOf(m.get("examId").toString()),
                        toMap(m -> Long.valueOf(m.get("courseId").toString()), m -> m)));
            }
            if (CollectionUtils.isNotEmpty(terminalExamCourseList)){
                terminalExamIdCourseIdMap = terminalExamCourseList.stream().collect(groupingBy(m -> Long.valueOf(m.get("examId").toString()),
                        toMap(m -> Long.valueOf(m.get("courseId").toString()), m -> m)));
            }

            //查找所有课程所有考试学生的成绩
            studentResult = exportExamRollService.getStudentExamInfo(parameterMap);
            //parameterMap.put("examCourseFullMark", examIdCourseIdMap);
            parameterMap.put("midtermExamCourseFullMark", midtermExamIdCourseIdMap);
            parameterMap.put("terminalExamCourseFullMark", terminalExamIdCourseIdMap);
            parameterMap.put("rollPlanScoreLevel", commonRepository.selectList("RollPlanScoreLevelMapper.getRollPlanScoreLevel", parameterMap));


            //生成学籍任务学生成绩
            rollPlanService.insertRollPlanStudentCourse(parameterMap, studentResult);


        }
        //新增学籍人物老师评价
        commonRepository.insert("RollPlanStudentEvaluationMapper.insertRollPlanStudentEvaluation", parameterMap);
    }

    /**
     * 获取学籍任务学生
     *
     * @param parameterMap rollPlanId
     * @return 学籍任务学生
     */
    @Override
    public Map<String, Object> getRollPlanStudent(Map<String, Object> parameterMap) {
        //参数校验
        if (parameterMap == null || !ObjectUtil.isValidId(parameterMap.get("userId"))
                || !ObjectUtil.isValidId(parameterMap.get("rollPlanId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        Map<String, Object> rs = new HashMap<>();
        Integer count = commonRepository.selectOne("RollPlanStudentMapper.getRollPlanStudentCount", parameterMap);
        rs.put("totalCount", count);
        if (count <= 0) {
            rs.put("student", new ArrayList<>());
        }
        List<Map<String, Object>> list = commonRepository.selectList("RollPlanStudentMapper.getRollPlanStudent", parameterMap);
        rs.put("student", list);
        return rs;
    }

    /**
     * 获取学籍任务学生详情
     *
     * @param parameterMap rollPlanStudentId
     * @return 学籍任务学生详情
     */
    @Override
    public Map<String, Object> getRollPlanStudentDetail(Map<String, Object> parameterMap) {
        //参数校验
        if (parameterMap == null || !ObjectUtil.isValidId(parameterMap.get("userId"))
                || !ObjectUtil.isValidId(parameterMap.get("rollPlanStudentId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        //是否修改
        Map<String, Object> before = commonRepository.selectOne("RollPlanStudentMapper.getRollPlanStudentDetail", parameterMap);
        if (ObjectUtil.isValueEquals(1, before.get("modifyStatus"))) {
            parameterMap.put("rollStudentId", before.get("rollStudentId"));
            //修改后
            Map<String, Object> after = commonRepository.selectOne("RollPlanStudentMapper.getRollStudentDetail", parameterMap);
            if (!ObjectUtil.isValueEquals(before.get("birthday"), after.get("birthday"))) {
                before.put("birthdayModified", after.get("birthday"));
            }
            if (!ObjectUtil.isValueEquals(before.get("ethnicityId"), after.get("ethnicityId"))) {
                before.put("ethnicityIdModified", after.get("ethnicityId"));
                before.put("ethnicityNameModified", after.get("ethnicityName"));
            }
            if (!ObjectUtil.isValueEquals(before.get("nativeAreaId"), after.get("nativeAreaId"))) {
                before.put("nativeAreaIdModified", after.get("nativeAreaId"));
                before.put("nativeAreaNameModified", after.get("nativeAreaName"));
            }
            if (!ObjectUtil.isValueEquals(before.get("registerAreaId"), after.get("registerAreaId"))) {
                before.put("registerAreaIdModified", after.get("registerAreaId"));
                before.put("registerAreaNameModified", after.get("registerAreaName"));
            }
            if (!ObjectUtil.isValueEquals(before.get("address"), after.get("address"))) {
                before.put("addressModified", after.get("address"));
            }
            if (!ObjectUtil.isValueEquals(before.get("politicalType"), after.get("politicalType"))) {
                before.put("politicalTypeModified", after.get("politicalType"));
            }
            if (!ObjectUtil.isValueEquals(before.get("community"), after.get("community"))) {
                before.put("communityModified", after.get("community"));
            }
            if (!ObjectUtil.isValueEquals(before.get("postcode"), after.get("postcode"))) {
                before.put("postcodeModified", after.get("postcode"));
            }

            List<Map<String, Object>> beforeParent = (List<Map<String, Object>>) before.get("parent");
            List<Map<String, Object>> afterParent = (List<Map<String, Object>>) after.get("parent");

            if (CollectionUtils.isEmpty(beforeParent) && CollectionUtils.isNotEmpty(afterParent)) {
                for (Map<String, Object> afterMap : afterParent) {
                    afterMap.put("appellationNameModified", afterMap.remove("appellationName"));
                    afterMap.put("parentNameModified", afterMap.remove("parentName"));
                    afterMap.put("tellPhoneModified", afterMap.remove("tellPhone"));
                    afterMap.put("workUnitModified", afterMap.remove("workUnit"));
                    afterMap.put("age", ObjectUtil.isBlank(afterMap.get("birthday")) ? "" : getYears(afterMap.get("birthday").toString()));
                }
                before.put("parent", afterParent);
            } else {
                //名称可以会重复。。。
                Map<Long, Map<String, Map<String, Object>>> collect = afterParent.stream().collect(
                        groupingBy(a -> Long.valueOf(a.get("appellationType").toString()),
                                toMap(a -> a.get("parentName").toString(), a -> a)));
                for (Map<String, Object> beforeMap : beforeParent) {
                    if (!afterParent.contains(beforeMap)) {//修改后与修改前不一样
                        Map<String, Map<String, Object>> afterMap = collect.getOrDefault(Long.valueOf(beforeMap.get("appellationType").toString()), new HashMap<>());
                        Map<String, Object> map = afterMap.getOrDefault(beforeMap.get("parentName").toString(), new HashMap<>());
                        if (!ObjectUtil.isValueEquals(map.get("appellationName"), beforeMap.get("appellationName"))) {
                            beforeMap.put("appellationNameModified", map.get("appellationName"));
                        }
                        if (!ObjectUtil.isValueEquals(map.get("parentName"), beforeMap.get("parentName"))) {
                            beforeMap.put("parentNameModified", map.get("parentName"));
                        }
                        if (!ObjectUtil.isValueEquals(map.get("tellPhone"), beforeMap.get("tellPhone"))) {
                            beforeMap.put("tellPhoneModified", map.get("tellPhone"));
                        }
                        if (!ObjectUtil.isValueEquals(map.get("workUnit"), beforeMap.get("workUnit"))) {
                            beforeMap.put("workUnitModified", map.get("workUnit"));
                        }
                    }

                    beforeMap.put("ageModified", ObjectUtil.isBlank(beforeMap.get("birthdayModified")) ? null : getYears(beforeMap.get("birthdayModified").toString()));
                    beforeMap.put("age", ObjectUtil.isBlank(beforeMap.get("birthday")) ? "" : getYears(beforeMap.get("birthday").toString()));

                }
            }
        }
        before.put("ageModified", ObjectUtil.isBlank(before.get("birthdayModified")) ? null : getYears(before.get("birthdayModified").toString()));
        before.put("age", ObjectUtil.isBlank(before.get("birthday")) ? null : getYears(before.get("birthday").toString()));
        return before;
    }

    /**
     * 获取未注册家长身份用户学生
     *
     * @param parameterMap gradeId tagId removeStudentId
     * @return 未注册家长身份用户学生
     */
    @Override
    public Map<String, Object> getUnregisteredStudent(Map<String, Object> parameterMap) {
        //参数校验
        if (parameterMap == null || !ObjectUtil.isValidId(parameterMap.get("userId"))
                || !ObjectUtil.isValidId(parameterMap.get("gradeId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        if (!ObjectUtil.isBlank(parameterMap.get("tagId"))) {
            parameterMap.put("tagId", parameterMap.get("tagId").toString().split(","));
        }
        if (!ObjectUtil.isBlank(parameterMap.get("removeStudentId"))) {
            parameterMap.put("studentId", parameterMap.get("removeStudentId").toString().split(","));
            parameterMap.put("removeStudentId", parameterMap.get("removeStudentId").toString().split(","));
        }
        Map<String, Object> rs = new HashMap<>();
        List<Long> ids = commonRepository.selectList("RollPlanStudentMapper.getStudentId", parameterMap);
        if(CollectionUtils.isEmpty(ids)){
            rs.put("totalCount", 0);
            rs.put("student", new ArrayList<>());
            return rs;
        }
        parameterMap.put("studentId", ids);
        Integer count = commonRepository.selectOne("RollPlanStudentMapper.getUnregisteredStudentCount", parameterMap);
        rs.put("totalCount", count);
        if (count <= 0) {
            rs.put("student", new ArrayList<>());
            return rs;
        }
        List<Map<String, Object>> student = commonRepository.selectList("RollPlanStudentMapper.getUnregisteredStudent", parameterMap);
        rs.put("student", student);
        return rs;
    }

    /**
     * 导出未注册家长身份用户学生
     *
     * @param params gradeId tagId removeStudentId
     * @return 未注册家长身份用户学生
     */
    @Override
    public String exportUnregisteredStudent(Map<String, Object> params) {
        //参数校验
        if (params == null || !ObjectUtil.isValidId(params.get("userId"))
                || !ObjectUtil.isValidId(params.get("gradeId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        params.remove("pageSize");
        Map<String, Object> unregisteredStudent = getUnregisteredStudent(params);
        List<Map<String, Object>> student = (List<Map<String, Object>>) unregisteredStudent.get("student");
        SimpleExcelHeader header = new SimpleExcelHeader(
                Arrays.asList("index", "className", "studentName", "studentNum"),
                Arrays.asList("序号", "班级", "姓名", "学号"));
        return new SimpleExcelReport(student, header).exportToFileStorage("未注册家长账号学生名单");
    }


    /**
     * 添加学生获取学生列表
     * isRollPlanStudent 0:获取学籍任务已添加学生, 1:获取学籍任务未添加学生
     * isRollStudent 0:获取所有学生, 1:获取学籍学生 2:获取无学籍学生
     *
     */
    @Override
    public Map<String, Object> appendStudentList(Map<String, Object> params){
        Verify.of(params)
                .isValidId("rollPlanId")
                .isValidId("gradeId")
                .isNotBlank("isRollPlanStudent")
                .isNotBlank("isRollStudent")
                .verify();
        int isRollPlanStudent = Integer.valueOf(params.get("isRollPlanStudent").toString());
        int isRollStudent = Integer.valueOf(params.get("isRollStudent").toString());
        Map<String, Object> rs = new HashMap<>();
        //获取学生信息
        List<Map<String, Object>> studentList = commonRepository.selectList("RollPlanStudentMapper.getAllStudent", params);

        //获取学籍任务未添加学生
        if (isRollPlanStudent == 1){
            studentList = studentList.stream().filter(s -> s.get("rollPlanStudentId") == null).collect(Collectors.toList());
            if (studentList.size() == 0){
                rs.put("totalCount", 0);
                rs.put("rollStudentCount", 0);
                rs.put("emptyRollStudentCount", 0);
                rs.put("student", Collections.emptyList());
                return rs;
            }
            //有学籍学号学生
            List<Map<String, Object>> rollStudentList = new ArrayList<>();
            //无学籍学号学生
            List<Map<String, Object>> emptyRollList = new ArrayList<>();
            for (Map<String, Object> student : studentList) {
                if (student.get("rollStudentId") != null){
                    rollStudentList.add(student);
                }else {
                    emptyRollList.add(student);
                }
            }
            rs.put("totalCount", studentList.size());
            rs.put("rollStudentCount", rollStudentList.size());
            rs.put("emptyRollStudentCount", emptyRollList.size());
            if (0 == isRollStudent){
                rs.put("student", studentList.stream().skip((int) params.get("currentIndex")).limit((int) params.get("pageSize")).collect(toList()));
            }else if (1 == isRollStudent){
                rs.put("student", rollStudentList.size() > 0 ?
                        rollStudentList.stream().skip((int) params.get("currentIndex")).limit((int) params.get("pageSize")).collect(toList())
                        : Collections.emptyList());
            }else {
                rs.put("student", emptyRollList.size() > 0 ?
                        emptyRollList.stream().skip((int) params.get("currentIndex")).limit((int) params.get("pageSize")).collect(toList())
                        : Collections.emptyList());
            }
        }else {
            studentList = studentList.stream().filter(s -> s.get("rollPlanStudentId") != null).collect(Collectors.toList());
            if (studentList.size() == 0){
                rs.put("totalCount", 0);
                rs.put("rollStudentCount", 0);
                rs.put("student", Collections.emptyList());
            }
            rs.put("totalCount", studentList.size());
            rs.put("rollStudentCount", studentList.size());
            rs.put("student", studentList
                    .stream().skip((int) params.get("currentIndex")).limit((int) params.get("pageSize")).collect(toList()));
        }

        return rs;
    }

    /**
     * 获取学生数量
     *
     */
    @Override
    public int getStudentCount(Map<String, Object> params) {
        Verify.of(params).isValidId("gradeId").verify();
        if (ObjectUtil.isValidId(params.get("rollPlanId"))){
            return commonRepository.selectOne("RollPlanStudentMapper.selectRollPlanStudentCount", params);
        }
        return commonRepository.selectOne("RollPlanStudentMapper.selectRollStudentCount", params);
    }

    /**
     * 当前时间与参数之间的年差距
     * @param date
     * @return 年差距
     */
    private int getYears(String date){
        Calendar cal = Calendar.getInstance();
        try {
            cal.setTime(DateUtil.parseDate(date));
        } catch (ParseException e) {
            LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
        }
        return DateUtil.getCurrentYear() - cal.get(Calendar.YEAR);
    }
}
