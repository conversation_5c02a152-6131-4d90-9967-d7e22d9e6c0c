package com.dongni.basedata.system.sso.liupanshui.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.system.account.enumeration.ClientTypeEnum;
import com.dongni.basedata.system.sso.liupanshui.service.LiuPanShuiAuthService;
import com.dongni.basedata.system.sso.service.SSOUserService;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 描述：六盘水单点登录controller
 * 创建人：jayfree
 * 创建时间：2019/8/05
 */
@RestController
public class LiuPanShuiAuthController extends BaseController {
    private final LiuPanShuiAuthService liuPanShuiAuthService;
    @Autowired
    private SSOUserService ssoUserService;
    @Autowired
    public LiuPanShuiAuthController(LiuPanShuiAuthService liuPanShuiAuthService) {
        this.liuPanShuiAuthService = liuPanShuiAuthService;
    }

    @DongniNotRequireLogin
    @GetMapping(value =BaseDataConfig.CONTEXT_PATH + "/system/auth/client/liupanshui")
    @DongniRequest(operationName = "第三方.单点登录.六盘水获取code")
    public Response auth(@RequestBody Map<String, Object> params) {
        return new Response(liuPanShuiAuthService.getCode(params));
    }

    @DongniNotRequireLogin
    @PostMapping(value =BaseDataConfig.CONTEXT_PATH + "/system/auth/liupanshui")
    @DongniRequest(operationName = "第三方.单点登录.六盘水")
    public Response processRequest(@RequestBody Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {

        params.put("referer", request.getHeader("referer"));
        Map<String, Object> accountMap = liuPanShuiAuthService.author(getParameterMap(), request, response);
        // 微信端 设置cookie
        if (accountMap != null && Integer.valueOf(params.get("clientType").toString()) == ClientTypeEnum.WE_CHAT.getType()) {
            ssoUserService.setCookieForWechat(response, accountMap);
        }
        if (accountMap != null) {
            return new Response(accountMap);
        }
        return new Response(ResponseStatusEnum.FAILURE, "单点登录用户信息获取失败");
    }
}
