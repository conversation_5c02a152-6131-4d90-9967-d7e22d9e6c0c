//package com.dongni.basedata.system.sso.tongan.cas.client.taglib;
//
//import javax.servlet.jsp.JspTagException;
//import javax.servlet.jsp.tagext.BodyTagSupport;
//
//public class ValidateUrlTag extends BodyTagSupport {
//   public int doEndTag() throws JspTagException {
//      String var1 = null;
//      if (super.bodyContent != null) {
//         var1 = super.bodyContent.getString();
//      }
//
//      if (var1 != null) {
//         var1 = var1.trim();
//      }
//
//      if (!(this.getParent() instanceof AuthTag)) {
//         throw new JspTagException("illegal cas:validateUrl outside cas:auth");
//      } else {
//         ((AuthTag)this.getParent()).setCasValidate(var1);
//         return 6;
//      }
//   }
//}
