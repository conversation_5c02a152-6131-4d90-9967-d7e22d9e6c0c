package com.dongni.basedata.system.sso.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.system.sso.service.ThirdRelationshipService;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.entity.Response;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by jayfree
 * time: 2019/6
 * description:第三方系统对接用户维护
 */
@RestController
@RequestMapping(value = BaseDataConfig.CONTEXT_PATH + "/system/auth/third/relationship")
public class ThirdRelationshipController extends BaseController {

    @Autowired
    private ThirdRelationshipService thirdRelationshipService;
    @Autowired
    private HttpServletRequest request;

    /**
     * 获取第三方系统列表信息
     */
    @GetMapping(value = "/company")
    @DongniRequest(operationName = "第三方.获取第三方系统列表信息", remark = {"TODO 权限"})
    public Response getThirdCompany() {
        return new Response(thirdRelationshipService.getThirdCompany(getParameterMap()));
    }
    /**
     * 获取第三方系统用户对应列表信息
     */
    @GetMapping(value = "/account")
    @DongniRequest(operationName = "第三方.获取第三方系统用户对应列表信息", remark = {"TODO 权限"})
    public Response getThirdAccountRelationList() {
        Map<String,Object> params = getParameterMap();
        params.put("requestUrl",request.getRequestURI());
        return new Response(thirdRelationshipService.getThirdAccountRelationList(params));
    }

    /**
     * 删除第三方系统用户对应
     * 非GET/POST治理 基础数据
     * 弃用, 请使用{@link ThirdRelationshipController#deleteThirdAccountRelation2()}
     */
    @Deprecated
    @DeleteMapping(value = "/account")
    @DongniRequest(operationName = "第三方.删除第三方系统用户对应(DELETE Deprecated)", remark = {"删除过期接口"})
    public Response deleteThirdAccountRelation() {
        thirdRelationshipService.deleteThirdAccountRelation(getParameterMap());
        return new Response();
    }

    /**
     * 删除第三方系统用户对应
     */
    @PostMapping(value = "/account/delete")
    @DongniRequest(operationName = "第三方.删除第三方系统用户对应", remark = {"TODO 权限"})
    public Response deleteThirdAccountRelation2() {
        thirdRelationshipService.deleteThirdAccountRelation(getParameterMap());
        return new Response();
    }

    /**
     * 更新第三方系统用户对应
     * 非GET/POST治理 基础数据
     * 弃用, 请使用 {@link ThirdRelationshipController#updateThirdAccountRelation2()}
     */
    @Deprecated
    @PutMapping(value = "/account")
    @DongniRequest(operationName = "第三方.更新第三方系统用户对应(PUT Deprecated)", remark = {"删除过期接口"})
    public Response updateThirdAccountRelation() {
        thirdRelationshipService.updateThirdAccountRelation(getParameterMap());
        return new Response();
    }

    /**
     * 更新第三方系统用户对应
     */
    @PostMapping(value = "/account/update")
    @DongniRequest(operationName = "第三方.更新第三方系统用户对应", remark = {"TODO 权限"})
    public Response updateThirdAccountRelation2() {
        thirdRelationshipService.updateThirdAccountRelation(getParameterMap());
        return new Response();
    }

    /**
     * 导出账号模板excel
     * params gradeId classId
     **/
    @GetMapping(value = "/school/account/export")
    @DongniRequest(operationName = "第三方.导出账号模板excel", remark = {"TODO 权限"})
    public Response exportThirdAccount() {
        Map<String, Object> params = getParameterMap();
        params.put("requestUrl",request.getRequestURI());
        return new Response(thirdRelationshipService.exportThirdAccount(params));
    }

    /**
     * 导入第三方系统用户对应列表excel
     * params importFile.xls 必选  thirdType
     */
    @PostMapping(value = "/school/account/import")
    @DongniRequest(operationName = "第三方.导入第三方系统用户对应列表excel", remark = {"TODO 权限"})
    public Response importStudent(@RequestBody Map<String,Object> params) {
        // 参数校验
        Verify.of(params)
                .isNotBlank("thirdCompanyName")
                .isNotBlank("filePath")
                .isNotBlank("thirdType").verify();
        List<String> header = new ArrayList<>();
        List<Map<String, String>> body = new ArrayList<>();
        FileStorageTemplate.get(params.get("filePath").toString(),file -> {
            // 获取文件头
            header.addAll(ExcelUtil.getHeader(file));
            // 获取文件体
            List<String> keys = Arrays.asList("index", "accountName", "userName", "userTypeName", "thirdId");
            body.addAll(ExcelUtil.getBody(file, keys));
        });

        int thirdType = Integer.valueOf(params.get("thirdType").toString());
        String thirdCompanyName = params.get("thirdCompanyName").toString();
        Map<String, Object> rs = new HashMap<>();

        // 参数校验体
        List<String> validator = new ArrayList<>();

        if (CollectionUtils.isEmpty(header) || header.size() < 5
                || !header.get(0).equals("序号")
                || !header.get(1).equals("账号")
                || !header.get(2).equals("用户名")
                || !header.get(3).equals("类别")
                || !header.get(4).equals(thirdCompanyName+"用户关键值")
                ) {
            validator.add("表头信息不完整，从左往右依次排列为：" +
                    "序号、账号、用户名、类别、"+thirdCompanyName+"用户关键值");
        }

        if (CollectionUtils.isEmpty(body)) {
            validator.add("表体内容不存在");
        }
        body.forEach(m -> {
            String row = "第" + m.get("rowNum") + "行,";
            if (ObjectUtil.isBlank(m.get("accountName"))) {
                validator.add(row + "懂你用户账号名未填写");
            }

            if (ObjectUtil.isBlank(m.get("thirdId"))) {
                validator.add(row + "第三方用户编号未填写");
            } else {
                m.put("thirdId", params.get("thirdType") + "_" + m.get("thirdId"));
            }

        });

        // 校验参数
        if (CollectionUtils.isNotEmpty(validator)) {
            rs.put("validator", validator);
            return new Response(rs);
        }

        // 封装参数
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("body", body);
        // 信息补充
        paramsMap.put("currentTime", DateUtil.getCurrentDateTime());
        paramsMap.put("userId", params.get("userId"));
        paramsMap.put("userName", params.get("userName"));
        paramsMap.put("thirdType", thirdType);
        // 数据库更新
        thirdRelationshipService.importThirdRelationship(paramsMap);

        return new Response();
    }


}
