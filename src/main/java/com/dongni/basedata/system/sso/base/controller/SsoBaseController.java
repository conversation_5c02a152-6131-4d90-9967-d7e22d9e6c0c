package com.dongni.basedata.system.sso.base.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.system.sso.base.service.AbstractSsoService;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单点登录通用Controller
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = BaseDataConfig.CONTEXT_PATH + "/system/auth")
public class SsoBaseController extends BaseController {

    @Autowired
    private List<AbstractSsoService> abstractSsoServiceList;

    /**
     * 单点登录，前端接收参数模式
     * @param params 前端传来的参数
     * @param request 请求
     * @param response 响应
     * @return 响应结果
     */
    @RequestMapping(value = "/sso", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    @DongniNotRequireLogin
    @DongniRequest(operationName = "第三方.单点登录.通用")
    public Response sso(@RequestBody Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {
        params.put("referer", request.getHeader("referer"));
        Map<String, Object> auth = new HashMap<>();
        String dictKey = MapUtil.getString(params, "dictKey", "nicezhuanye");

        for (AbstractSsoService abstractSsoService : abstractSsoServiceList) {
            if (abstractSsoService.getDictKeyList().contains(dictKey)) {
                auth = abstractSsoService.auth(params, request, response);
            }
        }
        return new Response(auth);
    }


}
