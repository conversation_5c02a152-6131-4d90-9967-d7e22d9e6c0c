package com.dongni.basedata.system.sso.longchaung.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.system.sso.enumeration.ThirdCompanyEnum;
import com.dongni.basedata.system.sso.longchaung.servcie.LongChuangAuthService;
import com.dongni.basedata.system.sso.service.ThirdResponseService;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020-02-28 11:26
 **/
@RestController
public class LongChuangAuthController extends BaseController {
    @Autowired
    private LongChuangAuthService longChuangAuthService;
    @Autowired
    private ThirdResponseService thirdResponseService;

    @DongniNotRequireLogin
    @PostMapping(value = BaseDataConfig.CONTEXT_PATH + "/system/auth/longchuang")
    @DongniRequest(operationName = "第三方.单点登录.龙创")
    public Response processRequestJSZX(@RequestBody Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {

        params.put("thirdType", ThirdCompanyEnum.LONG_CHUANG.getThirdType());
        params.put("openUserName", ThirdCompanyEnum.LONG_CHUANG.getOpenUserName());

        params.put("referer", request.getHeader("referer"));
        Map<String, Object> accountMap = longChuangAuthService.author(params, request, response);
        // 微信端 设置cookie
        return thirdResponseService.getResponse(params, response, accountMap);
    }
}

