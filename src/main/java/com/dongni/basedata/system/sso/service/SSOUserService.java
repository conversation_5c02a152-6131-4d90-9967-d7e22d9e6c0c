package com.dongni.basedata.system.sso.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.user.service.CommonUserService;
import com.dongni.basedata.system.account.enumeration.ClientTypeEnum;
import com.dongni.basedata.system.account.service.IAccountService;
import com.dongni.basedata.system.account.service.impl.LoginServiceImpl;
import com.dongni.basedata.system.account.util.AccountUtils;
import com.dongni.basedata.system.sso.utils.SsoTokenUtil;
import com.dongni.common.wechat.utils.WeChatUtils;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.mvc.service.LoginTokenService;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @Description  第三方登录 用户信息操作服务类
* @Date 10:42 22/10/2018
**/

@Service
public class SSOUserService {

    @Autowired
    private BaseDataRepository commonRepository;
    @Autowired
    private LoginServiceImpl loginService;
    @Autowired(required = false)
    private LoginTokenService loginTokenService;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private IAccountService iAccountService;

    /**
     * 获取用户信息
     *
     * @param accountInfo 单点登录返回的用户信息 userPhone, userType
     */
    public  Map<String, Object> getUserInfo(Map<String, Object> accountInfo) {

        // 数据校验
        if (accountInfo == null || StringUtils.isBlank(accountInfo.get("userPhone").toString())
                || StringUtils.isBlank(accountInfo.get("userType").toString())) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        SensitiveInfoUtil.aesEncrypt(accountInfo,"userPhone");
        Map<String, Object> accountMap = commonRepository.selectOne("BaseUserMapper.selectAccountByPhone", accountInfo);
        if (MapUtils.isEmpty(accountMap)) {
            return null;
        }

        // 查询并返回用户信息
        List<Map<String, Object>> list = commonRepository.selectList("UserMapper.getUser", accountMap);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Map<String, Object> userMap = list.get(0);
        return userMap;
    }

    /**
     * 获取用户信息
     *
     * @param accountInfo 单点登录返回的用户信息 accountId
     */
    public  Map<String, Object> getUserByAccountId(Map<String, Object> accountInfo) {
        // 查询并返回用户信息
        List<Map<String, Object>> list = commonRepository.selectList("UserMapper.getUser", accountInfo);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list = list.stream().filter(item -> Integer.valueOf(item.get("userStatus").toString()) != 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 获取用户信息
     *
     * @param params 单点登录返回的用户信息 userId
     */
    public  Map<String, Object> getUserByUserId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("userId")
                .verify();

        // 查询并返回用户信息
        List<Map<String, Object>> list = commonRepository.selectList("UserMapper.getUser", params);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 获取用户信息
     *
     * @param accountInfo 单点登录返回的用户信息 accountNameAes
     */
    public  Map<String, Object> getUserByAccountNameAes(Map<String, Object> accountInfo) {
        // 查询并返回用户信息
        List<Map<String, Object>> list = commonRepository.selectList("UserMapper.getUser", accountInfo);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list = list.stream().filter(item -> Integer.valueOf(item.get("userStatus").toString()) != 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }


    public void setToken(HttpServletRequest request, HttpServletResponse response, Map<String, Object> params, Map<String, Object> user) {
        setToken(request, response, params, user, 7200);
    }

    public void setToken(HttpServletRequest request,
                         HttpServletResponse response,
                         Map<String, Object> params,
                         Map<String, Object> user,
                         int expiry) {
        // 生成Token
        String token;
        Map<String, Object> tokenParams = SsoTokenUtil.setToken(request, params, user);
        token = loginTokenService.setToken(
                request,
                response,
                (Map)tokenParams.get("tokenInfoMap"),
                false,
                false,
                (Boolean) tokenParams.get("checkDeviceSecurity"),
                tokenParams.get("deviceIdentification").toString(),
                String.valueOf(1)
        );
        if (!ObjectUtil.isBlank(token)) {
            Cookie dongniLoginToken = new Cookie("dongniLoginToken", token);
            dongniLoginToken.setPath("/");
            dongniLoginToken.setMaxAge(expiry);  // TODO
            response.addCookie(dongniLoginToken);
        }

        // 不返回密码信息，避免密码泄露
        user.remove("password");
        user.remove("passwordModifyDateTime");
        user.put("dongniLoginToken", token);
    }

    public void setTokenForWechat(HttpServletRequest request, HttpServletResponse response, Map<String, Object> params, Map<String, Object> user) {
        // 生成Token
        String token;
        Map<String, Object> tokenParams = SsoTokenUtil.setToken(request, params, user);
        token = loginTokenService.setToken(
                request,
                response,
                (Map)tokenParams.get("tokenInfoMap"),
                false,
                false,
                (Boolean) tokenParams.get("checkDeviceSecurity"),
                tokenParams.get("deviceIdentification").toString(),
                String.valueOf(2)
        );
        if (!ObjectUtil.isBlank(token)) {
            Cookie dongniLoginToken = new Cookie("dongniLoginToken", token);
            dongniLoginToken.setPath("/");
            dongniLoginToken.setMaxAge(7200);  // TODO
            response.addCookie(dongniLoginToken);
        }

        // 不返回密码信息，避免密码泄露
        user.remove("password");
        user.remove("passwordModifyDateTime");
        user.put("dongniLoginToken", token);
    }

    /**
     * 获取用户信息
     *
     * thirdId 第三方系统用户对应id, thirdType 第三方产品类型
     */
    public  Map<String, Object> getThirdUserInfo(String thirdId, int thirdType) {

        // 数据校验
        if (thirdId == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        return commonRepository.selectOne("SSOUserMapper.getUserInfoByThirdId", MapUtil.of("thirdId", thirdId,"thirdType", thirdType));
    }

    /**
     * 微信端处理
     *
     * @param account
     * @param params
     */
    public void handleWeChat(Integer clientType, Map<String, Object> account, Map<String, Object> params) {
        if (clientType == ClientTypeEnum.WE_CHAT.getType()){
            // 获取微信的appId，从微信端登录需要使用的这些信息进行用户的openId获取及绑定
            account.put("appId", WeChatUtils.getWeChatConfig(params.get("referer")).get("appId"));
            List<Map<String, Object>> accountUsers = loginService.getAccountUser(account);
            if (accountUsers.isEmpty()) {
                return;
            }

            account.put("userList", accountUsers.get(0).get("userList"));
            //将账号下的返回的用户（1个）相关信息存放在redis中,3个月
            Map<String, String> userMap = new HashMap<>();
            userMap.put("userId", account.get("userId").toString());
            userMap.put("userName", account.get("userName").toString());
            userMap.put("relativeId", account.get("relativeId").toString());
            userMap.put("userType", account.get("userType").toString());
            userMap.put("accountId", account.get("accountId").toString());

            if (null != account.get("userLevel")) {
                userMap.put("userLevel", account.get("userLevel").toString());
            }

            JedisTemplate.execute(jedis -> {
                String key = JedisUtil.getKey("sysUserSimpleInfo", account.get("userId").toString());
                jedis.hmset(key, userMap);
                jedis.expire(key, 60 * 60 * 24 * 30 * 3);
                return null;
            });
        }
    }

    /**
     * 登录成功后设置cookie 仅针对微信
     * @param response
     * @param loginInfo
     */
    public void setCookieForWechat(HttpServletResponse response, Map<String, Object> loginInfo) {
        if (loginInfo.get("userList") != null) {
            List<Map> userList = (List<Map>) loginInfo.get("userList");
            // 保存用户信息到cookie中
            Map<String, Object> accountMap = new HashMap<>();
            accountMap.put("userId", userList.get(0).get("userId").toString());
            accountMap.put("userName", userList.get(0).get("userName").toString());
            accountMap.put("userType", userList.get(0).get("userType").toString());
            accountMap.put("dongniLoginToken", loginInfo.get("dongniLoginToken"));
            accountMap.put("accountId", userList.get(0).get("accountId").toString());
            AccountUtils.setAccountCookie(response, accountMap);
        } else if(loginInfo.containsKey("userId")) {
            // 保存用户信息到cookie中 todo 这里需要找黄鑫确认，比如说集美的微信单点返回的loginInfo里没有userList
            Map<String, Object> accountMap = new HashMap<>();
            accountMap.put("userId", loginInfo.get("userId").toString());
            accountMap.put("userName", loginInfo.get("userName").toString());
            accountMap.put("userType", loginInfo.get("userType").toString());
            accountMap.put("dongniLoginToken", loginInfo.get("dongniLoginToken"));
            accountMap.put("accountId", loginInfo.get("accountId").toString());
            AccountUtils.setAccountCookie(response, accountMap);
        }
    }


    /**
     * 通过userPhone查询用户
     * @param thirdUserInfo userPhone
     * @return
     */
    public Map<String, Object> getUserByPhone(Map<String, Object> thirdUserInfo) {
        SensitiveInfoUtil.aesEncrypt(thirdUserInfo,"userPhone");

        Map<String, Object> accountMap = commonRepository.selectOne("BaseUserMapper.selectAccountByPhone", thirdUserInfo);
        if (MapUtils.isEmpty(accountMap)) {
            return null;
        }

        // 查询并返回用户信息
        List<Map<String, Object>> list = commonRepository.selectList("UserMapper.getUser", accountMap);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    /**
     * 通过学生姓名 学号查询学生
     * @param params
     * @return
     */
    public Map<String, Object> getStudentUserInfo(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("studentName")
                .isNotBlank("studentNum")
                .verify();

        List<Map<String, Object>> studentUserList = commonRepository.selectList("CommonUserMapper.getStudentUserInfo", params);
        if (CollectionUtils.isEmpty(studentUserList)) {
            return null;
        }

        return studentUserList.get(0);
    }

    /**
     * 更新密码状态
     * @param params accountId passwordStatus
     */
    public void updateAccountPasswordStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("accountId")
                .isNotBlank("passwordStatus")
                .verify();
        iAccountService.updatePasswordStatus(params);
    }

    /**
     * 更新账号名称
     * @param params accountId accountName accountNameAes
     */
    public void updateAccountName(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("accountId")
                .isNotBlank("accountName")
                .isNotBlank("accountNameAes")
                .verify();

        params.put("userId", 1);
        params.put("userName", "升级学生单点");
        params.put("currentTime", DateUtil.getCurrentDateTime());

        commonRepository.update("AccountMapper.updateAccount", params);
    }
}
