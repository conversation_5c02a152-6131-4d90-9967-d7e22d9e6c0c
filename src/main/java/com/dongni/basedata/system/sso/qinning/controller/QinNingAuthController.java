package com.dongni.basedata.system.sso.qinning.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.system.sso.qinning.service.QinNingAuthService;
import com.dongni.basedata.system.sso.service.ThirdResponseService;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2019-12-24 16:31
 **/
@RestController
public class QinNingAuthController extends BaseController {
    @Autowired
    private QinNingAuthService qinNingAuthService;

    @Autowired
    private ThirdResponseService thirdResponseService;

    @DongniNotRequireLogin
    @PostMapping(value = BaseDataConfig.CONTEXT_PATH + "/system/auth/qinning")
    @DongniRequest(operationName = "第三方.单点登录.青柠")
    public Response processRequestBoXue(@RequestBody Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {

        params.put("referer", request.getHeader("referer"));
        Map<String, Object> accountMap = qinNingAuthService.author(params, request, response);
        // 微信端 设置cookie
        return thirdResponseService.getResponse(params, response, accountMap);
    }
}

