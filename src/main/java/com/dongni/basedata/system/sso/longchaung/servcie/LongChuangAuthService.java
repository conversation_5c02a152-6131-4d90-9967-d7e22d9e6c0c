package com.dongni.basedata.system.sso.longchaung.servcie;

import com.dongni.basedata.system.sso.haozhuanye.service.HaoZhuanYeAuthService;
import com.dongni.basedata.system.sso.service.SSOUserService;
import com.dongni.basedata.system.sso.service.ThirdLoginService;
import com.dongni.basedata.system.sso.utils.HttpClientUtils;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: Jianfeng
 * @create: 2020-02-28 11:27
 **/
@Service
public class LongChuangAuthService {

    private static final Logger log = LogManager.getLogger(HaoZhuanYeAuthService.class);

    @Autowired
    private ThirdLoginService thirdLoginService;
    @Autowired
    private SSOUserService ssoUserService;

    /**
     * 单点登录
     */
    public Map<String, Object> author(Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {
        Verify.of(params)
                .isValidId("clientType")
                .isNotBlank("access_token")
                .isNotBlank("terminal_type")
                .verify();

        // 请求获取用户信息
        String terminalType = params.get("terminal_type").toString();
        String accessToken = params.get("access_token").toString();

        String getUserInfoUrl = String.format("https://api.lunyu-edu.com/platform-neptune/user/detail?access_token=%s&terminal_type=%s", accessToken,terminalType);
        log.info("请求用户信息：url={}", getUserInfoUrl);
        Map userResponse = HttpClientUtils.get(getUserInfoUrl);

        // 用户信息校验
        Map<String,Object> userInfo = (Map<String, Object>) userResponse.get("data");
        if (userInfo == null
                || ObjectUtil.isBlank(userInfo.get("loginname"))) {
            log.error("单点登录失败：userInfo={}", userInfo);
            throw new CommonException(ResponseStatusEnum.PERMISSION_DENIED, "登录失败,用户信息不足");
        }

        String openUserName = params.get("openUserName").toString();
        int thirdType = Integer.parseInt(params.get("thirdType").toString());

        // 用户对应关系字段
        String thirdId = thirdType +"_"+ userInfo.get("loginname").toString();

        // 获取用户角色类型
        int userType = DictUtil.getDictValue("userType", "teacher");
        String roles = "教师";
        List<Map<String,Object>> roleIdList = (List<Map<String, Object>>) userInfo.get("roleIds");
        for (Map<String, Object> role : roleIdList) {
            if (ObjectUtil.isValueEquals(role.get("roleName").toString(), "学生")) {
                roles = "学生";
            }
        }

        // 如果是学生家长,需要获取信息自动注册
        Map<String, Object> thirdParams = new HashMap<>();
        String realName = userInfo.get("realName").toString();

        if (roles.equals("学生")){
            userType = DictUtil.getDictValue("userType", "student");
            thirdParams.put("studentName", realName);
            thirdParams.put("studentNum", thirdId);
            thirdParams.put("schoolName", "龙创");
        }

        thirdParams.put("thirdType", thirdType);
        thirdParams.put("openUserName", openUserName);
        thirdParams.put("userType", userType);
        thirdParams.put("thirdId", thirdId);

        // 获取懂你用户信息
        Map<String, Object> dongniUserInfo = thirdLoginService.getUserInfo(thirdParams);
        if (MapUtils.isEmpty(dongniUserInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请检查用户基础信息和第三方关键值是否导入");
        }

        // 不同客户端处理
        int clientType = Integer.parseInt(params.get("clientType").toString());
        ssoUserService.handleWeChat(clientType, dongniUserInfo, params);

        // 生成token
        ssoUserService.setToken(request, response, params, dongniUserInfo);

        return dongniUserInfo;
    }
}

