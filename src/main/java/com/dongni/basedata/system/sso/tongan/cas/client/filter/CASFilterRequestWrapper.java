package com.dongni.basedata.system.sso.tongan.cas.client.filter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

public class CASFilterRequestWrapper extends HttpServletRequestWrapper {
   public CASFilterRequestWrapper(HttpServletRequest var1) {
      super(var1);
   }

   public String getRemoteUser() {
      return (String)this.getSession().getAttribute("edu.yale.its.tp.cas.client.filter.user");
   }
}
