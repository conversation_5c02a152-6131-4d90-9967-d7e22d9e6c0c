package com.dongni.basedata.system.sso.base;

/**
 * <p>好专业角色枚举</p>
 *
 * <AUTHOR>
 * @since 2022/8/1 18:22
 */

public enum HaozhuanyeRoleEnum {

    /**
     * 学校管理员
     */
    SCHOOL_ADMIN("SCHO<PERSON>ADMIN", "学校管理员"),

    SCHOOL_MASTER("SCHOOLMASTER", "校长"),

    GRADE_ADMIN("GRADEADMIN", "年级主任"),

    CLASS_ADMIN("CLASSADMIN", "班主任"),

    SUBJECT_LEADER("SUBJECTLEADER", "备课组长"),

    EDUCATION_MASTER("EDUCATIONMASTER", "教务管理员"),

    TEACHING_TEAM_LEADER("TEACHINGTEAMLEADER", "教研组长"),

    TEACHING_DIRECTOR("TEACHINGDIRECTOR", "教导主任"),

    DATA_MAINTAIN("DATAMAINTAIN", "数据维护管理员"),

    PARENT("PARENT", "家长"),

    STUDENT("STUDENT", "学生"),

    SCHOOL_STAFF("SCHOOLSTAFF", "无执教老师"),

    CITY_ADMIN("CITYADMIN", "市级管理员"),

    PROVINCE_ADMIN("PROVINCEADMIN", "省级管理员"),

    ;

    private String roleID;

    private String roleName;

    HaozhuanyeRoleEnum(String roleID, String roleName) {
        this.roleID = roleID;
        this.roleName = roleName;
    }

    public String getRoleID() {
        return roleID;
    }

    public void setRoleID(String roleID) {
        this.roleID = roleID;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}
