package com.dongni.basedata.system.sso.tongan.cas.client;

import com.dongni.basedata.system.sso.tongan.cas.util.SecureURL;
import org.xml.sax.Attributes;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;
import org.xml.sax.helpers.DefaultHandler;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParserFactory;
import java.io.IOException;
import java.io.StringReader;

public class ServiceTicketValidator {
   private String casValidateUrl;
   private String proxyCallbackUrl;
   private String st;
   private String service;
   private String pgtIou;
   private String user;
   private String errorCode;
   private String errorMessage;
   private String entireResponse;
   private boolean renew;
   private boolean attemptedAuthentication;
   private boolean successfulAuthentication;

   // $FF: synthetic method
   private void finit$() {
      this.renew = false;
   }

   // $FF: synthetic method
   static String access$5(ServiceTicketValidator var0) {
      return var0.errorCode;
   }

   // $FF: synthetic method
   static String access$4(ServiceTicketValidator var0) {
      return var0.errorMessage;
   }

   // $FF: synthetic method
   static boolean access$3(ServiceTicketValidator var0) {
      return var0.successfulAuthentication;
   }

   // $FF: synthetic method
   static String access$2(ServiceTicketValidator var0) {
      return var0.pgtIou;
   }

   public static void main(String[] var0) throws Exception {
      System.setProperty("java.protocol.handler.pkgs", "com.sun.net.ssl.internal.www.protocol");
      ServiceTicketValidator var1 = new ServiceTicketValidator();
      var1.setCasValidateUrl("https://portal1.wss.yale.edu/cas/serviceValidate");
      var1.setProxyCallbackUrl("https://portal1.wss.yale.edu/casProxy/receptor");
      var1.setService(var0[0]);
      var1.setServiceTicket(var0[1]);
      var1.validate();
      System.out.println(var1.getResponse());
      System.out.println();
      if (var1.isAuthenticationSuccesful()) {
         System.out.println("user: " + var1.getUser());
         System.out.println("pgtIou: " + var1.getPgtIou());
      } else {
         System.out.println("error code: " + var1.getErrorCode());
         System.out.println("error message: " + var1.getErrorMessage());
      }

   }

   public void setCasValidateUrl(String var1) {
      this.casValidateUrl = var1;
   }

   public String getCasValidateUrl() {
      return this.casValidateUrl;
   }

   public void setProxyCallbackUrl(String var1) {
      this.proxyCallbackUrl = var1;
   }

   public void setRenew(boolean var1) {
      this.renew = var1;
   }

   public String getProxyCallbackUrl() {
      return this.proxyCallbackUrl;
   }

   public void setServiceTicket(String var1) {
      this.st = var1;
   }

   public void setService(String var1) {
      this.service = var1;
   }

   public String getUser() {
      return this.user;
   }

   public String getPgtIou() {
      return this.pgtIou;
   }

   public boolean isAuthenticationSuccesful() {
      return this.successfulAuthentication;
   }

   public String getErrorMessage() {
      return this.errorMessage;
   }

   public String getErrorCode() {
      return this.errorCode;
   }

   public String getResponse() {
      return this.entireResponse;
   }

   public void validate() throws IOException, SAXException, ParserConfigurationException {
      if (this.casValidateUrl != null && this.st != null) {
         this.clear();
         this.attemptedAuthentication = true;
         StringBuffer var1 = new StringBuffer();
         var1.append(this.casValidateUrl);
         if (this.casValidateUrl.indexOf(63) == -1) {
            var1.append('?');
         } else {
            var1.append('&');
         }

         var1.append("service=" + this.service + "&ticket=" + this.st);
         if (this.proxyCallbackUrl != null) {
            var1.append("&pgtUrl=" + this.proxyCallbackUrl);
         }

         if (this.renew) {
            var1.append("&renew=true");
         }

         String var2 = var1.toString();
         String var3 = SecureURL.retrieve(var2);
         this.entireResponse = var3;
         if (var3 != null) {
            XMLReader var4 = SAXParserFactory.newInstance().newSAXParser().getXMLReader();
            var4.setFeature("http://xml.org/sax/features/namespaces", false);
            var4.setContentHandler(this.newHandler());
            var4.parse(new InputSource(new StringReader(var3)));
         }

      } else {
         throw new IllegalStateException("must set validation URL and ticket");
      }
   }

   protected DefaultHandler newHandler() {
      return new ServiceTicketValidator.Handler();
   }

   protected void clear() {
      this.user = this.pgtIou = this.errorMessage = null;
      this.attemptedAuthentication = false;
      this.successfulAuthentication = false;
   }

   public ServiceTicketValidator() {
      this.finit$();
   }

   protected class Handler extends DefaultHandler {
      protected static final String AUTHENTICATION_SUCCESS = "cas:authenticationSuccess";
      protected static final String AUTHENTICATION_FAILURE = "cas:authenticationFailure";
      protected static final String PROXY_GRANTING_TICKET = "cas:proxyGrantingTicket";
      protected static final String USER = "cas:user";
      protected StringBuffer currentText;
      protected boolean authenticationSuccess;
      protected boolean authenticationFailure;
      protected String netid;
      protected String pgtIou;
      protected String errorCode;
      protected String errorMessage;

      // $FF: synthetic method
      private void finit$() {
         this.currentText = new StringBuffer();
         this.authenticationSuccess = false;
         this.authenticationFailure = false;
      }

      public void startElement(String var1, String var2, String var3, Attributes var4) {
         this.currentText = new StringBuffer();
         if (var3.equals("cas:authenticationSuccess")) {
            this.authenticationSuccess = true;
         } else if (var3.equals("cas:authenticationFailure")) {
            this.authenticationFailure = true;
            this.errorCode = var4.getValue("code");
            if (this.errorCode != null) {
               this.errorCode = this.errorCode.trim();
            }
         }

      }

      public void characters(char[] var1, int var2, int var3) {
         this.currentText.append(var1, var2, var3);
      }

      public void endElement(String var1, String var2, String var3) throws SAXException {
         if (this.authenticationSuccess) {
            if (var3.equals("cas:user")) {
               ServiceTicketValidator.this.user = this.currentText.toString().trim();
            }

            if (var3.equals("cas:proxyGrantingTicket")) {
               this.pgtIou = this.currentText.toString().trim();
            }
         } else if (this.authenticationFailure && var3.equals("cas:authenticationFailure")) {
            this.errorMessage = this.currentText.toString().trim();
         }

      }

      public void endDocument() throws SAXException {
         if (this.authenticationSuccess) {
            ServiceTicketValidator.this.user = ServiceTicketValidator.this.user;
            ServiceTicketValidator.this.pgtIou = this.pgtIou;
            ServiceTicketValidator.this.successfulAuthentication = true;
         } else {
            if (!this.authenticationFailure) {
               throw new SAXException("no indication of success of failure from CAS");
            }

            ServiceTicketValidator.this.errorMessage = this.errorMessage;
            ServiceTicketValidator.this.errorCode = this.errorCode;
            ServiceTicketValidator.this.successfulAuthentication = false;
         }

      }

      Handler() {
         this.finit$();
      }
   }
}
