package com.dongni.basedata.system.sso.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.system.account.service.IUserService;
import com.dongni.common.report.excel.ExcelReport;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.common.report.excel.simple.SimpleExcelReport;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @Description  第三方登录 用户信息操作服务类
* @Date 10:42 22/10/2018
**/

@Service
public class ThirdRelationshipService {

    @Autowired
    private BaseDataRepository commonRepository;
    @Autowired
    private IUserService userService;


    /**
     * 获取第三方系统列表信息
     */
    public List<Map<String, Object>> getThirdCompany(Map<String, Object> params) {
        return commonRepository.selectList("ThirdRelationshipMapper.getThirdCompanyName");
    }

    /**
     * 获取第三方系统用户对应列表信息
     */
    public Map<String, Object> getThirdAccountRelationList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isNotBlank("thirdType")
                .verify();
        List<Map<String, Object>> rsList = new ArrayList<>();
        Map<String, Object> rs = new HashMap<>();

        // 查询accountName
        if (!ObjectUtil.isBlank(params.get("search"))) {
            params.put("searchAes", SensitiveInfoUtil.aesEncrypt(params.get("search").toString()));
        }

        //老师
        List<Map<String, Object>> teacherList =  commonRepository.selectList("ThirdRelationshipMapper.getThirdAccountTeacherRelationList", params);
         //学生
        List<Map<String, Object>> studentList = commonRepository.selectList("ThirdRelationshipMapper.getThirdAccountStudentRelationList", params);
         //家长
        List<Map<String, Object>> parentList = commonRepository.selectList("ThirdRelationshipMapper.getThirdAccountParentRelationList", params);
         //校长
        List<Map<String, Object>> principalList = commonRepository.selectList("ThirdRelationshipMapper.getThirdAccountPrincipalRelationList", params);
         //教导主任
        List<Map<String, Object>> directorList = commonRepository.selectList("ThirdRelationshipMapper.getThirdAccountDirectorRelationList", params);
         //教务
        List<Map<String, Object>> schoolInstructorList = commonRepository.selectList("ThirdRelationshipMapper.getThirdAccountSchoolInstructorRelationList", params);
        rsList.addAll(teacherList);
        rsList.addAll(studentList);
        rsList.addAll(parentList);
        rsList.addAll(principalList);
        rsList.addAll(directorList);
        rsList.addAll(schoolInstructorList);
        
        rs.put("totalCount", rsList.size());
        if (!ObjectUtil.isBlank(params.get("currentIndex"))&&!ObjectUtil.isBlank(params.get("pageSize"))){
            rsList = rsList.stream()
                    .skip((int) params.get("currentIndex")).limit((int) params.get("pageSize"))
                    .collect(Collectors.toList());
        }
        //是否经过校验，是则显示解密后的信息
        if (userService.checkUser(params)){
            SensitiveInfoUtil.aesDecrypt(rsList,"accountNameAes");
        }
        rs.put("list", rsList);
        return rs;
    }


    /**
     * 导入第三方系统用户对应列表excel
     * params importFile.xls 必选  thirdType
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    public void importThirdRelationship(Map<String, Object> params) {
        Verify.of(params).isNotBlank("body")
                .isNotBlank("thirdType").verify();

        List<Map> body = (List)params.get("body");
        if (CollectionUtils.isNotEmpty(body)){
            SensitiveInfoUtil.aesEncrypt(body, "accountName");
        }

        //先删 （以导入的为准）
        commonRepository.delete("ThirdRelationshipMapper.deleteCompanyAccountRelation", params);
        commonRepository.insert("ThirdRelationshipMapper.insertThirdRelationship", params);
        commonRepository.update("ThirdRelationshipMapper.updateAccountPasswordStatus", params);
    }

    /**
     * 删除第三方系统用户对应
     */
    public void deleteThirdAccountRelation(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("thirdType")
                //.isNotBlank("accountNames")
                .isNotBlank("accountIds")
                .verify();
        params.put("accountNameList", getAccountNameAes(params));
        commonRepository.delete("ThirdRelationshipMapper.deleteAccountRelation", params);
    }

    /**
     * 更新第三方系统用户对应
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    public void updateThirdAccountRelation(Map<String, Object> params) {
        Verify.of(params)
               // .isNotBlank("accountName")
                .isNotBlank("sourceAccountId")
                .isNotBlank("thirdType")
                .isNotBlank("thirdId")
                .verify();
        params.put("accountIds", params.get("sourceAccountId"));
        params.put("accountId", params.get("sourceAccountId"));
        //先删
        deleteThirdAccountRelation(params);
        //插入
        //获取账号信息
        Map<String, Object> accountInfo = commonRepository.selectOne("ThirdRelationshipMapper.getAccountInfoByAccountId", params);
        accountInfo.put("thirdId", params.get("thirdType") + "_" + params.get("thirdId"));
        params.putAll(accountInfo);
        List<Map<String, Object>> body = new ArrayList<>();
        Map<String, Object> info = new HashMap<>(params);
        params.put("currentTime", DateUtil.getCurrentDateTime());
        body.add(info);
        params.put("body", body);
        commonRepository.update("ThirdRelationshipMapper.updateAccountPasswordStatus",params);
        commonRepository.insert("ThirdRelationshipMapper.insertThirdRelationship", params);
    }

    /**
     * 导出模板excel
     * params gradeId classId
     *
     */
    public String exportThirdAccount(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isValidId("schoolId")
                .isNotBlank("schoolName")
                .isNotBlank("thirdCompanyName")
                .isNotBlank("thirdType")
                .verify();
        Map<String, Object> rs =  getThirdAccountRelationList(params);
        params.remove("currentIndex");
        params.remove("pageSize");

        // 数据查询
        List list = (List)rs.get("list");
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        //是否经过校验，是则数据解密
        if (userService.checkUser(params)){
            SensitiveInfoUtil.aesDecrypt(list, "accountNameAes");
        }
        // 文件生成
        SimpleExcelHeader headers = new SimpleExcelHeader(Arrays.asList("index", "accountName", "userName", "userTypeName", "thirdId"),
                Arrays.asList("序号", "账号", "用户名", "类别", params.get("thirdCompanyName").toString()+"用户关键值"));
        // 数据查询
        List<Map<String, Object>> listExport = (List)list;
        listExport.forEach(a->{
            if (a.get("thirdId") != null) {
                String thirdId = a.get("thirdId").toString();
                a.put("thirdId", thirdId.substring(thirdId.indexOf("_") + 1));
            }
        });
        ExcelReport report = new SimpleExcelReport(listExport, headers);

        return report.exportToFileStorage(params.get("thirdCompanyName").toString());
    }

    /**
    * @Description  根据 accountIds 获取用户加密账号
    * @Param [params]
    * @return java.util.List<java.lang.String>
    **/
    private List<String> getAccountNameAes(Map<String, Object> params){
        Verify.of(params).isNotBlank("accountIds").verify();
        List<Long> accountIdList = StringUtil.strToList(params.get("accountIds").toString(), ",", Long.class);
        return commonRepository.selectList("ThirdRelationshipMapper.getAccountNameAesByAccountId", MapUtil.of("accountIdList", accountIdList));
    }

}
