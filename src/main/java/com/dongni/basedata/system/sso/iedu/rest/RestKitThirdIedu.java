package com.dongni.basedata.system.sso.iedu.rest;

import com.dongni.basedata.system.sso.iedu.util.IeduUtil;
import com.dongni.common.rest.kit.impl.RestKitTemplate;
import com.dongni.commons.utils.MD5Util;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2022/5/30 18:29
 */

@Component
public class RestKitThirdIedu extends RestKitTemplate {

    private static final Logger log = LogManager.getLogger(RestKitThirdIedu.class);

    private final String NONCESTR = "dongni";

    @Override
    protected void before(String url, Map<String, Object> queryParams, HttpHeaders headers) {
        setHeaders(queryParams, headers);
        setQueryParams(queryParams);
    }

    @Override
    protected <T> T after(String url, Map<String, Object> queryParams, ResponseEntity<T> responseEntity) {
        return responseEntity.getBody();
    }

    /**
     * 设置通用的请求参数 例：page pageSize
     * @param queryParams
     */
    private void setQueryParams(Map<String, Object> queryParams) {
        if (ObjectUtil.isNotBlank(queryParams.get("thirdSchoolId"))) {
            queryParams.put("orgId", queryParams.get("thirdSchoolId"));
        }
    }

    /**
     * 设置headers
     * @param headers
     */
    private void setHeaders(Map<String, Object> queryParams, HttpHeaders headers) {
        if (queryParams.containsKey("accessToken")) {
            headers.put("Authorization", Collections.singletonList("Bearer " + queryParams.get("accessToken")));
            queryParams.remove("accessToken");
        }
        headers.put("appid", Collections.singletonList(IeduUtil.getAppId()));
        headers.put("noncestr", Collections.singletonList(NONCESTR));
        addTimestampAndSignature(headers);
    }

    /**
     * 签名生成规则 MD5(appid+timestamp+appKey)
     * @param headers
     */
    private void addTimestampAndSignature(HttpHeaders headers) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signature = MD5Util.md5(IeduUtil.getAppId() + timestamp + IeduUtil.getAppKey());
        headers.put("timestamp", Collections.singletonList(timestamp));
        headers.put("signature", Collections.singletonList(signature));
    }
}
