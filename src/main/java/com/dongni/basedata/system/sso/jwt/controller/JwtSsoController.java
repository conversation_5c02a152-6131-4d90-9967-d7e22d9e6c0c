package com.dongni.basedata.system.sso.jwt.controller;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.basedata.system.sso.jwt.service.JwtSsoService;
import com.dongni.basedata.system.sso.service.ThirdResponseService;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020-04-26 10:57
 **/
@RestController
public class JwtSsoController extends BaseController {
    @Autowired
    private JwtSsoService jwtSsoService;

    @Autowired
    private ThirdResponseService thirdResponseService;

    @DongniNotRequireLogin
    @PostMapping(value = BaseDataConfig.CONTEXT_PATH + "/system/auth/jwt")
    @DongniRequest(operationName = "第三方.jwt验证")
    public Response processRequestJSZX(@RequestBody Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {

        params.put("referer", request.getHeader("referer"));
        Map<String, Object> accountMap = jwtSsoService.jwtSso(params, request, response);
        // 微信端 设置cookie
        return thirdResponseService.getResponse(params, response, accountMap);
    }
}

