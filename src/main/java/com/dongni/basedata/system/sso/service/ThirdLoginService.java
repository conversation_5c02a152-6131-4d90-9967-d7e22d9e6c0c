package com.dongni.basedata.system.sso.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.school.student.service.impl.StudentServiceImpl;
import com.dongni.basedata.system.account.service.IAccountService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.MD5Util;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import static com.dongni.commons.utils.SensitiveInfoUtil.aesEncrypt;

/**
 * <AUTHOR>
 * @Description 第三方登录 获取用户信息(老师  学生  家长)
 * @Date 2019/08/05
 **/

@Service
public class ThirdLoginService {

    @Autowired
    private BaseDataRepository repository;
    @Autowired
    private SSOUserService ssoUserService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private StudentServiceImpl studentService;

    /**
     * 获取用户信息
     *
     * @param params 单点登录返回的用户信息 userPhone, userType
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    public Map<String, Object> getUserInfo(Map<String, Object> params) {

        Verify.of(params)
                .isNotBlank("thirdId")
                .isNotBlank("thirdType")
                .isNumeric("userType")
                .verify();

        int userType = Integer.valueOf(params.get("userType").toString());

        String thirdId = params.get("thirdId").toString();

        // 校验账号
        Map<String, Object> accountInfo = repository.selectOne("ThirdLoginMapper.getAccountName", params);
        Map<String, Object> userInfo = new HashMap<>();

        if (MapUtils.isNotEmpty(accountInfo)) {
            userInfo = ssoUserService.getUserByAccountId(MapUtil.of("accountId", accountInfo.get("accountId"), "userType", userType));
        }

        if (MapUtils.isEmpty(accountInfo)) {
            Map<String, Object> role = null;
            if (userType == DictUtil.getDictValue("userType", "student")) {
                role = repository.selectOne("ThirdLoginMapper.getStudent", params);
            }
            if (userType == DictUtil.getDictValue("userType", "parent")) {
                if (!ObjectUtil.isBlank(params.get("parentPhone"))) {
                    aesEncrypt(params, "parentPhone");
                    role = repository.selectOne("ThirdLoginMapper.getParent", params);
                }
            }

            if (MapUtils.isNotEmpty(role)) {
                //判断角色信息
                //学生: 如果存在学生信息 则自动注册
                //初始化学生注册信息
                role.put("userType", userType);
                role.put("thirdId", thirdId);
                role.put("thirdType", params.get("thirdType"));
                Random random = new Random();
                String accountName = params.get("openUserName") + thirdId + random.nextInt(100000);
                if (ObjectUtil.isBlank(accountName)) {
                    Map<String, Object> thirdCompany = repository.selectOne("ThirdRelationshipMapper.getThirdCompany", params);
                    accountName = thirdCompany.get("thirdCompanyName") + thirdId + random.nextInt(100000);
                }
                role.put("accountName", accountName);
                //注册
                accountInfo = userRegister(role);
                userInfo = ssoUserService.getUserByAccountId(MapUtil.of("accountId", accountInfo.get("accountId")));
            }

        }
        return userInfo;
    }

    /**
     * @return void
     * @Description 学生自动注册
     * @Param [params]  studentId accountName 未加密
     **/
    private Map<String, Object> userRegister(Map<String, Object> params) {
        // 参数校验
        Verify.of(params)
                .isValidId("relativeId")
                .isNotBlank("accountName")
                .isNotBlank("roleName")
                .isNumeric("userType")
                .isNumeric("thirdType")
                .verify();
        //加密
        aesEncrypt(params, "accountName");
        String currentTime = DateUtil.getCurrentDateTime();
        // 密码加密，设置 salt
        String pwd = MD5Util.md5BySalt(StringUtils.substring(params.get("accountNameSrc").toString(), -6));

        Map<String, Object> user = new HashMap<>();
        user.put("relativeId", params.get("relativeId"));
        user.put("userStatus", "1");
        user.put("roleName", params.get("roleName"));
        user.put("userType", params.get("userType"));
        user.put("nickname", params.get("nickname") == null ? params.get("roleName") : params.get("nickname"));
        user.put("currentTime", currentTime);
        user.put("userId", 1);
        user.put("userName", "admin");

        Map<String, Object> account = new HashMap<>();
        account.put("accountName", params.get("accountName"));
        account.put("password", pwd);
        account.put("accountNameAes", params.get("accountNameAes"));
        account.put("accountStatus", "1");
        account.put("passwordStatus", "1");
        account.put("weChatOpenId", "");
        account.put("currentTime", currentTime);
        account.put("userId", 1);
        account.put("userName", "admin");

        Map<String, Object> thirdRelative = new HashMap<>();
        thirdRelative.put("accountName", params.get("accountName"));
        thirdRelative.put("accountNameAes", params.get("accountNameAes"));
        thirdRelative.put("thirdId", params.get("thirdId"));
        thirdRelative.put("thirdType", params.get("thirdType"));
        thirdRelative.put("currentTime", currentTime);
        thirdRelative.put("userId", 1);
        thirdRelative.put("userName", "admin");

        //插入数据
        repository.insert("ThirdLoginMapper.insertAccount", account);
        user.put("accountId", account.get("accountId"));
        repository.insert("ThirdLoginMapper.insertUser", user);
        repository.insert("ThirdLoginMapper.insertThirdRelationship", thirdRelative);

        return account;
    }

    /**
     * 获取用户信息 此方法不区分用户类型 遇到多角色的情况返回第一个
     *
     * @param params 单点登录返回的用户信息 userPhone
     */
    public Map<String, Object> getUserInfo4NoUserType(Map<String, Object> params) {

        Verify.of(params)
                .isNotBlank("thirdId")
                .isNotBlank("thirdType")
                .verify();

        // 校验账号
        Map<String, Object> accountInfo = repository.selectOne("ThirdLoginMapper.getAccountName", params);
        Map<String, Object> userInfo = new HashMap<>();

        if (MapUtils.isNotEmpty(accountInfo)) {
            userInfo = ssoUserService.getUserByAccountId(MapUtil.of("accountId", accountInfo.get("accountId")));
        }

        return userInfo;
    }

    /**
     * 通过手机号查询家长信息
     * @param params phoneAes
     * @return
     */
    public Map<String, Object> getAccountForParentByPhoneAes(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("phoneAes")
                .verify();

        return repository.selectOne("ParentMapper.getAccountForParentByPhoneAes", params);
    }

    /**
     * 插入账号
     * @param params accountName accountNameAes accountStatus passwordStatus password weChatOpenId userId userName updateTime
     */
    public void insertAccount(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("accountName")
                .isNotBlank("accountNameAes")
                .isNotBlank("accountStatus")
                .isNotBlank("passwordStatus")
                .isNotBlank("password")
                .isNotNull("weChatOpenId")
                .isNotBlank("userId")
                .isNotBlank("userName")
                .isNotBlank("updateTime")
                .verify();
        accountService.insertJwtAccount(params);
    }

    /**
     * 新增家长信息
     * @param params parentName accountName accountNameAes userId userName currentTime
     */
    public void insertParent(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("parentName")
                .isNotBlank("parentPhone")
                .isNotBlank("parentPhoneAes")
                .isNotBlank("userId")
                .isNotBlank("userName")
                .isNotBlank("currentTime")
                .verify();

        repository.insert("ThirdLoginMapper.insertParent", params);
    }

    /**
     * 新增家长学生绑定关系
     * @param params parentId studentId userId userName currentTime
     */
    public void insertParentStudent(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("parentId")
                .isNotBlank("studentId")
                .isNotBlank("userId")
                .isNotBlank("userName")
                .isNotBlank("currentTime")
                .verify();

        repository.insert("ThirdLoginMapper.insertParentStudent", params);
    }

    /**
     * 新增用户
     * @param params
     */
    public void insertUser4Jwt(Map<String, Object> params) {
        repository.insert("ThirdLoginMapper.insertUser4Jwt", params);
    }

    /**
     * 根据第三方查询学生信息
     * @param params list: studentName studentNum schoolName
     * @return
     */
    public List<Map<String, Object>> getStudentListByLogic(Map<String, Object> params) {
        return studentService.getStudentListByLogic(params);
    }

    /**
     * 查询家长所绑定的学生
     * @param params parentId
     * @return
     */
    public List<Map<String, Object>> getStudentListByParentId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("parentId")
                .verify();

       return  repository.selectList("ThirdLoginMapper.getStudentByParentId", params);
    }

    /**
     * 删除家长和学生绑定关系
     * @param params list [parentStudentId]
     */
    public void deleteParentStudentByIds(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .verify();
        repository.delete("ThirdLoginMapper.batchDeleteParentStudent", params);
        repository.delete("ThirdLoginMapper.batchDeleteUserForParent", params);
    }


    public void insertAccount4Sso(Map<String, Object> params) {
        repository.insert("ThirdLoginMapper.insertAccount4Sso", params);
    }

    public void insertParent4Sso(Map<String, Object> params) {
        repository.insert("ThirdLoginMapper.insertParent", params);
    }

    public void insertParentStudent4Sso(Map<String, Object> params) {
        repository.insert("ThirdLoginMapper.insertParentStudent4Sso", params);
    }

    public void insertUser4Sso(Map<String, Object> params) {
        repository.insert("ThirdLoginMapper.insertUser4Sso", params);
    }

    /**
     * 获取账号信息
     * @param saveParentParams accountNameAes
     */
    public Map<String, Object> getAccountByPhoneAes(Map<String, Object> saveParentParams) {
        return repository.selectOne("AccountMapper.getAccountForExist", saveParentParams);
    }
}
