package com.dongni.basedata.system.sso.jwt.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.system.sso.boxuebao.service.BoXueBaoAuthService;
import com.dongni.basedata.system.sso.service.SSOUserService;
import com.dongni.basedata.system.sso.service.ThirdLoginService;
import com.dongni.basedata.system.thirdparty.openapi.utils.TokenUtils;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import io.jsonwebtoken.Claims;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 第三方通过jwt方式单点登录
 * @author: Jianfeng
 * @create: 2020-04-24 11:02
 **/
@Service
public class JwtSsoService {
    private static final Logger log = LogManager.getLogger(JwtSsoService.class);

    @Autowired
    private ThirdLoginService thirdLoginService;
    @Autowired
    private SSOUserService ssoUserService;
    @Autowired
    private BaseDataRepository repository;

    public Map<String, Object> jwtSso(Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {
        Verify.of(params)
                .isValidId("clientType")
                .isNotBlank("token")
                .isNotBlank("appId")
                .verify();

        Map<String,Object> thirdCompany = repository.selectOne("ThirdRelationshipMapper.getThirdCompany", params);
        if (MapUtils.isEmpty(thirdCompany)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "第三方系统不存在");
        }

        String token = params.get("token").toString();
        Claims claims = TokenUtils.parseJwtAlg(token, thirdCompany.get("secret").toString());

        log.warn("MT测试输出token：token={} claims={}", token, claims);

        // 用户信息校验
        if (claims == null
                || ObjectUtil.isBlank(claims.get("keyword"))
                || ObjectUtil.isBlank(claims.get("userType"))
                || ObjectUtil.isBlank(claims.get("userName"))) {
            log.warn("第三方单点登录失败：token={} claims={}", token, claims);
            throw new CommonException(ResponseStatusEnum.PERMISSION_DENIED, "登录失败,用户信息不足");
        }

        String keyword = claims.get("keyword").toString();
        String userType = claims.get("userType").toString();
        String userName = claims.get("userName").toString();
        Object schoolName = claims.get("schoolName");
        Object phone = claims.get("phone");
        Object paramsInClaims = claims.get("params");

        // 第三方——懂你 用户对应关系字段
        int thirdType = Integer.parseInt(thirdCompany.get("thirdType").toString());
        String thirdId = thirdType +"_"+ keyword;

        // 如果是学生家长,需要获取信息自动注册
        Map<String, Object> thirdParams = new HashMap<>();

        if ("4".equals(userType)){
            thirdParams.put("studentName", userName);
            thirdParams.put("studentNum", thirdId);
            thirdParams.put("schoolName", ObjectUtil.isBlank(schoolName) ? thirdCompany.get("thirdCompanyName") : schoolName);
        }else if ("5".equals(userType)){
            thirdParams.put("parentPhone", ObjectUtil.isBlank(phone) ? thirdId : phone);
        }

        thirdParams.put("thirdType", thirdType);
        thirdParams.put("openUserName", thirdCompany.get("thirdCompanyName"));
        thirdParams.put("userType", userType);
        thirdParams.put("thirdId", thirdId);

        // 获取懂你用户信息
        Map<String, Object> dongniUserInfo = thirdLoginService.getUserInfo(thirdParams);
        if (MapUtils.isEmpty(dongniUserInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请检查用户基础信息和第三方关键值是否导入");
        }

        // 不同客户端处理
        int clientType = Integer.parseInt(params.get("clientType").toString());
        ssoUserService.handleWeChat(clientType, dongniUserInfo, params);

        // 生成token
        ssoUserService.setToken(request, response, params, dongniUserInfo);

        dongniUserInfo.put("params", paramsInClaims);
        return dongniUserInfo;
    }
}

