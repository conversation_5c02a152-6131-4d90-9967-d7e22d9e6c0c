package com.dongni.basedata.system.sso.wanxin.rest;

import com.dongni.common.rest.kit.impl.RestKitTemplate;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @description:
 * @author: Ji<PERSON>feng
 * @create: 2020-05-19 17:43
 **/
@Service
public class RestKitChangSha extends RestKitTemplate {
    @Override
    protected void before(String url, Map<String, Object> queryParams, HttpHeaders headers) {

    }

    @Override
    protected <T> T after(String url, Map<String, Object> queryParams, ResponseEntity<T> responseEntity) {
        if (!responseEntity.getStatusCode().is2xxSuccessful()) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "第三方请求异常");
        }

        return responseEntity.getBody();
    }
}
