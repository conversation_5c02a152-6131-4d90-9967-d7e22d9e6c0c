package com.dongni.basedata.system.thirdparty.openapi.utils;

import com.dongni.basedata.system.thirdparty.openapi.constants.OpenApiConstants;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.wooutils.json.JSON;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.SignatureException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * token生成器
 *
 * <AUTHOR>
 * @date 2018/07/05 09:31
 */
public class TokenUtils {

    private static final Logger log = LoggerFactory.getLogger(TokenUtils.class);

    /**
     * 根据userId使用JWT的方式生成token
     * @param userId 用户Id
     * @param userName 用户姓名
     * @return token
     */
    public static Map<String, Object> create(String userId, String userName) {
        Map<String, String> payloadMap = new HashMap<>();
        payloadMap.put("userId", userId);
        payloadMap.put("userName", userName);

        // 将签发时间放入载荷中编码，用于验证token是否过期
        payloadMap.put("iat", LocalDateTime.now().toString());
        try {
            String payload = new ObjectMapper().writeValueAsString(payloadMap);
            String token = Jwts.builder()
                    .setSubject(payload)
                    .signWith(SignatureAlgorithm.HS512, OpenApiConstants.JIKE_SECRET)
                    .compact();

            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("expires", OpenApiConstants.TOKEN_EXPIRES);
            return result;
        } catch (JsonProcessingException e) {
            LoggerFactory.getLogger(TokenUtils.class).error(e.getMessage(), e);
            throw new CommonException(ResponseStatusEnum.ENCRYPT_ERROR);
        }
    }

    /**
     * 创建JWT token
     * @param id 目前暂时使用userId
     * @param issuer 目前暂时设置为userName
     * @param ttlMillis token的存活时间，单位为毫秒
     * @return token
     */
    public static Map<String, Object> createJWT(String id, String issuer, long ttlMillis) {

        //The JWT signature algorithm we will be using to sign the token
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);

        //We will sign our JWT with our ApiKey secret
        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(OpenApiConstants.COMMMON_SECRET);
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());

        //Let's set the JWT Claims
        JwtBuilder builder = Jwts.builder().setId(id)
                .setIssuer(issuer)
                .setIssuedAt(now)
                .signWith(signatureAlgorithm, signingKey);

        //if it has been specified, let's add the expiration
        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date exp = new Date(expMillis);
            builder.setExpiration(exp);
        }

        //Builds the JWT and serializes it to a compact, URL-safe string
        Map<String, Object> result = new HashMap<>();
        result.put("token", builder.compact());
        result.put("expires", OpenApiConstants.TOKEN_EXPIRES);
        return result;
    }

    /**
     * 创建JWT token
     * @param id 目前暂时使用userId
     * @param issuer 目前暂时设置为userName
     * @param ttlMillis token的存活时间，单位为毫秒
     * @return token
     */
    public static Map<String, Object> createJWT(String id, String issuer, Map<String, Object> claims, long ttlMillis) {

        //The JWT signature algorithm we will be using to sign the token
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;

        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);

        //We will sign our JWT with our ApiKey secret
        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary("B6DD949AE772A0B42A85321AB227D929");
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());

        //Let's set the JWT Claims
        JwtBuilder builder = Jwts.builder().setId(id)
                .setIssuer(issuer)
                .setClaims(claims)
                .setIssuedAt(now)
                .signWith(signatureAlgorithm, signingKey);

        //if it has been specified, let's add the expiration
        if (ttlMillis >= 0) {
            long expMillis = nowMillis + ttlMillis;
            Date exp = new Date(expMillis);
            builder.setExpiration(exp);
        }

        //Builds the JWT and serializes it to a compact, URL-safe string
        Map<String, Object> result = new HashMap<>();
        result.put("token", builder.compact());
        result.put("expires", OpenApiConstants.TOKEN_EXPIRES);
        return result;
    }

    /**
     * 解析JWT token
     * @param jsonWebToken, parseSecret:解析秘钥
     * @return token
     */
    public static Claims parseJWT(String jsonWebToken, String parseSecret) {

        Claims claims;
        log.info("token:=========={} 解析开始", jsonWebToken);
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(parseSecret);
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
        try {
            claims = Jwts.parser()
                    .setSigningKey(signingKey)
                    .parseClaimsJws(jsonWebToken).getBody();
            log.info("token:{} 解析成功:{}", jsonWebToken, claims);
            return claims;
        } catch (ExpiredJwtException e) {
            LoggerFactory.getLogger(TokenUtils.class).error(e.getMessage(), e);
            log.info("token过期===================");
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "连接超时");
        } catch (SignatureException e) {
            LoggerFactory.getLogger(TokenUtils.class).error(e.getMessage(), e);
            log.info("token签名校验失败===================");
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "签名校验失败");
        } catch (Exception e) {
            LoggerFactory.getLogger(TokenUtils.class).error(e.getMessage(), e);
            log.info("token解析异常===================");
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未知错误");
        }
    }


    /**
     * 根据header判断加密算法来解析JWT token
     * @param jsonWebToken, parseSecret:解析秘钥
     * @return token
     */
    public static Claims parseJwtAlg(String jsonWebToken, String parseSecret) {
        if (ObjectUtil.isBlank(jsonWebToken) || ObjectUtil.isBlank(parseSecret)) {
            return null;
        }

        // 解析header
        String[] split = jsonWebToken.split("\\.");
        Base64.Decoder decoder = Base64.getDecoder();
        String headerString = new String(decoder.decode(split[0]), StandardCharsets.UTF_8);

        // 获取加密算法
        Map<String, Object> header = JSON.parseToMap(headerString);
        String alg = header.get("alg") == null ? "none" : header.get("alg").toString();
        SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.forName(alg);

        Claims claims;
        log.info("token:=========={} 解析开始", jsonWebToken);

        byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(parseSecret);
        Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
        try {
            claims = Jwts.parser()
                    .setSigningKey(signingKey)
                    .parseClaimsJws(jsonWebToken).getBody();
            log.info("token:{} 解析成功:{}", jsonWebToken, claims);
            return claims;
        } catch (ExpiredJwtException e) {
            LoggerFactory.getLogger(TokenUtils.class).error(e.getMessage(), e);
            log.info("token过期===================");
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "连接超时");
        } catch (SignatureException e) {
            LoggerFactory.getLogger(TokenUtils.class).error(e.getMessage(), e);
            log.info("token签名校验失败===================");
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "签名校验失败");
        } catch (Exception e) {
            LoggerFactory.getLogger(TokenUtils.class).error(e.getMessage(), e);
            log.info("token解析异常===================");
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "jwt未知错误");
        }
    }

    public static void main(String[] args) {
        Map<String, Object> claims = new HashMap<>(4);
        claims.put("keyword", "tongan350212198510310010");
        claims.put("userType", 3);
        claims.put("userName", "陈花");

        Map<String, Object> params = new HashMap<>();
        params.put("redirectPath", "/v2/homework/list/homework-list");
        claims.put("params", params);

        Map<String, Object> token = createJWT("1", "陈花", claims, 1000 * 7200L);
        System.out.println("token = " + token);
    }

}
