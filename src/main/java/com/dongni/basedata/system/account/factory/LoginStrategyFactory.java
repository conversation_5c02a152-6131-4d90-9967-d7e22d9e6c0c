package com.dongni.basedata.system.account.factory;

import com.dongni.basedata.system.account.handler.LoginStrategyHandler;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className LoginStrategyFactory
 * @since 2025/3/10 15:49
 */
@Component
public class LoginStrategyFactory implements InitializingBean {

    private static final Map<Integer, LoginStrategyHandler> LOGIN_STRATEGY_HANDLER_MAP = new HashMap<>();

    @Autowired
    private ApplicationContext appContext;

    /**
     * 根据提交类型获取对应的处理器
     *
     * @param loginStrategyType 登录策略类型 1-验证码登录 2-账号密码登录
     * @return 提交类型对应的处理器
     */
    public LoginStrategyHandler getHandler(Integer loginStrategyType) {
        LoginStrategyHandler loginStrategyHandler = LOGIN_STRATEGY_HANDLER_MAP.get(loginStrategyType);
        if (loginStrategyHandler == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "不支持的登录方式");
        }
        return loginStrategyHandler;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        appContext.getBeansOfType(LoginStrategyHandler.class)
                .values()
                .forEach(handler -> LOGIN_STRATEGY_HANDLER_MAP.put(handler.getLoginStrategyType(), handler));
    }

}
