package com.dongni.basedata.system.account.enumeration;

/**
 * 客户端类型
 *
 * <AUTHOR>
 * @date 2018/11/17 14:56
 */
public enum ClientTypeEnum {

    PC(1, "PC"),
    WE_CHAT(2, "微信");

    private int type;

    private String name;

    ClientTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
