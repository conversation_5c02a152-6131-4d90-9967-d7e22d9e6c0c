package com.dongni.basedata.system.account.service.impl;

import com.dongni.basedata.admin.service.impl.BaseSchoolServiceImpl;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.school.parent.service.ParentService;
import com.dongni.basedata.school.student.util.StudentUtil;
import com.dongni.basedata.system.account.enumeration.ClientTypeEnum;
import com.dongni.basedata.system.account.enumeration.UserStatusEnum;
import com.dongni.basedata.system.account.model.SysAccount;
import com.dongni.basedata.system.account.service.AccountUpgradeService;
import com.dongni.basedata.system.account.service.IAccountCheckService;
import com.dongni.basedata.system.account.service.IAccountService;
import com.dongni.basedata.system.account.service.IUserService;
import com.dongni.basedata.system.account.util.AccountUtils;
import com.dongni.basedata.system.sso.service.SSOUserService;
import com.dongni.basedata.system.sso.service.ThirdResponseService;
import com.dongni.common.server.CurrentServerUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.common.wechat.utils.WeChatTypeEnumeration;
import com.dongni.common.wechat.utils.WeChatUtils;
import com.dongni.commons.entity.Response;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.mvc.service.LoginTokenService;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.IdentifyCodeUtil;
import com.dongni.commons.utils.MD5Util;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.homework.service.HomeworkHandleService;
import com.dongni.tiku.common.util.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 登录接口实现
 * Created by 马腾 on 2016/1/13.
 */
@Service
public class AccountServiceImpl implements IAccountService {

    private static final Logger log = LogManager.getLogger(AccountServiceImpl.class);

    @Autowired
    private BaseDataRepository commonRepository;

    @Autowired
    private IAccountCheckService accountCheckService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HomeworkHandleService homeworkHandleService;

    @Autowired
    private UserMembershipService userMembershipService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private PasswordService passwordService;

    @Autowired
    private VerifyService verifyService;

    @Autowired
    private LoginTokenService loginTokenService;

    @Autowired
    private AccountWeChatService accountWeChatService;
    
    @Autowired
    private SysRegisterServiceImpl sysRegisterService;
    @Autowired
    private AccountUpgradeService accountUpgradeService;
    @Autowired
    private BaseSchoolServiceImpl baseSchoolService;
    @Autowired
    private ThirdResponseService thirdResponseService;
    @Autowired
    private SSOUserService ssoUserService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private ParentService parentService;
    @Autowired
    private IUserService userService;

    /**
     * 获取默认密码 截取后六位
     *   对于账号小于6位的，取账号本身
     * @param accountName 账号名
     * @return 密码
     */
    public static String getDefaultPassword(String accountName) {
        if (StringUtils.isBlank(accountName)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号名必须提供");
        }
        int accountNameLength = accountName.length();
        String password = accountName;
        if (accountNameLength > 6) {
            password = accountName.substring(accountNameLength - 6);
        }
        return password;
    }

    @Override
    public Map<String, Object> insertAccount(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNotBlank("accountNameSrc")
                .verify();

        String accountNameSrc = params.get("accountNameSrc").toString();
        String accountName = SensitiveInfoUtil.hide(accountNameSrc);
        String accountNameAes = SensitiveInfoUtil.aesEncrypt(accountNameSrc);

        Map<String, Object> accountInfo = MapUtil.copy(params, "userId", "userName", "accountNameSrc");
        String currentDateTime = DateUtil.getCurrentDateTime();
        accountInfo.put("createTime", currentDateTime);
        accountInfo.put("updateTime", currentDateTime);
        accountInfo.put("accountName", accountName);
        accountInfo.put("accountNameAes", accountNameAes);
        accountInfo.put("accountStatus", DictUtil.getDictValue("accountStatus", "on"));
        String password = null;
        if (params.get("password") == null) {
            password = getDefaultPassword(accountNameSrc);
        } else {
            password = params.get("password").toString();
        }
        accountInfo.put("password", MD5Util.md5BySalt(password));
        Integer passwordStatus = MapUtil.getInt(params, "passwordStatus", DictUtil.getDictValue("passwordStatus", "init"));
        accountInfo.put("passwordStatus", passwordStatus);

        // put accountId
        commonRepository.insert("AccountMapper.insertAccount", accountInfo);
        accountInfo.remove("password");
        return accountInfo;
    }

    @Override
    public void setAccountStatusOn(Map<String, Object> params) {
        params.put("accountStatus", DictUtil.getDictValue("accountStatus", "on"));
        setAccountStatus(params);
    }

    @Override
    public void setAccountStatusOff(Map<String, Object> params) {
        params.put("accountStatus", DictUtil.getDictValue("accountStatus", "off"));
        setAccountStatus(params);
    }

    private void setAccountStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNotBlank("accountId")
                .isNumeric("accountStatus")
                .verify();
        Map<String, Object> updateParams = MapUtil.copy(params,
                "userId", "userName", "accountId", "accountStatus"
        );
        ParamsUtil.setCurrentTime(updateParams);
        commonRepository.update("AccountMapper.updateAccount", updateParams);
    }

    /**
     * 学生、家长注册账号
     *
     * @param params studentName studentNum password rePassword  userType  accountName(email or phone identifyCode)
     * @return 注册状态
     */
    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public boolean register(Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {
        // 参数校验
        Verify.of(params)
                .isNotBlank("studentName")
                .isMobileNumber("accountName")
                .isNotBlank("password")
                .isNotBlank("rePassword")
                .isNotBlank("sucId")
                .isNumeric("userType")
                .verify();

        String studentNum = StudentUtil.getStudentNum(params);
        params.put("studentNum", studentNum);

        if (!params.get("password").toString().equals(params.get("rePassword"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "两次输入的密码不一致, 请重新输入");
        }

        // accountName 加密
        SensitiveInfoUtil.aesEncrypt(params,"accountName");

        // 打印日志信息
        Map<String, Object> logp = new HashMap<>(params);
        logp.remove("password");
        logp.remove("rePassword");
        log.info("学生、家长注册信息。params : " + logp);

        // 默认采用 4 ：学生注册；5：家长注册
        String userType = !"5".equals(params.get("userType").toString()) ? "4" : "5";
        params.put("userType", userType);

        // 密码加密，设置 salt
        String pwd = params.get("password").toString();
        pwd = MD5Util.md5BySalt(pwd);
        params.put("password", pwd);

        // 身份验证
        accountCheckService.checkRegisterStudentIdentify2(params);

        // 优先采用号码注册
        if (!IdentifyCodeUtil.isValidIdentifyCode(params.get("identifyCode"))) {
            log.error("使用手机注册时，验证码未填写，不能注册。params : " + params);
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        return registerByPhone(params, request, response);

    }


    /**
     * 使用号码注册
     *
     * @param params   studentName studentNum password rePassword  userType  accountName( phone identifyCode)
     * @param request
     * @param response
     * @return 状态
     */
    private boolean registerByPhone(Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {

        params.put("effectiveTime", 3 * 60 * 1000L); // 3分钟

        // 验证码和手机是否匹配
        accountCheckService.checkIdentifyCode(params);

        // 校验注册信息
        if (!accountCheckService.checkRegister(params)) {
            return false;
        }

        //判断用户曾经是否注销
        /*if (accountCheckService.checkIsUserCancel(params)) {

            return true;
        }*/

        // 同步账号
        String userId = synchroAccount(params,request, response);

        // 同步作业待办  学籍任务待办
        Map<String, Object> p = new HashMap<>(params);
        p.put("userId", userId);
        taskExecutor.execute(() -> {
            syncHomework(p);
            synchroRollPlanStudentTodo(p);
        });

        return true;
    }

    /**
     * 同步作业待办
     *
     * @param params userId  userName  studentId
     */
    @Override
    public void syncHomework(Map<String, Object> params) {
        if ("4".equals(params.get("userType").toString()) || "5".equals(params.get("userType").toString())) {
            Map<String, Object> p = new HashMap<>();
            p.put("todoUserId", params.get("userId"));
            p.put("todoUserName", params.get("userName"));
            p.put("todoStudentId", params.get("studentId"));
            p.put("userType", params.get("userType"));
            homeworkHandleService.syncHomework(p);
        }
    }

    /**
     * 同步学籍任务信息收集待办
     *
     * @param params userId  userName  studentId
     */
    private void synchroRollPlanStudentTodo(Map<String, Object> params) {
        if ("5".equals(params.get("userType"))) {
            Map<String, Object> p = new HashMap<>();
            p.put("todoUserId", params.get("userId"));
            p.put("todoUserName", params.get("userName"));
            p.put("todoStudentId", params.get("studentId"));
            p.put("userType", params.get("userType"));
            p.put("studentId", params.get("studentId"));
            //todo
//            rollPlanTodoTaskService.createRollPlanStudentTodoTask(p);
        }
    }


    /**
     * 学生/家长 同步账户 用户
     */
    private String synchroAccount(Map<String, Object> params, HttpServletRequest request, HttpServletResponse response) {

        Map<String, Object> u = new HashMap<>();
        u.put("accountName", params.get("accountName"));
        u.put("accountNameAes", params.get("accountNameAes"));
        u.put("userStatus", "1");
        u.put("relativeId", params.get("relativeId"));
        u.put("userType", params.get("userType"));
        u.put("currentTime", DateUtil.getCurrentDateTime());
        // 注册账号 creatorId 默认为 0
        u.put("userId", 0);
        if (!"5".equals(params.get("userType"))) { // 学生
            u.put("nickname", params.get("className"));
            u.put("userName", params.get("userName"));
        } else {                                  // 家长
            u.put("nickname", params.get("studentName"));
            u.put("userName", params.get("parentName"));
        }

        //1查询 account  通过账户名称(accountName)查询 account记录
        Map<String, Object> account = commonRepository.selectOne("AccountMapper.getAccount", u);
        Boolean accountExistFlag = MapUtils.isNotEmpty(account);
        if (null != account && ObjectUtil.isValidId(account.get("accountId"))) {
            if (ObjectUtil.isValueEquals(account.get("accountStatus"), "0")) {
                throw new CommonException(ResponseStatusEnum.ACCOUNT_ALREADY_OFF);
            }
            u.put("accountId", account.get("accountId"));
            commonRepository.delete("AccountMapper.deleteAccount", account);
        } else {
            account = new HashMap<>();
            account.put("accountName", params.get("accountName"));
            account.put("accountNameAes", params.get("accountNameAes"));
            account.put("accountStatus", "1");
            account.put("createTime", DateUtil.getCurrentDateTime());
            account.put("passwordStatus", "1");
            account.put("weChatOpenId", "");
        }
        account.put("updateTime", DateUtil.getCurrentDateTime());
        account.put("userId", 0);
        account.put("userName", params.get("accountName"));
        account.put("password", params.get("password"));
        commonRepository.insert("AccountMapper.insertAccount", account);

        // 获取账号ID
        Map<String, Object> accountInfo = accountService.getAccountInfoByAccountNameAes(u);
        if (MapUtils.isEmpty(accountInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号不存在");
        }

        // 如果账号存在。走绑定流程，之前注册过
        if (accountExistFlag) {
            HashMap<String, Object> userParam = new HashMap<>(8);
            userParam.put("accountId", MapUtil.getLong(accountInfo, "accountId"));
            userParam.put("studentName", MapUtil.getString(params, "studentName"));
            userParam.put("studentNum", MapUtil.getString(params, "studentNum"));
            userParam.put("schoolId", MapUtil.getLongNullable(params, "schoolId"));
            userParam.put("userId", 0L);
            userParam.put("userName", params.get("userName"));
            Map<String, Object> userInfo = userService.insertUser(userParam);
            return MapUtil.getString(userInfo, "userId");
        }

        u.put("accountId", accountInfo.get("accountId"));


        if ("5".equals(params.get("userType"))) {
            //家长注册
            u.put("parentName", params.get("studentName").toString() + "的家长");
            commonRepository.insert("ParentMapper.insertParent", u);
            Map<String, Object> parentInfo = parentService.getParentByPhoneAes(MapUtil.of("accountNameAes", params.get("accountNameAes")));
            if (MapUtils.isNotEmpty(parentInfo)) {
                u.put("parentId", MapUtil.getLong(parentInfo, "parentId"));
            }
            commonRepository.insert("ParentMapper.insertParentStudent", u);
            u.put("relativeId", u.get("parentStudentId"));
        }

        //2 得到 accountId 插入一条 user
        commonRepository.insert("AccountMapper.insertUser", u);

        if ("4".equals(params.get("userType")) && ObjectUtil.isNumeric(params.get("accountNameSrc"))) {
            //更新学生表的phone
            commonRepository.update("StudentMapper.updateStudentPhone", u);
        }

        if (MapUtils.isNotEmpty(account)) {
            if (ObjectUtil.isNotBlank(params.get("code"))) {
                Map<String, Object> wechatParams = new HashMap<>();
                wechatParams.put("code", params.get("code"));
                wechatParams.put("accountId", accountInfo.get("accountId"));
                wechatParams.put("referer", params.get("referer"));
                updateWeChat(wechatParams);
            }
            u.put("userType", params.get("userType"));
            u.put("clientType", params.get("clientType"));
            u.put("userId", u.get("currentUserId"));

            // 生成token
            if (ObjectUtil.isValueEquals(ClientTypeEnum.WE_CHAT.getType(), params.get("clientType"))) {
                ssoUserService.setTokenForWechat(request, response, params, u);
            }
            thirdResponseService.getResponse(params, response, u);
        }

        //新增用户会员信息，根据用户studentId查询是否有插入过，有则不插入
        params.put("studentId", params.get("relativeId"));
        //(这里居然没有存userId导致后面报错!!!!)
        params.put("userId",0);
        userMembershipService.initUserMembershipByRegister(params);


        //用户同意注册协议记录
        params.put("accountId", accountInfo.get("accountId"));
        commonRepository.insert("AccountMapper.insertSysProtocol", params);
        return u.get("currentUserId").toString();
    }

    /**
     * 更新账户密码
     *
     * @param accountId   账户Id
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 返回更新结果
     */
    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public Response updatePassword(HttpServletRequest request, HttpServletResponse response, Long accountId, String oldPassword, String newPassword) {
        if (oldPassword.equals(newPassword)) {
            return new Response(ResponseStatusEnum.FAILURE, "原密码与新密码相同");
        } else {

            //获取原密码  accountName  password
            Map<String, Object> accountInfo = commonRepository.selectOne("AccountMapper.findPassword", accountId);
            if (MapUtils.isEmpty(accountInfo)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "用户不存在");
            }
            String password = accountInfo.get("password").toString();

            String pwd = MD5Util.md5(oldPassword);
            String saltPwd = MD5Util.md5BySalt(oldPassword);

            //原密码为空或者原密码输入正确
            if (password.equals(pwd) || password.equals(saltPwd)) {
                // 校验密码 长度 是否多种组合
                passwordService.validatePassword(newPassword);
                passwordService.checkPasswordWeak(newPassword);
                boolean successFlag = updatePassword(request, response, accountId, newPassword);
                if (successFlag) {
                    return new Response(ResponseStatusEnum.SUCCESS, "修改成功,请重新登入");
                } else {
                    return new Response(ResponseStatusEnum.FAILURE, "修改失败");
                }

            } else {
                return new Response(ResponseStatusEnum.FAILURE, "原密码错误");
            }
        }
    }

    @Override
    public boolean updatePassword(HttpServletRequest request,
                                  HttpServletResponse response,
                                  Long accountId,
                                  String newPassword) {

        Map<String, Object> accountInfo = getAccountInfoByAccountId(MapUtil.of("accountId", accountId));
        if (MapUtils.isEmpty(accountInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号信息不存在, 请联系管理员");
        }

        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("accountId", accountId);
        parameterMap.put("newPassword", MD5Util.md5BySalt(newPassword));
        int result = commonRepository.update("AccountMapper.updatePassword", parameterMap);
        if (result > 0) {
            // 修改密码成功，清除密码错误次数
            verifyService.loginErrorPasswordClean(accountInfo);
            // 清除其他token
            if (loginTokenService.isEnabled()) {
                loginTokenService.cleanOtherTokensForModifyPassword(request, response);
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean updatePasswordForPaperRead(HttpServletRequest request, HttpServletResponse response, Long accountId, String newPassword, boolean cleanToken) {
        Map<String, Object> accountInfo = getAccountInfoByAccountId(MapUtil.of("accountId", accountId));
        if (MapUtils.isEmpty(accountInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号信息不存在, 请联系管理员");
        }

        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("accountId", accountId);
        parameterMap.put("newPassword", MD5Util.md5BySalt(newPassword));
        parameterMap.put("passwordStatus", 1);
        int result = commonRepository.update("AccountMapper.updatePassword", parameterMap);
        if (result > 0) {
            // 修改密码成功，清除密码错误次数
            verifyService.loginErrorPasswordClean(accountInfo);
            // 清除其他token
            if (loginTokenService.isEnabled() && cleanToken) {
                loginTokenService.cleanAccountTokens(accountId);
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void batchCheckAndUpdatePassword(HttpServletRequest request, HttpServletResponse response, List<Long> accountIdList, String newPassword) {
        List<Map<String, Object>> accountInfoList = getAccountInfoByAccountIds(MapUtil.of("list", accountIdList));
        if (CollectionUtils.isEmpty(accountInfoList)) {
            return;
        }

        accountService.batchUpdatePassword(accountIdList, newPassword);
        accountInfoList.stream().forEach(accountInfo -> {
            long accountId = MapUtil.getLong(accountInfo, "accountId");
            verifyService.loginErrorPasswordClean(accountInfo);
            if (loginTokenService.isEnabled()) {
                loginTokenService.cleanAccountTokens(accountId);
            }
        });
    }

    /**
     * 批量更新账号密码
     *
     * @param accountIds 账号ID列表
     * @param newPassword 新密码
     */
    @Override
    public void batchUpdatePassword(List<Long> accountIds, String newPassword) {
        batchUpdatePassword(accountIds, newPassword, 1);
    }

    @Override
    public void batchUpdatePassword(List<Long> accountIds, String newPassword, Integer passwordStatus) {
        passwordService.validatePassword(newPassword);
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("accountIds", accountIds);
        parameterMap.put("newPassword", MD5Util.md5BySalt(newPassword));
        parameterMap.put("passwordStatus", Optional.ofNullable(passwordStatus).orElse(1));
        commonRepository.update("AccountMapper.batchUpdatePassword", parameterMap);
    }

    /**
     * 重置密码
     *
     * @param accountId    账户Id
     * @param validateCode 校验码
     * @param realCode     真实验证码
     * @param newPassword  新密码
     * @return 返回保存结果
     */
    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public Response savePassword(HttpServletRequest request, HttpServletResponse response,
                                 Long accountId, String validateCode, String realCode, String newPassword) {
        if (!realCode.equals(validateCode)) {//校验验证码是否正确
            return new Response(ResponseStatusEnum.FAILURE, "验证码错误");
        } else {
            // 校验密码 长度 是否多种组合
            passwordService.validatePassword(newPassword);
            passwordService.checkPasswordWeak(newPassword);

            Map<String, Object> parameterMap = new HashMap<>();
            parameterMap.put("newPassword", MD5Util.md5BySalt(newPassword));
            parameterMap.put("accountId", accountId);
            int result = commonRepository.update("AccountMapper.updatePassword", parameterMap);
            Map<String, Object> accountInfo = commonRepository.selectOne("AccountMapper.findPassword", accountId);
            if (result > 0) {
                // 修改密码成功，清除密码错误次数
                verifyService.loginErrorPasswordClean(accountInfo);
                // 清除其他token
                if (loginTokenService.isEnabled()) {
                    loginTokenService.cleanOtherTokensForModifyPassword(request, response);
                }
                return new Response();
            } else {
                return new Response(ResponseStatusEnum.FAILURE, "修改失败");
            }
        }
    }

    /**
     * 修改账户绑定的手机号码
     *
     * @param accountId    账户Id
     * @param newPhone     新手机号码
     * @param validateCode 验证码
     * @param userName     用户名
     * @return 返回绑定结果
     */
    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public Response updatePhone(Long accountId, String oldPhone, String newPhone, String validateCode, Long userId, String userName) {
        // 数据操作
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("accountId", accountId);
        parameterMap.put("newPhone", newPhone);
        parameterMap.put("oldPhone", oldPhone);
        parameterMap.put("accountName", newPhone);
        parameterMap.put("identifyCode", validateCode);
        parameterMap.put("currentTime", DateUtil.getCurrentDateTime());
        parameterMap.put("userName", userName);
        parameterMap.put("userId", userId);

        // accountName 加密
        SensitiveInfoUtil.aesEncrypt(parameterMap,"accountName");

        //短信校验码验证
        sysRegisterService.validateIdentifyCode(MapUtil.of(
                "accountNameAes", MapUtil.getString(parameterMap, "accountNameAes"),
                "identifyCode", validateCode,
                "effectiveTime", 5 * 60 * 1000L // 5分钟
        ));
        
        // 验证 accountId 和 accountName 是否一致
        String id = commonRepository.selectOne("AccountMapper.getAccountIdByPhone", SensitiveInfoUtil.aesEncrypt(oldPhone));
        if (id == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "信息提供有误，不存在账号：" + oldPhone);
        }

        if (!id.equals(accountId.toString())) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "旧账号信息有误，ID不正确");
        }

        // 新账号不存在，则直接更新
        id = commonRepository.selectOne("AccountMapper.getAccountIdByPhone", SensitiveInfoUtil.aesEncrypt(newPhone));
        if (id == null) {
            parameterMap.put("phoneBindStatus", DictUtil.getDictValue("phoneBindStatus", "bound"));
            commonRepository.update("AccountMapper.updatePhone", parameterMap);
            return new Response();
        }

        // 新账号已经存在，那么不能更新
        throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "该手机号码已被绑定了");
    }

    /**
     * 修改微信账号
     *
     * @param parameterMap 参数
     * @return 返回修改结果
     */
    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public Response updateWeChat(Map<String, Object> parameterMap) {
        log.info("微信公众号登录入参: {}", parameterMap);
        if (!parameterMap.containsKey("accountId") || !parameterMap.containsKey("code")) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        String code = (String) parameterMap.get("code");

        Map<String, String> weChatConfig = WeChatUtils.getWeChatConfig(parameterMap.get("referer"), MapUtil.getStringNullable(parameterMap, "wechatType"));
        log.info("微信公众号绑定使用配置：{}", weChatConfig);
        // 获取access_token 和 openid
        String result = restTemplate.getForObject("https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + weChatConfig.get("appId") + "&secret=" + weChatConfig.get("secret") + "&code="
                + code + "&grant_type=authorization_code", String.class);
        log.info("微信公众号使用code请求accessToken返回: {}", result);
        ObjectMapper objectMapper = new ObjectMapper();

        Map resultMap;
        try {
            resultMap = objectMapper.readValue(result, Map.class);
        } catch (IOException e) {
            throw new CommonException(ResponseStatusEnum.FAILURE, e);
        }

        log.info("微信公众号使用code请求accessToken返回对象: {}", resultMap);

        // 将openid与账号绑定
        if(resultMap != null && !ObjectUtil.isBlank(resultMap.get("openid"))){
            parameterMap.put("weChatOpenId", resultMap.get("openid"));
            parameterMap.put("weChatType", Integer.valueOf(weChatConfig.get("weChatType")));
            parameterMap.put("currentTime",DateUtil.getCurrentDateTime());
            parameterMap.putIfAbsent("userId",1);
            parameterMap.putIfAbsent("userName","默认");

            if (!ObjectUtil.isValueEquals(WeChatTypeEnumeration.DONG_NI_TEACHER.getKey(), Integer.valueOf(weChatConfig.get("weChatType")))) {
                log.info("学生微信公众号更新account");
                commonRepository.update("AccountMapper.updateWeChat", parameterMap);
            }

            // 记录绑定的微信
            int sum = accountWeChatService.insertWeChat(parameterMap);
            log.info("账号{}已经绑定过{}个微信",parameterMap.get("accountId"),sum);
        }else {
            log.warn("账号{}从微信服务获取openId返回为空，所以不绑定",parameterMap.get("accountId"));
        }

        return new Response();
    }

    /**
     * 查询账户角色
     *
     * @param accountId 账户Id
     * @return 账户角色集合
     */
    @Override
    public List<Map<String, Object>> getAccountUser(Long accountId) {
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("accountId", accountId);
        parameterMap.put("userStatus", UserStatusEnum.ENABLE.getCode());
        return commonRepository.selectList("AccountMapper.findAccountUser", parameterMap);
    }

    /**
     * 根据账户Id获取账户信息
     *
     * @param accountId 账户Id
     * @return 返回账户Id:accountId 账户名:accountName  账户邮箱:accountEmail 账户微信:weChatOpenId 账户手机号码:accountPhone
     */
    @Override
    public Map<String, Object> getInformation(Long accountId) {
        return commonRepository.selectOne("AccountMapper.getInformation", accountId);
    }


    /**
     * 忘记密码 相关接口
     *
     * 1）判断邮箱、手机是否已经注册 isRegistered
     * 2）获取手机验证码 setIdentifyCode
     * 3）验证码校验 checkIdentifyCode
     * 4）获取邮箱验证链接 setEmailLink
     * 5）链接地址跳转与验证 checkEmailLink
     * 6）设置新密码  setNewPassword
     */

    /**
     * 设置新密码
     *
     * @param params newPassword、accountName、identifyCode（MD5）
     * @return true 修改成功，false 修改失败
     */
    @Override
    @Transactional(BaseDataRepository.TRANSACTION)
    public boolean setNewPassword(HttpServletRequest request, HttpServletResponse response, Map<String, Object> params) {
        // 参数校验
        if (params == null || ObjectUtil.isBlank(params.get("accountName"))
                || ObjectUtil.isBlank(params.get("newPassword"))
                || ObjectUtil.isBlank(params.get("identifyCode"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        // accountName 加密
        SensitiveInfoUtil.aesEncrypt(params,"accountName");
        
        // 校验验证码
        sysRegisterService.validateIdentifyCodeMd5(MapUtil.of(
                "accountNameAes", MapUtil.getString(params, "accountNameAes"),
                "identifyCode", MapUtil.getString(params, "identifyCode")
        ));
        
        // 数据操作
        Map<String, Object> account = commonRepository.selectOne("AccountMapper.getAccount", params);
        if (account == null || account.isEmpty()) {
            throw new CommonException(ResponseStatusEnum.ACCOUNT_NOT_EXISTS);
        }

        // 校验密码 长度 是否多种组合
        String newPassword = params.get("newPassword").toString();
        passwordService.validatePassword(newPassword);
        passwordService.checkPasswordWeak(newPassword);

        params.put("accountId", account.get("accountId"));
        params.put("newPassword", MD5Util.md5BySalt(newPassword));
        commonRepository.update("AccountMapper.updatePassword", params);

        // 清除密码错误次数
        verifyService.loginErrorPasswordClean(params);
        if (loginTokenService.isEnabled()) {
            loginTokenService.cleanOtherTokensForModifyPassword(request, response);
        }

        return true;
    }

    /**
     * 重置为初始密码
     *
     * @param params accountName
     *               [initPasswordType] 初始密码类型 如果不提供则设置为123456
     *                     last6 则使用后6位
     * @return
     */
    @Override
    @Transactional(value = BaseDataRepository.TRANSACTION, rollbackFor = Exception.class)
    public boolean resetPassword(Map<String, Object> params) {
        // 参数校验
        if (params == null || ObjectUtil.isBlank(params.get("accountName"))
                || !ObjectUtil.isValidId(params.get("userId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        if ("subjectAdmin".toLowerCase().equals(params.get("accountName").toString().toLowerCase())
                || "superAdmin".toLowerCase().equals(params.get("accountName").toString().toLowerCase())) {
            throw new CommonException(ResponseStatusEnum.PERMISSION_DENIED);
        }
        
        String accountName = MapUtil.getTrim(params, "accountName");
        params.put("accountName", accountName);
        // accountName 加密
        SensitiveInfoUtil.aesEncrypt(params,"accountName");

        //权限校验
        Map<String, Object> userInfo = commonRepository.selectOne("UserMapper.getSimpleUser", params);
        if (MapUtils.isEmpty(userInfo)) {
            throw new CommonException(ResponseStatusEnum.PERMISSION_DENIED, "权限不足，您的用户不存在!");
        }
        if (!DictUtil.isEquals(MapUtil.getInt(userInfo, "userStatus"), "userStatus", "on")) {
            throw new CommonException(ResponseStatusEnum.PERMISSION_DENIED, "权限不足，您的用户已被禁用!");
        }
        int userType = MapUtil.getInt(userInfo, "userType");
        // 校验具体权限
        if (!DictUtil.isEquals(userType, "userType",
                "product", "teacher", "operatorReviewer", "operatorProofreader", "examiner", "studyGuidePublisherAdmin")) {
            throw new CommonException(ResponseStatusEnum.PERMISSION_DENIED, "权限不足，您的用户无此操作权限!");
        }
    
        Long accountId = commonRepository.selectOne("AccountMapper.getAccountId", params);
        //设置初始密码
        String initPassword = "123456";
        String initPasswordType = MapUtil.getStringNullable(params, "initPasswordType");
        if ("last6".equals(initPasswordType)) {
            initPassword = MapUtil.getStringNullable(params, "prefix") + StringUtils.substring(accountName, -6);
        }
        params.put("password", MD5Util.md5BySalt(initPassword));
        params.put("passwordStatus", MapUtil.getInt(params, "passwordStatus", 0));
        int total = commonRepository.update("AccountMapper.resetPassword", params);
        if (total > 0) {
            // 清除密码错误次数
            verifyService.loginErrorPasswordClean(params);
            if (loginTokenService.isEnabled()) {
                loginTokenService.cleanAccountTokens(accountId);
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据accountId判断用户是否已经同意协议
     * @param accountId
     * @return false/true
     */
    @Override
    public boolean isAgreeProtocol(long accountId) {
        long count = commonRepository.selectOne("AccountMapper.getProtocolCountByAccountId", accountId);
        return count != 0;
    }

    /**
     * 用户同意协议后新增数据
     * @param params account_id
     * @return
     */
    @Override
    public Response insertSysProtocol(Map<String, Object> params) {
        commonRepository.insert("AccountMapper.insertSysProtocol", params);
        return new Response();
    }

    /**
     * 用户注销
     * @param params userId userName
     * @return
     */
    @Override
    public Response userCancel(Map<String, Object> params) {

        Map<String,Object> account = commonRepository.selectOne("AccountMapper.getAccountByUser",params);
        if(MapUtils.isEmpty(account)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS);
        }

        params.putAll(account);
        commonRepository.update("AccountMapper.userCancel", params);

        // 如果这个账号的用户都被注销，那么该账户也要被注销
        Integer count = commonRepository.selectOne("AccountMapper.getValidUserCount",params);
        if(count == null || count == 0){
            commonRepository.update("AccountMapper.accountCancel", params);
        }

        return new Response();
    }

    /**
     * 校验账号密码
     *   仅校验加盐密码
     *   该方法用于修改手机号码时校验密码是否正确
     * @param params
     *    - accountId
     *    - accountName   账号明文
     *    - password      用户输入明文密码
     * @return 校验成功 true
     */
    @Override
    public boolean validatePassword(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("accountId")
                .isNotBlank("accountName")
                .isNotBlank("password")
                .verify();
        SensitiveInfoUtil.aesEncrypt(params, "accountName");

        Long accountId = Long.parseLong(params.get("accountId").toString());
        Map<String, Object> account = commonRepository.selectOne("AccountMapper.findPassword", accountId);
        if (MapUtils.isEmpty(account)
                || !ObjectUtil.isValueEquals(account.get("accountNameAes"), params.get("accountNameAes"))) {
            return false;
        }

        String storePwd = account.get("password").toString();
        String saltPassword = MD5Util.md5BySalt((String) params.get("password"));
        return StringUtils.equals(saltPassword, storePwd);
    }

    /**
     * 通过 accountId 获取账号名称
     *
     * @param params accountId
     * @return 账号
     */
    @Override
    public String getAccountNameByAccountId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("accountId")
                .verify();
        return commonRepository.selectOne("AccountMapper.getAccountNameByAccountId",params);
    }

    @Override
    public Map<String, Object> getAccountInfoByAccountNameAes(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("accountNameAes")
                .verify();
        return commonRepository.selectOne("AccountMapper.getBaseAccountInfo", MapUtil.of(
                "accountNameAes", params.get("accountNameAes")
        ));
    }

    @Override
    public Map<String, Object> getAccountInfoByAccountId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("accountId")
                .verify();
        return commonRepository.selectOne("AccountMapper.getBaseAccountInfo", MapUtil.of(
                "accountId", params.get("accountId")
        ));
    }
    
    @Override
    public Map<String, Object> getAccountInfoByUserId(Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return commonRepository.selectOne("AccountMapper.getAccountInfoByUserId", MapUtil.of(
                "userId", params.get("userId")
        ));
    }
    

    @Override
    public void updateAccountNameForAccountNameChange(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isValidId("accountId")
                .isNotBlank("accountNameSrc")
                .verify();
        
        Map<String, Object> updateParams = MapUtil.copy(params, "userId", "userName", "accountId");
        String accountNameSrc = params.get("accountNameSrc").toString().trim();
        String accountNameAes = SensitiveInfoUtil.aesEncrypt(accountNameSrc);
        String accountNameHide = SensitiveInfoUtil.hide(accountNameSrc);
        updateParams.put("accountName", accountNameHide);
        updateParams.put("accountNameAes", accountNameAes);
        
        Map<String, Object> accountInfo = getAccountInfoByAccountId(params);
        if (MapUtils.isEmpty(accountInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号不存在:" + params.get("accountId"));
        }
        updateParams.put("phoneBindStatus", DictUtil.getDictValue("phoneBindStatus", "never"));
        updateParams.put("securityStatus", DictUtil.getDictValue("securityStatus", "disabled"));
        // 如果密码是初始 则更改
        if (DictUtil.isEquals(Integer.parseInt(accountInfo.get("passwordStatus").toString()), "passwordStatus", "init")) {
            String password = getDefaultPassword(accountNameSrc);
            updateParams.put("password", MD5Util.md5BySalt(password));
            updateParams.put("setPasswordModifyDateTimeNull", true);
        }

        ParamsUtil.setCurrentTime(updateParams);
        try {
            commonRepository.update("AccountMapper.updateAccountNameForAccountNameChange", updateParams);
        } catch (Exception e) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "更新联系方式失败，请刷新重试");
        }
    }

    @Override
    public void insertJwtAccount(Map<String, Object> params) {
        params.put("createTime", DateUtil.getCurrentDateTime());
        commonRepository.insert("AccountMapper.insertAccount", params);
    }

    @Override
    public void updateAccountName4Admin(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("accountId")
                .isNotBlank("accountName")
                .isNotBlank("accountNameAes")
                .verify();

        commonRepository.update("AccountMapper.updateAccountNameForAccountNameChange", params);
    }

    @Override
    public void updatePasswordStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("accountId")
                .isNotBlank("passwordStatus")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.update("AccountMapper.updateAccount", params);
    }

    @Override
    public String generateAccount(Long areaId) {
        String accountName;
        int count = 0;
        //判断是否存在账号，如果存在则循环直到不重复或重复次数超过一定次数
        while (true) {
            String randomNumber = AccountUtils.generateNumber(6);
            accountName = AccountUtils.joinAccountName(areaId, randomNumber);
            String accountNameAes = SensitiveInfoUtil.aesEncrypt(accountName);
            Map<String, String> account = commonRepository
                    .selectOne("AccountMapper.getAccountForExist", MapUtil.of("accountNameAes", accountNameAes));
            if (MapUtils.isEmpty(account)) {
                break;
            } else {
                count++;
            }
            if (count > 10000) {
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "数据库重复帐号太多,无法生成账号");
            }
        }
        return accountName;
    }

    @Override
    public void batchInsertAccount(List<Map<String, Object>> accountList) {
        if (CollectionUtils.isEmpty(accountList)) {
            return;
        }

        commonRepository.batchInsert("AccountMapper.batchInsertAccount", accountList);
    }

    @Override
    public List<Map<String, Object>> getAccountByAccountNameAesList(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .verify();
        return commonRepository.selectList("AccountMapper.getAccountByAccountNameAesList", params);
    }

    @Override
    public List<Map<String, Object>> getAccountListByAccountNameAes(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .verify();

        return commonRepository.selectList("AccountMapper.getAccountListByAccountNameAes", params);
    }

    @Override
    public Map<String, Object> getAccountBySchoolId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("schoolId")
                .verify();

        log.info("拉取密码请求参数-{}",params);
        String schoolId = MapUtil.getStringNullable(params, "schoolId");

        // 查询是否有这一所学校
        params.put("thirdPartyId", DictUtil.getDictValue("thirdParty", "nicezhuanye"));
        Map<String, Object> schoolInfo = baseSchoolService.getSyncSchool(params);
        if (MapUtils.isEmpty(schoolInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学校暂未绑定，请同步学校列表后再次拉取密码");
        }

        List<Map<String, Object>> userList = commonRepository.selectList("AccountMapper.getUserListListBySchoolId", params);

        // 获取教育局用户账号
        // 获取学校的areaCode
        Map<String, Object> schoolArea = baseSchoolService.getSchoolAreaById(params);
        if (MapUtils.isNotEmpty(schoolArea)) {
            List<Map<String, Object>> educationDirectorList = commonRepository.selectList("AccountMapper.selectEduDirector", schoolArea);
            userList.addAll(educationDirectorList);
        }

        List<Long> accountIdList = userList.stream()
                .map(item -> MapUtil.getLong(item, "accountId"))
                .distinct()
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        if (CollectionUtils.isEmpty(accountIdList)) {
            result.put("totalCount", 0);
            result.put("list", Lists.newArrayList());
            return result;
        }

        Integer totalCount = countAccountInfoByAccountIds(MapUtil.of("list", accountIdList));

        result.put("totalCount", totalCount);
        if (totalCount == 0) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未获取到任何需要同步的用户，请联系管理员");
        }

        Map<String, Object> tmpParams = new HashMap<>(params);
        tmpParams.put("list", accountIdList);

        List<Map<String, Object>> accountList = getAccountInfoByAccountIds(tmpParams);
        SensitiveInfoUtil.aesDecrypt(accountList, "accountNameAes");
        result.put("list", accountList);

        // 账号升级
        List<Map<String, Object>> accountUpgradeList = accountIdList.stream().map(item -> {
            Map<String, Object> accountUpgrade = new HashMap<>();
            accountUpgrade.put("accountId", item);
            accountUpgrade.put("upgradeStatus", 1);
            accountUpgrade.put("schoolId", schoolId);
            return accountUpgrade;
        }).collect(Collectors.toList());

        // 账号升级
        accountUpgradeService.upgradeAccounts(MapUtil.of("list", accountUpgradeList));
        // 密码状态更新为正常
        Map<String, Object> passwordStatusParams = MapUtil.of("list", accountIdList);
        passwordStatusParams.put("passwordStatus", DictUtil.getDictValue("passwordStatus", "normal"));
        updatePasswordStatusByIds(passwordStatusParams);

        // 清除token
        try {
            accountIdList.forEach(accountId -> {
                loginTokenService.cleanAccountTokens(accountId);
            });
        } catch (Exception e) {
            log.error("清除token失败");
        }

        return result;
    }

    @Override
    public void updatePasswordStatusByIds(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .isNumeric("passwordStatus")
                .verify();
        commonRepository.update("AccountMapper.updatePasswordStatusByIds", params);
    }

    /**
     * 账号升级
     * @param params list
     */
    public void updateUpgrade(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .verify();

        commonRepository.update("AccountMapper.updateUpgrade", params);
    }

    @Override
    public Boolean isHzyAccount(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("accountNameAes")
                .verify();

        Map<String, Object> accountInfo = getAccountInfoByAccountNameAes(params);
        if (MapUtils.isEmpty(accountInfo)) {
            return false;
        }

        Long accountId = MapUtil.getLong(accountInfo, "accountId");
        List<Map<String, Object>> userList = commonRepository.selectList("LoginMapper.selectAccount", params);
        if (CollectionUtils.isNotEmpty(userList)) {
            Integer product = DictUtil.getDictValue("userType", "product");
            List<Map<String, Object>> productUserList = userList.stream()
                    .filter(item -> ObjectUtil.isNotBlank(item.get("userType")))
                    .filter(item -> ObjectUtil.isValueEquals(item.get("userType"), product))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productUserList)) {
                return false;
            }
        }

        // 教育局用户不允许登录微信端
        Integer clientType = MapUtil.getInt(params, "clientType", ClientTypeEnum.PC.getType());
        if (ObjectUtil.isValueEquals(clientType, ClientTypeEnum.WE_CHAT.getType())) {
            if (CollectionUtils.isNotEmpty(userList)) {
                Integer educDirector = DictUtil.getDictValue("userType", "educDirector");
                Integer instructor = DictUtil.getDictValue("userType", "instructor");

                List<Map<String, Object>> regionUserList = userList.stream()
                        .filter(item -> ObjectUtil.isNotBlank(item.get("userType")))
                        .filter(item -> ObjectUtil.isValueEquals(item.get("userType"), educDirector) || ObjectUtil.isValueEquals(item.get("userType"), instructor))
                        .collect(Collectors.toList());

                if (regionUserList.size() == userList.size()) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "区域用户不支持移动端登录");
                }
            }
        }

        return accountUpgradeService.isUpgradeAccount(accountId, params);
    }

    @Override
    public void bindWechatOpenId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("accountNameAes")
                .isNotBlank("openid")
                .isNotBlank("referer")
                .verify();
        Map<String, String> weChatConfig = WeChatUtils.getWeChatConfig(params.get("referer"));
        Map<String, Object> accountInfo = getAccountInfoByAccountNameAes(params);
        if (MapUtils.isNotEmpty(accountInfo)) {
            // 将openid与账号绑定
            HashMap<String, Object> bindOpenidParams = new HashMap<String, Object>();
            bindOpenidParams.put("accountId", accountInfo.get("accountId"));
            bindOpenidParams.put("weChatOpenId", params.get("openid"));
            bindOpenidParams.put("weChatType", Integer.valueOf(weChatConfig.get("weChatType")));
            bindOpenidParams.put("currentTime", DateUtil.getCurrentDateTime());
            bindOpenidParams.put("userId",1);
            bindOpenidParams.put("userName","升级用户微信登陆");
            commonRepository.update("AccountMapper.updateWeChat", bindOpenidParams);

            // 记录绑定的微信
            int sum = accountWeChatService.insertWeChat(bindOpenidParams);
            log.info("账号{}已经绑定过{}个微信",bindOpenidParams.get("accountId"),sum);
        }
    }


    /**
     * 批量获取账号信息
     * @param params list[accountId...] pageNo pageSize
     * @return
     */
    public List<Map<String, Object>> getAccountInfoByAccountIds(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .verify();

        return commonRepository.selectList("AccountMapper.getAccountInfoByAccountIds", params);
    }

    /**
     * 统计账号数量
     * @param params list[accountId...]
     * @return accountId accountName accountNameAes password passwordStatus
     */
    public Integer countAccountInfoByAccountIds(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .verify();

        return commonRepository.selectOne("AccountMapper.countAccountInfoByAccountIds", params);
    }
    
    @Override
    public String handlerSpecialEnvAccountName(String accountName) {
        return handlerSpecialEnvParam(accountName, "用户名", (account, prefix) -> {
            // 拼接过了就不管了
            // prefix: putian_   account: putian_***********  return: putian_***********
            if (account.startsWith(prefix)) {
                return account;
            }
            // prefix: putian_   account: ***********         return: putian_***********
            return prefix + account;
        });
    }
    
    @Override
    public String handlerSpecialEnvPhoneNum(String accountName) {
        return handlerSpecialEnvParam(accountName, "手机号", (account, prefix) -> {
            // prefix: putian_   account: putian_***********  return: ***********
            if (account.startsWith(prefix)) {
                return account.substring(prefix.length());
            }
            // prefix: putian_   account: ***********         return: ***********
            return account;
        });
    }

    /**
     * 批量插入账号
     *
     * @param accountList 账号列表
     */
    @Override
    public void batchInsertAccountNotDuplicateKey(List<SysAccount> accountList) {
        commonRepository.insert("AccountMapper.batchInsertAccountNotDuplicateKey", accountList);
    }

    /**
     * 批量查询已经存在的账号
     *
     * @param accountNameList 账号列表
     * @return 已经存在的账号列表
     */
    @Override
    public List<String> selectAccountByAccountName(List<String> accountNameList) {
        if (CollectionUtils.isEmpty(accountNameList)) {
            return new ArrayList<>();
        }
        return commonRepository.selectList("AccountMapper.selectAccountByAccountName", accountNameList);
    }

    private String handlerSpecialEnvParam(String accountName,
                                          String paramDescription,
                                          BiFunction<String, String, String> accountNameAndPrefix2return) {
        accountName = MapUtil.getTrimNullable(accountName);
        if (accountName == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "处理环境特殊" + paramDescription + "时用户名不能为空");
        }
    
        // 所有环境都不需要处理的，返回原值
        Set<String> notHandlerAccountNameAllEnvSet = Stream.of(
                        "SuperAdmin",
                        "SubjectAdmin",
                        "SyncAdmin"
                )
                .collect(Collectors.toSet());
        if (notHandlerAccountNameAllEnvSet.contains(accountName)) {
            return accountName;
        }
        
        String prefix = "";
        
        // 普天
        if (CurrentServerUtil.isPuTian()){
            // 320981开头的为 学校管理员账号 不需要处理
            if (!accountName.startsWith("320981")) {
                // 其他的全部拼上 putian_
                prefix = "putian_";
            }
        }
    
        // 其他环境
        // else if () {}
        
        return accountNameAndPrefix2return.apply(accountName, prefix);
    }
    
    @Override
    public AccountNameAndPhoneNumber handlerSpecialEnvAccountNameAndPhoneNumber(String accountNameOrPhoneNumber) throws CommonException {
        if (StringUtils.isBlank(accountNameOrPhoneNumber)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号不能为空");
        }
        accountNameOrPhoneNumber = accountNameOrPhoneNumber.trim();
        if (accountNameOrPhoneNumber.contains("*")) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号不能携带星号: *");
        }
        String accountNameWithoutWhitespace = StringUtils.deleteWhitespace(accountNameOrPhoneNumber);
        if (!accountNameOrPhoneNumber.equals(accountNameWithoutWhitespace)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号不能包含空白字符");
        }
        String phoneNumber = handlerSpecialEnvPhoneNum(accountNameOrPhoneNumber);
        if (phoneNumber.length() > 18) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号不能超过 18 位");
        }
        String accountName = handlerSpecialEnvAccountName(accountNameOrPhoneNumber);
        
        String accountNameLowerCase = accountName.toLowerCase();
        if (accountNameLowerCase.contains("admin")) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号含有非法字符");
        }
        
        return new AccountNameAndPhoneNumber(accountName, phoneNumber);
    }

    @Override
    public void resetPassword(Long accountId, Long userId) {
        Map<String, Object> accountInfo = getAccountInfoByAccountId(MapUtil.of("accountId", accountId));
        if (MapUtils.isEmpty(accountInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "用户信息不存在");
        }
        String accountNameAes = MapUtil.getString(accountInfo, "accountNameAes");
        String accountName = SensitiveInfoUtil.aesDecrypt(accountNameAes);
        Map<String, Object> resetPasswordParams = new HashMap<>(4);
        resetPasswordParams.put("accountName", accountName);
        resetPasswordParams.put("userId", userId);
        resetPasswordParams.put("passwordStatus", 0);
        accountService.resetPassword(resetPasswordParams);
    }

    @Override
    public void batchResetPassword(HttpServletRequest request, HttpServletResponse response, List<Long> accountIdList) {
        List<Map<String, Object>> accountInfoList = getAccountInfoByAccountIds(MapUtil.of("list", accountIdList));
        if (CollectionUtils.isEmpty(accountInfoList)) {
            return;
        }

        // 校验密码 长度 是否多种组合
        accountService.batchUpdatePassword(accountIdList, "123456", 0);
        accountInfoList.stream().forEach(accountInfo -> {
            long accountId = MapUtil.getLong(accountInfo, "accountId");
            verifyService.loginErrorPasswordClean(accountInfo);
            if (loginTokenService.isEnabled()) {
                loginTokenService.cleanAccountTokens(accountId);
            }
        });
    }

    @Override
    public void batchInitPassword(HttpServletRequest request, HttpServletResponse response, List<Long> accountIdList) {
        List<Map<String, Object>> accountInfoList = getAccountInfoByAccountIds(MapUtil.of("list", accountIdList));
        if (CollectionUtils.isEmpty(accountInfoList)) {
            return;
        }

        // 校验密码 长度 是否多种组合
        accountService.batchUpdatePassword(accountIdList, "123456", 0);
        accountInfoList.stream().forEach(accountInfo -> {
            long accountId = MapUtil.getLong(accountInfo, "accountId");
            verifyService.loginErrorPasswordClean(accountInfo);
            if (loginTokenService.isEnabled()) {
                loginTokenService.cleanAccountTokens(accountId);
            }
        });
    }

    @Override
    public void batchUpdateAccountStatus(List<Long> accountIdList, Integer accountStatus) {
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        if (CollectionUtils.isEmpty(accountIdList)) {
            return;
        }
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("accountIdList", accountIdList);
        parameterMap.put("accountStatus", accountStatus);
        parameterMap.put("userId", dongniUserInfoContext.getUserId());
        parameterMap.put("userName", dongniUserInfoContext.getUserName());
        parameterMap.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.update("AccountMapper.batchUpdateAccountStatus", parameterMap);
    }

    @Override
    public void deleteAccountByAccountNameAesList(List<String> deleteAccountNameAesList) {
        if (CollectionUtils.isEmpty(deleteAccountNameAesList)) {
            return;
        }
        commonRepository.delete("AccountMapper.deleteAccountByAccountNameAesList", deleteAccountNameAesList);
    }

    @Override
    public List<Map<String, Object>> batchGetUserNoCancelCount(Map<String, Object> params) {
        List<Map<String, Object>> accountNameAesList = MapUtil.getListMap(params, "list");
        if (CollectionUtils.isEmpty(accountNameAesList)) {
            return Lists.newArrayList();
        }
        return commonRepository.selectList("AccountMapper.batchGetUserNoCancelCount", params);
    }

    @Override
    @Transactional(transactionManager = BaseDataRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updatePasswordOfPasswordManage(long accountId, String passwordMd5) {
        Map<String, Object> accountInfo = getAccountInfoByAccountId(MapUtil.of("accountId", accountId));
        if (MapUtils.isEmpty(accountInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "账号信息不存在, 请联系管理员");
        }

        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("accountId", accountId);
        parameterMap.put("newPassword", passwordMd5);
        int result = commonRepository.update("AccountMapper.updatePassword", parameterMap);
        if (result > 0) {
            // 修改密码成功，清除密码错误次数
            verifyService.loginErrorPasswordClean(accountInfo);
            // 清除其他token
            if (loginTokenService.isEnabled()) {
                loginTokenService.cleanAccountTokens(accountId);
            }
        }
    }


}
