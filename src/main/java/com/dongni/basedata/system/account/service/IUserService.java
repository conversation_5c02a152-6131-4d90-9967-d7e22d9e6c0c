package com.dongni.basedata.system.account.service;

import com.dongni.basedata.paper.read.bean.dto.SysUserDto;
import com.dongni.basedata.system.account.model.SysUser;
import com.dongni.basedata.system.account.model.dto.DeleteUserDTO;
import com.dongni.basedata.system.account.model.dto.InsertAccountUserDTO;
import com.dongni.basedata.system.account.model.dto.SysAccountUserDTO;

import com.dongni.basedata.system.account.model.dto.UserInfoDTO;
import java.util.List;
import java.util.Map;

/**
 * Created by Heweipo on 2016/6/18.
 * <p>
 * 用户角色CURD接口
 */
public interface IUserService {

    /**
     * 获取用户
     *
     * @param params accountId userType
     * @return 用户列表
     */
    Map<String, Object> getUser(Map<String, Object> params);


    /**
     * 新增关联用户(家长的孩子)
     *
     * @param params accountId studentName studentNum
     */
    Map<String, Object> insertUser(Map<String, Object> params);


    /**
     * 删除关联的用户
     *
     * @param params accountId userIds userType
     */
    void deleteUser(Map<String, Object> params);

    /**
     * 获取用户相关登录信息，用于上传，后期可能废弃
     *
     * @param params userId 必填
     * @return 用户相关登录信息
     */
    Map<String, Object> getSysUser2Upload(Map<String, Object> params);

    /**
     * 获取用户相关登录信息
     *
     * @param params userId 必填
     * @return 用户相关登录信息
     */
    Map<String, Object> getSysUser(Map<String, Object> params);

    /**
     * 获取用户的简单信息
     *
     * @param params userId
     * @return
     */
    Map<String, Object> getUserInfo(Map<String, Object> params);

    /**
     * 通过userId找到相关用户信息  联系方式 accountId userType relativeId
     * params targetUserId
     *
     * @return 结果
     */
    Map<String, Object> getSimpleUser(Map<String, Object> params);

    /**
     * 获取用户关联的所有角色
     *
     * @param parameterMap userId
     * @return
     */
    List<Map<String, Object>> getRelativeUser(Map<String, Object> parameterMap);

    /**
     * 获取学生用户的基本
     *
     * @param params relativeId
     * @return
     */
    Map<String, Object> getRelativeStuInfo(Map<String, Object> params);

    /**
     * 通过用户姓名获取所有用户的简单信息
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getSimpleUserInfo(Map<String, Object> parameterMap);

    /**
     *
     * 用户身份顺序调增
     *
     * @return 结果
     */
    void saveUserSorts(Map<String, Object> params);

    /**
     *
     * 根据姓名、学号或者账号查询学生
     *
     */
    List<Map<String, Object>> getStudent(Map<String, Object> params);

    /**
     *
     * 根据姓名、学号或者账号查询学生家长
     *
     */
    List<Map<String, Object>> getParent(Map<String, Object> params);

    /**
     *
     * 更改学生身份为家长
     *
     */
    void updateStudentToParent(Map<String, Object> params);

    /**
     *
     * 更改家长身份为学生
     *
     */
    void updateParentToStudent(Map<String, Object> params);

    /**
     *
     * 根据学生id查询所有学生家长
     *
     */
    List<Map<String, Object>> getParentList(Map<String, Object> params);
    /**
     *
     * 根据accountId查询所有孩子
     *
     */
    List<Map<String, Object>> getChildrenList(Map<String, Object> params);

    /**
     *
     * 校验用户密码
     *
     */
    void checkPassword(Map<String,Object> parameterMap);

    /**
     *
     * 校验用户身份，true：已校验，false：未校验
     *
     */
    boolean checkUser(Map<String,Object> parameterMap);


    /**
     *
     * 关闭校验状态
     *
     */
    void closeCheck(Map<String,Object> parameterMap);

    /**
     *
     * 获取当前的校验状态
     *
     */
    Map<String,Object> getCheck(Map<String,Object> parameterMap);

    /**
     *
     * 插入一条敏感信息查看记录
     *
     */
    void addResourceAccess(Map<String,Object> parameterMap);
    
    /**
     *
     */
    Long getThirdBizId(Map<String, Object> params);
    
    /**
     * 通过账号原文获取所有用户信息 包括注销的账户
     * @param accountNameSrc 登录的用户名原文
     * @return [{}]
     *   accountId, accountName, accountNameAes,
     *   password,
     *   accountStatus, 账号状态 1启用 0注销
     *   passwordModifyDateTime,
     *   securityStatus, 设备校验配置
     *   phoneBindStatus, 手机号绑定情况
     *   userId,
     *   userName,
     *   nickname,
     *   userStatus,
     *   userType,
     *   relativeId,
     */
    List<Map<String, Object>> getAllUserListByAccountNameSrc(String accountNameSrc);
    
    /**
     * 通过accountId获取所有用户信息 包括注销的账户
     * @param accountId 账户id
     * @return [{}] {@link #getAllUserListByAccountNameSrc(String)}
     *
     */
    List<Map<String, Object>> getAllUserListByAccountId(long accountId);
    
    /**
     * 设置用户启用状态
     * @param params
     *   - userId       接口请求的操作人userId
     *   - userName     接口请求的操作人userName
     *   - updateUserId 被设置为启用状态的userId
     */
    void setUserStatusOn(Map<String, Object> params);
    
    /**
     * 设置用户启用状态
     * @param params
     *   - userId       接口请求的操作人userId
     *   - userName     接口请求的操作人userName
     *   - updateUserId 被设置为启用状态的userId
     */
    void setUserStatusOff(Map<String, Object> params);
    
    /**
     * 插入用户信息 sys_user
     * @param params
     *   userId   接口请求的操作人userId
     *   userName 接口请求的操作人userName
     *   insertUserName         用户名称
     *   accountId        账户id
     *   insertUserType         用户类型
     *   relativeId       关联id
     *   [nickname]       如果不提供则使用userName
     * @return {}
     *   - 参数 + userId
     */
    Map<String, Object> insertUserInfo(Map<String, Object> params);
    
    /**
     * 获取userInfo
     * @param params userType relativeId
     * @return sys_user
     */
    Map<String, Object> getRelativeUserInfo(Map<String, Object> params);
    
    /**
     * 获取userInfoList
     * @param params userType relativeIdList
     * @return [{}] sys_user
     */
    List<Map<String, Object>> getRelativeUserInfoList(Map<String, Object> params);
    
    /**
     * 更新用户的userName
     * @param params
     *   - userId   请求接口的userId
     *   - userName 接口请求的userName
     *   - updateUserId          需要更新的userId
     *   - updateUserName        需要更新的userName
     *   - nickname        必须提供虽然我也不知道干啥的 一般来说就是userName 根据业务自行决定
     */
    void updateUserName(Map<String, Object> params);

    /**
     * 更新用户的userName
     * @param params
     *   - userId   请求接口的userId
     *   - userName 接口请求的userName
     *   - updateUserIdList       需要更新的userId
     *   - updateUserName        需要更新的userName
     *   - nickname        必须提供虽然我也不知道干啥的 一般来说就是userName 根据业务自行决定
     */
    void batchUpdateUserName(Map<String, Object> params);

    /**
     * 更新用户的userName
     * @param params
     *   - userId   请求接口的userId
     *   - userName 接口请求的userName
     *   - updateUserId          需要更新的userId
     *   - updateUserName        需要更新的userName
     *   - nickname        userName
     *   - userStatus   用户状态
     */
    void updateUserNameAndStatus(Map<String, Object> params);

    /**
     * 更新用户的userType
     * @param params
     *   - userId   请求接口的userId
     *   - userName 接口请求的userName
     *   - updateUserId          需要更新的userId
     *   - updateUserType        需要更新的新userType
     */
    void updateUserType(Map<String, Object> params);
    
    /**
     * 获取用户信息 仅获取有效的用户
     *   注意 一个account下可能有多个userType相同的数据
     * @param params
     *   queryUserType      查询的userType
     *   accountNameAesList 用户accountNameAesList
     * @return
     *   accountId
     *   accountNameAes
     *   userId
     *   userName
     */
    List<Map<String, Object>> getUserInfoByAccountNameAesAndUserType(Map<String, Object> params);

    /**
     * 批量更新用户状态
     * @param params relativeIdList userStatus userType
     */
    void batchUpdateUserStatus(Map<String, Object> params);

    /**
     * 该删除仅限同一张表，根据relativeId和userType删除角色
     * @param params relativeIdList userTypeList
     */
    void deleteUserByRelativeIdAndUserType(Map<String, Object> params);

    /**
     * 新增题库管理员用户，如：区域题库管理员 金卷题库管理员
     * @param params userId userName adminName adminPhoneAes userType relativeId
     */
    void insertUser4Admin(Map<String, Object> params);

    /**
     * 更新题库管理员用户，如：区域题库管理员 金卷题库管理员
     * @param params userId userName adminName adminPhone adminPhoneAes userType relativeId
     */
    void updateUser4Admin(Map<String, Object> params);

    /**
     * 批量新增用户
     * @param userList 用户列表
     */
    void batchInsertUserMap(List<Map<String, Object>> userList);

    /**
     * 校验用户是否为好专业学校管理员
     * @param params userId userType
     * @return
     */
    boolean isHzySchoolAdmin(Map<String, Object> params);

    /**
     * 批量插入用户，不允许重复
     *
     * @param userList 用户列表
     */
    void batchInsertUserNotDuplicateKey(List<SysUserDto> userList);

    /**
     * 批量新增用户，不允许重复
     * @param userList 用户列表
     */
    void batchInsertUser(List<SysUser> userList);

    /**
     * 删除用户
     * @param userId 用户ID
     */
    void deleteUserByUserId(Long userId);

    String createJwt(Map<String, Object> parameter);

    String createLoginJwt(Map<String, Object> params);

    /**
     * 根据账号获取该账号下所有的角色
     * @param accountNameAes 系统加密账号{@link com.dongni.commons.utils.SensitiveInfoUtil#aesEncrypt(String)}
     * @return 该账号下所有的角色列表，不包含禁用的角色
     */
    List<UserInfoDTO> getAllUserListByAccountNameAes(String accountNameAes);

    /**
     * 获取用户信息
     * @param userInfoParams accountId relativeId userType
     * @return
     */
    UserInfoDTO getSimpleUserInfoByLogic(Map<String, Object> userInfoParams);

    /**
     * 批量增加账号和账号对应的角色。
     * 1.账号存在，Ⅰ.账号已注销则更新账号状态和初始化密码，再新增角色。Ⅱ-账号正常，则新增角色；
     * 2.账号不存在，则新增账号和角色
     * @param accountUserDTOList 账号角色列表
     */
    void batchInsertAccountAndUser(List<InsertAccountUserDTO> accountUserDTOList);

    /**
     * 删除指定角色
     * @param deleteUserDTO userType relativeIdList  accountNameAesList
     */
    void deleteUserByRelativeInfo(DeleteUserDTO deleteUserDTO);

    /**
     * 获取用户信息
     * @param relativeIdList  业务的关联信息ID
     * @param userType        用户类型
     * @param accountNameAesList （可选）账号ID
     * @return 账号角色信息
     */
    List<SysAccountUserDTO> getUserListByRelativeIdAndUserType(List<Long> relativeIdList, Integer userType, List<String> accountNameAesList);
}
