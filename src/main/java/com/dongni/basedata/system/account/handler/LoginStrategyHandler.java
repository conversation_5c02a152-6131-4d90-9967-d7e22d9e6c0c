package com.dongni.basedata.system.account.handler;

import com.dongni.basedata.system.account.model.param.LoginWithMobileParam;
import com.dongni.basedata.system.account.model.vo.LoginVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className LoginHandler
 * @since 2025/3/10 15:50
 */
public interface LoginStrategyHandler {

    /**
     * 获取登录方式
     * @return 登录方式value
     */
    Integer getLoginStrategyType();

    /**
     * 移动端登录
     * @param loginWithMobileParam 登录前端入参
     * @param request   request
     * @param response  response
     * @return 登录成功对象或者用户列表
     */
    LoginVO loginWithMobile(LoginWithMobileParam loginWithMobileParam,
                            HttpServletRequest request,
                            HttpServletResponse response);

}
