package com.dongni.basedata.system.config.service;

import com.dongni.basedata.system.account.util.RSAUtil;
import com.dongni.commons.utils.MD5Util;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/12/1 下午 02:46
 * @Version 1.0.0
 */
@Service
public class SysConfigPasswordCheckService {
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 检查sys_config的密码
     *
     * @param uniqueKey sys_config.key
     * @param passwordRsa ras加密的密码
     * @return true检验通过 false不通过
     */
    public boolean checkPasswordRas(String uniqueKey, String passwordRsa) {
        String passwordOriginal = RSAUtil.decrypt(passwordRsa);
        return checkPasswordOriginal(uniqueKey, passwordOriginal);
    }

    /**
     * 检查sys_config的密码
     *
     * @param uniqueKey sys_config.key
     * @param passwordOriginal 密码原文
     * @return true检验通过 false不通过
     */
    public boolean checkPasswordOriginal(String uniqueKey, String passwordOriginal) {
        if (StringUtils.isBlank(passwordOriginal)) {
            return false;
        }
        Map<String, Object> config = systemConfigService.getSysConfigByKey(uniqueKey);
        String value = MapUtil.getString(config, "value");
        String passwordMd5 = MD5Util.md5BySalt(passwordOriginal);
        return StringUtils.equals(value, passwordMd5);
    }
}
