package com.dongni.basedata.admin.controller;

import com.dongni.basedata.admin.service.BaseOperateJyeooService;
import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniRequest;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2021/07/06 <br/>
 *  菁优题库权限开通的操作
 *     学校级别
 *     区域级别
 */
@RestController
@RequestMapping(BaseDataConfig.CONTEXT_PATH + "/admin/operate/jyeoo")
public class BaseOperateJyeooController {

    private final Logger log = LogManager.getLogger(BaseOperateJyeooController.class);

    @Autowired
    private BaseOperateJyeooService baseOperateJyeooService;
    
    // ------------------------------------------------------------------------- school
    
    /**
     * 获取菁优网状态的字典信息
     * @param params 不需要
     * @return [] key, value, label, status, cnName, remark, sort
     */
    @GetMapping("/school/status/name")
    @DongniRequest(operationName = "菁优网.获取菁优网状态的字典信息", remark = {"TODO 权限"})
    public Response getJyeooSchoolStatusName(Map<String, Object> params) {
        return new Response(baseOperateJyeooService.getJyeooSchoolStatusName(params));
    }
    
    /**
     * 查询学校菁优网学校开通状态列表
     * @param params hasChild areaId areaCode
     *               keyword(根据学校名称模糊查询)
     *               jyeooStatus(根据状态查询)
     *               endDate(根据到期时间查询)
     *               pageNo pageSize 分页支持
     * @return totalCount
     *         schools[]
     *            schoolId schoolName schoolGroupName stage schoolStatus schoolSort
     *            schoolPhone schoolPhoneAes address latitude longitude
     *            areaId areaName areaCode realAreaName(全路径) memberType
     *            jyeooStatus expireTime
     */
    @GetMapping("/school")
    @DongniRequest(operationName = "菁优网.查询菁优网学校开通状态列表", remark = {"TODO 权限"})
    public Response getJyeooSchool(Map<String, Object> params) {
        return new Response(baseOperateJyeooService.getJyeooSchool(params));
    }
    
    /**
     * 根据schoolId获取菁优网状态
     * @param params schoolId
     * @return jyeooStatus: 1/2/3/4
     */
    @GetMapping("/school/status")
    @DongniRequest(operationName = "菁优网.根据schoolId获取菁优网状态", remark = {"TODO 权限"})
    public Response getJyeooSchoolStatus(Map<String, Object> params) {
        return new Response(baseOperateJyeooService.getJyeooSchoolStatus(params));
    }
    
    /**
     * 更新菁优网学校开通状态
     * @param params schoolId schoolName schoolGroupName
     *               schoolStatusAfter(更新后的状态) jyeooStatusAfter(更新后的状态)
     */
    @PostMapping("/school")
    @DongniRequest(operationName = "菁优网.更新菁优网学校开通状态", remark = {"TODO 权限"})
    public Response updateJyeooSchoolState(Map<String, Object> params) {
        baseOperateJyeooService.updateJyeooSchoolState(params);
        return new Response();
    }
    
    /**
     * 初始化菁优学校开通状态
     */
    @PostMapping("/school/init")
    @DongniRequest(operationName = "菁优网.初始化菁优学校开通状态", remark = {"TODO 权限"})
    public Response jyeooSchoolInit(Map<String, Object> params, MultipartFile importFile) {
        return new Response(baseOperateJyeooService.jyeooSchoolInit(params, importFile));
    }
    
    // ------------------------------------------------------------------------- area for edu director
    
    /**
     * 获取菁优网状态的字典信息
     * @param params 不需要
     * @return [] key, value, label, status, cnName, remark, sort
     */
    @GetMapping("/area/status/name")
    @DongniRequest(operationName = "菁优网.获取菁优网状态的字典信息(area)", remark = {"TODO 权限"})
    public Response getJyeooAreaStatusName(Map<String, Object> params) {
        return new Response(baseOperateJyeooService.getJyeooAreaStatusName(params));
    }

    @GetMapping("/area")
    @DongniRequest(operationName = "菁优网.查询区域菁优网开通状态列表", remark = {"TODO 权限"})
    public Response getJyeooArea(Map<String, Object> params) {
        return new Response(baseOperateJyeooService.getJyeooArea(params));
    }
    
    @GetMapping("/area/status")
    @DongniRequest(operationName = "菁优网.根据areaId获取菁优网状态", remark = {"TODO 权限"})
    public Response getJyeooAreaStatus(Map<String, Object> params) {
        return new Response(baseOperateJyeooService.getJyeooAreaStatus(params));
    }
    
    @PostMapping("/area")
    @DongniRequest(operationName = "菁优网.更新菁优网区域开通状态", remark = {"TODO 权限"})
    public Response updateJyeooAreaStatus(Map<String, Object> params) {
        baseOperateJyeooService.updateJyeooAreaState(params);
        return new Response();
    }
    
    @PostMapping("/area/children")
    @DongniRequest(operationName = "菁优网.开通指定区域的子区域菁优网", remark = {"TODO 权限"})
    public Response updateJyeooAreaChildrenStatus(Map<String, Object> params) {
        baseOperateJyeooService.updateJyeooAreaChildrenStatus(params);
        return new Response();
    }
}
