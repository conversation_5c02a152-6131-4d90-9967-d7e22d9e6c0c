package com.dongni.basedata.admin.controller;

import com.dongni.basedata.admin.service.IBaseAreaService;
import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2018/6/6 0006 16:46
 *  区域控制器
 */
@RestController
@RequestMapping(BaseDataConfig.CONTEXT_PATH + "/admin/area")
public class BaseAreaController extends BaseController {

    @Autowired
    private IBaseAreaService iBaseAreaService;

    /**
     * 获取树状结构的区域数据 增加@DongniNotRequireLogin用于注册页面
     *
     * @return 树状结构
     */
    @GetMapping("tree")
    @DongniRequest(operationName = "区域.查询树状结构的区域数据", remark = {"TODO 权限"})
    public Response getTree() {
        return new Response(iBaseAreaService.findTree(this.getParameterMap()));
    }

    /**
     * 查询当前区域下的子区域
     *
     * @return 树状结构
     */
    @GetMapping("child")
    @DongniRequest(operationName = "区域.查询当前区域下的子区域", remark = {"TODO 权限"})
    public Response getChild() {
        return new Response(iBaseAreaService.findChild(this.getParameterMap()));
    }

    /**
     * 新增区域
     *
     * @param parameterMap 区域信息
     * @return
     */
    @PostMapping("")
    @DongniRequest(operationName = "区域.新增区域", remark = {"TODO 权限"})
    public Response addArea(@RequestBody Map<String, Object> parameterMap) {
        return new Response(iBaseAreaService.addArea(parameterMap));
    }

    /**
     * 删除区域
     *
     * @param areaId 区域ID
     * @return
     */
    @PostMapping("/{areaId}")
    @DongniRequest(operationName = "区域.删除区域", remark = {"TODO 权限"})
    public Response deleteArea(@PathVariable String areaId) {
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("areaId", areaId);
        iBaseAreaService.deleteArea(parameterMap);
        return new Response();
    }

    /**
     * 更新区域
     * 非GET/POST治理 基础数据
     * @param parameterMap areaId：区域ID，parentAreaId：父区域ID，areaName：区域名称
     * @return
     */
    @PutMapping
    @Deprecated
    @DongniRequest(operationName = "区域.更新区域(PUT Deprecated)", remark = {"删除过期接口"})
    public Response updateAreaDeprecated(@RequestBody Map<String, Object> parameterMap) {
        iBaseAreaService.updateArea(parameterMap);
        return new Response();
    }

    /**
     * 更新区域
     *
     * @param parameterMap areaId：区域ID，parentAreaId：父区域ID，areaName：区域名称
     * @return
     */
    @PostMapping("/update")
    @DongniRequest(operationName = "区域.更新区域", remark = {"TODO 权限"})
    public Response updateArea(@RequestBody Map<String, Object> parameterMap) {
        iBaseAreaService.updateArea(parameterMap);
        return new Response();
    }

    /**
     * 更新区域排序
     */
    @PostMapping("updateAreaSort")
    @DongniRequest(operationName = "区域.更新区域排序", remark = {"TODO 权限"})
    public Response updateAreaSort() {
        iBaseAreaService.updateAreaSort(getParameterMap());
        return new Response();
    }


}
