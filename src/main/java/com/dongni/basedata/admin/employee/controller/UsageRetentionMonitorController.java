package com.dongni.basedata.admin.employee.controller;

import com.dongni.basedata.admin.employee.service.UsageRetentionMonitorService;
import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.mvc.controller.DownloadController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date: 2019/4/10 15:16
 */

@RestController
@RequestMapping(BaseDataConfig.CONTEXT_PATH +"/admin/employee/usageRetentionMonitor")
public class UsageRetentionMonitorController extends DownloadController {

    @Autowired
    private UsageRetentionMonitorService usageRetentionMonitorService;

    @GetMapping("")
    @DongniRequest(operationName = "用户使用情况.获取指定月份使用留存率", remark = {"TODO 权限"})
    public Response getUsageRetention(){
        return new Response(usageRetentionMonitorService.getUsageRetention(getParameterMap()));
    }

    @GetMapping("/export")
    @DongniRequest(operationName = "用户使用情况.导出指定月份使用留存率", remark = {"TODO 权限"})
    public Response exportExamUsage(){
        return new Response(usageRetentionMonitorService.exportUsageRetention(getParameterMap()));
    }
}
