package com.dongni.basedata.admin.part.time.reviewer.bean.params;

import com.dongni.commons.entity.BaseRequestParams;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.*;
import java.util.List;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/11/1 下午 05:45
 * @Version 1.0.0
 */
@Getter
@Setter
public class StatusPartTimeReviewerParam extends BaseRequestParams {
    @NotEmpty(message = "请勾选要操作的兼职审核员!")
    private List<Long> partTimeViewerIdList;

    @NotNull(message = "状态值不能为空!")
    @Max(value = 1, message = "状态值只能1和0!")
    @Min(value = 0, message = "状态值只能1和0!")
    private Integer status;
}
