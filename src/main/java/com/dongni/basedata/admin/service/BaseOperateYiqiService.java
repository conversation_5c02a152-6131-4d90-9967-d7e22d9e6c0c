package com.dongni.basedata.admin.service;

import com.dongni.basedata.export.school.service.CommonSchoolService;
import com.dongni.common.utils.ComparatorEx;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.third.yiqi.service.YiqiAccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> <br/>
 * @date 2021/07/07 <br/>
 *
 */
@Service
public class BaseOperateYiqiService {
    
    @Autowired
    private IBaseSchoolService baseSchoolService;
    
    @Autowired
    private CommonSchoolService commonSchoolService;
    
    @Autowired
    private IBaseAreaService baseAreaService;
    
    @Autowired
    private YiqiAccountService yiqiAccountService;
    
    // ------------------------------------------------------------------------- school
    
    /**
     * 获取一起网状态的字典信息
     * @param params 不需要
     * @return [] key, value, label, status, cnName, remark, sort
     */
    public List<Map<String, Object>> getYiqiSchoolStatusName(Map<String, Object> params) {
        Map<String, Object> key2YiqiStatusInfo = DictUtil.getDict("yiqiStatus");
        if (MapUtils.isEmpty(key2YiqiStatusInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "精品题库开通状态字典未配置");
        }
        return MapUtil.getListType(key2YiqiStatusInfo.values(), MapUtil::getMap);
    }
    
    /**
     * 查询学校一起网学校开通状态列表
     * @param params stage(根据年段查询)
     *               [hasChild] [areaId] [areaCode]
     *               [keyword](根据学校名称模糊查询)
     *               [yiqiStatus](根据状态查询)
     *               [endDate](根据到期时间查询)
     *               [pageNo] [pageSize] 分页支持
     * @return totalCount
     *         schools[]
     *            schoolId schoolName schoolGroupName stage schoolStatus schoolSort
     *            schoolPhone schoolPhoneAes address latitude longitude
     *            areaId areaName areaCode realAreaName(全路径) memberType
     *            stage yiqiStatus expireTime yiqiUsername
     */
    public Map<String, Object> getYiqiSchool(Map<String, Object> params) {
        Verify.of(params).isInteger("stage").verify();
        int stage = MapUtil.getInt(params, "stage");
        //调用学校管理接口获取需要查询的学校
        Integer currentIndex = MapUtil.getIntNullable(params.remove("currentIndex"));
        Integer pageSize = MapUtil.getIntNullable(params.remove("pageSize"));
        
        //TODO[前端] 多学段-
        // 这个方法返回的stage是有问题的，后面直接覆盖掉
        Map<String, Object> schools = baseSchoolService.findSchools(params);
        List<Map<String, Object>> schoolList = MapUtil.getCast(schools, "schools");
        if (CollectionUtils.isEmpty(schoolList)) {
            return MapUtil.of("totalCount", 0, "schools", new ArrayList<>());
        }
        // 删掉YiqiStatus 不知道为什么有该字段
        schoolList.forEach(item->{
            item.remove("YiqiStatus");
            item.put("stage", stage);
        });
        
        //查询在t_yiqi_account表中的yiqi状态，不在的学校状态为“未启用”
        List<Long> schoolIdList = schoolList.stream()
                .map(a -> Long.parseLong(a.get("schoolId").toString()))
                .collect(Collectors.toList());
        params.put("schoolIdList", schoolIdList);
        //得到非“未开启”状态的学校信息
        List<Map<String, Object>> yiqiSchoolList = yiqiAccountService.getYiqiSchoolList(params);
        Map<Long,Map<String,Object>> schoolId2yiqiSchool = yiqiSchoolList
                .stream().collect(toMap(s->Long.valueOf(s.get("schoolId").toString()), s->s));
    
        int runnable = DictUtil.getDictValue("yiqiStatus", "runnable");
        for (Map<String,Object> school : schoolList){
            long schoolId = MapUtil.getLong(school, "schoolId");
            // 找不到就是未开启的
            Map<String, Object> yiqiSchool = schoolId2yiqiSchool.get(schoolId);
            Integer yiqiStatus = MapUtil.getInt(yiqiSchool, "yiqiStatus", runnable);
            String expireTime = MapUtil.getString(yiqiSchool, "expireTime", null, o -> DateUtil.formatDate((Date) o));
            String yiqiUsername = MapUtil.getStringNullable(yiqiSchool, "yiqiUsername");
            school.put("yiqiStatus", yiqiStatus);
            school.put("expireTime", expireTime);
            school.put("yiqiUsername", yiqiUsername);
        }
    
        Stream<Map<String, Object>> schoolStream = schoolList.stream();
        
        //按状态过滤
        Integer yiqiStatus = MapUtil.getIntNullable(params, "yiqiStatus");
        if (yiqiStatus != null) {
            schoolStream = schoolStream.filter(item -> yiqiStatus == MapUtil.getInt(item, "yiqiStatus"));
        }
        
        //按结束日期过滤
        if (params.get("endDate") != null && params.get("endDate") != "") {
            schoolStream = schoolStream
                    .filter(a -> a.get("expireTime") != null)
                    .filter(a -> a.get("expireTime").toString().equals(params.get("endDate").toString()));
        }
    
        //一起状态-可用状态
        Set<Integer> runningSet = Stream.of(
                DictUtil.getDictValue("yiqiStatus", "trial"),
                DictUtil.getDictValue("yiqiStatus", "running")
        ).collect(Collectors.toSet());
        //按到期时间升序
        schoolStream = schoolStream.sorted(ComparatorEx
                // 不是可用状态的放在后面 哪怕其有过期时间 其过期时间仅用来看的
                .<Map<String, Object>, Boolean>desc(o -> runningSet.contains(MapUtil.getInt(o, "yiqiStatus")))
                .thenAscNullLast(o -> MapUtil.getStringNullable(o, "expireTime"))
                .thenAsc(o -> MapUtil.getLong(o, "schoolId"))
        );
        
        schoolList = schoolStream.collect(toList());
        int count = schoolList.size();
        if (ObjectUtil.isIntegerNatural(currentIndex) && ObjectUtil.isIntegerPositive(pageSize)) {
            schoolList = schoolList.stream().skip(currentIndex).limit(pageSize).collect(Collectors.toList());
        }
        
        return MapUtil.of("totalCount", count, "schools", schoolList);
    }
    
    /**
     * 获取一起作业网学校状态
     * @param params schoolId 一个学校可能有多个学段 userInfo
     * @return yiqiStatus                一起开通状态 dict.yiqiStatus 这是一个总的状态 优先级 正式 试用 到期 未启用 关闭
     *         stageMapStatus            {stage -> yiqiStatus}  学段对应具体的一起开通状态
     *         [warningMessage]          "存在状态优先级未设置的一起状态"
     *         [noStatusLevelStageList]  stage的yiqiStatus未设置等级，防止数据库数据异常
     */
    public Map<String, Object> getYiqiSchoolStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .verify();
        List<Map<String, Object>> yiqiAccountSchoolList = yiqiAccountService.getYiqiAccountBySchoolId(params);
        return getYiqiStatus(yiqiAccountSchoolList);
    }
    
    /**
     * 更新一起作业网学校开通状态-学校申报专用
     * @param params 同updateYiqiSchoolStatus 见最后调用
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateYiqiSchoolStatusForSchoolApplication(Map<String, Object> params) {
        if (params == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "参数必须提供");
        }
        params.put("__updateType__", "schoolApplication");
        updateYiqiSchoolStatus(params);
    }
    
    /**
     * 更新一起作业网学校开通状态
     *    超级管理员-学校运营-精品题库开通 启用/关闭 需传递stage 可以传递绑定yiqiUsername
     *    学校申报-精品题库 正式/试用/关闭 传递__updateType__=schoolApplication不需要传递stage 全部学校学段开通 且没有yiqiUsername
     * @param params schoolId schoolName schoolGroupName
     *               yiqiStatusAfter(更新后的状态)
     *               [stage] 学段 前端调用必须提供
     *               [yiqiUsername] 不提供使用原来的，原来不存在使用懂你虚拟学校的，如果虚拟学校不存在，保存空字符串
     *               __updateType__ 用于判断是否为学校申报过来的
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateYiqiSchoolStatus(Map<String, Object> params) {
        Verify verify = Verify.of(params)
                .isValidId("schoolId")
                .isNotBlank("schoolName")
                .isNotBlank("schoolGroupName")
                .isNotBlank("yiqiStatusAfter");
        
        int runnable = DictUtil.getDictValue("yiqiStatus", "runnable"); // 未开启
        int blocked = DictUtil.getDictValue("yiqiStatus", "blocked");   // 到期
        // 调用方要求更新的最后状态
        int yiqiStatusAfter = MapUtil.getInt(params, "yiqiStatusAfter");
        if (yiqiStatusAfter == runnable || yiqiStatusAfter == blocked) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                    "状态参数错误，不能设置为 " + DictUtil.getDictLabelByValue("yiqiStatus", runnable)
                    + " 或 " + DictUtil.getDictLabelByValue("yiqiStatus", blocked)
            );
        }
        
        boolean schoolApplication = "schoolApplication".equals(MapUtil.getStringNullable(params, "__updateType__"));
        if (!schoolApplication) {
            verify.isInteger("stage");
        }
        verify.verify();
        
        // 支持多学段，每一个学段插入一个,
        List<Integer> schoolStage = commonSchoolService.getSchoolStage(params);
        // 需要处理的学校学段
        Integer stageNullable = MapUtil.getIntNullable(params, "stage");
        Set<Integer> stageSet = schoolStage.stream()
                .filter(Objects::nonNull)
                // 学校申报 屏蔽小学
                .filter(stage -> !schoolApplication || stage != 1)
                // 非学校只取接口要求的stage
                .filter(stage -> stageNullable == null || (stageNullable.equals(stage)))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(stageSet)) { return; }
        
        // 根据schoolId+[stage]获取学校账号信息
        List<Map<String, Object>> yiqiSchoolList = yiqiAccountService.getYiqiSchoolList(params);
        Map<Integer, Map<String, Object>> stage2yiqiSchool = yiqiSchoolList.stream()
                .collect(toMap(item -> MapUtil.getInt(item, "stage"), item -> item));
    
        int trial = DictUtil.getDictValue("yiqiStatus", "trial");       // 试用
        int running = DictUtil.getDictValue("yiqiStatus", "running");   // 正式
        int dead = DictUtil.getDictValue("yiqiStatus", "dead");         // 关闭
        
        // 对每一个stage进行操作
        for (Integer stage : stageSet) {
            // 当前一起作业网
            Map<String, Object> yiqiSchool = stage2yiqiSchool.get(stage);
            int yiqiStatusBefore = MapUtil.getInt(yiqiSchool, "yiqiStatus", runnable);
            
            // yiqiUsername  获取优先级 接口获取，yiqiSchool获取，默认的虚拟账号获取(懂你学校)
            // 关闭的不需要获取默认的
            boolean needDefaultYiqiUsername = true;
            String expireTime = null;
            
            // 试用 只有未开启的可以设置为试用
            if (yiqiStatusAfter == trial) {
                if (yiqiStatusBefore != runnable) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                            DictUtil.getDictLabelByValue("yiqiStatus", yiqiStatusBefore) + "状态不能被设置为" +
                            DictUtil.getDictLabelByValue("yiqiStatus", yiqiStatusAfter));
                }
                LocalDate expireLocalDate = LocalDate.now().plusDays(yiqiAccountService.getYiqiTrialDays());
                expireTime = DateUtil.formatDate(Date.from(expireLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            
            //正式
            else if (yiqiStatusAfter == running) {
                if (yiqiStatusBefore == running) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                            DictUtil.getDictLabelByValue("yiqiStatus", yiqiStatusBefore) + "状态不能被设置为" +
                            DictUtil.getDictLabelByValue("yiqiStatus", yiqiStatusAfter));
                }
                //如果之前的状态为试用状态，在原有的过期时间增加365天
                LocalDate expireLocalDate = null;
                if (yiqiStatusBefore == trial) {
                    Date yiqiSchoolExpireTime = MapUtil.getCast(yiqiSchool, "expireTime");
                    if (yiqiSchoolExpireTime != null) {
                        expireLocalDate = LocalDate.parse(DateUtil.formatDate(yiqiSchoolExpireTime));
                    }
                }
                if (expireLocalDate == null) {
                    expireLocalDate = LocalDate.now();
                }
                expireLocalDate = expireLocalDate.plusDays(yiqiAccountService.getYiqiRunningDays());
                expireTime = DateUtil.formatDate(Date.from(expireLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }
            // 关闭
            else if (yiqiStatusAfter == dead) {
                // 对于关闭的保留原来的 不管
                expireTime = MapUtil.getStringNullable(yiqiSchool, "expireTime");
                // 关闭不需要账号
                needDefaultYiqiUsername = false;
            }
    
            String yiqiUsername = "";
            if (ObjectUtil.isNotBlank(params.get("yiqiUsername"))) {
                yiqiUsername = MapUtil.getString(params, "yiqiUsername").trim();
            } else if (ObjectUtil.isNotBlank(MapUtil.getStringNullable(yiqiSchool, "yiqiUsername"))){
                yiqiUsername = MapUtil.getString(yiqiSchool, "yiqiUsername").trim();
            }
            if (needDefaultYiqiUsername && ObjectUtil.isBlank(yiqiUsername)) {
                Map<String, Object> getDefaultParams = MapUtil.copyWithUser(params,"userId", "userName");
                getDefaultParams.put("schoolId", -stage);
                getDefaultParams.put("stage", stage);
                // 指定了schoolId-stage 要么有 要么只有一个
                List<Map<String, Object>> defaultYiqiSchoolList = yiqiAccountService.getYiqiSchoolList(getDefaultParams);
                if (CollectionUtils.isNotEmpty(defaultYiqiSchoolList)) {
                    Map<String, Object> defaultYiqiSchool = defaultYiqiSchoolList.get(0);
                    if (ObjectUtil.isNotBlank(MapUtil.getStringNullable(defaultYiqiSchool, "yiqiUsername"))) {
                        yiqiUsername = MapUtil.getString(defaultYiqiSchool, "yiqiUsername").trim();
                    }
                }
            }
            
            Map<String, Object> setParams = MapUtil.copyWithUser(params, "schoolId", "schoolName", "schoolGroupName");
            setParams.put("yiqiStatus", yiqiStatusAfter);
            setParams.put("stage", stage);
            setParams.put("yiqiUsername", yiqiUsername);
            setParams.put("expireTime", expireTime);
            yiqiAccountService.insertOrUpdateYiqiAccount(setParams);
            setParams.put("schoolStatusBefore", yiqiStatusBefore);
            setParams.put("schoolStatusAfter", yiqiStatusAfter);
            setParams.put("yiqiStatusBefore", yiqiStatusBefore);
            setParams.put("yiqiStatusAfter", yiqiStatusAfter);
            yiqiAccountService.insertYiqiStatusLog(setParams);
        }
    }
    
    // ------------------------------------------------------------------------- area for edu director
    
    /**
     * 获取一起作业网状态的字典信息 教育局长
     * @param params 不需要
     * @return [] key, value, label, status, cnName, remark, sort
     */
    public List<Map<String, Object>> getYiqiAreaStatusName(Map<String, Object> params) {
        // 学校的状态
        List<Map<String, Object>> yiqiSchoolStatusName = getYiqiSchoolStatusName(params);
        // 去除试用的trial
        return yiqiSchoolStatusName.stream()
                .filter(item -> !"trial".equals(MapUtil.getString(item, "key")))
                .collect(toList());
    }
    
    /**
     * 获取一起作业网区域开通状态列表
     * @param params areaId
     * @return areaId areaName areaCode,
     *         parentAreaId level snName areaNo areaSort
     *         accountList: []
     *            stage, yiqiStatus, expireTime, remark, yiqiUsername
     */
    public Map<String, Object> getYiqiArea(Map<String, Object> params) {
        Verify.of(params).isValidId("areaId").verify();
        
        Map<String, Object> areaInfo = baseAreaService.getArea(params);
        if (MapUtils.isEmpty(areaInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "区域信息不存在");
        }
    
        List<Map<String, Object>> yiqiAccountAreaList = yiqiAccountService.getYiqiAccountAreaByAreaId(params);
        Map<Integer, Map<String, Object>> stage2yiqiAccount = yiqiAccountAreaList.stream()
                .collect(toMap(item -> MapUtil.getInt(item, "stage"), item -> item));
        
        int runnable = DictUtil.getDictValue("yiqiStatus", "runnable");
        List<Map<String, Object>> yiqiAccountInfoList = new ArrayList<>();
        for (Integer stageEnable : getEnableStageList()) {
            Map<String, Object> yiqiAccount = stage2yiqiAccount.get(stageEnable);
            int yiqiStatus = MapUtil.getInt(yiqiAccount, "yiqiStatus", runnable);
            Map<String, Object> yiqiAccountInfo = new HashMap<>();
            yiqiAccountInfo.put("stage", stageEnable);
            yiqiAccountInfo.put("yiqiStatus", yiqiStatus);
            yiqiAccountInfo.put("expireTime", MapUtil.getCast(yiqiAccount, "expireTime"));
            yiqiAccountInfo.put("remark", MapUtil.getStringNullable(yiqiAccount, "remark"));
            yiqiAccountInfo.put("yiqiUsername", MapUtil.getStringNullable(yiqiAccount, "yiqiUsername"));
            yiqiAccountInfoList.add(yiqiAccountInfo);
        }
        
        areaInfo.put("accountList", yiqiAccountInfoList);
        return areaInfo;
    }
    
    /**
     * 获取一起作业网区域开通状态
     * @param params areaId 一个areaId可能有多个学段
     * @return yiqiStatus                一起开通状态 dict.yiqiStatus 这是一个总的状态 优先级 正式 试用 到期 未启用 关闭
     *         stageMapStatus            {stage -> yiqiStatus}  学段对应具体的一起开通状态
     *         [warningMessage]          "存在状态优先级未设置的一起状态"
     *         [noStatusLevelStageList]  stage的yiqiStatus未设置等级，防止数据库数据异常
     */
    public Map<String, Object> getYiqiAreaStatus(Map<String, Object> params) {
        Verify.of(params).isValidId("areaId").verify();
        List<Map<String, Object>> yiqiAccountAreaList = yiqiAccountService.getYiqiAccountAreaByAreaId(params);
        return getYiqiStatus(yiqiAccountAreaList);
    }
    
    /**
     * 设置区域的状态
     * @param params areaId yiqiStatusAfter
     *               expireTime 不传无限制 传了有限制 例: 时间戳毫秒
     */
    public void insertOrUpdateYiqiAreaStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .isInteger("yiqiStatusAfter")
                .verify();
        
        Map<String, Object> areaInfo = baseAreaService.getArea(params);
        if (MapUtils.isEmpty(areaInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "区域信息不存在");
        }
        
        Map<String, Object> updateParams = MapUtil.copyWithUser(params, "areaId", "yiqiStatusAfter", "expireTime");
        MapUtil.copy(areaInfo, updateParams, "areaId", "areaName");
        updateParams.put("stageList", getEnableStageList());
        yiqiAccountService.insertOrUpdateYiqiAccountArea(updateParams);
    }
    
    /**
     * 应用到子节点 将areaId的一起账号信息应用到子节点
     * @param params areaId
     * @return nullable
     *         warningMessage: 该节点没有子节点
     */
    public Map<String, Object> insertOrUpdateYiqiAreaChildrenStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();
        Map<String, Object> areaInfo = baseAreaService.getArea(params);
        if (MapUtils.isEmpty(areaInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "区域信息不存在");
        }
        
        final long areaId = MapUtil.getLong(params, "areaId");
        List<Map<String, Object>> childrenAreaList = baseAreaService.getAreaListByAreaCode(areaInfo).stream()
                .filter(item -> MapUtil.getLong(item, "areaId") != areaId)
                .collect(toList());
        if (CollectionUtils.isEmpty(childrenAreaList)) {
            return MapUtil.of("warningMessage", "该节点没有子节点");
        }
        
        params.put("childrenAreaList", childrenAreaList);
        yiqiAccountService.insertOrUpdateYiqiAreaChildrenStatus(params);
        return null;
    }
    
    /**
     * 获取支持的学段
     * @return 数据保证不重复
     */
    private List<Integer> getEnableStageList() {
        return Stream.of(
                DictUtil.getDictValue("stage", "middle"),
                DictUtil.getDictValue("stage", "high")
        ).distinct().collect(Collectors.toList());
    }
    
    /**
     * 处理状态 因为一个学校/区域维度因多学段可能有多条数据
     * @param yiqiAccountList item:{yiqiStatus, stage}
     * @return yiqiStatus                一起开通状态 dict.yiqiStatus 这是一个总的状态 优先级 正式 试用 到期 未启用 关闭
     *         stageMapStatus            {stage -> yiqiStatus}  学段对应具体的一起开通状态
     *         [warningMessage]          "存在状态优先级未设置的一起状态"
     *         [noStatusLevelStageList]  stage的yiqiStatus未设置等级，防止数据库数据异常
     */
    private Map<String, Object> getYiqiStatus(List<Map<String, Object>> yiqiAccountList) {
        int runnable = DictUtil.getDictValue("yiqiStatus", "runnable"); // 未开启
        if (CollectionUtils.isEmpty(yiqiAccountList)) {
            return MapUtil.of("yiqiStatus", runnable, "stageMapStatus", new HashMap<>());
        }
        
        int trial = DictUtil.getDictValue("yiqiStatus", "trial");       // 试用
        int blocked = DictUtil.getDictValue("yiqiStatus", "blocked");   // 到期
        int running = DictUtil.getDictValue("yiqiStatus", "running");   // 正式
        int dead = DictUtil.getDictValue("yiqiStatus", "dead");         // 关闭
        
        // 一起开通状态 dict.yiqiStatus 这是一个总的状态 优先级 正式 试用 到期 未开启 关闭
        Map<Integer, Integer> yiqiStatusMapLevel = new HashMap<>();
        yiqiStatusMapLevel.put(running, yiqiStatusMapLevel.size());
        yiqiStatusMapLevel.put(trial, yiqiStatusMapLevel.size());
        yiqiStatusMapLevel.put(blocked, yiqiStatusMapLevel.size());
        yiqiStatusMapLevel.put(runnable, yiqiStatusMapLevel.size());
        yiqiStatusMapLevel.put(dead, yiqiStatusMapLevel.size());
    
        int allYiqiStatus = dead;
        Map<Integer, Integer> stageMapStatus = new HashMap<>();
        // 一起状态未设置优先级(not in yiqiStatusMapLevel)的stageList
        List<Integer> noStatusLevelStageList = new ArrayList<>();
        for (Map<String, Object> yiqiAccount : yiqiAccountList) {
            //查看一起作业网是否在使用中，是否更新到期状态
            int yiqiStatus = Integer.parseInt(yiqiAccount.get("yiqiStatus").toString());
            int stage = MapUtil.getInt(yiqiAccount, "stage");
            // 设置各个学段的状态
            stageMapStatus.put(stage, yiqiStatus);
            // 设置整个学校的总状态
            Integer yiqiStatusLevel = yiqiStatusMapLevel.get(yiqiStatus);
            if (yiqiStatusLevel == null) {
                // 防止数据库的数据异常，如 yiqiStatus=100并不在设置的优先级内部
                yiqiStatusLevel = Integer.MAX_VALUE;
                noStatusLevelStageList.add(stage);
            }
            if (yiqiStatusLevel < yiqiStatusMapLevel.get(allYiqiStatus)) {
                allYiqiStatus = yiqiStatus;
            }
        }
    
        Map<String, Object> result = MapUtil.of("yiqiStatus", allYiqiStatus, "stageMapStatus", stageMapStatus);
        if (CollectionUtils.isNotEmpty(noStatusLevelStageList)) {
            result.put("warningMessage", "存在状态优先级未设置的一起状态");
            result.put("noStatusLevelStageList", noStatusLevelStageList);
        }
        return result;
    }
}
