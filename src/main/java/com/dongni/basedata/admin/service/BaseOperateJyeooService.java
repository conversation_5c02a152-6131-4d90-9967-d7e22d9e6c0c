package com.dongni.basedata.admin.service;

import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.ResourceConfig;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.third.jyeoo.service.JyeooAreaService;
import com.dongni.tiku.third.jyeoo.service.JyeooAreaStatusLogService;
import com.dongni.tiku.third.jyeoo.service.JyeooHandleService;
import com.dongni.tiku.third.jyeoo.service.JyeooSchoolService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> <br/>
 * @date 2021/07/06 <br/>
 *  菁优题库权限开通的操作
 *     学校级别 school
 *     区域级别 area  教育局长使用
 */
@Service
public class BaseOperateJyeooService {

    @Autowired
    private JyeooSchoolService jyeooSchoolService;
    @Autowired
    private IBaseSchoolService iBaseSchoolService;
    @Autowired
    private JyeooHandleService jyeooHandleService;
    @Autowired
    private JyeooAreaService jyeooAreaService;
    @Autowired
    private IBaseAreaService iBaseAreaService;
    @Autowired
    private JyeooAreaStatusLogService jyeooAreaStatusLogService;
    
    /**
     * 菁优试用时间
     */
    private final static int JYEOO_TRIAL_DAYS = 90;
    /**
     * 菁优正式使用时间时间
     */
    private final static int JYEOO_RUNNING_DAYS = 365;
    
    // ------------------------------------------------------------------------- school
    
    /**
     * 获取菁优网状态的字典信息
     * @param params 不需要
     * @return [] key, value, label, status, cnName, remark, sort
     */
    public List<Map<String, Object>> getJyeooSchoolStatusName(Map<String, Object> params) {
        Map<String, Object> key2jyeooStatusInfo = DictUtil.getDict("jyeooStatus");
        if (MapUtils.isEmpty(key2jyeooStatusInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "菁优状态字典未配置");
        }
        return MapUtil.getListType(key2jyeooStatusInfo.values(), MapUtil::getMap);
    }
    
    /**
     * 查询学校菁优网学校开通状态列表
     * @param params hasChild areaId areaCode
     *               keyword(根据学校名称模糊查询)
     *               jyeooStatus(根据状态查询)
     *               endDate(根据到期时间查询)
     *               pageNo pageSize 分页支持
     * @return totalCount
     *         schools[]
     *            schoolId schoolName schoolGroupName stage schoolStatus schoolSort
     *            schoolPhone schoolPhoneAes address latitude longitude
     *            areaId areaName areaCode realAreaName(全路径) memberType
     *            jyeooStatus expireTime
     */
    public Map<String, Object> getJyeooSchool(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("pageNo")
                .isNumeric("pageSize")
                .verify();

        //菁优状态
        int runnable = DictUtil.getDictValue("jyeooStatus", "runnable");

        //调用学校管理接口获取需要查询的学校
        int currentIndexTemp = Integer.parseInt(params.get("currentIndex").toString());
        int pageSizeTemp = Integer.parseInt(params.get("pageSize").toString());
        params.remove("currentIndex");
        params.remove("pageSize");
        //TODO[前端] 多学段-
        Map<String, Object> schools = iBaseSchoolService.findSchools(params);
        List<Map<String, Object>> schoolList = MapUtil.getCast(schools, "schools");
        if (CollectionUtils.isEmpty(schoolList)) {
            return MapUtil.of("totalCount", 0, "schools", new ArrayList<>());
        }

        //查询在t_jyeoo_school表中的jyeoo状态，不在的学校状态为“未启用”
        List<Long> schoolIdList = schoolList.stream()
                .map(a -> Long.parseLong(a.get("schoolId").toString()))
                .collect(toList());
        params.put("schoolIdList", schoolIdList);
        //得到非“未开启”状态的学校信息
        List<Map<String, Object>> jyeooSchoolList = jyeooSchoolService.getJyeooSchoolListBySchoolIdList(params);
        Map<Long, Map<String, Object>> schoolId2jyeooSchool = jyeooSchoolList.stream()
                .collect(toMap(item -> MapUtil.getLong(item, "schoolId"), item -> item));
    
        //数据拼接
        for (Map<String, Object> school : schoolList) {
            long schoolId = MapUtil.getLong(school, "schoolId");
            // 找不到就是未开启的
            Map<String, Object> jyeooSchool = schoolId2jyeooSchool.get(schoolId);
            Integer jyeooStatus = MapUtil.getInt(jyeooSchool, "jyeooStatus", runnable);
            String expireTime = MapUtil.getString(jyeooSchool, "expireTime", null, o -> DateUtil.formatDate((Date) o));
            school.put("jyeooStatus", jyeooStatus);
            school.put("expireTime", expireTime);
        }
    
        Stream<Map<String, Object>> schoolStream = schoolList.stream();
        
        //按状态过滤
        Integer jyeooStatus = MapUtil.getIntNullable(params, "jyeooStatus");
        if (jyeooStatus != null) {
            schoolStream = schoolStream.filter(item -> jyeooStatus == MapUtil.getInt(item, "jyeooStatus"));
        }
        
        //按结束日期过滤
        if (params.get("endDate") != null && params.get("endDate") != "") {
            schoolStream = schoolStream
                    .filter(a -> a.get("expireTime") != null)
                    .filter(a -> a.get("expireTime").toString().equals(params.get("endDate").toString()));
        }
        
        //按到期时间升序
        schoolStream = schoolStream.sorted(Comparator
                .comparing(a -> a.get("expireTime") == null ? null : a.get("expireTime").toString(),
                        Comparator.nullsLast(String::compareTo)
                )
        );
        
        schoolList = schoolStream.collect(toList());
        int count = schoolList.size();
        schoolList = schoolList.stream().skip(currentIndexTemp).limit(pageSizeTemp).collect(toList());

        return MapUtil.of("totalCount", count, "schools", schoolList);
    }

    /**
     * 根据schoolId获取菁优网状态
     * @param params schoolId
     * @return jyeooStatus: 1/2/3/4
     */
    public Map<String, Object> getJyeooSchoolStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .verify();
        Map<String, Object> jyeooSchool = jyeooSchoolService.getJyeooSchoolBySchoolId(params);
        if (MapUtils.isEmpty(jyeooSchool)) {
            // 查不到就是未启用
            return MapUtil.of("jyeooStatus", DictUtil.getDictValue("jyeooStatus", "runnable"));
        } else {
            return MapUtil.of("jyeooStatus", jyeooSchool.get("jyeooStatus"));
        }
    }

    /**
     * 更新菁优网学校开通状态
     * @param params schoolId schoolName schoolGroupName
     *               schoolStatusAfter(更新后的状态) jyeooStatusAfter(更新后的状态)
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateJyeooSchoolState(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isValidId("schoolStatusAfter")
                .isValidId("jyeooStatusAfter")
                .isNotBlank("schoolName")
                .isNotBlank("schoolGroupName")
                .verify();
        //菁优状态  runnable未开启  blocked过期
        int runnable = DictUtil.getDictValue("jyeooStatus", "runnable");
        int blocked = DictUtil.getDictValue("jyeooStatus", "blocked");

        int jyeooStatusAfter = Integer.parseInt(params.get("jyeooStatusAfter").toString());
        int schoolStatusAfter = Integer.parseInt(params.get("schoolStatusAfter").toString());
        // 不能将状态设置为 未启用(初始状态) 过期(系统设置)
        if (jyeooStatusAfter == runnable || jyeooStatusAfter == blocked) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "状态参数错误");
        }

        params.put("jyeooStatus", jyeooStatusAfter);
        params.put("schoolStatus", schoolStatusAfter);

        //菁优状态
        int trial = DictUtil.getDictValue("jyeooStatus", "trial");
        int running = DictUtil.getDictValue("jyeooStatus", "running");
        int dead = DictUtil.getDictValue("jyeooStatus", "dead");

        //根据schoolId获取学校信息
        Map<String, Object> jyeooSchool = jyeooSchoolService.getJyeooSchoolBySchoolId(params);
        //当前菁优网和学校状态
        int jyeooStatusBefore = MapUtil.getInt(jyeooSchool, "jyeooStatus", runnable);
        int schoolStatusBefore = MapUtil.getInt(jyeooSchool, "schoolStatus", runnable);
        params.put("jyeooStatusBefore", jyeooStatusBefore);
        params.put("schoolStatusBefore", schoolStatusBefore);

        // 设置为trial试用  前置状态为runnable未开启
        if (jyeooStatusAfter == trial) {
            if (jyeooStatusBefore != runnable) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                        DictUtil.getDictLabelByValue("jyeooStatus", jyeooStatusBefore) + "状态不能被设置为" +
                                DictUtil.getDictLabelByValue("jyeooStatus", jyeooStatusAfter));
            }

            params.put("expireTime", LocalDate.now().plusDays(JYEOO_TRIAL_DAYS));
            jyeooSchoolService.updateJyeooState(params);
            //状态变化日志记录
            jyeooSchoolService.insertJyeooStatusLog(params);

            //请求菁优网接口
            params.put("schoolIds", Collections.singletonList(params.get("schoolId").toString()));
            jyeooHandleService.updateJyeooSchool(params);
            return;
        }

        // 设置为running正式 前置状态为trial试用 dead关闭 blocked过期
        if (jyeooStatusAfter == running) {
            // 2021-02-19 申报过后可以直接从未开启状态进入正式状态
            if (jyeooStatusBefore == running) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                        DictUtil.getDictLabelByValue("jyeooStatus", jyeooStatusBefore) + "状态不能被设置为" +
                                DictUtil.getDictLabelByValue("jyeooStatus", jyeooStatusAfter));
            }

            //如果之前的状态为试用状态，在原有的过期时间增加365天
            if (jyeooStatusBefore == trial) {
                String expireTime = DateUtil.formatDate((Date) jyeooSchool.get("expireTime"));
                params.put("expireTime", LocalDate.parse(expireTime).plusDays(JYEOO_RUNNING_DAYS));
                jyeooSchoolService.updateJyeooState(params);
            } else {
                params.put("expireTime", LocalDate.now().plusDays(JYEOO_RUNNING_DAYS));
                jyeooSchoolService.updateJyeooState(params);
            }
            //状态变化日志记录
            jyeooSchoolService.insertJyeooStatusLog(params);
            //请求菁优网接口
            params.put("schoolIds", Collections.singletonList(params.get("schoolId").toString()));
            jyeooHandleService.updateJyeooSchool(params);
            return;
        }

        // 设置为dead关闭 前置状态为trial试用 running正式
        if (jyeooStatusAfter == dead) {
            if (jyeooStatusBefore == runnable || jyeooStatusBefore == dead) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                        DictUtil.getDictLabelByValue("jyeooStatus", jyeooStatusBefore) + "状态不能被设置为" +
                                DictUtil.getDictLabelByValue("jyeooStatus", jyeooStatusAfter));
            }

            params.put("expireTime", jyeooSchool.get("expireTime"));
            jyeooSchoolService.updateJyeooState(params);
            //状态变化日志记录
            jyeooSchoolService.insertJyeooStatusLog(params);
        }
    }

    /**
     * 初始化菁优学校开通状态
     */
    public Map<String, Object> jyeooSchoolInit(Map<String, Object> params, MultipartFile importFile) {
        Map<String,Object> rs = new HashMap<>();

        // 获取文件流
        String fileName = importFile.getOriginalFilename();
        if(fileName == null || (!fileName.endsWith("xls") && !fileName.endsWith("xlsx"))){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        List<String> header = new ArrayList<>();
        List<Map<String,String>> body = new ArrayList<>();
        FileStorageTemplate.local(fileStorage -> {
            File target = new File(fileStorage.getRootPath()
                    + ResourceConfig.getString("rollExportPath") +"simple/import/"+fileName);
            InputStream is = null;
            try{
                is = importFile.getInputStream();
                FileUtils.copyInputStreamToFile(is,target);
            }catch (IOException e){
                throw new CommonException(ResponseStatusEnum.FILE_ERROR,e);
            }finally {
                IOUtils.closeQuietly(is);
            }
            header.addAll(ExcelUtil.getHeader(target));
            // 获取文件体
            List<String> keys = Arrays.asList("index", "schoolId", "schoolName", "schoolGroupName", "jyeooStatus", "trialDate", "runningDate");
            body.addAll(ExcelUtil.getBody(target, keys));
        });

        // 参数校验体
        List<String> validator = new ArrayList<>();

        // 获取文件头
        if(CollectionUtils.isEmpty(header) || header.size() < 7
                || !"序号".equals(header.get(0))
                || !"学校ID".equals(header.get(1))
                || !"学校名称".equals(header.get(2))
                || !"学校别名".equals(header.get(3))
                || !"菁优状态".equals(header.get(4))
                || !"试用时间".equals(header.get(5))
                || !"开通时间".equals(header.get(6))
        ){
            validator.add("表头信息不完整，从左往右依次排列为：序号、学校ID、学校名称、学校别名、菁优状态、试用时间、开通时间");
        }

        if(CollectionUtils.isEmpty(body)){
            validator.add("表体内容不存在");
        }


        body.forEach(m->{
            String rowNum = m.get("rowNum");
            if(ObjectUtil.isBlank(m.get("schoolId"))){
                validator.add("第"+rowNum+"行，学校ID未填写");
            }

            if(ObjectUtil.isBlank(m.get("schoolName"))){
                validator.add("第"+rowNum+"行，学校未填写");
            }

            if(ObjectUtil.isBlank(m.get("schoolGroupName"))){
                validator.add("第"+rowNum+"行，学校别名未填写");
            }

            if(ObjectUtil.isBlank(m.get("jyeooStatus"))){
                validator.add("第"+rowNum+"行，菁优状态未填写");
            }


        });

        // 校验参数
        if(CollectionUtils.isNotEmpty(validator)){
            rs.put("validator",validator);
            return rs;
        }

        // 数据库更新
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        body.forEach(a->{
            if (ObjectUtil.isValueEquals(a.get("jyeooStatus"),"试用")) {
                LocalDate trialDate = LocalDate.parse(a.get("trialDate"), df);
                a.put("expireTime",trialDate.plusDays(JYEOO_TRIAL_DAYS).toString());
                a.put("date", trialDate.toString());
                a.put("schoolStatus", "1");
                a.put("jyeooStatus", "1");
            } else if (ObjectUtil.isValueEquals(a.get("jyeooStatus"),"正式")) {
                LocalDate runningDate = LocalDate.parse(a.get("runningDate"), df);
                a.put("expireTime",runningDate.plusDays(JYEOO_RUNNING_DAYS).toString());
                a.put("date", runningDate.toString());
                a.put("schoolStatus", "3");
                a.put("jyeooStatus", "3");
            }
        });

        //初始化
        params.put("jyeooSchool", body);
        int count = jyeooSchoolService.initJyeooStatus(params);

        return MapUtil.of("count",count);
    }
    
    // ------------------------------------------------------------------------- area for edu director
    
    /**
     * 获取菁优网状态的字典信息 教育局长
     * @param params 不需要
     * @return [] key, value, label, status, cnName, remark, sort
     */
    public List<Map<String, Object>> getJyeooAreaStatusName(Map<String, Object> params) {
        // 学校的状态
        List<Map<String, Object>> jyeooSchoolStatusName = getJyeooSchoolStatusName(params);
        // 去除试用的trial
        return jyeooSchoolStatusName.stream()
                .filter(item -> !"trial".equals(MapUtil.getString(item, "key")))
                .collect(toList());
    }

    /**
     * 获取菁优区域开通状态详情
     * @param params areaId
     * @return areaId areaName jyeooStatus expireTime remark
     */
    public Map<String, Object> getJyeooArea(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();
        Map<String, Object> jyeooArea = jyeooAreaService.getJyeooAreaByAreaId(MapUtil.of("areaId", params.get("areaId")));
        // 查不到 -- 关闭
        if (MapUtils.isEmpty(jyeooArea)) {
            Map<String, Object> map = new HashMap<>();
            map.put("areaId", params.get("areaId"));
            map.put("jyeooStatus", DictUtil.getDictValue("jyeooStatus", "runnable"));
            map.put("expireTime", null);
            return map;
        }

        return jyeooArea;
    }

    /**
     * 获取当前节点的状态
     * @param params areaId
     * @return jyeooStatus
     */
    public Map<String, Object> getJyeooAreaStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();
        Map<String, Object> jyeooArea = getJyeooArea(params);
        return MapUtil.of("jyeooStatus", jyeooArea.get("jyeooStatus"));
    }


    /**
     * 开通jyeoo-区域端
     * @param params areaId jyeooStatus [expireTime] 时间戳类型[1625714083541]
     * @return
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateJyeooAreaState(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .isNumeric("jyeooStatus")
                .verify();

        int jyeooStatusAfter = MapUtil.getInt(params, "jyeooStatus");
        int running = DictUtil.getDictValue("jyeooStatus", "running");
        int dead = DictUtil.getDictValue("jyeooStatus", "dead");
        if (!(jyeooStatusAfter == running || jyeooStatusAfter == dead)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "只能设置为开启或关闭");
        }

        Long expireTimeMilliseconds = MapUtil.getLongNullable(params, "expireTime");
        Date expireDate = Optional.ofNullable(expireTimeMilliseconds).map(Date::new).orElse(null);
        if (expireDate != null && jyeooStatusAfter == running) {
            LocalDate expireLocalDate = expireDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (expireLocalDate.toEpochDay() < LocalDate.now().toEpochDay()) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "设置为开启时过期日期不得小于当前日期");
            }
        }

        Map<String, Object> areaInfo = iBaseAreaService.getArea(params);
        if (MapUtils.isEmpty(areaInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS);
        }

        params.put("hasChild", DictUtil.getDictValue("hasChild", "unContain"));
        params.put("areaInfo", areaInfo);
        params.put("areaName", areaInfo.get("areaName"));

        // 菁优日志记录
        saveJyeooAreaStatusLog(params);
        // 菁优区域开通
        saveJyeooArea(params);
    }

    /**
     * 开通jyeoo-区域端
     * @param params areaId
     * @return
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateJyeooAreaChildrenStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();

        Map<String, Object> jyeooArea = jyeooAreaService.getJyeooAreaByAreaId(params);
        if (ObjectUtil.isBlank(jyeooArea)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "父节点未开通菁优题库，无法应用于子节点");
        }

        Map<String, Object> areaInfo = iBaseAreaService.getArea(params);
        if (MapUtils.isEmpty(areaInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "区域不存在");
        }
        params.put("hasChild", DictUtil.getDictValue("hasChild", "contain"));
        params.put("areaName", areaInfo.get("areaName"));
        params.put("areaCode", areaInfo.get("areaCode"));
        params.put("jyeooStatus", jyeooArea.get("jyeooStatus"));
        params.put("expireTime",jyeooArea.get("expireTime"));
        if (ObjectUtil.isNotBlank(jyeooArea.get("expireTime"))) {
            params.put("expireTime", DateUtil.getDateLongValue(jyeooArea.get("expireTime").toString()));
        }


        // 菁优区域日志记录
        batchSaveJyeooAreaStatusLog(params);
        // 菁优区域开通
        batchSaveJyeooArea(params);
    }

    /**
     * 根据areaCode注册教育局端用户
     * @param params areaCode
     */
    private void registerJyeooArea4AreaCode(Map<String, Object> params) {
        params.put("hasChild", DictUtil.getDictValue("hasChild", "contain"));
        jyeooHandleService.registerEduUser(params);
    }

    /**
     * 批量保存菁优区域开通记录
     * @param params areaCode jyeooStatus expireTime 用户三件套
     */
    private void batchSaveJyeooArea(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("areaCode")
                .isNumeric("jyeooStatus")
                .verify();

        // 根据areaCode查询本身以及子区域
        List<Map<String, Object>> areaList = iBaseAreaService.getAreaListByAreaCode(params);
        if (CollectionUtils.isEmpty(areaList)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS);
        }
        String expireTime = null;
        if (ObjectUtil.isNotBlank(params.get("expireTime"))) {
            expireTime = DateUtil.formatDateTime(params, "expireTime");
        }
        String remark = MapUtil.getString(params, "remark", "");

        // 组装
        List<Map<String, Object>> jyeooAreaList = Lists.newArrayList();
        for (Map<String, Object> areaItem : areaList) {
            Map<String, Object> jyeooArea = new HashMap<>(areaItem);
            jyeooArea.put("jyeooStatus", params.get("jyeooStatus"));
            jyeooArea.put("expireTime", expireTime);
            jyeooArea.put("remark", remark);
            jyeooArea.put("userId", params.get("userId"));
            jyeooArea.put("userName", params.get("userName"));
            jyeooArea.put("currentTime", DateUtil.getCurrentDateTime());
            jyeooAreaList.add(jyeooArea);
        }
        // 插入新数据
        jyeooAreaService.batchInsertJyeooArea(MapUtil.of("jyeooAreaList", jyeooAreaList));
    }

    /**
     * 包含子区域的菁优日志保存
     * @param params
     */
    private void batchSaveJyeooAreaStatusLog(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("areaCode")
                .verify();

        List<Map<String, Object>> areaList = iBaseAreaService.getAreaListByAreaCode(params);
        if (CollectionUtils.isEmpty(areaList)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS);
        }

        // 组装数据
        Set<Long> areaIds = areaList.stream().map(item -> MapUtil.getLong(item, "areaId")).collect(Collectors.toSet());

        List<Map<String, Object>> beforeJyeooAreaList =
                jyeooAreaService.getJyeooAreaByAreaIds(MapUtil.of("areaIds", areaIds));

        Map<Long, Map<String, Object>> beforeJyeooAreaMap = beforeJyeooAreaList
                .stream()
                .collect(toMap(item -> MapUtil.getLong(item, "areaId"), item -> item));

        List<Map<String, Object>> jyeooAreaStatusLogList = Lists.newArrayList();
        areaList.forEach(area -> {
            HashMap<String, Object> jyeooAreaStatusLog = new HashMap<>(area);
            Map<String, Object> beforeJyeooArea = beforeJyeooAreaMap.get(MapUtil.getLong(area, "areaId"));
            // 不存在则设置原来的状态为关闭
            if (MapUtils.isEmpty(beforeJyeooArea)) {
                jyeooAreaStatusLog.put("jyeooAreaStatusBefore", DictUtil.getDictValue("jyeooStatus", "runnable"));
            } else {
                jyeooAreaStatusLog.put("jyeooAreaStatusBefore", beforeJyeooArea.get("jyeooStatus"));
            }
            jyeooAreaStatusLog.put("jyeooAreaStatusAfter", params.get("jyeooStatus"));
            jyeooAreaStatusLog.put("userId", params.get("userId"));
            jyeooAreaStatusLog.put("userName", params.get("userName"));
            jyeooAreaStatusLog.put("currentTime", DateUtil.getCurrentDateTime());
            jyeooAreaStatusLogList.add(jyeooAreaStatusLog);
        });

        // 插入日志
        jyeooAreaStatusLogService.batchInsertJyeooAreaStatusLog(MapUtil.of("jyeooAreaStatusLogList", jyeooAreaStatusLogList));

    }

    /**
     * 注册菁优账号
     * @param params
     */
    private void registerJyeooUser4AreaId(Map<String, Object> params) {
        params.put("hasChild", DictUtil.getDictValue("hasChild", "unContain"));
        jyeooHandleService.registerEduUser(params);
    }

    /**
     * 新增菁优区域开通
     * @param params
     */
    private void saveJyeooArea(Map<String, Object> params) {
        // 处理expireTime 和 remark
        Long expireTimeMilliseconds = MapUtil.getLongNullable(params, "expireTime");
        Date expireDate = Optional.ofNullable(expireTimeMilliseconds).map(Date::new).orElse(null);
        String expireTime = Optional.ofNullable(expireDate).map(DateUtil::formatDate).orElse(null);
        String remark = MapUtil.getString(params, "remark", "");
        Map<String, Object> jyeooArea = new HashMap<>(params);
        jyeooArea.put("expireTime", expireTime);
        jyeooArea.put("remark", remark);
        // 插入新数据
        jyeooAreaService.insertJyeooArea(jyeooArea);
    }

    /**
     * 新增菁优区域状态转换记录日志
     * @param params
     */
    private void saveJyeooAreaStatusLog(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .verify();

        Map<String, Object> areaInfo = MapUtil.getMap(params, "areaInfo");

        // 插入日志
        Map<String, Object> beforeJyeooArea =
                jyeooAreaService.getJyeooAreaByAreaId(MapUtil.of("areaId", params.get("areaId")));
        HashMap<String, Object> logParams = new HashMap<>(areaInfo);
        // 不存在则设置原来的状态为关闭
        if (MapUtils.isEmpty(beforeJyeooArea)) {
            logParams.put("jyeooAreaStatusBefore", DictUtil.getDictValue("jyeooStatus", "runnable"));
        } else {
            logParams.put("jyeooAreaStatusBefore", beforeJyeooArea.get("jyeooStatus"));
        }
        logParams.put("jyeooAreaStatusAfter", params.get("jyeooStatus"));
        logParams.put("userId", params.get("userId"));
        logParams.put("userName", params.get("userName"));
        logParams.put("currentTime", DateUtil.getCurrentDateTime());
        jyeooAreaStatusLogService.insertJyeooAreaStatusLog(logParams);
    }
}
