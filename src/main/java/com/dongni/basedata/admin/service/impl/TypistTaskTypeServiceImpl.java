package com.dongni.basedata.admin.service.impl;

import com.dongni.basedata.admin.service.ITypistTaskTypeService;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.commons.utils.DateUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/12/22 下午 02:51
 * @Version 1.0.0
 */
@Service
public class TypistTaskTypeServiceImpl implements ITypistTaskTypeService {
    @Autowired
    private BaseDataRepository baseDataRepository;

    /**
     * 批量插入录题人员任务类型
     *
     * @param typistTaskTypeList typistTaskType
     * @param params typistId userId userName
     */
    @Override
    public void batchInsertTypistTaskType(List<Integer> typistTaskTypeList, Map<String, Object> params) {
        if (CollectionUtils.isNotEmpty(typistTaskTypeList)) {
            List<Map<String, Object>> collect = typistTaskTypeList.stream().map(i ->
                    MapUtil.of("userId", params.get("userId"),
                    "userName", params.get("userName"),
                    "currentTime", DateUtil.getCurrentDateTime(),
                    "typistId", params.get("typistId"),
                    "typistTaskType", i)).collect(Collectors.toList());
            baseDataRepository.insert("TypistTaskTypeMapper.batchInsertTypistTaskType", collect);
        }
    }

    /**
     * 更新录题人员任务类型（先删后插）
     *
     * @param typistTaskTypeList typistTaskType
     * @param params operateTypistId userId userName
     */
    @Override
    public void updateTypistTaskType(List<Integer> typistTaskTypeList, Map<String, Object> params) {
        params.put("typistId", params.get("operateTypistId"));
        baseDataRepository.delete("TypistTaskTypeMapper.deleteTypistTaskType", MapUtil.getLong(params, "typistId"));
        batchInsertTypistTaskType(typistTaskTypeList, params);
    }

    /**
     * 根据typistId列表获取录题人员的任务类型
     *
     * @param typistIdList 录题人员id列表
     * @return typistId typistTaskType
     */
    @Override
    public List<Map<String, Object>> getByTypistIdList(List<Long> typistIdList) {
        return baseDataRepository.selectList("TypistTaskTypeMapper.getByTypistIdList", typistIdList);
    }
}
