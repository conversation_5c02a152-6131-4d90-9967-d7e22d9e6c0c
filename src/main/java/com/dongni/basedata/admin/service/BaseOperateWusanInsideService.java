package com.dongni.basedata.admin.service;

import cn.hutool.core.date.DatePattern;
import com.dongni.basedata.admin.bean.WusanSchoolStatus;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.wusan.inside.bean.WusanSchoolStatusUpdatedDTO;
import com.dongni.tiku.wusan.inside.bean.entity.WusanSchoolStatusEntity;
import com.dongni.tiku.wusan.inside.service.WusanInsideSchoolService;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * 五三题库
 * <AUTHOR>
 * @date 2024/07/18
 */
@Service
public class BaseOperateWusanInsideService {

    /**
     * 五三在线题库试用时间
     */
    private final static int WUSAN_INSODE_TRIAL_DAYS = 90;
    /**
     * 五三在线题库正式使用时间时间
     */
    private final static int WUSAN_INSODE_RUNNING_DAYS = 365;
    
    @Autowired
    private WusanInsideSchoolService wusanInsideSchoolService;
    
    /**
     * 获取学校五三题库开通的状态
     * @param schoolId 学校id
     * @return 五三开通状态
     */
    public WusanSchoolStatus getWusanSchoolStatusBySchoolId(long schoolId) {
        WusanSchoolStatusEntity wusanSchoolStatusEntity = wusanInsideSchoolService.getWusanSchoolEntity(schoolId);
        
        WusanSchoolStatus wusanSchoolStatus = new WusanSchoolStatus();
        if (wusanSchoolStatusEntity == null) {
            Map<String, Object> dict = DictUtil.getDict("tikuCommonStatus", "runnable");
            if (MapUtils.isEmpty(dict)) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "数据tikuCommonStatus:runnable字典不存在");
            }
            wusanSchoolStatus.setWusanStatus(MapUtil.getInt(dict, "value"));
            wusanSchoolStatus.setWusanStatusName(MapUtil.getString(dict, "label"));
        } else {
            int status = wusanSchoolStatusEntity.getStatus();
            wusanSchoolStatus.setWusanStatus(status);
            wusanSchoolStatus.setWusanStatusName(DictUtil.getDictLabel("tikuCommonStatus", status));
        }
        return wusanSchoolStatus;
    }


    /**
     * 更新菁优网学校开通状态
     * @param wusanSchoolStatusUpdatedDTO 五三学校更新对象
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateWusanSchoolStatus(WusanSchoolStatusUpdatedDTO wusanSchoolStatusUpdatedDTO) {
        //题库开通状态
        int runnable = DictUtil.getDictValue("tikuCommonStatus", "runnable");
        int blocked = DictUtil.getDictValue("tikuCommonStatus", "blocked");
        int trial = DictUtil.getDictValue("tikuCommonStatus", "trial");
        int running = DictUtil.getDictValue("tikuCommonStatus", "running");
        int dead = DictUtil.getDictValue("tikuCommonStatus", "dead");
        int wusanStatusAfter = wusanSchoolStatusUpdatedDTO.getStatus();
        Long schoolId = wusanSchoolStatusUpdatedDTO.getSchoolId();

        // 不能将状态设置为 未启用(初始状态) 过期(系统设置)
        if (wusanStatusAfter == runnable || wusanStatusAfter == blocked) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "状态参数错误");
        }

        //根据schoolId获取学校信息
        WusanSchoolStatusEntity wusanSchoolInfo = wusanInsideSchoolService.getWusanSchoolEntity(schoolId);
        Date expireTimeBefore = ObjectUtil.isBlank(wusanSchoolInfo)? new Date(): wusanSchoolInfo.getExpireTime();
        //当前菁优网和学校状态
        int wusanStatusBefore = ObjectUtil.isBlank(wusanSchoolInfo)? runnable: wusanSchoolInfo.getStatus();
        wusanSchoolStatusUpdatedDTO.setStatusAfter(wusanStatusAfter);
        wusanSchoolStatusUpdatedDTO.setStatusBefore(wusanStatusBefore);
        // 设置为trial试用  前置状态为runnable未开启
        if (wusanStatusAfter == trial) {
            if (wusanStatusBefore != runnable) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                        DictUtil.getDictLabelByValue("tikuCommonStatus", wusanStatusBefore) + "状态不能被设置为" +
                                DictUtil.getDictLabelByValue("tikuCommonStatus", wusanStatusAfter));
            }
            LocalDateTime expireLocalDateTime = LocalDateTime.now().plusDays(WUSAN_INSODE_TRIAL_DAYS);
            Date expireTime = Date.from(expireLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
            wusanSchoolStatusUpdatedDTO.setExpireTime(expireTime);
            wusanInsideSchoolService.updateWusanSchoolStateAndInsertWusanSchoolStatusLog(wusanSchoolStatusUpdatedDTO);
            return;
        }

        // 设置为running正式 前置状态为trial试用 dead关闭 blocked过期
        if (wusanStatusAfter == running) {
            // 2021-02-19 申报过后可以直接从未开启状态进入正式状态
            if (wusanStatusBefore == running) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                        DictUtil.getDictLabelByValue("tikuCommonStatus", wusanStatusBefore) + "状态不能被设置为" +
                                DictUtil.getDictLabelByValue("tikuCommonStatus", wusanStatusAfter));
            }

            //如果之前的状态为试用状态，在原有的过期时间增加365天
            if (wusanStatusBefore == trial) {
                String expireTimeStr = DateUtil.formatDateTime(expireTimeBefore);
                Date expireTime = Date.from(LocalDateTime.parse(expireTimeStr, DatePattern.NORM_DATETIME_FORMATTER).plusDays(WUSAN_INSODE_RUNNING_DAYS).atZone(ZoneId.systemDefault()).toInstant());
                wusanSchoolStatusUpdatedDTO.setExpireTime(expireTime);

            } else {
                Date expireTime = Date.from(LocalDateTime.now().plusDays(WUSAN_INSODE_RUNNING_DAYS).atZone(ZoneId.systemDefault()).toInstant());
                wusanSchoolStatusUpdatedDTO.setExpireTime(expireTime);
            }
            wusanInsideSchoolService.updateWusanSchoolStateAndInsertWusanSchoolStatusLog(wusanSchoolStatusUpdatedDTO);
            return;
        }

        // 设置为dead关闭 前置状态为trial试用 running正式
        if (wusanStatusAfter == dead) {
            if (wusanStatusBefore == runnable || wusanStatusBefore == dead) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                        DictUtil.getDictLabelByValue("tikuCommonStatus", wusanStatusBefore) + "状态不能被设置为" +
                                DictUtil.getDictLabelByValue("tikuCommonStatus", wusanStatusAfter));
            }
            wusanSchoolStatusUpdatedDTO.setExpireTime(new Date());
            wusanInsideSchoolService.updateWusanSchoolStateAndInsertWusanSchoolStatusLog(wusanSchoolStatusUpdatedDTO);
        }
    }

}
