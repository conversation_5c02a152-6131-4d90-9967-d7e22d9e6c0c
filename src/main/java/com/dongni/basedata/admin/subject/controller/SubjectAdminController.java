package com.dongni.basedata.admin.subject.controller;

import com.dongni.basedata.admin.subject.service.SubjectAdminService;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by scott
 * time: 15:06 2019/8/1
 * description:区域题库管理员
 */
@RestController
@RequestMapping("/base/data/subject/admin")
public class SubjectAdminController extends BaseController {


    @Autowired
    private SubjectAdminService areaAdminService;

    /**
     * 新增管理员
     * params areaAdminName areaAdminPhone
     *
     * @return 结果
     */
    @PostMapping("")
    @DongniRequest(operationName = "区域题库管理员.新增管理员", remark = {"TODO 权限"})
    public Response insertSubjectAdmin() {
        areaAdminService.insertSubjectAdmin(getParameterMap());
        return new Response();
    }

    /**
     * 获取管理员
     * params userId userName
     *
     * @return 结果
     */
    @GetMapping("")
    @DongniRequest(operationName = "区域题库管理员.获取管理员列表", remark = {"TODO 权限"})
    public Response getSubjectAdmin() {
        return new Response(areaAdminService.getSubjectAdmin(getParameterMap()));
    }

    /**
     * 更新管理员
     * params areaAdminId
     *
     * @return 结果
     */
    @PostMapping("/update")
    @DongniRequest(operationName = "区域题库管理员.更新管理员", remark = {"TODO 权限"})
    public Response updateSubjectAdmin() {
        areaAdminService.updateSubjectAdmin(getParameterMap());
        return new Response();
    }

    /**
     * 更新管理员
     * 非GET/POST治理 基础数据
     * params areaAdminId
     *
     * @return 结果
     */
    @PutMapping
    @Deprecated
    @DongniRequest(operationName = "区域题库管理员.更新管理员(PUT Deprecated)", remark = {"删除过期接口"})
    public Response updateSubjectAdminDeprecated() {
        areaAdminService.updateSubjectAdmin(getParameterMap());
        return new Response();
    }

    /**
     * 更新管理员
     * params areaAdminId
     *
     * @return 结果
     */
    @PostMapping("/status")
    @DongniRequest(operationName = "区域题库管理员.更新管理员状态", remark = {"TODO 权限"})
    public Response updateSubjectAdminStatus() {
        areaAdminService.updateSubjectAdminStatus(getParameterMap());
        return new Response();
    }

    /**
     * 删除管理员
     * params areaAdminId
     *
     */
    @PostMapping("/delete")
    @DongniRequest(operationName = "区域题库管理员.删除管理员", remark = {"TODO 权限"})
    public Response deleteSubjectAdmin() {
        areaAdminService.deleteSubjectAdmin(getParameterMap());
        return new Response();
    }

    /**
     * 删除管理员
     * 非GET/POST治理 基础数据
     * params areaAdminId
     *
     */
    @DeleteMapping
    @Deprecated
    @DongniRequest(operationName = "区域题库管理员.删除管理员(DELETE Deprecated)", remark = {"删除过期接口"})
    public Response deleteSubjectAdminDeprecated() {
        areaAdminService.deleteSubjectAdmin(getParameterMap());
        return new Response();
    }

}
