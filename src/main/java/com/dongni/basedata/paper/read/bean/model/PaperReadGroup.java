package com.dongni.basedata.paper.read.bean.model;

import com.dongni.common.entity.BaseEntity;
import com.dongni.common.validator.group.CreateGroup;
import com.dongni.common.validator.group.UpdateGroup;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 阅卷组实体类
 *
 * <AUTHOR>
 * @Date 2023/5/8 下午 04:52
 * @Version 1.0.0
 */
@Getter
@Setter
public class PaperReadGroup extends BaseEntity {
    private static final long serialVersionUID = 6129855361531628546L;

    /**
     * 阅卷组主键
     */
    @NotNull(message = "阅卷组主键不能为空", groups = {UpdateGroup.class})
    private Long paperReadGroupId;

    /**
     * 阅卷组集合主键
     */
    @NotNull(message = "阅卷组集合主键不能为空", groups = {CreateGroup.class})
    private Long paperReadGroupSetId;

    /**
     * 阅卷组名称
     */
    @NotBlank(message = "阅卷组名称不能为空", groups = {CreateGroup.class, UpdateGroup.class})
    private String groupName;

    /**
     * 阅卷组编码规则
     */
    @NotBlank(message = "阅卷组编码规则不能为空", groups = {CreateGroup.class})
    @Length(min = 5, max = 12, message = "阅卷组编码规则长度为5-12个字符", groups = {CreateGroup.class})
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "编码规则必须使用字母、数字或者字母数字的组合", groups = {CreateGroup.class})
    private String groupCode;

    /**
     * 阅卷组对应的课程id
     */
    @NotNull(message = "阅卷组对应的课程id不能为空", groups = {CreateGroup.class, UpdateGroup.class})
    private Long courseId;

    /**
     * 阅卷组对应的课程名称
     */
    @NotBlank(message = "阅卷组对应的课程名称不能为空", groups = {CreateGroup.class, UpdateGroup.class})
    private String courseName;

    /**
     * 是否允许组内协助阅卷-字典值assistPaperReadStatus
     */
    private Integer assistPaperReadStatus;
}
