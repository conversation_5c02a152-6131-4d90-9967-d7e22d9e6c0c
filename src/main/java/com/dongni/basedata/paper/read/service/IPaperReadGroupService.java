package com.dongni.basedata.paper.read.service;

import com.dongni.basedata.paper.read.bean.dto.PaperReadAccountSequenceDto;
import com.dongni.basedata.paper.read.bean.dto.PaperReadGroupDto;
import com.dongni.basedata.paper.read.bean.model.PaperReadGroup;
import com.dongni.basedata.paper.read.bean.params.PaperReadGroupGeneratePreviewParams;
import com.dongni.basedata.paper.read.bean.params.PaperReadGroupPageParams;
import com.dongni.basedata.paper.read.bean.vo.PaperReadAccountPreviewVo;
import com.dongni.common.entity.PageResult;
import com.dongni.common.service.IBaseService;
import com.dongni.exam.common.mark.vo.PaperReadGroupVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 阅卷组service层接口
 *
 * <AUTHOR>
 * @Date 2023/5/8 下午 05:18
 * @Version 1.0.0
 */
public interface IPaperReadGroupService extends IBaseService<PaperReadGroup> {
    /**
     * 插入阅卷组
     *
     * @param paperReadGroup 阅卷组实体类
     */
    void insert(PaperReadGroup paperReadGroup);


    /**
     * 批量保存阅卷组以及生成序列
     * @param paperReadGroupList 阅卷组实体列表
     */
    void batchInsertPaperReadGroupAndAccountSequence(List<PaperReadGroup> paperReadGroupList);

    /**
     * 更新阅卷组
     *
     * @param paperReadGroup 阅卷组实体类
     */
    void update(PaperReadGroup paperReadGroup);

    /**
     * 更新阅卷组的修改时间
     *
     * @param paperReadGroupId 阅卷组实体类
     */
    void updateModifyDateTime(Long paperReadGroupId);

    /**
     * 删除阅卷组
     *
     * @param paperReadGroupId 阅卷组id
     */
    void delete(Long paperReadGroupId);

    /**
     * 删除阅卷组对应的账号和序列
     *
     * @param paperReadGroupId 阅卷组id
     * @param isReadDeleteSequence 是否删除序列
     */
    void deleteAccountAndSequence(Long paperReadGroupId, boolean isReadDeleteSequence);

    /**
     * 分页查询阅卷组
     *
     * @param paperReadGroupPageParams 阅卷组分页查询参数
     * @return 阅卷组列表
     */
    PageResult<PaperReadGroupDto> list(PaperReadGroupPageParams paperReadGroupPageParams);

    /**
     * 根据唯一键获取阅卷组的数量
     *
     * @param paperReadGroup 阅卷组实体类
     * @return 0：不存在，1：存在
     */
    Integer getByUniqueKey(PaperReadGroup paperReadGroup);

    /**
     * 根据阅卷组id获取阅卷组信息
     *
     * @param paperReadGroupId 阅卷组id
     * @return 阅卷组信息
     */
    PaperReadGroup getById(Long paperReadGroupId);

    /**
     * 根据名称和集合查询阅卷组
     *
     * @param paperReadGroup 阅卷组实体类
     * @return 0：不存在，1：存在
     */
    Integer getByGroupNameAndSetId(PaperReadGroup paperReadGroup);

    /**
     * 根据阅卷组id列表获取合并后的编码规则
     *
     * @param paperReadGroupIdList 阅卷组id列表
     * @return 合并后的编码规则
     */
    List<PaperReadAccountSequenceDto> getSequenceCode(List<Long> paperReadGroupIdList);

    /**
     * 批量生成账号预览
     *
     * @param paperReadGroupGeneratePreviewParams 阅卷组批量生成账号参数
     * @return 阅卷组批量生成账号预览结果列表
     */
    List<PaperReadAccountPreviewVo> preview(PaperReadGroupGeneratePreviewParams paperReadGroupGeneratePreviewParams);

    /**
     * 批量生成账号
     *
     * @param paperReadGroupGeneratePreviewParams 阅卷组批量生成账号参数
     */
    void generate(PaperReadGroupGeneratePreviewParams paperReadGroupGeneratePreviewParams);

    /**
     * 导出阅卷组集合账号
     * @param paperReadGroupIds 阅卷组ID列表
     * @return excel文件地址
     */
    String exportPaperReadGroupAccount(List<Long> paperReadGroupIds);

    /**
     * 批量重置组内信息
     * 清除虚拟账号绑定的姓名、手机号码、登录密码
     * 真实账号不处理
     *
     * @param paperReadGroupIdList 阅卷组id列表参数
     */
    void reset(List<Long> paperReadGroupIdList, HttpServletRequest request, HttpServletResponse response);

    /**
     * 根据联考员userId、courseId、gradeId获取阅卷组列表
     *
     * @param userId 联考员userId
     * @param courseId 课程id
     * @param gradeType 年级类型
     * @return 阅卷组列表
     */
    List<PaperReadGroupVO> listPaperReadGroupForMark(Long userId, Long courseId, Integer gradeType);

    /**
     * 根据id批量查询阅卷组列表
     *
     * @param paperReadGroupIds 阅卷组id列表
     * @param groupName
     * @return 阅卷组列表
     */
    List<PaperReadGroupVO> listByIds(List<Long> paperReadGroupIds, String groupName);

    /**
     * 根据id批量查询阅卷组列表 不含老师信息
     * @param paperReadGroupIds 阅卷组id列表
     * @return 阅卷组列表
     */
    List<PaperReadGroupVO> listOnlyInfoByIds(List<Long> paperReadGroupIds);

    /**
     * 根据阅卷组集合id查询底下的阅卷组
     *
     * @param paperReadGroupSetId 阅卷组集合id
     * @return 阅卷组列表
     */
    List<PaperReadGroup> getByPaperReadGroupSetId(Long paperReadGroupSetId);

    /**
     * 获取阅卷组集合下所有的阅卷组
     * @param paperReadGroupSetId 阅卷组集合ID
     * @return 阅卷组列表
     */
    List<PaperReadGroupDto> getPaperReadGroupListBySetId(long paperReadGroupSetId);

    /**
     * 获取比执行id大且修改时间在一个月前的阅卷组
     *
     * @param paperReadGroupId 阅卷组id
     */
    PaperReadGroup getByGreaterThanId(Long paperReadGroupId);
}