package com.dongni.basedata.paper.read.service;

import com.dongni.basedata.paper.read.bean.model.PaperReadGroupSetConfig;
import com.dongni.common.service.IBaseService;

/**
 * 阅卷组集合配置service层接口
 *
 * <AUTHOR>
 * @Date 2023/5/8 上午 10:11
 * @Version 1.0.0
 */
public interface IPaperReadGroupSetConfigService extends IBaseService<PaperReadGroupSetConfig> {
    /**
     * 插入阅卷组集合配置
     *
     * @param paperReadGroupSetConfig 阅卷组集合配置实体类
     */
    void insert(PaperReadGroupSetConfig paperReadGroupSetConfig);

    /**
     * 更新阅卷组集合配置
     *
     * @param paperReadGroupSetConfig 阅卷组集合配置实体类
     */
    void update(PaperReadGroupSetConfig paperReadGroupSetConfig);

    /**
     * 根据id查询阅卷组集合配置
     *
     * @param paperReadGroupSetId 阅卷组集合id
     * @return 阅卷组集合配置实体类
     */
    PaperReadGroupSetConfig getConfig(Long paperReadGroupSetId);

    /**
     * 根据阅卷组结合id删除配置
     *
     * @param paperReadGroupSetId 阅卷组集合id
     */
    void delete(Long paperReadGroupSetId);
}
