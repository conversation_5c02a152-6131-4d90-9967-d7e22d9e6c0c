package com.dongni.basedata.paper.read.bean.dto;

import com.dongni.common.report.excel.annotations.ExcelColumn;
import com.dongni.common.report.excel.read.bean.BaseExcelDTO;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <p>阅卷组导入对象</p>
 *
 * <AUTHOR>
 * @className PaperReadGroupExcelDTO
 * @since 2023/5/10 19:20
 */
public class PaperReadGroupExcelDTO extends BaseExcelDTO {

    @ExcelColumn("阅卷组名称")
    @NotBlank(message = "阅卷组名称不能为空")
    private String paperReadGroupName;

    @ExcelColumn("编码规则")
    @NotBlank(message = "编码规则不能为空")
    @Length(min = 3, max = 12, message = "长度请在3-12位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "编码规则必须使用字母、数字或者字母数字的组合")
    private String paperReadGroupCode;

    @ExcelColumn("科目")
    @NotBlank(message = "科目不能为空")
    private String courseName;

    private Long courseId;

    private Long paperReadGroupSetId;

    public String getPaperReadGroupName() {
        return paperReadGroupName;
    }

    public void setPaperReadGroupName(String paperReadGroupName) {
        this.paperReadGroupName = paperReadGroupName;
    }

    public String getPaperReadGroupCode() {
        return paperReadGroupCode;
    }

    public void setPaperReadGroupCode(String paperReadGroupCode) {
        this.paperReadGroupCode = paperReadGroupCode;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getPaperReadGroupSetId() {
        return paperReadGroupSetId;
    }

    public void setPaperReadGroupSetId(Long paperReadGroupSetId) {
        this.paperReadGroupSetId = paperReadGroupSetId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }


}
