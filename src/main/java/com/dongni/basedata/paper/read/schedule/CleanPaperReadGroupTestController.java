package com.dongni.basedata.paper.read.schedule;

import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.annotation.DongniRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/5/22 下午 03:13
 * @Version 1.0.0
 */
@RestController
@RequestMapping(BaseDataConfig.CONTEXT_PATH + "/paper/read/schedule")
public class CleanPaperReadGroupTestController {

    @Autowired
    private CleanPaperReadGroupSchedule cleanPaperReadGroupSchedule;

    @GetMapping("/test")
    @DongniNotRequireLogin
    @DongniRequest(operationName = "阅卷组.清理阅卷组", remark = {"TODO 权限", "不受保护的测试接口"})
    public Response test() {
        cleanPaperReadGroupSchedule.cleanPaperReadGroup();
        return new Response();
    }
}
