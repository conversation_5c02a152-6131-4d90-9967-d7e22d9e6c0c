package com.dongni.basedata.manager.impl;

import com.dongni.basedata.bean.BaseDataMongodb;
import com.dongni.common.mongo.IManager;
import org.springframework.stereotype.Service;

/**
 * <pre>
 * 本文档存放学校健康度页面列表的部分数据，用于后续导出全部考试科目数据使用
 * 使用mongoDB考虑俩点：
 * 1. 该数据查询复杂且生产环境数据量较大，不能在导出报表时再查询一遍
 * 2. 该数据量较大，使用redis存储不合适
 *
 * 数据展示格式：
 * {
 *     "_id": ObjectId("xxxxxxxxxxxxx"),
 *     "userId": xxxxxx,
 *     "schoolId": xxxxxx,
 *     "schoolName": "xxxxxx",
 *     "gradeIds": [xxx1, xxx2, xxx3],
 *     "schoolMemberType": "正式",
 *     "schoolEmployee": "周潇东",
 *     "schoolCreateDateTime": "2023-01-30 16:15:41",
 *     "createDateTime": ISODate("2023-03-14T23:54:16.403+0000"),
 *     "__expireDate": ISODate("2023-04-13T16:00:00.000+0000")      // 自动过期时间
 * }
 * </pre>
 *
 * <AUTHOR>
 * @Date 2023/4/11 下午 05:42
 * @Version 1.0.0
 */
@Service
public class SchoolHealthTmpQueryManager extends IManager {
    protected SchoolHealthTmpQueryManager(BaseDataMongodb baseDataMongodb) {
        super(baseDataMongodb, BaseDataMongodb.COLLECTION_SCHOOL_HEALTH_TMP_QUERY, "学校健康度考试科目报表查询参数临时表");
    }
}
