package com.dongni.basedata.client.controller;


import com.dongni.basedata.client.service.ClientExceptionAnalysisService;
import com.dongni.basedata.config.BaseDataConfig;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Created by Heweipo on 2018/7/31.
 *
 * 客户端日志控制器
 */
@RestController
@RequestMapping(BaseDataConfig.CONTEXT_PATH+"/common/client/exception")
public class ClientExceptionAnalysisController extends BaseController {

    @Autowired
    private ClientExceptionAnalysisService analysisService;

    @PostMapping
    @DongniRequest(operationName = "客户端日志.保存异常信息", remark = {"TODO 权限"})
    public Response save(@RequestBody Map<String,Object> params){
        analysisService.save(params);
        return new Response();
    }

    @GetMapping
    @DongniRequest(operationName = "客户端日志.查询异常信息", remark = {"TODO 权限"})
    public Response get(){
        return new Response( analysisService.get(getParameterMap()));
    }

}
