package com.dongni.basedata.third.receiver;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.spring.SpringContextUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.ThirdTable;
import com.dongni.third.base.progress.data.AbstractThirdDataService;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/08/01 09:50
 */
public class ThirdTaskReceiver {

    private final static Logger log = LoggerFactory.getLogger(ThirdTaskReceiver.class);

    private Map<ThirdTable, Map<String, List<Map<String, Object>>>> dataMap = new HashMap<>();

    private Map<String, Map<String, List<Map<String, Object>>>> prodAndThirdMap2Table = new HashMap<>();

    /**
     * 接收重中间库同步过来的数据
     *
     * @param syncParams 同步参数
     * @param thirdTable 同步表信息
     * @param data 同步表数据
     */
    public void receiveData(SyncParams syncParams, ThirdTable thirdTable, Map<String, List<Map<String, Object>>> data) {
        if (syncParams == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "同步参数不能为空");
        }
        if (thirdTable == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "同步表信息不能为空");
        }
        if (data == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "同步表数据不能为空");
        }
        dataMap.put(thirdTable, data);
    }

    /**
     * 接收第三方同步任务结束通知
     *    不归spring容器管理, 手动设置事务
     * @param syncParams 同步参数
     */
    public void receiveEndNotification(SyncParams syncParams) {
        if (syncParams == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "同步参数不能为空");
        }

        

        // 学校映射
        if(syncParams.getCurrentThirdSchoolId() > 0){
            BaseDataRepository repository = SpringContextUtil.getBean(BaseDataRepository.class);
            Map<String,Object> school = repository.selectOne("SyncBaseMapper.getSyncSchool",syncParams);
            if(MapUtils.isEmpty(school)){
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"currentThirdSchoolId="+syncParams.get("currentThirdSchoolId")+"生产库中不存在，必须先同步学校");
            }
            log.info("从中间库同步{}({})的数据到生产库",school.get("schoolId"),school.get("schoolName"));
            syncParams.put("schoolId",school.get("schoolId"));
        }
        // 全量同步
        else{
            syncParams.put("schoolId",0L); // 设置为 0，那么必然查询不到数据，然后这条数据变为新增，从而不影响其他学校
        }

        // 排序
        List<Map<String, Object>> syncTableList = new ArrayList<>();
        for (ThirdTable thirdTable : dataMap.keySet()) {
            Map<String, Object> syncParamsTmp = new HashMap<>(syncParams);
            syncParamsTmp.put("tableName", thirdTable.getTableName());
            syncParamsTmp.put("data", dataMap.get(thirdTable));
            syncParamsTmp.put("sort", thirdTable.getTableSortValue());
            syncTableList.add(syncParamsTmp);
        }
        syncTableList.sort(Comparator.comparing(item -> Integer.valueOf(item.get("sort").toString())));

        // 执行更新
        DataSourceTransactionManager dataSourceTransactionManager = SpringContextUtil.getBean(BaseDataRepository.TRANSACTION);
        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus status = dataSourceTransactionManager.getTransaction(defaultTransactionDefinition);

        for (Map<String, Object> syncTable : syncTableList) {
            String tableName = (String) syncTable.get("tableName");

            String methodName = ThirdServiceMapping.getMethodName(tableName);
            Object bean = SpringContextUtil.getBean(methodName.split("\\.")[0]);
            Class<?> aClass = bean.getClass();
            try {
                Method method = aClass.getMethod(methodName.split("\\.")[1], Map.class);
                log.info("开始同步{}",method.getName());
                method.invoke(bean, syncTable);
                log.info("完成同步{}",method.getName());
            } catch (Exception e) {
                dataSourceTransactionManager.rollback(status);
                log.error("同步正式库出现异常：{}",e.getMessage(),e);
                if (e instanceof CommonException) { throw (CommonException) e; }
                if (e.getCause() instanceof CommonException) { throw (CommonException) e.getCause(); }
                throw new CommonException(ResponseStatusEnum.DATA_ERROR,e);
            }
        }
        dataSourceTransactionManager.commit(status);
    }


    /**
     * 查询正式库的数据
     *
     * @param syncParams  同步参数
     * @param taskReceiver
     * @param thirdServiceList 每个表同步处理的service
     * @return 每张表的数据
     */
    public void queryProductAndThirdData(SyncParams syncParams,
                                         ThirdTaskReceiver taskReceiver,
                                         List<AbstractThirdDataService> thirdServiceList) {

        // 学校映射
        if(syncParams.getCurrentThirdSchoolId() > 0){
            BaseDataRepository repository = SpringContextUtil.getBean(BaseDataRepository.class);
            Map<String,Object> school = repository.selectOne("SyncBaseMapper.getSyncSchool",syncParams);
            if(org.apache.commons.collections.MapUtils.isEmpty(school)){
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"currentThirdSchoolId="+syncParams.get("currentThirdSchoolId")+"生产库中不存在，必须先同步学校");
            }
            log.info("从中间库同步{}({})的数据到生产库",school.get("schoolId"),school.get("schoolName"));
            syncParams.put("schoolId",school.get("schoolId"));
        }
        // 全量同步
        else{
            // 设置为 0，那么必然查询不到数据，然后这条数据变为新增，从而不影响其他学校
            syncParams.put("schoolId",0L);
        }

        for (AbstractThirdDataService thirdDataService : thirdServiceList) {
            String tableName = thirdDataService.getThirdTable().getTableName();
            List<Map<String, Object>> thirdDataList = thirdDataService.queryThirdData(syncParams);
            List<Map<String, Object>> prodDataList =  thirdDataService.queryProductData(syncParams, thirdDataList);
            Map<String, List<Map<String, Object>>> prodAndThirdMap = new HashMap<>();
            prodAndThirdMap.put("prod", prodDataList);
            prodAndThirdMap.put("third", thirdDataList);

            taskReceiver.receiveProdAndThirdMap2Table(tableName, prodAndThirdMap);
        }


    }

    public Map<String, Map<String, List<Map<String, Object>>>> getProdAndThirdMap2Table() {
        return prodAndThirdMap2Table;
    }

    public void receiveProdAndThirdMap2Table(String tableName, Map<String, List<Map<String, Object>>> prodAndThirdMap) {
        prodAndThirdMap2Table.put(tableName, prodAndThirdMap);
    }
}
