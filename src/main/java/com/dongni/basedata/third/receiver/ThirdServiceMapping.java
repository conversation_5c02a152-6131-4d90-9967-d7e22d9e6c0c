package com.dongni.basedata.third.receiver;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;

/**
 * <AUTHOR>
 * @date 2019/08/05 17:02
 *  无序的 只是保持与 ThirdTable 顺序一样而已
 */
public enum ThirdServiceMapping {
    /***/
    
    SCHOOL("t_third_school", "thirdSyncService.syncSchool"),
    SCHOOL_STAGE("t_third_school_stage", "thirdSyncService.syncSchoolStage"),
    GRADE("t_third_grade", "thirdSyncService.syncGrade"),
    IDENTITY_CARD("t_third_identity_card", "thirdSyncService.syncIdentityCard"),
    TEACHER("t_third_teacher", "thirdSyncService.syncTeacher"),
    
    STUDENT("t_third_student", "thirdSyncService.syncStudent"),
    TAG("t_third_tag", "thirdSyncService.syncTag"),
    STUDENT_TAG("t_third_student_tag", "thirdSyncService.syncStudentTag"),
    PARENT("t_third_parent", "thirdSyncService.syncParent"),
    CLASS("t_third_class", "thirdSyncService.syncClass"),
    
    COURSE("t_third_course", "thirdSyncService.syncCourse"),
    SCHOOL_COURSE("t_third_school_course", "thirdSyncService.syncSchoolCourse"),
    SCHOOL_PRINCIPAL("t_third_school_principal", "thirdSyncService.syncSchoolPrincipal"),
    GRADE_DIRECTOR("t_third_grade_director", "thirdSyncService.syncGradeDirector"),
    CLASS_TEACHER("t_third_class_teacher", "thirdSyncService.syncClassTeacher"),
    
    CLASS_STUDENT("t_third_class_student", "thirdSyncService.syncClassStudent"),
    PARENT_STUDENT("t_third_parent_student", "thirdSyncService.syncParentStudent"),
    EDUCATION_DIRECTOR("t_third_education_director", "thirdSyncService.syncEducationDirector"),
    ACCOUNT("t_third_account", "thirdSyncService.syncAccount"),
    USER("t_third_user", "thirdSyncService.syncUser");

    private String tableName;

    private String methodName;

    ThirdServiceMapping(String tableName, String methodName) {
        this.tableName = tableName;
        this.methodName = methodName;
    }

    public String tableName() {
        return tableName;
    }

    /**
     * 获取方法名
     *
     * @param tableName
     * @return
     */
    public static String getMethodName(String tableName) {
        for (ThirdServiceMapping mapping : ThirdServiceMapping.values()) {
            if (ObjectUtil.isValueEquals(mapping.tableName, tableName)) {
                return mapping.methodName;
            }
        }
        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "开发阶段异常:" + tableName);
    }
}
