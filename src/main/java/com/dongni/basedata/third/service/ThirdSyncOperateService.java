package com.dongni.basedata.third.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.dongni.basedata.third.service.ThirdSyncService.addListKey;
import static com.dongni.basedata.third.service.ThirdSyncService.deleteListKey;
import static com.dongni.basedata.third.service.ThirdSyncService.updateListKey;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/31.
 *
 * 具体操作
 */
@Service
public class ThirdSyncOperateService {
    
    private final static Logger log = LoggerFactory.getLogger(ThirdSyncOperateService.class);
    
    @Autowired
    private BaseDataRepository repository;
    @Autowired
    private ThirdDataSyncService thirdDataSyncService;
    @Autowired
    private ThirdTableItemService thirdTableItemService;
    
    /**
     * 同步操作具体 CURD
     * @return
     */
    public Map<String, Object> operate(Map<String, Object> params, String suffix) {
        return operate(params,
                "ThirdSelectMapper.select" + suffix,
                "ThirdInsertMapper.insert" + suffix,
                "ThirdUpdateMapper.update" + suffix,
                "ThirdDeleteMapper.delete" + suffix);
    }
    
    /**
     * 物理删除，但是会备份；
     *
     */
    public Map<String, Object> operate(Map<String, Object> params, String select, String insert, String update, String delete) {
        
        Long thirdPartyId = Long.valueOf(params.get("thirdPartyId").toString());
        Long userId = Long.valueOf(params.get("userId").toString());
        String userName = params.get("userName").toString();
        String tableName = params.get("tableName").toString();
        String currentTime = DateUtil.getCurrentDateTime();
        
        Map<String, Object> data = (Map) params.get("data");
        List<Map<String, Object>> addList = (List) data.get("addList");
        List<Map<String, Object>> updateList = (List) data.get("updateList");
        List<Map<String, Object>> deleteList = (List) data.get("deleteList");
        List<Map<String, Object>> normalList = (List) data.get("normalList");
        List<Map<String, Object>> invalidList = (List) data.get("invalidList");
        
        // 获取所有的数据
        List<Map<String, Object>> unknownList = (List) data.get("unknownList");
        unknownList = Optional.ofNullable(unknownList).orElseGet(ArrayList::new);
        if (CollectionUtils.isNotEmpty(addList)) {
            unknownList.addAll(addList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            unknownList.addAll(updateList);
        }
        if (CollectionUtils.isNotEmpty(deleteList)) {
            unknownList.addAll(deleteList);
        }



        List<Map<String, Object>> insertLs = new ArrayList<>();
        List<Map<String, Object>> updateLs = new ArrayList<>();
        List<Map<String, Object>> deleteLs = new ArrayList<>();
        List<Map<String, Object>> normalLs = new ArrayList<>();
        List<Map<String, Object>> invalidLs = new ArrayList<>();

        insertLs.addAll(addList);
        deleteLs.addAll(deleteList);
        updateLs.addAll(updateList);
        invalidLs.addAll(invalidList);
        normalLs.addAll(normalList);


        
        Map<String, Object> p = new HashMap<>();
        p.put("thirdPartyId", thirdPartyId);
        p.put("userId", userId);
        p.put("userName", userName);

        // 执行删除，避免插入时的逻辑主键冲突
        if (CollectionUtils.isNotEmpty(deleteLs)) {
            p.put("currentTime", currentTime);
            p.put("list", deleteLs);
            repository.delete(delete, p);

            // 备份数据，标记为 deleted
            thirdTableItemService.delete(deleteLs, params);
        }

        // 执行更新，避免插入时的逻辑主键冲突
        if (CollectionUtils.isNotEmpty(updateLs)) {
            p.put("currentTime", DateUtil.getCurrentDateTime());
            p.put("list", updateLs);

            // 先删除
            repository.delete(delete, p);

            // 后插入
            for (Map<String, Object> item : updateLs) {
                item.put("modifierId", userId);
                item.put("modifierName", userName);
                item.put("modifyDateTime", currentTime);

                if (ObjectUtil.isNumeric(item.get("createDateTime"))) {
                    long createDateTime = Long.parseLong(item.get("createDateTime").toString());
                    Date date = new Date(createDateTime);
                    item.put("createDateTime", DateUtil.formatDateTime(date));
                }
            }
            repository.insert(update, p);

            // 过滤出备份的数据，更新
            thirdTableItemService.update(updateLs, params);
        }

        // 最后执行新增，有可能出现逻辑主键冲突
        if (CollectionUtils.isNotEmpty(insertLs)) {

            for (Map<String, Object> map : insertLs) {
                map.put("modifierId", userId);
                map.put("modifierName", userName);
                map.put("modifyDateTime", currentTime);
            }

            repository.batchInsert(insert, insertLs);
            // 学校关联关系
            thirdTableItemService.insert(insertLs, params);
        }

        // 更新状态
        thirdDataSyncService.saveThirdDataSync(params, insertLs, updateLs, deleteLs, normalLs, invalidLs);

        data.put(addListKey, insertLs);
        data.put(updateListKey, updateLs);
        data.put(deleteListKey, deleteLs);
        return data;
        
        
    }
    
    public List<Map<String, Object>> getOldUserList(Map<String, Object> params) {
        List<Map<String, Object>> userList = new ArrayList<>();
        
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserSchoolAdmin", params));
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserTeacher", params));
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserStudent", params));
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserParent", params));
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserSchoolPrincipal", params));
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserGradeDirector", params));
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserInstructor", params));
        userList.addAll(repository.selectList("ThirdSelectMapper.getUserEducationDirector", params));
        
        return userList;
    }

    public List<Map<String, Object>> getOldSchoolPrincipalCourseList(Map<String, Object> params) {
        return repository.selectList("ThirdSelectMapper.selectSchoolPrincipalCourse", params);
    }
    
}
