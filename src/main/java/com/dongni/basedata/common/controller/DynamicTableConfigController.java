package com.dongni.basedata.common.controller;

import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.entity.Response;
import com.dongni.exam.common.mark.serivice.basedata.IDynamicTableConfigService;
import com.dongni.exam.common.mark.vo.DynamicTableConfigVO;
import com.dongni.exam.mark.controller.MarkBaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 动态表格配置
 */
@RestController
@RequestMapping("/dynamic/table/config")
public class DynamicTableConfigController extends MarkBaseController {

	@Autowired
	private IDynamicTableConfigService dynamicTableConfigService;

	/**
	 * 保存配置
	 */
	@PostMapping("/save")
	@DongniRequest(operationName = "动态表格配置.保存配置", remark = {"TODO 权限"})
	public Response setProgressConfig() {
		DynamicTableConfigVO params = getParams(DynamicTableConfigVO.class);
		dynamicTableConfigService.saveConfig(params);
		return new Response();
	}

	/**
	 * 查询配置
	 */
	@GetMapping("/detail")
	@DongniRequest(operationName = "动态表格配置.查询配置", remark = {"TODO 权限"})
	public Response getProgressConfig() {
		DynamicTableConfigVO params = getParams(DynamicTableConfigVO.class);
		return new Response(dynamicTableConfigService.getDynamicTableConfig(params));
	}
}
