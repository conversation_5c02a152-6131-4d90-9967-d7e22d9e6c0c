package com.dongni.basedata.task.service;

import com.dongni.analysis.stat.service.ExamStatQueueService;
import com.dongni.basedata.admin.employee.service.ServerQueueMonitorService;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.task.bean.TaskQueue;
import com.dongni.basedata.task.bean.TaskType;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.tiku.common.util.KnowledgeUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.pugwoo.wooutils.json.JSON;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务中心
 *
 * <AUTHOR>
 * @date 2019/02/15 17:07
 */
@Service
public class TaskService {

    @Autowired
    private BaseDataRepository repository;
    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private ExamStatQueueService examStatQueueService;
    @Autowired
    private ExamService examService;
    /**
     * 是第几次来刷新知识点。
     * 2023年4月对高中做过一次刷新，那个是第一次。2024年6月需要刷新小学和初中的，这个是第二次
     */
    private static final int CREATE_BY_SCHEDULE_VERSION = 2;

    /**
     * 新增统计任务
     *
     * @param params examId examName statId statName totalStep paramsJson
     * @return
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    public Long insertAnalysisTask(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .isNumeric("totalStep")
                .isNotBlank("paramsJson")
                .verify();

        Map<String, Object> examDetail = examService.getExamDetail(params);
        params.putAll(examDetail);

        // 新增任务
        params.put("taskName", params.get("examName"));
        params.put("taskType", DictUtil.getDictValue("taskType", "analysis"));
        params.put("step", 0);
        params.put("taskStatus", DictUtil.getDictValue("taskStatus", "waiting"));
        params.put("currentTime", DateUtil.getCurrentDateTime());
        repository.insert("TaskMapper.insertTask", params);

        // 新增统计任务
        repository.insert("TaskMapper.insertTaskAnalysis", params);

        return Long.valueOf(params.get("taskId").toString());

    }

    /**
     * 查询统计任务
     *
     * @param params examId stats
     * @return
     */
    public List<Map<String, Object>> getTaskInStats(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNotNull("stats")
                .verify();

        return repository.selectList("TaskMapper.getTaskInStats", params);

    }

    /**
     * 查询统计任务
     *
     * @param params examId statId [pageNo] [pageSize]
     * @return
     */
    public Map<String, Object> getTaskAnalysis(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .verify();

        Map<String, Object> result = new HashMap<>();

        Integer count = repository.selectOne("TaskMapper.selectTaskAnalysisCount", params);
        if (count == 0) {
            result.put("totalCount", count);
            result.put("list", Collections.emptyList());
        }

        List<Map<String, Object>> taskList = repository.selectList("TaskMapper.selectTaskAnalysis", params);

        result.put("totalCount", count);
        result.put("list", taskList);

        return result;

    }

    /**
     * 获取在指定时间之后有原始报告的、已完成的统计任务的考试ids
     * 统计失败、统计完成、统计终止 都认为是已完成
     */
    public List<Long> getExamIdsByTaskCompleteTime(Long completeTime) {
        return repository.selectList("TaskMapper.getExamIdsByTaskCompleteTime",
          MapUtil.of("completeTime", DateUtil.formatDateTime(new Date(completeTime)), "taskStatusList",
            DictUtil.getDictValues("taskStatus", "fail", "finish", "stopped")));
    }

    /**
     * 执行对应考试下的所有和知识点有关的统计
     * @params startDate endDate 从定时任务来的话就没有这两个参数，调用api的话就可以传这两个参数
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void refreshExamKnowledge(Map<String, Object> params){

        if (!KnowledgeUtil.isXkwVersion20240614QuestionKnowledgeExchanged()) {
            //不是已经切换好知识点的环境，不刷新统计
            return;
        }
        params.putIfAbsent("createBySchedule", CREATE_BY_SCHEDULE_VERSION);
        //取最新的一条刷新日志
        Map<String, Object> latestRefreshLog = examRepository.selectOne("ExamKnowledgeRefreshLogMapper.getLatestRefreshLog", params);
        LocalDateTime now =
          MapUtils.isEmpty(latestRefreshLog) ? LocalDateTime.now() : ((Timestamp) latestRefreshLog.get("startDate")).toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        params.putIfAbsent("endDate", formatter.format(now));
        //一次刷新近1年的考试
        params.putIfAbsent("startDate", formatter.format(now.minusYears(1)));
        params.put("currentTime", DateUtil.getCurrentDateTime());
        //只查询在时间范围内的、小学和初中的校内考
        List<Long> examIds = examRepository.selectList("ExamMapper.getExamIdsByStartDateAndEndDate", params);
        params.put("examCount", examIds.size());
        //保存到刷新日志中
        examRepository.insert("ExamKnowledgeRefreshLogMapper.insertExamKnowledgeRefreshLog", params);

        Map<String, Object> baseParam = MapUtil.of("userId", 1, "userName", "定时任务", "statId", 0);
        examIds.forEach(examId -> {
            baseParam.put("examId", examId);
            examStatQueueService.refreshKnowledge(baseParam);
        });
    }

    /**
    * @Description: 根据taskId获取统计任务详情
    * @Param: taskId
    * @return:
    */
    public Map getTaskByTaskId(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("taskId")
                .verify();

        return repository.selectOne("TaskMapper.getTaskByTaskId", params);
    }

    /**
     * 根据examId和statId获取正在统计中的统计任务id
     * @param examId 考试id
     * @param statId 报告id
     * @return 统计任务id
     */
    public Long getExecutingTaskIdByExamIdAndStatId(long examId, long statId) {
        return repository.selectOne("TaskMapper.getExecutingTaskIdByExamIdAndStatId",
          MapUtil.of("examId", examId, "statId", statId, "taskStatus", DictUtil.getDictValue("taskStatus", "executing")));
    }

    /**
     * 重新刷新统计
     *
     * @param params taskId
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    public void againAnalysis(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("taskId")
                .verify();

        // todo 分布式锁
        synchronized (TaskService.class) {
            // 查询统计参数
            Map<String, Object> taskAnalysis = repository.selectOne("TaskMapper.getTaskAnalysisDetail", params);

            // 查询是否在redis中
            String queueKey = JedisUtil.getKey("task.params", TaskType.ANALYSIS.getKey(), taskAnalysis.get("queueCode").toString());
            Boolean isExist = JedisTemplate.execute(jedis -> jedis.exists(queueKey));
            if (isExist) {
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前任务统计中，请勿重复操作");
            }

            // 重置统计状态
            params.put("step", 0);
            params.put("taskStatus", DictUtil.getDictValue("taskStatus", "waiting"));
            params.put("currentTime", DateUtil.getCurrentDateTime());
            repository.update("TaskMapper.updateTaskAnalysis", params);

            // 添加到统计队列
            Map<String, Object> paramsJsonMap = JSON.parse(taskAnalysis.get("paramsJson").toString(), Map.class);
            Map<String, String> queueParams = new HashMap<>();
            paramsJsonMap.forEach((key, value) -> queueParams.put(key, value.toString()));
            TaskQueue.addTaskQueue(queueParams);
        }
    }

    /**
     * 将大于10分钟进度没有变化的任务重新加入队列
     *
     * @param params userId userName
     */
    @Transactional(BaseDataRepository.TRANSACTION)
    public void retryAnalysis(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();

        int waiting = DictUtil.getDictValue("taskStatus", "waiting");
        int executing = DictUtil.getDictValue("taskStatus", "executing");

        // 重新所有正在执行和等待中的任务
        params.put("currentTime", DateUtil.getCurrentDateTime());
        params.put("taskType", TaskType.ANALYSIS.getType());
        params.put("taskStatusList", Arrays.asList(waiting, executing));
        List<Map<String, Object>> taskList = repository.selectList("TaskMapper.getTaskList", params);

        // 正在执行的任务
        List<Map<String, Object>> executingTaskList = taskList.stream()
                .filter(item -> ObjectUtil.isValueEquals(item.get("taskStatus"), executing)
                        && (item.get("retryCount") == null || Integer.valueOf(item.get("retryCount").toString()) < 3))
                .collect(Collectors.toList());

        // 等待中的任务
        List<Map<String, Object>> waitingTaskList = taskList.stream()
                .filter(item -> ObjectUtil.isValueEquals(item.get("taskStatus"), waiting)
                        && (item.get("retryCount") == null || Integer.valueOf(item.get("retryCount").toString()) < 3))
                .collect(Collectors.toList());

        // 重试次数等于3，将任务置为异常
        List<Long> errTaskIds = taskList.stream()
                .filter(item -> item.get("retryCount") != null
                        && Integer.valueOf(item.get("retryCount").toString()) == 3)
                .map(item -> Long.valueOf(item.get("taskId").toString()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errTaskIds)) {
            params.put("taskStatus", DictUtil.getDictValue("taskStatus", "fail"));
            params.put("taskIds", errTaskIds);
            repository.update("TaskMapper.batchUpdateTaskStatus", params);

            // 需要多插入一条记录，防止第三次执行时，将状态修改为异常
            repository.update("TaskMapper.batchInsertTaskRetry", params);
        }

        // 10分钟之前
        Date before10Date = new Date(System.currentTimeMillis() - 10 * 60 * 1000);

        // 正在执行中的任务10分钟进度无变化
        for (Map<String, Object> item : executingTaskList) {
            reStartAnalysis(params, before10Date, item);
        }

        // redis队列中不存在，并且已经等待10分钟无反应
        String queueKey = JedisUtil.getKey("task.queue", TaskType.ANALYSIS.getKey());
        for (Map<String, Object> item : waitingTaskList) {
            Boolean isExist = JedisTemplate.execute(jedis -> {
                List<String> ls = jedis.lrange(queueKey, 0, -1);
                if (CollectionUtils.isNotEmpty(ls)) {
                    for (String code : ls) {
                        if (item.get("queueCode").toString().equalsIgnoreCase(code)) {
                            return true;
                        }
                    }
                }
                return false;
            });

            if (!isExist) {
                reStartAnalysis(params, before10Date, item);
            }
        }
    }

    /**
     * 重新开始统计
     *
     * @param params
     * @param before10Date
     * @param item
     */
    private void reStartAnalysis(Map<String, Object> params, Date before10Date, Map<String, Object> item) {
        Date modifyDateTime = (Date) item.get("modifyDateTime");
        if (modifyDateTime.before(before10Date)) {
            params.put("taskId", item.get("taskId"));

            // 添加一次统计刷新记录
            repository.insert("TaskMapper.insertTaskRetry", params);

            // 先删除参数
            String key = JedisUtil.getKey("task.params", TaskType.ANALYSIS.getKey(), item.get("queueCode").toString());
            JedisTemplate.execute(jedis -> jedis.del(key));

            // 将当前任务重新加入到统计队列
            againAnalysis(params);
        }
    }
}
