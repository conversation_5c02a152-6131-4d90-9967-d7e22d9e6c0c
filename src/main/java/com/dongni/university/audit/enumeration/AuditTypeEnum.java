package com.dongni.university.audit.enumeration;

/**
 * <AUTHOR>
 * @date 2018/09/13 19:15
 */
public enum AuditTypeEnum {

    PAPER(1, "试卷"),
    EXAM(2, "成绩"),
    COURSE(3, "课程自评");

    private int type;

    private String name;

    AuditTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
