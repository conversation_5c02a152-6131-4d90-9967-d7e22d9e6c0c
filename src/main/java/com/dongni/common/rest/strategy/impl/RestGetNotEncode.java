package com.dongni.common.rest.strategy.impl;

import com.dongni.common.rest.strategy.RestGetStrategy;
import com.dongni.commons.utils.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.net.URI;
import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/12/15 <br/>
 *
 */
@Component
public class RestGetNotEncode extends RestGetStrategy {
    
    private final static Logger LOGGER = LoggerFactory.getLogger(RestGetNotEncode.class);
    
    @Override
    public <T> ResponseEntity<T> get(String url, Map<String, Object> queryParams, Class<T> type) {
        URI uri = HttpUtil.getUri(url, false, null, queryParams);
        
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(headers);
        
//        LOGGER.info("请求:GET " + uri.toString());
        return restTemplate.exchange(uri, HttpMethod.GET, request, type);
    }
}
