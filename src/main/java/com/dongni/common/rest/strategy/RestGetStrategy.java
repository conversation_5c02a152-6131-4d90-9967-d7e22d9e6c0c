package com.dongni.common.rest.strategy;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

public abstract class RestGetStrategy {

    @Autowired
    protected RestTemplate restTemplate;

    /**
     * 请求头
     */
    protected HttpHeaders headers = new HttpHeaders();

    /**
     * @Description: 设置请求头
     */
    public RestGetStrategy setHeaders(HttpHeaders headers) {
        this.headers = headers;
        return this;
    }

    /**
     * 获取请求头
     * @return
     */
    public HttpHeaders getHeaders() {
        return this.headers;
    }

    public abstract <T> ResponseEntity<T> get(String url, Map<String,Object> queryParams,Class<T> type);

}
