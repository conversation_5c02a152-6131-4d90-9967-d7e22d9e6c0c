package com.dongni.common.schedule;

import com.dongni.common.utils.IPUtil;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.ResourceConfig;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.service.AnswerCardCompleteService;
import com.dongni.exam.card.service.AnswerCardService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 答题卡识别任务调度相关处理
 * Created by 马腾 on 2016/9/27.
 */
@Service
public class AnswerCardRecognitionSchedule {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final String answerCardMap = ResourceConfig.getString("answerCardMap");
    private final String answerCardRecognitionAfterQueue = ResourceConfig.getString("answerCardRecognitionAfterQueue");
    private final String answerCardCompleteAfterQueue = ResourceConfig.getString("answerCardCompleteAfterQueue");

    @Autowired
    private ExamRepository repository;

    @Autowired
    private AnswerCardService answerCardService;

    @Autowired
    private AnswerCardCompleteService answerCardCompleteService;

    /**
     * 当答题卡识别完成后做的处理，更新识别类型，修改待办的跳转链接。每隔3秒从队列中获取需要处理的任务
     */
    @Scheduled(cron = "*/3 * * * * ?")
    public void doAfterRecognition() {
        Map<String, String> params = getParams(answerCardRecognitionAfterQueue);

        // 如果获取到了相应的科目Map，则进行识别后的处理
        if(params != null && params.size() > 0) {
            logger.info(params.get("examUploaderId") + " - 识别完成之后的处理任务开始");

            // 记录处理日志
            Map<String, Object> logParams = new HashMap<>(params);
            logParams.put("recognitionType", "start");
            // 默认处理正常
            logParams.put("handleStatus", 0);

            // 出错后的重试次数
            int tryCount = 0;
            // 后处理是否成功
            boolean isSuccess = false;

            while (tryCount < 3) {
                try {
                    answerCardService.updateAnswerCardAfterRecognition(params);
                    isSuccess = true;
                    break;
                } catch (Exception e) {
                    logger.error(" 异常处理完成之后的处理任务异常：", e);

                    // 处理异常记录异常信息
                    logParams.put("handleStatus", -1);
                    logParams.put("errMsg", e.getMessage());
                }

                try {
                    // 5s后再次重试
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                }

                tryCount++;
            }
            this.clear(params);

            if(!isSuccess) {
                // 记录错误日志
                try {
                    insertLog(logParams);
                } catch (Exception e) {
                    logger.error("日志记录失败：", e);
                }
            }

            logger.info(params.get("examUploaderId") + " - 识别完成之后的处理任务结束");
        }
    }

    /**
     * 异常处理之后的执行任务，需要重新识别异常答题卡，然后产生阅卷待办。每隔3秒从队列中获取需要处理的任务
     */
    @Scheduled(cron = "*/3 * * * * ?")
    public void doComplete() {
        Map<String, String> parameterMap = getParams(answerCardCompleteAfterQueue);

        // 如果获取到了相应的科目Map，则进行识别后的处理
        if(parameterMap != null && parameterMap.size() > 0) {
            logger.info(parameterMap.get("examUploaderId") + " - 异常处理完成之后的处理任务开始");

            // 记录处理日志
            Map<String, Object> logParams = new HashMap<>(parameterMap);
            logParams.put("recognitionType", "complete");
            // 默认处理正常
            logParams.put("handleStatus", 0);

            // 出错后的重试次数
            int tryCount = 0;
            // 后处理是否成功
            boolean isSuccess = false;
            
            if (MapUtil.getBoolean(parameterMap.get("notUpdateAnswerCardAfterComplete"))) {
                // 生产者要求不进行后续操作
                isSuccess = true;
                logParams.put("errMsg", "notUpdateAnswerCardAfterComplete");
            } else {
                while (tryCount < 3) {
                    try {
                        answerCardCompleteService.updateAnswerCardAfterComplete(ObjectUtil.removeMapNull(parameterMap));
                        isSuccess = true;
                        break;
                    } catch (Exception e) {
                        logger.error(" 异常处理完成之后的处理任务异常：", e);
    
                        // 处理异常记录异常信息
                        logParams.put("handleStatus", -1);
                        logParams.put("errMsg", e.getMessage());
                    }
        
                    try {
                        // 5s后再次重试
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                    }
        
                    tryCount++;
                }
            }

            this.clear(parameterMap);

            if(!isSuccess) {
                // 记录错误日志
                try {
                    insertLog(logParams);
                } catch (Exception e) {
                    logger.error("日志记录失败：", e);
                }
            }

            logger.info(parameterMap.get("examUploaderId") + " - 异常处理完成之后的处理任务结束");
        }
    }

    private Map<String, String> getParams(String queueName) {
        AtomicReference<String> __examUploaderId__ = new AtomicReference<>();
        Map<String, String> redisMap = JedisTemplate.execute(jedis -> {
            String examUploaderId = jedis.rpop(queueName);
            if (StringUtils.isNotBlank(examUploaderId)) {
                __examUploaderId__.set(examUploaderId);
                return jedis.hgetAll(answerCardMap + examUploaderId);
            }
            return null;
        });

        if(MapUtils.isEmpty(redisMap)) {
            String examUploaderId = __examUploaderId__.get();
            if(StringUtils.isNotBlank(examUploaderId)) {
                redisMap = answerCardService.getTaskRedis(examUploaderId);
            }
        }

        return redisMap;
    }

    /**
     * 清除redis遗留数据
     * @param params
     */
    private void clear(Map<String, String> params) {
        String mapName = ResourceConfig.getString("answerCardMap") + params.get("examUploaderId");
        // 识别次数keyName
        String recognitionTimesKey = ResourceConfig.getString("answerCardRecognitionTimes") + params.get("examUploaderId");
        // 处理完成之后，删除掉该科目相应的Map
        JedisTemplate.executePipeline(pipeline -> {
            pipeline.del(mapName);
            pipeline.del(recognitionTimesKey);
            pipeline.del(mapName + ":error");
        });
    }

    /**
     * 记录日志
     *
     * @param params
     */
    private void insertLog(Map<String, Object> params) {

        InetAddress localHostLANAddress = IPUtil.getLocalHostLANAddress();
        if (localHostLANAddress != null) {
            params.put("serverIp", localHostLANAddress.getHostAddress());
        } else {
            params.put("serverIp", "-1");
        }

        params.put("currentTime", DateUtil.getCurrentDateTime());
        repository.insert("ExamRecognitionLogMapper.insertLog", params);
    }
}
