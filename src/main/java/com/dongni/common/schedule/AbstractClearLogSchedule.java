package com.dongni.common.schedule;

import com.dongni.commons.redis.template.JedisTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 清理日志
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2019-10-24 19:44
 **/
@Service
public abstract class AbstractClearLogSchedule {

    private final static Logger log = LoggerFactory.getLogger(AbstractClearLogSchedule.class);

    /**
     * 清理的日志时期
     */
    protected String dateTime;

    /**
     * 清理的日志开始时期
     */
    protected String startTime;

    /**
     * 打印log的日期描述信息 例：2019-10-05 10:14:26至2019-10-25 10:14:26
     */
    protected String durationDate;
    protected int numberOfDays = 7;

    /**
    * 默认清理15天前的日志
    */
    public AbstractClearLogSchedule() {
        init(numberOfDays);
    }

    /**
     * 获取日志表名
     * @return 表名
     */
    public abstract String getLogTableName();

    /**
     * 分布式锁
     */
    public abstract String getRedisLock();

    /**
     * 获取需要清理日志的最大Id
     *
     * @param dateTime 小于该日期的日志会被清理
     * @param startTime 大于该日期的日志会被清理
     * @return 日志表主键
     */
    public abstract Long getBeforeMaxLogId(String dateTime, String startTime);

    /**
     * 清理日志
     * @param beforeMaxLogId 小于该Id的日志会被清理
     */
    public abstract void deleteLogData(long beforeMaxLogId);

    /**
    * 根据设置的天数计算dateTime和durationDate
    * @Param: numberOfDays 天数
    */
    public void setNumberOfDays(int numberOfDays) {
        init(numberOfDays);
    }

    /**
     * 清理日志
     */
    public void clearLog() {
        init(numberOfDays);
        String logTableName = getLogTableName();

        log.info("定时任务开始，清理{}的日志 {}", durationDate, logTableName);
        long startTime = System.currentTimeMillis();

        Long beforeMaxLogId = getBeforeMaxLogId(dateTime, this.startTime);
        log.info("查询删除日志的最大Id耗时：{}ms", System.currentTimeMillis() - startTime);

        if (beforeMaxLogId == null) {
            log.info("定时任务结束，{}中没有需要删除的日志", logTableName);
            return;
        }

        try {
            deleteLogData(beforeMaxLogId);
        } catch (Exception e) {
            log.error("定时任务异常，清理{}失败：e={}", logTableName, e);
        }
        log.info("定时任务结束，清理{}的日志 {},耗时：{}ms", durationDate, logTableName,System.currentTimeMillis() - startTime);
    }

    /**
    * 计算dateTime和durationDate和startTime
    * @Param:  numberOfDays 天数
    */
    protected void init(int numberOfDays) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startDateTime = LocalDateTime.now().minusDays(numberOfDays).format(formatter);

        this.dateTime = startDateTime;
        this.startTime = LocalDateTime.now().minusDays(numberOfDays + 1).format(formatter);
        this.durationDate = startDateTime + "至" + LocalDateTime.now().format(formatter);
    }
}

