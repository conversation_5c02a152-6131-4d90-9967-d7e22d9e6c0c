package com.dongni.common.db;

import com.alibaba.druid.pool.DruidDataSource;

/**
 * <AUTHOR>
 */
public abstract class AbstractDatasourceBeans {
	public DruidDataSource createDataSourceWithDefaultConfig(){
		DruidDataSource dataSource = new DruidDataSource();
		dataSource.setMaxWait(15000); // 15秒钟连接不上
		dataSource.setMinIdle(1); // 最小连接池数量
		dataSource.setTimeBetweenEvictionRunsMillis(60000);
		dataSource.setMinEvictableIdleTimeMillis(300000);
		//建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效
		dataSource.setTestWhileIdle(true);
		//申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
		dataSource.setTestOnBorrow(true);
		//归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能
		dataSource.setTestOnReturn(false);
		// 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭
		//dataSource.setPoolPreparedStatements(true);
		// 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。
		//dataSource.setMaxOpenPreparedStatements(20);
		//asyncInit是1.1.4中新增加的配置，如果有initialSize数量较多时，打开会加快应用启动时间
		dataSource.setAsyncInit(true);
		dataSource.setKeepAlive(true);
		dataSource.setValidationQueryTimeout(1000);
		return dataSource;
	}
}
