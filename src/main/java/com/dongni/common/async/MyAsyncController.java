package com.dongni.common.async;

import com.dongni.basedata.admin.service.IBaseSchoolService;
import com.dongni.common.auth.DongniClientUtil;
import com.dongni.common.auth.impl.DongniClientAuthHuagaoImpl;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.entity.Response;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> <br/>
 * @date 2021/04/20 <br/>
 *
 */
@RestController
@RequestMapping("common/my")
public class MyAsyncController {

    @Autowired
    private MyAsyncExecutor myAsyncExecutor;

    /**
     * 是否将错误信息回显到前端，默认关闭
     * 关闭时返回 null
     */
    @Value("${dongni.web.exception.stack.enabled:false}")
    private boolean enableExceptionStack;

    @Autowired
    public HttpServletRequest request;

    @Autowired
    private IBaseSchoolService baseSchoolService;

    @GetMapping("/async")
    public Response getAsyncInfo(Map<String, Object> params) {
        Verify.of(params)
            .isNotBlank("uuid")
                .isValidId("userId")
            .verify();
        AsyncInfo result = myAsyncExecutor.getResult(MapUtil.getString(params, "uuid"), MapUtil.getLong(params, "userId"));
        if (result.isException() && !enableExceptionStack) {
            result.setError(null);
        }
        ResponseStatusEnum responseStatus = ResponseStatusEnum.codeBy(result.getResponseStatusCode());
        if (responseStatus == ResponseStatusEnum.SYSTEM_RESOURCE_LIMIT) {
            throw new CommonException(responseStatus);
        }
        return new Response(responseStatus, result);
    }

    @GetMapping("/client/async")
    @DongniNotRequireLogin
    public Response getClientAsyncInfo(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("uuid")
                .isNotBlank("authKey")
                .verify();
        DongniClientUtil.valid(request, params, new DongniClientAuthHuagaoImpl());
        params.put("userId", baseSchoolService.getSchoolId(params));
        AsyncInfo result = myAsyncExecutor.getResult(MapUtil.getString(params, "uuid"), MapUtil.getLong(params, "userId"));
        if (result.isException() && !enableExceptionStack) {
            result.setError(null);
        }
        return new Response(result);
    }

    @PostMapping("/async/test/string")
    public Response testString(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            for (int i = 0; i < 200; i++) {
                try {
                    Thread.sleep(1000);
                    MyAsyncExecutor.updateInfo("哈哈哈" + i);
                } catch (InterruptedException ignore) {
                }
            }
            return "test String";
        }));
    }
    
    @PostMapping("/async/test/string2")
    public Response testString2(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            MyAsyncExecutor.updateInfo("[1/3] 第一阶段", 0.0);
            try {
                Thread.sleep(5_000);
            } catch (InterruptedException ignore) {
            }
            MyAsyncExecutor.updateInfo("[2/3] 第二阶段", 0.3);
            try {
                Thread.sleep(6_000);
            } catch (InterruptedException ignore) {
            }
            MyAsyncExecutor.updateInfo("[3/3] 第三阶段", 0.75);
            try {
                Thread.sleep(7_000);
            } catch (InterruptedException ignore) {
            }
            return "test String2";
        }));
    }
    
    @PostMapping("/async/test/string3")
    public Response testString3(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            MyAsyncExecutor.updateInfo("[1/3] 第一阶段", 0.0, 0.3, 30);
            try {
                Thread.sleep(20_000);
            } catch (InterruptedException ignore) {
            }
            MyAsyncExecutor.updateInfo("[2/3] 第二阶段", 0.3, 0.75, 30);
            try {
                Thread.sleep(40_000);
            } catch (InterruptedException ignore) {
            }
            MyAsyncExecutor.updateInfo("[3/3] 第三阶段", 0.75, 1.0, 20);
            try {
                Thread.sleep(20_000);
            } catch (InterruptedException ignore) {
            }
            return "test String2";
        }));
    }

    @PostMapping("/async/test/long")
    public Response testLong(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException ignore) {
            }
            return 123456L;
        }));
    }

    @PostMapping("/async/test/map")
    public Response testMap(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException ignore) {
            }
            return MapUtil.of("hello", "world");
        }));
    }

    @PostMapping("/async/test/list")
    public Response testList(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException ignore) {
            }
            return Stream.of(
                    MapUtil.of("hello", "world"),
                    MapUtil.of("德玛西亚", "人在塔在")
            ).collect(toList());
        }));
    }

    @PostMapping("/async/test/exception")
    public Response testException(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException ignore) {
            }
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "哟抛了一个异常");
        }));
    }

    @PostMapping("/async/test/exception2")
    public Response testException2(HttpServletRequest request, Map<String, Object> params) {
        Verify.of(params).isValidId("userId").verify();
        return new Response(myAsyncExecutor.execute(request, params, () -> {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException ignore) {
            }
            try {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "哟抛了一个异常");
            } catch (Exception e) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "哟套了一个异常", e);
            }
        }));
    }
}
