package com.dongni.common.config.table;

import com.dongni.exam.common.mark.config.table.IDynamicTableModule;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 区域报告成绩单
 *
 * <AUTHOR>
 * @Date 2025/2/24 周一 上午 10:41
 * @Version 1.0.0
 */
@Component
public class TranscriptAreaExam implements IDynamicTableModule {
    @Override
    public long getModuleId() {
        return 6;
    }

    @Override
    public List<String> getFields() {
        return Arrays.asList("schoolName", "areaName", "schoolPropertyName", "className", "studentNum", "studentExamNum", "studentNo");
    }

    @Override
    public List<String> getDefaultFields() {
        return Arrays.asList("schoolName", "className", "studentNum", "studentExamNum");
    }
}
