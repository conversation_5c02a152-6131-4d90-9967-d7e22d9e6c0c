package com.dongni.common.config.table;

import com.dongni.exam.common.mark.config.table.IDynamicTableModule;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 联考报告成绩单
 *
 * <AUTHOR>
 * @Date 2025/2/24 周一 上午 10:32
 * @Version 1.0.0
 */
@Component
public class TranscriptUnionExam implements IDynamicTableModule {
    @Override
    public long getModuleId() {
        return 4;
    }

    @Override
    public List<String> getFields() {
        return Arrays.asList("schoolName", "className", "studentNum", "studentExamNum", "studentNo");
    }

    @Override
    public List<String> getDefaultFields() {
        return Arrays.asList("schoolName", "className", "studentNum", "studentExamNum");
    }
}
