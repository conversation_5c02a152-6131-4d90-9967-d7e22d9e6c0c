package com.dongni.common.temp.condition;

import com.dongni.common.utils.spring.SpringProfilesActiveUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * Create by sapluk <br/>
 * time 11:28 2018/11/26 <br/>
 * description: <br/>
 *  OSS Service Impl Bean 条件
 */
public class CommonRedisCondition implements Condition {

    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        String mavenProfile = SpringProfilesActiveUtil.getSpringProfilesActive(conditionContext.getEnvironment());
        if (StringUtils.isNotBlank(mavenProfile) && StringUtils.endsWith(mavenProfile, "-local")) {
            String commonRedisHost = conditionContext.getEnvironment().getProperty("dongni.redis.common.host");
            return StringUtils.isNotBlank(commonRedisHost);
        }
        return false;
    }
}
