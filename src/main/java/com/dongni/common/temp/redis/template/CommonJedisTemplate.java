package com.dongni.common.temp.redis.template;

import com.dongni.common.temp.redis.client.CommonJedisClient;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.service.IJedisHandle;
import com.dongni.commons.redis.service.IPipelineHandle;
import com.dongni.commons.redis.service.ITransactionHandle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Transaction;
import redis.clients.jedis.exceptions.JedisConnectionException;

import java.util.List;

/**
 * Jedis Template ,只能通过execute方法执行操作，禁止自行获取Jedis实例
 * 避免自行获取Jedis实例忘记释放
 * Created by 马腾 on 2016/5/18.
 *  注意 该类仅限特殊情况使用，没事不要用！！！！！！！！
 */
@Deprecated
public class CommonJedisTemplate {

    private static final Logger log = LoggerFactory.getLogger(CommonJedisTemplate.class);

    /**
     * 执行Redis命令
     *
     * @param handle 功能接口
     * @param <T>    返回类型
     * @return 结果
     */
    public static <T> T execute(IJedisHandle<T> handle) {
        T result;
        try (Jedis jedis = CommonJedisClient.getJedisPool().getResource()) {
            result = handle.execute(jedis);

        } catch (Exception e) {
            log.error("redis execute 出现异常：{}", e.getMessage(), e);
            if (e instanceof JedisConnectionException) {
                throw new CommonException(ResponseStatusEnum.REDIS_CONNECTION_TIMEOUT);
            }
            if (e instanceof CommonException) {
                throw e;
            }
            throw new CommonException(ResponseStatusEnum.REDIS_PROCESS_ERROR, e);
        }
        return result;
    }

    /**
     * 执行 Redis命令，使用管道流
     *
     * @param handle 功能接口
     * @return 结果
     */
    public static List<Object> executePipeline(IPipelineHandle handle) {
        try (Jedis jedis =  CommonJedisClient.getJedisPool().getResource()) {
            Pipeline pipeline = jedis.pipelined();
            handle.execute(pipeline);
            return pipeline.syncAndReturnAll();
        } catch (Exception e) {
            log.error("redis pipeline 出现异常：{}", e.getMessage(), e);
            if (e instanceof JedisConnectionException) {
                throw new CommonException(ResponseStatusEnum.REDIS_CONNECTION_TIMEOUT);
            }
            throw new CommonException(ResponseStatusEnum.REDIS_PROCESS_ERROR, e);
        }
    }

    /**
     * 执行 Redis命令，使用该事务操作
     *
     * @param handle 功能接口
     * @return 结果
     */
    public static List<Object> executeTransaction(ITransactionHandle handle, String... keys) {
        try (Jedis jedis =  CommonJedisClient.getJedisPool().getResource()) {
            if (keys != null && keys.length > 0) {
                jedis.watch(keys);
            }
            Transaction transaction = jedis.multi();
            handle.execute(transaction);
            return transaction.exec();
        } catch (Exception e) {
            log.error("redis transaction 出现异常：{}", e.getMessage(), e);
            if (e instanceof JedisConnectionException) {
                throw new CommonException(ResponseStatusEnum.REDIS_CONNECTION_TIMEOUT);
            }
            throw new CommonException(ResponseStatusEnum.REDIS_PROCESS_ERROR, e);
        }
    }

}
