package com.dongni.common.temp.redis.client;

import com.dongni.common.temp.condition.CommonRedisCondition;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Redis 客户端，获取
 * Created by 马腾 on 2016/5/18.
 */
@Component
@Conditional(CommonRedisCondition.class)
public final class CommonJedisClient {

    private static JedisPool jedisPool = null;

    private static String host;
    private static Integer port;
    private static String password;
    private static Integer database;
    private static Integer connectionTimeout;
    private static Integer poolMaxTotal;
    private static Integer poolMaxIdle;
    private static Integer poolMinIdle;
    private static Integer maxWaitMillis;
    private static Integer softMinEvictableIdleTimeMillis;
    private static Boolean testOnBorrow;
    private static Boolean testOnReturn;
    private static Boolean testOnCreate;
    private static Boolean testWhileIdle;

    @Value("${dongni.redis.common.host}")
    public void setHost(String host) {
        CommonJedisClient.host = host;
    }

    @Value("${dongni.redis.common.port:37645}")
    public void setPort(Integer port) {
        CommonJedisClient.port = port;
    }

    @Value("${dongni.redis.common.password:Dongni2015}")
    public void setPassword(String password) {
        if (password.equals("")) {
            password = null;
        }
        CommonJedisClient.password = password;
    }

    @Value("${dongni.redis.common.database:0}")
    public void setDatabase(Integer database) {
        CommonJedisClient.database = database;
    }

    @Value("${dongni.redis.common.connectionTimeout:10000}")
    public void setConnectionTimeout(Integer connectionTimeout) {
        CommonJedisClient.connectionTimeout = connectionTimeout;
    }

    @Value("${dongni.redis.common.pool.max-total:511}")
    public void setPoolMaxTotal(Integer poolMaxTotal) {
        CommonJedisClient.poolMaxTotal = poolMaxTotal;
    }

    @Value("${dongni.redis.common.pool.max-idle:32}")
    public void setPoolMaxIdle(Integer poolMaxIdle) {
        CommonJedisClient.poolMaxIdle = poolMaxIdle;
    }

    @Value("${dongni.redis.common.pool.min-idle:2}")
    public void setPoolMinIdle(Integer poolMinIdle) {
        CommonJedisClient.poolMinIdle = poolMinIdle;
    }

    @Value("${dongni.redis.common.pool.max-wait-millis:3000}")
    public void setMaxWaitMillis(Integer maxWaitMillis) {
        CommonJedisClient.maxWaitMillis = maxWaitMillis;
    }

    @Value("${dongni.redis.common.pool.soft-min-evictable-idle-time-millis:60000}")
    public void setSoftMinEvictableIdleTimeMillis(Integer softMinEvictableIdleTimeMillis) {
        CommonJedisClient.softMinEvictableIdleTimeMillis = softMinEvictableIdleTimeMillis;
    }

    @Value("${dongni.redis.common.pool.test-on-borrow:true}")
    public void setTestOnBorrow(Boolean testOnBorrow) {
        CommonJedisClient.testOnBorrow = testOnBorrow;
    }

    @Value("${dongni.redis.common.pool.test-on-return:true}")
    public void setTestOnReturn(Boolean testOnReturn) {
        CommonJedisClient.testOnReturn = testOnReturn;
    }

    @Value("${dongni.redis.common.pool.test-on-create:true}")
    public void setTestOnCreate(Boolean testOnCreate) {
        CommonJedisClient.testOnCreate = testOnCreate;
    }

    @Value("${dongni.redis.common.pool.test-while-idle:true}")
    public void setTestWhileIdle(Boolean testWhileIdle) {
        CommonJedisClient.testWhileIdle = testWhileIdle;
    }

    public CommonJedisClient() {
    }

    public static JedisPool getJedisPool() {
        if (jedisPool == null) {

            JedisPoolConfig config = new JedisPoolConfig();

            // 连接资源调配
            config.setMaxTotal(poolMaxTotal);
            config.setMaxIdle(poolMaxIdle);
            config.setMinIdle(poolMinIdle);

            config.setMaxWaitMillis(maxWaitMillis); // 从连接池获取连接超时时间为 3 秒
            config.setSoftMinEvictableIdleTimeMillis(softMinEvictableIdleTimeMillis); // 10分钟后检查空闲连接，且清除

            // 额外执行 ping ， 保证连接可用，因为服务端可以主动断开连接
            config.setTestOnBorrow(testOnBorrow);
            config.setTestOnReturn(testOnReturn);
            config.setTestOnCreate(testOnCreate);
            config.setTestWhileIdle(testWhileIdle);

            jedisPool = new JedisPool(config, host, port, connectionTimeout, password, database);

        }
        return jedisPool;
    }
}
