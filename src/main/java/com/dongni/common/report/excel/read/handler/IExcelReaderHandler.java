package com.dongni.common.report.excel.read.handler;

import com.dongni.common.report.excel.read.bean.ExcelReadMetaData;

import java.util.List;

/**
 * <AUTHOR>
 * @description excel导入处理接口
 * @date 2022年05月13日
 */
public interface IExcelReaderHandler {
    /**
     * 获取原始的excel数据列表
     * @param excelReadMetaData 读取元数据
     * @return excel数据
     */
    <T> List<T> read(ExcelReadMetaData<T> excelReadMetaData);

    /**
     * 获取原始的excel数据列表
     * @param excelReadMetaData 读取元数据
     * @return excel数据
     */
    <T> List<T> readForEasyExcel(ExcelReadMetaData<T> excelReadMetaData);

}
