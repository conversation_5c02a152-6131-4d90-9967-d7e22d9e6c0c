package com.dongni.common.report.pdf.utils;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import java.io.IOException;

/**
 * Created by sifan.liu on 2017/4/12.
 */
public class PDFUtil {
    public static int DEFAULT_FONT_SIZE = 12;

    /**
     * @param fontSize 字体大小
     * @return
     * @throws IOException
     * @throws DocumentException
     */
    public static Font createChineseSong(int fontSize) {
        try {
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            return new Font(bfChinese, fontSize, Font.NORMAL);
        }catch (Exception e){
            throw new RuntimeException();
        }
    }

    /**
     * 创建宋体
     *
     * @return 宋体
     * @throws IOException
     * @throws DocumentException
     */
    public static Font createChineseSong() {
        return createChineseSong(DEFAULT_FONT_SIZE);
    }
}
