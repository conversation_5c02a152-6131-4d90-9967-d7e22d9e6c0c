package com.dongni.common.report.pdf.service;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageGet;
import com.dongni.commons.utils.file.CompressFileUtils;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.pugwoo.wooutils.json.JSON;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020/04/22 13:10
 */
@Service
public class MergeService {
    private static final Logger log = LogManager.getLogger(MergeService.class);


    public String mergeToZIP(Map<String, Object> params) {
        // 参数校验
        Verify.of(params)
                // 多条url用逗号隔开
                .isNotBlank("urlList")
                .isNotBlank("fileName")
                .verify();
        log.info("param:" + JSON.toJson(params));

        String[] result = new String[]{""};
        Object urlObject = params.get("urlList");
        if (ObjectUtil.isBlank(urlObject)) {
            return result[0];
        }
        String[] splitUrls = urlObject.toString().split(",");
        List<String> urlList = Arrays.asList(splitUrls);
        String fileName = params.get("fileName").toString();
        try {
            List<FileStorageGet> fileStorageGets = new ArrayList<>();
            urlList.forEach(url -> {
                FileStorageGet fileStorageGet1 = new FileStorageGet();
                fileStorageGet1.setFilePath(url);
                fileStorageGets.add(fileStorageGet1);
            });
            FileStorageTemplate.batchGet(fileStorageGets, fileStorageBatchGet -> {
                List<FileStorageGet> fileStorageGetList = fileStorageBatchGet.getFileStorageGets();
                // 文件位置以第一个为基础
                String zipPath = fileStorageGetList.get(0).getFile().getParent() + "/" + fileName + ".zip";
                fileStorageGetList.forEach(fileStorageGet -> {
                    File file1 = fileStorageGet.getFile();
                    String newName = file1.getParent() + "/" + fileName + "." + file1.getName().split("\\.")[1];
                    File file = new File(newName);
                    try {
                        FileUtils.copyFile(file1, file);
                    } catch (IOException e) {
                        LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                    }
                    File zipFile = new File(zipPath);
                    CompressFileUtils.zip(file, zipFile);
                    // 删除本地file1
//                    FileStorageTemplate.delete(file1.getAbsolutePath());
                });
                result[0] = export(fileName, new File(zipPath));
            });
        } catch (Exception e) {
            log.error("param:" + JSON.toJson(params) +
                    "error:" + e.getMessage());
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, e.getMessage(), e);
        }
        return result[0];
    }

    /**
     * 导出zip文件
     *
     * @param fileName
     * @return
     */
    public String export(String fileName, File file) {
        return FileStorageTemplate.put(fileStoragePut -> {
            // 确定好文件的位置
            String lf = fileStoragePut.getRootPath()
                    + fileName
                    + ".zip";
            File f = new File(lf);
            try {
                FileUtils.copyFile(file, f);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }
            File parent = f.getParentFile();
            if (parent != null && !parent.mkdirs() && !parent.isDirectory()) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "生成" + lf + "报错：无法创建文件");
            }
            fileStoragePut.setAutoExpire(true);
            fileStoragePut.setLocalFile(f);
        });
    }

}
