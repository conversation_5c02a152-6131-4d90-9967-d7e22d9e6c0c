package com.dongni.common.report.pdf.service;


import com.alibaba.fastjson.JSON;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.report.monitor.service.ExamStudentCourseStatReportService;
import com.dongni.analysis.report.monitor.service.ExamStudentStatReportService;
import com.dongni.analysis.view.monitor.service.ExamStudentCourseStatService;
import com.dongni.analysis.view.monitor.service.ExamStudentPaperStatService;
import com.dongni.analysis.view.monitor.service.ExamStudentStatService;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.pdf.utils.CreateWordDj4Subject;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.dispiay.service.DisplayExamService;

import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.own.service.OwnPaperService;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @description
 * @date 2020/04/17 09:42
 */
@Service
public class SubjectWordService {
    private static final Logger log = LogManager.getLogger(SubjectWordService.class);

    @Autowired
    private ExamStudentPaperStatService examStudentPaperStatService;
    @Autowired
    private ExamStudentCourseStatService examStudentCourseStatService;
    @Autowired
    private ExamStudentStatService examStudentStatService;
    @Autowired
    private ExamConfigService examConfigService;
    @Autowired
    private ExamStudentCourseStatReportService examStudentCourseStatReportService;
    @Autowired
    private MergeService mergeService;
    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private ExamStudentStatReportService studentExcelService;
    @Autowired
    private DisplayExamService displayExamService;
    @Autowired
    private CreateScorePdfService createScorePdfService;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private BaseDataRepository baseDataRepository;
    @Autowired
    private CommonCourseService commonCourseService;

    public boolean isRanking(Map<String, Object> params) {
        try {
            Document examConfig = displayExamService.getExamConfig(params);
            if (MapUtils.isEmpty(examConfig)) {
                return true;
            }
            Integer userType = Integer.valueOf(params.get("userType").toString());
            List<Map<String, Object>> displayExam = (List<Map<String, Object>>) examConfig.get("displayExam");
            Map<String, Object> display = displayExam.stream()
                    .filter(item -> userType == Integer.valueOf(item.get("userType").toString())).collect(toList()).get(0);
            Integer gradeRankingStatus = Integer.valueOf(display.get("gradeRankingStatus").toString());
            if (gradeRankingStatus == 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("权限校验错误");
            return true;
        }

    }

    /**
     * 生成pdf，返回路径
     */
    public String toPdf(Map<String, Object> params){
        return FileStorageTemplate.put(fileStoragePut -> {
            File file = new File(fileStoragePut.getRootPath() + System.currentTimeMillis() +  "成绩单.pdf");
            getPdf(params,file);
            fileStoragePut.setLocalFile(file);
            fileStoragePut.setAutoExpire(true);
        });
    }

    /**
     * 生成pdf
     */
    public void getPdf(Map<String, Object> params,File file){
        //判断是总分还是单科 为true则是总分
        boolean courseFlag = ObjectUtil.isBlank(params.get("courseId"));
        boolean displayExamRanking = MapUtils.getBoolean(params, "displayExamRanking", true);
        boolean displayClassRanking = MapUtils.getBoolean(params, "displayClassRanking", true);
        boolean displayStudentScore = MapUtils.getBoolean(params, "displayStudentScore", true);
        Map<String, Object> configDisplay = displayExamService.getExamRankingDisplay(params);
        boolean showExamRanking = displayExamRanking && MapUtil.getBoolean(configDisplay, "isDisplayExamRanking", true);
        boolean showClassRanking = displayClassRanking && MapUtil.getBoolean(configDisplay, "isDisplayClassRanking", true);

        Map<String, Object> data;
        FileOutputStream fileOutputStream;

        //表格内容对应的字段名
        List<String> fields;
        //表头名称
        List<String> columnHeaders = new ArrayList<>();
        CreateWordDj4Subject createWordDj4Subject = new CreateWordDj4Subject();
        if (courseFlag) {
            //总分的成绩单
            data = getTranscriptTotal(params);
            if (ObjectUtil.isNotEmptyCollections(data)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
            }
            data.put("showExamRanking", showExamRanking);
            data.put("showClassRanking", showClassRanking);
            data.put("showStudentScore", displayStudentScore);
            data.put("examName", params.get("examName"));
            titleUtil(params, data);

            List<Map<String, Object>> students = (List<Map<String, Object>>) data.get("student");
            if (CollectionUtils.isEmpty(students)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "无导出数据");
            }
            // 设置标题
            String classFlag = data.get("class").toString();
            String title = "总分成绩单 —— ";
            if ("y".equals(classFlag)) {
                String className = students.get(0).get("className").toString();
                title = title + className;
            } else {
                title = title + "全年级";
            }
            data.put("title",title);

            //设置前四列的表头、表格内容对应的字段名
            fields = createWordDj4Subject.getKeyListTotal(showExamRanking, showClassRanking, displayStudentScore);
            columnHeaders.add("姓名");
            columnHeaders.add("班级");
            if (displayStudentScore) {
                columnHeaders.add("总分");
            }
            if (showExamRanking) {
                columnHeaders.add("年级排名");
            }
            if (showClassRanking) {
                columnHeaders.add("班级排名");
            }
            data.put("fields",fields);
            data.put("columnHeaders",columnHeaders);

            //获取课程的排序
            List<Map<String,Object>> examCourse = examRepository.selectList("ExamMapper.getExamCourse",params);
            List<Map<String, Object>> courseInfos = baseDataRepository.selectList("CourseMapper.getCourseList", MapUtil.of("courseId",
              commonCourseService.getCourseIdsByCourseIds(examCourse.stream().map(x -> MapUtils.getLong(x, "courseId")).collect(toList()))));
            data.put("courseInfos", courseInfos);

            //生成文件
            try {
                fileOutputStream = new FileOutputStream(file);
                createScorePdfService.export2OutputStreamWithContentTotal(data,fileOutputStream,showExamRanking, showClassRanking, displayStudentScore);
            } catch (FileNotFoundException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "写文件异常", e);
            }
        } else {
            //单科的成绩单
            data = getTranscriptSingle(params);
            if (ObjectUtil.isNotEmptyCollections(data)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
            }
            data.put("showExamRanking", showExamRanking);
            data.put("showClassRanking", showClassRanking);
            data.put("showStudentScore", displayStudentScore);
            data.put("examName", params.get("examName"));
            titleUtil(params, data);

            List<Map<String, Object>> students = (List<Map<String, Object>>) data.get("student");
            if (CollectionUtils.isEmpty(students)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "无导出数据");
            }

            // 设置标题
            String classFlag = data.get("class").toString();
            String title = students.get(0).get("courseName").toString() + "成绩单 —— ";
            if ("y".equals(classFlag)) {
                String className = students.get(0).get("className").toString();
                title = title + className;
            } else {
                title = title + "全年级";
            }
            data.put("title",title);

            //设置表格的表头、表格内容对应的字段名
            Long paperId = MapUtils.getLong(params, "paperId");
            boolean showCorrectRate = examConfigService.showCorrectRate(params);
            fields = createWordDj4Subject.getKeyListSingle(students, showCorrectRate,
              PaperUtil.getPaperStructure(ownPaperService.getPaperDetail(MapUtil.of("paperId", paperId))).size(),
                    showExamRanking, showClassRanking, displayStudentScore);
            columnHeaders.add("姓名");
            columnHeaders.add("班级");
            if (displayStudentScore) {
                columnHeaders.add(showCorrectRate ? "正确率" : "总分");
            }
            if (showExamRanking) {
                columnHeaders.add("年级排名");
            }
            if (showClassRanking) {
                columnHeaders.add("班级排名");
            }
            int headerSize = columnHeaders.size();
            //小题题号
            for(int i = headerSize; i < fields.size() ; i++){
                columnHeaders.add(fields.get(i));
            }
            data.put("fields",fields.toArray(new String[fields.size()]));
            data.put("columnHeaders",columnHeaders.toArray(new String[columnHeaders.size()]));

            //生成文件
            try {
                fileOutputStream = new FileOutputStream(file);
                createScorePdfService.export2OutputStreamWithContentSingle(data,showCorrectRate,fileOutputStream,headerSize);
            } catch (FileNotFoundException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "写文件异常", e);
            }
        }
    }

    public String toWordAll(Map<String, Object> params) {
        boolean courseFlag = ObjectUtil.isBlank(params.get("courseId"));
        boolean isRanking = isRanking(params);
        String wordUrl = "";
        Map<String, Object> data;
        // 空 说明差的总成绩
        if (courseFlag) {
            data = getTranscriptTotal(params);
            // 是否年级排名
            data.put("isRanking", isRanking);
            if (ObjectUtil.isNotEmptyCollections(data)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
            }
            data.put("examName", params.get("examName"));
            titleUtil(params, data);
            CreateWordDj4Subject createWordDj4Subject = new CreateWordDj4Subject();
            try {
                wordUrl = createWordDj4Subject.startToWord(data);
            } catch (Exception e) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, e.getMessage());
            }
        } else {
            data = getTranscriptSingle(params);
            if (ObjectUtil.isNotEmptyCollections(data)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
            }
            // 是否年级排名
            data.put("isRanking", isRanking);
            // 考试名
            data.put("examName", params.get("examName"));
            CreateWordDj4Subject createWordDj4Subject = new CreateWordDj4Subject();
            try {
//            List<Map<String, Object>> student = (List<Map<String, Object>>) transcript.get("student");
                data.put("questionCount", PaperUtil.getPaperStructure(
                  ownPaperService.getPaperDetail(MapUtil.of("paperId", MapUtils.getLong(params, "paperId")))).size());
                wordUrl = createWordDj4Subject.startToWord(data);
            } catch (Exception e) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, e.getMessage());
            }
        }
        // 入库 t_word_download
        Long wordDownloadId = null;
        Map<String, Object> wordParams = new HashMap<>();
        wordParams.put("wordDownloadId", wordDownloadId);
        wordParams.put("wordReportStatus", DictUtil.getDictValue("wordReportStatus", "convert"));
        wordParams.put("areaId", 0);
        wordParams.put("examId", params.get("examId"));
        wordParams.put("wordReportName", "成绩单");
        wordParams.put("wordReportJson", JSON.toJSONString(data));
        wordParams.put("userId", params.get("userId"));
        wordParams.put("userName", params.get("userName"));
        wordParams.put("currentTime", DateUtil.getCurrentDateTime());
        wordParams.put("address", wordUrl);
        examRepository.insert("WordReportExportMapper.insertReturnId", wordParams);
        boolean flagPdfExist = true;
        String pdfUrl = "";
        byte loopNum = 0;
        while (flagPdfExist) {
            Map<String, Object> flagPdfExistMap = new HashMap<>();
            flagPdfExistMap.put("wordDownloadId", wordParams.get("wordDownloadId"));
            // 这里可以优化为查缓存
            Map<String, Object> pdfExistObject = examRepository.selectOne("WordReportExportMapper.getFileStatus", flagPdfExistMap);
            if (MapUtils.isNotEmpty(pdfExistObject)) {
                int wordReportStatus = Integer.parseInt(String.valueOf(pdfExistObject.get("wordReportStatus")));
                if (wordReportStatus == 1) { // 已完成
                    flagPdfExist = false;
                    pdfUrl = pdfExistObject.get("url").toString();
                } else if (wordReportStatus == 2) { // 出错了
                    break;
                } else {
                    try {
                        Thread.sleep(5000L);
                    } catch (InterruptedException e) {
                        LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                    }
                }
            }
            if (loopNum > 20) {
                break;
            }
            loopNum++;
        }
        if (StringUtils.isBlank(pdfUrl)) {
            if (loopNum > 20) {
                // 更新数据库,下次不再转化了  2
                if (!ObjectUtil.isBlank(wordParams.get("wordDownloadId"))) {
                    wordParams.put("wordReportStatus", DictUtil.getDictValue("wordReportStatus", "fail"));
                    examRepository.update("WordReportExportMapper.updateWordDownload", wordParams);
                    throw new CommonException(ResponseStatusEnum.GET_FILE_INPUT_STREAM_ERROR, "当前pdf服务器繁忙，请稍后重试");
                }
            }
            throw new CommonException(ResponseStatusEnum.GET_FILE_INPUT_STREAM_ERROR, "网络错误，请重试");
        }
        return pdfUrl;
    }

    /**
     * 总分成绩单
     *
     * @param params
     * @return
     */
    private Map<String, Object> getTranscriptTotal(Map<String, Object> params) {

        //  综合课程
        params.remove("pageSize");
        params.remove("pageNo");
        Map<String, Object> result = new HashMap<>();
        List examStudentDetail = examStudentStatService.getExamStudentDetailForDpf(params);
        if (CollectionUtils.isEmpty(examStudentDetail)) {
            return new HashMap<>();
        }
        List<Document> documents = displayExamService.filterResultListMap(examStudentDetail, params);
        // 分数格式化
        for (Document item : documents) {
            Object allTotalScore = item.get("totalScore");
            if (allTotalScore != null) {
                item.put("totalScore", ExcelFormatUtil.forMatScore(allTotalScore));
            }
            // courseDetail这个是不为null的
            List<Document> courseDetail = MapUtil.getCast(item, "courseDetail");
            for (Document course : courseDetail) {
                Object courseTotalScore = course.get("totalScore");
                if (courseTotalScore != null) {
                    course.put("totalScore", ExcelFormatUtil.forMatScore(courseTotalScore));
                }
            }
        }

        result.put("student", documents);
        return result;
    }

    /**
     * 标题要根据不同的查询条件变化
     *
     * @param params
     * @param result
     */
    private void titleUtil(Map<String, Object> params, Map<String, Object> result) {
        // 如果courseId为空， 查的是所有科目
        Object courseId = params.get("courseId");
        if (ObjectUtil.isBlank(courseId)) {
            result.put("course", "n");
        } else {
            result.put("course", "y");
        }
        // 如果classId为空，查的是所有班级
        Object classId = params.get("classId");
        if (ObjectUtil.isBlank(classId)) {
            result.put("class", "n");
        } else {
            result.put("class", "y");
        }
    }


    /**
     * 单科成绩单
     *
     * @param params
     * @return
     */
    private Map<String, Object> getTranscriptSingle(Map<String, Object> params) {
        Map<String, Object> question = examStudentPaperStatService.getQuestion(params);
        Map<String, Object> result = examStudentCourseStatService.getResult(params);
        titleUtil(params, question);
        if (!question.isEmpty() && !result.isEmpty()) {
            List<Map<String, Object>> questionStudent = (List<Map<String, Object>>) question.getOrDefault("student",new ArrayList<>());
            List<Map<String, Object>> resultStudent = (List<Map<String, Object>>) result.getOrDefault("student",new ArrayList<>());

            for (int i = 0; i < questionStudent.size(); i++) {
                Map<String, Object> questionMap = questionStudent.get(i);
                Map<String, Object> resultMap = resultStudent.get(i);
                // 总分， 总排名
                Object examRanking = resultMap.get("examRanking");
                Object classRanking = resultMap.get("classRanking");
                Object totalScore = resultMap.get("totalScore");
                if (ObjectUtil.isNotBlank(examRanking)) {
                    questionMap.put("examRanking", examRanking);
                    questionMap.put("correctRateRanking", resultMap.get("correctRateRanking"));
                }
                if (ObjectUtil.isNotBlank(classRanking)) {
                    questionMap.put("classRanking", classRanking);
                    questionMap.put("correctRateClassRanking", resultMap.get("correctRateClassRanking"));
                }
                // 缺考考生没有totalScore
                if (ObjectUtil.isBlank(totalScore)) {
                    questionMap.put("totalScore", "缺");
                } else {
                    questionMap.put("totalScore", ExcelFormatUtil.forMatScore(totalScore));
                }
            }
        }
        return question;
    }


    /**
     * 合并生成pdf，word，excel, zip为一个接口
     * 异步生成word和excel 后再合并为zip
     *
     * @param params
     * @return
     */
    public String wordAndExcelToZip(Map<String, Object> params) {
        log.info("异步生成word和excel的zip文件param:" + JSON.toJSON(params));
        // 异步
        // countDownLatch 可以协调线程， 等待所有线程完成后再向下执行
        final CountDownLatch latch = new CountDownLatch(2);
        ExecutorService threadPool = Executors.newFixedThreadPool(2);
        List<Future<String>> futureTaskList = new ArrayList<Future<String>>(2);
        try {
            futureTaskList.add(threadPool.submit(() -> {
                latch.countDown();
                boolean courseFlag = ObjectUtil.isBlank(params.get("courseId"));
                if (courseFlag) {
                    // /analysis/report/monitor/exam/student/union/result
                    return studentExcelService.getUnionResult(params);
                } else {
                    // /analysis/report/monitor/exam/student/course/result
                    return examStudentCourseStatReportService.getResult(params).exportToFileStorage("成绩单");
                }
            }));
            futureTaskList.add(threadPool.submit(() -> {
                latch.countDown();
                return toPdf(params);
            }));
            latch.await(120, TimeUnit.SECONDS);
            StringBuilder urls = new StringBuilder();
            for (Future<String> future : futureTaskList) {
                if (urls.length() > 0) {
                    urls.append(",");
                }
                urls.append(future.get());
            }
            Map<String, Object> zipParam = new HashMap<>();
            zipParam.put("urlList", urls);
            zipParam.put("fileName", "成绩单");
            return mergeService.mergeToZIP(zipParam);
        } catch (Exception e) {
            throw new CommonException(ResponseStatusEnum.FILE_ERROR, "error:" + e.getMessage());
        } finally {
            threadPool.shutdown();
        }
    }
}
