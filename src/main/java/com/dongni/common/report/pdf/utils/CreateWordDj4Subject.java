package com.dongni.common.report.pdf.utils;

import com.dongni.common.report.word.DocxFontEnum;
import com.dongni.common.report.word.ExportWord;
import com.dongni.common.report.word.WordUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.file.DocxUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;

import java.awt.event.KeyListener;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;

import java.math.BigInteger;
import java.util.*;


/**
 * <AUTHOR>
 * @description
 * @date 2020/04/16 17:48
 */
public class CreateWordDj4Subject extends ExportWord {


    /**
     * 总分成绩单
     *
     * @param transcript
     * @return
     * @throws Exception
     */
    public String startToWord(Map<String, Object> transcript) throws Exception {
        MainDocumentPart mainDocumentPart = wordPackage.getMainDocumentPart();
        List<Map<String, Object>> params = (List<Map<String, Object>>) transcript.get("student");
        if (CollectionUtils.isEmpty(params)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "无导出数据");
        }
        // 设置标题
        String examName = transcript.get("examName").toString();
        String classFlag = transcript.get("class").toString();
        String courseFlag = transcript.get("course").toString();
        Boolean isRanking = (Boolean) transcript.get("isRanking");
        String title = setTitle(examName, classFlag, courseFlag, params, mainDocumentPart);

        if (title.contains("总")) {
            for (Map<String, Object> param : params) {
                // 添加字段
                Tbl table = createTableWithContentTotal(getKeyListTotal(true, null, true), param, isRanking);
                // 添加边框
                addBorders(table);
                mainDocumentPart.addObject(table);
//                mainDocumentPart.addParagraphOfText(" ");
                P p1 = WordUtil.addParagraphText(factory, " ",
                        WordUtil.getRPr(factory, DocxFontEnum.TWO_STRONG));
                WordUtil.setParagraphSpacing(factory, p1, JcEnumeration.CENTER, true, "0",
                        "0", null, null, true, "200", STLineSpacingRule.AUTO);
                mainDocumentPart.addObject(p1);

            }
        } else {
            List<String> keyList = getKeyListSingle(params, false, MapUtils.getInteger(transcript, "questionCount"),
                    true, true, true);
            for (Map<String, Object> param : params) {
                // 添加字段
                Tbl table = createTableWithContentSingle(keyList, param, isRanking);
                // 添加边框
                addBorders(table);
                mainDocumentPart.addObject(table);
//                mainDocumentPart.addParagraphOfText(" ");
                P p1 = WordUtil.addParagraphText(factory, " ",
                        WordUtil.getRPr(factory, DocxFontEnum.TWO_STRONG));
                WordUtil.setParagraphSpacing(factory, p1, JcEnumeration.CENTER, true, "0",
                        "0", null, null, true, "200", STLineSpacingRule.AUTO);
                mainDocumentPart.addObject(p1);
            }
        }

        // 添加页码
        this.addPageFooter();

        return export("成绩单");
    }

    /**
     * 设置标题
     *
     * @param
     * @return
     */
    public String setTitle(String examName,
                           String classFlag,
                           String courseFlag,
                           List<Map<String, Object>> params,
                           MainDocumentPart mainDocumentPart) {
        if ("y".equals(courseFlag)) {
            String courseName = params.get(0).get("courseName").toString();
            examName = courseName + "成绩单--";
        } else {
            examName = "总分成绩单--";
        }
        if ("y".equals(classFlag)) {
            String className = params.get(0).get("className").toString();
            examName = examName + className;
        } else {
            examName = examName + "全年级";
        }
        // 页边距
        setDocMarginSpace(wordPackage, factory, "306", "329", "306", "329");
        P p1 = WordUtil.addParagraphText(factory, examName,
                WordUtil.getRPr(factory, DocxFontEnum.TWO_STRONG));
        WordUtil.setParagraphSpacing(factory, p1, JcEnumeration.CENTER, true, "0",
                "0", null, null, true, "240", STLineSpacingRule.AUTO);
        DocxUtil.setParagrahHorizontallyAlign(p1, JcEnumeration.CENTER);
        mainDocumentPart.addObject(p1);
        WordUtil.addBreak(wordPackage, factory);
//        WordUtil.addBreak(wordPackage, factory);
        return examName;
    }

    /**
     * @Description：设置页边距
     */
    public void setDocMarginSpace(WordprocessingMLPackage wordPackage,
                                  ObjectFactory factory, String top, String left, String bottom,
                                  String right) {
        SectPr sectPr = getDocSectPr(wordPackage);
        SectPr.PgMar pg = sectPr.getPgMar();
        if (pg == null) {
            pg = factory.createSectPrPgMar();
            sectPr.setPgMar(pg);
        }
        if (StringUtils.isNotBlank(top)) {
            pg.setTop(new BigInteger(top));
        }
        if (StringUtils.isNotBlank(bottom)) {
            pg.setBottom(new BigInteger(bottom));
        }
        if (StringUtils.isNotBlank(left)) {
            pg.setLeft(new BigInteger(left));
        }
        if (StringUtils.isNotBlank(right)) {
            pg.setRight(new BigInteger(right));
        }
    }

    /**
     * @Description：设置页边距和页面大小
     */
    public SectPr setDocMarginSpacAndSize(
                                  SectPr sectPr,
                                  ObjectFactory factory, String top, String left, String bottom,
                                  String right,String header,String footer,String w,String h) {
        if(sectPr == null){
            sectPr = factory.createSectPr();
        }

        SectPr.PgMar pg = sectPr.getPgMar();
        if (pg == null) {
            pg = factory.createSectPrPgMar();
            sectPr.setPgMar(pg);
        }
        if (StringUtils.isNotBlank(top)) {
            pg.setTop(new BigInteger(top));
        }
        if (StringUtils.isNotBlank(bottom)) {
            pg.setBottom(new BigInteger(bottom));
        }
        if (StringUtils.isNotBlank(left)) {
            pg.setLeft(new BigInteger(left));
        }
        if (StringUtils.isNotBlank(right)) {
            pg.setRight(new BigInteger(right));
        }
        if (StringUtils.isNotBlank(header)) {
            pg.setHeader(new BigInteger(header));
        }

        if (StringUtils.isNotBlank(footer)) {
            pg.setFooter(new BigInteger(footer));
        }
        SectPr.PgSz pgSz = sectPr.getPgSz();
        if(pgSz == null){
            pgSz = factory.createSectPrPgSz();
            sectPr.setPgSz(pgSz);
        }
        if (StringUtils.isNotBlank(w)) {
            pgSz.setW(new BigInteger(w));
        }
        if (StringUtils.isNotBlank(h)) {
            pgSz.setH(new BigInteger(h));
        }

        return sectPr;
    }





    public SectPr getDocSectPr(WordprocessingMLPackage wordPackage) {
        return wordPackage.getDocumentModel().getSections().get(0)
                .getSectPr();
    }


    private void addTableCell(Tr tableRow, String content, boolean flag) {

        Tc tableCell = factory.createTc();
        if ("studentName".equals(content)) {
            content = new String("姓名");
        } else if ("className".equals(content)) {
            content = new String("班级");
        } else if ("examRanking".equals(content)) {
            // 年级排名
            content = new String("排名");
        } else if ("totalScore".equals(content)) {
            content = new String("总分");
        }
        // 创建一个段落插入到表格中
        P p1 = WordUtil.addParagraphText(factory, content,
                WordUtil.getRPr(factory, DocxFontEnum.SIX));
        WordUtil.setParagraphSpacing(factory, p1, JcEnumeration.CENTER, true, "0",
                "0", null, null, true, "240", STLineSpacingRule.AUTO);
        DocxUtil.setParagrahHorizontallyAlign(p1, JcEnumeration.CENTER);
        tableCell.getContent().add(p1);

        if (flag) {
            tableCell.setTcPr(new TcPr());
            CTShd shd = new CTShd();
            shd.setVal(STShd.CLEAR);
            shd.setColor("auto");
            shd.setFill("d0d0d0");
            tableCell.getTcPr().setShd(shd);
        }
//        setCellWidth(tableCell, 376);

        tableRow.getContent().add(tableCell);
    }

    /**
     * 加边框
     *
     * @param table
     */
    private void addBorders(Tbl table) {
        table.setTblPr(new TblPr());
        CTBorder border = new CTBorder();
        border.setColor("auto");
        border.setSz(new BigInteger("4"));
        border.setSpace(new BigInteger("0"));
        border.setVal(STBorder.SINGLE);

        TblBorders borders = new TblBorders();
        borders.setBottom(border);
        borders.setLeft(border);
        borders.setRight(border);
        borders.setTop(border);
        borders.setInsideH(border);
        borders.setInsideV(border);
        table.getTblPr().setTblBorders(borders);
    }


    private Tbl createTableWithContentTotal(List<String> keyList,
                                            Map<String, Object> student,
                                            Boolean isRanking) throws Exception {
        // 记录下当前第几个key
        int currentKey = 0;
        //行最多24 个字段
        int column = 12;
        // 默认一行
        int row = 1;
        int keySize = keyList.size();
        List<Map<String, Object>> courses =sortCourse((List<Map<String, Object>>) student.get("courseDetail"));
//        List<Map<String, Object>> list = sortCourse(courses);
        int coursesSize = courses.size();


        int mo = (coursesSize * 2) / (column - 4);
        int yu = (coursesSize * 2) % (column - 4);


        if (coursesSize <= 4) {
            column = keySize + coursesSize * 2;
        } else {
            if (mo > 0 && yu > 0) {
                row = mo + 1;
            } else if (yu == 0 && mo > 0) {
                row = mo;
            }
        }
        Tbl table = DocxUtil.createTable(wordPackage, 0, column);
        // 行
        for (int k = 0; k < row; k++) {
            Tr tableRow1 = factory.createTr();
            Tr tableRow2 = factory.createTr();
            // 循环列
            int c = 0;
            for (int i = 0; i < 4; i++) {
                c = i;
                String keyString = "";
                String valueString = "";
                if (k == 0) {
                    // 前面四个， 姓名 班级 排名 总分
                    keyString = keyList.get(i);
                    valueString = absence(student.get(keyString));
                    if (!isRanking && "examRanking".equals(keyString) && !"缺".equals(valueString)) {
                        valueString = "*";
                    }
                    if ("totalScore".equals(keyString) && !"缺".equals(valueString)) {
                        valueString = WordUtil.formatScore(valueString).toString();
                    }
                    if (i == 3) {
                        if ("缺".equals(absence(student.get(keyList.get(i - 1))))) {
                            valueString = "缺";
                        }
                    }
                }
                addTableCell(tableRow1, keyString, false);
                addTableCell(tableRow2, valueString, false);
            }
            // 循环所有课程
            for (int j = c + 1; j < column; j = j + 2) {
                if (currentKey < coursesSize) {
                    Map<String, Object> course = courses.get(currentKey);
                    // 课程
                    String keyStringCourse = course.get("courseName").toString();
                    String valueStringCourse = absence(course.get("totalScore"));

                    if (!"缺".equals(valueStringCourse)) {
                        valueStringCourse = WordUtil.formatScore(valueStringCourse).toString();
                    }
                    if (!isRanking && "examRanking".equals(keyStringCourse) && !"缺".equals(valueStringCourse)) {
                        valueStringCourse = "*";
                    }
                    addTableCell(tableRow1, keyStringCourse, false);
                    addTableCell(tableRow2, valueStringCourse, false);
                    // 年级排名
                    String valueStringRank = absence(course.get("examRanking"));
                    if (!isRanking && !"缺".equals(valueStringRank)) {
                        valueStringRank = "*";
                    }
                    addTableCell(tableRow1, "排名", false);
                    addTableCell(tableRow2, valueStringRank, false);
                    currentKey++;
                } else {
                    addTableCell(tableRow1, "", false);
                    addTableCell(tableRow2, "", false);
                    addTableCell(tableRow1, "", false);
                    addTableCell(tableRow2, "", false);
                }
            }
            table.getContent().add(tableRow1);
            table.getContent().add(tableRow2);
        }
        return table;

    }


    /**
     * 内容
     *
     * @return
     */
    private Tbl createTableWithContentSingle(List<String> keyList,
                                             Map<String, Object> student,
                                             Boolean isRanking) throws Exception {
//        Tbl table = factory.createTbl();

        // 用于标识缺考条件下， 不再显示小题缺考和 小题显示
        boolean breakFlag = false;

        // 记录下当前第几个key
        int currentKey = 0;
        //行最多24 个字段
        int column = 14;
        // 默认一行
        int row = 1;
        int keySize = keyList.size();
        int mo = (keySize - 4) / (column - 4);
        int yu = (keySize - 4) % (column - 4);

        Tbl table = DocxUtil.createTable(wordPackage, 0, column);

        if (mo > 0 && yu > 0) {
            row = mo + 1;
        } else if (yu == 0 && mo > 0) {
            row = mo;
        }
        // 循环行
        forColumn:
        for (int k = 0; k < row; k++) {
            Tr tableRow1 = factory.createTr();
            Tr tableRow2 = factory.createTr();
            // 循环列
            for (int i = 0; i < column; i++) {
                if (currentKey > keySize - 1) {
                    addTableCell(tableRow1, "", true);
                    addTableCell(tableRow2, "", false);
                    continue;
                }
                if (i < 4 && k > 0) {
                    addTableCell(tableRow1, "", true);
                    addTableCell(tableRow2, "", false);
                } else if (k == 0 && i < 4) {
                    // 姓名 班级 排名 总分
                    String key = keyList.get(currentKey);
                    String value = absence(student.get(key));
                    if ("examRanking".equals(key) && !isRanking && !"缺".equals(value)) {
                        value = "*";
                    }
                    if ("totalScore".equals(key) && !"缺".equals(value)) {
                        value = WordUtil.formatScore(value).toString();
                    }
                    addTableCell(tableRow1, key, true);
                    addTableCell(tableRow2, value, false);
                    // 如果到总分了， 上次循环currentKey -1 的value是不是缺考。 如果是缺考则不现实后面的题目了
                    if (i == 2) {
                        String breakKey = keyList.get(currentKey);
                        String breakValue = absence(student.get(breakKey));
                        if ("缺".equals(breakValue)) {
                            breakFlag = true;
                        }
                    }
                    currentKey++;
                } else {
                    if (breakFlag) {
                        addTableCell(tableRow1, "", true);
                        addTableCell(tableRow2, "", false);
                    } else {
                        String key = keyList.get(currentKey);
                        List<Map<String, Object>> questionList = (List<Map<String, Object>>) student.get("questions");
                        if (questionList == null || questionList.size() == 0) {
                            addTableCell(tableRow1, key, true);
                            addTableCell(tableRow2, "缺", false);
                        } else {
                            String finallyScore = questionList.get(currentKey - 4).get("finallyScore").toString();

                            addTableCell(tableRow1, key, true);
                            addTableCell(tableRow2, WordUtil.formatScore(finallyScore).toString(), false);
                        }
                    }
                    currentKey++;
                }
            }
            table.getContent().add(tableRow1);
            table.getContent().add(tableRow2);
            if (breakFlag) {
                break;
            }
        }
        return table;
    }


    public List<String> getKeyListSingle(List<Map<String, Object>> param, boolean showCorrectRate, int questionCount,
                                         Boolean showExamRanking, Boolean showClassRanking, Boolean displayStudentScore) {
        List<String> keyList = new ArrayList<>();
        // 姓名
        keyList.add("studentName");
        // 班级
        keyList.add("className");
        // 总分
        if (displayStudentScore) {
            keyList.add(showCorrectRate ? "correctRate" : "totalScore");
        }
        if (showExamRanking != null && showExamRanking) {
            // 年级排名
            keyList.add(showCorrectRate ? "correctRateRanking" : "examRanking");
        }
        if (showClassRanking != null && showClassRanking) {
            // 班级排名
            keyList.add(showCorrectRate ? "correctRateClassRanking" : "classRanking");
        }

        TreeMap<Integer, String> questionNumberMap = new TreeMap<>(Integer::compareTo);
        Map<Integer, List<String>> pointMap = new HashMap<>();
        List<Map<String, Object>> attend = param.stream()
          .filter(x -> "0".equals(x.getOrDefault("resultStatus", 1).toString())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attend)) {
            attend.forEach(student -> {
                MapUtil.getListMap(student, "questions").forEach(q -> {
                    Integer questionNumber = MapUtils.getInteger(q, "questionNumber");
                    if (!questionNumberMap.containsKey(questionNumber)) {
                        questionNumberMap.put(questionNumber, questionNumber + "-" + q.get("structureNumber"));
                        if (q.get("point") != null) {
                            pointMap.put(questionNumber, (MapUtil.getListMap(q, "point")).stream()
                              .map(p -> questionNumber + "-" + p.get("structureNumber")).collect(Collectors.toList()));
                        }
                    }
                });
                if (questionNumberMap.size() >= questionCount) {
                    return;
                }
            });

            questionNumberMap.forEach((k, v) -> {
                keyList.add(v);
                if (pointMap.containsKey(k)) {
                    keyList.addAll(pointMap.get(k));
                }
            });
        }

        return keyList;
    }

    public List<String> getKeyListTotal(Boolean showExamRanking, Boolean showClassRanking, Boolean displayStudentScore) {
        List<String> keyList = new ArrayList<>();
        // 姓名
        keyList.add("studentName");
        // 班级
        keyList.add("className");
        // 总分
        if (displayStudentScore) {
            keyList.add("totalScore");
        }
        if (showExamRanking != null && showExamRanking) {
            // 年级排名
            keyList.add("examRanking");
        }
        if (showClassRanking != null && showClassRanking) {
            // 班级排名
            keyList.add("classRanking");
        }
        return keyList;
    }

    // 如果分数为空， 那么就是缺考
    public String absence(Object score) {
        if (ObjectUtil.isBlank(score)) {
            return "缺";
        }
        return score.toString();
    }


    /**
     * 对 course排序
     *
     * @param params
     * @return
     */
    public List<Map<String, Object>> sortCourse(List<Map<String, Object>> params) {
        List<String> subjectList = Arrays.asList("语文", "数学", "英语", "物理", "化学", "生物", "理综", "政治", "历史", "地理", "文综");
        List<Map<String, Object>> result1 = new ArrayList<>();

        subjectList.forEach(s -> {
            // 如果有这个key， 就put、进去  如果没有，就下一个
            if (!CollectionUtils.isEmpty(params)) {
                ListIterator<Map<String, Object>> mapListIterator = params.listIterator();
                while (mapListIterator.hasNext()) {
                    Map<String, Object> e = mapListIterator.next();
                    if (e.get("courseName").toString().equals(s)) {
                        result1.add(e);
                        mapListIterator.remove();
                    }
                }
            }
        });
        // 得到那些已经有了的key  吧没有的key put 进来
        if (CollectionUtils.isEmpty(params)) {
            return result1;
        }
        result1.addAll(params);
        return result1;
    }



}
