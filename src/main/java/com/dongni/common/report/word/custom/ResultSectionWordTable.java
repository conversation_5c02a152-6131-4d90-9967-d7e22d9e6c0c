package com.dongni.common.report.word.custom;

import com.dongni.common.report.word.SimpleWordTable;
import com.dongni.common.report.word.WordUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.commons.collections.MapUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.wml.Tr;

import java.util.*;

/**
 * Created by <PERSON>wei<PERSON> on 2019/9/3.
 *
 * 各校成绩分布自定义表
 */
public class ResultSectionWordTable extends SimpleWordTable {

    private int avgColumn = 8;

    public ResultSectionWordTable(WordprocessingMLPackage wordPackage, List<String> title){
        super(wordPackage,title);
        setColWidthPercent(new double[]{12.5,12.5,12.5,12.5,12.5,12.5,12.5,12.5});
    }

    /**
     * 自定义表头
     */
    @Override
    public void generateTitle() {
        // do nothing
    }

    /**
     * 自定义表体
     *
     * @param key
     * @param body
     */
    @Override
    public void generateBody(List<String> key, List<Map<String, Object>> body) {

        // 表头确定
        List<Map<String,Object>> sec = (List) body.get(0).get("scoreSection");
        List<List<Map<String,Object>>> tts = new ArrayList<>();
        List<Map<String,Object>> tt = null;

        sec.sort(Comparator.comparing(s->Double.valueOf(s.get("open").toString())));
        Collections.reverse(sec);
        for (int i = 0; i < sec.size(); i++){
            Map<String,Object> s = sec.get(i);
            if(i % 4 == 0){
                tt = new ArrayList<>(8);
                Map<String,Object> t1 = new HashMap<>();
                t1.put("title","上级对象");
                t1.put("key","parentName");
                t1.put("col",0);
                tt.add(t1);

                Map<String,Object> t2 = new HashMap<>();
                t2.put("title","当前对象");
                t2.put("key","currentName");
                t2.put("col",1);
                tt.add(t2);

                Map<String,Object> t3 = new HashMap<>();
                t3.put("title","类别");
                t3.put("key","schoolPropertyName");
                t3.put("col",2);
                tt.add(t3);

                Map<String,Object> t4 = new HashMap<>();
                t4.put("title","实参人数");
                t4.put("key","participationNumber");
                t4.put("col",3);
                tt.add(t4);

                Map<String,Object> t5 = new HashMap<>();
                t5.put("title","指标");
                t5.put("key","本段|比例|累计");
                tt.add(t5);
                tts.add(tt);
            }

            Map<String,Object> t6 = new HashMap<>();
            t6.put("title",getValue(s));
            t6.put("key",i);
            tt.add(t6);
        }

        // 表头表体
        int index = -1;
        for (int i = 0 ; i < tts.size(); i++){

            // 表头
            List<Map<String,Object>> t = tts.get(i);
            Tr firstTr = factory.createTr();
            index++;
            setTableTrHeight(firstTr, titleHeight);
            table.getContent().add(firstTr);
            for (Map<String,Object> c : t) {
                createTitleCell(firstTr,c.get("title").toString());
            }

            // 表体
            for (int j = 0; j < body.size(); j++){
                Map<String,Object> school = body.get(j);
                List<Map<String,Object>> section = (List) school.get("scoreSection");
                if(section == null){
                    continue;
                }

                section.sort(Comparator.comparing(s-> MapUtils.getDouble(s,"open",0D)));
                Collections.reverse(section);

                Tr row2 = factory.createTr();
                Tr row3 = factory.createTr();
                Tr row4 = factory.createTr();
                index +=3;

                table.getContent().add(row2);
                table.getContent().add(row3);
                table.getContent().add(row4);

                for (Map<String,Object> c : t) {

                    String k = c.get("key").toString();
                    if(ObjectUtil.isNumeric(k)){
                        if(section.isEmpty()){
                            continue;
                        }
                        Map<String,Object> m = section.get(Integer.parseInt(k));
                        createBodyCell(row2,m.get("total") == null ? "":m.get("total").toString());
                        createBodyCell(row3,m.get("ratio") == null ? "": WordUtil.formatRate(m.get("ratio")).toString());
                        createBodyCell(row4,m.get("cumulative") == null?"":m.get("cumulative").toString());
                    }else if(k.contains("|")){
                        createBodyCell(row2,"本段");
                        createBodyCell(row3,"比例");
                        createBodyCell(row4,"累计");
                    } else {
                        // 第二行至第四行需要合并
                        createBodyCell(row2,  school.get(k) == null ? "":school.get(k).toString());
                        createBodyCell(row3, "");
                        createBodyCell(row4, "");
                        mergeCellsVertically((int) c.get("col"),index-2,index);
                    }
                }
            }
        }
    }

    private String getValue(Map<String,Object> section){

        if(section.get("close") == null){
            return section.get("open").toString();
        }

        return  "(" + section.get("close") + "-" + section.get("open") +"]";
    }

}
