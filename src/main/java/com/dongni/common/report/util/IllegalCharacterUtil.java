package com.dongni.common.report.util;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/4/18 周四 上午 10:31
 * @Version 1.0.0
 */
public class IllegalCharacterUtil {
    // sheetName不能是这些特殊字符
    private static final Pattern SHEET_NAME_INVALID_CAHR_PATTERN1 = Pattern.compile("[/\\\\?*:\\[\\]]");
    private static final Pattern SHEET_NAME_INVALID_CAHR_PATTERN2 = Pattern.compile("^'|'$");

    // 文件名不能是这些特殊字符
    // linux => /      windows => \/:*?"<>|
    private static final Pattern FILE_NAME_INVALID_CAHR_PATTERN = Pattern.compile("[\\\\/:*?\"<>|]");

    /**
     * <pre>
     * 过滤sheetName非法字符
     * 官方要求: <a href="https://support.microsoft.com/en-us/office/rename-a-worksheet-3f1f7148-ee83-404d-8ef0-9ff99fbad1f9">微软支持</a>
     * wps有同样的要求，可在wps输入一个错误的sheetName有弹窗提示
     * 要求如下:
     * 1.不能为空  -- 抛异常
     * 2.不超过31个字符  -- 截断
     * 3./ \ ? * : [ ]  包含这些特殊字符  -- 替换为 "-"
     * 4.名称开头或结尾是单引号  --  替换为 双引号
     * 5.不能是History,内部使用的保留字,但其实还是可以使用滴  -- 改为 "history"
     * </pre>
     *
     * @param sheetName 未处理的sheetName
     * @return 已处理的sheetName
     */
    public static String normalizeSheetName(String sheetName) {
        int length = StringUtils.length(sheetName);
        if (length == 0) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "sheetName不能为空!");
        }
        // 改为小写
        if ("History".equals(sheetName)) {
            sheetName = "history";
        }
        // 超过31个字符截断
        if (length > 31) {
            sheetName = sheetName.substring(0, 31);
        }
        // 替换特殊字符为 "_"
        sheetName = SHEET_NAME_INVALID_CAHR_PATTERN1.matcher(sheetName).replaceAll("_");
        // 开头和结果单引号替换为双引号
        sheetName = SHEET_NAME_INVALID_CAHR_PATTERN2.matcher(sheetName).replaceAll("\"");

        return sheetName;
    }

    /**
     * 处理文件名非法字符，将非法字符替换为 "-"
     *
     * @param fileName 未处理的fileName
     * @return 已处理的fileName
     */
    public static String normalizeFileName(String fileName) {
        return FILE_NAME_INVALID_CAHR_PATTERN.matcher(fileName).replaceAll("_");
    }
}
