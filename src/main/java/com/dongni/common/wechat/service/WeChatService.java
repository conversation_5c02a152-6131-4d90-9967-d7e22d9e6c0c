package com.dongni.common.wechat.service;

import com.dongni.basedata.system.account.service.impl.AccountServiceImpl;
import com.dongni.basedata.system.account.service.impl.HzyLonginService;
import com.dongni.basedata.system.account.util.RSAUtil;
import com.dongni.common.temp.redis.template.CommonJedisTemplate;
import com.dongni.common.wechat.utils.WeChatTypeEnumeration;
import com.dongni.common.wechat.utils.WeChatUtils;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信相关接口实现
 * Created by 马腾 on 2016/6/27.
 */
@Service
public class WeChatService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private AccountServiceImpl accountService;
    @Autowired
    private HzyLonginService hzyLonginService;

    private static final Logger log = LogManager.getLogger(WeChatService.class);

    /**
     * 获取微信网页端授权access_token
     *
     * @param referer 当前访问的域名，从request的header中获取
     * @return 返回结果
     */
    public Map<String, Object> getAccessToken(Object referer) {

        Map<String, String> weChatConfig = WeChatUtils.getWeChatConfig(referer);

        // 先从缓存中获取，如果缓存没有则抛出异常
        String token = JedisTemplate.execute(jedis -> jedis.get(weChatConfig.get("accessTokenKey")));

        if (token == null){
            //本地获取Redis
            token = CommonJedisTemplate.execute(jedis -> jedis.get(weChatConfig.get("accessTokenKey")));
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("access_token", token);
        resultMap.put("appId", weChatConfig.get("appId"));
        resultMap.put("jsApiTicketKey", weChatConfig.get("jsApiTicketKey"));
        return resultMap;
    }

    public Map<String, String> getSign(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isNotBlank("url").verify();

        Map<String, Object> tokenResult = this.getAccessToken(parameterMap.get("referer"));

        // 直接从redis中获取
        String jsApiTicket = JedisTemplate.execute(jedis -> jedis.get(tokenResult.get("jsApiTicketKey").toString()));

        Map<String, String> resultMap = WeChatUtils.sign(jsApiTicket, (String) parameterMap.get("url"));
        resultMap.put("appId", tokenResult.get("appId").toString());
        return resultMap;
    }


    /**
     * 文件下载接口
     *
     * @param params media_id exercisePlanId
     * @return 下载情况
     */
    public Map<String, Object> downloadFile(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isNotBlank("media_id").verify();

        log.info("******************media_id:"+params.get("media_id"));

        boolean success = this.downloadFile(params, 0);
        if (!success) {
            log.error("从微信下载图片参数：{}，出现异常", params);
            throw new CommonException(ResponseStatusEnum.WE_CHAT_DOWNLOAD_FILE_ERROR);
        }

        Map<String, Object> rs = new HashMap<>();
        rs.put("filePath", params.get("path"));
        return rs;
    }

    public Map<Integer, String> getAllToken() {
        Map<Integer, String> result = new HashMap<>();
        // 获取懂你的accessToken
        result.put(WeChatTypeEnumeration.DONG_NI.getKey(), getAccessToken(WeChatTypeEnumeration.DONG_NI.getValue()).get("access_token").toString());
        Map<String, Object> inpossAccessToken = getAccessToken(WeChatTypeEnumeration.INPOSS.getValue());
        if (inpossAccessToken.get("access_token") != null) {
            result.put(WeChatTypeEnumeration.INPOSS.getKey(), inpossAccessToken.get("access_token").toString());
        }
        Map<String, Object> dongniTeacherAccessToken = getAccessToken(WeChatTypeEnumeration.DONG_NI_TEACHER.getValue());
        if (dongniTeacherAccessToken.get("access_token") != null) {
            result.put(WeChatTypeEnumeration.DONG_NI_TEACHER.getKey(), dongniTeacherAccessToken.get("access_token").toString());
        }
        return result;
    }

    public Map<Integer, Map<String, String>> getAllConfig() {
        Map<Integer, Map<String, String>> weChatConfigMap = new HashMap<>();
        weChatConfigMap.put(WeChatTypeEnumeration.DONG_NI.getKey(), WeChatUtils.getWeChatConfig(WeChatTypeEnumeration.DONG_NI.getValue()));
        weChatConfigMap.put(WeChatTypeEnumeration.INPOSS.getKey(), WeChatUtils.getWeChatConfig(WeChatTypeEnumeration.INPOSS.getValue()));
        weChatConfigMap.put(WeChatTypeEnumeration.DONG_NI_TEACHER.getKey(), WeChatUtils.getWeChatConfig(WeChatTypeEnumeration.DONG_NI_TEACHER.getValue()));
        return weChatConfigMap;
    }

    /**
     * 下载文件具体实现，加入重试功能
     *
     * @param params     请求参数
     * @param retryCount 重试次数
     */
    private boolean downloadFile(Map<String, Object> params, int retryCount) {
        boolean success = false;
        String requestUrl = "http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=" +
                getAccessToken(params.get("referer")).get("access_token") +
                "&media_id=" + params.get("media_id");
        params.put("requestUrl", requestUrl);
        HttpGet httpGet = new HttpGet(requestUrl);
        try (CloseableHttpClient httpClient = HttpClientBuilder.create().build();
             CloseableHttpResponse response = httpClient.execute(httpGet);
             InputStream inputStream = response.getEntity().getContent()) {
            // 如果包含这个header，说明请求下载成功，存到oss中
            if (response.containsHeader("Content-disposition")) {
                String fileName = params.get("media_id") + ".jpg";
                String path = FileStorageTemplate.put(fileStoragePut -> {
                    fileStoragePut.setInputStream(inputStream);
                    fileStoragePut.setFileName(fileName);
                    fileStoragePut.setAutoExpire(true);
                });
                success = true;
                params.put("path",path);
            }
        } catch (Exception e) {
            log.error("下载文件出现异常：{}",e.getMessage(),e);
        }

        // 如果下载不成功，且重试次数小于3，则进行重尝试
        if (!success && retryCount < 3) {
            retryCount++;
            return downloadFile(params, retryCount);
        }

        return success;
    }


    public Map<String, Object> getUserInfo(Map<String, Object> params) {

        String code = MapUtil.getString(params, "code");

        Map<String, String> weChatConfig = WeChatUtils.getWeChatConfig(params.get("referer"), MapUtil.getStringNullable(params, "wechatType"));

        // 获取access_token 和 openid
        String strResult = restTemplate.getForObject("https://api.weixin.qq.com/sns/oauth2/access_token?appid=" + weChatConfig.get("appId") + "&secret=" + weChatConfig.get("secret") + "&code="
                + code + "&grant_type=authorization_code", String.class);
        log.info("微信openid获取, 请求参数-{}, 返回结果-{}", params, strResult);
        ObjectMapper objectMapper = new ObjectMapper();

        Map openidResult;
        try {
            openidResult = objectMapper.readValue(strResult, Map.class);
        } catch (IOException e) {
            throw new CommonException(ResponseStatusEnum.FAILURE, e);
        }


        if (MapUtils.isEmpty(openidResult) || ObjectUtil.isBlank(openidResult.get("openid"))) {
            log.error("微信openid获取失败, 请求参数-{}, 返回结果-{}", params, openidResult);
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "微信信息获取失败");
        }

        // 获取unionid
        String accessToken = MapUtil.getString(openidResult, "access_token");
        String openid = MapUtil.getString(openidResult, "openid");
        String unionidUrl = String.format("https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s", accessToken, openid);

        log.info("unionID - 请求地址: {}", unionidUrl);
        String unionStr = restTemplate.getForObject(unionidUrl, String.class);
        log.info("微信unionid获取, 请求参数-{}, 返回结果-{}", params, unionStr);
        Map unionResult;
        try {
            unionResult = objectMapper.readValue(unionStr, Map.class);
        } catch (IOException e) {
            throw new CommonException(ResponseStatusEnum.FAILURE, e);
        }

        if (MapUtils.isEmpty(unionResult) || ObjectUtil.isBlank(unionResult.get("unionid"))) {
            log.error("微信unionid获取失败, 请求参数-{}, 返回结果-{}", openid, unionResult);
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "微信信息获取失败");
        }

        String unionid = MapUtil.getString(unionResult, "unionid");
        Map<String, Object> result = new HashMap<>();
        result.put("openid", RSAUtil.encrypt(openid));
        result.put("unionid", RSAUtil.encrypt(unionid));

        if (ObjectUtil.isNotBlank(params.get("accountName")) && ObjectUtil.isNotBlank(params.get("password"))) {
            try {
                params.put("accountNameEncrypt",params.get("accountName"));
                params.put("passwordEncrypt",params.get("password"));

                String accountName = RSAUtil.decrypt(MapUtil.getString(params, "accountName"));
                String password = RSAUtil.decrypt(MapUtil.getString(params, "password"));

                // 请求
                // 请求好专业接口 判断账号密码是否正确
                Map<String, Object> hzyParams = new HashMap<>();

                Map<String, Object> parameter = new HashMap<>();
                parameter.put("cellphone",params.get("accountNameEncrypt"));
                parameter.put("pwd",params.get("passwordEncrypt"));
                parameter.put("type", "dongni_tz");

                hzyParams.put("parameter", parameter);

                Map<String, Object> hzyUserInfo = hzyLonginService.getHzyUserInfoWithFC(params,hzyParams);
                log.info("好专业用户请求参数 - {}，返回参数 - {}", hzyParams, hzyUserInfo);
                if (MapUtils.isEmpty(hzyUserInfo)) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "好专业用户获取失败");
                }

                Integer hzyCode = MapUtil.getIntNullable(hzyUserInfo, "code");
                String errorMessage = MapUtil.getStringNullable(hzyUserInfo, "errorMessage");
                if (hzyCode == null || hzyCode != 0) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, errorMessage);
                }

                Map<String, Object> hzyResult = MapUtil.getMap(hzyUserInfo, "result");
                if (MapUtils.isEmpty(hzyResult)) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "好专业返回的用户列表为空");
                }

                List<Map<String, Object>> userList = MapUtil.getListMap(hzyResult, "userList");
                if (CollectionUtils.isEmpty(userList)) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "好专业返回的用户列表为空");
                }

                Map<String, Object> bindOpenIdParams = new HashMap<>();
                bindOpenIdParams.put("accountNameAes", SensitiveInfoUtil.aesEncrypt(accountName));
                bindOpenIdParams.put("openid", openid);
                bindOpenIdParams.put("referer", params.get("referer"));
                accountService.bindWechatOpenId(bindOpenIdParams);
            } catch (Exception e) {
                log.error("绑定opneId异常:{}", e.getMessage());
                return result;
            }

        }

        return result;
    }
}
