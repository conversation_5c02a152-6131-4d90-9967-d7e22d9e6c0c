package com.dongni.common.wechat.service;

import com.dongni.basedata.bean.BaseDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021年12月24日
 */
@Service
public class WechatTemplateService {

    @Autowired
    private BaseDataRepository baseDataRepository;

    /**
     * 成绩推送模板列表
     * @param params
     * @return
     */
    public List<Map<String, Object>> getWechatTemplateList(Map<String, Object> params) {

        return baseDataRepository.selectList("WechatTemplateMapper.getWechatTemplateList", params);

    }


}
