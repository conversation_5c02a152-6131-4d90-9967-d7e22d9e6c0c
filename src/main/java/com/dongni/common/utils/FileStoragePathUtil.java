package com.dongni.common.utils;

import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.constant.FileStorageConstants;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2025/07/22
 */
public class FileStoragePathUtil {
    
    /**
     * 内部链接转公共链接
     *   {studentId: 1}               ->  {studentId: 1}
     *   {studentId: 1, link: "xxx" } ->  {studentId: 1, link: "xxx", publishLink: "https://cdn.dongni100.com/xxx"}
     *   {studentId: 1, link: null }  ->  {studentId: 1, link: null , publishLink: null }
     * @param data       数据map
     * @param innerKey   内部链接的字段名称 如果内部链接字段不存在，则不会进行任何操作
     * @param publishKey 公共链接的字段名称
     */
    public static void publicPath(Map<String, Object> data, String innerKey, String publishKey) {
        if (MapUtils.isEmpty(data) || !data.containsKey(innerKey)) {
            return;
        }
        publicPath(() -> MapUtil.getTrimNullable(data, innerKey), publishPath -> data.put(publishKey, publishPath));
    }
    
    /**
     * 内部链接转公共链接
     *    如果内部链接为 null or "" or "   "
     *    则公共链接为 null
     * @param filePathSupplier   提供内部链接 expire/60/074f343b8c9742b4bcf6f7ab3f14dc42_1846052978/1753092735441.docx
     * @param publicPathConsumer 消费公共链接 https://cdn.dongni100.com/expire/60/074f343b8c9742b4bcf6f7ab3f14dc42_1846052978/1753092735441.docx
     */
    public static void publicPath(Supplier<String> filePathSupplier, Consumer<String> publicPathConsumer) {
        if (filePathSupplier == null || publicPathConsumer == null) {
            return;
        }
        String publicUrl = getPublicPath(filePathSupplier.get());
        publicPathConsumer.accept(publicUrl);
    }
    
    /**
     * 内部链接转公共链接
     *   如果内部链接为 null or "" or "   "
     *   则公共链接为 null
     * @param filePath 内部链接
     * @return 公共链接
     */
    public static String getPublicPath(String filePath) {
        String publicUrl = null;
        if (StringUtils.isNotBlank(filePath)) {
            if (filePath.startsWith(FileStorageConstants.PATH_SEPARATOR)) {
                publicUrl = FileStorageTemplate.getCdnUrl() + filePath;
            } else {
                publicUrl = FileStorageTemplate.getCdnUrl() + FileStorageConstants.PATH_SEPARATOR + filePath;
            }
        }
        return publicUrl;
    }
}
