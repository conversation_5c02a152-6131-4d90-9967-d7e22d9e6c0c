package com.dongni.common.utils;

import java.io.Serializable;
import java.util.Comparator;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR> <br/>
 * @date 2021/07/07 <br/>
 * @see java.util.Comparator
 */
@FunctionalInterface
public interface ComparatorEx<T> extends Comparator<T> {
    
    /**
     * 升序
     * @param keyExtractor 将对象转换为排序的健
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#comparing(Function)
     */
    static <T, U extends Comparable<? super U>> ComparatorEx<T> asc(Function<? super T, ? extends U> keyExtractor) {
        return comparing(keyExtractor, Comparator.naturalOrder());
    }
    
    /**
     * 升序 null值放最前面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#comparing(Function)
     */
    static <T, U extends Comparable<? super U>> ComparatorEx<T> ascNullFirst(Function<? super T, ? extends U> keyExtractor) {
        return comparing(keyExtractor, Comparator.nullsFirst(Comparator.naturalOrder()));
    }
    
    /**
     * 升序 null值放最后面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#comparing(Function)
     */
    static <T, U extends Comparable<? super U>> ComparatorEx<T> ascNullLast(Function<? super T, ? extends U> keyExtractor) {
        return comparing(keyExtractor, Comparator.nullsLast(Comparator.naturalOrder()));
    }
    
    /**
     * 逆序
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#comparing(Function)
     */
    static <T, U extends Comparable<? super U>> ComparatorEx<T> desc(Function<? super T, ? extends U> keyExtractor) {
        return comparing(keyExtractor, Comparator.reverseOrder());
    }
    
    /**
     * 逆序 null值放最前面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#comparing(Function)
     */
    static <T, U extends Comparable<? super U>> ComparatorEx<T> descNullFirst(Function<? super T, ? extends U> keyExtractor) {
        return comparing(keyExtractor, Comparator.nullsFirst(Comparator.reverseOrder()));
    }
    
    /**
     * 逆序 null值放最后面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#comparing(Function)
     */
    static <T, U extends Comparable<? super U>> ComparatorEx<T> descNullLast(Function<? super T, ? extends U> keyExtractor) {
        Comparator<U> comparator = Comparator.naturalOrder();
        return comparing(keyExtractor, Comparator.nullsLast(Comparator.reverseOrder()));
    }
    
    /**
     * 升序
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default <U extends Comparable<? super U>> ComparatorEx<T> thenAsc(Function<? super T, ? extends U> keyExtractor) {
        return thenComparing(keyExtractor, Comparator.naturalOrder());
    }
    
    /**
     * 升序 null值放最前面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default <U extends Comparable<? super U>> ComparatorEx<T> thenAscNullFirst(Function<? super T, ? extends U> keyExtractor) {
        return thenComparing(keyExtractor, Comparator.nullsFirst(Comparator.naturalOrder()));
    }
    
    /**
     * 升序 null值放最后面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default <U extends Comparable<? super U>> ComparatorEx<T> thenAscNullLast(Function<? super T, ? extends U> keyExtractor) {
        return thenComparing(keyExtractor, Comparator.nullsLast(Comparator.naturalOrder()));
    }
    
    /**
     * 逆序
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default <U extends Comparable<? super U>> ComparatorEx<T> thenDesc(Function<? super T, ? extends U> keyExtractor) {
        return thenComparing(keyExtractor, Comparator.reverseOrder());
    }
    
    /**
     * 逆序 null值放最前面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default <U extends Comparable<? super U>> ComparatorEx<T> thenDescNullFirst(Function<? super T, ? extends U> keyExtractor) {
        return thenComparing(keyExtractor, Comparator.nullsFirst(Comparator.reverseOrder()));
    }
    
    /**
     * 逆序 null值放最后面
     * @param keyExtractor 将对象转换为排序的值
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default <U extends Comparable<? super U>> ComparatorEx<T> thenDescNullLast(Function<? super T, ? extends U> keyExtractor) {
        return thenComparing(keyExtractor, Comparator.nullsLast(Comparator.reverseOrder()));
    }
    
    // ------------------------------------------------------------------- treeCode排序
    
    /**
     * 升序 按照treeCode进行排序
     *    treeCode以"."作为分隔符 {@link TreeCodeUtil#DELIMITER}
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     */
    static <T> ComparatorEx<T> ascTreeCode(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 升序 null值放最前面 按照treeCode进行排序
     *    treeCode以"."作为分隔符 {@link TreeCodeUtil#DELIMITER}
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     */
    static <T> ComparatorEx<T> ascTreeCodeNullFirst(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 升序 null值放最后面 按照treeCode进行排序
     *    treeCode以"."作为分隔符 {@link TreeCodeUtil#DELIMITER}
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     */
    static <T> ComparatorEx<T> ascTreeCodeNullLast(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 逆序 按照treeCode进行排序
     *    treeCode以"."作为分隔符 {@link TreeCodeUtil#DELIMITER}
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     */
    static <T> ComparatorEx<T> descTreeCode(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 逆序 null值放最前面 按照treeCode进行排序
     *    treeCode以"."作为分隔符 {@link TreeCodeUtil#DELIMITER}
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     */
    static <T> ComparatorEx<T> descTreeCodeNullFirst(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 逆序 null值放最后面 按照treeCode进行排序
     *    treeCode以"."作为分隔符 {@link TreeCodeUtil#DELIMITER}
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     */
    static <T> ComparatorEx<T> descTreeCodeNullLast(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 升序 按照treeCode进行排序
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default ComparatorEx<T> thenAscTreeCode(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 升序 null值放最前面 按照treeCode进行排序
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default ComparatorEx<T> thenAscTreeCodeNullFirst(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 升序 null值放最后面 按照treeCode进行排序
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default ComparatorEx<T> thenAscTreeCodeNullLast(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 逆序 按照treeCode进行排序
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default ComparatorEx<T> thenDescTreeCode(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 逆序 null值放最前面 按照treeCode进行排序
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default ComparatorEx<T> thenDescTreeCodeNullFirst(Function<? super T, String> keyExtractor) {
    
    }
    
    /**
     * 逆序 null值放最后面 按照treeCode进行排序
     * @param keyExtractor 获取treeCode
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function)
     */
    default ComparatorEx<T> thenDescTreeCodeNullLast(Function<? super T, String> keyExtractor) {
    
    }
    
    // ------------------------------------------------------------------- comparator转换
    
    /**
     * 可将Comparator转为ComparatorEx
     * @param comparator comparator
     * @return ComparatorEx
     */
    static <T> ComparatorEx<T> comparing(Comparator<T> comparator) {
        Objects.requireNonNull(comparator);
        return (ComparatorEx<T> & Serializable) comparator::compare;
    }
    
    /**
     * 将对象获取排序的健及如何比较转换为比较器
     * @param keyExtractor 将对象转换为排序的健
     * @param keyComparator 用于比较排序键的Comparator
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#comparing(Function, Comparator)
     */
    static <T, U> ComparatorEx<T> comparing(Function<? super T, ? extends U> keyExtractor,
                                            Comparator<? super U> keyComparator) {
        Objects.requireNonNull(keyExtractor);
        Objects.requireNonNull(keyComparator);
        return comparing((c1, c2) -> keyComparator.compare(keyExtractor.apply(c1), keyExtractor.apply(c2)));
    }
    
    /**
     * {@inheritDoc}
     * @param other Comparator
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Comparator)
     */
    @Override
    default ComparatorEx<T> thenComparing(Comparator<? super T> other) {
        Objects.requireNonNull(other);
        return comparing((c1, c2) -> {
            int res = compare(c1, c2);
            return (res != 0) ? res : other.compare(c1, c2);
        });
    }
    
    /**
     * {@inheritDoc}
     * @param keyExtractor 将对象转换为排序的值
     * @param keyComparator 用于比较排序键的Comparator
     * @return 通过提取的键进行比较的比较器
     * @see Comparator#thenComparing(Function, Comparator)
     */
    @Override
    default <U> ComparatorEx<T> thenComparing(Function<? super T, ? extends U> keyExtractor,
                                              Comparator<? super U> keyComparator) {
        return thenComparing(comparing(keyExtractor, keyComparator));
    }
}
