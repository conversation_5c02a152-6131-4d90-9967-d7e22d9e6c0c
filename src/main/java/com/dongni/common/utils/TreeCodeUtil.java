package com.dongni.common.utils;

import cn.hutool.core.util.StrUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.conversions.Bson;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.regex;
import static java.util.stream.Collectors.groupingBy;

/**
 *
 * <AUTHOR>
 * 2023/03/07
 *
 * 用于树形数据的检索，对于树形结构的数据，会添加treeCode字段，用于快速检索
 *    1
 *      1.1
 *      1.2
 *        1.2.1
 *        1.2.2
 * 如需要检索出1及所有子孙节点，则精确查询1 + 按照模糊查询1.%
 * 如需要检索出1的所有子孙节点，则按照模糊查询1.%
 *
 */
public class TreeCodeUtil {
    
    public static final String DELIMITER = ".";
    
    public static Collection<Bson> getTreeCodeBsonQueryByTreeCodes(Collection<String> treeCodeCollection) {
        Collection<String> treeCodePrefixes = getTreeCodePrefixes(treeCodeCollection);
        return getTreeCodeBsonQueryByTreeCodePrefixes(treeCodePrefixes);
    }
    
    public static Collection<Bson> getTreeCodeBsonQueryByTreeCodePrefixes(Collection<String> treeCodePrefixCollection) {
        if (CollectionUtils.isEmpty(treeCodePrefixCollection)) { return new ArrayList<>(); }
        return treeCodePrefixCollection.stream()
                .distinct()
                .map(treeCode -> {
                            if (treeCode.endsWith(DELIMITER)) {
                                // .结尾的需要用模糊查询 like '2.2.1.%' 且 特殊字符匹配需要转义
                                String pattern = "^" + treeCode.replace(DELIMITER, "\\" + DELIMITER);
                                return regex("treeCode", pattern);
                            } else {
                                // 非.结尾的需要精准查询 = 2.2.1
                                return eq("treeCode", treeCode);
                            }
                        }
                )
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用于查询的treeCodeList
     *    例:
     *      输入: ["2.2.1","2.2.1.2.2","2.2.2.2.3","2.3.4.","3."]
     *      返回: ["3.","2.2.1", "2.2.1.", "2.3.4.", "2.2.2.2.3", "2.2.2.2.3."]
     *      说明: "2.2.1.2.2" 因 "2.2.1" 包含了，所以被移除
     *            2.2.1 如果用模糊查询可能会查询出 2.2.10 的数据，这是不对的
     *
     * @param treeCodeCollection 需要查询的treeCodeList
     * @return 用于查询的treeCodeList
     *         对于非.结尾的，需要使用精确查询
     *             如 = 2.2.1
     *             mongo  eq(field, "2.2.1")
     *         对于以.结尾的，需要使用模糊查询
     *             如 like '2.2.1.%'
     *             mongo  regex(field, "^2\.2\.1\.") 注意需要转义
     */
    public static Collection<String> getTreeCodePrefixes(Collection<String> treeCodeCollection) {
        if (CollectionUtils.isEmpty(treeCodeCollection)) { return new ArrayList<>(); }
        treeCodeCollection = treeCodeCollection.stream()
                .map(MapUtil::getTrimNullable)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(treeCodeCollection)) { return new ArrayList<>(); }
        
        // treeCode前缀 返回用
        List<String> treeCodePrefixList = new ArrayList<>();
        // 用于校验是否以此为前缀，在dotCount计算完成后才加入，dotCount为n时，校验dotCount为0~n-1的treeCode
        List<String> treeCodePrefixCheckList = new ArrayList<>();
        Map<Integer, List<String>> dotCount2treeCode = treeCodeCollection.stream()
                .collect(groupingBy(item -> StrUtil.count(item, DELIMITER)));
        List<Integer> dotCountSortedList = dotCount2treeCode.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
        for (Integer dotCount : dotCountSortedList) {
            List<String> dotCountTreeCodeList = dotCount2treeCode.get(dotCount);
            List<String> dotCountTreeCodeCheckPrefixList = new ArrayList<>();
            for (String dotCountTreeCode : dotCountTreeCodeList) {
                boolean exist = treeCodePrefixCheckList.stream()
                        .anyMatch(dotCountTreeCode::startsWith);
                if (!exist) {
                    treeCodePrefixList.add(dotCountTreeCode);
                    if (!dotCountTreeCode.endsWith(DELIMITER)) {
                        treeCodePrefixList.add(dotCountTreeCode + DELIMITER);
                    }
                    dotCountTreeCodeCheckPrefixList.add(dotCountTreeCode);
                }
            }
            treeCodePrefixCheckList.addAll(dotCountTreeCodeCheckPrefixList);
        }
        return treeCodePrefixList;
    }

    
}
