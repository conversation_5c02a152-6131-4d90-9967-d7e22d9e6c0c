package com.dongni.common.utils;

import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> <br/>
 * @date 2020/08/04 <br/>
 * 数学计算
 */
public class MathUtil {
    
    /**
     * 求总体方差
     * 只遍历数组一次求方差，利用公式DX=EX^2-(EX)^2
     *
     * @param list 待求方差的集合 不能转为数值的会剔除不参与计算
     * @return 方差
     */
    public static <T> double variance(List<T> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        
        double sum = 0, sum2 = 0;
        int len = 0;
        for (T item : list) {
            if (ObjectUtil.isNumeric(item)) {
                double itemValue = Double.parseDouble(item.toString());
                sum += itemValue;
                sum2 += itemValue * itemValue;
                len++;
            }
        }
        
        // 防止提供的元素都无法转为数字
        if (len == 0) { return 0; }
        return sum2 / len - (sum / len) * (sum / len);
    }
    
    /**
     * 求总体标准差
     * @param list    待求标准差的集合 不能转为数值的会剔除不参与计算
     * @return 标准差
     */
    public static <T> double standardDeviation(List<T> list) {
        return Math.sqrt(variance(list));
    }
    
    /**
     * 合计数组
     * @param doubleList 待算数据
     * @return 和
     */
    public static double sumDouble(List<Double> doubleList) {
        if (CollectionUtils.isEmpty(doubleList)) {
            return 0d;
        }
        return doubleList.stream().filter(Objects::nonNull).mapToDouble(item -> item).sum();
    }
}
