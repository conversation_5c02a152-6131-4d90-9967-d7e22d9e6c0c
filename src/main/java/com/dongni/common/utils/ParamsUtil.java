package com.dongni.common.utils;

import com.dongni.commons.utils.DateUtil;
import com.dongni.tiku.common.util.MapUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> <br/>
 * @date 2020/04/09 <br/>
 *
 */
public class ParamsUtil {
    
    /**
     * 设置currentTime字段 覆盖
     * @param params map
     */
    public static void setCurrentTime(Map<String, Object> params) {
        setCurrentTime(params, true);
    }
    
    /**
     * 设置currentTime字段 不存在才设置
     * @param params map
     */
    public static void setCurrentTimeIfAbsent(Map<String, Object> params) {
        setCurrentTime(params, false);
    }
    
    /**
     * 设置currentTime字段 不存在才设置
     * @param params map
     * @param always 是否始终设置
     *               true  覆盖
     *               false 如果不存在才设置
     */
    private static void setCurrentTime(Map<String, Object> params, boolean always) {
        if (params == null) {
            return;
        }
        if (params.get("currentTime") == null || always) {
            params.put("currentTime", DateUtil.getCurrentDateTime());
        }
    }
    
    
    public static final String SORT_DEFAULT_KEY = "__default";
    
    /**
     * 处理排序字段 sortField sortType
     * @param params  [sortField] [sortType]
     * @param sortFieldMap 排序字段映射 web -> table.field
     *                     如果不传或者传的不在map中的则使用默认的__default
     *                     如果__default不提供，则会移除sortField，sortType
     *                     例: {
     *                           "entrustId": "entrust_id",
     *                           "__default": "entrust_id"
     *                     }
     */
    public void handlerSortParam(Map<String, Object> params, Map<String, String> sortFieldMap) {
        handlerSortParam(params, sortFieldMap, true);
    }
    
    /**
     * 处理排序字段 sortField sortType
     * @param params  [sortField] 排序字段 会用sortFieldMap进行映射
     *                [sortType]  排序方式 "desc"为逆序，"asc"为顺序，如果都不是，按照defaultAsc处理
     * @param sortFieldMap 排序字段映射 web -> table.field
     *                     如果不传或者传的不在map中的则使用默认的__default
     *                     如果sortField为null, 则会移除sortField，sortType
     *                     例: {
     *                           "entrustId": "entrust_id",
     *                           "__default": "entrust_id"
     *                     }
     * @param defaultAsc   true 默认正序
     */
    public static void handlerSortParam(Map<String, Object> params, Map<String, String> sortFieldMap, boolean defaultAsc) {
        String webSortField = MapUtil.getTrimNullable(params, "sortField");
        String sortField = getTarget(sortFieldMap, webSortField);
        if (sortField == null) {
            params.remove("sortField");
            params.remove("sortType");
            return;
        }
        
        Map<String, String> sortTypeMap = new HashMap<>();
        sortTypeMap.put("asc", "ASC");
        sortTypeMap.put("desc", "DESC");
        sortTypeMap.put("__default", defaultAsc ? "ASC" : "DESC");
        String webSortType = MapUtil.getTrimNullable(params, "sortType");
        webSortType = Optional.ofNullable(webSortType).map(String::toLowerCase).orElse(null);
        String sortType = getTarget(sortTypeMap, webSortType);
        
        params.put("sortField", sortField);
        params.put("sortType", sortType);
    }
    
    /**
     * 映射
     * @param sourceToTargetMap 映射列表 可内置 __default
     * @param source 原字段
     * @return 映射的字段
     */
    private static String getTarget(Map<String, String> sourceToTargetMap, String source) {
        String target = source == null ? null : sourceToTargetMap.get(source);
        return target != null ? target : sourceToTargetMap.get(SORT_DEFAULT_KEY);
    }
}
