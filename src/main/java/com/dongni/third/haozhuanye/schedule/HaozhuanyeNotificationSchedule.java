package com.dongni.third.haozhuanye.schedule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.dongni.common.jobs.JobGroup;
import com.dongni.common.jobs.RedisSync;

/**
 * @description:
 * @author: liusifan
 * @create: 2020-04-21 17:46
 **/
@Component
@ConditionalOnProperty("dongni.third.haozhuanye.host")//只有配置好专业参数的环境才需要运行
public class HaozhuanyeNotificationSchedule {
    private final static Logger LOGGER = LoggerFactory.getLogger(HaozhuanyeNotificationSchedule.class);

    @Autowired
    private HaozhuanyeNotificationService haozhuanyeNotificationService;

    /**
     * 好专业通知处理
     */
    @Scheduled(cron = "0 0/5 * * * ? ")
    @RedisSync(group = JobGroup.THIRD, name = "NOTICE:HAOZHUANYE", expireSecond = 3600, waitLockMillisecond = 0)
    public void syncDataByNotification() {
        LOGGER.info("定时处理好专业消息通知-开始");
        haozhuanyeNotificationService.syncDataByNotification();
        haozhuanyeNotificationService.syncDataByLocalNotification();
        LOGGER.info("定时处理好专业消息通知-结束");
    }
}

