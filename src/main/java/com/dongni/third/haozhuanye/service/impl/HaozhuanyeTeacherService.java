package com.dongni.third.haozhuanye.service.impl;

import com.aliyuncs.utils.StringUtils;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.third.base.common.util.ThirdDataUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdTeacherService;
import com.dongni.third.base.service.table.ThirdSchoolService;
import com.dongni.third.haozhuanye.api.Haozhuanye2DongniApi;
import com.dongni.third.haozhuanye.util.HaozhuanyeDataUtils;
import com.dongni.third.tongan.util.TonganDataUtils;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * @ Author     ：yuding
 * @ Date       ：Created in 15:13 2020/10/26
 * @ Description：
 * @ Modified By：
 */
@Service
public class HaozhuanyeTeacherService extends AbstractThirdTeacherService {
    @Autowired
    private Haozhuanye2DongniApi haozhuanye2DongniApi;
    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = MapUtil.of("schoolId", syncParams.getCurrentThirdSchoolKey(), "thirdPartyId", syncParams.getThirdPartyId());
        return haozhuanye2DongniApi.getTeacher(p, syncParams);
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams,
                                                            List<Map<String, Object>> apiDataList,
                                                            Map<String, List<Map<String, Object>>> extendApiData) {
        String thirdSchoolKey = syncParams.getCurrentThirdSchoolKey();
        
        List<Map<String, Object>> thirdDataList = new ArrayList<>();
        for (Map<String, Object> apiData : apiDataList) {
            Map<String, Object> thirdData = new HashMap<>();
            String thirdTeacherKey =  apiData.get("teacherEID").toString();
            // 好专业用户eid
            String loginName =  apiData.get("loginName").toString();
            thirdData.put(PRIMARY_KEY_FIELD, HaozhuanyeDataUtils.getThirdTeacherPK(thirdSchoolKey, thirdTeacherKey));
            
            String phone = MapUtil.getStringNullable(apiData, "phone");
            if(StringUtils.isEmpty(phone)) {
                phone = loginName; //phone在t_teacher表中实际为必填且校内唯一
            }
            
            thirdData.put("teacherName", apiData.get("teacherName"));
            thirdData.put("teacherPhone", phone);
            thirdData.put("teacherPhoneAes", SensitiveInfoUtil.aesEncrypt(phone));

            thirdData.put(OLD_PRIMARY_KEY_LIST_FIELD, Stream.of(
                    HaozhuanyeDataUtils.getOldThirdTeacherPK(thirdSchoolKey, loginName)
            ).collect(toList()));
    
            thirdData.put(ThirdSchoolService.THIRD_PRIMARY_KEY_FIELD, HaozhuanyeDataUtils.getThirdPrimaryKeySchool(thirdSchoolKey));
            // thirdData.put(ThirdIdentityCardService.THIRD_PRIMARY_KEY_FIELD, thirdTeacherKey); // 好专业不同步身份证号
            thirdDataList.add(thirdData);
        }
        
        Map<String, String> putThirdDataKeyMapInfoKey = new HashMap<>(1);
        putThirdDataKeyMapInfoKey.put("thirdSchoolId", "thirdSchoolId");
        ThirdDataUtil.putInfoToThirdDataList(syncParams, thirdDataList,
                ThirdSchoolService.THIRD_PRIMARY_KEY_FIELD, "thirdPrimaryKeyList",
                "没有对应的学校", putThirdDataKeyMapInfoKey,
                thirdSchoolService::getThirdPrimaryKeyMapThirdSchoolInfo);
        
        return thirdDataList;
    }

    @Override
    public Set<String> getRetainKeys() {
        return Sets.newHashSet("pk", // 正式库主键
                "identityCardId",
                "creatorId",
                "creatorName",
                "createDateTime");
    }

    @Override
    protected LinkedHashMap<String, Function<Map<String, Object>, Object>> getSimpleMsgFieldMap() {
        LinkedHashMap<String, Function<Map<String, Object>, Object>> simpleMsgFieldMap = new LinkedHashMap<>();
        simpleMsgFieldMap.put("姓名", thirdData -> thirdData.get("teacherName").toString());
        simpleMsgFieldMap.put("手机号", thirdData -> thirdData.get("teacherPhone").toString());
        return simpleMsgFieldMap;
    }

}
