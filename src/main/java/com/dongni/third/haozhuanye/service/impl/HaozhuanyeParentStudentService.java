package com.dongni.third.haozhuanye.service.impl;

import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.common.util.ThirdDataUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdParentStudentService;
import com.dongni.third.base.service.table.ThirdParentService;
import com.dongni.third.base.service.table.ThirdStudentService;
import com.dongni.third.haozhuanye.api.Haozhuanye2DongniApi;
import com.dongni.third.haozhuanye.util.HaozhuanyeDataUtils;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <p>家长学生关系</p>
 *
 * <AUTHOR>
 * @since 2022/7/11 18:20
 */
@Service
public class HaozhuanyeParentStudentService extends AbstractThirdParentStudentService {

    @Autowired
    private Haozhuanye2DongniApi haozhuanye2DongniApi;
    @Autowired
    private ThirdStudentService thirdStudentService;
    @Autowired
    private ThirdParentService thirdParentService;

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = MapUtil.of("schoolId", syncParams.getCurrentThirdSchoolKey(), "thirdPartyId", syncParams.getThirdPartyId());
        return haozhuanye2DongniApi.getParent(p, syncParams);
    }

    @Override
    public Map<String, List<Map<String, Object>>> getExtendApiData(SyncParams syncParams) {
        Map<String, List<Map<String, Object>>> rs = new HashMap<>();
        Map<String, Object> p = MapUtil.of("schoolId", syncParams.getCurrentThirdSchoolKey(), "thirdPartyId", syncParams.getThirdPartyId());
        rs.put("studentList", haozhuanye2DongniApi.getStudent(p, syncParams));
        return rs;
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams,
                                                            List<Map<String, Object>> apiDataList,
                                                            Map<String, List<Map<String, Object>>> extendApiData) {

        List<Map<String, Object>> studentList = extendApiData.get("studentList");
        Map<String, Map<String, Object>> studentMapByEID = new HashMap<>();
        for (Map<String, Object> item : studentList) {
            studentMapByEID.put(MapUtil.getString(item, "studentEID"), item);
        }

        String thirdSchoolKey = syncParams.getCurrentThirdSchoolKey();
        List<Map<String, Object>> thirdDataList = new ArrayList<>();
        for (Map<String, Object> apiData : apiDataList) {

            Map<String, Object> commonThirdData = new HashMap<>();
            String parentEID = MapUtil.getTrim(apiData, "parentEID");
            String parentLoginName = MapUtil.getTrim(apiData, "loginName");
            List<Map<String, Object>> students = MapUtil.getListMap(apiData, "students");

            if (CollectionUtils.isEmpty(students)) {
                putErrMsg(commonThirdData, apiData, "学生列表为空");
                continue;
            }

            String thirdParentKey = parentEID;
            for (Map<String, Object> student : students) {
                Map<String, Object> thirdData = new HashMap<>(commonThirdData);
                thirdDataList.add(thirdData);

                String studentEID = MapUtil.getStringNullable(student, "studentEID");

                if (ObjectUtil.isBlank(studentEID)) {
                    putErrMsg(thirdData, apiData, "学生studentEID为空");
                    continue;
                }

                String thirdStudentKey = studentEID;
                Map<String, Object> thirdStudentInfo = studentMapByEID.get(studentEID);
                String studentLoginName = MapUtil.getString(thirdStudentInfo, "loginName");

                thirdData.put(PRIMARY_KEY_FIELD, HaozhuanyeDataUtils.getThirdParentStudentPK(thirdSchoolKey, thirdParentKey, thirdStudentKey));
                thirdData.put(OLD_PRIMARY_KEY_LIST_FIELD, Stream.of(
                        HaozhuanyeDataUtils.getOldThirdParentStudentPK(thirdSchoolKey, parentLoginName, studentLoginName)
                ).collect(toList()));

                thirdData.put(ThirdParentService.THIRD_PRIMARY_KEY_FIELD, HaozhuanyeDataUtils.getThirdParentPK(thirdParentKey));
                thirdData.put(ThirdStudentService.THIRD_PRIMARY_KEY_FIELD, HaozhuanyeDataUtils.getThirdStudentPK(thirdSchoolKey, thirdStudentKey));
            }
        }

        Map<String, String> putThirdDataKeyMapInfoKey = new HashMap<>();

        putThirdDataKeyMapInfoKey.put("thirdStudentId", "thirdStudentId");
        ThirdDataUtil.putInfoToThirdDataList(syncParams, thirdDataList,
                ThirdStudentService.THIRD_PRIMARY_KEY_FIELD, "thirdPrimaryKeyList",
                "没有对应的学生", putThirdDataKeyMapInfoKey,
                thirdStudentService::getThirdPrimaryKeyMapThirdStudentInfo);

        putThirdDataKeyMapInfoKey.clear();
        putThirdDataKeyMapInfoKey.put("thirdParentId", "thirdParentId");
        ThirdDataUtil.putInfoToThirdDataList(syncParams, thirdDataList,
                ThirdParentService.THIRD_PRIMARY_KEY_FIELD, "thirdPrimaryKeyList",
                "没有对应的家长", putThirdDataKeyMapInfoKey,
                thirdParentService::getThirdPrimaryKeyMapParentInfo);

        return thirdDataList;
    }

}
