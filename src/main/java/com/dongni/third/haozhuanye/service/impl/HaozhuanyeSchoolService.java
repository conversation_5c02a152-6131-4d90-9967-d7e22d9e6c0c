package com.dongni.third.haozhuanye.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.dongni.basedata.admin.service.IBaseAreaService;
import com.dongni.basedata.admin.service.IBaseSchoolService;
import com.dongni.basedata.third.receiver.ThirdTaskReceiver;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.bean.ThirdRepository;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdSchoolService;
import com.dongni.third.haozhuanye.api.Haozhuanye2DongniApi;
import com.dongni.third.haozhuanye.util.HaozhuanyeDataUtils;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.NonNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: Jianfeng
 * @create: 2020-04-13 11:47
 **/
@Service
public class HaozhuanyeSchoolService extends AbstractThirdSchoolService {
    @Autowired
    private Haozhuanye2DongniApi haozhuanye2DongniApi;
    @Autowired
    protected ThirdRepository repository;
    @Autowired
    private IBaseAreaService baseAreaService;

    @Autowired
    private IBaseSchoolService baseSchoolService;

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = new HashMap<>();
        p.put("thirdPartyId", syncParams.getThirdPartyId());
        List<Map<String, Object>> school;
        String currentThirdSchoolKey = syncParams.getCurrentThirdSchoolKey();
        if("0".equals(currentThirdSchoolKey)) {
        	school = haozhuanye2DongniApi.getSchool(p);
        } else {
        	p.put("schoolId", currentThirdSchoolKey);
        	school = new ArrayList<>();
        	school.add(haozhuanye2DongniApi.getSchoolDetail(p));
        }
        return school;
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams,
                                                            List<Map<String, Object>> apiDataList,
                                                            Map<String, List<Map<String, Object>>> extendApiData) {
        Map<String, Map<String, Object>> schoolAppData = "0".equals(syncParams.get("currentThirdSchoolKey"))
                ? new HashMap<>() : handleSchoolAppData(apiDataList, extendApiData);

        boolean isSyncAllSchool = "0".equals(syncParams.get("currentThirdSchoolKey"));

        // 获取过期时间
        List<Map<String, Object>> thirdSchoolInfoList = getThirdSchoolInfoList();
        Map<String, List<Map<String, Object>>> thirdSchoolMap = thirdSchoolInfoList
                .stream()
                .collect(Collectors.groupingBy(item -> MapUtil.getString(item, "thirdPrimaryKey")));

        // 好专业同步懂你已经存在的学校，需要设置申报状态，如果在懂你已经是开通的状态，不需要走申报，如果是未开通的状态，需要走申报
        List<Map<String, Object>> dongniInnerSchoolList = getDongniInnerSchool(syncParams);
        Map<String, Map<String, Object>> dongniInnerSchoolMap = dongniInnerSchoolList
                .stream()
                .collect(Collectors.toMap(item ->
                        MapUtil.getString(item, "areaId") + StrUtil.COLON + MapUtil.getString(item, "schoolName"),
                        item -> item
                ));



        // 获取懂你区域ID
        Map<String, Map<String, Object>> areaDataMap = getAreaDataList();

        List<Map<String, Object>> thirdDataList = new ArrayList<>();
        for (Map<String, Object> apiData : apiDataList) {
            Map<String, Object> thirdData = new HashMap<>();
            String thirdSchoolKey = MapUtil.getTrim(apiData, "schoolID");
            thirdData.put(PRIMARY_KEY_FIELD, HaozhuanyeDataUtils.getThirdPrimaryKeySchool(thirdSchoolKey));

            Map<String, Object> schoolAppItem = Optional.ofNullable(schoolAppData.get(thirdSchoolKey)).orElseGet(HashMap::new);
            boolean areaFlag = setAreaId(areaDataMap, apiData, thirdData);
            if (!areaFlag) {
                thirdDataList.add(thirdData);
                continue;
            }
            thirdData.put("schoolStatus", 1);
            thirdData.put("stage", -1);
            thirdData.put("gradeNumber", -1);
            thirdData.put("schoolName", apiData.get("schoolName"));
            thirdData.put("schoolGroupName", apiData.get("schoolName"));
            thirdData.put("schoolPhone", thirdData.get("areaId").toString() + thirdSchoolKey);
            thirdData.put("schoolPhoneAes", SensitiveInfoUtil.aesEncrypt(thirdData.get("schoolPhone").toString()));
            thirdData.put("address", apiData.get("schoolName"));
            thirdData.put("memberType", MapUtil.getInt(schoolAppItem, "memberType", 1));
            thirdData.put("belongType", setBelongType(thirdData, dongniInnerSchoolMap, thirdSchoolMap));
            thirdData.put("schoolAscription", 1);
            thirdData.put("companyId", 0);

            if (isSyncAllSchool) {
                String primaryKey = MapUtil.getString(thirdData, PRIMARY_KEY_FIELD);
                List<Map<String, Object>> expireDatetimeAndMemberTypeList = thirdSchoolMap.get(primaryKey);
                // 同步学校列表的时候，新学校默认新增一年，已同步过的使用原来的时间。
                if (CollectionUtil.isNotEmpty(expireDatetimeAndMemberTypeList)) {
                    thirdData.put("expireDatetime", expireDatetimeAndMemberTypeList.get(0).get("expireDatetime"));
                    thirdData.put("memberType", expireDatetimeAndMemberTypeList.get(0).get("memberType"));
                } else {
                    thirdData.put("expireDatetime", LocalDateTime.now().plusYears(1));
                }
            } else {
                thirdData.put("expireDatetime", schoolAppItem.get("expireTime"));
                // 好专业过期时间到期 设置关闭状态
                if (ObjectUtil.isValueEquals(syncParams.get(SyncParams.HZYKey.SCHOOL_CLOSE_KEY), true)) {
                    thirdData.put("schoolStatus", 0);
                }
            }
            checkMemberTypeAndExpireDatetime(syncParams, apiData, schoolAppItem);
            // 默认公办
            thirdData.put("schoolProperty", schoolAppItem.get("schoolProperty") == null ? 1: schoolAppItem.get("schoolProperty"));
            thirdDataList.add(thirdData);
        }
        return thirdDataList;
    }


    /**
     * 设置belongType 同步懂你不存在的学校belongType = 3 同步懂你已存在的学校belongType = 1
     * 这里区分开是为了申报时进行识别是同步的学校在懂你是否已存在
     * 已存在：新增一条申报记录 状态为已通过
     * 不存在：新增一条申报记录 状态为待审核 成老师审核后该学校才可用
     * @param thirdData
     * @param dongniInnerSchoolMap
     * @param thirdSchoolMap
     * @return
     */
    private Integer setBelongType(Map<String, Object> thirdData,
                                 Map<String, Map<String, Object>> dongniInnerSchoolMap,
                                 Map<String, List<Map<String, Object>>> thirdSchoolMap) {


        String primaryKey = MapUtil.getString(thirdData, PRIMARY_KEY_FIELD);
        String areaId = MapUtil.getString(thirdData, "areaId");
        String schoolName = MapUtil.getString(thirdData, "schoolName");
        String uk = areaId + StrUtil.COLON + schoolName;

        // 先判断中间库是否已有记录，中间已有记录取中间库的belongType
        List<Map<String, Object>> thirdSchoolList = thirdSchoolMap.get(primaryKey);
        if (CollectionUtil.isNotEmpty(thirdSchoolList)) {
            return MapUtil.getInt(thirdSchoolList.get(0), "belongType");
        }

        // 中间库不存在判断是否同步已存在的学校，如果已存在则belongType = 1
        Map<String, Object> dongniInnerSchool = dongniInnerSchoolMap.get(uk);
        if (MapUtils.isNotEmpty(dongniInnerSchool)) {
            return MapUtil.getInt(dongniInnerSchool, "belongType");
        }

        // 同步懂你不存在的学校belongType=3
        return DictUtil.getDictValue("belongType", "nicezhuanye");
    }


    /**
     * 获取在懂你创建的学校
     * @return
     * @param syncParams
     */
    private List<Map<String, Object>> getDongniInnerSchool(SyncParams syncParams) {
        Map<String, Object> params = new HashMap<>(syncParams);

        return baseSchoolService.getDongniInnerSchool(params);

    }

    /**
     * 好专业region转变成懂你
     *
     * @param regionId     好专业的regionId
     * @param provinceName 省名
     * @param cityName     市名
     * @param districtName 区名
     * @return 懂你
     */
    public Long changeRegionIdToDong(@NonNull Integer regionId, String provinceName, String cityName, String districtName) {
        Map<String, Map<String, Object>> areaDataMap = getAreaDataList();
        Map<String, Object> param = MapUtil.of("regionID", regionId, "provinceName", provinceName, "districtName", districtName, "cityName", cityName);
        Map<String, Object> result = Maps.newHashMapWithExpectedSize(4);
        if (setAreaId(areaDataMap, param, result)) {
            return MapUtils.getLong(result, "areaId");
        }
        return null;
    }

    /**
     * 设置同步的区域ID
     *
     * @param areaDataMap 懂你区域map
     * @param apiData     第三方一所学校数据
     * @param thirdData
     * @return 区域ID
     */
    private boolean setAreaId(Map<String, Map<String, Object>> areaDataMap,
                              Map<String, Object> apiData,
                              Map<String, Object> thirdData) {

        String regionID = MapUtil.getStringNullable(apiData, "regionID");
        String parentAreaId = null;

        Map<String, Object> dongniArea = areaDataMap.get(regionID);


        if (MapUtils.isEmpty(dongniArea)) {
            String provinceName = MapUtil.getStringNullable(apiData, "provinceName");
            if (ObjectUtil.isBlank(provinceName)) {
                putErrMsg(thirdData, apiData, "懂你没有对应的regionID，并且provinceName为空");
                return false;
            }

            String districtName = MapUtil.getStringNullable(apiData, "districtName");
            String cityName = MapUtil.getStringNullable(apiData, "cityName");

            Map<String, Object> provinceParams = new HashMap<>();
            provinceParams.put("areaName", provinceName);
            List<Map<String, Object>>  provinceList = baseAreaService.getAreaByName(provinceParams);
            if (CollectionUtils.isEmpty(provinceList)) {
                putErrMsg(thirdData, apiData, "懂你没有对应的区域: " + provinceName);
                return false;
            }

            if (provinceList.size() > 1) {
                putErrMsg(thirdData, apiData, "懂你存在重复的区域数据：" + provinceName);
                return false;
            }

            Map<String, Object> provinceInfo = provinceList.get(0);
            String provinceAreaId = MapUtil.getString(provinceInfo, "areaId");
            regionID = provinceAreaId;
            parentAreaId = provinceAreaId;

            if (ObjectUtil.isNotBlank(cityName)) {
                Map<String, Object> cityParams = new HashMap<>();
                cityParams.put("areaName", cityName);
                cityParams.put("parentAreaId", parentAreaId);
                List<Map<String, Object>>  cityList = baseAreaService.getAreaByName(cityParams);
                if (CollectionUtils.isEmpty(cityList)) {
                    putErrMsg(thirdData, apiData, "懂你没有对应的区域: " + provinceName);
                    return false;
                }

                if (cityList.size() > 1) {
                    putErrMsg(thirdData, apiData, "懂你存在重复的区域数据：" + provinceName);
                    return false;
                }

                Map<String, Object> cityInfo = cityList.get(0);
                String cityAreaId = MapUtil.getString(cityInfo, "areaId");
                regionID = cityAreaId;
                parentAreaId = cityAreaId;
            }

            if (ObjectUtil.isNotBlank(districtName)) {
                Map<String, Object> districtParams = new HashMap<>();
                districtParams.put("areaName", districtName);
                districtParams.put("parentAreaId", parentAreaId);
                List<Map<String, Object>>  districtList = baseAreaService.getAreaByName(districtParams);
                if (CollectionUtils.isEmpty(districtList)) {
                    putErrMsg(thirdData, apiData, "懂你没有对应的区域: " + provinceName);
                    return false;
                }

                if (districtList.size() > 1) {
                    putErrMsg(thirdData, apiData, "懂你存在重复的区域数据：" + provinceName);
                    return false;
                }

                Map<String, Object> districtInfo = districtList.get(0);
                String districtAreaId = MapUtil.getString(districtInfo, "areaId");
                regionID = districtAreaId;
                parentAreaId = districtAreaId;
            }

            thirdData.put("areaId", regionID);

            return true;
        }

        thirdData.put("areaId", MapUtil.getLong(dongniArea, "areaId"));
        return true;
    }

    /**
     * 获取区域数据
     * @return
     */
    private Map<String, Map<String, Object>> getAreaDataList() {
        Map<String, Object> params = new HashMap<>();
        List<Map<String, Object>> areaList = baseAreaService.getAreaList(params);

        return areaList.stream().collect(Collectors.toMap(item -> MapUtil.getString(item, "areaId"), item -> item));
    }

    /**
     * 校验是否有传memberType和expireDatetime
     * @param params
     * @param item
     * @param schoolAppItem
     */
    private void checkMemberTypeAndExpireDatetime(Map<String, Object> params,
                                                  Map<String, Object> item,
                                                  Map<String, Object> schoolAppItem) {

        String errMsg = "";
        if(ObjectUtil.isValueEquals(params.get("thirdPartyId"), DictUtil.getDictValue("thirdParty", "nicezhuanye"))
                && !"0".equals(params.get("currentThirdSchoolKey"))) {

            if (ObjectUtil.isBlank(schoolAppItem.get("memberType"))) {
                errMsg = String.format("schoolID: %s schoolName: %s 未提供学校权限\n", item.get("schoolID"), item.get("schoolName"));
            }

            if (ObjectUtil.isBlank(schoolAppItem.get("expireTime"))) {
                errMsg += String.format("schoolID: %s schoolName: %s 未提供过期时间", item.get("schoolID"), item.get("schoolName"));
            }
            log.error(errMsg);
        }
    }

    private Map<String, Map<String, Object>> handleSchoolAppData(List<Map<String, Object>> thirdData,
                                                    Map<String, List<Map<String, Object>>> extendThirdData) {

        Map<String, Map<String, Object>> schoolAppMapBySchoolID = new HashMap<>();

        List<Map<String, Object>> schoolAppInfoList = CollectionUtil.isEmpty(extendThirdData.get("schoolApp"))?
                Lists.newArrayList(): extendThirdData.get("schoolApp");

        String appNames = "精准化教学";

        for (Map<String, Object> schoolAppItem : schoolAppInfoList) {

            Map<String, Object> memberTypeAndExpireTimeInfo = new HashMap<>();

            // hzy有些历史数据schoolProperty为null，默认为公办
            Integer hzySchoolProperty = Integer.valueOf(
                    schoolAppItem.getOrDefault("schoolProperty", 1).toString()
            );

            Integer dnSchoolProperty = ObjectUtil.isValueEquals(hzySchoolProperty, 0)?
                    DictUtil.getDictValue("schoolProperty", "private") :
                    DictUtil.getDictValue("schoolProperty", "public");

            // 好专业的学校ID
            String schoolID = schoolAppItem.get("schoolID").toString();

            // 不包含schoolApps字段
            if (!schoolAppItem.containsKey("schoolApps")) {
                memberTypeAndExpireTimeInfo.put("openType", null);
                memberTypeAndExpireTimeInfo.put("expireTime", null);
                memberTypeAndExpireTimeInfo.put("schoolProperty", dnSchoolProperty);
                schoolAppMapBySchoolID.put(schoolID, memberTypeAndExpireTimeInfo);
                continue;
            }

            List<Map<String, Object>> schoolApps = MapUtil.getListMap(schoolAppItem, "schoolApps");


            Integer openType = schoolApps.stream()
                    .filter(item -> item.get("appName").toString().contains(appNames))
                    .filter(item -> item.containsKey("openType"))
                    .map(item -> Integer.valueOf(item.get("openType").toString()))
                    .max(Comparator.comparing(item -> item))
                    .orElseGet(() -> null);

            // 转换数据
            Integer memberType = ObjectUtil.isNotBlank(openType)
                    && ObjectUtil.isValueEquals(openType, DictUtil.getDictValue("memberType", "formal"))?
                    DictUtil.getDictValue("memberType", "formal"): DictUtil.getDictValue("memberType", "trial ");




            String localDateTime = schoolApps.stream()
                    .filter(item -> item.get("appName").toString().contains(appNames))
                    .filter(item -> item.containsKey("expireTime"))
                    .map(item -> LocalDateTimeUtil.parse(item.get("expireTime").toString(), DatePattern.UTC_PATTERN).toString())
                    .max(Comparator.comparing(item -> item))
                    .orElseGet(() -> null);


            memberTypeAndExpireTimeInfo = MapUtil.of("memberType", memberType, "expireTime", localDateTime, "schoolProperty", dnSchoolProperty);

            schoolAppMapBySchoolID.put(schoolID, memberTypeAndExpireTimeInfo);
        }

        return schoolAppMapBySchoolID;
    }

    @Override
    public void syncThirdToProduct(SyncParams syncParams, ThirdTaskReceiver receiver) {
        super.syncThirdToProduct(syncParams, receiver);
    }


    /**
     * 获取学校的扩展数据
     * @param params currentThirdSchoolKey
     * @return
     */
    @Override
    public Map<String, List<Map<String, Object>>> getExtendApiData(SyncParams params) {

        Map<String, List<Map<String, Object>>> rs = new HashMap<>();
        List<Map<String, Object>> schoolAppList = Lists.newArrayList();

        // 获取学校的过期时间和学校权限,请求的接口必须传schoolId, 为了减少请求的次数，同步学校列表不去更新memberType和expireDatetime
        // 1. 学校列表同步则不处理 2. 学校基础数据同步则更新字段的状态
        // 基础数据全量同步
        if(ObjectUtil.isNotBlank(params.get("currentThirdSchoolKey"))
                && !ObjectUtil.isValueEquals(params.get("currentThirdSchoolKey"), "0")) {
            Map<String, Object> schoolApp = haozhuanye2DongniApi.getSchoolApp(MapUtil.of("schoolId", params.get("currentThirdSchoolKey"), "thirdPartyId", params.getThirdPartyId()));
            schoolAppList.add(schoolApp);
            rs.put("schoolApp", schoolAppList);
        }

        return rs;
    }

    @Override
    public Set<String> getIgnoreKeys() {
        return Sets.newHashSet(
                "pk", // 正式库主键
                "thirdPrimaryKey",
                "schoolSort",
                "creatorId",
                "creatorName",
                "createDateTime",
                "modifierId",
                "modifierName",
                "modifyDateTime"
        );
    }

    @Override
    public Set<String> getRetainKeys() {
        return Sets.newHashSet("pk", // 正式库主键
                "schoolSort",
                "schoolProperty",
                "schoolAscription",
                "companyId",
                "schoolGroupName",
                "schoolPhone",
                "schoolPhoneAes",
                "areaId",
                "creatorId",
                "creatorName",
                "createDateTime");
    }

    /**
     * 获取中间库的学校数据
     */
    @Override
    public List<Map<String, Object>> getThirdSchoolInfoList() {
        Map<String, Object> map = new HashMap<>();
        map.put("thirdPartyId", DictUtil.getDictValue("thirdParty", "nicezhuanye"));
        List<Map<String, Object>> schoolExpireDateTimeList = repository.selectList("ThirdSchoolMapper.getSchoolExpireDateTime", map);
        return schoolExpireDateTimeList;
    }
}

