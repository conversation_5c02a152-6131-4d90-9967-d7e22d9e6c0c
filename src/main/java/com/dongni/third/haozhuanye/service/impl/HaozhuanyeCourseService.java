package com.dongni.third.haozhuanye.service.impl;

import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.common.util.ThirdDataUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdCourseService;
import com.dongni.third.base.service.table.ThirdSchoolService;
import com.dongni.third.haozhuanye.api.Haozhuanye2DongniApi;
import com.dongni.third.haozhuanye.util.HaozhuanyeDataUtils;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2022/7/22 11:40
 */
@Service
public class HaozhuanyeCourseService extends AbstractThirdCourseService {
    @Autowired
    private Haozhuanye2DongniApi haozhuanye2DongniApi;

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = MapUtil.of("schoolId", syncParams.getCurrentThirdSchoolKey(), "thirdPartyId", syncParams.getThirdPartyId());
        List<Map<String, Object>> courseList = haozhuanye2DongniApi.getCourse(p, syncParams);
        return courseList;
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams,
                                                            List<Map<String, Object>> apiDataList,
                                                            Map<String, List<Map<String, Object>>> extendApiData) {

        String thirdSchoolKey = syncParams.getCurrentThirdSchoolKey();
        List<Map<String, Object>> thirdDataList = Lists.newArrayList();

        for (Map<String, Object> apiData : apiDataList) {

            String courseName = MapUtil.getStringNullable(apiData, "name");
            String id = MapUtil.getStringNullable(apiData, "id");
            Integer subjectAttr = MapUtil.getIntNullable(apiData, "subjectAttr");
            List<String> scopeEduStages = MapUtil.getListType(apiData, "scopeEduStages", MapUtil::getStringNullable);

            String thirdCourseKey = id;
            Map<String, Object> commonThirdData = new HashMap<>();
            commonThirdData.put(ThirdSchoolService.THIRD_PRIMARY_KEY_FIELD, thirdSchoolKey);

            if (ObjectUtil.isBlank(courseName)) {
                putErrMsg(commonThirdData, apiData, "课程名称为空");
                thirdDataList.add(commonThirdData);
                continue;
            }

            if (ObjectUtil.isBlank(id)) {
                putErrMsg(commonThirdData, apiData, "subjectID为空");
                thirdDataList.add(commonThirdData);
                continue;
            }

            if (ObjectUtil.isBlank(subjectAttr)) {
                putErrMsg(commonThirdData, apiData, "subjectAttr为空");
                thirdDataList.add(commonThirdData);
                continue;
            }

            if (CollectionUtils.isEmpty(scopeEduStages)) {
                putErrMsg(commonThirdData, apiData, "适用范围为空");
                thirdDataList.add(commonThirdData);
                continue;
            }

            Integer memberCount = subjectAttr == 0? 1: 2;

            for (String eduStage : scopeEduStages) {
                Map<String, Object> thirdData = new HashMap<>(commonThirdData);
                Integer stage;
                if (ObjectUtil.isValueEquals("小学", eduStage)) {
                    stage  = 1;
                }
                else if (ObjectUtil.isValueEquals("初中", eduStage)) {
                    stage  = 2;
                }

                else if (ObjectUtil.isValueEquals("高中", eduStage)) {
                    stage  = 3;
                }
                else {
                    putErrMsg(thirdData, apiData, "未知学段");
                    thirdDataList.add(thirdData);
                    continue;
                }
                thirdData.put(PRIMARY_KEY_FIELD, HaozhuanyeDataUtils.getThirdSchoolCoursePK(thirdSchoolKey, stage, thirdCourseKey));
                boolean dongniInnerCourse = ThirdDataUtil.isDongniInnerCourse(stage, courseName);
                if (dongniInnerCourse) {
                    putErrMsg(thirdData, apiData, "懂你内置课程无需同步");
                    thirdDataList.add(thirdData);
                    continue;
                }


                thirdData.put("courseName", courseName);
                thirdData.put("courseSort", id);
                thirdData.put("courseType", 2);
                thirdData.put("courseEnName", "");
                thirdData.put("courseCnName", "");
                thirdData.put("stage", stage);
                thirdData.put("artsScience", 0);
                thirdData.put("memberCount", memberCount);
                thirdData.put("memberStr", "");
                thirdDataList.add(thirdData);
            }

        }

        return thirdDataList;
    }
}
