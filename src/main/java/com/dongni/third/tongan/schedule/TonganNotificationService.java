package com.dongni.third.tongan.schedule;

import com.dongni.common.utils.DictUtil;
import com.dongni.third.base.bean.ThirdRepository;
import com.dongni.third.base.progress.create.web.ThirdTaskCreateBeanService;
import com.dongni.third.tongan.util.TonganDataUtils;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020-04-07 17:13
 **/
@Service
public class TonganNotificationService {

    @Autowired
    private ThirdTaskCreateBeanService thirdTaskCreateBeanService;
    @Autowired
    private ThirdRepository thirdRepository;

    public void syncDataByNotification() {

        Long thirdPartyId = TonganDataUtils.getThirdPartyId();

        // 通用的参数
        Integer thirdTaskTypeNotification = DictUtil.getDictValue("thirdTaskType", "notification");
        Map<String, Object> syncCommonParams = MapUtil.of(
                "thirdPartyId", thirdPartyId, "thirdTaskType", thirdTaskTypeNotification,
                "userId", 1, "userName", "admin"
        );

        // 学校同步
        Map<String, Object> syncParams = new HashMap<>(syncCommonParams);
        thirdTaskCreateBeanService.getThirdTaskCreateService(syncParams).createSyncSchool(syncParams);

        List<Map<String,Object>> schoolInfoList = thirdRepository.selectList("ThirdSchoolMapper.getSchoolIds", syncCommonParams);

        // 全量同步
        syncParams = new HashMap<>(syncCommonParams);
        syncParams.put("thirdSchoolIds", schoolInfoList.stream().map(a -> a.get("thirdSchoolId").toString()).collect(Collectors.joining(",")));
        thirdTaskCreateBeanService.getThirdTaskCreateService(syncParams).createSyncSchoolAll(syncParams);
    }

}

