package com.dongni.third.tongan.service.impl;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.common.util.ThirdDataUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdAccountService;
import com.dongni.third.base.service.table.ThirdSchoolService;
import com.dongni.third.tongan.api.TonganDataApi;
import com.dongni.third.tongan.util.TonganDataUtils;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * @ Author     ：guo zhengming
 * @ Date       ：Created in 11:45 2020/4/3
 * @ Description：account表数据同步
 * @ Modified By：
 */
@Service
public class TonganAccountService extends AbstractThirdAccountService {
    @Autowired
    private TonganDataApi tonganDataApi;
//    @Autowired
//    private TonganParentService tonganParentService;
    @Autowired
    private TonganEducationDirectorService tonganEducationDirectorService;

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        String currentThirdSchoolKey = syncParams.getCurrentThirdSchoolKey();
        long thirdTaskId = syncParams.getThirdTaskId();
    
        List<Map<String, Object>> studentList = tonganDataApi.getStudent(thirdTaskId, MapUtil.of("tfk", currentThirdSchoolKey));
        List<Map<String, Object>> transientStudentList = tonganDataApi
                .getStudentTransient(thirdTaskId, MapUtil.of("tfk", currentThirdSchoolKey))
                .stream()
                .filter(item -> ObjectUtil.isValueEquals(item.get("tfk"), currentThirdSchoolKey))
                .filter(item -> ObjectUtil.isValueEquals(item.get("tfk"), item.get("IN_TENANT_FK")))
                .collect(toList());
        List<Map<String, Object>> teacherList = tonganDataApi.getTeacher(thirdTaskId, MapUtil.of("tfk", currentThirdSchoolKey));
        List<Map<String, Object>> educationDirectorList = tonganEducationDirectorService.getApiDataList(syncParams);
    
        studentList.forEach(item -> item.put("_thirdAccountKeyForOldPrimaryKey20201217", item.get("id")));
        transientStudentList.forEach(item -> item.put("_thirdAccountKeyForOldPrimaryKey20201217", item.get("id")));
        teacherList.forEach(item -> item.put("_thirdAccountKeyForOldPrimaryKey20201217", item.get("idcard")));
        
        List<Map<String, Object>> apiDataList = new ArrayList<>();
        apiDataList.addAll(studentList);
        apiDataList.addAll(transientStudentList);
        apiDataList.addAll(teacherList);
        apiDataList.forEach(item -> item.put("_thirdSchoolKey", item.get("tfk").toString()));
        
        // 局领导的thirdSchoolKey为94
        educationDirectorList.forEach(item -> item.put("_thirdSchoolKey", "94"));
        apiDataList.addAll(educationDirectorList);
        
        return apiDataList;
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams, List<Map<String, Object>> apiDataList, Map<String, List<Map<String, Object>>> extendApiData) {
        int accountStatusOn = DictUtil.getDictValue("accountStatus", "on");
        int passwordStatusNormal = DictUtil.getDictValue("passwordStatus", "normal");
        int securityStatusDisabled = DictUtil.getDictValue("securityStatus", "disabled");
        int phoneBindStatusNever = DictUtil.getDictValue("phoneBindStatus", "never");
        
        List<Map<String, Object>> thirdDataList = new ArrayList<>();
        List<Map<String, Object>> needSetThirdSchoolIdList = new ArrayList<>();
        for (Map<String, Object> apiData : apiDataList) {
            Map<String, Object> thirdData = new HashMap<>();
    
            String identityCardNo = ObjectUtil.isNotBlank(apiData.get("idcard"))
                    ? apiData.get("idcard").toString().trim() : null;
            if (identityCardNo == null) {
                putErrMsg(thirdData, apiData, "账号未提供身份证号");
                thirdDataList.add(thirdData);
                continue;
            }
            String identityCardNoUpper = identityCardNo.toUpperCase();
            String thirdSchoolKey = apiData.get("_thirdSchoolKey").toString();
            if ("94".equals(thirdSchoolKey)) {
                // 教育局的 后面会被框架覆盖为当前同步学校的thirdSchoolId
                thirdData.put("thirdSchoolId", 0);
            } else {
                thirdData.put(ThirdSchoolService.THIRD_PRIMARY_KEY_FIELD, TonganDataUtils.getThirdPrimaryKeySchool(thirdSchoolKey));
                needSetThirdSchoolIdList.add(thirdData);
            }
            
            thirdData.put(PRIMARY_KEY_FIELD, TonganDataUtils.getThirdPrimaryKeyAccount(thirdSchoolKey, identityCardNoUpper));
            
            String accountName = "tongan" + identityCardNoUpper;
            thirdData.put("_accountNameSrc", accountName);
            thirdData.put("accountName", accountName);
            thirdData.put("accountNameAes", SensitiveInfoUtil.aesEncrypt(accountName));
            thirdData.put("accountStatus", accountStatusOn);
            thirdData.put("password", "tonganNotPassword");
            thirdData.put("passwordStatus", passwordStatusNormal);
            thirdData.put("securityStatus", securityStatusDisabled);
            thirdData.put("phoneBindStatus", phoneBindStatusNever);
    
            String thirdAccountKeyForOldPrimaryKey20201217 = ObjectUtil.isNotBlank(apiData.get("_thirdAccountKeyForOldPrimaryKey20201217"))
                    ? apiData.get("_thirdAccountKeyForOldPrimaryKey20201217").toString() : null;
            if (thirdAccountKeyForOldPrimaryKey20201217 != null) {
                thirdData.put(OLD_PRIMARY_KEY_LIST_FIELD, Stream.of(
                        TonganDataUtils.getThirdPrimaryKeyAccount_20201217(thirdAccountKeyForOldPrimaryKey20201217)
                ).collect(toList()));
            }
            
            thirdDataList.add(thirdData);
        }
    
        Map<String, String> putThirdDataKeyMapInfoKey = new HashMap<>();
        // put thirdSchoolId
        putThirdDataKeyMapInfoKey.clear();
        putThirdDataKeyMapInfoKey.put("thirdSchoolId", "thirdSchoolId");
        ThirdDataUtil.putInfoToThirdDataList(syncParams, needSetThirdSchoolIdList,
                ThirdSchoolService.THIRD_PRIMARY_KEY_FIELD, "thirdPrimaryKeyList",
                "没有对应的学校", putThirdDataKeyMapInfoKey,
                thirdSchoolService::getThirdPrimaryKeyMapThirdSchoolInfo);
        
        return thirdDataList;
    }
    
    @Override
    protected LinkedHashMap<String, Function<Map<String, Object>, Object>> getSimpleMsgFieldMap() {
        LinkedHashMap<String, Function<Map<String, Object>, Object>> simpleMsgFieldMap = new LinkedHashMap<>();
        simpleMsgFieldMap.put("账户名称", thirdData -> getSensitiveHideInfo(thirdData, "_accountNameSrc", "accountName"));
        return simpleMsgFieldMap;
    }
}
