package com.dongni.third.huaibei.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.third.base.config.ThirdConfig;
import com.dongni.third.huaibei.api.HuaibeiDataApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020-04-13 11:24
 * 调用调试用 可删
 **/
@RestController
@DongniNotRequireLogin
@RequestMapping(ThirdConfig.CONTEXT_PATH + "/huaibei/test")
public class HuaibeiTestController extends BaseController {
    @Autowired
    private HuaibeiDataApi huaibeiDataApi;

    @GetMapping("/school")
    public Response school() {
        return new Response(huaibeiDataApi.getSchool(getParameterMap()));
    }
    
    @GetMapping("/schoolDetail")
    public Response schoolDetail() {
        return new Response(huaibeiDataApi.getSchoolDetail(getParameterMap()));
    }
    
    @GetMapping("/grade")
    public Response grade() {
        return new Response(huaibeiDataApi.getGrade(getParameterMap()));
    }
    
    @GetMapping("/teacher")
    public Response teacher() {
        return new Response(huaibeiDataApi.getTeacher(getParameterMap()));
    }
    
    @GetMapping("/student")
    public Response student() {
        return new Response(huaibeiDataApi.getStudent(getParameterMap()));
    }
    
    @GetMapping("/class")
    public Response clazz() {
        return new Response(huaibeiDataApi.getClass(getParameterMap()));
    }
    
    @GetMapping("/course")
    public Response course() {
        return new Response(huaibeiDataApi.getCourse(getParameterMap()));
    }
    
    @GetMapping("/classTeacher")
    public Response classTeacher() {
        return new Response(huaibeiDataApi.getClassTeacher(getParameterMap()));
    }
}

