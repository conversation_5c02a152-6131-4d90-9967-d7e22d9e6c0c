package com.dongni.third.base.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.third.base.config.ThirdConfig;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/12/4.
 *
 * 第三方学校审核
 */
@RestController
@RequestMapping(ThirdConfig.APP + "/school/check")
public class ThirdSchoolCheckController extends BaseController{
    
    @GetMapping
    public Response getSchoolCheckList() {
        throw new CommonException(ResponseStatusEnum.FAILURE, "暂不支持该功能");
    }

    @PostMapping
    public Response updateSchoolCheckStatus() {
        throw new CommonException(ResponseStatusEnum.FAILURE, "暂不支持该功能");
    }
}
