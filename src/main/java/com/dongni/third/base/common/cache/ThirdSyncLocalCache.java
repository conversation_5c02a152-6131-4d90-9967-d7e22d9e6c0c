package com.dongni.third.base.common.cache;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.LFUCache;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> <br/>
 * @date 2020/12/25 <br/>
 * 同步本地缓存
 */
public class ThirdSyncLocalCache {
    
    /** 本地缓存key容量
     *  默认 128
     */
    public static final int CAPACITY = 8;
    
    /**
     * 本地缓存key的默认超时时间
     * 默认 5 分钟
     * 对于put原则上应该每个都设置，次配置是防止忘记设置的情况
     */
    public static final int DEFAULT_EXPIRE_MILLISECOND = 300_000;
    
    /**
     * key   thirdTaskId
     * value key: 建议使用接口标识+参数  如  同安获取学校信息 "school:${tfk}"
     */
    private static Cache<Long, Map<String, List<Map<String, Object>>>> LOCAL_CACHE =
            new LFUCache<>(CAPACITY, DEFAULT_EXPIRE_MILLISECOND);
    
    /**
     * 获取
     * @param thirdTaskId 同步id
     * @param apiKey      数据标识 建议使用接口标识+参数
     * @return 可能为空
     */
    public static List<Map<String, Object>> get(final long thirdTaskId, final String apiKey) {
        return Optional.ofNullable(LOCAL_CACHE.get(thirdTaskId))
                .map(item -> item.get(apiKey))
                .orElse(null);
    }
    
    /**
     * 获取
     * @param thirdTaskId 同步id
     * @param apiKey      数据标识 建议使用接口标识+参数
     * @param dataListSupplier 如果get(thirdTaskId, apiKey)==null 则去获取
     * @return 数据 不可能为null
     */
    public static List<Map<String, Object>> get(final long thirdTaskId,
                                                final String apiKey,
                                                final Supplier<List<Map<String, Object>>> dataListSupplier) {
        return get(thirdTaskId, apiKey, dataListSupplier, DEFAULT_EXPIRE_MILLISECOND);
    }
    
    /**
     * 获取
     * @param thirdTaskId 同步id
     * @param apiKey      数据标识 建议使用接口标识+参数
     * @param dataListSupplier 如果get(thirdTaskId, apiKey)==null 则去获取，如果获取到的为null，会被设置为[], 表示已经获取过了
     * @param expireMillisecond 过时时间 一般不用传这个 不要设太长
     * @return 数据 不可能为null
     */
    public static List<Map<String, Object>> get(final long thirdTaskId,
                                                final String apiKey,
                                                final Supplier<List<Map<String, Object>>> dataListSupplier,
                                                final long expireMillisecond) {
        List<Map<String, Object>> dataList = get(thirdTaskId, apiKey);
        if (dataList == null) {
            dataList = Optional.ofNullable(dataListSupplier.get()).orElseGet(ArrayList::new);
            set(thirdTaskId, apiKey, dataList, expireMillisecond);
        }
        return dataList;
    }
    
    /**
     * 获取
     * @param thirdTaskId 同步id
     * @param apiKey      数据标识 建议使用接口标识+参数
     * @param dataSupplier 如果get(thirdTaskId, apiKey)==null 则去获取
     * @return 数据
     */
    public static Map<String, Object> getMap(final long thirdTaskId,
                                             final String apiKey,
                                             final Supplier<Map<String, Object>> dataSupplier) {
        return getMap(thirdTaskId, apiKey, dataSupplier, DEFAULT_EXPIRE_MILLISECOND);
    }
    
    /**
     * 获取
     * @param thirdTaskId 同步id
     * @param apiKey      数据标识 建议使用接口标识+参数
     * @param dataSupplier 如果get(thirdTaskId, apiKey)==null 则去获取，
     *                     如果调用方法获取的到的结果是null，则会缓存为[null]，表示已经获取过了
     * @param expireMillisecond 过时时间 一般不用传这个 不要设太长
     * @return 数据
     */
    public static Map<String, Object> getMap(final long thirdTaskId,
                                             final String apiKey,
                                             final Supplier<Map<String, Object>> dataSupplier,
                                             final long expireMillisecond) {
        List<Map<String, Object>> dataList = get(thirdTaskId, apiKey);
        if (dataList == null) {
            dataList = Stream.of(dataSupplier.get()).collect(Collectors.toList());
            set(thirdTaskId, apiKey, dataList, expireMillisecond);
        }
        return dataList.get(0);
    }
    
    /**
     * 设置
     * @param thirdTaskId 同步id
     * @param apiKey      数据标识 建议使用接口标识+参数
     * @param dataList    设置的对象
     * @param expireMillisecond 过期时间 必须提供 小于等于0不处理将直接返回
     */
    public static void set(final long thirdTaskId,
                           final String apiKey,
                           final List<Map<String, Object>> dataList,
                           final long expireMillisecond) {
        if (expireMillisecond <= 0) { return; }
        Map<String, List<Map<String, Object>>> cacheData = Optional.ofNullable(LOCAL_CACHE.get(thirdTaskId)).orElseGet(HashMap::new);
        cacheData.put(apiKey, Optional.ofNullable(dataList).orElseGet(ArrayList::new));
        LOCAL_CACHE.put(thirdTaskId, cacheData, expireMillisecond);
    }
    
    /**
     * 删除key
     * @param thirdTaskId 同步id
     */
    public static void del(long thirdTaskId) {
        LOCAL_CACHE.remove(thirdTaskId);
    }
    
    /**
     * 刷新过期的key
     *    一般情况下可以不用调用
     *    如果需要 可以在定时任务里面进行
     * @return 清理的缓存对象个数
     */
    public static int prune() {
        return LOCAL_CACHE.prune();
    }
    
    /**
     * 清空
     */
    public static void flush() {
        LOCAL_CACHE.clear();
    }
}
