package com.dongni.third.base.progress.create.web.impl;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.third.base.bean.ThirdRepository;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.ThirdTaskManager;
import com.dongni.third.base.progress.consumer.impl.ThirdTaskStartHaozhuanyeService;
import com.dongni.third.base.progress.create.web.IThirdTaskCreateService;
import com.dongni.third.base.service.ThirdTaskService;
import com.dongni.third.base.service.table.ThirdSchoolService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> <br/>
 * @date 2020/12/11 <br/>
 *
 */
public abstract class AbstractTaskCreateService implements IThirdTaskCreateService {
    
    @Autowired
    protected ThirdRepository thirdRepository;
    
    @Autowired
    protected ThirdSchoolService thirdSchoolService;
    @Autowired
    private ThirdTaskService thirdTaskService;
    @Autowired
    ThirdTaskStartHaozhuanyeService thirdTaskStartHaozhuanyeService;
    
    @Override
    public void createSyncSchool(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("thirdPartyId")
                .verify();
        checkThirdPartyId(params);
        
        int syncTypeAll = DictUtil.getDictValue("syncType", "all");
        // 前端不会传 thirdTaskType=normal手动更新字段
        int thirdTaskType = getThirdTaskTypeDefaultNormal(params.get("thirdTaskType"));
        
        params.put("thirdTaskName", "学校列表和非校用户");
        params.put("thirdTaskType", thirdTaskType);
        params.put("syncType", syncTypeAll);
        ThirdTaskManager.create(params);
    }
    
    @Override
    public void createSyncSchoolAll(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("thirdPartyId")
                .isNotBlank("thirdSchoolIds")
                .verify();
        checkThirdPartyId(params);
        
        int syncTypeAll = DictUtil.getDictValue("syncType", "all");
        // 前端不会传 thirdTaskType=normal手动更新字段
        int thirdTaskType = getThirdTaskTypeDefaultNormal(params.get("thirdTaskType"));
        
        params.put("syncType", syncTypeAll);
        params.put("thirdTaskType", thirdTaskType);
        
        // thirdSchoolId -> schoolName
        Map<Long, String> thirdSchoolIdMapSchoolName = getThirdSchoolIdMapSchoolName(params);
    
        for (Map.Entry<Long, String> entry : thirdSchoolIdMapSchoolName.entrySet()) {
            Map<String, Object> createParams = new HashMap<>(params);
            createParams.put("thirdSchoolId", entry.getKey());
            createParams.put("thirdTaskName", entry.getValue());
            ThirdTaskManager.create(createParams);
//            Map<String, Object> task = thirdTaskService.getThirdTaskParseByThirdTaskIdForDebugOnly(MapUtil.getLong(createParams,"thirdTaskId"));
//            thirdTaskStartHaozhuanyeService.start(task);
        }
    }
    
    @Override
    public void createSyncSchoolAllByThirdSchoolPrimaryKey(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("thirdPartyId")
                .isNotBlank("thirdSchoolPrimaryKeys")
                .verify();
        checkThirdPartyId(params);
        
        int syncTypeAll = DictUtil.getDictValue("syncType", "all");
        // 前端不会传 thirdTaskType=normal手动更新字段
        int thirdTaskType = getThirdTaskTypeDefaultNormal(params.get("thirdTaskType"));
        
        params.put("syncType", syncTypeAll);
        params.put("thirdTaskType", thirdTaskType);
        
        // thirdSchoolId -> schoolName
        Map<Long, String> thirdSchoolIdMapSchoolName = getThirdSchoolIdMapSchoolName(params);
        
        // thirdSchoolPrimaryKey -> schoolInfo(可能还没同步过来所以找不到){ thirdSchoolId, schoolName }
        Map<String, Map<String, Object>> thirdSchoolPrimaryKey2SchoolInfo = new HashMap<>();
        
        for (Map.Entry<String, Map<String, Object>> entry : thirdSchoolPrimaryKey2SchoolInfo.entrySet()) {
            String thirdSchoolPrimaryKey = entry.getKey();
            Map<String, Object> schoolInfo = entry.getValue();
            Map<String, Object> createParams = new HashMap<>(params);
            if (MapUtils.isEmpty(schoolInfo)) {
                createParams.put(SyncParams.CommonKey.THIRD_SCHOOL_PRIMARY_KEY_FIELD, thirdSchoolPrimaryKey);
                createParams.put("thirdTaskName", "通过第三方学校主键:" + thirdSchoolPrimaryKey);
            } else {
                createParams.put("thirdSchoolId", MapUtil.getLong(schoolInfo, "thirdSchoolId"));
                createParams.put("thirdTaskName", MapUtil.getTrim(schoolInfo, "thirdTaskName"));
            }
            ThirdTaskManager.create(createParams);
        }
    }
    
    /**
     * 校验thirdPartyId
     *    如果thirdPartyId与service的thirdPartyId不匹配会异常
     *    异常: 获取serviceThirdPartyId失败 可能是因为系统还未完全初始化导致获取字典失败 可重试
     *    异常: ThirdPartyId匹配失败        正常使用不会出现，在程序开发时忘记更改某些数据时会出现
     * @param params thirdPartyId
     */
    protected void checkThirdPartyId(Map<String, Object> params) {
        Verify.of(params).isValidId("thirdPartyId").verify();
        long thirdPartyId = MapUtil.getLong(params, "thirdPartyId");
        long serviceThirdPartyId;
        try {
            serviceThirdPartyId = getThirdPartyId();
        } catch (Exception ignore) {
            throw new CommonException(ResponseStatusEnum.FAILURE,
                    "获取serviceThirdPartyId失败, 请重试或联系管理员! thirdPartyId: " + thirdPartyId);
        }
        if (thirdPartyId != serviceThirdPartyId) {
            throw new CommonException(ResponseStatusEnum.FAILURE,
                    "ThirdPartyId匹配失败, 请联系管理员! thirdPartyId: " + thirdPartyId + "; serviceThirdPartyId: " + thirdPartyId);
        }
    }
    
    /**
     * 获取thirdTaskType
     * @param thirdTaskType 允许为空 如果为空 默认为thirdTaskType.normal
     * @return thirdTaskType
     */
    protected int getThirdTaskTypeDefaultNormal(Object thirdTaskType) {
        return Optional.ofNullable(thirdTaskType)
                .map(MapUtil::getIntNullable)
                .orElseGet(() -> DictUtil.getDictValue("thirdTaskType", "normal"));
    }
    
    /**
     * 获取学校id与学校名称的对应关系
     * @param params thirdPartyId
     *               thirdSchoolIds 例: "123,456,789"
     * @return {thirdSchoolId: schoolName}
     */
    protected Map<Long, String> getThirdSchoolIdMapSchoolName(Map<String, Object> params) {
        List<Map<String, Object>> thirdSchoolList = thirdSchoolService.getThirdSchoolByIds(params);
        return thirdSchoolList.stream()
                .collect(toMap(item -> MapUtil.getLong(item, "thirdSchoolId"), item -> item.get("schoolName").toString()));
    }
}
