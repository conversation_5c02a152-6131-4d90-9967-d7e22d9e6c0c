package com.dongni.third.base.progress.consumer.impl;

import com.dongni.third.base.progress.ThirdTaskManager;
import com.dongni.third.base.progress.consumer.AbstractThirdTaskStartService;
import com.dongni.third.yuexun.service.*;
import com.dongni.third.yuexun.util.YueXunDataUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021年08月06日
 */
@Service
public class ThirdTaskStartYueXunService extends AbstractThirdTaskStartService {

    @Autowired
    private YueXunSchoolService yueXunSchoolService;
    @Autowired
    private YueXunGradeService yueXunGradeService;
    @Autowired
    private YueXunSchoolStageService yueXunSchoolStageService;
    @Autowired
    private YueXunStudentService yueXunStudentService;
    @Autowired
    private YueXunClassService yueXunClassService;
    @Autowired
    private YueXunAccountService yueXunAccountService;
    @Autowired
    private YueXunClassStudentService yueXunClassStudentService;
    @Autowired
    private YueXunTeacherService yueXunTeacherService;
    @Autowired
    private YueXunUserService yueXunUserService;
    @Autowired
    private YueXunClassTeacherService yueXunClassTeacherService;
    @Autowired
    private YueXunCourseService yueXunCourseService;
    @Autowired
    private YueXunEducationDirectorService yueXunEducationDirectorService;
    @Autowired
    private YueXunSchoolPrincipalService yueXunSchoolPrincipalService;
    @Autowired
    private YueXunSchoolPrincipalCourseService yueXunSchoolPrincipalCourseService;
    @Autowired
    private YueXunParentService yueXunParentService;
    @Autowired
    private YueXunParentStudentService yueXunParentStudentService;
    @Autowired
    private YueXunGradeDirectorService yueXunGradeDirectorService;
    @Autowired
    private YueXunInstructorService yueXunInstructorService;
    @Autowired
    private YueXunInstructorCourseService yueXunInstructorCourseService;

    @Override
    public long getThirdPartyId() {
        return YueXunDataUtils.getThirdPartyId();
    }

    @Override
    protected void startBySchool(Map<String, Object> task) {
        ThirdTaskManager
                .parse(task)
                .add(yueXunSchoolService)
                .add(yueXunGradeService)
                .add(yueXunSchoolStageService)
                .add(yueXunSchoolPrincipalService)
                .add(yueXunSchoolPrincipalCourseService)
                .add(yueXunStudentService)
                .add(yueXunClassService)
                .add(yueXunCourseService)
                .add(yueXunAccountService)
                .add(yueXunClassStudentService)
                .add(yueXunClassTeacherService)
                .add(yueXunParentService)
                .add(yueXunParentStudentService)
                .add(yueXunTeacherService)
                .add(yueXunUserService)
                .add(yueXunInstructorService)
                .add(yueXunInstructorCourseService)
                .add(yueXunEducationDirectorService)
                .add(yueXunGradeDirectorService)
                .startBySchool();
    }

    @Override
    protected void startByAll(Map<String, Object> task) {
        ThirdTaskManager.parse(task)
                .add(yueXunSchoolService)
                .startByAll();
    }
}
