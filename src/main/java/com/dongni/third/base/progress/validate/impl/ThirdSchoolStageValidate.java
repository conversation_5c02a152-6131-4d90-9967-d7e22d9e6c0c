package com.dongni.third.base.progress.validate.impl;

import com.dongni.third.base.bean.ThirdRepository;
import com.dongni.third.base.progress.ThirdTable;
import com.dongni.third.base.progress.validate.ThirdIdExistValidateService;
import com.dongni.third.base.progress.validate.ThirdValidate;
import com.dongni.third.base.progress.validate.ThirdValidateService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.dongni.third.base.progress.validate.ThirdDataUkDuplicateStrategy.USE_TOP;

/**
 * <AUTHOR> <br/>
 * @date 2020/12/28 <br/>
 *
 */
@Service
public class ThirdSchoolStageValidate implements ThirdValidate {
    
    @Autowired
    private ThirdRepository thirdRepository;
    @Autowired
    private ThirdIdExistValidateService thirdIdExistValidateService;
    @Autowired
    private ThirdValidateService thirdValidateService;
    
    @Override
    public void validate(Map<String, Object> params) {
        List<Map<String, Object>> validateDataList = thirdRepository.selectList("ThirdSchoolStageMapper.getSchoolStageEffective", params);
        if (CollectionUtils.isEmpty(validateDataList)){
            return;
        }
    
        List<Map<String, Object>> validator = new ArrayList<>();
    
        // check fk
        validator.addAll(thirdIdExistValidateService.validateThirdBizIdExist(params, validateDataList,
                "thirdSchoolId"
        ));
        
        // check uk
        validator.addAll(
                thirdValidateService.validateUk(validateDataList, "学校id-年段", USE_TOP,
                "thirdSchoolId", "stage"
        ));
    
        params.put("tableName", ThirdTable.SCHOOL_STAGE.getTableName());
        thirdValidateService.updateThirdItemError(params, validator);
    }
}
