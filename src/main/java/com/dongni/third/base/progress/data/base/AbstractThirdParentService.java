package com.dongni.third.base.progress.data.base;

import com.dongni.basedata.third.service.ThirdSyncService;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.ThirdTable;
import com.dongni.third.base.progress.data.AbstractThirdDataService;
import com.dongni.third.base.progress.validate.impl.ThirdParentValidate;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 家长
 *
 * <AUTHOR>
 * @date 2019/07/29 11:14
 */
@Service
public abstract class AbstractThirdParentService extends AbstractThirdDataService {

    @Autowired
    private ThirdParentValidate thirdParentValidate;

    @Autowired
    private ThirdSyncService thirdSyncService;
    
    @Override
    public ThirdTable getThirdTable() {
        return ThirdTable.PARENT;
    }
    /**
     * 根据学校ID从中间库获取学校下的当前相关的学校数据
     *
     * @return
     */
    @Override
    public String getSchoolRelateDataSqlStatement() {
        return "ThirdParentMapper.getParentBySchoolId";
    }

    /**
     * 新增到中间库mysql
     *
     * @return
     */
    @Override
    public String getInsertSqlStatement() {
        return "ThirdParentMapper.insertParent";
    }

    /**
     * 更新到中间库mysql
     *   使用先删后插代替了
     * @return
     */
    @Override
    public String getUpdateSqlStatement() {
        return "ThirdParentMapper.updateParent";
    }

    /**
     * 从中间库mysql取出数据的sql声明
     *
     * @return
     */
    @Override
    public String getQuerySqlStatement() {
        return "ThirdParentMapper.getParentEffective";
    }

    /**
     * 数据校验
     *
     * @param syncParams syncParams
     */
    @Override
    public void validate(SyncParams syncParams) {
        thirdParentValidate.validate(syncParams);
    }


    @Override
    public List<String> getUniqueKeyList() {
        return Lists.newArrayList("parentPhoneAes");
    }

    @Override
    public String getProdSqlStatement() {
        return "ThirdSelectMapper.selectParent";
    }

    @Override
    protected String getProdSqlStatementByLogic() {
        return null;
    }

    @Override
    public void syncProdData(Map<String, Object> params) {
        thirdSyncService.syncParent(params);
    }

    @Override
    public boolean getGlobalFlag() {
        return true;
    }
}
