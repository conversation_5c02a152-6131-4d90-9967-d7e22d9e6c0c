package com.dongni.third.base.service.table;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/12/11 <br/>
 *
 */
@Service
public class ThirdTeacherService extends AbstractThirdTableService {
    
    public static final String THIRD_PRIMARY_KEY_FIELD = "__thirdTeacherPrimaryKey";
    
    /**
     * 获取thirdTeacher信息
     * @param params thirdPartyId thirdPrimaryKeyList
     * @return thirdPrimaryKey -> thirdTeacherInfo{thirdBizId,==thirdTeacherId,teacherName,thirdPrimaryKey,schoolName}
     */
    public Map<String, Map<String, Object>> getThirdPrimaryKeyMapTeacherInfo(Map<String, Object> params) {
        return getThirdPrimaryKeyMapBizId(params, "ThirdTeacherMapper.getTeacherInThirdPrimaryKeyList");
    }

    /**
     * 通过中间库ID获取老师信息
     * @param thirdTeacherIds 中间库主键列表
     * @return
     */
    public List<Map<String, Object>> getThirdTeacherInfoListByIds(List<Long> thirdTeacherIds) {
        if (CollectionUtils.isEmpty(thirdTeacherIds)) {
            return Lists.newArrayList();
        }

        return thirdRepository.selectList("ThirdTeacherMapper.getThirdTeacherInfoListByIds", thirdTeacherIds);
    }

}
