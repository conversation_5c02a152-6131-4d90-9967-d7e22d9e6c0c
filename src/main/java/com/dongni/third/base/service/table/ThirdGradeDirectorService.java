package com.dongni.third.base.service.table;

import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/12/11 <br/>
 *
 */
@Service
public class ThirdGradeDirectorService extends AbstractThirdTableService {
    
    public static final String THIRD_PRIMARY_KEY_FIELD = "__thirdGradeDirectorPrimaryKey";
    
    /**
     * 获取thirdGradeDirector信息
     * @param params thirdPartyId thirdPrimaryKeyList
     * @return thirdPrimaryKey -> thirdEducationDirectorInfo{
     *                               thirdGradeDirectorId,
     *                               thirdBizId,
     *                               thirdPrimaryKey,
     *                               schoolName
     *                            }
     */
    public Map<String, Map<String, Object>> getThirdPrimaryKeyMapGradeDirectorInfo(Map<String, Object> params) {
        return getThirdPrimaryKeyMapBizId(params, "ThirdGradeDirectorMapper.getGradeDirectorInThirdPrimaryKeyList");
    }
}