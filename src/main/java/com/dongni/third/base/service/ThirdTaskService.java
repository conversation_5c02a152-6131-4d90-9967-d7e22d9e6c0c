package com.dongni.third.base.service;

import cn.hutool.core.util.StrUtil;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.third.base.bean.ThirdRepository;
import com.dongni.third.base.progress.ThirdTable;
import com.dongni.third.base.progress.ThirdTaskStep;
import com.dongni.third.base.progress.debug.ThirdDebug;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import com.pugwoo.wooutils.json.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * Created by Heweipo on 2019/8/9.
 *
 * 第三方同步任务列表
 *
 * t_third_task         任务
 *   third_task_id
 *   third_task_name
 *   third_task_type
 *   third_party_id
 *   sync_params
 *   third_task_status
 *   total_step
 *   step
 *   err_msg
 *   creator_id
 *   creator_name
 *   create_date_time
 *   modifier_id
 *   modifier_name
 *   modify_date_time
 *
 */
@Service
public class ThirdTaskService {

    private static final Logger log = LoggerFactory.getLogger(ThirdTaskService.class);

    @Autowired
    private ThirdRepository thirdRepository;

    @Autowired
    private BaseDataRepository baseDataRepository;
    
    @Autowired
    private ThirdTaskItemService thirdTaskItemService;
    
    @Autowired
    private ThirdTaskInvalidService thirdTaskInvalidService;
    
    // --------------------------------------------------------------------------------------- select start
    
    /**
     * 获取第一条就绪的同步任务
     *    third_task_status = 0 就绪的
     *    order by createDateTime, third_task_id
     *    limit 1
     *  注意: 仅查询，不会更改状态，所以请自行加锁执行，避免同时执行同一任务
     * @return {} nullable
     *    thirdTaskId
     *    thirdTaskName
     *    thirdTaskType
     *    thirdPartyId
     *    syncParams
     */
    public Map<String, Object> getFirstThirdTask() {
        return getFirstThirdTask(null);
    }
    
    /**
     * 获取第一条就绪的同步任务
     *    third_task_status = 0 就绪的
     *    order by createDateTime, third_task_id
     *    limit 1
     *  注意: 仅查询，不会更改状态，所以请自行加锁执行，避免同时执行同一任务
     * @param thirdTaskStatus nullable 预置参数 任务同步状态默认为ready
     *                        调试时调用方可以传入其他值，避免任务被抢走
     * @return {} nullable
     *    thirdTaskId
     *    thirdTaskName
     *    thirdTaskType
     *    thirdPartyId
     *    syncParams
     */
    public Map<String, Object> getFirstThirdTask(Integer thirdTaskStatus) {
        Map<String, Object> params = new HashMap<>();
        params.put("thirdTaskStatus", getThirdTaskStatusDefaultReady(thirdTaskStatus));
        return thirdRepository.selectOne("ThirdTaskMapper.getFirst", params);
    }
    
    /**
     * 获取thirdTaskId同步任务
     *  注意: 仅查询，不会更改状态，所以请自行加锁执行，避免同时执行同一任务
     * @param params thirdTaskId
     * @return {} nullable
     *    thirdTaskId
     *    thirdTaskName
     *    thirdTaskType
     *    thirdPartyId
     *    syncParams
     */
    public Map<String, Object> getTaskByThirdTaskId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("thirdTaskId")
                .verify();
        return thirdRepository.selectOne("ThirdTaskMapper.getTaskByThirdTaskId", params);
    }
    
    /**
     *
     * @param thirdTaskStatus
     * @return
     *   thirdTaskId
     *   "thirdTaskName"
     *   "thirdTaskType"
     *   "thirdPartyId"
     *             --- 以下为 syncParams 解析出来的 根据业务不同 可能会有不同的值 "syncParams"已被remove掉
     *   ------------------------------- 通用的
     *   "syncType": 1,
     *   "thirdSchoolId": 1111,
     *   "currentThirdSchoolKey": "194",
     *   "currentThirdSchoolId": 1111,
     *   "userId": 3,
     *   "userName": "同步管理员",
     *   "userType": 17,
     *   ------------------------------- 可能因一些平台不同携带的乱七八糟的参数
     *   "thirdSchoolName": "厦门市梧侣学校",
     *   "thirdSchoolIds": 1111
     *   "accountId": 3,
     *   "clientType": 1,
     */
    public Map<String, Object> getFirstThirdTaskParse(Integer thirdTaskStatus) {
        while (true) {
            Map<String, Object> firstThirdTask = getFirstThirdTask(thirdTaskStatus);
            if (firstThirdTask == null) {
                return null;
            }
            Map<String, Object> syncParams;
            try {
                syncParams = JSON.parseToMap(firstThirdTask.remove("syncParams").toString());
            } catch (Exception e) {
                updateThirdTaskFailure(firstThirdTask, "解析syncParams出错: " + e.getMessage());
                continue;
            }
            firstThirdTask.putAll(syncParams);
            updateThirdTaskDoing(firstThirdTask);
            return firstThirdTask;
        }
    }
    
    /**
     * 获取指定的信息并解析
     * @param thirdTaskId thirdTaskId
     * @return
     *   thirdTaskId
     *   "thirdTaskName"
     *   "thirdTaskType"
     *   "thirdPartyId"
     *             --- 以下为 syncParams 解析出来的 根据业务不同 可能会有不同的值 "syncParams"已被remove掉
     *   ------------------------------- 通用的
     *   "syncType": 1,
     *   "thirdSchoolId": 1111,
     *   "currentThirdSchoolKey": "194",
     *   "currentThirdSchoolId": 1111,
     *   "userId": 3,
     *   "userName": "同步管理员",
     *   "userType": 17,
     *   ------------------------------- 可能因一些平台不同携带的乱七八糟的参数
     *   "thirdSchoolName": "厦门市梧侣学校",
     *   "thirdSchoolIds": 1111
     *   "accountId": 3,
     *   "clientType": 1,
     */
    public Map<String, Object> getThirdTaskParseByThirdTaskIdForDebugOnly(long thirdTaskId) {
        Map<String, Object> thirdTask = getTaskByThirdTaskId(MapUtil.of("thirdTaskId", thirdTaskId));
        if (MapUtils.isEmpty(thirdTask)) {
            throw new CommonException(ResponseStatusEnum.FAILURE, "获取不到任务: thirdTaskId: " + thirdTaskId);
        }

        Map<String, Object> syncParams;
        try {
            syncParams = JSON.parseToMap(thirdTask.remove("syncParams").toString());
        } catch (Exception e) {
            updateThirdTaskFailure(thirdTask, "解析syncParams出错: " + e.getMessage());
            throw new CommonException(ResponseStatusEnum.FAILURE, e);
        }
        thirdTask.putAll(syncParams);
        updateThirdTaskDoing(thirdTask);
        return thirdTask;
        
    }
    
    /**
     * 获取同步任务列表
     *    SyncAdmin同步管理员账号 同步任务列表/同步消息列表
     * @param params [thirdPartyId]   同步平台  例: 4dict.thirdParty.tongan
     *               [thirdTaskType]  同步类型  1dict.thirdTaskType.normal 普通 同步任务列表 在同步学校中点击同步时产生的类型
     *                                         2dict.thirdTaskType.notification 推送消息 同步消息列表 由第三方通知懂你进行数据的类型
     * @return totalCount, thirdTask: [{}, {}, ...]
     * {
     *     thirdTaskId,     任务编号
     *     thirdTaskName,   任务名称
     *     thirdPartyId,    同步系统 dict.thirdParty
     *     thirdTaskStatus, 状态    dict.thirdTaskStatus  0ready就绪 1doing同步中 2done同步完成 -1failure失败
     *     totalStep,       总进度
     *     step,            当前进度
     *     creatorId,
     *     creatorName,
     *     createDateTime   任务时间
     * }
     */
    public Map<String, Object> getThirdTask(Map<String, Object> params) {
        Integer totalCount = thirdRepository.selectOne("ThirdTaskMapper.getThirdTaskCount", params);
        List<Map<String, Object>> thirdTask = null;
        if (totalCount != null && totalCount > 0) {
            thirdTask =  thirdRepository.selectList("ThirdTaskMapper.getThirdTask", params);
        }
        return MapUtil.of("totalCount", totalCount, "thirdTask", thirdTask);
    }

    /**
     * 获取同步任务的信息 单表信息 t_third_task
     *    主要用于在同步任务列表，重新获取正在同步的任务的状态，正在同步中的任务有个按钮用于刷新进度
     * @param params thirdTaskId
     * @return {}
     *  thirdTaskId, thirdTaskName, thirdPartyId, syncParams, thirdTaskStatus, totalStep, step, errMsg,
     *  creatorId, creatorName, createDateTime, modifierId, modifierName, modifyDateTime
     */
    public Map<String,Object> getThirdTaskInfo(Map<String,Object> params){
        Verify.of(params).isValidId("thirdTaskId").verify();
        return thirdRepository.selectOne("ThirdTaskMapper.getThirdTaskInfo",params);
    }

    /**
     * 获取同步任务的明细
     *   同步明细页-中间库  同步任务列表明细按钮进入
     * @param params thirdTaskId
     * @return 任务明细
     * thirdTaskId, thirdTaskName, thirdPartyId, syncParams, thirdTaskStatus, totalStep, step, errMsg,
     * creatorId, creatorName, createDateTime, modifierId, modifierName, modifyDateTime
     * table: [{}, {}, ...]
     *    {tableName, tableNameAlias, dataStatus, total}
     */
    public Map<String, Object> getThirdTaskDetail(Map<String, Object> params) {
        Verify.of(params).isValidId("thirdTaskId").verify();
        Map<String, Object> rs = thirdRepository.selectOne("ThirdTaskMapper.getThirdTaskInfo", params);
        if (MapUtils.isEmpty(rs)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "任务不存在或已被删除");
        }
    
        // 获取合法的数据
        List<Map<String, Object>> itemCountList = thirdTaskItemService.getItemCountGroupByTableAndStatus(params);
        itemCountList = CollectionUtils.isEmpty(itemCountList) ? new ArrayList<>() : itemCountList;
    
        // tableName -> tableNameAlias  没啥的，t_third_task_invalid没有tableNameAlias，只是为了填充数据而已
        Map<String, String> tableNameMapTableNameAlias = itemCountList.stream()
                .collect(toMap(
                        item -> item.get("tableName").toString(), item -> item.get("tableNameAlias").toString(),
                        (oldValue, newValue) -> oldValue
                ));
    
        // 获取不合法的数据
        List<Map<String, Object>> invalidCountList = thirdTaskInvalidService.getInvalidCountGroupByTable(params);
        if (CollectionUtils.isNotEmpty(invalidCountList)) {
            // 填充 t_third_task_invalid 没有的字段
            final int apiDataIllegal = DictUtil.getDictValue("dataStatus", "apiDataIllegal");
            invalidCountList.forEach(item -> {
                String tableName = item.get("tableName").toString();
                item.put("dataStatus", apiDataIllegal);
                String tableNameAlias = tableNameMapTableNameAlias.get(tableName);
                if (tableNameAlias == null) {
                    tableNameAlias = Optional.ofNullable(ThirdTable.getByTableName(tableName))
                            .map(ThirdTable::getTableNameAlias)
                            .orElse(tableName);
                }
                item.put("tableNameAlias", tableNameAlias);
            });
            itemCountList.addAll(invalidCountList);
        }
    
    
        Map<String, Integer> tableNameMapSortValue = ThirdTable.getTableNameMapSortValue();
        itemCountList.sort(Comparator
                .comparing(item -> Optional
                        .ofNullable(tableNameMapSortValue.get(((Map) item).get("tableName").toString()))
                        .orElse(Integer.MAX_VALUE)
                )
                .thenComparing(item -> ((Map) item).get("tableName").toString())
                .thenComparing(item -> MapUtil.getInt(((Map) item), "dataStatus")));
        rs.put("table", itemCountList);
        return rs;
    }
    
    /**
     * 获取任务明细
     *   同步明细-中间库 详情的查看
     * @param params thirdTaskId dataStatus [tableName]
     * @return {totalCount, thirdTaskItem: [{}, {}]}
     *   {
     *     thirdTaskItemId, 明细id  如果dataStatus==dict.dataStatus.illegal 为 t_third_task_invalid.third_task_invalid_id
     *                              如果dataStatus!=dict.dataStatus.illegal 为 t_third_task_item.third_task_item_id
     *     thirdTaskId,     任务id
     *     tableName,       同步表名 例t_third_account
     *     tableNameAlias,  同步表名 例账户
     *     thirdBizId,      thirdBizId 对应表的主键 如 t_third_account.third_account_id
     *     dataStatus,      数据类型
     *     errMsg,          错误信息
     *     creatorId,       创建人
     *     creatorName,     创建人
     *     createDateTime   创建时间
     *   }
     */
    public Map<String,Object> getThirdTaskItem(Map<String,Object> params){
        Verify.of(params).isValidId("thirdTaskId").isNumeric("dataStatus").verify();

        // 是否为不合法数据 不合法数据在t_third_task_invalid 而合法数据在t_third_task_item
        final int apiDataIllegal = DictUtil.getDictValue("dataStatus", "apiDataIllegal");
        int dataStatus = Integer.parseInt(params.get("dataStatus").toString());
        boolean isApiDataIllegal = apiDataIllegal == dataStatus;
        
        int totalCount;
        if (isApiDataIllegal) {
            totalCount = thirdTaskInvalidService.getInvalidCount(params);
        } else {
            totalCount = thirdTaskItemService.getItemCount(params);
        }
        
        List<Map<String,Object>> thirdTaskItemList = null;
        if (totalCount > 0) {
            if (isApiDataIllegal) {
                thirdTaskItemList = thirdTaskInvalidService.getInvalidList(params);
                // 非法数据要填充dataStatus为illegal, 将tablePrimaryId字段名填充为item的
                thirdTaskItemList.forEach(item -> {
                    item.put("dataStatus", apiDataIllegal);
                    item.put("thirdTaskItemId", item.remove("thirdTaskInvalidId"));
                    String tableName = item.get("tableName").toString();
                    String tableNameAlias = Optional.ofNullable(ThirdTable.getByTableName(tableName))
                                .map(ThirdTable::getTableNameAlias)
                                .orElse(tableName);
                    item.put("tableNameAlias", tableNameAlias);
                    
                });
            } else {
                thirdTaskItemList = thirdTaskItemService.getItemList(params);
            }
        }
        
        return MapUtil.of("totalCount", totalCount, "thirdTaskItem", thirdTaskItemList);
    }
    
    // --------------------------------------------------------------------------------------- select end
    
    // --------------------------------------------------------------------------------------- insert start
    
    /**
     * 创建任务
     *    仅供 ThirdTaskManager 调用，理论上其为唯一调用方
     * @param params thirdTaskName thirdPartyId thirdTaskType
     *               [thirdTaskStatus] 预置参数 任务同步状态默认为ready
     *                                 调试时调用方可以传入其他值，避免任务被抢走
     */
    @Transactional(ThirdRepository.TRANSACTION)
    public long insertThirdTask(Map<String,Object> params){
        Verify.of(params)
                .isValidId("thirdPartyId")
                .isNumeric("thirdSchoolId")
                .isNumeric("thirdTaskType")
                .isNotBlank("thirdTaskName")
                .verify();
        
        // syncParams 存储了一些额外信息，消费任务的时候会用到
        Map<String, Object> syncParams = new HashMap<>(params);
        syncParams.remove("thirdPartyId");
        syncParams.remove("thirdTaskType");
        syncParams.remove("thirdTaskName");
        
        Map<String, Object> taskParams = new HashMap<>(params);
        taskParams.put("syncParams", JSON.toJson(syncParams));
        taskParams.put("thirdTaskStatus", getThirdTaskStatusDefaultReady(params.get("thirdTaskStatus")));
        taskParams.put("totalStep", ThirdTaskStep.getTotalStep());
        taskParams.put("step", 0);
        taskParams.put("currentTime", DateUtil.getCurrentDateTime());
        
        thirdRepository.update("ThirdTaskMapper.insertThirdTask",taskParams);
        return MapUtil.getLong(taskParams, "thirdTaskId");
    }
    
    /**
     * 获取thirdTaskStatus
     * @param thirdTaskStatus 允许为空 如果为空 默认为thirdTaskStatus.ready
     * @return thirdTaskType
     */
    protected int getThirdTaskStatusDefaultReady(Object thirdTaskStatus) {
        return Optional.ofNullable(thirdTaskStatus)
                .map(MapUtil::getIntNullable)
                .orElseGet(() -> DictUtil.getDictValue("thirdTaskStatus", ThirdDebug.getThirdTaskStatusDefault()));
    }
    
    // --------------------------------------------------------------------------------------- insert end
    
    // --------------------------------------------------------------------------------------- update start
    
    /**
     * 更新任务信息
     * @param params thirdTaskId,
     *               [thirdTaskStatus],  同步状态
     *               [step],             第几步
     *               [totalStep],        总共几步 (正常来说，这个字段是不需要更新的)
     *               [errMsg]            错误信息
     */
    public void updateThirdTask(Map<String,Object> params){
        Verify.of(params)
                .isValidId("thirdTaskId")
                .verify();
        params.putIfAbsent("userId", 3);
        params.putIfAbsent("userName", "定时任务");
        params.put("currentTime",DateUtil.getCurrentDateTime());
        thirdRepository.update("ThirdTaskMapper.updateThirdTask",params);
    }
    
    public void updateThirdTaskFailure(Map<String,Object> params, String errMsg){
        Verify.of(params).isValidId("thirdTaskId").verify();
        Map<String, Object> tempParams = new HashMap<>(params);
        tempParams.put("thirdTaskStatus", DictUtil.getDictValue("thirdTaskStatus", "failure"));
        tempParams.put("errMsg", errMsg);
        updateThirdTask(tempParams);
    }
    
    public void updateThirdTaskDoing(Map<String,Object> params){
        Verify.of(params).isValidId("thirdTaskId").verify();
        Map<String, Object> tempParams = new HashMap<>(params);
        putThirdTaskStatusDoing(tempParams);
        updateThirdTask(tempParams);
    }
    
    public void updateThirdTaskDone(Map<String,Object> params){
        Verify.of(params).isValidId("thirdTaskId").verify();
        Map<String, Object> tempParams = new HashMap<>(params);
        tempParams.put("thirdTaskStatus", DictUtil.getDictValue("thirdTaskStatus", "done"));
        updateThirdTask(tempParams);
    }
    // --------------------------------------------------------------------------------------- update end
    
    // --------------------------------------------------------------------------------------- delete start
    // 没有删除 t_third_task 没有删除
    // --------------------------------------------------------------------------------------- delete end
    
    /**
     * 给params put一个thirdTaskStatus
     * @param params 不会检验是否为null
     */
    public static void putThirdTaskStatusDoing(Map<String, Object> params) {
        params.put("thirdTaskStatus", DictUtil.getDictValue("thirdTaskStatus", "doing"));
    }
    
//    /**
//     * 开始任务，从第三方同步数据到中间库（学校全量同步）
//     *
//     * @param params
//     */
//    public void reSync2Prod(Map<String, Object> params) {
//        Verify.of(params)
//                .isValidId("thirdTaskId")
//                .verify();
//
//        Map<String, Object> thirdTask = thirdRepository.selectOne("ThirdMapper.getThirdTask", params);
//        if (ObjectUtil.isValueEquals(thirdTask.get("thirdTaskStatus"), DictUtil.getDictValue("thirdTaskStatus", "done"))) {
//            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "同步已完成，不能重复同步");
//        }
//
//        int potevio = DictUtil.getDictValue("thirdParty","potevio");
//        int teewon = DictUtil.getDictValue("thirdParty","teewon");
//        List<AbstractThirdDataService> serviceList = new ArrayList<>();
//
//        Map<String, AbstractThirdDataService> thirdBeans = SpringContextUtil.getApplicationContext().getBeansOfType(AbstractThirdDataService.class);
//        if (ObjectUtil.isValueEquals(thirdTask.get("thirdPartyId"), potevio)) {
//            // 普天
//            thirdBeans.forEach((beanName, service) -> {
//                if (beanName.toLowerCase().contains("putian")) {
//                    serviceList.add(service);
//                }
//            });
//        } else if (ObjectUtil.isValueEquals(thirdTask.get("thirdPartyId"), teewon)) {
//            // TODO 天闻
//            thirdBeans.forEach((beanName, service) -> {
//                if (beanName.toLowerCase().contains("teewonclassservice")) {
//                    serviceList.add(service);
//                }
//            });
//        }
//
//        if (CollectionUtils.isEmpty(serviceList)) {
//            return;
//        }
//
//        // 去重和排序
//        ThirdTaskReceiver taskReceiver = new ThirdTaskReceiver();
//
//        Map<String, Object> syncParams = JSON.parseToMap(thirdTask.get("syncParams").toString());
//        syncParams.put("thirdTaskId", params.get("thirdTaskId"));
//        params.put("thirdPartyId",syncParams.get("thirdPartyId"));
//
//        try {
//            taskReceiver.receiveEndNotification(params);
//
//            syncParams.put("currentTime", DateUtil.getCurrentDateTime());
//            syncParams.put("thirdTaskStatus", DictUtil.getDictValue("thirdTaskStatus", "done"));
//            ThirdRepository thirdRepository = SpringContextUtil.getBean(ThirdRepository.class);
//            thirdRepository.insert("ThirdMapper.updateThirdTask", syncParams);
//
//        } catch (Exception e) {
//            log.error("同步到生产库失败：{}", e.getMessage(),e);
//
//            syncParams.put("currentTime", DateUtil.getCurrentDateTime());
//            syncParams.put("thirdTaskStatus", DictUtil.getDictValue("thirdTaskStatus", "failure"));
//            ThirdRepository thirdRepository = SpringContextUtil.getBean(ThirdRepository.class);
//            thirdRepository.insert("ThirdMapper.updateThirdTask", syncParams);
//
//            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "同步到生产库失败");
//        }
//
//    }
    
    public long getCommonalityCourseMaxId() {
        return  thirdRepository.selectOne("ThirdTaskMapper.getCommonalityCourseMaxId");
    }

    public Map<String, Object> getThirdTask4Employee(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .verify();
        Integer totalCount = thirdRepository.selectOne("ThirdTaskMapper.getThirdTaskCountForEmployee", params);
        List<Map<String, Object>> thirdTask = Lists.newArrayList();
        if (totalCount != null && totalCount > 0) {
            thirdTask =  thirdRepository.selectList("ThirdTaskMapper.getThirdTaskForEmployee", params);
        }
        return MapUtil.of("totalCount", totalCount, "thirdTask", thirdTask);
    }

    /**
     * 获取某个学校最新的同步任务
     * @param params thirdSchoolName
     * @return
     */
    public List<Map<String, Object>> getLatestThirdTaskList(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("thirdTaskName")
                .verify();
        return thirdRepository.selectList("ThirdTaskMapper.getLatestThirdTaskList", params);
    }

    /**
     * 获取小于某个thirdTaskId的任务
     * @param params thirdPartyId thirdTaskName [thirdTaskId]
     * @return
     */
    public List<Map<String, Object>> getRangeThirdTaskListByNameAndId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("thirdTaskName")
                .isInteger("thirdPartyId")
                .verify();

        return thirdRepository.selectList("ThirdTaskMapper.getRangeThirdTaskListByNameAndId", params);
    }

    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void deleteThirdTask(List<Long> thirdTaskIdList) {
        BatchDataUtil.execute(thirdTaskIdList, (thirdTaskItemIdList) -> {
            // 删除 third.t_third_task
            thirdRepository.delete("ThirdTaskMapper.deleteThirdTask", thirdTaskItemIdList);
            // 删除 third.t_third_task_item
            thirdRepository.delete("ThirdTaskItemMapper.deleteThirdTaskItem", thirdTaskItemIdList);
            // 删除 third.t_third_task_invalid
            thirdRepository.delete("ThirdTaskInvalidMapper.deleteThirdTaskInvalid", thirdTaskItemIdList);
            // 删除 base_data.t_third_data_sync
            baseDataRepository.delete("ThirdDataSyncMapper.deleteThirdDataSync", thirdTaskItemIdList);
        }, 3);
    }

    /**
     * 检测用户是否有重复的任务,进行过滤
     * @param params thirdPartyId thirdSchoolIds userId
     */
    public void checkAndFilterRepetitiveTask(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("thirdPartyId")
                .isNotBlank("thirdSchoolIds", "请选择学校")
                .verify();
        String thirdSchoolIds = MapUtil.getString(params, "thirdSchoolIds");
        List<String> thirdSchoolIdList = StrUtil.split(thirdSchoolIds, ",");

        // 获取当前在就绪中的任务
        params.put("thirdSchoolIdList", thirdSchoolIdList);
        params.put("thirdTaskStatus", DictUtil.getDictValue("thirdTaskStatus", "ready"));
        params.put("thirdTaskType", DictUtil.getDictValue("thirdTaskType", "normal"));
        List<Map<String, Object>> thirdTaskList = thirdRepository.selectList("ThirdTaskMapper.getThirdTaskForEmployee", params);
        if (CollectionUtils.isEmpty(thirdTaskList)) {
            return;
        }

        // 不为空，存在已有就绪任务
        String thirdTaskName = thirdTaskList.stream()
                .map(item -> MapUtil.getString(item, "thirdTaskName"))
                .distinct()
                .collect(Collectors.joining(","));
        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, String.format("【%s】任务正在等待，请勿重复生成同步任务", thirdTaskName));
    }
}
