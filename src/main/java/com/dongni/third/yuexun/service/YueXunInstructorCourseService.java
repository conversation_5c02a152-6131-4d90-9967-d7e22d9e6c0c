package com.dongni.third.yuexun.service;

import cn.hutool.core.collection.CollectionUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.common.util.ThirdDataUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdInstructorCourseService;
import com.dongni.third.base.service.table.ThirdCourseService;
import com.dongni.third.base.service.table.ThirdInstructorService;
import com.dongni.third.base.service.table.ThirdSchoolPrincipalService;
import com.dongni.third.yuexun.api.YueXunDataApi;
import com.dongni.third.yuexun.util.YueXunThirdPrimaryKey;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2021年12月10日
 */
@Service
public class YueXunInstructorCourseService extends AbstractThirdInstructorCourseService {

    @Autowired
    private YueXunDataApi yueXunDataApi;
    @Autowired
    private ThirdCourseService thirdCourseService;
    @Autowired
    private ThirdInstructorService thirdInstructorService;

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = new HashMap<>();
        p.put("openTenantId", syncParams.getCurrentThirdSchoolKey());
        p.put("thirdPartyId", syncParams.getThirdPartyId());
        List<Map<String, Object>> teacherList = yueXunDataApi.getTeacherList(syncParams.getThirdTaskId(), p);

        List<Map<String, Object>> userRoleInfo = yueXunDataApi.getUserRoleList(syncParams.getThirdTaskId(), p);

        // 局领导
        Map<String, List<Map<String, Object>>> userRole = userRoleInfo
                .stream()
                .filter(role -> {
                    int dutyType = MapUtil.getInt(role, "dutyType");
                    return ObjectUtil.isValueEquals(dutyType, 3);
                })
                .collect(groupingBy(item -> item.get("accountOpenId").toString()));

        return teacherList
                .stream()
                .filter(apiData -> ObjectUtil.isValueEquals(MapUtil.getIntNullable(apiData, "enabled"), 1))
                .filter(teacher -> {
                    String openId = MapUtil.getString(teacher, "openId");
                    return CollectionUtil.isNotEmpty(userRole.get(openId));
                })
                .map(HashMap::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams, List<Map<String, Object>> apiDataList, Map<String, List<Map<String, Object>>> extendApiData) {
        String currentThirdSchoolKey = syncParams.getCurrentThirdSchoolKey();

        // 数据转换
        List<Map<String, Object>> thirdDataList = new ArrayList<>();
        List<Map<String, Object>> needSetThirdCourseIdList = new ArrayList<>();
        for (Map<String,Object> apiData : apiDataList){

            String thirdPrincipalKey = MapUtil.getString(apiData, "openId");
            List<Map<String, Object>> innerCourseList = ThirdDataUtil.getDongniInnerCourseByStage();

            for (Map<String, Object> dongniInnerCourseInfo : innerCourseList) {
                Map<String, Object> thirdData = new HashMap<>();
                int stage = MapUtil.getInt(dongniInnerCourseInfo, "stage");
                String thirdCourseId = MapUtil.getString(dongniInnerCourseInfo, "thirdCourseId");
                thirdData.put("thirdCourseId", thirdCourseId);
                thirdData.put("stage", stage);
                thirdData.put("courseName", dongniInnerCourseInfo.get("thirdCourseName"));
                // primaryKey
                thirdData.put(PRIMARY_KEY_FIELD, YueXunThirdPrimaryKey.getThirdPrimaryKeyInstructorCourse(thirdPrincipalKey, stage, thirdCourseId));
                thirdData.put(ThirdSchoolPrincipalService.THIRD_PRIMARY_KEY_FIELD,
                        YueXunThirdPrimaryKey.getThirdPrimaryKeyInstructor(thirdPrincipalKey));

                thirdDataList.add(thirdData);
            }
        }

        Map<String, String> putThirdDataKeyMapInfoKey = new HashMap<>();

        putThirdDataKeyMapInfoKey.clear();
        putThirdDataKeyMapInfoKey.put("thirdCourseId", "thirdCourseId");
        putThirdDataKeyMapInfoKey.put("courseName", "courseName");
        ThirdDataUtil.putInfoToThirdDataList(syncParams, needSetThirdCourseIdList,
                ThirdCourseService.THIRD_PRIMARY_KEY_FIELD, "thirdPrimaryKeyList",
                "没有对应的课程", putThirdDataKeyMapInfoKey,
                thirdCourseService::getThirdPrimaryKeyMapThirdCourseInfo);

        putThirdDataKeyMapInfoKey.clear();
        putThirdDataKeyMapInfoKey.put("thirdInstructorId", "thirdInstructorId");
        ThirdDataUtil.putInfoToThirdDataList(syncParams, thirdDataList,
                ThirdSchoolPrincipalService.THIRD_PRIMARY_KEY_FIELD, "thirdPrimaryKeyList",
                "没有对应的教研员", putThirdDataKeyMapInfoKey,
                thirdInstructorService::getThirdPrimaryKeyMapInstructorInfo);

        return thirdDataList;
    }

}
