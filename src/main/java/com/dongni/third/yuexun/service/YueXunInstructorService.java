package com.dongni.third.yuexun.service;

import cn.hutool.core.collection.CollectionUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdInstructorService;
import com.dongni.third.yuexun.api.YueXunDataApi;
import com.dongni.third.yuexun.util.YueXunThirdPrimaryKey;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2021年12月10日
 */
@Service
public class YueXunInstructorService extends AbstractThirdInstructorService {

    @Autowired
    private YueXunDataApi yueXunDataApi;

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = new HashMap<>();
        p.put("openTenantId", syncParams.getCurrentThirdSchoolKey());
        p.put("thirdPartyId", syncParams.getThirdPartyId());
        List<Map<String, Object>> teacherList = yueXunDataApi.getTeacherList(syncParams.getThirdTaskId(), p);

        List<Map<String, Object>> userRoleInfo = yueXunDataApi.getUserRoleList(syncParams.getThirdTaskId(), p);

        // 局领导
        Map<String, List<Map<String, Object>>> userRole = userRoleInfo
                .stream()
                .filter(role -> {
                    int dutyType = MapUtil.getInt(role, "dutyType");
                    return ObjectUtil.isValueEquals(dutyType, 3);
                })
                .collect(groupingBy(item -> item.get("accountOpenId").toString()));

        return teacherList
                .stream()
                .filter(apiData -> ObjectUtil.isValueEquals(MapUtil.getIntNullable(apiData, "enabled"), 1))
                .filter(teacher -> {
                    String openId = MapUtil.getString(teacher, "openId");
                    return CollectionUtil.isNotEmpty(userRole.get(openId));
                })
                .map(HashMap::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams, List<Map<String, Object>> apiDataList, Map<String, List<Map<String, Object>>> extendApiData) {
        List<Map<String, Object>> thirdDataList = new ArrayList<>();

        for (Map<String, Object> apiData : apiDataList) {
            Map<String, Object> thirdData = new HashMap<>();
            String thirdUserKey = apiData.get("openId").toString();
            String phone = apiData.get("mobile").toString();

            thirdData.put(PRIMARY_KEY_FIELD, YueXunThirdPrimaryKey.getThirdPrimaryKeyEducationDirector(thirdUserKey));

            thirdData.put("instructorName", apiData.get("employeeName"));
            thirdData.put("instructorPhone", phone);
            thirdData.put("instructorPhoneAes", SensitiveInfoUtil.aesEncrypt(phone));
            thirdData.put("areaId", 350203L);
            thirdDataList.add(thirdData);
        }

        return thirdDataList;
    }

    @Override
    protected LinkedHashMap<String, Function<Map<String, Object>, Object>> getSimpleMsgFieldMap() {
        LinkedHashMap<String, Function<Map<String, Object>, Object>> simpleMsgFieldMap = new LinkedHashMap<>();
        simpleMsgFieldMap.put("教研员姓名", thirdData -> thirdData.get("instructorName").toString());
        return simpleMsgFieldMap;
    }
}
