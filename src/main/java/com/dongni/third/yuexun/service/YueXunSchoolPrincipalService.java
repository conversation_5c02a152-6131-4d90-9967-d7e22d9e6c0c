package com.dongni.third.yuexun.service;

import cn.hutool.core.collection.CollectionUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.common.util.ThirdDataUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdSchoolPrincipalService;
import com.dongni.third.base.service.table.ThirdSchoolService;
import com.dongni.third.yuexun.api.YueXunDataApi;
import com.dongni.third.yuexun.util.YueXunThirdPrimaryKey;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2021年08月19日
 */
@Service
public class YueXunSchoolPrincipalService extends AbstractThirdSchoolPrincipalService {

    @Autowired
    private YueXunDataApi yueXunDataApi;

    /**
     * 悦讯角色类型 - 懂你角色类型
     */
    private static final List<Integer> SCHOOL_PRINCIPAL_ROLE_LIST = Lists.newArrayList();

    static {
        // 校长
        SCHOOL_PRINCIPAL_ROLE_LIST.add(1);
        // 教导主任
        SCHOOL_PRINCIPAL_ROLE_LIST.add(4);
        // 教务主任
        SCHOOL_PRINCIPAL_ROLE_LIST.add(5);
        // 教研组长
        SCHOOL_PRINCIPAL_ROLE_LIST.add(7);
    }

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = new HashMap<>();
        p.put("openTenantId", syncParams.getCurrentThirdSchoolKey());
        p.put("thirdPartyId", syncParams.getThirdPartyId());
        List<Map<String, Object>> teacherList = yueXunDataApi.getTeacherList(syncParams.getThirdTaskId(), p);

        List<Map<String, Object>> userRoleInfo = yueXunDataApi.getUserRoleList(syncParams.getThirdTaskId(), p);

        Map<String, List<Map<String, Object>>> userRole = userRoleInfo
                .stream()
                .filter(role -> {
                    int dutyType = MapUtil.getInt(role, "dutyType");
                    return SCHOOL_PRINCIPAL_ROLE_LIST.contains(dutyType);
                })
                .collect(groupingBy(item -> item.get("accountOpenId").toString()));

        List<Map<String, Object>> schoolPrincipalList = Lists.newArrayList();
        for (Map<String, Object> teacher : teacherList) {
            HashMap<String, Object> schoolPrincipal = new HashMap<>(teacher);
            String openId = MapUtil.getString(schoolPrincipal, "openId");
            Integer enabled = MapUtil.getIntNullable(schoolPrincipal, "enabled");
            boolean isEnable = ObjectUtil.isValueEquals(enabled, 1);
            List<Map<String, Object>> userRoleList = userRole.get(openId);
            Set<Integer> dutyTypeSet = Sets.newHashSet();
            // 账号可用并且是校领导角色
            if (isEnable && CollectionUtil.isNotEmpty(userRoleList)) {
                Set<Integer> dutyTypeList = userRoleList
                        .stream()
                        .map(item -> MapUtil.getInt(item, "dutyType")).collect(Collectors.toSet());

                // 校长
                if (dutyTypeList.contains(1)) {
                    dutyTypeSet.add(1);
                }
                // 教导主任
                if (dutyTypeList.contains(4)) {
                    dutyTypeSet.add(3);
                }

                // 教务主任
                if (dutyTypeList.contains(5)) {
                    dutyTypeSet.add(5);
                }

                // 教研组长必须提供课程 因为还有一张表引用 t_school_principal_course
                if (dutyTypeList.contains(7)) {
                    dutyTypeSet.add(6);
                }
                schoolPrincipal.put("dutyTypeList", dutyTypeSet);
                schoolPrincipal.put("_rolenames", dutyTypeList);
                schoolPrincipalList.add(schoolPrincipal);
            }

        }

        return schoolPrincipalList;
    }

    @Override
    public Map<String, List<Map<String, Object>>> getExtendApiData(SyncParams syncParams) {
        Map<String, List<Map<String, Object>>> extendApiData = new HashMap<>();
        Map<String, Object> p = new HashMap<>();
        p.put("openTenantId", syncParams.getCurrentThirdSchoolKey());
        p.put("thirdPartyId", syncParams.getThirdPartyId());
        List<Map<String, Object>> schoolSemesterList = yueXunDataApi.getSchoolSemester(syncParams.getThirdTaskId(), p);
        // 按学期筛选数据
        Map<String, Object> currentSchoolSemesterMap = schoolSemesterList.stream()
                .filter(schoolSemester -> ObjectUtil.isValueEquals("1", schoolSemester.get("currentFlag")))
                .findFirst().orElse(MapUtil.of("semesterCode", "0"));
        p.put("semesterCode", currentSchoolSemesterMap.get("semesterCode").toString());
        List<Map<String, Object>> courseTeacher = yueXunDataApi.getCourseTeacher(syncParams.getThirdTaskId(), p);
        extendApiData.put("courseTeacher", courseTeacher);
        return extendApiData;
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams, List<Map<String, Object>> apiDataList, Map<String, List<Map<String, Object>>> extendApiData) {
        String currentThirdSchoolKey = syncParams.getCurrentThirdSchoolKey();
        String currentThirdSchoolName = syncParams.getCurrentThirdSchoolName();
        List<Map<String, Object>> courseTeacherList = Optional.ofNullable(extendApiData.get("courseTeacher")).orElse(Lists.newArrayList());
        Map<String, List<Map<String, Object>>> courseTeacherMap = courseTeacherList.stream().collect(groupingBy(item -> MapUtil.getString(item.get("openId"))));

        // 数据转换
        List<Map<String, Object>> thirdDataList = new ArrayList<>();
        for (Map<String,Object> apiData : apiDataList){
            Map<String, Object> dataThirdCommon = new HashMap<>();

            String thirdTeacherKey = apiData.get("openId").toString();
            String principalPhone = apiData.get("mobile").toString();

            dataThirdCommon.put("schoolName", currentThirdSchoolName);
            dataThirdCommon.put("principalPhone", principalPhone);
            dataThirdCommon.put("principalPhoneAes", SensitiveInfoUtil.aesEncrypt(principalPhone));
            dataThirdCommon.put("principalName", apiData.get("employeeName").toString());
            dataThirdCommon.put(ThirdSchoolService.THIRD_PRIMARY_KEY_FIELD, YueXunThirdPrimaryKey.getThirdPrimaryKeySchool(currentThirdSchoolKey));

            List<Integer> dutyTypeList = MapUtil.getListInteger(apiData, "dutyTypeList");

            for (Integer dutyType : dutyTypeList) {
                Map<String, Object> thirdData = new HashMap<>(dataThirdCommon);

                Integer updateType = MapUtil.getIntNullable(apiData, "updateType");
                if (updateType != null && updateType == 3) {
                    putErrMsg(thirdData, apiData, "数据更新类型为删除");
                    thirdDataList.add(thirdData);
                    continue;
                }

                thirdData.put("dutyType", dutyType);
                thirdData.put(PRIMARY_KEY_FIELD, YueXunThirdPrimaryKey.getThirdPrimaryKeySchoolPrincipal(currentThirdSchoolKey, thirdTeacherKey, dutyType));

                thirdDataList.add(thirdData);
            }
        }

        Map<String, String> putThirdDataKeyMapInfoKey = new HashMap<>();

        putThirdDataKeyMapInfoKey.clear();
        putThirdDataKeyMapInfoKey.put("thirdSchoolId", "thirdSchoolId");
        ThirdDataUtil.putInfoToThirdDataList(syncParams, thirdDataList,
                ThirdSchoolService.THIRD_PRIMARY_KEY_FIELD, "thirdPrimaryKeyList",
                "没有对应的学校", putThirdDataKeyMapInfoKey,
                thirdSchoolService::getThirdPrimaryKeyMapThirdSchoolInfo);


        return thirdDataList;
    }

    @Override
    protected LinkedHashMap<String, Function<Map<String, Object>, Object>> getSimpleMsgFieldMap() {
        LinkedHashMap<String, Function<Map<String, Object>, Object>> simpleMsgFieldMap = new LinkedHashMap<>();
        simpleMsgFieldMap.put("校领导姓名", thirdData -> thirdData.get("principalName").toString());
        simpleMsgFieldMap.put("领导类型", thirdData -> {
            int dutyType = MapUtil.getInt(thirdData, "dutyType");
            switch (dutyType) {
                case 1: return "校长(校领导)";
                case 3: return "教导主任";
                case 5: return "学校教研员(教务科)";
                case 6: return "教研组长";
                default: return "不详" + dutyType;
            }
        });
        return simpleMsgFieldMap;
    }

}
