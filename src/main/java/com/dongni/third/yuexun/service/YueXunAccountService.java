package com.dongni.third.yuexun.service;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.third.base.progress.SyncParams;
import com.dongni.third.base.progress.data.base.AbstractThirdAccountService;
import com.dongni.third.yuexun.api.YueXunDataApi;
import com.dongni.third.yuexun.util.YueXunDataUtils;
import com.dongni.third.yuexun.util.YueXunThirdPrimaryKey;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description: 悦讯基础数据同步账号表service
 * @author: guoqiang
 * @create: 2021-07-27 16:57
 */
@Service
public class YueXunAccountService extends AbstractThirdAccountService {

    @Autowired
    private YueXunDataApi yueXunDataApi;

    @Autowired
    private YueXunTeacherService yueXunTeacherService;

    @Autowired
    private YueXunStudentService yueXunStudentService;

    @Autowired
    private YueXunParentService yueXunParentService;

    @Override
    public List<Map<String, Object>> getApiDataList(SyncParams syncParams) {
        Map<String, Object> p = new HashMap<>();
        p.put("thirdPartyId", String.valueOf(syncParams.getThirdPartyId()));
        p.put("openTenantId", syncParams.getCurrentThirdSchoolKey());

        List<Map<String, Object>> accountList = Lists.newArrayList();
        List<Map<String, Object>> students = yueXunStudentService.getApiDataList(syncParams);

        List<Map<String, Object>> teacherList = yueXunDataApi.getTeacherList(syncParams.getThirdTaskId(), p);

        List<Map<String, Object>> parentList = yueXunParentService.getApiDataList(syncParams);

        students.forEach(item ->{
            item.put("accountName", item.get("studentOpenId"));
            item.put("userType", DictUtil.getDictValue("userType","student"));
        });
        accountList.addAll(students);

        teacherList.stream()
                .filter(apiData -> ObjectUtil.isValueEquals(MapUtil.getIntNullable(apiData, "enabled"), 1))
                .forEach(item -> {
                    item.put("accountName", item.get("openId"));
                    item.put("userType", DictUtil.getDictValue("userType", "teacher"));
                });
        accountList.addAll(teacherList);

        parentList.forEach(item ->{
            item.put("accountName", item.get("openId"));
            item.put("userType", DictUtil.getDictValue("userType","parent"));
        });
        accountList.addAll(parentList);
        return accountList;
    }

    @Override
    public List<Map<String, Object>> transferApiToThirdData(SyncParams syncParams, List<Map<String, Object>> apiDataList, Map<String, List<Map<String, Object>>> extendApiData) {
        String currentThirdSchoolKey = syncParams.getCurrentThirdSchoolKey();

        int accountStatusOn = DictUtil.getDictValue("accountStatus", "on");
        int passwordStatusNormal = DictUtil.getDictValue("passwordStatus","normal");
        int securityStatusDisabled = DictUtil.getDictValue("securityStatus","disabled");
        int phoneBindStatusNever = DictUtil.getDictValue("phoneBindStatus","never");

        List<Map<String,Object>> accountList = new ArrayList<>(apiDataList.size());
        // 数据转换
        for (Map<String,Object> data : apiDataList){
            Map<String,Object> account = new HashMap<>();

            validApiDataItem(account, data, "accountName", "openId为空");
            if (isErrorItem(account)) {
                accountList.add(account);
                continue;
            }

            String accountName = "YueXun" + data.get("accountName");
            account.put("_accountNameSrc", accountName);
            account.put("accountName", accountName);
            account.put("accountNameAes", SensitiveInfoUtil.aesEncrypt(accountName));
            account.put("accountStatus", accountStatusOn);
            account.put("securityStatus", securityStatusDisabled);
            account.put("password", "398FC32B5057084A04B60E8612BABD08");
            account.put("passwordStatus", passwordStatusNormal);
            account.put("phoneBindStatus", phoneBindStatusNever);
            account.put("thirdPartyId", YueXunDataUtils.getThirdPartyId());

            account.put(PRIMARY_KEY_FIELD, YueXunThirdPrimaryKey.getThirdPrimaryKeyAccount(data.get("accountName").toString()));
            accountList.add(account);
        }

        return accountList;
    }

}
