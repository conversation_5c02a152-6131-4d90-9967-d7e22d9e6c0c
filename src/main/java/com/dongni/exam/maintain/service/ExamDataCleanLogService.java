package com.dongni.exam.maintain.service;

import com.dongni.commons.utils.DateUtil;
import com.dongni.exam.bean.ExamRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Heweipo on 2017/2/23.
 *
 * 数据清理日志记录 接口实现类
 */
@Service
public class ExamDataCleanLogService {

    // 日志
    private static final Logger log = LogManager.getLogger(ExamDataCleanLogService.class);

    @Autowired
    private ExamRepository commonRepository;

    /**
     * 保存操作失败时的记录
     *
     */
    public String insertCleanDataLog(Map<String, Object> params, String type, long start, Throwable exceptionMsg) {
        Map<String, Object> p = new HashMap<>(params);
        p.put("invokeName", type);
        p.put("currentTime2", DateUtil.getCurrentDateTime());
        p.put("isSuccess", exceptionMsg == null ? 1 : 0);
        p.put("exceptionMsg",exceptionMsg == null ? null : getStackTrace(exceptionMsg));

        ObjectMapper mapper = new ObjectMapper();
        try {
            p.put("execParams", mapper.writeValueAsString(params));
        } catch (JsonProcessingException e) {
            LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
        }

        long execUseTime = System.currentTimeMillis() - start;
        p.put("execUseTime", execUseTime);

        commonRepository.insert("ExamDataCleanLogMapper.insertExamDataCleanLog", p);
        return exceptionMsg == null ? "操作成功" : exceptionMsg.getMessage();
    }

    /**
     * 将异常堆栈转换为字符串
     *
     * @param aThrowable 异常
     * @return String
     */
    public static String getStackTrace(Throwable aThrowable) {
        final Writer result = new StringWriter();
        final PrintWriter printWriter = new PrintWriter(result);
        aThrowable.printStackTrace(printWriter);
        return result.toString();
    }
}
