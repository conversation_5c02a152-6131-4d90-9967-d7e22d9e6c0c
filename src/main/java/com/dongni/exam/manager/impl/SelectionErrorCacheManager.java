package com.dongni.exam.manager.impl;

import com.dongni.common.mongo.IManager;
import com.dongni.exam.bean.ExamMongodb;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/8/2 上午 11:00
 * @Version 1.0.0
 */
@Service
public class SelectionErrorCacheManager extends IManager {

    protected SelectionErrorCacheManager(ExamMongodb examMongodb) {
        super(examMongodb, ExamMongodb.COLLECTION_SELECTION_ERROR_CACHE, "选科异常处理缓存表");
    }
}
