package com.dongni.exam.card.service;

import cn.hutool.core.collection.CollectionUtil;
import com.aliyun.openservices.shade.org.apache.commons.lang3.tuple.ImmutablePair;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.ResourceConfig;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.bean.vo.AbsentStudentVO;
import com.dongni.exam.card.bean.vo.ExamUploaderCompleteStatVO;
import com.dongni.exam.card.constant.AnswerCardConstants;
import com.dongni.exam.card.util.BitUtil;
import com.dongni.exam.card.util.RecognitionCardConfig;
import com.dongni.exam.common.mark.vo.DoItemsVO;
import com.dongni.exam.newcard.bean.BO.FileContinueErrCountBO;
import com.dongni.exam.newcard.bean.VO.AnswerCardVO;
import com.dongni.exam.newcard.bean.VO.DeleteCardVO;
import com.dongni.exam.newcard.dao.NewAnswerCardDao;
import com.dongni.exam.newcard.dao.NewAnswerCardRecycleDao;
import com.dongni.exam.newcard.service.*;
import com.dongni.exam.newcard.service.impl.ExceptionCardImpl;
import com.dongni.exam.newcard.service.impl.NewExceptionCardImpl;
import com.dongni.exam.newcard.service.impl.NewRecognitionTask;
import com.dongni.exam.plan.bean.ExamUploaderBO;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.bean.dto.ExamPaperDTO;
import com.dongni.exam.plan.bean.vo.ExamSchoolVO;
import com.dongni.exam.plan.bean.vo.ExamStuExtVO;
import com.dongni.exam.plan.service.*;
import com.dongni.exam.schedule.RTRedisService;
import com.dongni.tiku.common.util.MapUtil;
import com.hqjl.redis.queue.MsgProducer;
import com.yunpian.sdk.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dongni.exam.card.util.AnswerCardUtils.convertTifToPng;
import static java.util.stream.Collectors.*;

@Service
public class AnswerCardBatchService {

    @Autowired
    private ExamRepository commonRepository;

    @Autowired
    private NewRecognitionTask newRecognitionTask;

    @Autowired
    private IAnswerCardRecycle answerCardRecycle;

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private ExceptionCardImpl exceptionCardImpl;

    @Autowired
    private IAnswerCarSizeService answerCarSizeService;

    @Autowired
    private ExamStuExtServiceImpl examStuExtService;

    @Autowired
    private ExamUploaderToRedisService examUploaderToRedisService;

    @Transactional(ExamRepository.TRANSACTION)
    public Object addBatch(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isNotBlank("deviceId")
                .isNotBlank("batchType")
                .isNotBlank("userId")
                .isNotBlank("userName")
                .isNotBlank("batchSeq")
                .verify();

        params.put("batchStartTime", new Date());
        params.put("creatorId", params.get("userId"));
        int batchSeq = MapUtil.getInt(params, "batchSeq");
        List<Map<String, Object>> batchList = commonRepository.selectList("UploadBatchMapper.getBatchByDeviceIdAndBatchSeq", params);
        for (Map<String, Object> map : batchList) {
            if (batchSeq == MapUtil.getInt(map, "batchSeq")) {
                params.put("batchId", map.get("batchId"));
                commonRepository.update("UploadBatchMapper.updateBatchScanCardNum", params);
                return map.get("batchId");
            }
        }
        int batchType = MapUtil.getInt(params, "batchType");
        // 本地上传默认数量.
        if (batchType == 2) {
            params.put("uploadedCardCount", MapUtil.getInt(params, "batchScanCardNum", 0));
        } else {
            params.put("uploadedCardCount", 0);
        }
        commonRepository.insert("UploadBatchMapper.insertUploadBatch", params);
        return params.get("batchId");
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void updateBatchScanNum(Map<String, Object> params) {
        if (params.containsKey("batchId") && params.containsKey("batchScanCardNum")) {
            commonRepository.update("UploadBatchMapper.updateBatchScanCardNum", params);
        }
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void updateUploadedCardCount(Map<String, Object> params) {
        if (params.containsKey("batchId") && params.containsKey("cardCount")) {
            commonRepository.update("UploadBatchMapper.updateUploadedCardCount", params);
        }
    }

    public List<?> getBatchList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();

        int fromClient = MapUtil.getInt(params, "fromClient", 0);
        if (fromClient == 1) {
            return commonRepository.selectList("UploadBatchMapper.getClientBatchList", params);
        }

        answerCardService.addExamUploaderIds(params);
        if (MapUtil.getInt("batchType", 0) == 1 && StringUtils.isEmpty(MapUtil.getString("deviceId"))) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> list = commonRepository.selectList("UploadBatchMapper.getBatchList", params);
        if (!list.isEmpty()) {
            List<Map<String, Object>> recognitionStatics = commonRepository.selectList("UploadBatchMapper.getBatchRecognitionCount", params);

            Map<Long, Map<String, Object>> staticsMap = new HashMap<>();
            for (Map<String, Object> map : recognitionStatics) {
                Long batchId = MapUtil.getLong(map, "batchId", 0L);
                staticsMap.put(batchId, map);
            }

            Long userId = MapUtil.getLong(params, "userId", 0L);
            Integer batchType = MapUtil.getInt(params, "batchType", 0);
            boolean isUpload = batchType == 2;
            for (Map<String, Object> map : list) {
                map.put("checkTemplateStatus", MapUtil.getInt(map, "checkTemplateStatus") == 1 ? false : true);
                if (isUpload) {
                    if (!MapUtil.getLong(map, "creatorId", 0L).equals(userId)) {
                        continue;
                    }
                }

                long batchId = Long.parseLong(map.get("batchId").toString());
                Map<String, Object> batchStatus = staticsMap.getOrDefault(batchId, Collections.emptyMap());
                int recognitionStatus = MapUtil.getInt(batchStatus, "recognitionStatus", 0);
                int recognitionFailStatus = MapUtil.getInt(batchStatus, "recognitionFailStatus", 0);
                int pictureCount = MapUtil.getInt(batchStatus, "pictureCount", 0);
                int batchScanCardNum = MapUtil.getInt(map, "batchScanCardNum", 0);
                int cardExceptionNum = MapUtil.getInt(map, "cardExceptionNum", 0);
                int uploadedCardCount = MapUtil.getInt(map, "uploadedCardCount", 0);
                map.put("noRecognitionCount", pictureCount - recognitionFailStatus - recognitionStatus);
                map.put("recognitionCount", recognitionStatus + recognitionFailStatus);
                map.put("recognitionFailCount", recognitionFailStatus);
                map.put("cardCount", pictureCount);
                map.put("noUploadCount", batchScanCardNum - uploadedCardCount);
                map.put("batchScanCardNum", batchScanCardNum + cardExceptionNum);
            }
            list = list.stream().filter(item -> {
                int batchType2 = MapUtil.getInt(item, "batchType", 1);
                int batchScanStatus = MapUtil.getInt(item, "batchScanStatus", 1);
                int cardCount = MapUtil.getInt(item, "cardCount", 0);
                int cardExceptionNum = MapUtil.getInt(item, "cardExceptionNum", 0);
                // 没图片的话，
                if (cardCount < 1) {
                    if (batchType2 == 2) {
                        return false;
                    } else if (batchType2 == 1 && batchScanStatus == 0 && cardExceptionNum == 0) {
                        return false;
                    }
                }
                return true;
            }).collect(toList());
        }

        if (list.size() > 0) {
            List<Long> batchErrorIds = commonRepository.selectList("UploadBatchMapper.getBatchErrorInfos", params);
            if (CollectionUtil.isNotEmpty(batchErrorIds)) {
                list.forEach(batchInfo -> {
                    long batchId = MapUtil.getLong(batchInfo, "batchId", 0L);
                    batchInfo.put("batchLose", batchErrorIds.contains(batchId));
                });
            } else {
                list.forEach(batchInfo -> batchInfo.put("batchLose", false));
            }
        }

        answerCarSizeService.addMarkSizeExceptionForBatchList(params, list);

        //批次查询对应的 上传分组异常的数量
        Long examUploaderId = cn.hutool.core.map.MapUtil.getLong(params, "examUploaderId");
        List<FileContinueErrCountBO> errLists = commonRepository.selectList("AnswerCardMapper.getFileContinueErrCount", Collections.singletonMap("examUploaderId", examUploaderId));
        Map<Long, Long> errFileContinueMap = errLists.stream().collect(Collectors.toMap(FileContinueErrCountBO::getBatchId, FileContinueErrCountBO::getErrCount));
        for (Map<String, Object> item : list) {
            Long batchId = cn.hutool.core.map.MapUtil.getLong(item, "batchId");
            if (errFileContinueMap.containsKey(batchId)) {
                item.put("uploadGroupExceptCount", errFileContinueMap.get(batchId));
            } else {
                item.put("uploadGroupExceptCount", 0);
            }
        }
        return list;
    }

    public ImmutablePair<Long, Object> getBatchDetail(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isNotBlank("batchId")
                .verify();
        Object pageNo = params.get("pageNo");
        if (pageNo != null) {
            Object pageSize = params.getOrDefault("pageSize", 10);
            Integer currentIndex = (Integer.valueOf(pageNo.toString()) - 1) * Integer.valueOf(pageSize.toString());
            params.put("currentIndex", currentIndex);
        }

        Long count = commonRepository.selectOne("UploadBatchMapper.getAnswerCardGroupCount", params);
        List<String> answerCardCodes = commonRepository.selectList("UploadBatchMapper.getAnswerCardCodes", params);
        if (CollectionUtils.isEmpty(answerCardCodes)) {
            return new ImmutablePair<>(count, Collections.emptyList());
        }

        params.put("answerCardCodes", answerCardCodes);
        List<Map<String, Object>> list = commonRepository.selectList("UploadBatchMapper.getBatchDetail", params);
        Map<String, Map<String, Object>> answerCardGroups = new HashMap<>();
        for (Map<String, Object> map : list) {
            String answerCardCode = map.get("answerCardCode").toString();
            Map<String, Object> cardGroup = answerCardGroups.computeIfAbsent(answerCardCode, k -> {
                int paperStatus = Double.valueOf((Objects.toString(map.get("paperStatus"), "0"))).intValue();
                map.put("paperStatus", parseStatus(paperStatus));
                map.put("files", new ArrayList<>());
                return map;
            });
            String filePath = map.remove("filePath").toString();
            int pageNumber = Integer.parseInt(map.remove("pageNumber").toString());
            @SuppressWarnings("unchecked")
            List<ImmutablePair<String, Integer>> files = (List<ImmutablePair<String, Integer>>) cardGroup.get("files");
            files.add(ImmutablePair.of(filePath, pageNumber));
        }

        List<Map<String, Object>> ret = new ArrayList<>();
        int start = MapUtil.getInt(params, "currentIndex", 0) + 1;
        for (String answerCardCode : answerCardCodes) {
            Map<String, Object> map = answerCardGroups.get(answerCardCode);
            @SuppressWarnings("unchecked")
            List<ImmutablePair<String, Integer>> files = (List<ImmutablePair<String, Integer>>) map.get("files");
            files.sort(Comparator.comparingInt(ImmutablePair::getRight));
            map.put("index", start++);
            ret.add(map);
        }
        return new ImmutablePair<>(count, ret);
    }

    private static int parseStatus(int errorCode) {
        if (errorCode == 0) {
            return 0;
        }
        for (int i = 1; i <= 32; i++) {
            if (BitUtil.readBit(errorCode, i)) {
                return i;
            }
        }
        return 33;
    }

    @Autowired
    private NewExceptionCardImpl newExceptionCard;

    @Transactional(ExamRepository.TRANSACTION)
    public Object deleteAnswerCard(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();
        if (params.get("batchId") == null && params.get("answerCardCodeList") == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        Map<String, Object> examUploaderInfo = commonRepository.selectOne("ExamUploaderMapper.getExamUploaderInfo", params);
        params.putAll(examUploaderInfo);
        params.put("currentTime", new Date());


        Map<String, Object> batch = commonRepository.selectOne("UploadBatchMapper.getBatch", params);
        if (batch == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "批次不存在");
        }
        int scanStatus = MapUtil.getInt(batch, "scanStatus");
        if (scanStatus != 0) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "批次尚未扫描完成");
        }

        List<Map<String, Object>> deleteCards = commonRepository.selectList("UploadBatchMapper.getDeleteCards", params);
        checkAnswerCardsRecognized(params, deleteCards, "删除的答题卡，正在识别中，请稍后重试删除操作！");
        DeleteCardVO deleteCardVO = new DeleteCardVO();
        deleteCardVO.setExamUploaderId(MapUtil.getLong(params, "examUploaderId"));
        deleteCardVO.setBatchId(MapUtil.getLong(params, "batchId", 0L));
        deleteCardVO.setUserId(MapUtil.getLong(params, "userId"));
        deleteCardVO.setUserName(MapUtil.getString(params, "userName"));
        if (params.containsKey("answerCardCodeList")) {
            List<String> answerCardCodeList = MapUtil.getListType(params, "answerCardCodeList", Object::toString);
            deleteCardVO.setAnswerCardCodes(answerCardCodeList);
        }
        deleteCardVO.setDeleteCode(36);
        newExceptionCard.deleteCards(deleteCardVO);
        // 删除阅卷任务和记录
        DoItemsVO doItemsVO = new DoItemsVO();
        doItemsVO.setExamId(Long.parseLong(params.get("examId").toString()));
        doItemsVO.setPaperId(Long.parseLong(params.get("paperId").toString()));
        doItemsVO.setUserId(Long.parseLong(params.get("userId").toString()));
        doItemsVO.setUserName(params.get("userName").toString());
        //examItemService.deleteItems(doItemsVO);


        // 批次等于0, 暂时保留.
        /*
        Long count = commonRepository.selectOne("UploadBatchMapper.getAnswerCardGroupCount", params);
        if (count == 0) {
            commonRepository.delete("UploadBatchMapper.deleteUploaderBatch", params);
        }*/
        return 0;
    }

    @Autowired
    private OfflineScoreService offlineScoreService;

    /**
     * check delete cards is recognized.
     *
     * @param params
     * @param cards
     */
    public void checkAnswerCardsRecognized(Map<String, Object> params, List<Map<String, Object>> cards, String message) {
        if (CollectionUtils.isEmpty(cards))
            return;
        Map<String, List<Map<String, Object>>> taskIdCardMap = cards.stream().collect(groupingBy(item -> MapUtil.getString(item, "taskId", "")));
        List<Map<String, Object>> taskInfoList = commonRepository.selectList("UploadBatchMapper.getRecognizingTaskInfoList", params);
        if (CollectionUtils.isEmpty(taskInfoList))
            return;
        for (String taskId : taskIdCardMap.keySet()) {
            if (StringUtils.isEmpty(taskId))
                continue;
            boolean isRecognizing = taskInfoList.stream().anyMatch(taskInfo -> {
                String id = MapUtil.getString(taskInfo, "taskId");
                return taskId.equals(id);
            });
            if (isRecognizing) {
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, message);
            }
        }
    }


    public Map<String, Object> getCardScanStatics(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();
        long examUploaderId = MapUtil.getLong(params, "examUploaderId");
        ExamUploaderBO bo = commonRepository.selectOne("PlanExamUploaderMapper.getExamUploader", params);
        params.put("examId", bo.getExamId());
        params.put("paperId", bo.getPaperId());
        params.put("schoolId", bo.getSchoolId());
        if (bo.isUploadByClass()) {
            List<Long> classIds = examRepository.selectList("AnswerCardClassStatisticsMapper.getClassExamUploaderClassIds", bo);
            params.put("classIds", classIds);
        }
        params.put("examUploaderIds", Collections.singletonList(examUploaderId));
        params.put("needPaperBits", RecognitionCardConfig.getNeedPaperBits());
        params.put("needPersonBits", RecognitionCardConfig.getNeedPersonBits());
        params.put("templateNumber", answerCardService.getTemplateNumber(params));

        // 把相关的examUploaderIds给传进来。
        params.put("__examUploaderIds__", commonRepository.selectList("PlanExamUploaderMapper.getPaperSchoolExamUploaderIds", params));
        Map<String, Object> data = commonRepository.selectOne("UploadBatchMapper.getCardScanStatics", params);
        double mustErrCount = MapUtil.getInt(data, "needPaperCount", 0)
                + MapUtil.getDouble(data, "needStudentCount", 0D)
                + MapUtil.getInt(data, "systemErrorCount", 0);
        data.put("mustErrCount", mustErrCount);
        if (MapUtil.getBoolean(params, "needRecognitionTask", false)) {
            data.put("recognitionTaskIndex", newRecognitionTask.getTemplateTaskQueue(params));
        }

//        data.put("absentStudentCount", MapUtil.getInt(data, "manualAbsentStudentCount", 0)
//                + MapUtil.getInt(data, "absentStudentCount", 0));

        Map<String, Set> offlineReviewMap = offlineScoreService.getOfflineScoreNeedReview(params);
        double noMustErrCount = MapUtil.getDouble(data, "noMustErrCount", 0.0);
        noMustErrCount += MapUtil.getDouble(data, "lowFeatureCount", 0.0);
        noMustErrCount += Stream.of("redPick", "handNumber").mapToInt(key -> offlineReviewMap.get(key).size()).sum();
        data.put("noMustErrCount", noMustErrCount);
        data.put("uploadGroupException", MapUtil.getLong(params, "uploadGroupException", 0L));

        int errorCount = MapUtil.getInt(data, "errorCount", 0);
        if (errorCount > 0) {
            examUploaderToRedisService.addExamUploaderId(examUploaderId);
        }

        return data;
    }

    /**
     * 获取答题卡组的图片路径
     *
     * @param params
     * @return
     */
    public List<Map<String, Object>> getFilePathsByAnswerCardCodes(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();

        if (!params.containsKey("answerCardCode")) {
            String answerCardCode = commonRepository.selectOne("UploadBatchMapper.getLastAnswerCardCode", params);
            if (answerCardCode == null) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "试题不存在");
            }
            params.put("answerCardCode", answerCardCode);
        }
        List<Map<String, Object>> list = commonRepository.selectList("UploadBatchMapper.getFilePathsByAnswerCardCode", params);
        for (Map<String, Object> map : list) {
            Object filePath = map.get("filePath");
            convertTifToPng((String) filePath);
            map.put("filePath", filePath);
        }
        return list;

    }


    public Object getStudentAbsentStatics(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();
        return commonRepository.selectList("UploadBatchMapper.getAbsentStatics", params);
    }


    @Autowired
    private NewExamResultService newExamResultService;

    /**
     * 导出（异常|缺考|正常）学生 excel
     *
     * @param params examUploaderId [resultStatus] [searchValue]
     * @return 学生列表excel
     */
    public List<Map<String, Object>> getAbsentStudents(Map<String, Object> params) {
        long examUploaderId = MapUtil.getLong(params, "examUploaderId", 0L);
        NewExamUploaderBO newExamUploaderBO = new NewExamUploaderBO();
        if (examUploaderId > 0) {
            newExamUploaderBO = newPlanExamUploaderService.getExamUploader(examUploaderId);
        } else {
            newExamUploaderBO.setExamId(MapUtil.getLong(params, "examId"));
            newExamUploaderBO.setPaperId(MapUtil.getLong(params, "paperId"));
        }

        long examId = newExamUploaderBO.getExamId();
        params.putIfAbsent("examId", examId);
        params.putIfAbsent("paperId", newExamUploaderBO.getPaperId());
        List<AbsentStudentVO> studentVOS = newExamResultService.downloadAbsentStudentList(newExamUploaderBO);
        BatchDataUtil.execute(studentVOS, absentStudentVOS -> {
            List<Long> stuIds = absentStudentVOS.stream().map(AbsentStudentVO::getStudentId).collect(toList());
            Map<Long, ExamStuExtVO> stuExtVOInfo = examStuExtService.findExtByExamAndStus(examId, stuIds);
            for (AbsentStudentVO vo : absentStudentVOS) {
                long stuId = vo.getStudentId();
                ExamStuExtVO stuExtVO = stuExtVOInfo.getOrDefault(stuId, null);
                if (null == stuExtVO) {
                    continue;
                }
                vo.setSeatNumber(stuExtVO.getSeatNumber());
                vo.setExamRoomNo(stuExtVO.getExamRoomNo());
            }
        });
        List<Map<String, Object>> studentList = studentVOS.stream().map(AbsentStudentVO::getDownloadRowData).collect(toList());
        return studentList;
    }

    public void updateBatchStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isNotBlank("deviceId")
//                .isNotBlank("batchScanCardNum")
                .isNotBlank("batchSeq")
                .isValidId("batchId")
                .verify();

        int batchScanCardNum = MapUtil.getInt(params, "batchScanCardNum", -1);
        if (batchScanCardNum > -1) {
            commonRepository.update("UploadBatchMapper.updateBatchScanCardNum", params);
        }
        int scanStatus = MapUtil.getInt(params, "scanStatus", -1);
        if (scanStatus == -1) {
            return;
        }
        params.put("scanStatus", scanStatus);
        commonRepository.update("UploadBatchMapper.updateScanStatus", params);
    }

    /**
     * 统计完成时的数据.
     *
     * @param params
     * @return
     */

    @Autowired
    private AnswerCardService answerCardService;

    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;

    @Resource
    private NewAnswerCardDao newAnswerCardDao;

    @Autowired
    private NewAnswerCardRecycleService NewAnswerCardRecycleService;

    public ExamUploaderCompleteStatVO getExamUploaderCompleteStatistic(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();
        long examUploaderId = MapUtil.getLong(params, "examUploaderId");
        NewExamUploaderBO bo = newPlanExamUploaderService.getExamUploader(examUploaderId);
        if (bo == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "答题卡上传人不存在");
        }
        checkStudentAnswerCardRepeat(params);
        ExamUploaderCompleteStatVO statistics = newAnswerCardDao.getExamUploaderCompleteStatistic(bo);
        List<Long> studentIds = NewAnswerCardRecycleService.getAbsentAnswerCardStudentIdsByExamUploaderId(bo, false);
        statistics.setAbsentStudentCount(studentIds.size());
        if (statistics.getAbsentStudentCount() > 0) {
            studentIds = NewAnswerCardRecycleService.getAbsentAnswerCardStudentIdsByExamUploaderId(bo, true);
            statistics.setFillingAbsentStudentCount(studentIds.size());
        } else {
            statistics.setFillingAbsentStudentCount(0);
        }
        statistics.setScanStudentCount(statistics.getNormalStudentCount() + statistics.getAbsentStudentCount());
        statistics.setUnuploadStudentCount(newExamResultService.getUnuploadedStudentCount(bo));
        statistics.setManualAbsentStudentCount(statistics.getAbsentStudentCount() - statistics.getFillingAbsentStudentCount());
        return statistics;
    }

    public void checkStudentAnswerCardRepeat(Map<String, Object> params) {
        Map<String, Object> data = commonRepository.selectOne("UploadBatchMapper.getStudentCardRepeat", params);
        int studentCount = MapUtil.getInt(data, "studentCount", 0);
        int answerCardCodeCount = MapUtil.getInt(data, "answerCardCodeCount", 0);

        if (studentCount < 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "处理完成的学生数为零！");
        }

        if (answerCardCodeCount > studentCount) {
            List<Map<String, Object>> studentCardList = commonRepository.selectList("UploadBatchMapper.getStudentCardCount", params);
            int minCount = commonRepository.selectOne("UploadBatchMapper.getAnswerCardCodeCount", params);
            studentCardList = studentCardList.stream().filter(item -> MapUtil.getInt(item, "c") > minCount).collect(toList());
            // 将这几个学生标记成答题卡重复，好让用户处理.
            if (studentCardList.size() > 0) {
                params.put("studentCardList", studentCardList);
                commonRepository.update("ExceptionCard.updateCardRepeat", params);
            }
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "处理完成的学生数小于答题卡份数！");
        }
    }

    @Autowired
    private IRecognitionTaskService recognitionTaskService;

    public void deleteBatchesForCancel(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();

        commonRepository.delete("UploadBatchMapper.deleteBatchesForCancel", params);
        params.put("abandonType", 1);
        recognitionTaskService.deleteRecognitionTaskByCancel(params);
        answerCardRecycle.deleteRecycle(params);
    }

    public void handleActionExamUploader(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();
        String action = MapUtil.getString(params, "action", "");
        String mapName = ResourceConfig.getString("answerCardRecognitionMap") + params.get("examUploaderId");
        int abandonType = MapUtil.getInt(params, "abandonType");
        if ("lock".equals(action)) {
            Map<String, String> redisMap = new HashMap<>();
            redisMap.put("abandonType", Integer.toString(abandonType));
            redisMap.put("templateType", Integer.toString(MapUtil.getInt(params, "templateType", 0)));
            redisMap.put("templateCode", Integer.toString(MapUtil.getInt(params, "templateCode", 0)));
            JedisTemplate.executePipeline(pipeline -> {
                pipeline.hmset(mapName, redisMap);
                pipeline.expire(mapName, AnswerCardConstants.CARD_LOCK_EXPIRE_SECONDS);
            });

            params.put("abandonInfo", JsonUtil.toJson(redisMap));
            commonRepository.update("RecognitionTaskMapper.updateAbandonInfo", params);

        } else {
            JedisTemplate.execute(pipeline -> {
                boolean isExists = pipeline.exists(mapName);
                if (isExists) {
                    pipeline.del(mapName);
                }
                return isExists;
            });
        }
    }

    public boolean isExamUploaderLock(Map<String, Object> params) {
        long examUploaderId = MapUtil.getLong(params, "examUploaderId");
        String mapName = ResourceConfig.getString("answerCardRecognitionMap") + examUploaderId;
        return JedisTemplate.execute(pipeline -> {
            boolean isExists = pipeline.exists(mapName);
            return isExists;
        });
    }

    // 当替换答题卡的时候，将正在进行中的任务锁住，不让扫描识别任务.
    public void updateExamPaperLockExamUploaders(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("oldPaperId")
                .verify();

        String action = MapUtil.getString(params, "action");
        List<Map<String, Object>> examUploaders = commonRepository.selectList("RecognitionTaskMapper.getRecognitionExamUploaders", params);
        if (CollectionUtils.isEmpty(examUploaders))
            return;

        for (Map<String, Object> examUploader : examUploaders) {
            MsgProducer producer = RTRedisService.getProducer();
            if (producer != null) {
                producer.removeMsg(MapUtil.getLong(examUploader, "examUploaderId"));
            }
            examUploader.put("action", action);
            examUploader.put("abandonType", 2);
            handleActionExamUploader(examUploader);
        }
    }

    /**
     * 判断批次上传是否结束
     * 本地上传，入库并且scan_status = 0. batchType == 2.
     * 边扫边传的批次的话，还得判断答题卡张数是否等于扫描张数.
     *
     * @param params
     * @return
     */
    public boolean isBatchFinished(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("batchId")
                .isValidId("examUploaderId")
                .verify();

        Long batchId = MapUtil.getLong(params, "batchId");
        List<Map<String, Object>> uploadBatches = commonRepository.selectList("UploadBatchMapper.getUploadBatchFinished", params);
        uploadBatches = uploadBatches.stream().filter(item -> {
            Long _batchId = MapUtil.getLong(item, "batchId", 0L);
            boolean isEquals = _batchId.equals(batchId);
            return isEquals;
        }).collect(toList());
        boolean isFinished;
        if (uploadBatches != null && uploadBatches.size() > 0) {
            Map<String, Object> batchInfo = uploadBatches.get(0);
            isFinished = isUploadBatchFinished(batchInfo);
        } else {

            isFinished = true;
        }
        return isFinished;
    }

    private boolean isUploadBatchFinished(Map<String, Object> batchInfo) {
        boolean isFinished = MapUtil.getInt(batchInfo, "batchType") == 2;
        if (!isFinished) {
            int scanStatus = MapUtil.getInt(batchInfo, "scanStatus", 1);
            if (scanStatus == 0) {
                int cardCount = MapUtil.getInt(batchInfo, "cardCount", 0);
                int deleteCardCount = MapUtil.getInt(batchInfo, "deleteCardCount", 0);
                int batchScanCardNum = MapUtil.getInt(batchInfo, "batchScanCardNum", 0);
                int uploadedCardCount = MapUtil.getInt(batchInfo, "uploadedCardCount", 0);
                isFinished = batchScanCardNum <= (cardCount + deleteCardCount)
                        || batchScanCardNum <= uploadedCardCount;
            }
        }
        return isFinished;
    }

    /**
     * 将活跃的批次设置待重组.
     *
     * @param params
     */
    public void updateAliveBatches(Map<String, Object> params) {
        List<Map<String, Object>> aliveUploadBatches = getAliveBatches(params);
        if (aliveUploadBatches.size() > 0) {
            params.put("aliveUploadBatches", aliveUploadBatches);
            params.put("needRecombineCard", 1);
            commonRepository.update("UploadBatchMapper.updateBatchNeedRecombineCards", params);
        }
    }

    public List<Map<String, Object>> getAliveBatches(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();

        List<Map<String, Object>> uploadBatches = commonRepository.selectList("UploadBatchMapper.getUploadBatchFinished", params);
        uploadBatches = uploadBatches.stream().filter(uploadBatchInfo -> !isUploadBatchFinished(uploadBatchInfo)).collect(toList());
        return uploadBatches;
    }

    public boolean isExamUploaderUploadFinished(Map<String, Object> params) {
        return CollectionUtil.isEmpty(getAliveBatches(params));
    }

    @Autowired
    private NewAnswerCardService newAnswerCardService;

    /**
     * 定时任务，将表里活跃的批次已经上传结束进行重组，并改为need_recombine_card = 0.
     */
    public void updateNeedRecombineCards() {
        // 查询批次表里需要重组的数量.
        List<Map<String, Object>> aliveBatches = commonRepository.selectList("UploadBatchMapper.getAliveBatches");
        if (aliveBatches.size() > 0) {
            Map<Long, List<Map<String, Object>>> examUploaderBatches = aliveBatches.stream().collect(groupingBy(batchInfo -> MapUtil.getLong(batchInfo, "examUploaderId")));
            Map<String, Object> params = new HashMap<>();
            for (Long examUploaderId : examUploaderBatches.keySet()) {
                params.put("examUploaderId", examUploaderId);
                // 此时此刻还活跃的.
                List<Map<String, Object>> examUploaderAliveBatches = getAliveBatches(params);
                // 上一个时间点还活跃的.
                List<Map<String, Object>> _examUploaderAliveBatches_ = examUploaderBatches.get(examUploaderId);
                _examUploaderAliveBatches_ = _examUploaderAliveBatches_.stream().filter(_batchInfo_ -> {
                    Long _batchId_ = MapUtil.getLong(_batchInfo_, "batchId");
                    return !examUploaderAliveBatches.stream().anyMatch(batchInfo -> {
                        Long batchId = MapUtil.getLong(_batchInfo_, "batchId");
                        return _batchId_.equals(batchId);
                    });
                }).collect(toList());

                if (_examUploaderAliveBatches_.size() > 0) {
                    Map<String, Object> examUploader = commonRepository.selectOne("AnswerCardMapper.selectExamUploader", params);
                    params.putAll(examUploader);
                    params.put("userId", _examUploaderAliveBatches_.get(0).get("userId"));
                    params.put("userName", _examUploaderAliveBatches_.get(0).get("userName"));
                    params.put("needBatches", _examUploaderAliveBatches_);
                    newAnswerCardService.recombineAnswerCards(params, answerCardService.getTemplateNumber(params));
                    params.put("needRecombineCard", 0);
                    commonRepository.update("UploadBatchMapper.updateBatchNeedRecombineCards", params);
                }
            }
        }
    }

    /**
     * 判断该批次是否有等待识别，或者即将进入识别.
     *
     * @param params
     * @return
     */
    public boolean isBatchWaitingOrRecognition(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isValidId("batchId")
                .verify();

        int count = commonRepository.selectOne("UploadBatchMapper.getWaitingOrRecognitionCardCount", params);
        return count > 0;
    }


    public void updateBatches2NormalExamUploader(List<Map<String, Object>> cards, Long examUploaderId) {
        if (CollectionUtils.isEmpty(cards)) {
            return;
        }
        Set<Long> batchIds = cards.stream().map(item -> MapUtil.getLong(item, "batchId")).collect(toSet());
        Map<String, Object> params = new HashMap<>();
        params.put("batchIds", batchIds);
        params.put("examUploaderId", examUploaderId);
        commonRepository.update("UploadBatchMapper.updateBatchesWithExamUploader", params);
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void updateBatch(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("batchId")
                .verify();
        int cardExceptionNum = MapUtil.getInt(params, "cardExceptionNum", 0);
        int cardExceptionDeleteNum = MapUtil.getInt(params, "cardExceptionDeleteNum", 0);

        if (cardExceptionNum == 0 && cardExceptionDeleteNum == 0) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "更新参数不合法！");
        }
        commonRepository.update("UploadBatchMapper.updateBatch", params);
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void updateBatch2(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("batchId")
                .verify();
        commonRepository.update("UploadBatchMapper.updateBatch2", params);
    }

    /**
     * 转移批次里的异常答题卡...
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void transferExceptionCard(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isValidId("toPaperId")
                .isValidId("batchId")
                .verify();
        long examUploaderId = MapUtil.getLong(params, "examUploaderId");
        long batchId = MapUtil.getLong(params, "batchId");
        List<AnswerCardVO> cardVOList = newAnswerCardDao.getBatchExceptionCards(examUploaderId, batchId);
        if (cardVOList.size() < 1) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "批次没有异常的答题卡，无法进行转移！");
        }
        exceptionCardImpl.handleTransferCards(params, cardVOList);
    }

    @Autowired
    private ExamService examService;

    @Autowired
    private NewExamPaperService newExamPaperService;

    @Autowired
    private NewExamSchoolService newExamSchoolService;

    public String getDLFileName(Map<String, Object> params) {
        Map<String, Object> examInfo = examService.getExamDetail(params);
        long examId = MapUtil.getLong(params, "examId");
        long paperId = MapUtil.getLong(params, "paperId");
        ExamPaperDTO examPaperDTO = newExamPaperService.getExamPaperDTOOfExamIdAndPaperId(examId, paperId);
        String fileName = examPaperDTO.getCourseName() + "_未上传及缺考考生";
        int correctMode = MapUtil.getInt(examInfo, "correctMode");
        long examUploaderId = MapUtil.getLong(params, "examUploaderId", 0L);
        NewExamUploaderBO newExamUploaderBO = null;
        if (examUploaderId > 0L) {
            newExamUploaderBO = newPlanExamUploaderService.getExamUploader(examUploaderId);
        }
        if (correctMode == 0) {
            int size = null != newExamUploaderBO && newExamUploaderBO.getClassIdList() != null ? newExamUploaderBO.getClassIdList().size() : 0;
            if (size == 1) {
                fileName += "_" + newExamUploaderBO.getClassNames();
            }
        }
        if(null != newExamUploaderBO && newExamUploaderBO.getSchoolId() > 0L) {
            ExamSchoolVO examSchoolVO = newExamSchoolService.getExamSchoolOfExamIdAndSchoolId(newExamUploaderBO.getExamId(), newExamUploaderBO.getSchoolId());
            fileName += "_" + examSchoolVO.getSchoolName();
        }
        fileName += NewExamResultService.getDownloadDate();
        return fileName;
    }

    @Resource
    private NewAnswerCardRecycleDao newAnswerCardRecycleDao;
    public List<String> getBatchCardList(Map<String, Object> params) {
        long examUploaderId = MapUtil.getLong(params, "examUploaderId");
        Long batchId =  commonRepository.selectOne("UploadBatchMapper.getBatchId", params);
        if(null == batchId) {
            return Collections.emptyList();
        }
        List<String> fileNames = newAnswerCardDao.getBatchFileNames(examUploaderId, batchId);
        List<String> bakFileNames = newAnswerCardRecycleDao.getBatchFileNames(examUploaderId, batchId);
        if(!CollectionUtils.isEmpty(bakFileNames)) {
            fileNames.addAll(bakFileNames);
        }
        return fileNames;
    }
}
