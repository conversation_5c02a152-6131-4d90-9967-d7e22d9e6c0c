package com.dongni.exam.card.controller;

import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.card.service.AnswerCardManualService;
import com.dongni.exam.config.ExamConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/1/18.
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/answerCard/manual")
public class AnswerCardManualController extends BaseController {

    @Autowired
    private AnswerCardManualService answerCardManualService;

    /**
     * 将tif图片转为png图片，支持批量转换
     * @param paramMap fileUrls 图片cdn相对路径集合
     * @return 转换后的图片cdn相对路径集合
     */
    @PostMapping(value = "/paper/answerCard/tif")
    public Response convertTifToPng(@RequestBody Map<String, Object> paramMap) {
        return new Response(answerCardManualService.convertTifToPng(paramMap));
    }

    /**
     * 手动绘制的答题卡模板保存方法
     *
     * @param parameterMap 需要保存的答题卡模板信息
     * @return templateCode
     */
    @RequestMapping(value = "/paper/answerCard/template/manual", method = RequestMethod.POST)
    public Response saveAnswerCardTemplateManual(@RequestBody Map<String, Object> parameterMap) {
        return new Response(answerCardManualService.saveAnswerCardTemplateManual(parameterMap));
    }


    /**
     * 编辑试题结构后保存三方模板方法
     *
     * @param parameterMap 需要保存的答题卡模板信息
     *
     * @return templateCode
     */
    @RequestMapping(value = "/paper/answerCard/template/update", method = RequestMethod.POST)
    public Response resetPaperAndSaveAnswerCardTemplateManual(@RequestBody Map<String, Object> parameterMap) {
        return new Response(answerCardManualService.resetPaperAndSaveAnswerCardTemplateManual(parameterMap));
    }


    /**
     * 手动绘制的答题卡模板-客观题自动绘制
     *
     * @param parameterMap 需要识别的答题卡客观题信息
     * @return 识别结果
     */
    @RequestMapping(value = "/paper/answerCard/template/manual/choiceAutoDraw", method = RequestMethod.POST)
    public Response choiceAutoDraw(@RequestBody Map<String, Object> parameterMap) {
        return new Response(answerCardManualService.choiceAutoDraw(parameterMap));
    }


    // dongni-dev-internal.dongni100.com
    // 科目检测
    @RequestMapping(value = "/paper/answerCard/template/manual/subjectCheck", method = RequestMethod.POST)
    public Response subjectCheck(@RequestBody Map<String, Object> parameterMap) {
        return new Response(answerCardManualService.subjectCheck(parameterMap));
    }


    @RequestMapping(value = "/paper/answerCard/template/manual/findTemplatePoint", method = RequestMethod.POST)
    public Response findTemplatePoint(@RequestBody Map<String, Object> parameterMap) {
        return new Response(answerCardManualService.findTemplatePoint(parameterMap));
    }

    //文档图像自动判断大小度和小角度
    @RequestMapping(value = "/paper/answerCard/template/manual/textImageOrientation", method = RequestMethod.POST)
    public Response textImageOrientation(@RequestBody Map<String, Object> parameterMap) {
        return new Response(answerCardManualService.textImageOrientation(parameterMap));
    }

    @RequestMapping(value = "/paper/answerCard/template/manual/autoPlotFeature", method = RequestMethod.POST)
    public Response autoPlotFeature(@RequestBody Map<String, Object> parameterMap) {
        return new Response(answerCardManualService.autoPlotFeature(parameterMap));
    }

    /**
     * 更新模板参数
     *
     * @return
     */
    @RequestMapping(value ="/paper/answerCard/template/manual/update", method = RequestMethod.POST)
    public com.dongni.common.entity.Response updateAnswerCardTemplateManual(@RequestBody Map<String, Object> parameterMap) {
        answerCardManualService.updateAnswerCardTemplateManual(parameterMap);
        return new com.dongni.common.entity.Response();
    }

}
