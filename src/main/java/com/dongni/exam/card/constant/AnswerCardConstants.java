package com.dongni.exam.card.constant;

/**
 * 答题卡常量类
 * <AUTHOR>
 */
public final class AnswerCardConstants {
    private AnswerCardConstants() {}

    /**
     * redis中的答题卡key标志过期时间为24小时
     */
    public static final int CARD_KEY_EXPIRE_SECONDS = 86400;

    public static final int CARD_LOCK_EXPIRE_SECONDS = 300;

    /**
     * 判定识别异常的最后修改时间间隔为2分钟
     */
    public static final int CARD_EXCEPTION_TIME_DURATION = 120000;

    /**
     * 设定网络客户端那边过来识别任务大小：
     */
    public static final int CARD_TASK_GROUP_SIZE = 4;

    public static final int MAX_CARD_QUERY_SIZE = 256;

}
