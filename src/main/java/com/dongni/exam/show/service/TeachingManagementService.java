package com.dongni.exam.show.service;

import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.area.service.CommonAreaService;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ Author     ：guo zhengming
 * @ Date       ：Created in 13:45 2020/4/22
 * @ Description：教学管理
 * @ Modified By：
 */
@Service
public class TeachingManagementService {
    @Autowired
    private CommonAreaService commonAreaService;
    @Autowired
    private BaseDataRepository commonRepository;


    public Map<String, Object> getTeachingManagementData(Map<String, Object> params) {
        Verify.of(params).isValidId("areaId").isNotBlank("stage");
        Map<String, Object> rs = new HashMap<>();
        Map<String, Object> allSchoolData = new HashMap<>();
        List<Map<String, Object>> schoolData = new ArrayList<>();
        Map<String, Object> area = commonAreaService.getArea(params);
        area.put("stage", params.get("stage"));
        List<Map<String, Object>> schools = commonAreaService.getAreaSchool(area);
        //过滤掉已经注销的学校
        List<Map<String, Object>> areaSchool = schools
                .stream().filter(item -> ObjectUtil.isValueEquals(item.get("schoolStatus").toString(), 1))
                .collect(Collectors.toList());

        List<Object> schoolIds = areaSchool.stream().map(item -> item.get("schoolId")).collect(Collectors.toList());
        List<Map<String, Object>> schoolTeachers;
        List<Map<String, Object>> schoolStudents;
        if (CollectionUtils.isEmpty(schoolIds)) {
            schoolTeachers = new ArrayList<>();
            schoolStudents = new ArrayList<>();
        } else {
//            schoolTeachers = commonRepository
//                    .selectList("CommonSchoolMapper.getSchoolTeacher", MapUtil.of("schoolIds", schoolIds));
//            schoolStudents = commonRepository
//                    .selectList("CommonSchoolMapper.getSchoolStudent", MapUtil.of("schoolIds", schoolIds));
            schoolTeachers = BatchDataUtil.submit(schoolIds, ids -> commonRepository
                    .selectList("CommonSchoolMapper.getSchoolTeacher", MapUtil.of("schoolIds", ids)), 100);
            schoolStudents = BatchDataUtil.submit(schoolIds, ids -> commonRepository
                    .selectList("CommonSchoolMapper.getSchoolStudent", MapUtil.of("schoolIds", ids)), 50);
        }

        allSchoolData.put("schoolNumber", areaSchool.size());
        allSchoolData.put("schoolTeacherNumber", schoolTeachers.size());
        allSchoolData.put("schoolStudentNumber", schoolStudents.size());
        areaSchool.forEach(item -> {
            Long schoolId = Long.valueOf(item.get("schoolId").toString());
            Map<String, Object> data = new HashMap<>(item);
            List<Map<String, Object>> teachers = schoolTeachers.stream()
                    .filter(t -> Long.valueOf(t.get("schoolId").toString()).equals(schoolId))
                    .collect(Collectors.toList());
            List<Map<String, Object>> students = schoolStudents.stream()
                    .filter(t -> Long.valueOf(t.get("schoolId").toString()).equals(schoolId))
                    .collect(Collectors.toList());
            data.put("schoolTeacherNumber", teachers.size());
            data.put("schoolStudentNumber", students.size());
            schoolData.add(data);
        });

        rs.put("allSchoolData", allSchoolData);
        rs.put("schoolData", schoolData);
        return rs;
    }
}
