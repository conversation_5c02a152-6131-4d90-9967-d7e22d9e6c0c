package com.dongni.exam.hall.plan.service;

import java.util.List;
import java.util.Map;

/**
 * 考务工作中心业务接口
 *
 * <AUTHOR>
 * @date 2018/10/29 17:38
 */
public interface IHallPlanService {

    /**
     * 获取考务安排列表
     *
     * @param params schoolId gradeId [pageNo] [pageSize]
     * @return
     */
    Map<String, Object> getHallList(Map<String, Object> params);

    /**
     * 获取考务详情
     *
     * @param params hallId
     * @return
     */
    Map<String, Object> getHallDetail(Map<String, Object> params);

    /**
     * 获取班级
     *
     * @param params gradeId [pageNo] [pageSize]
     * @return
     */
    Map<String, Object> getClassList(Map<String, Object> params);

    /**
     * 获取科目
     *
     * @param params schoolId stage
     * @return
     */
    List<Map<String, Object>> getCourseList(Map<String, Object> params);

    /**
     * 查询年级考试
     *
     * gradeId
     * @return 考试结果
     */
    List<Map<String, Object>> getExamGrade(Map<String,Object> parameterMap);

    /**
     * 获取以往考务安排
     *
     * @param params schoolId [pageNo] [pageSize]
     * @return
     */
    Map<String, Object> getHallHistoryList(Map<String, Object> params);

    /**
     * 获取考场
     *
     * @param params hallId
     * @return
     */
    List<Map<String, Object>> getHallPlaceList(Map<String, Object> params);

    /**
     * 新增考务
     *
     * @param params hallName schoolId gradeId gradeType startDate endDate
     *               classList: [
     *                  classId className teacherId teacherName artsScience studentCount
     *               ]
     *               courseList: [
     *                  courseId courseName startDate endDate artsScience
     *               ]
     *               proctorList: [
     *                  teacherRelativeId teacherType teacherName teacherPhone gradeId gradeType courseId courseName
     *                  invigilateStatus invigilateCount
     *               ]
     */
    void insertHall(Map<String, Object> params);

    /**
     * 删除考务
     *
     * @param params hallId
     */
    void deleteHall(Map<String, Object> params);

}
