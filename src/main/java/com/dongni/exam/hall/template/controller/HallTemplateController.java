package com.dongni.exam.hall.template.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.hall.template.service.IHallTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * Created by scott
 * time: 11:52 2018/10/29
 * description:考场模板管理
 */
@RestController
@RequestMapping(value = "/exam/hall/template")
public class HallTemplateController extends BaseController {

    @Autowired
    private IHallTemplateService hallTemplateService;

    /**
     * 新增考场
     *
     * @return 结果
     */
    @PostMapping("")
    public Response insertHallTemplate(@RequestBody Map<String, Object> params) {
        hallTemplateService.insertHallTemplate(params);
        return new Response();
    }

    /**
     *  非GET/POST治理 考试
     *  删除考场
     *
     * @return 结果
     */
    @DeleteMapping("")
    @Deprecated
    public Response deleteHallTemplate(@RequestBody Map<String, Object> params) {
        hallTemplateService.deleteHallTemplate(params);
        return new Response();
    }

    /**
     * 删除考场
     *
     * @return 结果
     */
    @PostMapping("/delete")
    public Response deleteHallTemplate1(@RequestBody Map<String, Object> params) {
        hallTemplateService.deleteHallTemplate(params);
        return new Response();
    }

    /**
     * 非GET/POST治理 考试
     * 更新考场
     *
     * @return 结果
     */
    @PutMapping("")
    @Deprecated
    public Response updateHallTemplate(@RequestBody Map<String, Object> params) {
        hallTemplateService.updateHallTemplate(params);
        return new Response();
    }

    /**
     * 更新考场
     *
     * @return 结果
     */
    @PostMapping("/update")
    public Response updateHallTemplate1(@RequestBody Map<String, Object> params) {
        hallTemplateService.updateHallTemplate(params);
        return new Response();
    }

    /**
     * 获取考场
     *
     * @return 考场
     */
    @GetMapping("")
    public Response getHallTemplate() {
        return new Response(hallTemplateService.getHallTemplate(getParameterMap()));
    }

    /**
     * 获取考场详情
     *
     * @return 考场详情
     */
    @RequestMapping(value = "/info")
    public Response getHallTemplateInfo() {
        return new Response(hallTemplateService.getHallTemplateInfo(getParameterMap()));
    }

    /**
     * 获取楼栋教室
     *
     * @return 楼栋教室
     */
    @RequestMapping(value = "/floor/room")
    public Response getHallTemplateFloorRoom() {
        return new Response(hallTemplateService.getHallTemplateFloorRoom(getParameterMap()));
    }


}
