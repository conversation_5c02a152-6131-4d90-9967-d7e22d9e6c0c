package com.dongni.exam.hall.rule.assigning;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/11/1.
 */
public class AssigningUtil {

    /**
     * 初始化
     * @param course
     * @return
     */
    public static Map<Long,Set<Long>> initInvigilateCourse(List<Map<String, Object>> course){
        Map<Long,Set<Long>> invigilateCourse = new HashMap<>();
        for (int i = 0; i < course.size(); i++){
            Map<String,Object> c = course.get(i);
            long min = ((Date) c.get("startDate")).getTime();
            long max = ((Date) c.get("endDate")).getTime();

            Set<Long> ids = new HashSet<>();
            long id = Long.parseLong(c.get("courseId").toString());

            for (int j = 0; j < course.size(); j++){
                if(i == j){
                    ids.add(id);
                    continue;
                }

                long startDate = ((Date) course.get(j).get("startDate")).getTime();
                long endDate = ((Date) course.get(j).get("endDate")).getTime();
                if((min >= startDate && min <= endDate) || (max >= startDate && max <= endDate) || (min <= startDate && max >= endDate)){
                    ids.add(id);
                }
            }

            invigilateCourse.put(id,ids);
        }

        return invigilateCourse;
    }

    /**
     *
     * @param assigned
     * @param invigilateCourse
     * @return
     */
    public static Map<Long,Set<Long>> initInvigilateCount(List<Map<String,Object>> assigned,Map<Long,Set<Long>> invigilateCourse){
        Map<Long,Set<Long>> invigilateCount = new HashMap<>();
        if(CollectionUtils.isNotEmpty(assigned)){
            for (Map<String,Object> a : assigned){
                long proctorId = Long.valueOf(a.get("hallProctorId").toString());
                Set<Long> set = invigilateCount.getOrDefault(proctorId,new HashSet<>());
                set.addAll(invigilateCourse.get(Long.valueOf(a.get("courseId").toString())));
                invigilateCount.put(proctorId,set);
            }
        }
        return invigilateCount;
    }

    /**
     *
     * @param capacity
     * @param size
     * @param courseId
     * @param min
     * @param max
     * @param invigilateCourse
     * @param invigilateCount
     * @return
     */
    public static List<Map<String,Object>> getRandomElement(List<Map<String,Object>> capacity,
                                                             int size,
                                                             long courseId,
                                                             long min,
                                                             long max,
                                                             Map<Long,Set<Long>> invigilateCourse,
                                                             Map<Long,Set<Long>> invigilateCount){
        List<Map<String,Object>> rs = new ArrayList<>();
        loop : for ( ; rs.size() < size && capacity.size() > 0; ){

            // 这里需要规则校验（时间是否合适、场次是否合适）
            Map<String,Object> r = new HashMap<>(capacity.remove(RandomUtils.nextInt(0,capacity.size())));

            // 校验时间是否合适
            List<Map<String,Object>> pd = (List) r.get("hallProctorDate");
            if(CollectionUtils.isNotEmpty(pd)){
                for (Map<String,Object> d : pd){
                    long startDate = ((Date) d.get("startDate")).getTime();
                    long endDate = ((Date) d.get("endDate")).getTime();
                    // 校验时间交叉
                    if((min >= startDate && min <= endDate) || (max >= startDate && max <= endDate) || (min <= startDate && max >= endDate)){
                        continue loop;
                    }
                }
            }

            // 校验场次是否合适
            int limit = Integer.parseInt(r.get("invigilateCount").toString());
            Set<Long> courseIds = invigilateCount.getOrDefault(Long.valueOf(r.get("hallProctorId").toString()),new HashSet<>());
            if(courseIds.contains(courseId)){
                continue;
            }

            if(limit != -1){
                if(limit == 0) continue;

                if(courseIds.size() >= limit){
                    continue;
                }
            }

            rs.add(r);

            // 记录监考员监考的科目
            Long proctorId = Long.valueOf(r.get("hallProctorId").toString());
            courseIds.addAll(invigilateCourse.get(courseId));
            invigilateCount.put(proctorId,courseIds);
        }

        return rs;
    }


}
