package com.dongni.exam.exercise.gulou;

import com.dongni.common.entity.Response;
import com.dongni.exam.config.ExamConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/4/16 周三 下午 03:42
 * @Version 1.0.0
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/gulou/document")
public class GuLouDocumentController {
    @Autowired
    private GuLouDocumentService guLouDocumentService;

    /**
     * 手动调用推送接口
     *
     * @param params wrongDocumentIds userId userName
     */
    @PostMapping("/batchPush")
    public Response batchPush(Map<String, Object> params) {
        guLouDocumentService.batchPush(params);
        return new Response();
    }
}
