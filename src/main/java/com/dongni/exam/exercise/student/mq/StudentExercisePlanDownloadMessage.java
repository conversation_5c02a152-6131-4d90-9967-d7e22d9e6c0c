package com.dongni.exam.exercise.student.mq;

import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2024/07/12
 */
@Data
public class StudentExercisePlanDownloadMessage {
    
    /** 下载错题列表主键 */
    private Long wrongDocumentId;
    
    /** 下载文档参数 */
    private Map<String, Object> params;
    
    /** 序列化之前调用 用于解决map带来的数据问题 */
    void beforeSerialize() {
    
    }
    
    /** 反序列化之后调用 用于解决map带来的数据问题 */
    void afterDeserialize() {
    
    }
}
