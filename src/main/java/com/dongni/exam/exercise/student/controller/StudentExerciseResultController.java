package com.dongni.exam.exercise.student.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.exercise.student.service.StudentExerciseResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/11/18.
 *
 * 学生练习成绩
 */
@RestController
@RequestMapping("/exam/exercise/student/result")
public class StudentExerciseResultController extends BaseController {

    @Autowired
    private StudentExerciseResultService studentExerciseResultService;

    @GetMapping("/knowledge")
    public Response getKnowledge(){
        return new Response(studentExerciseResultService.getKnowledge(getParameterMap()));
    }
}
