package com.dongni.exam.mark.ai.bean;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/10/23 17:45
 */
@Data
public class OcrCompositionTrain {
    private Long examItemId;
    private Long examUploaderId;
    private Long examId;
    private Long paperId;
    private Integer grade;
    private Integer questionNumber;
    private String ocrContent;
    private String ocrResult;
    private Double scoreValue;
    private Double finallyScore;
    /**
     * 机器打分
     */
    private Double robotScore;
    /**
     * 分差
     */
    private Double scoreDiff;
    /**
     * 操作时间
     */
    private Date modifyTime;

    private Date createTime;
    /**
     * 操作批此标识
     */
    private String executeSign;

    private Integer status;
}
