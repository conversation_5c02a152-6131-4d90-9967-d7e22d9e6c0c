package com.dongni.exam.mark.ai.service;

import com.dongni.exam.mark.ai.bean.dto.EICDTO;
import com.dongni.exam.mark.ai.bean.vo.EICVO;
import com.dongni.exam.mark.ai.bean.vo.PostEICVO;

import java.util.List;

public interface IExamItemCutting {
	void insertExamItemCutting(PostEICVO postEICVO);

	List<EICVO> getQuestionEICList(PostEICVO postEICVO);

	void saveEICList(PostEICVO postEICVO);

	void locationQN(PostEICVO postEICVO);

	void deleteExamItemCuttingByQNs(long examId, long paperId, List<Integer> questionNumbers);

    int canOpenIntelligence(PostEICVO postEICVO);
}
