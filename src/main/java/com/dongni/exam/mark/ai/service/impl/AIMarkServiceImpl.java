package com.dongni.exam.mark.ai.service.impl;

import com.dongni.exam.mark.ai.OCRExamItem;
import com.dongni.exam.mark.ai.service.IAIMarkService;
import com.dongni.exam.mark.ai.service.status.IAIMarkStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AIMarkServiceImpl implements IAIMarkService {

    @Autowired
    private IAIMarkStatus initAIMarkStatus;

    @Autowired
    private IAIMarkStatus resultAIMarkStatus;

    @Override
    public void saveExamItemAIMarkList(List<OCRExamItem> itemList, int status) {
        IAIMarkStatus iaiMarkStatus = getAIMarkStatus(status);
        iaiMarkStatus.handle(itemList);
    }

    private IAIMarkStatus getAIMarkStatus(int status) {
        return status == 0 ? initAIMarkStatus : resultAIMarkStatus;
    }
}
