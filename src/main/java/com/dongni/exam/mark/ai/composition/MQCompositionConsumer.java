package com.dongni.exam.mark.ai.composition;

import com.dongni.commons.mq.consumer.BaseMessageConsumer;
import com.dongni.exam.mark.ai.composition.entity.AICompositionItem;
import com.dongni.exam.mark.ai.composition.entity.ExamItemDto;
import com.dongni.exam.mark.ai.composition.service.impl.AICompositionServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.unit.DataSize;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/16 15:43
 * @description:
 */
@Service
public class MQCompositionConsumer extends BaseMessageConsumer<ExamItemDto> {
    private static final Logger logger = LoggerFactory.getLogger(MQCompositionConsumer.class);
    public static final String PAPER_READ_OCR_COMPOSITION_REC = "PAPER_READ_OCR_COMPOSITION_REC";
    @Override
    protected TypeReference<ExamItemDto> getTypeReference() {
        return new TypeReference<ExamItemDto>() {};
    }

    @Override
    protected String getTopic() {
        return PAPER_READ_OCR_COMPOSITION_REC;
    }

    @Autowired
    private AICompositionServiceImpl aiCompositionService;
    @Override
    public void onMessages(Collection<ExamItemDto> messageList) {
        logger.info("MQCompositionConsumer start, handle size: {}", messageList.size());
        List<AICompositionItem> aiCompositionItems = messageList.stream().map(item -> {
            AICompositionItem aiCompositionItem = new AICompositionItem();
            aiCompositionItem.setGrade(item.getGradeType());
            aiCompositionItem.setUrl(item.getUrl());
            aiCompositionItem.setPaperReadId(item.getPaperReadId());
            aiCompositionItem.setExamItemId(item.getExamItemId());
            aiCompositionItem.setEngType(item.getEngType());
            aiCompositionItem.setRequiredNum(item.getRequiredNum());
            aiCompositionItem.setDifficultyMode(item.getDifficultyMode());
            return aiCompositionItem;
        }).collect(Collectors.toList());
        aiCompositionService.handleAIComposition(aiCompositionItems);
    }
    @Override
    public int getFetchMinBytes() {
        return (int) DataSize.ofKilobytes(4).toBytes();
    }

    @Override
    protected int getMaxFetchBytes() {
        return getFetchMinBytes() * 3;
    }
}
