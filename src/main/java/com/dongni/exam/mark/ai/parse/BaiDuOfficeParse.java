package com.dongni.exam.mark.ai.parse;

import com.dongni.exam.mark.ai.CombineImage;
import com.dongni.exam.mark.ai.CombineItem;
import com.dongni.exam.mark.ai.bean.AIResult;
import com.dongni.exam.mark.ai.bean.BaiDuOfficeResult;
import com.dongni.exam.mark.ai.parse.impl.BaiDuOfficeLocationWord;
import com.hqjl.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/18 10:50
 * @description:
 */
public class BaiDuOfficeParse implements IParse {
    private static final Logger logger = LoggerFactory.getLogger(BaiDuHWParse.class);

    private CombineImage combineImage;

    private BaiDuOfficeResult baiDuOfficeResult;

    public BaiDuOfficeParse(CombineImage image, String result) {
        combineImage = image;
        baiDuOfficeResult = JSONUtil.json2Obj(result, BaiDuOfficeResult.class);
    }


    @Override
    public void parse() {
        int errorCode = baiDuOfficeResult.getError_code();
        List<CombineItem> itemList = combineImage.getItems();

        if (errorCode != 0) {
            return;
        }

        itemList.forEach(item -> {
            item.setLocationWord(new BaiDuOfficeLocationWord());
            item.setRecognitionType(combineImage.getRecognitionType());
        });
        List<BaiDuOfficeResult.Words> wordsList = baiDuOfficeResult.getResults();
        itemList.forEach(item -> {
            // 将识别结果放到对于的CombineItem
            wordsList.forEach(word -> {
                item.getLocationWord().addWord(item, word);
            });
            item.getLocationWord().parse(item);
        });
    }

    @Override
    public AIResult getResult() {
        return this.baiDuOfficeResult;
    }
}
