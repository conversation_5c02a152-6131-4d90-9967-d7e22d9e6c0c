package com.dongni.exam.mark.ai.parse.impl;

import com.dongni.exam.mark.ai.CombineItem;
import com.dongni.exam.mark.ai.OCRExamItem;
import com.dongni.exam.mark.ai.bean.BaiDuHWResult;
import com.dongni.exam.mark.ai.bean.TencentResult;
import com.dongni.exam.mark.ai.parse.AbstractLocationWord;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/28
 */
public class TencentLocationWord extends AbstractLocationWord<TencentResult.Word> {
    private final static Logger logger = LoggerFactory.getLogger(TencentLocationWord.class);
    private List<TencentResult.Word> wordList;

    public void addWord(CombineItem combineItem,TencentResult.Word word) {
        if (!word.isUsed()) {
            List<TencentResult.Word.Point> points = word.getPolygon();
            if (isInclude(combineItem,points)) {
                if (wordList == null) {
                    wordList = new ArrayList<>();
                }
                word.setUsed(true);
                wordList.add(word);
            }
        }
    }

    private boolean isInclude(CombineItem<TencentLocationWord> combineItem,List<TencentResult.Word.Point> points) {
        BaiDuHWResult.Word.Location location = new BaiDuHWResult.Word.Location();
        location.setTop(points.get(0).getY());
        location.setLeft(points.get(0).getX());
        location.setWidth(points.get(1).getX() - points.get(0).getX());
        location.setWidth(points.get(3).getY() - points.get(0).getY());
        return isInclude(combineItem,location);
    }

    @Override
    public void parse(CombineItem combineItem) {
        OCRExamItem ocrExamItem = combineItem.getOcrExamItem();
        if (wordList != null) {
            wordList.sort(Comparator.comparingInt(w -> w.getPolygon().get(0).getX()));
            int i = 0;
            for (TencentResult.Word w : wordList) {
                String value = ocrExamItem.getRecognitionValue();
                String wv = w.getDetectedText();
                if(StringUtils.isNotBlank(wv)) {
                    wv = wv.replaceAll("☰", "");
                }
                if(StringUtils.isNotBlank(wv)) {
                    if(StringUtils.isNotBlank(value)) {
                        value += " ";
                    }
                    value += wv;
                    float average = w.getConfidence();
                    if (i == 0) {
                        ocrExamItem.setProbability(average);
                    } else {
                        if (ocrExamItem.getProbability() > average) {
                            ocrExamItem.setProbability(average);
                        }
                    }
                    i++;
                }
                ocrExamItem.setRecognitionValue(value);
            }
//            System.out.println("url = " + ocrExamItem.getUrl() + ", value = " + ocrExamItem.getRecognitionValue() + ", p = " + ocrExamItem.getProbability());
        } else {
            logger.error("parse itemId = {}, url = {} empty.", ocrExamItem.getItemId(), ocrExamItem.getUrl());
        }
    }
}
