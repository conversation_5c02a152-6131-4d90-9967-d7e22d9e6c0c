package com.dongni.exam.mark.service;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.repository.ICommonRepository;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by Heweipo on 2017/5/9.
 *
 * 典型错误标签CURD
 */
@Service
public class ExamMarkMistakeLabelService{

    @Autowired
    private ExamRepository commonRepository;


    /**
     * 获取系统标签以及该考试新建的标签
     *
     * @param params examPaperId
     * @return 系统标签以及该考试新建的标签
     */
    public List<Map<String, Object>> getMistakeLabel(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isNotBlank("examPaperId").verify();

        // 数据操作
        String examPaperId = params.get("examPaperId").toString();
        if(examPaperId.contains(":")){
            examPaperId = examPaperId.split(":")[0];
            params.put("examPaperId",examPaperId);
        }
        //根据examPaperId查询courseId
        Map<String,Object> course = commonRepository.selectOne("ExamMarkMistakeLabelMapper.getCourse",params);
        params.put("courseId",course.get("courseId"));
        return commonRepository.selectList("ExamMarkMistakeLabelMapper.getMistakeLabel",params);
    }

    /**
     * 新增考试典型错误标签
     *
     * @param params examPaperId mistakeName
     * @return examMistakeLabelId
     */
    public String insertMistakeLabel(Map<String, Object> params) {
        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .isNotBlank("examPaperId")
                .isNotBlank("mistakeName")
                .isValidId("teacherId")
                .verify();

        // 数据操作
        String examPaperId = params.get("examPaperId").toString();
        if(examPaperId.contains(":")){
            examPaperId = examPaperId.split(":")[0];
            params.put("examPaperId",examPaperId);
        }
        //根据examPaperId查询courseId
        Map<String,Object> course = commonRepository.selectOne("ExamMarkMistakeLabelMapper.getCourse",params);
        params.put("courseId",course.get("courseId"));
        params.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.insert("ExamMarkMistakeLabelMapper.insertMistakeLabel",params);

        return String.valueOf(params.get("examMistakeLabelId"));
    }

    /**
     * 删除未使用的标签
     *
     * @param params examMistakeLabelId
     */
    public void deleteMistakeLabel(Map<String, Object> params) {
        // 参数校验
        if(params == null || !ObjectUtil.isValidId(params.get("examMistakeLabelId"))){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        // 数据校验
        String id = commonRepository.selectOne("ExamMarkMistakeLabelMapper.getMistakeLabelItem",params);
        if(!ObjectUtil.isBlank(id)){
            throw new CommonException(ResponseStatusEnum.DATA_BE_USED,"标签已经被使用，不能删除");
        }

        // 数据操作
        commonRepository.delete("ExamMarkMistakeLabelMapper.deleteMistakeLabel",params);
    }
}
