package com.dongni.exam.mark.service.handle;

import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.mark.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;

import java.math.BigDecimal;
import java.util.*;

import static com.dongni.exam.mark.util.ExamMarkReadUtil.computePointAvg;

/**
 * Created by Heweipo on 2018/12/10.
 *
 * 阅卷中调整阅卷方式
 */
@Service
public class ExamMarkReadPaperTypeHandle {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private ExamMarkTodoService examMarkTodoService;
    @Autowired
    private ExamMarkMonitorService examMarkMonitorService;
    @Autowired
    private ExamMarkReadBlockService examMarkReadBlockService;
    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;
    @Autowired
    private ExamMarkCompleteService examMarkCompleteService;
    @Autowired
    private ExamMarkSynchroRecordService synchroRecordService;
    @Autowired
    private ExamMarkHandle examMarkHandle;

    /**
     * 阅卷中修改批改方式或仲裁分差
     * 1、Redis数据处理：1ToN，NTo1，MToN
     * 2、MySQL数据处理：paperRead 更新
     *
     * @param params examPaperId questionNumber readPaperType readPaperThreshold paperReadId
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateQuestionReadPaperType(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isNotBlank("examPaperId").isValidId("readBlockId").isNotBlank("readPaperType").verify();

        // 参数初始化
        String examPaperId = params.get("examPaperId").toString();
        Long start = System.currentTimeMillis();
        examMarkMonitorService.asynSaveMonitor(params,"阅卷中调整批改方式开始");
        log.info("阅卷进行中，{}修改考试{}批改方式开始.",params.get("userName"),examPaperId);

        // 考试基础数据校验
        Map<String,Object> exam = commonRepository.selectOne("ExamMarkReadPaperTypeHandleMapper.getExamPaper",params);
        if(MapUtils.isEmpty(exam)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"考试不存在或已被删除");
        }

        // 条件判断
        if(DictUtil.getDictValue("correctMode","readByClass") == Integer.parseInt(exam.get("correctMode").toString())){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"班级阅卷不支持修改批改方式，只能是一评");
        }

        int examPaperStatus = Integer.parseInt(exam.get("examPaperStatus").toString());
        if(!examMarkHandle.canExecuteHandle(examPaperStatus)){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,"阅卷中才需要从这里调整");
        }

        // 题块转换
        Map<String,Object> block = examMarkReadBlockService.getReadBlockMap(params).get(Long.valueOf(params.get("readBlockId").toString()));
        if(MapUtils.isEmpty(block)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"阅卷安排已经调整或该试题没有待批改");
        }
        String qnStr = block.get("questionNumber").toString();
        String paperReadIdStr = block.get("paperReadId").toString();

        // 数据初始化
        params.put("questionNumber",qnStr);
        params.put("paperReadId",paperReadIdStr);
        params.put("currentTime", DateUtil.getCurrentDateTime());
        int readPaperType = Integer.parseInt(params.get("readPaperType").toString());

        String examQnTraceKey = JedisUtil.getKey("examQnTrace",examPaperId,qnStr);
        Map<String,String> oldReadPaper = JedisTemplate.execute(jedis -> jedis.hgetAll(examQnTraceKey));
        if(MapUtils.isEmpty(oldReadPaper) || oldReadPaper.get("readPaperType") == null){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,"未找到对应题号的信息，请确认该题是否在阅卷中");
        }
        int oldReadPaperType = Integer.parseInt(oldReadPaper.get("readPaperType"));

        //int oldReadPaperType = commonRepository.selectOne("ExamMarkReadPaperTypeHandleMapper.getPaperReadType",params);
        if(readPaperType == oldReadPaperType){
            if(oldReadPaperType == 1){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"该题号的批改方式更新前后没有变化");
            }else {
                if(!ObjectUtil.isNumeric(params.get("readPaperThreshold"))){
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"多评必须指定阈值");
                }
                boolean typeEquals = params.get("thresholdType").equals(oldReadPaper.get("thresholdType"));
                boolean thresholdEquals = new BigDecimal(params.get("readPaperThreshold").toString()).compareTo(new BigDecimal(oldReadPaper.get("readPaperThreshold"))) == 0;
                if(thresholdEquals && typeEquals){
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"该题号的批改方式、仲裁分值、仲裁分差计算方式更新前后都没有变化");
                }
                if(new BigDecimal(params.get("readPaperThreshold").toString()).compareTo(BigDecimal.ZERO) == 0){
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"仲裁分值不能为 0");
                }
                examMarkMonitorService.asynSaveMonitor(params,"阅卷中调整仲裁分值/仲裁分差计算方式开始");
                JedisTemplate.execute(jedis -> {
                    jedis.hset(examQnTraceKey,"readPaperThreshold",params.get("readPaperThreshold").toString());
                    jedis.hset(examQnTraceKey, "thresholdType", params.get("thresholdType").toString());
                    return null;
                });
                commonRepository.update("ExamMarkReadPaperTypeHandleMapper.updatePaperRead", params);
                examMarkMonitorService.asynSaveMonitor(params,"阅卷中调整仲裁分值/仲裁分差计算方式完成");
                return;
            }
        }

        // 更新Redis
        if(readPaperType == 1){
            handleRedisQuestionReadNTo1(params);
        }

        if(readPaperType > 1){
            if(!ObjectUtil.isNumeric(params.get("readPaperThreshold"))){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"多评必须指定阈值");
            }

            if(oldReadPaperType == 1){

                String tids = handleRedisQuestionRead1ToN(params);

                Map<String,Object> p = new HashMap<>();
                p.put("tids",tids);
                p.put("examPaperId",examPaperId);
                examMarkTodoService.insertTeacherTodoTask(p);

            }else{

                handleRedisQuestionReadMToN(params,oldReadPaperType,readPaperType);

            }
        }

        // 更新数据库需要 paperReadId
        commonRepository.update("ExamMarkReadPaperTypeHandleMapper.updatePaperRead", params);

        // 日志记录一下
        examMarkMonitorService.asynSaveMonitor(params,"阅卷中调整批改方式："+oldReadPaperType+" 改为"+readPaperType+"成功");

        log.info("考试进行中，修改批改方式结束.总共耗时：" + (System.currentTimeMillis()-start));
    }


    /**
     * 阅卷中修改批改方式,一次阅卷变为N次阅卷
     */
    private String handleRedisQuestionRead1ToN(Map<String,Object> params){
        final String examPaperId = params.get("examPaperId").toString();
        final String questionNumber = params.get("questionNumber").toString();
        final int readType = Integer.valueOf(params.get("readPaperType").toString());
        final String readPaperThreshold = params.get("readPaperThreshold").toString();

        String examQnTraceKey = JedisUtil.getKey("examQnTrace",examPaperId,questionNumber);
        String examQnDoneKey = JedisUtil.getKey("examQnDone",examPaperId);
        return JedisTemplate.execute(jedis -> {

            // 1、readPaperType
            if(jedis.sismember(examQnDoneKey,questionNumber)){
                log.info("该题号{}已经批改完成，无需修改批改方式.",questionNumber);
                throw new CommonException(ResponseStatusEnum.DATA_BE_USED,"试题已经阅卷完成，无效操作");
            }

            // 处理该题阅卷老师的记录
            String readTeacherIds = jedis.hget(examQnTraceKey,"readTeacherIds");
            String[] tids = readTeacherIds.split(",");
            if(tids.length < readType){
                throw new CommonException(ResponseStatusEnum.DATA_ERROR,readType+"次阅卷老师数量不够");
            }

            // 2、更新 examQnDetail examQnTrace readPaperType
            Map<String,String> p = new HashMap<>();
            p.put("readPaperType",readType+"");
            p.put("readPaperThreshold",readPaperThreshold);
            p.put("thresholdType", params.get("thresholdType").toString());
            jedis.hmset(examQnTraceKey,p);

            // 遍历每一位老师，还原他们生效的批改记录
            for (String tid : tids){
                // 3、teacherQn teacherQnTodo teacherQnDone 老师题号库
                String teacherQnDoneKey = JedisUtil.getKey("teacherQnDone",examPaperId,tid);
                if(jedis.sismember(teacherQnDoneKey,questionNumber)){
                    String teacherQnTodoKey = JedisUtil.getKey("teacherQnTodo",examPaperId,tid);
                    jedis.smove(teacherQnDoneKey,teacherQnTodoKey,questionNumber);
                }

                // 4、readRecord 更新历史数据
                String teacherQnItemDoneKey = JedisUtil.getKey("teacherQnItemDone",examPaperId,tid,questionNumber);
                Set<String> items = jedis.smembers(teacherQnItemDoneKey);
                if(CollectionUtils.isNotEmpty(items)){
                    for (String item : items){
                        String record = JedisUtil.getKey("teacherReadRecord",examPaperId,tid,item);
                        jedis.hset(record,"choiceStatus","0");
                    }
                }

                // 老师存储的待批改试题应该也放到2库去
                if(jedis.scard(JedisUtil.getKey("teacherQnItemTodo",examPaperId,tid,questionNumber)) > 0){
                    jedis.sunionstore(JedisUtil.getKey("examRead2QnItemTodo",examPaperId,questionNumber),
                            JedisUtil.getKey("examRead2QnItemTodo",examPaperId,questionNumber),
                            JedisUtil.getKey("teacherQnItemTodo",examPaperId,tid,questionNumber));
                }
            }

            // 5、examReadQnItemDone examRead2QnItemTodo 已完成的迁移到2次库
            if(jedis.scard(JedisUtil.getKey("examReadQnItemDone",examPaperId,questionNumber)) > 0 ){
                jedis.sunionstore(JedisUtil.getKey("examRead2QnItemTodo",examPaperId,questionNumber),
                        JedisUtil.getKey("examRead2QnItemTodo",examPaperId,questionNumber),
                        JedisUtil.getKey("examReadQnItemDone",examPaperId,questionNumber));
                jedis.del(JedisUtil.getKey("examReadQnItemDone",examPaperId,questionNumber));
            }

            // 6、examQn examQnTodo examQnDone 考试题号库
            if(jedis.sismember(examQnDoneKey,questionNumber)){
                String examQnTodoKey = JedisUtil.getKey("examQnTodo",examPaperId);
                jedis.smove(examQnDoneKey,examQnTodoKey,questionNumber);
            }

            // 待办需要
            return readTeacherIds;
        });

    }


    /**
     * 阅卷中修改批改方式,N次阅卷变为一次阅卷
     */
    private void handleRedisQuestionReadNTo1(Map<String,Object> params){
        final String examPaperId = params.get("examPaperId").toString();
        final String questionNumber = params.get("questionNumber").toString();
        final String readType = params.get("readPaperType").toString();

        String examQnTraceKey = JedisUtil.getKey("examQnTrace",examPaperId,questionNumber);
        String readRecordDoneKey = JedisUtil.getKey("readRecordDone", examPaperId);
        String examReadQnItemDone = JedisUtil.getKey("examReadQnItemDone", examPaperId, questionNumber);
        String examRead1QnItemTodoKey = JedisUtil.getKey("examRead1QnItemTodo",examPaperId,questionNumber);
        String examRead2QnItemTodoKey = JedisUtil.getKey("examRead2QnItemTodo",examPaperId,questionNumber);
        String examQnDoneKey = JedisUtil.getKey("examQnDone",examPaperId);
        JedisTemplate.execute(jedis -> {

            // 1、获取比较 readPaperType
            String readPaperType = jedis.hget(examQnTraceKey,"readPaperType");
            if(jedis.sismember(examQnDoneKey,questionNumber)){
                log.info("该题号{}已经批改完成，无需修改批改方式.",questionNumber);
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"试题已经阅卷完成，无效操作");
            }

            // 2、更新 examQnDetail examQnTrace readPaperType
            params.put("readPaperThreshold",0);
            Map<String,String> p1 = new HashMap<>();
            p1.put("readPaperType",readType);
            p1.put("readPaperThreshold","0");
            jedis.hmset(examQnTraceKey,p1);
            jedis.del(examRead2QnItemTodoKey);


            // 3、把需要三评的试题都取平均分
            Set<String> items;
            String[] cids;
            String[] tids = jedis.hget(examQnTraceKey,"readTeacherIds").split(",");
            Set<String> delKes = new HashSet<>();

            int type = Integer.valueOf(readPaperType);
            if (type >= 3){
                String examRead3QnItemTodoKey = JedisUtil.getKey("examRead3QnItemTodo",examPaperId,questionNumber);
                items = jedis.smembers(examRead3QnItemTodoKey);
                delKes.add(examRead3QnItemTodoKey);

                // 四评
                if(type == 4){
                    String examRead4QnItemTodoKey = JedisUtil.getKey("examRead4QnItemTodo",examPaperId,questionNumber);
                    items.addAll(jedis.smembers(examRead4QnItemTodoKey));
                    delKes.add(examRead4QnItemTodoKey);
                }

                computeTwoTeacherAvg( examPaperId,readType,questionNumber,readRecordDoneKey,examReadQnItemDone,items,delKes,jedis);
            }

            // 二次阅卷
            else if(type == 2){
                // 负责人阅卷
                computePrincipalAvg(examPaperId,readType,questionNumber,readRecordDoneKey,examReadQnItemDone,examQnTraceKey,delKes,jedis);
            }

            // 4、处理只有一次批阅的记录
            for (String tid : tids){
                // 只要批改过1次，那么就作为最后得分
                String teacherQnItemDoneKey = JedisUtil.getKey("teacherQnItemDone",examPaperId,tid,questionNumber);
                Set<String> is = jedis.sdiff(teacherQnItemDoneKey,examReadQnItemDone);
                if(CollectionUtils.isNotEmpty(is)){
                    for (String item : is){
                        String record = JedisUtil.getKey("teacherReadRecord",examPaperId,tid,item);
                        jedis.hset(record,"choiceStatus","1");
                        if (item.contains(",")) {
                            String itemRecord = JedisUtil.getKey("teacherReadRecord",examPaperId,tid,item);
                            jedis.hset(itemRecord, "choiceStatus", "1");
                        }
                        jedis.sadd(examReadQnItemDone,item);
                    }
                }

                // 目的就是为了清理1次库
                String teacherQnItemTodoKey = JedisUtil.getKey("teacherQnItemTodo",examPaperId,tid,questionNumber);
                if(jedis.scard(teacherQnItemTodoKey) > 0){
                    jedis.sunionstore(examRead1QnItemTodoKey,examRead1QnItemTodoKey,teacherQnItemTodoKey);
                    jedis.del(teacherQnItemTodoKey);
                }
            }
            // 清理老师批阅待仲裁
            for (String tid : tids) {
                jedis.del(JedisUtil.getKey("teacherQnItemPrincipal", examPaperId, tid, questionNumber));
            }

            // 清理1次库,最后的清理
            jedis.sdiffstore(examRead1QnItemTodoKey,examRead1QnItemTodoKey,examReadQnItemDone);

            // 5 判断是否试题阅卷完成
            long count = jedis.scard(examReadQnItemDone);
            String resultItemCount = jedis.hget(examQnTraceKey,"resultItemCount");
            if(ObjectUtil.isValueEquals(count,resultItemCount)){

                // 该试题完成
                Map<String,Object> p = new HashMap<>();
                p.put("examPaperId",examPaperId);
                for (String tid : tids){
                    jedis.smove(JedisUtil.getKey("teacherQnTodo",examPaperId,tid),
                            JedisUtil.getKey("teacherQnDone",examPaperId,tid),questionNumber);

                    if(jedis.scard(JedisUtil.getKey("teacherQnTodo", examPaperId, tid)) == 0){
                        p.put("teacherId",tid);
                        examMarkTodoService.deleteTeacherTodoTask(p);
                    }
                }

                if(type != 3){
                    String chargeTeacherIds = jedis.hget(examQnTraceKey,"chargeTeacherIds");
                    cids =  chargeTeacherIds.split(",");
                    for (String cid : cids){
                        String principalQnTodoKey = JedisUtil.getKey("principalQnTodo", examPaperId, cid);
                        jedis.srem(principalQnTodoKey,questionNumber);
                        if(jedis.scard(principalQnTodoKey) == 0){
                            p.put("teacherId",cid);
                            examMarkTodoService.deletePrincipalTodoTask(p);
                        }
                    }
                }

                // 该科目完成
                String examQnTodoKey = JedisUtil.getKey("examQnTodo",examPaperId);
                jedis.smove(examQnTodoKey,examQnDoneKey,questionNumber);

                // 阅卷完成
                boolean isComplete = (jedis.scard(examQnTodoKey) == 0);
                if(isComplete){
                    params.put("isPrincipal", "0");
                    log.info("阅卷{}完成，批改方式修直接导致阅卷完成.",examPaperId);
                    examMarkMonitorService.asynSaveMonitor(params,"批改方式修直接导致阅卷完成");

                    // 执行阅卷完成
                    params.remove("questionNumber");
                    params.put("needAsync", true);
                    examMarkCompleteService.readComplete(params);
                    return true;
                }
                // 题号完成需要统计
                else {
                    log.info("阅卷{}题号{}已完成，批改方式修直接导致题号完成",examPaperId,questionNumber);
                    examMarkMonitorService.asynSaveMonitor(params,"批改方式修直接导致题号完成:"+questionNumber);

                    // 如果当前题号已完成，则需要异步执行统计
                    params.put("needAsync", true);
                    examMarkCompleteService.readComplete(params);
                    return true;
                }
            }

            return true;
        });
    }

    /**
     * 计算负责人需批改的平均分
     */
    private void computePrincipalAvg(String examPaperId,
                                     String readType,
                                     String questionNumber,
                                     String readRecordDoneKey,
                                     String examReadQnItemDone,
                                     String examQnTraceKey,
                                     Set<String> delKes,
                                     Jedis jedis){

        String chargeTeacherIds = jedis.hget(examQnTraceKey,"chargeTeacherIds");
        String[] cids =  chargeTeacherIds.split(",");
        String[] ckeys = new String[cids.length+1];
        String chargeQnItemTodoKey = JedisUtil.getKey("chargeQnItemTodo",examPaperId,questionNumber);
        ckeys[cids.length] = chargeQnItemTodoKey;
        for (int i = 0 ; i < cids.length; i++){
            ckeys[i] = JedisUtil.getKey("principalQnItemTodo",examPaperId,cids[i],questionNumber);
        }

        Set<String> items = jedis.sunion(ckeys);
        delKes.add(chargeQnItemTodoKey);
        delKes.addAll(Arrays.asList(ckeys));

        computeTwoTeacherAvg(examPaperId,readType,questionNumber,readRecordDoneKey,examReadQnItemDone,items,delKes,jedis);
        for (String cid : cids) {
            String principalQnTodo = JedisUtil.getKey("principalQnTodo", examPaperId, cid);
            jedis.srem(principalQnTodo, questionNumber);
            removePrincipalTask(examPaperId, jedis, cid, questionNumber);
        }
    }

    public void removePrincipalTask(String examPaperId, Jedis jedis, String cid, String questionNumber) {
        String principalQnKey = JedisUtil.getKey("principalQn", examPaperId, cid);
        Long principalQn = jedis.scard(principalQnKey);
        jedis.srem(principalQnKey, questionNumber);
        if (principalQn == null || principalQn == 0) {
            // 删除负责人仲裁待办
            Map<String, Object> deleteParams = new HashMap<>();
            deleteParams.put("teacherId", cid);
            deleteParams.put("examPaperId", examPaperId);
            examMarkTodoService.deletePrincipalTodoTask(deleteParams);
        }
    }

    /**
     * 计算两个老师的提交平均分
     */
    private void computeTwoTeacherAvg(String examPaperId,
                            String readType,
                            String questionNumber,
                            String readRecordDoneKey,
                            String examReadQnItemDone,
                            Set<String> items,
                            Set<String> delKes,
                            Jedis jedis){
        // 解析数据求平均分
        log.info("{}阅卷中修改批改方式,{}次阅卷变为1次阅卷时有{}个需要取平均值",examPaperId,readType,items.size());
        if(CollectionUtils.isNotEmpty(items)){
            String[] qns = questionNumber.split(",");
            Map<String, List<Map<String,String>>> qn2Items = new HashMap<>();
            for (String item : items) {
                // 新的数据该从 examItemHand 获取，examItemHand 是有序的
                String examItemHandKey = JedisUtil.getKey("examItemHand",examPaperId,item);
                List<String> ts = jedis.lrange(examItemHandKey,0,-1);
                String[] itemIds = item.split(",");
                for (int j = 0; j < itemIds.length; j++) {
                    String itemId = itemIds[j];
                    String qn = qns[j];
                    List<Map<String, String>> adminSubjectItems = qn2Items.computeIfAbsent(qn, (qn1)->new ArrayList<>());
                    List<Map<String,String>> lm = new ArrayList<>(2);
                    int i = 0;
                    for (String t : ts){
                        lm.add(jedis.hgetAll(JedisUtil.getKey("teacherReadRecord", examPaperId, t, itemId)));
                        i++;
                        if(i == 2) break;
                    }

                    float finallyScore = 0f;
                    float m2Score = Float.parseFloat(lm.get(0).get("readScore"));
                    float m1Score = Float.parseFloat(lm.get(1).get("readScore"));

                    // 如果有批分点呢？对批分点需要取平均值
                    int pointNumber = Integer.parseInt(jedis.hget(JedisUtil.getKey("paperReadTrace",examPaperId,qn),"pointNumber"));
                    if(pointNumber > 1){
                        finallyScore = computePointAvg(lm.get(0),lm.get(1),pointNumber);
                    }else {
                        finallyScore = (m2Score + m1Score) / 2;
                    }

                    Map<String,String> m = lm.get(0);
                    m.put("finallyScore", finallyScore + "");
                    m.put("readScore", finallyScore + "");
                    m.put("choiceStatus", "1");
                    m.put("userId", "1");
                    m.put("userName", "admin");
                    m.put("teacherId", "1");
                    m.put("teacherName", "admin");
                    String timeStr = DateUtil.getCurrentDateTime();
                    m.put("modifyTime", timeStr);
                    m.put("createTime", timeStr);

                    adminSubjectItems.add(m);
                }
            }

            // 提交 admin 平均分
            if(!qn2Items.isEmpty()){
                Pipeline pipeline = jedis.pipelined();
                qn2Items.forEach((qn, adminSubjectItems) -> {
                    for (Map<String,String> m : adminSubjectItems){
                        String itemId = m.get("examResultItemId");
                        String adminKey = JedisUtil.getKey("teacherReadRecord", examPaperId, "admin", itemId);
                        pipeline.hmset(adminKey, m);
                        synchroRecordService.addReadRecord(pipeline,adminKey);
                        if (qns.length == 1) {
                            pipeline.sadd(readRecordDoneKey, adminKey);
                            pipeline.sadd(examReadQnItemDone, itemId);
                        }
                    }
                });

                if (qns.length > 1) {
                    for (String item : items) {
                        String adminKey = JedisUtil.getKey("teacherReadRecord", examPaperId, "admin", item);
                        pipeline.sadd(readRecordDoneKey, adminKey);
                        pipeline.sadd(examReadQnItemDone, item);
                    }
                }
                pipeline.sync();
            }

            log.info("{}阅卷中修改批改方式,{}次阅卷变为1次阅卷时有计划{}个实际{}个取平均分处理完成",examPaperId,readType,items.size(),items.size());

            // 清理这些待批阅
            jedis.del(delKes.toArray(new String[]{}));
        }
    }



    /**
     * 阅卷中修改批改方式,M次阅卷变为N次阅卷
     */
    private void handleRedisQuestionReadMToN(Map<String,Object> params, int m, int n){
        final String examPaperId = params.get("examPaperId").toString();
        final String questionNumber = params.get("questionNumber").toString();
        final String readPaperThreshold = params.get("readPaperThreshold").toString();
        final String readType = n+"";

        String qnTrace = JedisUtil.getKey("examQnTrace",examPaperId,questionNumber);
        String examQnDoneKey = JedisUtil.getKey("examQnDone",examPaperId);

        JedisTemplate.execute(jedis -> {

            String readPaperType = jedis.hget(qnTrace,"readPaperType");
            if(ObjectUtil.isValueEquals(readType,readPaperType)){
                log.info("该题号{}的批改方式更新前后没有变化，readPaperType:{}.",questionNumber,readPaperType);
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"批改方式更新前后没有变化");
            }
            if(jedis.sismember(examQnDoneKey,questionNumber)){
                log.info("该题号{}已经批改完成，无需修改批改方式.",questionNumber);
                throw new CommonException(ResponseStatusEnum.DATA_BE_USED,"试题已经阅卷完成，不能修改");
            }

            // 2、更新 examQnDetail examQnTrace readPaperType
            params.put("readPaperThreshold",readPaperThreshold);

            Map<String,String> p1 = new HashMap<>();
            p1.put("readPaperType",readType);
            p1.put("readPaperThreshold",readPaperThreshold);
            p1.put("thresholdType", params.get("thresholdType").toString());
            jedis.hmset(qnTrace,p1);


            // 3、处理该题阅卷老师的记录，必须清理老师的待批改
            String examRead3QnItemTodoKey = JedisUtil.getKey("examRead3QnItemTodo",examPaperId,questionNumber);
            String examRead4QnItemTodoKey = JedisUtil.getKey("examRead4QnItemTodo",examPaperId,questionNumber);
            String chargeQnItemTodoKey = JedisUtil.getKey("chargeQnItemTodo",examPaperId,questionNumber);
            String chargeTeacherIds = jedis.hget(qnTrace,"chargeTeacherIds");
            String[] cids = chargeTeacherIds.split(",");
            String readTeacherIds = jedis.hget(qnTrace,"readTeacherIds");
            String[] tids = readTeacherIds.split(",");

            // 负责人总库和3库之间的数据转移以及待办

            // 2To3 || 2To4
            if(m == 2 && (n == 3 || n == 4)){
                Set<String> items = jedis.smembers(chargeQnItemTodoKey);
                jedis.del(chargeQnItemTodoKey);
                jedis.hdel(qnTrace,"chargeItemCount","chargeReadCount");

                // 清理负责人的题号和待批改
                for (int i = 0 ; i < cids.length; i++){
                    String principalQnItemTodoKey = JedisUtil.getKey("principalQnItemTodo",examPaperId,cids[i],questionNumber);
                    String principalQnTodoKey = JedisUtil.getKey("principalQnTodo",examPaperId,cids[i]);
                    String principalQnDoneKey = JedisUtil.getKey("principalQnDone",examPaperId,cids[i]);
                    if(jedis.sismember(principalQnTodoKey,questionNumber)){
                        items.addAll(jedis.smembers(principalQnItemTodoKey));
                        jedis.del(principalQnItemTodoKey);
                        jedis.smove(principalQnTodoKey,principalQnDoneKey,questionNumber);
                        if(jedis.scard(principalQnTodoKey) == 0){
                            params.put("teacherId",cids[i]);
                            examMarkTodoService.deletePrincipalTodoTask(params);
                        }
                    }
                }

                // 删除老师批阅进入待总裁
                for (String tid : tids) {
                    jedis.del(JedisUtil.getKey("teacherQnItemPrincipal", examPaperId, tid, questionNumber));
                }

                // 转移负责人待批改给三评总库
                log.info("{}阅卷中修改批改方式,{}次阅卷变为{}次阅卷，需要从负责人总库转移{}个到3库",examPaperId,m,n,items.size());
                if(CollectionUtils.isNotEmpty(items)){
                    jedis.sadd(examRead3QnItemTodoKey,items.toArray(new String[]{}));
                }
            }

            // 3To2
            else if(m == 3 && n == 2){
                long size = jedis.scard(examRead3QnItemTodoKey);
                log.info("{}阅卷中修改批改方式,{}次阅卷变为{}次阅卷，需要从3库转移{}个到负责人总库",examPaperId,m,n,size);

                // 只处理3库的，因为老师自己库里面的数据很难查找
                if(size > 0){
                    Pipeline pipeline = jedis.pipelined();
                    pipeline.sunionstore(chargeQnItemTodoKey,chargeQnItemTodoKey,examRead3QnItemTodoKey);
                    pipeline.del(examRead3QnItemTodoKey);
                    for (String cid : cids){
                        String principalQnKey = JedisUtil.getKey("principalQn", examPaperId, cid);
                        String principalQnTodoKey = JedisUtil.getKey("principalQnTodo", examPaperId, cid);
                        pipeline.sadd(principalQnKey, questionNumber);
                        pipeline.srem(JedisUtil.getKey("principalQnDone", examPaperId, cid), questionNumber);
                        pipeline.sadd(principalQnTodoKey, questionNumber);
                    }
                    pipeline.hset(qnTrace,"chargeItemCount",size+"");
                    pipeline.hset(qnTrace,"chargeReadCount","0");
                    pipeline.sync();

                    // 用来生成负责人待办
                    Map<String,Object> p = new HashMap<>();
                    p.put("examPaperId",examPaperId);
                    p.put("chargeTeacherIds",cids);
                    examMarkTodoService.insertPrincipalTodoTask(p);
                }
            }

            // 3To4
            else if(m == 3 && n == 4){
                log.info("{}阅卷中修改批改方式,{}次阅卷变为{}次阅卷，无需移动",examPaperId,m,n);
            }

            // 4To2
            else if(m == 4 && n == 2) {
                long size = jedis.scard(examRead3QnItemTodoKey) + jedis.scard(examRead4QnItemTodoKey);
                log.info("{}阅卷中修改批改方式,{}次阅卷变为{}次阅卷，需要从3|4库转移{}个到负责人总库", examPaperId, m, n, size);

                // 只处理3库的，因为老师自己库里面的数据很难查找
                if (size > 0) {
                    Pipeline pipeline = jedis.pipelined();
                    pipeline.sunionstore(chargeQnItemTodoKey, chargeQnItemTodoKey, examRead3QnItemTodoKey,examRead4QnItemTodoKey);
                    pipeline.del(examRead3QnItemTodoKey);
                    pipeline.del(examRead4QnItemTodoKey);
                    for (String cid : cids) {
                        String principalQnKey = JedisUtil.getKey("principalQn", examPaperId, cid);
                        String principalQnTodoKey = JedisUtil.getKey("principalQnTodo", examPaperId, cid);
                        pipeline.sadd(principalQnKey, questionNumber);
                        pipeline.srem(JedisUtil.getKey("principalQnDone", examPaperId, cid), questionNumber);
                        pipeline.sadd(principalQnTodoKey, questionNumber);
                    }
                    pipeline.hset(qnTrace, "chargeItemCount", size + "");
                    pipeline.hset(qnTrace, "chargeReadCount", "0");
                    pipeline.sync();

                    // 用来生成负责人待办
                    Map<String, Object> p = new HashMap<>();
                    p.put("examPaperId", examPaperId);
                    p.put("chargeTeacherIds", cids);
                    examMarkTodoService.insertPrincipalTodoTask(p);
                }

            }

            // 4To3
            else if(m == 4 && n == 3) {
                Set<String> items = jedis.smembers(chargeQnItemTodoKey);
                jedis.del(chargeQnItemTodoKey);
                jedis.hdel(qnTrace,"chargeItemCount","chargeReadCount");

                // 清理负责人的题号和待批改
                for (int i = 0 ; i < cids.length; i++){
                    String principalQnItemTodoKey = JedisUtil.getKey("principalQnItemTodo",examPaperId,cids[i],questionNumber);
                    String principalQnTodoKey = JedisUtil.getKey("principalQnTodo",examPaperId,cids[i]);
                    String principalQnDoneKey = JedisUtil.getKey("principalQnDone",examPaperId,cids[i]);
                    if(jedis.sismember(principalQnTodoKey,questionNumber)){
                        items.addAll(jedis.smembers(principalQnItemTodoKey));
                        jedis.del(principalQnItemTodoKey);
                        jedis.smove(principalQnTodoKey,principalQnDoneKey,questionNumber);
                        if(jedis.scard(principalQnTodoKey) == 0){
                            params.put("teacherId",cids[i]);
                            examMarkTodoService.deletePrincipalTodoTask(params);
                        }
                    }
                }


                // 删除老师批阅进入待总裁
                for (String tid : tids) {
                    jedis.del(JedisUtil.getKey("teacherQnItemPrincipal", examPaperId, tid, questionNumber));
                }

                // 4库和负责人的数据，都都使用前三个人的平均值
                items.addAll(jedis.smembers(examRead4QnItemTodoKey));

                // 解析数据求平均分
                log.info("{}阅卷中修改批改方式,{}次阅卷变为{}次阅卷时有{}个需要取平均值",examPaperId,m,n,items.size());
                if(CollectionUtils.isNotEmpty(items)){
                    List<Map<String,String>> adminSubjectItems = new ArrayList<>();
                    for (String itemId : items){

                        // 新的数据该从 examItemHand 获取，examItemHand 是有序的
                        String examItemHandKey = JedisUtil.getKey("examItemHand",examPaperId,itemId);
                        List<String> ts = jedis.lrange(examItemHandKey,0,-1);

                        List<Map<String,String>> lm = new ArrayList<>(2);
                        int i = 0;
                        for (String t : ts){
                            lm.add(jedis.hgetAll(JedisUtil.getKey("teacherReadRecord", examPaperId, t, itemId)));
                            i++;
                            if(i == 3) break;
                        }

                        float m1Score = Float.parseFloat(lm.get(1).get("readScore"));
                        float m2Score = Float.parseFloat(lm.get(0).get("readScore"));
                        float m3Score = Float.parseFloat(lm.get(2).get("readScore"));
                        float finallyScore = (m2Score + m1Score + m3Score) / 3;
                        Map<String,String> mp = lm.get(0);
                        mp.put("finallyScore", finallyScore + "");
                        mp.put("readScore", finallyScore + "");
                        mp.put("choiceStatus", "1");
                        mp.put("userId", "1");
                        mp.put("userName", "admin");
                        mp.put("teacherId", "1");
                        mp.put("teacherName", "admin");
                        String timeStr = DateUtil.getCurrentDateTime();
                        mp.put("modifyTime", timeStr);
                        mp.put("createTime", timeStr);
                        adminSubjectItems.add(mp);
                    }

                    // 提交 admin 平均分
                    if(CollectionUtils.isNotEmpty(adminSubjectItems)){
                        Pipeline pipeline = jedis.pipelined();
                        for (Map<String,String> mp : adminSubjectItems){
                            String itemId = mp.get("examResultItemId");
                            String adminKey = JedisUtil.getKey("teacherReadRecord", examPaperId, "admin", itemId);
                            pipeline.hmset(adminKey, mp);
                            synchroRecordService.addReadRecord(pipeline,adminKey);
                            pipeline.sadd(JedisUtil.getKey("readRecordDone", examPaperId), adminKey);
                            pipeline.sadd(JedisUtil.getKey("examReadQnItemDone", examPaperId, questionNumber), itemId);
                        }
                        pipeline.sync();
                    }

                    log.info("{}阅卷中修改批改方式,{}次阅卷变为{}次阅卷时有计划{}个实际{}个取平均分处理完成",examPaperId,m,n,items.size(),adminSubjectItems.size());

                }
            }

            else {
                log.info("{}阅卷中修改批改方式,{}次阅卷变为{}次阅卷系统并不支持",examPaperId,m,n);
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"系统暂不支持");
            }

            return true;
        });
    }


}
