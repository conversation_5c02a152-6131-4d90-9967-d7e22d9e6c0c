package com.dongni.exam.mark.service.handle;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.mark.manager.ICardReportItemManager;
import com.dongni.exam.mark.service.ExamMarkMonitorService;
import com.dongni.exam.mark.service.ExamMarkPreparationService;
import com.dongni.exam.mark.service.ExamMarkReadBlockService;
import com.dongni.exam.mark.service.ExamMarkTodoService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Pipeline;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;


/**
 * Created by Heweipo on 2019/7/23.
 *
 * 阅卷初始化调整（异常卷处理导致的阅卷重新初始化）
 */
@Service
public class ExamMarkPreparationHandle {

    private static final Logger log = LogManager.getLogger(ExamMarkPreparationHandle.class);

    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private ExamMarkPreparationService examMarkPreparationService;
    @Autowired
    private ExamMarkReadBlockService examMarkReadBlockService;
    @Autowired
    private ExamMarkMonitorService examMarkMonitorService;
    @Autowired
    private ExamMarkTodoService examMarkTodoService;
    @Autowired
    private ExamMarkCleanHandle examMarkCleanHandle;
    @Autowired
    private ExamMarkHandle examMarkHandle;

    @Autowired
    private ICardReportItemManager cardReportItemManager;

    /**
     * 处理异常答题卡切分
     * @param params  examId paperId questionNumbers
     */
    public void handleExceptionCardSplit(Map<String,Object> params){

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotBlank("questionNumbers")
                .verify();

        // 参数初始化
        List<String> questionNumbers = (List) params.get("questionNumbers");
        if(CollectionUtils.isEmpty(questionNumbers)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"未指定试题");
        }

        examMarkMonitorService.asynSaveMonitor(params,"异常卷拆分开始");

        // 分组试题（被拆分和拆分后的试题）
        List<String> qns = commonRepository.selectList("ExamMarkPreparationHandleMapper.getPaperReadQuestionNumber",params);
        if(CollectionUtils.isEmpty(qns)){
            examMarkMonitorService.asynSaveMonitor(params,"异常卷拆分完成，因为还未阅卷安排所以无需初始化阅卷");
            return;
        }

        Set<String> all = new HashSet<>(qns);
        List<String> del = new ArrayList<>();
        List<String> ins = new ArrayList<>();
        for (String qn : questionNumbers){
            if(!all.contains(qn)){
                del.add(qn);
            }else {
                ins.add(qn);
            }
        }

        if(del.isEmpty() || ins.isEmpty()){
            examMarkMonitorService.asynSaveMonitor(params,"异常卷拆分完成，没有需要阅卷初始化的试题");
            return;
        }

        params.put("ins",ins);
        params.put("del",del);

        // 获取公共参数(examPaperId isReadByClass)
        Map<String,Object> exam = commonRepository.selectOne("ExamMarkPreparationHandleMapper.getExamPaper",params);
        params.putAll(exam);

        boolean isReadByClass = ObjectUtil.isValueEquals(DictUtil.getDictValue("correctMode", "readByClass"), exam.get("correctMode"));
        params.put("isReadByClass",isReadByClass);

        int readArrange = DictUtil.getDictValue("examPaperStatus","readArrange");
        if(isReadByClass){
            params.put("examPaperStatus",readArrange);
            List<Long> classIds = commonRepository.selectList("ExamMarkPreparationHandleMapper.getExamClassIds",params);
            if(CollectionUtils.isEmpty(classIds)){
                examMarkMonitorService.asynSaveMonitor(params,"异常卷拆分完成，没有需要阅卷初始化的班级");
                return;
            }
            params.put("classIds",classIds);
        }else {
            if(Integer.parseInt(exam.get("examPaperStatus").toString()) <= readArrange){
                examMarkMonitorService.asynSaveMonitor(params,"异常卷拆分完成，因为还未阅卷安排所以无需初始化阅卷");
                return;
            }
        }

        // 拆分试题同时清理及初始化阅卷
        splitAndStoreExceptionCardItem(params);

        // 日志记录
        examMarkMonitorService.asynSaveMonitor(params,"异常卷拆分完成");
    }

    private void splitAndStoreExceptionCardItem(Map<String,Object> params){

        long examPaperId = (long) params.get("examPaperId");
        List<String> ins = (List) params.get("ins");
        List<String> del = (List) params.get("del");
        boolean isReadByClass = (boolean) params.get("isReadByClass");

        // 按班级阅卷
        if(isReadByClass){
            List<Long> classIds = (List) params.get("classIds");
            for (Long classId : classIds){
                splitAndStoreExceptionCardItem(examPaperId+":"+classId,  del, ins, params);
            }
        }
        // 按试题阅卷
        else {
            splitAndStoreExceptionCardItem(examPaperId+"", del, ins, params);
        }
    }

    /**
     * 分割试题同时清理及初始化阅卷系统
     */
    private void splitAndStoreExceptionCardItem(String cacheId, List<String> del, List<String> ins, Map<String, Object> params) {

        // 是否正在阅卷
        boolean isReading = initIfNotInReading(cacheId,params);

        // 如果都没有阅卷数据，那么执行阅卷初始化方法即可
        if(!isReading){
            return;
        }

        Map<String,Object> p = new HashMap<>();
        p.put("examPaperId",cacheId);
        List<Map<String,Object>> blocks = examMarkReadBlockService.getReadBlockList(p);

        Map<String,Map<String,Object>> qnBlock = blocks.stream().collect(toMap(b->b.get("questionNumber").toString(),b->b));
//        for (String qn : del){
//            if(!qnBlock.containsKey(qn)){
//                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"题块已被调整或是组合题块");
//            }
//        }

        // 清理删除（被拆分）的试题
        JedisTemplate.execute(jedis -> {
            Set<String> tids = new HashSet<>();
            Set<String> chargeTids = new HashSet<>();
            for (String qn : del){
                String examQnTraceKey = JedisUtil.getKey("examQnTrace",cacheId,qn);
                if (!jedis.exists(examQnTraceKey)) {
                    continue;
                }
                List<String> tid = jedis.hmget(examQnTraceKey, "readTeacherIds", "chargeTeacherIds");
                if(StringUtils.isNotBlank(tid.get(0))){
                    tids.addAll(Arrays.asList(tid.get(0).split(",")));
                }
                if(StringUtils.isNotBlank(tid.get(1))){
                    chargeTids.addAll(Arrays.asList(tid.get(1).split(",")));
                }
            }

            // 只是把入口清理掉，真正的数据没有清理
            String[] delArray = del.toArray(new String[]{});
            Pipeline pipeline = jedis.pipelined();
            pipeline.srem(JedisUtil.getKey("examQn",cacheId),delArray);
            pipeline.srem(JedisUtil.getKey("examQnTodo",cacheId),delArray);
            pipeline.srem(JedisUtil.getKey("examQnDone",cacheId),delArray);
            for (String tid : tids){
                pipeline.srem(JedisUtil.getKey("teacherQn",cacheId,tid),delArray);
                pipeline.srem(JedisUtil.getKey("teacherQnTodo",cacheId,tid),delArray);
                pipeline.srem(JedisUtil.getKey("teacherQnDone",cacheId,tid),delArray);
            }
            for (String tid : chargeTids) {
                pipeline.srem(JedisUtil.getKey("principalQn", cacheId,tid), delArray);
                pipeline.srem(JedisUtil.getKey("principalQnTodo", cacheId,tid), delArray);
                pipeline.srem(JedisUtil.getKey("principalQnDone", cacheId,tid), delArray);
            }
            pipeline.sync();

            Set<String> keys = new HashSet<>();
            for (String qn : delArray) {
                examMarkCleanHandle.getQnKeys(jedis, cacheId, keys, qn, false);
            }
            if (!CollectionUtils.isEmpty(keys)) {
                List<String> ls = new ArrayList<>(keys);
                int size = 50; // 100 个需要 20 毫秒，会进入慢查询
                int length = ls.size();
                int init = 0;

                // 一次提交所有，但是每次执行的命令不要太久
                pipeline = jedis.pipelined();
                while(length > 0){
                    pipeline.del(ls.subList(init, (init+size)>ls.size()?ls.size():(init+size)).toArray(new String[]{}));
                    init += size;
                    length -= size;
                }

                pipeline.sync();
            }
            return null;
        });

        // 初始化试题以及生成待办
        Map<String,Object> query = new HashMap<>(params);
        query.put("qns",ins);
        if(cacheId.contains(":")){
            query.put("classId",cacheId.split(":")[1]);
        }
        examMarkPreparationService.executeExamMarkQuestionPreparation(query);
    }


    /**
     * 处理异常卷的阅卷缓存
     * @param params examId paperId studentIds questionNumbers
     */
    public void handleExceptionCardCache(Map<String,Object> params){

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotBlank("studentIds")
                .isNotBlank("questionNumbers")
                .verify();

        // 参数初始化
        long examId = Long.parseLong(params.get("examId").toString());
        long paperId = Long.parseLong(params.get("paperId").toString());
        Collection studentIds = (Collection) params.get("studentIds");
        if(CollectionUtils.isEmpty(studentIds)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"未指定学生");
        }

        List<String> questionNumbers = (List) params.get("questionNumbers");
        if(CollectionUtils.isEmpty(questionNumbers)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"未指定试题");
        }

        examMarkMonitorService.asynSaveMonitor(params,"异常卷处理开始");
        log.info("异常卷重新初始化考试{}试卷{}的阅卷数据{}",examId,paperId,params);

        // 获取公共参数(examPaperId isReadByClass)
        Map<String,Object> exam = commonRepository.selectOne("ExamMarkPreparationHandleMapper.getExamPaper",params);
        params.putAll(exam);

        boolean isReadByClass = ObjectUtil.isValueEquals(DictUtil.getDictValue("correctMode", "readByClass"), exam.get("correctMode"));
        params.put("isReadByClass",isReadByClass);

        if(isReadByClass){
            List<Long> classIds = commonRepository.selectList("ExamMarkPreparationHandleMapper.getExamClassIdsByStudent",params);
            params.put("classIds",classIds);
        }

        // 首先清理掉错误的阅卷数据, 然后重新初始化新的阅卷数据
        cleanAndStoreExceptionCardItem(params);

        // 最后生成待办
        insertExamMarkTask(params);

        // 日志记录
        examMarkMonitorService.asynSaveMonitor(params,"异常卷处理完成");
    }


    /**
     * 清理异常卷遗留的垃圾数据
     * @param params cacheId examPaperId isReadByClass classIds studentIds questionNumbers
     */
    private void cleanAndStoreExceptionCardItem(Map<String,Object> params){

        long examPaperId = (long) params.get("examPaperId");
        long examId = (long) params.get("examId");
        long paperId = (long) params.get("paperId");
        List<String> questionNumbers = (List) params.get("questionNumbers");
        boolean isReadByClass = (boolean) params.get("isReadByClass");

        // 按班级阅卷
        if(isReadByClass){
            List<Long> classIds = (List) params.get("classIds");
            for (Long classId : classIds){
                cleanAndStoreExamMarkItem(examPaperId+":"+classId, examId, paperId, questionNumbers,params);
            }
        }
        // 按试题阅卷
        else {
            cleanAndStoreExamMarkItem(examPaperId+"", examId, paperId, questionNumbers,params);
        }
    }

    private boolean initIfNotInReading(String cacheId, Map<String,Object> params){
        boolean isReading = isReading(cacheId, params);

        if(!isReading){
            Map<String,Object> p = new HashMap<>();
            p.put("userId",params.get("userId"));
            p.put("userName",params.get("userName"));
            if(cacheId.contains(":")){
                p.put("examPaperId",cacheId.split(":")[0]);
                p.put("classId",cacheId.split(":")[1]);
            }else {
                p.put("examPaperId",cacheId);
            }
            p.put("schoolId", params.get("schoolId"));
            examMarkPreparationService.executeExamMarkPreparation(p);
        }

        return isReading;
    }

    private boolean isReading(String cacheId, Map<String, Object> params) {
        // 是否正在阅卷
        return JedisTemplate.execute(jedis -> {

            boolean isExist = jedis.exists(JedisUtil.getKey("examPaperTrace",cacheId));
            if(isExist){
                // 如果需要保留阅卷记录，则不对阅卷记录进行清理
                String readRecordsFlag = "retain";
                if(params.get("readRecordsOperate")!=null){
                    readRecordsFlag = params.get("readRecordsOperate").toString();
                }
                if("retain".equals(readRecordsFlag.trim())) {
                    return true;
                }
                boolean isNeedClean = jedis.sismember(JedisUtil.getKey("examPaperDone"),cacheId);
                if(isNeedClean){
                    // 数据清理
                    examMarkMonitorService.asynSaveMonitor(params,"异常卷处理前先把阅卷数据清理，方便阅卷重新初始化");

                    Map<String,Object> p = new HashMap<>();
                    p.put("userId",params.get("userId"));
                    p.put("userName",params.get("userName"));
                    p.put("examPaperId",cacheId);
                    examMarkCleanHandle.cleanExamPaper(p);
                }else {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * 清理之前的数据同时存储新的数据
     * @param cacheId 缓存ID
     * @param examId 考试ID
     * @param paperId 试卷ID
     * @param questionNumbers 题号列表
     */
    private void cleanAndStoreExamMarkItem(String cacheId, long examId, long paperId, List<String> questionNumbers, Map<String,Object> params) {

        // 是否正在阅卷
        boolean isReading = isReading(cacheId,params);

        // 如果都没有阅卷数据，那么执行阅卷初始化方法即可
        if (!isReading) {
            Map<String, Object> p = new HashMap<>(params);
            if (cacheId.contains(":")) {
                p.put("classId", cacheId.split(":")[1]);
            }
            examMarkPreparationService.executeExamMarkPreparation(p);
            return;
        }
        // 如果有阅卷数据，那么要删除再加入，先获取要处理的题号. fixed 但是有可能这道题并没有初始化，也就是没有阅卷数据
        Map<String,Object> p = new HashMap<>();
        p.put("examPaperId",cacheId);
        Set<String> existQns = getExistQns(questionNumbers, p);
        if(existQns.size() < questionNumbers.size()){
            List<String> unInitQns = questionNumbers.stream().filter(q->!existQns.contains(q)).collect(Collectors.toList());

            Map<String,Object> np = new HashMap<>(params);
            if(cacheId.contains(":")){
                np.put("classId",cacheId.split(":")[1]);
            }
            np.put("qns",unInitQns);
            examMarkPreparationService.executeExamMarkPreparation(np);

            // 都未初始化，直接退出
            if(existQns.size() == 0) return;
        }

        String readRecordsFlag = "";
        if(params.get("readRecordsOperate")!=null){
            readRecordsFlag = params.get("readRecordsOperate").toString();
        }

        // 查询参数
        p.put("examId",examId);
        p.put("paperId",paperId);
        p.put("studentIds",params.get("studentIds"));
        if(cacheId.contains(":")){
            p.put("classId",cacheId.split(":")[1]);
        }

        // 只替换答题明细并删除有异常卷提交记录阅卷记录
        if("retain".equals(readRecordsFlag.trim())) {
            Map<String,Object> np = new HashMap<>(params);
            if(cacheId.contains(":")){
                np.put("classId",cacheId.split(":")[1]);
            }
            np.put("qns",existQns);
            Map<String, Object> map = new HashMap<>();
            map.put("examId", examId);
            map.put("paperId", paperId);
            long recognitionId = MapUtil.getLong(params, "recognitionId");
            for (String existQn : existQns) {
                p.put("questionNumber",existQn.split(","));
                Long classId = null;
                if(cacheId.contains(":")){
                    classId = Long.parseLong(cacheId.split(":")[1]);
                    p.put("classId",cacheId.split(":")[1]);
                }
                p.put("recognitionId", params.get("recognitionId"));
                List<Integer> qns = Arrays.stream(existQn.split(",")).map(Integer::parseInt).collect(Collectors.toList());
                List<String> items = cardReportItemManager.getCleanExamCardReportItem(recognitionId, qns, classId);
                if (CollectionUtils.isEmpty(items)) {
                    continue;
                }
                cleanQnHis(cacheId, params, p, existQn, items);
            }
            examMarkPreparationService.preparationItemDetail(np, cacheId, map);
            return;
        }

        // 对每一题块进行清理
        for (String qn : existQns){

            p.put("questionNumber",qn.split(","));
            List<String> items = commonRepository.selectList("ExamMarkPreparationHandleMapper.getCleanExceptionCardItem",p);

            // 如果是选做题，那么可能没有数据，但是之前需要被清理的数据已经删除了！！！
            if(CollectionUtils.isEmpty(items)){
                log.info("异常卷处理时清理异常答题卡的明细时没有从MySQL查询到考试{}试卷{}题号{}的数据",examId,paperId,qn);
                continue;
            }

            cleanQnHis(cacheId, params, p, qn, items);
        }

        // 阅卷进度修补
        examMarkHandle.fixExamMarkProgress(cacheId);
    }

    private void cleanQnHis(String cacheId, Map<String, Object> params, Map<String, Object> p, String qn, List<String> items) {
        // 把需要的数据加入
        String[] all = items.toArray(new String[]{});
        String examQnTraceKey = JedisUtil.getKey("examQnTrace", cacheId, qn);
        String examRead1QnItemTodoKey = JedisUtil.getKey("examRead1QnItemTodo", cacheId, qn);
        String examReadQnItemKey = JedisUtil.getKey("examReadQnItem", cacheId, qn);
        String examRead2QnItemTodoKey = JedisUtil.getKey("examRead2QnItemTodo", cacheId, qn);
        String examRead3QnItemTodoKey = JedisUtil.getKey("examRead3QnItemTodo", cacheId, qn);
        String examRead4QnItemTodoKey = JedisUtil.getKey("examRead4QnItemTodo", cacheId, qn);
        String examReadMQnItemTodoKey = JedisUtil.getKey("examReadMQnItemTodo", cacheId, qn);
        String examReadQnItemDoneKey = JedisUtil.getKey("examReadQnItemDone", cacheId, qn);
        String readRecordDoneKey = JedisUtil.getKey("readRecordDone", cacheId);

        // 彻底清理
        JedisTemplate.execute(jedis -> {

            int readPaperType = Integer.parseInt(jedis.hget(examQnTraceKey, "readPaperType"));
            List<String> s = jedis.hmget(examQnTraceKey, "readTeacherIds","chargeTeacherIds");
            String[] tids = s.get(0).split(",");
            String[] cids = s.get(1).split(",");

            // 清理总库
            Pipeline pipeline = jedis.pipelined();
            pipeline.srem(examRead1QnItemTodoKey,all);
            if(readPaperType > 1){
                pipeline.srem(examRead2QnItemTodoKey,all);
                if(readPaperType > 2){
                    pipeline.srem(examRead3QnItemTodoKey,all);
                    if(readPaperType > 3){
                        pipeline.srem(examRead4QnItemTodoKey,all);
                    }
                }
            }
            pipeline.srem(examReadQnItemDoneKey,all);

            List<String> detail = new ArrayList<>();
            items.forEach(i->{
                if(i.contains(",")){
                    for (String t : i.split(",")){
                        detail.add(JedisUtil.getKey("examItem", cacheId,t));
                    }
                }else {
                    detail.add(JedisUtil.getKey("examItem", cacheId,i));
                }
            });
            pipeline.del(detail.toArray(new String[]{}));

            pipeline.sync();

            // 多评库重新计算
            if(readPaperType > 1){
                jedis.srem(examReadMQnItemTodoKey, items.toArray(new String[]{}));
                for (String i : items){
                    String chargeQnItemTodoKey = JedisUtil.getKey("chargeQnItemTodo", cacheId, qn);
                    String adminQnItemDoneKey = JedisUtil.getKey("principalQnItemDone", cacheId,"admin", qn);

                    if(jedis.sismember(chargeQnItemTodoKey,i)){
                        jedis.srem(chargeQnItemTodoKey,i);
                    }else if(jedis.sismember(adminQnItemDoneKey,i)){
                        jedis.srem(adminQnItemDoneKey,i);
                        jedis.del(JedisUtil.getKey("principalReadRecord", cacheId,"admin",i));
                        jedis.srem(readRecordDoneKey,JedisUtil.getKey("principalReadRecord", cacheId,"admin",i));
                        jedis.del(JedisUtil.getKey("chargeItemSubmit", cacheId,i));
                        jedis.del(JedisUtil.getKey("chargeItemGrab", cacheId,i));
                    }

                    // 对每个负责人进行处理
                    for (String cid : cids){
                        String principalQnItemTodoKey = JedisUtil.getKey("principalQnItemTodo", cacheId,cid, qn);
                        String principalQnItemDoneKey = JedisUtil.getKey("principalQnItemDone", cacheId,cid, qn);

                        if(jedis.sismember(principalQnItemTodoKey,i)){
                            jedis.srem(principalQnItemTodoKey,i);
                        }else if(jedis.sismember(principalQnItemDoneKey,i)){
                            jedis.srem(principalQnItemDoneKey,i);
                            jedis.del(JedisUtil.getKey("principalReadRecord", cacheId,cid,i));
                            jedis.srem(readRecordDoneKey,JedisUtil.getKey("principalReadRecord", cacheId,cid,i));
                            jedis.lrem(JedisUtil.getKey("principalQnReadList", cacheId,cid, qn),1,i);
                            jedis.del(JedisUtil.getKey("chargeItemSubmit", cacheId,i));
                            jedis.del(JedisUtil.getKey("chargeItemGrab", cacheId,i));
                        }
                    }
                }
            }

            // 清理老师库
            for (String tid : tids){
                String teacherQnItemTodoKey = JedisUtil.getKey("teacherQnItemTodo", cacheId,tid, qn);
                Set<String> set = jedis.smembers(teacherQnItemTodoKey);
                if(CollectionUtils.isNotEmpty(set)){
                    for (String i : set){
                        if(items.contains(i)) {
                            jedis.srem(teacherQnItemTodoKey,i);
                        }
                    }
                }

                String teacherQnItemDoneKey = JedisUtil.getKey("teacherQnItemDone", cacheId,tid, qn);
                set = jedis.smembers(teacherQnItemDoneKey);
                if(CollectionUtils.isNotEmpty(set)){
                    for (String i : set){
                        if(items.contains(i)) {
                            jedis.srem(teacherQnItemDoneKey,i);
                            jedis.del(JedisUtil.getKey("teacherReadRecord", cacheId,tid,i));
                            jedis.srem(readRecordDoneKey,JedisUtil.getKey("teacherReadRecord", cacheId,tid,i));
                            jedis.lrem(JedisUtil.getKey("teacherQnReadList", cacheId,tid, qn),1,i);
                            if(qn.contains(",")){
                                String[] qns = qn.split(",");
                                String[] is = i.split(",");
                                for (int j = 0; j < qns.length; j++){
                                    String r = is[j];
                                    String record = JedisUtil.getKey("teacherReadRecord", cacheId,tid,r);
                                    jedis.del(record);
                                    jedis.srem(readRecordDoneKey,record);
                                    jedis.lrem(JedisUtil.getKey("teacherQnReadList", cacheId,tid,qns[j]),1,r);
                                }
                            }
                            jedis.del(JedisUtil.getKey("examItemHand", cacheId,i));
                            jedis.del(JedisUtil.getKey("examItemGrab", cacheId,i));
                        }
                    }
                    jedis.del(JedisUtil.getKey("teacherQnScoreCollect", cacheId,tid, qn));
                }
            }
            return all;
        });


        // 重新存储
        JedisTemplate.execute(jedis -> {

            String s = jedis.hget(examQnTraceKey, "readTeacherIds");
            String[] tids = s.split(",");

            // 计算老师平均分
            Map<String,Map<String,String>> tidQnScore = new HashMap<>();
            for (String tid : tids){
                float sum = 0f;
                float max = 0f;
                Map<String, Object> rs = new HashMap<>();
                rs.put("totalCount", 0);
                rs.put("max", 0);
                rs.put("sum", 0);

                String teacherQnReadListKey = JedisUtil.getKey("teacherQnReadList", cacheId, tid, qn);
                String teacherQnScoreKey = JedisUtil.getKey("teacherQnScore", cacheId, tid, qn);

                List<String> ls = jedis.lrange(teacherQnReadListKey, 0, -1);
                if (CollectionUtils.isEmpty(ls)) {
                    jedis.del(teacherQnScoreKey);
                    continue;
                }

                // 遍历记录一个个获取
                Pipeline pipeline = jedis.pipelined();
                ls.forEach(itemId -> pipeline.hget(JedisUtil.getKey("teacherReadRecord", cacheId, tid, itemId), "readScore"));
                List<Object> l2 = pipeline.syncAndReturnAll();
                for (Object obj : l2) {
                    float l = Float.valueOf(obj.toString());
                    sum += l;
                    if (l > max) {
                        max = l;
                    }
                }

                rs.put("totalCount", l2.size());
                rs.put("sum", sum);
                rs.put("max", max);
                tidQnScore.put(teacherQnScoreKey,ObjectUtil.transferMapStr(rs));
            }

            Pipeline pipeline = jedis.pipelined();

            // 获取明细数据
            List<Map<String, String>> itemTodo = commonRepository.selectList("ExamMarkPreparationHandleMapper.getItemDetailTodo", p);
            itemTodo.forEach(map -> {
                pipeline.hmset("EP:ERI:" + cacheId + ":item:" + map.get("examResultItemId"), ObjectUtil.removeMapNull(map));
            });

            // 判断是否加载数据不一致
            if(qn.split(",").length * items.size() != itemTodo.size()){
                Map<String,Object> pp = new HashMap<>(params);
                pp.put("questionNumber", qn);
                examMarkMonitorService.asynSaveMonitor(pp,"获取itemId的数量:"+ qn.split(",").length * items.size()+"与item明细的数量"+itemTodo.size()+"不一致");
            }

            // 加入到1库
            pipeline.sadd(examReadQnItemKey, items.toArray(new String[]{}));
            pipeline.sadd(examRead1QnItemTodoKey, items.toArray(new String[]{}));

            // 未完成题号
            pipeline.smove(JedisUtil.getKey("examQnDone", cacheId),JedisUtil.getKey("examQnTodo", cacheId), qn);
            for (String tid : tids){
                pipeline.smove(JedisUtil.getKey("teacherQnDone", cacheId,tid),JedisUtil.getKey("teacherQnTodo", cacheId,tid), qn);

                // 老师平均分
                String teacherQnScoreKey = JedisUtil.getKey("teacherQnScore", cacheId, tid, qn);
                if(tidQnScore.containsKey(teacherQnScoreKey)){
                    pipeline.hmset(teacherQnScoreKey,tidQnScore.get(teacherQnScoreKey));
                }
            }
            pipeline.sync();

            return all;
        });
    }

    private Set<String> getExistQns(List<String> questionNumbers, Map<String, Object> p) {
        List<Map<String,Object>> blocks = examMarkReadBlockService.getReadBlockList(p);
        Map<String,Map<String,Object>> qnBlock = blocks.stream().collect(toMap(b->b.get("questionNumber").toString(),b->b));
        String cacheId = p.get("examPaperId").toString();

        return JedisTemplate.execute(jedis -> {
            Set<String> existQns = new HashSet<>();
            for (String qn : questionNumbers){
                if(qnBlock.containsKey(qn) && jedis.exists(JedisUtil.getKey("examQnTrace",cacheId,qn))){
                    existQns.add(qn);
                }else {
                    for (String q : qnBlock.keySet()){
                        if(q.contains(",") && new HashSet<>(Arrays.asList(q.split(","))).contains(qn)
                                && jedis.exists(JedisUtil.getKey("examQnTrace",cacheId,q))){
                            existQns.add(q);
                        }
                    }
                }
            }
            return existQns;
        });

    }

    /**
     * 生成阅卷待办
     * @param params examPaperId isReadByClass classIds studentIds questionNumbers
     */
    private void insertExamMarkTask(Map<String,Object> params) {
        long examPaperId = (long) params.get("examPaperId");
        List<String> questionNumbers = (List) params.get("questionNumbers");
        boolean isReadByClass = (boolean) params.get("isReadByClass");

        // 按班级阅卷
        if(isReadByClass){
            List<Long> classIds = (List) params.get("classIds");
            for (Long classId : classIds){
                String tids = getExamMarkTeacher(examPaperId+":"+classId,questionNumbers);
                params.put("examPaperId",examPaperId+":"+classId);
                params.put("tids",tids);
                if(ObjectUtil.isBlank(tids)) continue;
                examMarkTodoService.insertTeacherTodoTask(params);
            }
        }
        // 按试题阅卷
        else {
            String tids = getExamMarkTeacher(examPaperId+"",questionNumbers);
            params.put("tids",tids);
            if(StringUtils.isNotEmpty(tids)) {
                examMarkTodoService.insertTeacherTodoTask(params);
            }
        }
    }


    /**
     * 获取试题相关的阅卷老师
     * @param cacheId 缓存ID
     * @param questionNumbers 试题
     * @return 老师ID
     */
    private String getExamMarkTeacher(String cacheId, List<String> questionNumbers) {
        // 获取要处理的题号
        Map<String, Object> p = new HashMap<>();
        p.put("examPaperId", cacheId);
        Set<String> existQns = getExistQns(questionNumbers, p);

        Set<String> tids = new HashSet<>();

        // 对每一题块获取老师
        JedisTemplate.execute(jedis -> {
            for (String qn : existQns) {
                String s = jedis.hget(JedisUtil.getKey("examQnTrace", cacheId, qn), "readTeacherIds");
                tids.addAll(Arrays.asList(s.split(",")));
            }
            return tids;
        });

        return StringUtils.join(tids,",");
    }

}
