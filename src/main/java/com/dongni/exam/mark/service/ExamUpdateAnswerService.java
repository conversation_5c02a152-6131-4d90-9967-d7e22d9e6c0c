package com.dongni.exam.mark.service;

import com.dongni.analysis.stat.service.ExamStatQueueService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.enums.UnitTypeEnum;
import com.dongni.exam.homework.service.StudentHomeworkService;
import com.dongni.exam.homework.service.handle.HomeworkQuestionHandle;
import com.dongni.exam.mark.enums.QuestionDisputeByOptionsEnum;
import com.dongni.exam.plan.service.ExamPaperService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.exam.question.service.ExamQuestionStructureService;
import com.dongni.tiku.common.enumeration.PaperAnswerStatus;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.entrust.service.dongni.EntrustService;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.own.service.OwnPaperService;
import com.dongni.tiku.own.service.OwnUpdateAnswerService;
import com.dongni.tiku.own.service.PaperObjQuestionAdditionalAnswerService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Create by sapluk <br/>
 * time 14:34 2018/12/25 <br/>
 * description: <br/>
 *  修正答案  仅对客观题进行修正答案操作
 *  1. 获取题目信息
 *  2. 修正答案
 *     2.1 更新mongoPaper
 *     2.2 更新学生答题及分数  t_exam_item
 *  3. 记录附加答案信息  t_question_additional
 *  4. 记录更改记录      t_question_dispute
 */
@Service
public class ExamUpdateAnswerService {

    private static final Logger log = LogManager.getLogger(ExamUpdateAnswerService.class);

    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private OwnUpdateAnswerService ownUpdateAnswerService;
    @Autowired
    private ExamQuestionStructureService questionStructureService;
    @Autowired
    private HomeworkQuestionHandle homeworkQuestionHandle;
    @Autowired
    private PaperManager paperManager;
    @Autowired
    private PaperObjQuestionAdditionalAnswerService paperObjQuestionAdditionalAnswerService;
    @Autowired
    private ExamService examService;
    @Autowired
    private StudentHomeworkService studentHomeworkService;
    @Autowired
    private ExamUpdateAnswerService examUpdateAnswerService;
    @Autowired
    private ExamStatQueueService examStatQueueService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private EntrustService entrustService;
    @Autowired
    private ExamQuestionStructureService examQuestionStructureService;

    private static final String updateAllRightKey = "updateAllRightParams";
    private static final String updateByOptionsAnswerValueKey = "updateByOptionsAnswerValueParams";
    private static final String updateByOptionsAnswerCountKey = "updateByOptionsAnswerCountParams";
    private static final String updateByAnswerKey = "updateByAnswerParams";
    private static final String updateByAdditionalKey = "updateByAdditionalParams";

    /**
     * 考试工具箱 更正答案 获取试卷题目结构信息，仅获取客观题
     * @param params
     *   paperId 试卷id
     *   examId
     * @return questionObjectives 试卷中的试题结构信息
     *         additional         附加分数 t_question_additional 跟考试有关的试题附加分数
     */
    public Map<String, Object> getQuestions(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isValidId("examId")
                .verify();

        List<Map<String, Object>> paperStructureAndQuestionInfoList = getObjectiveStructureAndSomeInfo(params);
        examQuestionStructureService.changePaperQn2MarkQn(MapUtils.getLong(params, "paperId"), paperStructureAndQuestionInfoList);
        List<Map<String, Object>> additionalList = examRepository.selectList("ExamUpdateAnswerMapper.getAdditionalInfo", params);
        Map<Integer, Map<String, Object>> qn2AdditionalInfo = additionalList.stream()
          .collect(Collectors.toMap(x -> MapUtils.getInteger(x, "questionNumber"), x -> x, (v1, v2) -> {
              throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                "客观题答案数据异常，请联系管理员处理！questionNumber：" + MapUtils.getInteger(v1, "questionNumber"));
          }));
        if (ObjectUtil.isValidId(params.get("questionNumber"))) {
            // todo 可能出现客观题和主观题questionNumber相同，出现重复数据
            paperStructureAndQuestionInfoList = paperStructureAndQuestionInfoList.stream()
              .filter(x -> ObjectUtil.isValueEquals(x.get("questionNumber"), params.get("questionNumber"))).collect(Collectors.toList());
        }
        for (Map<String, Object> paperStructureAndQuestionInfo : paperStructureAndQuestionInfoList) {
            Integer questionNumber = MapUtils.getInteger(paperStructureAndQuestionInfo, "questionNumber");
            Map<String, Object> qnAdditional = qn2AdditionalInfo.get(questionNumber);
            if (MapUtils.isEmpty(qnAdditional)) {
                additionalList.add(MapUtil.of(
                        "additional", Collections.emptyList(),
                        "questionNumber", questionNumber,
                        "isAllRight", 0,
                        "isByOptions", 0,
                        "maxAnswerCount", null
                ));
            } else if (QuestionDisputeByOptionsEnum.isByAnswerValue(MapUtils.getInteger(qnAdditional, "isByOptions"))
              && !ObjectUtil.isNumeric(qnAdditional.get("maxAnswerCount"))) {
                // 处理下历史数据
                qnAdditional.put("maxAnswerCount", MapUtils.getString(qnAdditional, "newAnswer", "").split(",").length);
            }
        }

        return MapUtil.of(
                "questionObjectives", paperStructureAndQuestionInfoList,
                "additional", additionalList
        );
    }
    
    /**
     * 获取客观题试卷结构  更正答案页面用
     * @param params paperId
     * @return 客观题的试卷结构
     */
    private List<Map<String, Object>> getObjectiveStructureAndSomeInfo(Map<String, Object> params) {
        Verify.of(params).isValidId("paperId").verify();
        long paperId = MapUtil.getLong(params, "paperId");
        Document paper = ownPaperService.getPaper(paperId);
    
        Map<String, Map<String, Map<String, Object>>> objectiveStructureAndQuestions = PaperUtil.getObjectiveStructureAndQuestions(paper);
        Map<String, Map<String, Object>> questionNumber2realQuestion = objectiveStructureAndQuestions.get("questionNumber2realQuestion");
        Map<String, Map<String, Object>> questionNumber2paperQuestion = objectiveStructureAndQuestions.get("questionNumber2paperQuestion");
        
        List<Map<String, Object>> paperStructure = PaperUtil.getPaperStructure(paper);
        List<Map<String, Object>> paperObjectivesStructure = paperStructure.stream()
                .filter(o -> MapUtil.getInt(o, "readType") == 1)
                .collect(Collectors.toList());
        for (Map<String, Object> structure : paperObjectivesStructure) {
            String questionNumber = structure.get("questionNumber").toString();
            Map<String, Object> paperQuestion = questionNumber2paperQuestion.get(questionNumber);
            structure.put("question", questionNumber2realQuestion.get(questionNumber));
            structure.put("belongType", paperQuestion.get("belongType"));
        }
        
        return paperObjectivesStructure;
    }

    /**
     * 考试工具箱 更正答案
     *   1. 更正 mongo.paper.question
     *           mongo.question
     *   2. 更正 mysql.t_exam_item
     *      3.1 更正答案
     *      3.2 更正成绩   多选题需要计算，附加答案
     * @param params
     * - examId   考试id
     * - paperId  试卷id
     * - questions{    需要更改的试题
     *     ${markQuestionNumber} : {    阅卷的questionNumber -> tqm -> 试卷的questionNumber
     *         structureNumber  显示的题目名称
     *         newAnswer      新的正确答案，如果是多选题，则答案为 "A,B,C"
     *         oldAnswer      旧的答案，会校验是否为旧的答案，如果不是，则抛异常
     *         isRadio        是否单选题
     *         isAllRight     是否全部给满分
     *         scoreValue     该题的分数，用于校验参数是否正常，会与试卷进行对比，如果不同，则抛异常
     *         optionsCount   选择个数
     *         halfRight  少选得分，因为可能出现满分1.5分，一半为0.75分的情况，即半对分值
     *         additional: [
     *             {answer, scoreValue}
     *         ]
     *     }
     * @param needUpdateOtherExam 是否需要在这里更新当前试卷关联的其他试卷发布的考试的客观题成绩
     * - 更正答案  需要
     * - 设置客观题答案、试题设置、批量设置得分规则  不需要（这几个地方要么需要在自己的业务处理完了才去更新其他考试的，不能直接在这里更新，要么本来就不用更新其他考试的）
     */
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName="EXAM", name="UpdatePaperStructure", argValueKeys = {"[0].paperId"}, expireTime = -1)
    public String updateQuestionAnswer(Map<String, Object> params, boolean needUpdateOtherExam) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotBlank("questions")
                .verify();

        Map<String, Map<String, Object>> markQuestionNumber2UpdateInfo = MapUtil.getCast(params, "questions");
        if (MapUtils.isEmpty(markQuestionNumber2UpdateInfo)) {
            return "";
        }
        log.info("考试工具箱-更正答案-开始更新答案分值 : {}", params);
        
        long paperId = MapUtil.getLong(params, "paperId");
        Document paperSimple = paperManager.getPaperSimple(paperId);
        Long sourcePaperId = MapUtil.getLongNullable(paperSimple, "sourcePaperId");
        Long lockRootPaperId = Optional.ofNullable(sourcePaperId).orElse(paperId);
        return JedisTemplate.lockExecute("EXAM:PAPER:ANSWER:UPDATE:PAPER_ID:" + lockRootPaperId,
                0, "有其他人正在更正答案，请稍后再试", () -> {
                    // 将参数的阅卷qn->更新信息 转换为 试卷qn->更新信息
                    examQuestionStructureService.getPaperObjQn2MarkQn(paperId);
                    Map<Integer, Integer> objectiveQuestionMarkQn2PaperQn = examQuestionStructureService.getObjectiveQuestionMarkQn2PaperQn(paperId);
                    Map<String, Map<String, Object>> paperQuestionNumber2UpdateInfo = new HashMap<>();
                    for (Map.Entry<String, Map<String, Object>> entry : markQuestionNumber2UpdateInfo.entrySet()) {
                        String markQuestionNumberStr = entry.getKey();
                        Integer markQuestionNumber = MapUtil.getIntNullable(markQuestionNumberStr);
                        if (markQuestionNumber == null) {
                            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                                    "更新答案参数错误,阅卷题号不是合法的questionNumber: \"" + markQuestionNumberStr + "\"");
                        }
                        Integer paperQuestionNumber = objectiveQuestionMarkQn2PaperQn.get(markQuestionNumber);
                        if (paperQuestionNumber == null) {
                            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                                    "阅卷questionNumber(" + markQuestionNumberStr + ")找不到对应的试卷questionNumber");
                        }
                        paperQuestionNumber2UpdateInfo.put(String.valueOf(paperQuestionNumber), entry.getValue());
                    }
                    
                    // 校验设置的附加答案是否正确 (仅当前考试的)
                    Document paper = paperManager.getPaper(paperId);
                    List<Map<String, Object>> paperStructureList = PaperUtil.getPaperStructure(paper);
                    for (Map<String, Object> paperStructure : paperStructureList) {
                        String paperQuestionNumber = MapUtils.getString(paperStructure, "questionNumber");
                        Map<String, Object> updateInfo = paperQuestionNumber2UpdateInfo.get(paperQuestionNumber);
                        if (MapUtils.isNotEmpty(updateInfo)) {
                            //用附加答案中最新的答案覆盖试卷中对应试题的答案，校验附加答案时需要用这个数据
                            //mongo现在回滚不了，如果先改mongo-paper中的数据再校验的话，会有一致性问题
                            paperStructure.put("answer", updateInfo.get("newAnswer"));
                            paperStructure.put("scoreValue",
                              Optional.ofNullable(updateInfo.get("newScoreValue")).orElse(paperStructure.get("scoreValue")));
                            paperStructure.put("halfRight",
                              Optional.ofNullable(updateInfo.get("newHalfRight")).orElse(paperStructure.get("halfRight")));
                        }
                    }
                    PaperUtil.checkAdditionalAnswerAndFixScore(paperStructureList, paperQuestionNumber2UpdateInfo);

                    //试卷已经是有答案的状态了，且有正在进行中的t_exam_uploader时，不让更新，避免可能造成客观题分数不对
                    //可能存在多场考试都用了这张试卷的情况，但这里只改了examId对应的这一场考试，俊杰说暂时不考虑其他考试的情况，其他考试需要分别去各自的工具箱中使用保存答案来刷新分数
                    int oldAnswerStatus = MapUtils.getInteger(paper, "answerStatus");
                    if (ObjectUtil.isValueEquals(oldAnswerStatus, PaperAnswerStatus.HAS.getStatus()) &&
                      MapUtils.isNotEmpty(examRepository.selectOne("ExamUpdateAnswerMapper.getExamPaperUnfinishedUpload", params))) {
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "当前诊断有正在进行中的上传任务，请在处理完成后再更正答案");
                    }
                    
                    // 更新mongodb paper answer 答案 只更新试题正确答案的变化，附加答案的信息不存到mongo中
                    ownUpdateAnswerService.updatePaperAnswer(params, paperQuestionNumber2UpdateInfo);
                    
                    // 操作记录及附加答案分数操作记录
                    handleAdditionalAndDispute(params, markQuestionNumber2UpdateInfo);
                    // 更新已经批阅的客观题
                    updateExamItemScore(params, oldAnswerStatus);
                    // learnmore 2020年3月9日 线上作业
                    homeworkQuestionHandle.updateQuestionAnswer(params);

                    if (needUpdateOtherExam) {
                        return examPaperService.batchUpdateExamByUpdatePaperAnswer(params, null, null);
                    }
                    return "";
                });
    }

    public void updateQuestionAnswerLocal() {
        List<Long> paperIds = entrustService.getAnswerPaperIdByCreateDateTime();
        List<Long> examIds = examPaperService.getExamIdsByPaperIds(paperIds);
        for (Long examId : examIds) {
            try {
                updateQuestionAnswerLocal(examId);
            } catch (Exception e) {
                log.warn("重置客观题成绩出错！examId：" + examId);
            }
        }
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void updateQuestionAnswerLocal(Long examId) {
        Map<String, Object> params = MapUtil.of("examId", examId, "userId", 1, "userName", "admin");
        List<Map<String, Object>> examPapers = examPaperService.getExamPaper(params);
        if (examPapers.isEmpty()) {
            return;
        }
        examPapers.forEach(examPaper -> {
            Long paperId = MapUtils.getLong(examPaper, "paperId");
            Map<String, Object> paperParams = new HashMap<>(params);
            paperParams.put("paperId", paperId);
            Document paper = paperManager.getPaperSimple(paperId);
            if (ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS.getStatus())) {
                examUpdateAnswerService.updateExamItemScore(paperParams, examUpdateAnswerService.getKey2UpdateParams(examId, paperId));
            }
        });
        examStatQueueService.computeAll(params);
    }

    /**
     * 更新客观题分值时，各个key对应的更新参数list
     * @param questionMap 需要更改的试题相关信息
     *    {
     *     ${questionNumber} : {
     *         structureNumber  显示的题目
     *         newAnswer      新的正确答案，如果是多选题，则答案为 "A,B,C"
     *         oldAnswer      旧的答案，会校验是否为旧的答案，如果不是，则抛异常
     *         isRadio        是否单选题
     *         isAllRight     是否全部给满分
     *         scoreValue     该题的分数，用于校验参数是否正常，会与试卷进行对比，如果不同，则抛异常
     *         optionsCount   选择个数
     *         halfRight      少选得分，因为可能出现满分1.5分，一半为0.75分的情况，即半对分值
     *         additional: [
     *             {answer, scoreValue}
     *         ]
     *     }
     */
    public Map<String, List<Map<String, Object>>> getKey2UpdateParams(Map<String, Map<String, Object>> questionMap) {
        //在附加答案设置了全对的
        List<Map<String, Object>> updateAllRightParams = new ArrayList<>();
        //在附加答案设置了按正确选项得分的
        List<Map<String, Object>> updateByOptionsAnswerValueParams = new ArrayList<>();
        //在附加答案设置了按按正确选择数得分的
        List<Map<String, Object>> updateByOptionsAnswerCountParams = new ArrayList<>();
        //通过客观题的正确答案更新
        List<Map<String, Object>> updateByAnswerParams = new ArrayList<>();
        //在附加答案设置了自定义的附加答案的
        List<Map<String, Object>> updateByAdditionalParams = new ArrayList<>();
        int multipleQn = UnitTypeEnum.MultipleQn.getUnitType();
        for (Map.Entry<String, Map<String, Object>> questionEntry : questionMap.entrySet()) {
            Integer questionNumber = Integer.parseInt(questionEntry.getKey());
            Map<String, Object> question = questionEntry.getValue();
            if (ObjectUtil.isBlank(question.get("newAnswer"))) {
                continue;
            }
            String newAnswer = question.get("newAnswer").toString();
            Double scoreValue = MapUtils.getDouble(question, "scoreValue");
            //是否是单选题或判断题
            Boolean isRadio = MapUtils.getBoolean(question, "isRadio", true);
            Integer unitType = MapUtils.getInteger(question, "unitType");

            // 全给分，无论什么答案，都按照满分给分
            String isAllRight = question.getOrDefault("isAllRight", "").toString();
            if (Boolean.parseBoolean(isAllRight) || "1".equals(isAllRight)) {
                Map<String, Object> updateParam = new HashMap<>();
                updateParam.put("questionNumber", questionNumber);
                updateParam.put("finallyScore", scoreValue);
                updateAllRightParams.add(updateParam);
                continue;
            }

            // 按选项得分
            Integer byOptions = MapUtils.getInteger(question, "isByOptions");
            if (QuestionDisputeByOptionsEnum.isByOptions(byOptions)) {
                Map<String, Object> updateParam = new HashMap<>();
                updateParam.put("questionNumber", questionNumber);
                updateParam.put("correctAnswer", newAnswer);
                Integer maxAnswerCount = MapUtils.getInteger(question, "maxAnswerCount");
                List<Map<String, Object>> additionalList = MapUtil.getListMap(question, "additional");
                //学生填涂个数大于设置的最大选项个数时零分，否则按选中的正确选项实际分值相加
                if (QuestionDisputeByOptionsEnum.isByAnswerValue(byOptions)) {
                    if (CollectionUtils.isEmpty(additionalList)) {
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                          "题号" + question.get("structureNumber") + "未设置各个选项的得分");
                    }
                    if (additionalList.stream().mapToDouble(x -> MapUtils.getDouble(x, "scoreValue", 0D)).sum() > scoreValue) {
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "题号" + question.get("structureNumber") +
                          "各个选项的得分和不能大于试题满分");
                    }
                    if (!ObjectUtil.isNumeric(maxAnswerCount)) {
                        // 处理下历史数据
                        updateParam.put("maxAnswerCount", newAnswer.split(",").length);
                    } else if (!maxAnswerCount.equals(-1)) {
                        updateParam.put("maxAnswerCount", maxAnswerCount);
                    }
                    updateParam.put("optionScores", additionalList);
                    if (byOptions.equals(QuestionDisputeByOptionsEnum.BY_ANSWER_VALUE_NO_ERROR_OPTION.getValue())) {
                        //选择的是"按正确选项得分（有错误答案时不得分）"时，在上面两条规则的基础上，如果学生选中了错误答案，也直接0分
                        List<String> answerList = Arrays.asList(newAnswer.split(","));
                        HashSet<String> answerSet = new HashSet<>(answerList);
                        if (additionalList.stream().anyMatch(x -> !answerSet.contains(x.get("answer").toString()) &&
                          !MapUtils.getDouble(x, "scoreValue").equals(0.0))) {
                            //非正确答案如果设置成了非0分，则说明设置的有问题，抛异常
                            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                              "题号" + question.get("structureNumber") + "的非正确答案仅支持设置为0分");
                        }
                        updateParam.put("correctAnswerChars", answerList);
                    }
                    updateParam.put("finallyScore", scoreValue);
                    updateByOptionsAnswerValueParams.add(updateParam);
                } else if (QuestionDisputeByOptionsEnum.isByAnswerCount(byOptions)) {
                    if (CollectionUtils.isEmpty(additionalList)) {
                        continue;
                    }
                    additionalList.stream().collect(Collectors.groupingBy(x -> MapUtils.getInteger(x, "answerCount", 0)))
                      .forEach((countKey, scores) -> {
                          if (scores.size() > 1) {
                              throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                                "题号" + question.get("structureNumber") + "的得分规则中，正确选择数\"" + countKey + "\"不可出现多次");
                          }
                      });
                    if (additionalList.stream().anyMatch(x -> MapUtils.getDouble(x, "scoreValue", 0D) > scoreValue)) {
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "题号" + question.get("structureNumber") +
                          "存在正确选项数的得分大于试题满分");
                    }
                    if (!maxAnswerCount.equals(-1)) {
                        updateParam.put("maxAnswerCount", maxAnswerCount);
                    }
                    updateParam.put("optionScores", additionalList);
                    List<String> answerList = Arrays.asList(newAnswer.split(","));
                    updateParam.put("correctAnswerChars", answerList);
                    if (byOptions.equals(QuestionDisputeByOptionsEnum.BY_ANSWER_COUNT_NO_ERROR_OPTION.getValue())) {
                        //选择的是"按正确选择数得分（有错误答案时不得分）"时，在上面两条规则的基础上，如果学生选中了错误答案，也直接0分
                        updateParam.put("noErrorOption", true);
                    }
                    if (Boolean.FALSE.equals(isRadio) || ObjectUtil.isValueEquals(multipleQn, unitType)) {
                        updateParam.put("halfRight", MapUtils.getDouble(question, "halfRight", 0D));
                    }
                    updateParam.put("finallyScore", scoreValue);
                    updateByOptionsAnswerCountParams.add(updateParam);
                }
                continue;
            }

            // 非全给分情况, 单选题和正确答案一样的满分，否则0分，多选题还需要再看半对的情况
            Map<String, Object> updateParam = new HashMap<>();
            updateParam.put("questionNumber", questionNumber);
            updateParam.put("finallyScore", scoreValue);
            updateParam.put("correctAnswer", newAnswer);
            // 多选题，将正确答案从字符串转成List，用于在sql中处理半对分值的选项  "A,B,D" -> ["A","B","D"]
            // 同样是多选题，不同的地方用的标记还不一样 （＝。＝）
            if (Boolean.FALSE.equals(isRadio) || ObjectUtil.isValueEquals(multipleQn, unitType)) {
                updateParam.put("correctAnswerChars", Arrays.asList(newAnswer.split(",")));
                updateParam.put("halfRight", MapUtils.getDouble(question, "halfRight", 0D));
            }
            updateByAnswerParams.add(updateParam);

            // 处理附加选项
            if (question.get("additional") != null) {
                List<Map<String, Object>> additionalList = MapUtil.getCast(question, "additional");
                // 记录分值对应的答案
                Map<Double, List<String>> scoreAnswer = new HashMap<>();
                for (Map<String, Object> additional : additionalList) {
                    // 如果附加选项中有正确答案，则直接忽略掉该附加选项
                    if (!ObjectUtil.isValueEquals(newAnswer, additional.get("answer").toString())) {
                        if (Double.parseDouble(additional.get("scoreValue").toString()) > Double.parseDouble(question.get("scoreValue").toString())) {
                            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                                    "题号" + question.get("structureNumber") + "的附加答案" + additional.get("answer") + "的分值超过该题满分");
                        }
                        Double score = MapUtils.getDouble(additional, "scoreValue");
                        if (scoreAnswer.containsKey(score)) {
                            scoreAnswer.get(score).add(additional.get("answer").toString());
                        } else {
                            scoreAnswer.put(score, ListUtils.newArrayList(additional.get("answer").toString()));
                        }
                    }
                }

                // 处理 scoreAnswer
                for (Map.Entry<Double, List<String>> scoreAnswerEntry: scoreAnswer.entrySet()) {
                    Map<String, Object> additionalParam = new HashMap<>();
                    additionalParam.put("questionNumber", questionNumber);
                    additionalParam.put("finallyScore", scoreAnswerEntry.getKey());
                    additionalParam.put("recognitionValues", scoreAnswerEntry.getValue());
                    updateByAdditionalParams.add(additionalParam);
                }
            }
        }
        Map<String, List<Map<String, Object>>> key2UpdateParams = new HashMap<>();
        key2UpdateParams.put(updateAllRightKey, updateAllRightParams);
        key2UpdateParams.put(updateByOptionsAnswerValueKey, updateByOptionsAnswerValueParams);
        key2UpdateParams.put(updateByOptionsAnswerCountKey, updateByOptionsAnswerCountParams);
        key2UpdateParams.put(updateByAnswerKey, updateByAnswerParams);
        key2UpdateParams.put(updateByAdditionalKey, updateByAdditionalParams);
        return key2UpdateParams;
    }

    public Map<String, List<Map<String, Object>>> getKey2UpdateParams(Long examId, Long paperId) {
        return getKey2UpdateParams(getQuestionMap(examId, paperId, null));
    }

    public Map<String, List<Map<String, Object>>> getKey2UpdateParams(Long examId, Long paperId, Integer questionNumber) {
        return getKey2UpdateParams(getQuestionMap(examId, paperId, questionNumber));
    }

    /**
     * 根据examId、paperId、questionNumber获取题号-试题信息map
     * questionNumber是可选参数，传null时表示获取试卷所有试题的数据
     * 这里的附加答案是从考试中取的
     */
    public Map<String, Map<String, Object>> getQuestionMap(Long examId, Long paperId, Integer questionNumber) {
        Map<String, Object> query = MapUtil.of("examId", examId, "paperId", paperId);
        if(questionNumber != null){
            query.put("questionNumber", questionNumber);
        }
        Map<String, Object> questions = getQuestions(query);
        return getQuestionMap(MapUtil.getListMap(questions, "questionObjectives"), MapUtil.getListMap(questions, "additional"));
    }

    /**
     * 根据paperId获取题号-试题信息map
     * 这里的附加答案是从mongo中取的
     */
    public Map<String, Map<String, Object>> getQuestionMap(Long paperId) {
        Document additionalAnswer = paperObjQuestionAdditionalAnswerService.getByPaperId(paperId);
        if (additionalAnswer == null) {
            return Collections.emptyMap();
        }
        List<Map<String, Object>> additions = MapUtil.getListMap(additionalAnswer, "additionalAnswer");
        if (CollectionUtils.isEmpty(additions)) {
            return Collections.emptyMap();
        }
        List<Map<String, Object>> paperStructure = PaperUtil.getPaperStructure(paperManager.getPaper(paperId));
        examQuestionStructureService.changePaperQn2MarkQn(paperId, paperStructure);
        return getQuestionMap(paperStructure, additions);
    }

    public Map<String, Map<String, Object>> getQuestionMap(List<Map<String, Object>> questions, List<Map<String, Object>> additions) {
        // 客观题附加答案信息
        Map<Integer, Map<String, Object>> qn2Addition = additions.stream()
          .collect(Collectors.toMap(x -> MapUtils.getInteger(x, "questionNumber"), x -> x));
        // 客观题试题信息
        return questions.stream()
          .filter(x -> MapUtils.getInteger(x, "readType", 0) == 1)
          .collect(Collectors.toMap(x -> MapUtils.getString(x, "questionNumber"), x -> {
              int qn = MapUtils.getInteger(x, "questionNumber");
              if (qn2Addition.containsKey(qn)) {
                  MapUtil.copy(qn2Addition.get(qn), x, "additional", "isAllRight", "isByOptions", "maxAnswerCount");
              } else {
                  x.put("additional", Collections.emptyList());
                  x.put("isAllRight", 0);
                  x.put("isByOptions", 0);
                  x.put("maxAnswerCount", null);
              }
              if (MapUtils.getInteger(x, "unitType", 1) == 2) {
                  //unitType：1单选 2多选 3判断
                  x.put("isRadio", false);
                  //单选题和判断题没有半对，只有多选题有半对
                  x.put("halfRight", ObjectUtil.isNumeric(x.get("halfRight")) ? MapUtils.getDouble(x, "halfRight") : 0);
              } else {
                  x.remove("halfRight");
              }
              String answer = MapUtils.getString(x, "answer");
              x.put("oldAnswer", answer);
              x.put("newAnswer", answer);
              return x;
          }));
    }

    /**
     * 根据参数去更新试卷的对应客观题的得分
     * @param params examId 考试id
     *               paperId  试卷id
     *               studentIds  可选参数，需要更新的学生ids
     * @param key2UpdateParams 按key的顺序更新item时的参数
     */
    public void updateExamItemScore(Map<String, Object> params, Map<String, List<Map<String, Object>>> key2UpdateParams){
        Map<String, Object> normal = MapUtil.of(                      // 通用信息
          "examId", Long.valueOf(params.get("examId").toString()),
          "paperId", Long.valueOf(params.get("paperId").toString()),
          "userId", Long.valueOf(params.get("userId").toString()),
          "userName", params.get("userName").toString(),
          "currentTime", DateUtil.getCurrentDateTime()
        );
        if(params.containsKey("studentIds")){
            //处理完成那里可能会传这个，传的话就只修改对应学生
            normal.put("studentIds", params.get("studentIds"));
        }
        if (params.containsKey("readStatus")){
            //上传答案、更正答案、处理完成都会传这个，传的话就在改试题得分的同时也更新下阅卷状态
            normal.put("readStatus", params.get("readStatus"));
        }

        //全给分(全对)给分
        List<Map<String, Object>> updateAllRightParams = new ArrayList<>(
          key2UpdateParams.getOrDefault(updateAllRightKey, Collections.emptyList()));
        updateAllRightParams.forEach(x -> x.putAll(normal));
        examRepository.batchUpdate("ExamUpdateAnswerMapper.updateFinallyScore", updateAllRightParams);
        //按正确选项得分
        List<Map<String, Object>> updateByOptionsAnswerValueParams = new ArrayList<>(
          key2UpdateParams.getOrDefault(updateByOptionsAnswerValueKey, Collections.emptyList()));
        updateByOptionsAnswerValueParams.forEach(x -> x.putAll(normal));
        examRepository.batchUpdate("ExamUpdateAnswerMapper.updateFinallyScoreByOptionsAnswerValue", updateByOptionsAnswerValueParams);
        //按正确选择数得分
        List<Map<String, Object>> updateByOptionsAnswerCountParams = new ArrayList<>(
          key2UpdateParams.getOrDefault(updateByOptionsAnswerCountKey, Collections.emptyList()));
        updateByOptionsAnswerCountParams.forEach(x -> x.putAll(normal));
        examRepository.batchUpdate("ExamUpdateAnswerMapper.updateFinallyScoreByOptionsAnswerCount", updateByOptionsAnswerCountParams);
        //根据试题的正确答案给分，单选、多选都适用
        List<Map<String, Object>> updateByAnswerParams = new ArrayList<>(
          key2UpdateParams.getOrDefault(updateByAnswerKey, Collections.emptyList()));
        updateByAnswerParams.forEach(x -> x.putAll(normal));
        examRepository.batchUpdate("ExamUpdateAnswerMapper.updateByCorrectAnswer", updateByAnswerParams);
        //根据附加选项给分(自定义)
        List<Map<String, Object>> updateByAdditionalParams = new ArrayList<>(
          key2UpdateParams.getOrDefault(updateByAdditionalKey, Collections.emptyList()));
        updateByAdditionalParams.forEach(x -> x.putAll(normal));
        examRepository.batchUpdate("ExamUpdateAnswerMapper.updateFinallyScore", updateByAdditionalParams);
    }

    /**
     * 根据参数去更新试卷的对应客观题的得分
     * @param params examId 考试id
     *               paperId  试卷id
     *               questions 试题的map，前端传的就是map类型
     */
    public void updateExamItemScore(Map<String, Object> params, int oldAnswerStatus){
        Long paperId = MapUtils.getLong(params, "paperId");
        Document paper = paperManager.getPaperSimple(paperId);
        if (!ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS.getStatus())) {
            return;
        }
        Map<String, Object> updateParam = new HashMap<>(params);
        updateParam.put("readStatus", DictUtil.getDictValue("readStatus", "finished"));
        if (examService.checkExamTypeOnline(params)) {
            //线上作业，只更新已经提交作业的学生的客观题成绩
            List<Long> studentIds = studentHomeworkService.getSubmittedStudentIds(MapUtils.getLong(params, "examId"));
            if (studentIds.isEmpty()) {
                return;
            }
            updateParam.put("studentIds", studentIds);
        }
        Map<String, Map<String, Object>> questionMap = MapUtil.getCast(updateParam, "questions");
        if (!ObjectUtil.isValueEquals(oldAnswerStatus, PaperAnswerStatus.HAS.getStatus())) {
            Map<String, Map<String, Object>> allQuestionMap = getQuestionMap(MapUtils.getLong(params, "examId"), paperId, null);
            allQuestionMap.putAll(questionMap);
            questionMap = allQuestionMap;
        }
        updateExamItemScore(updateParam,  getKey2UpdateParams(questionMap));
    }

    /**
     * 操作记录及附加答案分数操作记录
     *    附加答案分值记录
     *    操作记录记录
     * @param params
     * - examId   考试id
     * - paperId  试卷id
     * - questions{    需要更改的试题
     *     ${questionNumber} : {
     *         structureNumber  显示的题目
     *         newAnswer      新的正确答案，如果是多选题，则答案为 "A,B,C"
     *         oldAnswer      旧的答案，会校验是否为旧的答案，如果不是，则抛异常
     *         isRadio        是否单选题
     *         isAllRight     是否全部给满分
     *         scoreValue     该题的分数，用于校验参数是否正常，会与试卷进行对比，如果不同，则抛异常
     *         optionsCount   选择个数
     *         halfRight  少选得分，因为可能出现满分1.5分，一半为0.75分的情况，即半对分值
     *         additional: [
     *             {answer, scoreValue}
     *         ]
     *     }
     */
    public void handleAdditionalAndDispute(final Map<String, Object> params, Map<String, Map<String, Object>> questionMap) {

        Map<String, Object> baseInfo = MapUtil.copy(params, "examId", "paperId", "userId", "userName");
        baseInfo.put("currentTime", DateUtil.getCurrentDateTime());
        for (Map.Entry<String, Map<String, Object>> questionEntry : questionMap.entrySet()) {
            // 更新操作记录，将原有的设计为旧的，插入新的
            String questionNumber = questionEntry.getKey();
            Map<String, Object> question = questionEntry.getValue();
            question.putAll(baseInfo);
            question.put("questionNumber", questionNumber);
            examRepository.update("ExamUpdateAnswerMapper.updateDisputeOld", question);
            examRepository.insert("ExamUpdateAnswerMapper.insertDispute", question);

            // 更新附加答案  先删后插
            examRepository.delete("ExamUpdateAnswerMapper.deleteAdditional", question);
            List<Map<String, Object>> additionalNews = MapUtil.getCast(question, "additional");
            if (CollectionUtils.isEmpty(additionalNews)) {
                continue;
            }
            Map<String, Object> insertAdditionalParams = new HashMap<>(baseInfo);
            insertAdditionalParams.put("questionNumber", questionNumber);
            for (Map<String, Object> additionalNew : additionalNews) {
                insertAdditionalParams.put("additionalAnswer", additionalNew.get("answer"));
                insertAdditionalParams.put("answerCount", additionalNew.getOrDefault("answerCount", 0));
                insertAdditionalParams.put("scoreValue", additionalNew.get("scoreValue"));
                examRepository.insert("ExamUpdateAnswerMapper.insertAdditional", insertAdditionalParams);
            }
        }
    }

    /**
     * 根据考试+试卷从mongo中初始化附加答案数据到考试中
     * @param params examId、userId、userName
     * @param examPapers 考试的需要初始化附加答案数据的试卷
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void initExamPaperAdditionalAnswer(Map<String, Object> params, List<Map<String, Object>> examPapers) {
        Map<String, Object> baseParam = MapUtil.copy(params, "examId", "userId", "userName");
        for (Map<String, Object> examPaper : examPapers) {
            Long paperId = MapUtils.getLong(examPaper, "paperId");
            Document paperDetail = paperManager.getPaperSimple(paperId);
            if (ObjectUtil.isValueEquals(paperDetail.get("answerStatus"), PaperAnswerStatus.HAS_NOT.getStatus())) {
                continue;
            }
            Map<String, Map<String, Object>> questionMap = getQuestionMap(paperId);
            if (questionMap.isEmpty()) {
                continue;
            }
            baseParam.put("paperId", paperId);
            handleAdditionalAndDispute(baseParam, questionMap);
        }
    }

    /**
     * 根据考试+试卷ids删除考试中对应试卷的附加答案数据
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void deleteExamPaperAdditionalAnswer(Long examId, List<Long> paperIds) {
        if (paperIds.isEmpty()) {
            return;
        }
        Map<String, Object> deleteParams = MapUtil.of("examId", examId, "paperIds", paperIds);
        examRepository.delete("ExamUpdateAnswerMapper.deleteExamPaperDispute", deleteParams);
        examRepository.delete("ExamUpdateAnswerMapper.deleteExamPaperAdditional", deleteParams);
    }

    /**
     * 获取答案子集
     *    注意，不保证子集顺序，
     *         仅保证子集的具体内容为提供的答案字符串前后顺序，如子集 "A,B" 不会出现 "B,A"
     * @param newAnswer 字符串，逗号分隔, "A,B,C"
     * @return ["A", "C", "A,B", "A,C", "B", "B,C"]
     */
    public static List<String> getAnswersSubList(String newAnswer) {
        if (StringUtils.isBlank(newAnswer)) {
            return Collections.emptyList();
        }
        String[] answers = newAnswer.split(",");
        int answersLength = answers.length;
        List<String> answersSubList = new ArrayList<>();
        for (int i = 1, iLen = (int) Math.pow(2, answersLength) - 1; i < iLen; i++) {
            List<String> temp = new ArrayList<>();
            for (int j = 0; j < answersLength; j++) {
                int index = (i & (1 << j));
                if (index > 0) {
                    temp.add(answers[j]);
                }
            }
            answersSubList.add(StringUtils.join(temp, ","));
        }
        return answersSubList;
    }
}
