package com.dongni.exam.mark.bigmodel.helper;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.exam.mark.bigmodel.bean.LLMChatResult;
import io.netty.channel.ChannelOption;
import java.time.Duration;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException.BadRequest;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

@Component
public class LLMChatHelper {

	private static final Logger logger = LoggerFactory.getLogger(LLMChatHelper.class);

	private WebClient webClient;
	private static final int MAX_CONNECTIONS = 200;
	private static final int LEFT_CONN_CNT_THRESHOLD = 32;
	private static final long MAX_WAIT_TIME_IN_MILLIS = TimeUnit.MINUTES.toMillis(2);
	private static final int CONN_CNT_CHECK_INTERVAL = 10000;
	private ConnectionProvider connectionProvider;

	public Mono<LLMChatResult> chatResponseMemo(String requestData) {
		// 模拟 HTTP 请求，这里使用一个示例 URL，你需要替换为实际的 URL
		Mono<String> httpMemo = webClient.post().uri("https://ark.cn-beijing.volces.com/api/v3/chat/completions")
			.header(HttpHeaders.CONTENT_TYPE, "application/json")
			.header(HttpHeaders.AUTHORIZATION, "Bearer c117d066-241a-43d7-8634-23721fbc6315")
			.body(BodyInserters.fromObject(requestData)).retrieve().bodyToMono(String.class)
			// 每个请求最多等待 90s
			.timeout(Duration.ofSeconds(90))
			.retry(2)
			.onErrorResume(error -> {
				if (error instanceof BadRequest) {
					logger.error(((BadRequest) error).getResponseBodyAsString());
					logger.error("request data is :\n{}", requestData);
				} else {
					logger.error(error.getMessage(), error);
					logger.error("unexpected error happened,request data is :{}", requestData);
				}
				return Mono.just("");
			});

		return httpMemo.map(result -> {
			if (StringUtils.isBlank(result)) {
				return LLMChatResult.empty();
			}
			MetaObject metaObject = SystemMetaObject.forObject(JSONUtil.parse(result, HashMap.class));
			String resultValue = (String) metaObject.getValue("choices[0].message.content");
			String reasoningContent = (String) metaObject.getValue("choices[0].message.reasoning_content");
			return new LLMChatResult(resultValue,reasoningContent);
		});
	}

	public void waitIfBusy() {
		long leftWaitTime = MAX_WAIT_TIME_IN_MILLIS;
		int inActiveCnt;
		while ((inActiveCnt = availableConnCnt()) < LEFT_CONN_CNT_THRESHOLD && leftWaitTime > 0) {

			try {
				logger.error("剩余可用连接数为{}", inActiveCnt);
				//noinspection BusyWait
				Thread.sleep(CONN_CNT_CHECK_INTERVAL);
				leftWaitTime -= CONN_CNT_CHECK_INTERVAL;
			} catch (InterruptedException ignored) {
			}
		}
		if (inActiveCnt < 32 && leftWaitTime < 0) {
			throw new RuntimeException("等待超时,稍后重试");
		}
	}

	private int availableConnCnt() {
		Matcher activeConnectionMatcher = Pattern.compile(".*activeConnections=(\\d+),.*")
			.matcher(SystemMetaObject.forObject(connectionProvider).getValue("channelPools").toString());
		if (activeConnectionMatcher.matches()) {
			int activeCnt = Integer.parseInt(activeConnectionMatcher.group(1));
			return MAX_CONNECTIONS - activeCnt;
		}
		return 1 << 8;
	}

	@PostConstruct
	private void init() {
		//连接池，根据实际情况设置最大连接数和最长等待时间
		connectionProvider = ConnectionProvider.fixed(
			"llm-connection-provider",
			MAX_CONNECTIONS,
			Duration.ofSeconds(60).toMillis()
		);

		// 根据实际情况设置buffSize
		int bufferSize = 1024 * 1024; // 1MB
		// 配置 Reactor Netty HttpClient，并设置缓冲区大小

		HttpClient httpClient = HttpClient.create(connectionProvider)
			.tcpConfiguration(tcpClient -> tcpClient
				.option(ChannelOption.SO_RCVBUF, bufferSize) // 设置接收缓冲区大小
				.option(ChannelOption.SO_SNDBUF, bufferSize) // 设置发送缓冲区大小
			);

		// 创建一个 WebClient 实例用于发起 HTTP 请求
		webClient = WebClient.builder()
			.clientConnector(new ReactorClientHttpConnector(httpClient))
			.build();
	}

}
