package com.dongni.exam.mark.bigmodel.bean;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @date 2025/4/28 9:52
 */
@Data
public class TimePrint {
    private static final ConcurrentLinkedQueue<TimePrint> queue = new ConcurrentLinkedQueue<>();
    private Long examItemId;
    private Date ocrStartTime;
    private Date ocrEndTime;
    private Long useTime;

    public TimePrint(Long examItemId) {
        this.examItemId = examItemId;
        this.ocrStartTime = new Date();
        queue.add(this);
    }

    public static String print() {
        StringBuilder sb = new StringBuilder();
        int i = 1;
        for (TimePrint print : queue) {
            if (Objects.nonNull(print.ocrEndTime) && Objects.nonNull(print.ocrStartTime)) {
                print.setUseTime(print.ocrEndTime.getTime() - print.ocrStartTime.getTime());
                sb.append(i).append(":").append(print.examItemId).append(":").append(print.useTime).append("ms").append(DateUtil.formatDateTime(print.ocrStartTime)).append(":").append(DateUtil.formatDateTime(print.ocrEndTime)).append("\n");
                i = i + 1;
            }
        }
        return sb.toString();
    }
}
