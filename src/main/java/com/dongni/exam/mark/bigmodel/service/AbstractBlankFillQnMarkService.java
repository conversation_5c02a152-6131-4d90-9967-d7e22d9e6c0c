package com.dongni.exam.mark.bigmodel.service;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.set;

import cn.hutool.crypto.digest.DigestUtil;
import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.EscapeUtils;
import com.dongni.common.utils.ResourceUtils;
import com.dongni.common.utils.VMTool;
import com.dongni.commons.mq.producer.MessageProducer;
import com.dongni.exam.mark.bigmodel.bean.AIQuestionInfo;
import com.dongni.exam.mark.bigmodel.bean.AiExamQuestion;
import com.dongni.exam.mark.bigmodel.bean.LLMChatResult;
import com.dongni.exam.mark.bigmodel.bean.MarkRule;
import com.dongni.exam.mark.bigmodel.bean.NamePath;
import com.dongni.exam.mark.bigmodel.bean.ReadOverRes;
import com.dongni.exam.mark.bigmodel.bean.RecRule;
import com.dongni.exam.mark.bigmodel.bean.RichAIItem;
import com.dongni.exam.mark.bigmodel.bean.RichAIQuestion;
import com.dongni.exam.mark.bigmodel.helper.LLMChatHelper;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.UpdateOptions;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * 大模型-填空题批改服务
 */
public abstract class AbstractBlankFillQnMarkService implements LLMMarkService {
	private static final Logger logger = LoggerFactory.getLogger(AbstractBlankFillQnMarkService.class);
	/**
	 * 批改结果发布topic
	 */
	protected static final String LLM_MARK_RESULT_TOPIC = "PAPER_READ_LLM_REC_RESULT";
	/**
	 * 批改任务接收topic
	 */
	protected static final String LLM_MARK_TOPIC = "PAPER_READ_LLM_REC";

	protected static final int MARK_BATCH_SIZE = 32;

	protected Map<Character, String> similarMap;

	@Autowired
	private MessageProducer messageProducer;

	@Value("${dongni.file-storage.cdn-url}")
	private String cdnPrefix;
	@Autowired
	private  AnalysisMongodb analysisMongodb;
	@Autowired
	protected LLMChatHelper llmChatHelper;
	private MongoCollection<Document> paperReadLLMPrompt;
	protected String[] latexLines;
	protected Properties recRulesMap = new Properties();
	protected Properties markRuleMap = new Properties();


	@Override
	public NamePath getQuestionType() {
		return new NamePath("填空题", "blankfill");
	}



	@Override
	public void mark(List<AiExamQuestion> aiExamQuestion){
		if(aiExamQuestion.isEmpty()){
			return;
		}
		Map<Long, List<AiExamQuestion>> pr2SItemListMap = aiExamQuestion.stream().collect(
			Collectors.groupingBy(AiExamQuestion::getPaperReadId)
		);
		//根据每个题第一条数据获取题目上的必要信息
		List<AIQuestionInfo> questionInfos = pr2SItemListMap.values().stream().map(x -> {
			AiExamQuestion sample = x.get(0);
			return new AIQuestionInfo(
				sample.getPaperReadId(),
				sample.getQuestionUrl(),
				sample.getQuestionDesc(),
				sample.getMarkStdUrl(),
				sample.getMarkStd(),
				sample.getScoreValue()
			);
		}).collect(Collectors.toList());
		//根据题干和标准答案的输入信息提取题目信息
		List<RichAIQuestion> richAIQuestions = extractQuestionAndAnswer(questionInfos);
		for (RichAIQuestion richAIQuestion : richAIQuestions) {
			List<AiExamQuestion> allQuestionItems = pr2SItemListMap.getOrDefault(richAIQuestion.getPaperReadId(), Collections.emptyList());
			//小批量异步执行批改任务
			BatchDataUtil.execute(allQuestionItems, questionItems -> {
				llmChatHelper.waitIfBusy();
				Mono<List<RichAIItem>> responseMono = markItems(richAIQuestion, questionItems);
				responseMono.map(richAIItems -> {
					List<ReadOverRes> result = richAIItems.stream().map(x -> {
						ReadOverRes res = new ReadOverRes();
						AiExamQuestion examItem = x.getExamItem();
						res.setPaperReadId(examItem.getPaperReadId());
						res.setExamItemId(examItem.getExamItemId());
						res.setConfidence(0D);
						res.setScore(0.0);
						Map<String, String> tags = x.getTags();
						if (!tags.isEmpty()) {
							res.setText(tags.get("学生作答"));
							try {
								res.setScore(Double.valueOf(tags.get("得分").replaceAll("\\\\n|分", "").trim()));
								res.setConfidence(1D);
							} catch (Exception ignored) {
								logger.warn("{}打分结果[{}]带有非数字信息", examItem.getExamItemId(), tags.get("得分"));
								res.setConfidence(0D);
							}
							res.setMarkComment(tags.get("思考"));
							logger.info("{}学生作答为:{},评分为:{}",res.getExamItemId(),res.getText(),res.getScore());
						}
						return res;
					}).collect(Collectors.toList());
					//发送批改完成信息
					messageProducer.send(LLM_MARK_RESULT_TOPIC, String.valueOf(richAIQuestion.getPaperReadId()), result);
					return result;
				}).subscribe();
			}, MARK_BATCH_SIZE);
		}
	}


	@SneakyThrows
	public Mono<List<RichAIItem>> markItems(RichAIQuestion richAIQuestion, List<AiExamQuestion> questionItems){
		if(questionItems.isEmpty()){
			return Mono.empty();
		}
		String tplFileName = "prompt_stu_answer.vm";
		if (StringUtils.isBlank(richAIQuestion.getQuestionTags().get("题干"))
			&& ResourceUtils.isResourceExists(getDirPath() + "prompt_stu_answer_no_question.vm")) {
			logger.info("无题干且无题干提示词配置存在，使用" + getDirPath() + "prompt_stu_answer_no_question.vm");
			tplFileName = "prompt_stu_answer_no_question.vm";
		}

		String promptTpl = getReadFileToString(getDirPath() + tplFileName);



		Map<String, Object> promptDataMap = new HashMap<>();
		String gradeName = "";
		AiExamQuestion sampleItem = questionItems.get(0);
		try {
			gradeName = DictUtil.getDictLabel("gradeType", sampleItem.getGradeType());
		} catch (Exception ignored) {
		}
		promptDataMap.put("gradeName", gradeName);
		promptDataMap.put("scoreValue", sampleItem.getScoreValue());
		promptDataMap.put("questionTags", richAIQuestion.getQuestionTags());
		promptDataMap.put("answerTags", richAIQuestion.getAnswerTags());
		promptDataMap.put("recRules", richAIQuestion.getRecRules());
		promptDataMap.put("recRuleList", richAIQuestion.getRecRuleList());
		promptDataMap.put("markRules", richAIQuestion.getMarkRules());
		promptDataMap.put("markRuleList", richAIQuestion.getMarkRuleList());
		String blankNumStr = richAIQuestion.getAnswerTags().get("填空数量");
		int blankNum = toInt(blankNumStr, 1);
		promptDataMap.put("scoreValuePerBlank", sampleItem.getScoreValue() / blankNum);
		String prompt = VMTool.parse(promptTpl, promptDataMap);

		String customPrompt = getPaperReadLLMPromptInfo(richAIQuestion.getPaperReadId(), "prompt");
		if (StringUtils.isNoneBlank(customPrompt)) {
			prompt = customPrompt;
		}
		String escapedPrompt = EscapeUtils.escape(prompt);
		String requestDataTpl = getReadFileToString(getDirPath() + "request_native.vm");
		// 使用 Flux 处理数据
		Mono<List<RichAIItem>> itemsMono = Flux.fromIterable(questionItems).parallel()
			.runOn(Schedulers.parallel()).flatMap(examItem -> {
				Map<String, Object> requestDataMap = new HashMap<>();
				requestDataMap.put("imageList", getImgUrlList(examItem.getUrl()));
				requestDataMap.put("prompt", escapedPrompt);
				String model = "doubao-1-5-thinking-vision-pro-250428";
				String thinking = "enabled";
				requestDataMap.put("modelKey", model);
				requestDataMap.put("thinking", thinking);
				customizeModelParam(requestDataMap);
				String requestData = VMTool.parse(requestDataTpl, requestDataMap);
				return llmChatHelper.chatResponseMemo(requestData).map(x -> {
					RichAIItem richAIItem = new RichAIItem(examItem, x);
					if (x.isEmpty() && examItem.incrRetryCount() <= 3) {
						logger.error("{}第{}次批阅失败，开启下一次尝试", examItem.getExamItemId(), examItem.getRetryCount());
						messageProducer.send(LLM_MARK_TOPIC, String.valueOf(examItem.getPaperReadId()), examItem);
					}
					return richAIItem;
				});

			}).sequential().collectList();
		return itemsMono;
	}

	private static int toInt(String blankNumStr, int defaultValue) {
		try {
			return Integer.parseInt(blankNumStr.trim());
		} catch (NumberFormatException e) {
		}
		return defaultValue;
	}

	@SneakyThrows
	@Override
	public List<RichAIQuestion> extractQuestionAndAnswer(List<AIQuestionInfo> questionInfos) {
		String questionPromptTpl = getReadFileToString(getDirPath() + "prompt_question.vm");
		String answerPromptTpl = getReadFileToString(getDirPath() + "promopt_answer.vm");
		String requestDataTpl = getReadFileToString(getDirPath() + "request_question_or_answer.vm");
		// 使用 Flux 处理数据
		Mono<List<RichAIQuestion>> listMono = Flux.fromIterable(questionInfos).parallel()
			.runOn(Schedulers.parallel()).flatMap(question -> {
				RichAIQuestion richAIQuestion = new RichAIQuestion(question);
				Flux<RichAIQuestion> curFlux = Flux.empty();
				String questionImgUrls = question.getQuestionImgUrls();
				if (StringUtils.isNoneBlank(questionImgUrls)
					|| StringUtils.isNoneBlank(question.getQuestionWord())) {
					String questionDesc = "图片和'"+question.getQuestionWord()+"'";
					if(StringUtils.isBlank(question.getQuestionImgUrls())){
						questionDesc = "'"+question.getQuestionWord()+"'";
					}
					if (StringUtils.isBlank(question.getQuestionWord())) {
						questionDesc = "图片";
					}
					Map<String, Object> promptDataMap = new HashMap<>();
					promptDataMap.put("desc",questionDesc);
					String prompt = VMTool.parse(questionPromptTpl, promptDataMap);
					Map<String, Object> questionDataMap = new HashMap<>();
					questionDataMap.put("imageList", getImgUrlList(question.getQuestionImgUrls()));
					questionDataMap.put("prompt", EscapeUtils.escape(prompt));
					String requestData = VMTool.parse(requestDataTpl, questionDataMap);
					Mono<RichAIQuestion> memo = llmChatHelper.chatResponseMemo(requestData).map(LLMChatResult::getContent).map(richAIQuestion::setQnAnalysisResult);
					curFlux = curFlux.concatWith(memo);
				}
				if (StringUtils.isNoneBlank(question.getAnswerImgUrls())
					|| StringUtils.isNoneBlank(question.getAnswerWord())) {
					String answerDesc = "图片和'"+question.getAnswerWord()+"'";
					if (StringUtils.isBlank(question.getAnswerImgUrls())) {
						answerDesc = "'" + question.getAnswerWord() + "'";
					}
					if (StringUtils.isBlank(question.getAnswerWord())) {
						answerDesc = "图片";
					}

					Map<String, Object> promptDataMap = new HashMap<>();
					promptDataMap.put("desc", answerDesc);
					promptDataMap.put("score_value", question.getScoreValue());
					String prompt = VMTool.parse(answerPromptTpl, promptDataMap);
					Map<String, Object> answerDataMap = new HashMap<>();
					answerDataMap.put("imageList", getImgUrlList(question.getAnswerImgUrls()));
					answerDataMap.put("prompt", EscapeUtils.escape(prompt));
					String requestData = VMTool.parse(requestDataTpl, answerDataMap);
					Mono<LLMChatResult> llmChatResultMono;
					String answerHex = DigestUtil.md5Hex(requestData);
					Document storedAnswerInfo = getPaperReadLLMPromptInfo(richAIQuestion.getPaperReadId(), "answerAnalysisResult");
					if (storedAnswerInfo != null && Objects.equals(answerHex, storedAnswerInfo.getString("hex"))
						&& StringUtils.isNoneBlank(storedAnswerInfo.getString("content"))) {
						llmChatResultMono = Mono.just(new LLMChatResult(storedAnswerInfo.getString("content"),
							storedAnswerInfo.getString("reasoningContent")
						));
					} else {
						llmChatResultMono = llmChatHelper.chatResponseMemo(requestData).map(x->{
							Document ds = new Document();
							ds.put("hex", answerHex);
							ds.put("content", x.getContent());
							//ds.put("reasoningContent", x.getReasoningContent());
							paperReadLLMPrompt.updateOne(eq("paperReadId", richAIQuestion.getPaperReadId()),
								set("answerAnalysisResult", ds),
								new UpdateOptions().upsert(true)
								);
							return x;
						});
					}
					Mono<RichAIQuestion> memo = llmChatResultMono.map(LLMChatResult::getContent).map(richAIQuestion::setAnswerAnalysisResult);
					curFlux = curFlux.concatWith(memo);
				}
				return curFlux;
			}).collectSortedList((a, b) -> 0);
		//noinspection StreamToLoop,ConstantConditions
		List<RichAIQuestion> richAIQuestionList = listMono.block().stream().distinct().collect(Collectors.toList());
		for (RichAIQuestion richAIQuestion : richAIQuestionList) {
			postProcess(richAIQuestion);
		}
		return richAIQuestionList;
	}


	protected void customizeModelParam(Map<String, Object> ignored) {

	}

	protected void postProcess(RichAIQuestion richAIQuestion) {
		richAIQuestion.init();
		//对数据类型后置处理
		List<String> recRules = postProcessRecRule(richAIQuestion);
		richAIQuestion.setRecRules(recRules);
		richAIQuestion.setRecRuleList(recRules);
		//对给分标准后置处理
		List<String> markRules = postProcessMarkRule(richAIQuestion);
		richAIQuestion.setMarkRules(markRules);
		richAIQuestion.setMarkRuleList(markRules);
	}

	protected List<String> postProcessRecRule(RichAIQuestion richAIQuestion) {
		List<String> recRules = new ArrayList<>();
		for (RecRule rule : RecRule.values()) {
			if (rule.match(richAIQuestion)) {
				String desc = (String) recRulesMap.get(rule.key());
				if (desc != null) {
					recRules.add(desc);
				}
			}
		}
		return recRules;
	}

	protected List<String> postProcessMarkRule(RichAIQuestion richAIQuestion) {
		List<String> markRules = new ArrayList<>();
		for (MarkRule rule : MarkRule.values()) {
			if (rule.match(richAIQuestion)) {
				String desc = (String) markRuleMap.get(rule.key());
				switch (rule) {
					case LetterReplaceRule:
						desc = generateReplaceRule(richAIQuestion.getAnswerTags().get("标准答案"));
						break;
					case CorrectScoreRule:
						desc = String.format(desc, richAIQuestion.getAiQuestionInfo().getScoreValue());
						break;
					case CustomRule:
						desc = richAIQuestion.getAnswerTags().get("给分标准");
						break;
					case FormatRule:
						desc = String.format(desc,richAIQuestion.getQuestionTags().get("格式要求"));
					default:
				}
				if (StringUtils.isNoneBlank(desc)) {
					markRules.add(desc);
				}
			}
		}
		return markRules;
	}

	protected String generateReplaceRule(String answer) {
		for (String latexLine : latexLines) {
			if(!answer.contains("\\")){
				break;
			}
			answer = answer.replaceAll(latexLine, "");
		}
		Map<Character, String> answerSimpleMap = new HashMap<>();
		for (char c : answer.toCharArray()) {
			String line = similarMap.get(c);
			if (line != null) {
				answerSimpleMap.put(c, line);
			}
		}
		Map<Character, String> uniqueChar2LineMap = answerSimpleMap.entrySet().stream().collect(
			Collectors.groupingBy(Entry::getValue)
		).values().stream().filter(
			x -> x.size() == 1
		).collect(
			Collectors.toMap(x -> x.get(0).getKey(), x -> x.get(0).getValue())
		);
		if (uniqueChar2LineMap.isEmpty()) {
			return "";
		}
		StringBuilder sb = new StringBuilder();

		sb.append("对学生作答的内容执行以下替换:");
		uniqueChar2LineMap.forEach((k, v) -> {
			sb.append("将字符 ");
			for (char c : v.toCharArray()) {
				if (c != k) {
					sb.append(c).append("、");
				}
			}
			sb.deleteCharAt(sb.length() - 1);
			sb.append(" 替换为 ").append(k).append(",");
		});
		sb.deleteCharAt(sb.length() - 1);
		sb.append(",以替换后的答案给分。");
		return sb.toString();
	}

	private String getReadFileToString(String resourcePath) {
		return ResourceUtils.readResourceFile2String(resourcePath);
	}

	private List<String> getImgUrlList(String imgUrls) {
		if (imgUrls == null) {
			return Collections.emptyList();
		}
		imgUrls = imgUrls.trim();
		if (StringUtils.isBlank(imgUrls)) {
			logger.warn("urls is empty");
			return Collections.emptyList();
		}
		List<String> imageUrls = Arrays.stream(imgUrls.split(";")).map(x -> {
			if (x.startsWith("http")) {
				return x;
			}
			return cdnPrefix + "/" + x;
		}).collect(Collectors.toList());
		return imageUrls;
	}


	@PostConstruct
	private void init() {
		if (!cdnPrefix.startsWith("http")) {
			cdnPrefix = "https:" + cdnPrefix;
		}
		paperReadLLMPrompt = analysisMongodb.getMongoDatabase().getCollection("paperReadLLMPrompt");
		initMore();
	}

	protected void initMore() {
		String similarTxt = ResourceUtils.readResourceFile2String(getDirPath() + "similar.txt");
		String[] similarLines = similarTxt.split("\\n");
		similarMap = new HashMap<>();
		for (String line : similarLines) {
			for (char c : line.toCharArray()) {
				similarMap.put(c, line);
			}
		}
		if(ResourceUtils.isResourceExists(getDirPath() + "latex.txt")){
			String latexTxt = ResourceUtils.readResourceFile2String(getDirPath() + "latex.txt");
			latexLines = latexTxt.split("\\n");
		}else{
			latexLines = new String[]{};
		}
		if(ResourceUtils.isResourceExists(getCommonPath() + "markRules.properties")){
			markRuleMap = ResourceUtils.readProperties(getCommonPath() + "markRules.properties");
		}
		if(ResourceUtils.isResourceExists(getDirPath() + "markRules.properties")){
			Properties courseQuestionTypeMarkRules = ResourceUtils.readProperties(getDirPath() + "markRules.properties");
			markRuleMap.putAll(courseQuestionTypeMarkRules);
		}
		initRecRules();


	}

	protected void initRecRules() {
		if(ResourceUtils.isResourceExists(getCommonPath() + "recRules.properties")){
			recRulesMap = ResourceUtils.readProperties(getCommonPath() + "recRules.properties");
		}
        if(ResourceUtils.isResourceExists(getDirPath() + "recRules.properties")){
            Properties courseQuestionTypeMarkRules = ResourceUtils.readProperties(getDirPath() + "recRules.properties");
            recRulesMap.putAll(courseQuestionTypeMarkRules);
        }
	}

	private<T> T getPaperReadLLMPromptInfo(long paperReadId,String field) {
		List<Document> customPrompt = paperReadLLMPrompt.find(eq("paperReadId", paperReadId)).limit(1)
			.into(new ArrayList<>());
		if (!customPrompt.isEmpty()) {
			//noinspection unchecked
			return (T)(customPrompt.get(0).get(field));
		}
		return null;
	}

}
