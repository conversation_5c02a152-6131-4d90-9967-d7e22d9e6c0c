package com.dongni.exam.mark.controller;

import com.dongni.common.async.MyAsyncExecutor;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.mark.service.ExamPaperObjQnSettingService;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: hzw
 * @date: 2024/11/14
 * @description:
 */
@RestController
@RequestMapping(value = "/exam/paper/objective/question")
public class ExamPaperObjQnSettingController  extends BaseController {

	@Autowired
	private ExamPaperObjQnSettingService examPaperObjQnSettingService;
	@Autowired
	private MyAsyncExecutor myAsyncExecutor;

	/**
	 * 获取诊断管理-试题设置-客观题处展示的试题列表
	 */
	@GetMapping("/list")
	public Response getQuestionList(Map<String, Object> params) {
		return new Response(examPaperObjQnSettingService.getExamPaperObjectiveQuestionList(params));
	}

	/**
	 * 获取诊断管理-试题设置-客观题-单个试题详情
	 */
	@GetMapping("/single")
	public Response getSingleQuestionInfo(Map<String, Object> params) {
		return new Response(examPaperObjQnSettingService.getSingleQuestionInfo(params));
	}

	/**
	 * 诊断管理-试题设置-客观题-单个试题-试题设置
	 */
	@PostMapping("/single/async")
	public Response updateSingleQuestionInfo(Map<String, Object> params) {
		return new Response(myAsyncExecutor.execute("试题设置", params,
			() -> examPaperObjQnSettingService.updateSingleQuestionInfo(params)
		));
	}

	/**
	 * 诊断管理-试题设置-客观题-设置客观题答案
	 * 异步轮询
	 */
	@PostMapping("/options/count/and/answer/async")
	@DongniRequest(operationName = "设置客观题答案")
	public Response updateQuestionOptionsCountAndAnswerAsync(Map<String, Object> params) {
		return new Response(myAsyncExecutor.execute("设置客观题答案", params,
			() -> examPaperObjQnSettingService.updateQuestionOptionsCountAndAnswer(params)
		));
	}

	/**
	 * 诊断管理-试题设置-客观题-批量修改分值
	 * 异步轮询
	 */
	@PostMapping("/score/async")
	@DongniRequest(operationName = "批量修改分值")
	public Response updateQuestionScoreAsync(Map<String, Object> params) {
		return new Response(myAsyncExecutor.execute("批量修改分值", params,
			() -> examPaperObjQnSettingService.updateQuestionScore(params)
		));
	}

	/**
	 * 诊断管理-试题设置-客观题-批量设置得分规则
	 * 异步轮询
	 */
	@PostMapping("/additional/and/dispute/async")
	@DongniRequest(operationName = "批量设置得分规则")
	public Response updateQuestionAdditionalAndDisputeAsync(Map<String, Object> params) {
		return new Response(myAsyncExecutor.execute("批量修改分值", params,
			() -> examPaperObjQnSettingService.updateQuestionAdditionalAndDisputeAsync(params)
		));
	}
}
