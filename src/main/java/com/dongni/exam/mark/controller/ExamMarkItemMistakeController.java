package com.dongni.exam.mark.controller;

import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.mark.service.ExamMarkItemMistakeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Created by Heweipo on 2017/5/9.
 *
 * 老师标记学生作答为典型错误
 */
@RestController
//@RequestMapping("/exam/mark/item/mistake")
public class ExamMarkItemMistakeController extends BaseController {

    @Autowired
    private ExamMarkItemMistakeService examMarkItemMistakeService;

    /**
     * 获取学生作答的典型错误标签
     * params examResultItemId
     * @return 错误标签集合
     */
//    @RequestMapping(method = RequestMethod.GET)
    Response getItemMistake(){
        return new Response(examMarkItemMistakeService.getItemMistake(getParameterMap()));
    }

    /**
     * 标记学生作答为典型错误标签
     * @param params examResultItemId examMistakeLabelId mistakeName
     * @return examItemMistakeId
     */
//    @RequestMapping(method = RequestMethod.POST)
    Response insertItemMistake(@RequestBody Map<String,Object> params){
        return new Response(examMarkItemMistakeService.insertItemMistake(params));
    }

    /**
     * 标记学生作答为典型错误标签
     * @param params examResultItemId examMistakeLabelId mistakeName
     * @return examItemMistakeId
     */
    @RequestMapping(value = "/multi",method = RequestMethod.POST)
    Response insertItemMistakes(@RequestBody Map<String,Object> params){
        return new Response(examMarkItemMistakeService.insertItemMistakes(params));
    }


    /**
     * 删除学生作答上的典型错误标签
     * @param params examItemMistakeId
     */
//    @RequestMapping(method = RequestMethod.DELETE)
    Response deleteItemMistake(@RequestBody Map<String,Object> params){
        examMarkItemMistakeService.deleteItemMistake(params);
        return new Response();
    }

}
