package com.dongni.exam.mark.controller;

import cn.hutool.core.bean.BeanUtil;
import com.dongni.commons.annotation.DongniNotCheckToken;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.mark.ai.composition.entity.TrainDTO;
import com.dongni.exam.mark.ai.composition.service.CompositionTrainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/10/28 14:22
 */
@RestController()
@RequestMapping("/train")
public class CompositionTrainController extends BaseController {

    @Autowired
    private CompositionTrainService compositionTrainService;

    @DongniNotRequireLogin
    @DongniNotCheckToken
    @RequestMapping("/english")
    public void train() {
        TrainDTO para = new TrainDTO();
        BeanUtil.fillBeanWithMap(getParameterMap(), para, true);
        compositionTrainService.trainGoodExamData(para);
    }
}
