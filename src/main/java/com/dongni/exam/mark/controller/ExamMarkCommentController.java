package com.dongni.exam.mark.controller;

import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.DownloadController;
import com.dongni.exam.mark.service.ExamMarkCommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * Created by <PERSON>wei<PERSON> on 2018/11/20.
 *
 * 老师批注、优秀作答、学霸答案、考试评语
 */
@RestController
@RequestMapping("/exam/mark/comment")
public class ExamMarkCommentController extends DownloadController {

    @Autowired
    private ExamMarkCommentService examMarkCommentService;

//    @PostMapping
    public Response saveComment(){
        return new Response(examMarkCommentService.saveComment(getParameterMap()));
    }

//    @PostMapping("/answer/status")
    public Response saveAnswerStatus(){
        return new Response(examMarkCommentService.saveAnswerStatus(getParameterMap()));
    }

//    @GetMapping
    public Response getCommentAndAnswerStatus(){
        return new Response(examMarkCommentService.getCommentAndAnswerStatus(getParameterMap()));
    }

//    @GetMapping("/all")
    public Response getItemAllComment(){
        return new Response(examMarkCommentService.getItemAllComment(getParameterMap()));
    }


    /**
     * 考试工具箱 评语导入 获取学生评语导入模版
     *   按照 examId schoolId 检索出参加考试的班级的所有学生，包括缺考的学生/不参考的学生
     * 参数： -
     *  - examId   考试id
     *  - schoolId 学校id
     *
     * @return xls模版文件
     *   序号/班级/姓名/考号/评语
     */
    @GetMapping("/student/tpl")
    public Response getStudentCommentTemplate() {
        return new Response(examMarkCommentService.getStudentCommentTemplate(getParameterMap()));
    }

    /**
     * 考试工具箱 评语导入 导入学生考试评语
     *   - 校验表结果是否完整
     *   - 校验表数据是否完整 (班级/姓名/学号) (评语可以为空/评语为空时会被过滤掉)
     * 参数：
     *   examId   考试id
     *   schoolId 学校id
     *   importFile MultipartFile 学生评语文件excel，在下载的模版上进行修改
     * @return updateExamStudentCommentCount 实际的更新数，评论为空的会被过滤掉
     */
    @PostMapping("/student/tpl")
    public Response importStudentCommentTemplate(MultipartFile importFile) {
        Map<String, Object> params = getParameterMap();
        params.put("importFile", importFile);
        return new Response(examMarkCommentService.importStudentCommentTemplate(params));
    }
    
    /** 
    * @Description: 获取学生评语 
    * @Param: examId studentId schoolId
    * @return: 学生评语
    */
    @GetMapping("/student/comment")
    public Response getStudentComment() {
        return new Response(examMarkCommentService.getStudentComment(getParameterMap()));
    }
}
