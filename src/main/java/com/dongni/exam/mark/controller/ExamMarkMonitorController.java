package com.dongni.exam.mark.controller;

import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.mark.service.ExamMarkMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/11/27.
 *
 */
@RestController
@RequestMapping("/exam/mark/monitor")
public class ExamMarkMonitorController extends BaseController {

    @Autowired
    private ExamMarkMonitorService examMarkMonitorService;

    @GetMapping
    public Response getMonitorList(){
        return new Response(examMarkMonitorService.getMonitorList(getParameterMap()));
    }

}
