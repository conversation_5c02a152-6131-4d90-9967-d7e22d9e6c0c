package com.dongni.exam.mark.read.intelligent.service;

import com.dongni.common.report.excel.ExportExcelTemplate;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.arrange.service.PaperReadArrangeService;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.summingInt;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> <br>
 * 2021/11/10 <br>
 *
 */
@Service
public class ReadIntelligentService {
    
    @Autowired
    private PaperReadArrangeService paperReadArrangeService;
    
    @Autowired
    private ReadIntelligentExamInfoService readIntelligentExamInfoService;
    
    /**
     * 阅卷安排-智能分配-下载表格
     * @param params examId paperId
     *               readBlockWorkloadList 第一步题目工作量的信息
     *                   readBlockId, workload
     *               schoolCourseWorkloadList 第三步每一行的信息
     *                   schoolId courseId workloadPerTeacher(单个老师工作设置量) studentCount(学生数量设置)
     * @return 下载链接
     */
    public String downloadExamMarkIntelligentExcel(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotEmptyCollections("readBlockWorkloadList")
                .isNotEmptyCollections("schoolCourseWorkloadList")
                .verify();
        
        // ---------------------------------------------------------------------------------- readBlockId -> workload
        // readBlockId, workload
        List<Map<String, Object>> readBlockWorkloadList = MapUtil.getCast(params, "readBlockWorkloadList");
        Map<Long, Double> readBlockId2workload = readBlockWorkloadList.stream()
                .collect(toMap(o -> MapUtil.getLong(o, "readBlockId"), o -> MapUtil.getDouble(o, "workload")));
        
        // ---------------------------------------------------------------------------------- teacher
        // schoolId courseId workloadPerTeacher studentCount
        List<Map<String, Object>> schoolCourseWorkloadList = MapUtil.getCast(params, "schoolCourseWorkloadList");
        Map<Long, Map<Long, Integer>> schoolId2courseId2workloadPerTeacher = schoolCourseWorkloadList.stream()
                .collect(groupingBy(o -> MapUtil.getLong(o, "schoolId"),
                        toMap(o -> MapUtil.getLong(o, "courseId"), o -> MapUtil.getInt(o, "workloadPerTeacher"))));
        Map<Long, Integer> courseId2studentCountSum = schoolCourseWorkloadList.stream()
                .collect(groupingBy(o -> MapUtil.getLong(o, "courseId"), summingInt(o -> MapUtil.getInt(o, "studentCount"))));
        
        // schoolId, schoolName, courseId, courseName, teacherName, teacherPhone
        List<Map<String, Object>> teacherList = readIntelligentExamInfoService.getTeacherList4ReadIntelligent(params);
        for (Map<String, Object> teacher : teacherList) {
            long schoolId = MapUtil.getLong(teacher, "schoolId");
            long courseId = MapUtil.getLong(teacher, "courseId");
            Integer workloadPerTeacher = Optional.ofNullable(schoolId2courseId2workloadPerTeacher)
                    .map(o -> o.get(schoolId))
                    .map(o -> o.get(courseId))
                    .orElse(null);
            if (workloadPerTeacher == null) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未提供" + teacher.get("teacherName") + "老师的学校课程单个老师工作设置量");
            }
            teacher.put("schoolName", MapUtil.getTrim(teacher, "schoolName"));
            teacher.put("teacherName", MapUtil.getTrim(teacher, "teacherName"));
            teacher.put("courseName", MapUtil.getTrim(teacher, "courseName"));
            teacher.put("teacherPhone", MapUtil.getTrim(teacher, "teacherPhone"));
            teacher.put("workload", workloadPerTeacher);
        }
        
        // ---------------------------------------------------------------------------------- readBlock
        // readBlockId, blockName, courseId, courseName, blockSort
        List<Map<String, Object>> readBlockList = paperReadArrangeService.getPaperReadArrangeForIntelligent(params);
        for (Map<String, Object> readBlock : readBlockList) {
            long readBlockId = MapUtil.getLong(readBlock, "readBlockId");
            long courseId = MapUtil.getLong(readBlock, "courseId");
            Double readBlockWorkload = readBlockId2workload.get(readBlockId);
            if (readBlockWorkload == null) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未提供题块的工作量:readBlockId=" + readBlockId);
            }
            Integer studentCountSum = courseId2studentCountSum.get(courseId);
            if (studentCountSum == null) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未提供课程的学生设置人数:courseId=" + courseId);
            }
            readBlock.put("readBlockDisplay", readBlockId + "-" + MapUtil.getString(readBlock, "blockName"));
            readBlock.put("courseName", MapUtil.getTrim(readBlock, "courseName"));
            readBlock.put("readBlockWorkload", readBlockWorkload);
            readBlock.put("studentCount", studentCountSum);
        }
        
        // ---------------------------------------------------------------------------------- 填充模板数据
        // 按课程-学校排序方便操作
        teacherList.sort(Comparator
                .<Map<String, Object>, Long>comparing(o -> MapUtil.getLong(o, "courseId"))
                .thenComparing(o -> MapUtil.getLong(o, "schoolId"))
        );
        // 按课程-sort-题块id排序方便操作
        readBlockList.sort(Comparator
                .<Map<String, Object>, Long>comparing(o -> MapUtil.getLong(o, "courseId"))
                .thenComparing(o -> MapUtil.getInt(o, "blockSort"))
                .thenComparing(o -> MapUtil.getLong(o, "readBlockId"))
        );
        
        Map<String, Object> data = new HashMap<>(3);
        // schoolName, teacherName, courseName, teacherPhone, workload
        data.put("teacherList", teacherList);
        // readBlockDisplay, courseName, readBlockWorkload, studentCount
        data.put("readBlockList", readBlockList);
        return new ExportExcelTemplate("read-intelligent/examMarkIntelligent.xls", data)
                .exportToFileStorage("阅卷老师分配表", null);
    }
}
