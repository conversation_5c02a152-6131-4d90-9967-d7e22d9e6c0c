package com.dongni.exam.mark.bean.dto;

import java.util.List;

/**
 * @author: hzw
 * @date: 2024/4/9
 * @description:
 */
public class ExamPaperQnScoreDTO {

	private Long examId;

	private Long paperId;

	private List<QnScore> qnScores;

	public static class QnScore {

		private Integer questionNumber;

		private List<RecognitionValueScore> recognitionValueScores;

		public static class RecognitionValueScore {

			private String recognitionValue;

			private double score;

			public String getRecognitionValue() {
				return recognitionValue;
			}

			public RecognitionValueScore setRecognitionValue(String recognitionValue) {
				this.recognitionValue = recognitionValue;
				return this;
			}

			public double getScore() {
				return score;
			}

			public RecognitionValueScore setScore(double score) {
				this.score = score;
				return this;
			}
		}

		public Integer getQuestionNumber() {
			return questionNumber;
		}

		public QnScore setQuestionNumber(Integer questionNumber) {
			this.questionNumber = questionNumber;
			return this;
		}

		public List<RecognitionValueScore> getRecognitionValueScores() {
			return recognitionValueScores;
		}

		public QnScore setRecognitionValueScores(
			List<RecognitionValueScore> recognitionValueScores) {
			this.recognitionValueScores = recognitionValueScores;
			return this;
		}
	}

	public Long getExamId() {
		return examId;
	}

	public ExamPaperQnScoreDTO setExamId(Long examId) {
		this.examId = examId;
		return this;
	}

	public Long getPaperId() {
		return paperId;
	}

	public ExamPaperQnScoreDTO setPaperId(Long paperId) {
		this.paperId = paperId;
		return this;
	}

	public List<QnScore> getQnScores() {
		return qnScores;
	}

	public ExamPaperQnScoreDTO setQnScores(List<QnScore> qnScores) {
		this.qnScores = qnScores;
		return this;
	}
}
