package com.dongni.exam.mark.util;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/16.
 *
 * 数值分段，把一个集合按照分数段统计
 */
public class NumericalSubsectionUtil {

    /**
     * 一段一段切割，从 步长 开始， 最后加上满分
     * @param subsection 每段数值
     * @param fullMark 满分分数
     * @return 切割段
     */
    public static List<Float> splitScoreSection(float subsection, float fullMark){
        return splitScoreSection(subsection,fullMark,false);
    }

    /**
     * 一段一段切割，从 0 开始，最后加上满分
     * @param subsection 每段数值
     * @param fullMark 满分分数
     * @return 切割段
     */
    public static List<Float> splitScoreSection(float subsection, float fullMark, boolean isNeedZero){
        List<Float> section = new ArrayList<>();
        float close = isNeedZero ? 0 : subsection;
        while (fullMark >= close){
            section.add(close);
            close = close + subsection;
        }
        if(fullMark < close && fullMark > (close-subsection)){
            section.add(fullMark);
        }

        return section;
    }


    /**
     * 对成绩按照自定义的分数段进行分组计算
     * @param scores 分数集合
     * @param section 分数段集合
     * @param fullMark 满分分数
     * @return 分组结果
     */
    public static Map<String,Object> processScoreSubsection(List<Float> scores, List<Float> section, float fullMark){
        Map<String,Object> all = new HashMap<>();
        int[] count = new int[section.size()];
        all.put("section",section);
        all.put("count",count);
        Collections.sort(scores,(o1, o2) -> {
            float rs = o1 - o2;
            if (rs > 0){
                return 1;
            }
            if(rs == 0){
                return 0;
            }
            return -1;
        });

        int i = 0;
        float close;
        for (Float score : scores){
            close = section.get(i);
            if(score < close){
                count[i] = count[i] + 1;
            }else if(close == fullMark){
                count[i] = count[i] + 1;
            }else{
                do {
                    i++;
                    close = section.get(i);
                }while(score >= close && close != fullMark);
                count[i] = count[i] + 1;
            }
        }

        return all;
    }

    /**
     * 对成绩按照自定义的分数段进行分组计算,这里的分数段必须完全包含分数的值，因为是全等于才会加1
     * @param scores 分数集合
     * @param section 分数段集合
     * @return 分组结果
     */
    public static Map<String,Object> processScoreSubsection(List<Float> scores, List<Float> section){
        Map<String,Object> all = new HashMap<>();
        int[] count = new int[section.size()];
        all.put("section",section);
        all.put("count",count);

        for (float score : scores){
            for (int i = 0; i < section.size(); i++){
                float close = section.get(i);
                if (i == section.size() - 1) close += 1;
                float open = i == 0 ? 0 : section.get(i-1);
                if (open <= score && score < close) {
                    count[i] = count[i] + 1;
                }
            }
        }

        return all;
    }





}
