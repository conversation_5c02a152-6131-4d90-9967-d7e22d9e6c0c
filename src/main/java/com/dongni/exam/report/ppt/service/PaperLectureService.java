package com.dongni.exam.report.ppt.service;

import com.dongni.analysis.view.monitor.service.ExamClassQuestionStatService;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.common.http.HttpTimeout;
import com.dongni.common.http.RestService;
import com.dongni.common.report.ppt.ExportPPT;
import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageRequest;
import com.dongni.commons.filestorage.service.IFileStorageOperateService;
import com.dongni.commons.utils.file.CompressFileUtils;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.download.service.ExamReportDownloadService;
import com.dongni.exam.report.item.ExcellentMistakeCommentExportService;
import com.dongni.exam.report.pdf.PDFRenderHelper;
import com.dongni.exam.report.ppt.util.PPTChartUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by Heweipo on 2020/5/6.
 *
 * 试卷讲解单PPT生成
 */
@Service
public class PaperLectureService {

    // 日志记录
    private static final Logger log = LogManager.getLogger(PaperLectureService.class);

    @Autowired
    private RestService restService;
    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private ExamClassQuestionStatService examClassQuestionStatService;
    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;
    @Autowired
    private ExamReportDownloadService examReportDownloadService;
    @Autowired
    private ExcellentMistakeCommentExportService excellentMistakeCommentExportService;
    @Autowired
    private PDFRenderHelper pdfRenderHelper;
    @Autowired
    private IFileStorageOperateService fileStorageOperateService;

    public String exportPPTReport(Map<String,Object> params){

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .isValidId("courseId")
                .isValidId("paperId")
                .isValidId("schoolId")
                .isValidId("classId")
                .verify();

        // 基本信息
        Map<String,Object> rs = examRepository.selectOne("PaperLectureMapper.getExam",params);
        if(MapUtils.isEmpty(rs)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"考试不存在或已被删除");
        }
        Map<String,Object> course = commonCourseService.getCourseById(params);
        if(MapUtils.isEmpty(course)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"课程参数不正确");
        }
        params.put("classIds",params.get("classId"));
        List<Map<String,Object>> cs = commonClassService.getClassInfo(params);
        if(CollectionUtils.isEmpty(cs)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"班级参数不正确");
        }

        String fileName = rs.get("examName")+"("+course.get("courseName")+"、"+cs.get(0).get("className")+")";
        log.info("生成{}PPT开始执行",fileName);

        // 获取试题数量
        List<Document> qs = examClassQuestionStatService.getExamClassQuestion(params);
        if (qs != null) {
            // 过滤到作文题，前端不会渲染这些题目返回的数据是空的。
            Set<Integer> filterQuestionType = Stream.of(8, 21, 66).collect(Collectors.toSet());
            qs.removeIf(i -> filterQuestionType.contains(MapUtil.getInt(i, "questionType")));
        }
        if(CollectionUtils.isEmpty(qs)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"未找到试卷的试题信息");
        }

        // 接口调用
        Map<String,Object> p = new HashMap<>();
        p.put("userId",params.get("userId"));
        p.put("userName",params.get("userName"));
        p.put("userType",params.get("userType"));
        p.put("examId",params.get("examId"));
        p.put("statId",params.get("statId").toString());
        p.put("courseId",params.get("courseId"));
        p.put("paperId",params.get("paperId"));
        p.put("schoolId",params.get("schoolId").toString());
        p.put("classId",params.get("classId"));
        p.put("token",params.get("token"));

        int template = 8;
        int size = qs.size() / template + ((qs.size() % template) == 0 ? 0 : 1);
        List<String> urls = new ArrayList<>();
        for (int i = 0; i < size; i++){
            Map<String,Object> np = new HashMap<>(p);
            np.put("type", i == 0 ? "first" : "second");
            np.put("questionData", qs.subList(i*template, Math.min((i + 1) * template, qs.size())));
            List<String> rr = HttpTimeout.execute(HttpTimeout.of(1000 * 60 * 3),
                    () -> PPTChartUtil.getChartUrls(restService, np));
            if(CollectionUtils.isEmpty(rr)){
                log.error("异步请求NodeJS渲染PPT图片出未正常返回数据，需手动重试");
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"幻灯片样式渲染失败，请重试");
            }
            urls.addAll(rr);
        }

        // PPT 生成
        ExportPPT ppt = new ExportPPT();
        ppt.addSlides(urls);

        // 不带type参数，只导出PPT
        if (!ObjectUtil.isNotEmptyCollections(params.get("type"))) {
            return ppt.export(fileName);
        }
        // 带了type参数，需要导出优秀作答、典型错误或批注
        else {
            return FileStorageTemplate.put(fileStoragePut -> {
                String rootPath = fileStoragePut.getRootPath();
                String itemRootPath = rootPath + "item" + File.separator;
                // 作答情况
                params.put("rootPath", itemRootPath);
                excellentMistakeCommentExportService.zipNotPut(params);
                // ppt
                ppt.save(fileName, itemRootPath);
                // 打包上传
                File zip = new File(rootPath + "习题讲解单.zip");
                CompressFileUtils.zip(new File(itemRootPath), zip);
                fileStoragePut.setAutoExpire(true);
                fileStoragePut.setLocalFile(zip);
            });
        }
    }

    public String exportPDFReport(Map<String, Object> params) {

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("schoolId")
                .isValidId("classId")
                .isValidId("courseId")
                .isNumeric("statId")
                .verify();

        // 基本信息
        Map<String, Object> rs = examRepository.selectOne("PaperLectureMapper.getExam", params);
        if (MapUtils.isEmpty(rs)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试不存在或已被删除");
        }
        Map<String, Object> course = commonCourseService.getCourseById(params);
        if (MapUtils.isEmpty(course)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "课程参数不正确");
        }
        params.put("classIds", params.get("classId"));
        List<Map<String, Object>> cs = commonClassService.getClassInfo(params);
        if (CollectionUtils.isEmpty(cs)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "班级参数不正确");
        }

        String fileName = rs.get("examName") + "-" + cs.get(0).get("className") + "-试题讲解.pdf";
        params.put("fileName", fileName);

        String pdfUrl = pdfRenderHelper.getPdfUrl("/api/export/paper-explain/pdf/render", params);

        // 不带type参数，只导出PPT
        if (!ObjectUtil.isNotEmptyCollections(params.get("type"))) {
            return pdfUrl;
        }
        // 带了type参数，需要导出优秀作答、典型错误或批注
        else {
            return FileStorageTemplate.put(fileStoragePut -> {
                String rootPath = fileStoragePut.getRootPath();
                String itemRootPath = rootPath + "item" + File.separator;
                // 作答情况
                params.put("rootPath", itemRootPath);
                excellentMistakeCommentExportService.zipNotPut(params);
                copyFile(pdfUrl, itemRootPath, fileName);
                // 打包上传
                File zip = new File(rootPath + "习题讲解单.zip");
                CompressFileUtils.zip(new File(itemRootPath), zip);
                fileStoragePut.setAutoExpire(true);
                fileStoragePut.setLocalFile(zip);
            });
        }
    }

    private void copyFile(String pdfUrl, String rootPath, String fileName) {
        FileStorageRequest fileStorageRequest = new FileStorageRequest();
        fileStorageRequest.setFilePath(pdfUrl);
        fileStorageRequest.setLocalFilePath(rootPath + fileName);
        fileStorageRequest.setFileName(fileName);
        fileStorageOperateService.get(fileStorageRequest);
    }

    public Map<String,Object> exportDownloadCentre(Map<String, Object> params) {
        //生成下载记录
        Map<String,Object> result = new HashMap<>();
        // 基本信息
        Map<String,Object> rs = examRepository.selectOne("PaperLectureMapper.getExam",params);
        if(MapUtils.isEmpty(rs)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"考试不存在或已被删除");
        }
        Map<String,Object> course = commonCourseService.getCourseById(params);
        if(MapUtils.isEmpty(course)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"课程参数不正确");
        }
        params.put("classIds",params.get("classId"));
        List<Map<String,Object>> cs = commonClassService.getClassInfo(params);
        if(CollectionUtils.isEmpty(cs)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"班级参数不正确");
        }
        String fileName = rs.get("examName")+"("+course.get("courseName")+"、"+cs.get(0).get("className")+")";

        params.put("reportStatus",0);
        params.put("fileName",fileName+".pptx");
        examReportDownloadService.insertDownloadItem(params);
        //异步执行下载任务
        myAsyncConfigurer.getAsyncExecutor().execute(()->dealAsyncDownloadTask(params));
        //返回记录id
        result.put("examReportDownloadId",params.get("examReportDownloadId"));
        result.put("fileName",fileName+".pptx");
        return result;
    }

    private void dealAsyncDownloadTask(Map<String,Object> params) {
        String path;
        try {
            path = exportPPTReport(params);
            params.put("reportStatus",1);
            params.put("fileUrl",path);
        }catch (Exception e){
            params.put("reportStatus",2);
            params.put("failMessage",e.getMessage());
            log.error("生成报表出现错误：{}",e.getMessage(),e);
        }finally {
            examReportDownloadService.updateDownloadItem(params);
        }
    }
}
