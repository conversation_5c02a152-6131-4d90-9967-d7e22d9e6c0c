package com.dongni.exam.report.word.bean;

import com.hqjl.bi.annotations.BIColumn;

/**
 * 各校一分四率分析
 */
public class MemberScoreRate {

	@BIColumn("当前对象")
	private String schoolName;
	@BIColumn("实参人数")
	private Integer participationNumber;
	@BIColumn(value = "均分", format = "0.00")
	private Double averageScore;
	@BIColumn(value = "超均率", format = "0.00%")
	private Double averageSubtractionRate;
	@BIColumn(value = "比均率", format = "0.00%")
	private Double avgRate;
	@BIColumn("优秀")
	private Integer excellentNumber;
	@BIColumn(value = "优秀率", format = "0.00%")
	private Double excellentRate;
	@BIColumn("良好")
	private Integer goodNumber;
	@BIColumn(value = "良好率", format = "0.00%")
	private Double goodRate;
	@BIColumn("及格")
	private Integer passNumber;
	@BIColumn(value = "及格率", format = "0.00%")
	private Double passRate;
	@BIColumn("低分")
	private Integer lowScoreNumber;
	@BIColumn(value = "低分率", format = "0.00%")
	private Double lowScoreRate;

	public MemberScoreRate(String schoolName, Integer participationNumber, Double averageScore, Double averageSubtractionRate,
		Double avgRate, Integer excellentNumber, Double excellentRate, Integer goodNumber, Double goodRate, Integer passNumber,
		Double passRate, Integer lowScoreNumber, Double lowScoreRate) {
		this.schoolName = schoolName;
		this.participationNumber = participationNumber;
		this.averageScore = averageScore;
		this.averageSubtractionRate = averageSubtractionRate;
		this.avgRate = avgRate;
		this.excellentNumber = excellentNumber;
		this.excellentRate = excellentRate;
		this.goodNumber = goodNumber;
		this.goodRate = goodRate;
		this.passNumber = passNumber;
		this.passRate = passRate;
		this.lowScoreNumber = lowScoreNumber;
		this.lowScoreRate = lowScoreRate;
	}

	public String getSchoolName() {
		return schoolName;
	}

	public Integer getParticipationNumber() {
		return participationNumber;
	}

	public Double getAverageScore() {
		return averageScore;
	}

	public Double getAverageSubtractionRate() {
		return averageSubtractionRate;
	}

	public Double getAvgRate() {
		return avgRate;
	}

	public Integer getExcellentNumber() {
		return excellentNumber;
	}

	public Double getExcellentRate() {
		return excellentRate;
	}

	public Integer getGoodNumber() {
		return goodNumber;
	}

	public Double getGoodRate() {
		return goodRate;
	}

	public Integer getPassNumber() {
		return passNumber;
	}

	public Double getPassRate() {
		return passRate;
	}

	public Integer getLowScoreNumber() {
		return lowScoreNumber;
	}

	public Double getLowScoreRate() {
		return lowScoreRate;
	}
}
