package com.dongni.exam.report.word.service;

import com.dongni.analysis.area.view.exam.service.ExamAreaBoxStatService;
import com.dongni.analysis.area.view.exam.service.ExamAreaCourseLineStatService;
import com.dongni.analysis.area.view.exam.service.ExamAreaQuestionService;
import com.dongni.analysis.area.view.exam.service.ExamAreaScoreSectionService;
import com.dongni.analysis.area.view.exam.service.ExamAreaStatService;
import com.dongni.analysis.view.monitor.service.ExamStatService;
import com.dongni.common.http.RestService;
import com.dongni.common.report.word.AnalysisExportWord;
import com.dongni.common.report.word.BackgroundWordTable;
import com.dongni.common.report.word.DocxFontEnum;
import com.dongni.common.report.word.MultipleTitleWordTable;
import com.dongni.common.report.word.SimpleWordTable;
import com.dongni.common.report.word.WordUtil;
import com.dongni.common.report.word.custom.ResultSectionWordTable;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.report.word.util.WordChartUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.docx4j.wml.Tbl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 学科成绩分析
 * @author: Jianfeng
 * @create: 2019-09-03 10:34
 **/
@Service
public class CourseGradeAnalysisService {

    @Autowired
    private ExamAreaCourseLineStatService examAreaCourseLineStatService;

    @Autowired
    private ExamAreaStatService examAreaStatService;

    @Autowired
    private ExamAreaScoreSectionService examAreaScoreSectionService;

    @Autowired
    private ExamAreaBoxStatService examAreaBoxStatService;

    @Autowired
    private ExamAreaQuestionService examAreaQuestionService;

    @Autowired
    private ExamStatService examStatService;

    @Autowired
    private RestService restService;

    /**
     * 添加学科成绩分析
     *
     * @param word
     * @param params
     */
    public void addCourseGradeAnalysis(AnalysisExportWord word, Map<String, Object> params) {

        word.addPageTitle("第二部分 学科成绩分析");

        word.addContent("学科总分分析，帮助我们从宏观角度了解和发现整体样本和个体样本之间的水平和差异。" +
                        "包括总分的整体平均水平和不同层次，总分与学科的不同层次、分布情况、集中和离散趋势的差异。");

        word.addBreak();

        addArrangementAnalysis(word, params);
        word.addBreak();

        addAchievementDistribution(word, params);
        word.addBreak();

        addFocusAndDispersed(word, params);
        word.addBreak();

        int tempAreaGroupId = Integer.parseInt(params.get("areaGroupId").toString());
        params.remove("areaGroupId");
        addQuestionAnalysis(word, params);
        params.put("areaGroupId", tempAreaGroupId);
    }


    /**
     * 添加不同层次分析
     *
     * @param word
     * @param params
     */
    private void addArrangementAnalysis(AnalysisExportWord word, Map<String, Object> params) {

        word.addContentTitle("1 不同层次分析");

        word.addContent("1.1 单双上线分析");

        params.put("line", "A");
        Map<String, List> lineA = examAreaCourseLine(params);
        params.put("line", "B");
        Map<String, List> lineB = examAreaCourseLine(params);
        List<Map<String,Object>> dataLinA = lineA.get("data");
        List<Map<String,Object>> dataLinB = lineB.get("data");

        for (int i = 0; i < dataLinA.size(); i++) {
            dataLinA.get(i).putAll(dataLinB.get(i));
        }

        List<String> keyLineA = Arrays.asList("parentName", "currentName", "schoolPropertyName", "participationNumber",
                "singleUpA", "doubleUpA", "hitRateA", "contributionRateA");
        List<String> keyLineB = Arrays.asList("parentName", "currentName", "schoolPropertyName", "participationNumber",
                "singleUpB", "doubleUpB", "hitRateB", "contributionRateB");

        MultipleTitleWordTable multipleTitleWordTable = new MultipleTitleWordTable(word.getWordPackage(),Arrays.asList(lineA.get("titleLine"),lineB.get("titleLine")));

        String compareHitRateA = "";
        String compareHitRateB = "";
        String compareContributionRateA = "";
        String compareContributionRateB = "";

        if (CollectionUtils.isNotEmpty(dataLinA) && CollectionUtils.isNotEmpty(dataLinB)) {
            compareHitRateA = dataLinA.get(0).get("hitRateANoFormat").toString();
            compareHitRateB = dataLinB.get(0).get("hitRateBNoFormat").toString();
            compareContributionRateA = dataLinA.get(0).get("contributionRateANoFormat").toString();
            compareContributionRateB = dataLinB.get(0).get("contributionRateBNoFormat").toString();
        }

        String finalCompareHitRateA = compareHitRateA;
        String finalCompareHitRateB = compareHitRateB;
        String finalCompareContributionRateA = compareContributionRateA;
        String finalCompareContributionRateB = compareContributionRateB;
        multipleTitleWordTable.setCellColorRule(((data, row, col, key1, value) -> {
            if (key1.equals("hitRateA") || key1.equals("contributionRateA") || key1.equals("hitRateB") || key1.equals("contributionRateB")) {
                //总体不参与比较
                if (row==0){
                    return false;
                }
                //公办民办不参与比较
                if (!ObjectUtil.isBlank(data.get("schoolProperty")) && ObjectUtil.isBlank(data.get("schoolName"))){
                    return false;
                }

                if (key1.equals("hitRateA")) {
                    return WordUtil.compare(finalCompareHitRateA, value);
                } else if (key1.equals("hitRateB")) {
                    return WordUtil.compare(finalCompareHitRateB, value);
                } else if (key1.equals("contributionRateA")) {
                    return WordUtil.compare(finalCompareContributionRateA, value);
                } else if (key1.equals("contributionRateB")) {
                    return WordUtil.compare(finalCompareContributionRateB, value);
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }));


        List<Tbl> tbl = multipleTitleWordTable.generate(Arrays.asList(keyLineA, keyLineB), dataLinA);
        word.addTables(tbl);

        word.addBreak();

        word.addContent("备注：以A线为例说明计算公式",0,DocxFontEnum.FIVE);
        word.addContent("第一步：确定A线上线群体",0,DocxFontEnum.FIVE);
        word.addContent("第二步：有效系数=A线分数线/（A线学生总分和/A线学生人数）",0,DocxFontEnum.FIVE);
        word.addContent("第三步：单科有效分=（A线学生单科分数和/A线学生人数）*有效系数",0,DocxFontEnum.FIVE);
        word.addContent("第四步：贡献率=（双上线人数/总分上线人数）*100%",0,DocxFontEnum.FIVE);
        word.addContent("第五步：命中率=（双上线人数/单上线人数）*100%",0,DocxFontEnum.FIVE);
        word.addContent("单上线人数：单科上线人数     双上线人数：单科和总分都上线的人数",0,DocxFontEnum.FIVE);

        word.addBreak();

        word.addContent("1.2 一分四率分析");
        params.put("isShow", true);
        Map<String, Object> map=examAreaStatService.getExamArea(params);
        List<Document> scoreSectionList=(List<Document>)map.get("list");
        examStatService.dealScoreSection(scoreSectionList);
        List<Map<String, Object>> examArea = new ArrayList<>();
        scoreSectionList.forEach(a ->{
            List<Map<String, Object>> courseScoreSelectionList = a.get("course", Collections.emptyList());
            if(courseScoreSelectionList.isEmpty()){
                return;
            }
            if(Objects.equals(MapUtils.getInteger(a,"totalStudent"),0)){
                return;
            }

            Map<String, Object> course = courseScoreSelectionList.get(0);
            if (StringUtils.isBlank(MapUtils.getString(course, "passRate"))) {
                return;
            }
            String schoolProperty = ObjectUtil.isValueEquals(a.get("schoolProperty"), 1) ? "公办" : "民办";
            course.put("type", a.get("schoolName") == null ? a.get("areaName"): schoolProperty);
            course.put("parentName", a.get("parentName"));
            course.put("currentName", a.get("currentName"));
            course.put("schoolPropertyName", a.get("schoolPropertyName"));
            examArea.add(course);
        });

        word.addBreak();
        word.addContent("区县");

        examArea.forEach(a->{
            if (a.get("schoolName") == null) {
                a.put("type", a.get("areaName"));
            }
            BigDecimal passRate = new BigDecimal(a.get("passRate").toString());
            a.put("noPassRate", new BigDecimal("1").subtract(passRate));
        });

        List<Map<String, Object>> area = examArea.stream()
                .filter(a -> a.get("schoolName") == null
                        &&!ObjectUtil.isValueEquals(a.get("areaName"),"公办")
                        &&!ObjectUtil.isValueEquals(a.get("areaName"),"民办"))
                .collect(Collectors.toList());

        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", params.get("userId"));
        queryParams.put("userName", params.get("userName"));
        queryParams.put("userType", params.get("userType"));
        queryParams.put("chartData", area);
        queryParams.put("type", "fourRateArea");
        word.addImage(WordChartUtil.getChartUrl(restService, queryParams));

        word.addContent("学校");

        List<Map<String, Object>> school = examArea.stream().filter(a -> a.get("schoolName") != null).collect(Collectors.toList());

        queryParams.put("userId", params.get("userId"));
        queryParams.put("userName", params.get("userName"));
        queryParams.put("userType", params.get("userType"));
        queryParams.put("chartData", school);
        queryParams.put("type", "fourRateSchool");

        word.addImage(WordChartUtil.getChartUrl(restService, queryParams));
        word.addBreak();

        examArea.forEach(a->{
            WordUtil.formatScore(a,"averageScore");
            WordUtil.formatRate(a,"averageSubtractionRate");
            WordUtil.formatRate(a,"avgRate");
            WordUtil.formatRate(a,"excellentRate");
            WordUtil.formatRate(a,"goodRate");
            WordUtil.formatRate(a,"passRate");
            WordUtil.formatRate(a,"lowScoreRate");
        });

        List<String> titleRate = Arrays.asList("上级对象", "当前对象", "类别","实参人数",
                "均分","超均率",
                "优秀","优秀率",
                "良好","良好率",
                "及格","及格率",
                "低分","低分率");
        SimpleWordTable wordTableRate = new SimpleWordTable(word.getWordPackage(),titleRate);

        word.addTable(wordTableRate.generate(Arrays.asList("parentName", "currentName", "schoolPropertyName","participationNumber",
                "averageScore","averageSubtractionRate", /*"avgRate",*/
                "excellentNumber","excellentRate",
                "goodNumber","goodRate",
                "passNumber","passRate",
                "lowScoreNumber","lowScoreRate"),
                examArea));

        word.addBreak();

        Map<String, Object> examConfig = (Map<String, Object>) params.get("examConfig");
        Object scoreRate = examConfig.get("scoreRate");
        List<Map<String, Object>> scoreRateCourse;
        boolean enableRanking = false;
        if (scoreRate instanceof Document) {
            scoreRateCourse = MapUtil.getCast((Document) scoreRate, "course");
            enableRanking = MapUtil.getBoolean((Document) scoreRate, "enableRanking");
        } else {
            scoreRateCourse = (List<Map<String, Object>>) scoreRate;
        }
        Map<String, Object> rateConfig = scoreRateCourse.stream()
                .filter(item -> ObjectUtil.isValueEquals(item.get("courseId"), params.get("courseId")))
                .findFirst().get();

        String scoreRateTermExcellent = enableRanking ? "排名前" : "得分率≥";
        String scoreRateTermLow = enableRanking ? "排名后" : "得分率<";
        word.addContent("备注，各学科四率标准如下：",0,DocxFontEnum.FIVE);
        word.addContent("优秀率：" + scoreRateTermExcellent + rateConfig.get("excellentRate")
                + "% ；良好率：" + scoreRateTermExcellent + rateConfig.get("goodRate") + "% ；",0,DocxFontEnum.FIVE);
        word.addContent("及格率：" + scoreRateTermExcellent + rateConfig.get("passRate")
                + "% ；低分率：" + scoreRateTermLow + rateConfig.get("lowScoreRate") + "% ；",0,DocxFontEnum.FIVE);
    }

    /**
    * @Description:
    * @Param: line(A;B)
    */
    private Map<String,List> examAreaCourseLine(Map<String, Object> params) {

        List<Map<String, Object>> examAreaCourseLineA = (List<Map<String, Object>>) examAreaCourseLineStatService.getExamAreaCourseLine(params).get("list");

        String lineX = "line" + params.get("line");

        final double[] score = {0};
        final String[] courseName = {""};
        List<Map<String, Object>> data = new ArrayList<>();
        examAreaCourseLineA.forEach(a->{
            Map<String, Object> dataMap = new HashMap<>();
            String schoolProperty = ObjectUtil.isValueEquals(a.get("schoolProperty"), 1) ? "公办" : "民办";
            dataMap.put("type", a.get("schoolName") == null ? a.get("areaName"): schoolProperty);
            dataMap.put("schoolName", a.get("schoolName"));
            dataMap.put("schoolProperty", a.get("schoolProperty"));
            dataMap.put("parentName", a.get("parentName"));
            dataMap.put("currentName", a.get("currentName"));
            dataMap.put("schoolPropertyName", a.get("schoolPropertyName"));
            dataMap.put("participationNumber", a.get("participationNumber"));

            Map<Object, List<Map<String, Object>>> lineCourseMap = ((List<Map<String, Object>>) a.get("course")).stream()
                    .collect(Collectors.groupingBy(b -> b.get("courseId")));

            List<Map<String, Object>> courseLineList = lineCourseMap.getOrDefault(Long.parseLong(params.get("courseId").toString()),
              Collections.emptyList());
            if (courseLineList.isEmpty()) {
                return;
            }
            //noinspection unchecked
            Map<String, Object> line = (Map<String, Object>) courseLineList.get(0).getOrDefault(lineX, Collections.emptyMap());
            if (line.isEmpty()) {
                return;
            }

            score[0] = Double.parseDouble(line.get("score").toString());
            courseName[0] = lineCourseMap.get(Long.parseLong(params.get("courseId").toString())).get(0).get("courseName").toString();

            dataMap.put("hitRate" + params.get("line") + "NoFormat", line.get("hitRate"));
            dataMap.put("contributionRate" + params.get("line") + "NoFormat", line.get("contributionRate"));

            dataMap.put("singleUp" + params.get("line"), line.get("singleUp"));
            dataMap.put("doubleUp" + params.get("line"), line.get("doubleUp"));
            dataMap.put("hitRate" + params.get("line"), WordUtil.formatRate(line.get("hitRate").toString()));
            dataMap.put("contributionRate" + params.get("line"), WordUtil.formatRate(line.get("contributionRate")));

            data.add(dataMap);
        });

        List<String> titleLine = Arrays.asList("上级对象", "当前对象", "类别","实参人数",
                params.get("line") + "线"+ courseName[0]+"("+ WordUtil.formatScore(score[0])+"分)"+"|单上线|双上线|命中率|贡献率");

        Map<String, List> rs = new HashMap<>();
        rs.put("titleLine", titleLine);
        rs.put("data", data);
        return rs;
    }


    /**
     * 添加成绩分布
     *
     * @param word
     * @param params
     */
    private void addAchievementDistribution(AnalysisExportWord word, Map<String, Object> params) {

        word.addContentTitle("2 成绩分布");

        word.addBreak();

        Map<String, Object> map = examAreaScoreSectionService.getExamAreaCourseScoreSection(params);

        List<Document> scoreSectionList = (List<Document>) map.get("list");
        examStatService.dealScoreSection(scoreSectionList);

        scoreSectionList.forEach(a->{
            String schoolProperty = ObjectUtil.isValueEquals(a.get("schoolProperty"), 1) ? "公办" : "民办";
            a.put("areaName", a.get("schoolName") == null ? a.get("areaName"): schoolProperty);
        });

        ResultSectionWordTable wordTable = new ResultSectionWordTable(word.getWordPackage(),Arrays.asList(""));
        word.addTable(wordTable.generate(Arrays.asList(""),new ArrayList<>(scoreSectionList)));

    }


    /**
     * 添加集中与离散趋势
     *
     * @param word
     * @param params
     */
    private void addFocusAndDispersed(AnalysisExportWord word, Map<String, Object> params) {

        word.addContentTitle("3 集中与离散趋势");

        word.addContent("3.1 均分、标准差等分析");
        word.addContent("数据集中趋势的描述： 平均分、中位数和众数；数据离散趋势的描述：最高分、最低分、极差和标准差。");

        params.put("isShow", true);
        Map<String, Object> map=examAreaStatService.getExamArea(params);
        List<Document> scoreSectionList=(List<Document>)map.get("list");
        examStatService.dealScoreSection(scoreSectionList);
        List<Map<String, Object>> examArea = new ArrayList<>();
        scoreSectionList.forEach(a ->{
            List<Map<String, Object>> courseScoreSelectionList = (List<Map<String, Object>>) a.get("course");
            if(courseScoreSelectionList.isEmpty()){
                return;
            }
            if(Objects.equals(MapUtils.getInteger(a,"totalStudent"),0)){
                return;
            }
            Map<String, Object> course = courseScoreSelectionList.get(0);
            String schoolProperty = ObjectUtil.isValueEquals(a.get("schoolProperty"), 1) ? "公办" : "民办";
            course.put("type", a.get("schoolName") == null ? a.get("areaName"): schoolProperty);
            course.put("schoolProperty", a.get("schoolProperty"));
            course.put("parentName", a.get("parentName"));
            course.put("currentName", a.get("currentName"));
            course.put("schoolPropertyName", a.get("schoolPropertyName"));
            examArea.add(course);
        });

        String compareScoreStr = "";
        if (CollectionUtils.isNotEmpty(examArea)) {
            compareScoreStr = WordUtil.formatScore(examArea.get(0).get("averageScore")).toString();
        }

        examArea.forEach(a->{
            WordUtil.formatScore(a,"averageScore");
            WordUtil.formatScore(a,"standardDeviation");
            WordUtil.formatScore(a,"highestScore");
            WordUtil.formatScore(a,"lowestScore");
            if (!ObjectUtil.isBlank(a.get("modeValue"))) {
                Arrays.asList(a.get("modeValue").toString().split(",")).stream()
                        .map(b -> WordUtil.formatScore(b).toString())
                        .collect(Collectors.joining(","));
            }

            WordUtil.formatScore(a,"medianValue");

        });

        List<String> titleFocus = Arrays.asList("上级对象", "当前对象", "类别","实参人数",
                "最高分","最低分","均分","标准差","众数","中位数");

        BackgroundWordTable wordTableFocus = new BackgroundWordTable(word.getWordPackage(), titleFocus);
        String finalCompareScoreStr = compareScoreStr;
        wordTableFocus.setCellColorRule(((data, row, col, key1, value) -> {
            if (key1.equals("averageScore")) {
                //总体不参与比较
                if (row==0){
                    return false;
                }
                //公办民办不参与比较
                if (!ObjectUtil.isBlank(data.get("schoolProperty")) && ObjectUtil.isBlank(data.get("schoolName"))){
                    return false;
                }

                String valueStr = value.toString();
                if (valueStr.compareTo(finalCompareScoreStr) > 0) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }));

        word.addTable(wordTableFocus.generate(Arrays.asList("parentName", "currentName", "schoolPropertyName","participationNumber",
                "highestScore","lowestScore","averageScore","standardDeviation","modeValue","medianValue"),
                examArea));

        word.addBreak();

        word.addContent("3.2 教学箱体图");

        List<Map<String,Object>> examAreaSchoolBox = (List<Map<String, Object>>) examAreaBoxStatService.getExamAreaSchoolBox(params).get("list");
        List<Map<String,Object>> schoolList = examAreaSchoolBox.stream()
                .filter(s->!ObjectUtil.isBlank(s.get("schoolName")))
                .collect(Collectors.toList());
        if (!examAreaSchoolBox.isEmpty()) {
            schoolList.add(0, examAreaSchoolBox.get(0));
        }


        // 九个学校一张图
        int pageSiz = 9;
        if(!schoolList.isEmpty()){
            Map<String, Object> total = schoolList.remove(0);
            for (int currentIndex = 0; currentIndex < schoolList.size(); currentIndex += pageSiz) {
                List<Map<String, Object>> collect = schoolList.stream()
                  .skip(currentIndex).limit(pageSiz).collect(Collectors.toList());
                collect.add(0, total);

                Map<String, Object> queryParams = new HashMap<>();
                queryParams.put("chartData", collect);
                queryParams.put("type", "teachBox");
                queryParams.put("userId", params.get("userId"));
                queryParams.put("userName", params.get("userName"));
                queryParams.put("userType", params.get("userType"));
                word.addImage(WordChartUtil.getChartUrl(restService, queryParams));
            }
        }


        examAreaSchoolBox.forEach(a->{
            String schoolProperty = ObjectUtil.isValueEquals(a.get("schoolProperty"), 1) ? "公办" : "民办";
            a.put("type", a.get("schoolName") == null ? a.get("areaName"): schoolProperty);
            WordUtil.formatScore(a,"averageScore");
            WordUtil.formatScore(a,"standardDeviation");
            WordUtil.formatScore(a,"medianValue");
            WordUtil.formatScore(a,"upperQuartile");
            WordUtil.formatScore(a,"lowerQuartile");
            WordUtil.formatScore(a,"quartileHighestScore");
            WordUtil.formatScore(a,"quartileLowestScore");
            WordUtil.formatScore(a,"range");
            WordUtil.formatScore(a,"medianValue");

        });

        List<String> titleBox = Arrays.asList("上级对象", "当前对象", "类别",
                "满分","平均分","标准差",
                "最高分","最低分","极差","中位数",
                "上四分位","下四分位");

        SimpleWordTable wordTableBox = new SimpleWordTable(word.getWordPackage(),titleBox);

        word.addTable(wordTableBox.generate(Arrays.asList("parentName", "currentName", "schoolPropertyName",
                "fullMark","averageScore","standardDeviation",
                "quartileHighestScore","quartileLowestScore","range","medianValue",
                "upperQuartile","lowerQuartile"),
                examAreaSchoolBox));
    }


    /**
     * 添加试题分析
     *
     * @param word
     * @param params
     */
    private void addQuestionAnalysis(AnalysisExportWord word, Map<String, Object> params) {

        word.addContentTitle("4 试题分析");

        word.addContent("4.1 题型均分分析");

        List<Map<String, Object>> dataQuestionType = new ArrayList<>();
        Set<String> questionTypeAndScoreValue = new LinkedHashSet<>();
        List<String> questionType = Lists.newArrayList();
        List<Map<String, Object>> examAreaQuestionType = (List<Map<String, Object>>) examAreaQuestionService.getExamAreaQuestionType(params).get("list");
        examAreaQuestionType.forEach(a->{
            Map<String, Object> dataQuestionTypeMap = new HashMap<>();
            String schoolProperty = ObjectUtil.isValueEquals(a.get("schoolProperty"), 1) ? "公办" : "民办";
            dataQuestionTypeMap.put("type", a.get("schoolName") == null ? a.get("areaName"): schoolProperty);
            dataQuestionTypeMap.put("schoolName", a.get("schoolName"));
            dataQuestionTypeMap.put("parentName", a.get("parentName"));
            dataQuestionTypeMap.put("currentName", a.get("currentName"));
            dataQuestionTypeMap.put("schoolPropertyName", a.get("schoolPropertyName"));
            dataQuestionTypeMap.put("总分-averageScore", WordUtil.formatScore(a.get("averageScore")));
            dataQuestionTypeMap.put("总分-averageRate", WordUtil.formatRate(a.get("averageRate")));

            List<Map<String,Object>> questionTypeList = (List<Map<String, Object>>) a.get("questionType");

            questionTypeList.forEach(b->{
//                dataQuestionTypeMap.put(b.get("questionTypeName").toString(), WordUtil.formatScore(b.get("averageScore")));
                questionTypeAndScoreValue.add(b.get("questionTypeName") + "|均分|得分率|排名");
                dataQuestionTypeMap.put(b.get("questionTypeName").toString() + "-averageScore" , WordUtil.formatScore(b.get("averageScore")));
                dataQuestionTypeMap.put(b.get("questionTypeName").toString() + "-scoreRate" , WordUtil.formatRate(b.get("scoreRate")));
                // 只有学校需要排名数据
                if (a.containsKey("schoolId")) {
                    dataQuestionTypeMap.put(b.get("questionTypeName").toString() + "-ranking", b.get("ranking"));
                }
//                questionType.add(b.get("questionTypeName").toString());
                questionType.add(b.get("questionTypeName").toString() + "-averageScore");
                questionType.add(b.get("questionTypeName").toString() + "-scoreRate");
                questionType.add(b.get("questionTypeName").toString() + "-ranking");
            });
            dataQuestionType.add(dataQuestionTypeMap);
        });

        List<String> titleQuestionType = new ArrayList<>(Arrays.asList("上级对象", "当前对象", "类别"));

        titleQuestionType.addAll(questionTypeAndScoreValue);

        // 添加总分
        titleQuestionType.add("总分|均分|得分率");

        SimpleWordTable wordTableQuestionType = new SimpleWordTable(word.getWordPackage(),titleQuestionType);

        List<String> keyQuestionType = new ArrayList<>(Arrays.asList("parentName", "currentName", "schoolPropertyName"));
        keyQuestionType.addAll(questionType.stream().distinct().collect(Collectors.toList()));
        keyQuestionType.add("总分-averageScore");
        keyQuestionType.add("总分-averageRate");

        word.addTable(wordTableQuestionType.generate(keyQuestionType,dataQuestionType));

        word.addBreak();

        word.addContent("4.2 小题均分分析");

        List<Map<String,Object>> examAreaQuestionList = (List<Map<String, Object>>) examAreaQuestionService.getExamAreaQuestion(params).get("list");
        if (examAreaQuestionList == null || examAreaQuestionList.isEmpty()) {
            return;
        }
        List<String> commonTitle = Arrays.asList("上级对象", "当前对象", "类别");

        int listSize = ((List)examAreaQuestionList.get(0).get("question")).size();
        int pageSize = 9;
        int totalPage = listSize / pageSize + 1;

        for (int i = 0; i < totalPage; i++) {

            List<Map<String, Object>> questionDataList = new ArrayList<>();

            List<String> title = new ArrayList<>();
            List<String> field = new ArrayList<>();
            List<String> structureNumber = new ArrayList<>();
            List<String> questionNumber = new ArrayList<>();

            for (Map<String, Object> map : examAreaQuestionList) {
                Map<String, Object> questionMap = new HashMap<>();
                String schoolProperty = ObjectUtil.isValueEquals(map.get("schoolProperty"), 1) ? "公办" : "民办";
                questionMap.put("parentName"+i, map.get("parentName"));
                questionMap.put("currentName"+i, map.get("currentName"));
                questionMap.put("schoolPropertyName"+i, map.get("schoolPropertyName"));
                questionDataList.add(questionMap);

                List<Map<String,Object>> list = (List) map.get("question");

                list.stream().skip(i * pageSize).limit(pageSize).forEach(a->{
                    questionMap.put(a.get("questionNumber").toString(), WordUtil.formatScore(a.get("averageScore")));

                    structureNumber.add(a.get("structureNumber").toString()+"("+WordUtil.formatScore(a.get("scoreValue"))+")");
                    questionNumber.add(a.get("questionNumber").toString());
                });
            }

            title.addAll(commonTitle);
            title.addAll(structureNumber.stream().distinct().collect(Collectors.toList()));
            field.add("parentName" + i);
            field.add("currentName" + i);
            field.add("schoolPropertyName" + i);
            field.addAll(questionNumber.stream().distinct().collect(Collectors.toList()));

            SimpleWordTable wordTableBox = new SimpleWordTable(word.getWordPackage(),title);

            word.addTable(wordTableBox.generate(field,questionDataList));
        }

    }

}

