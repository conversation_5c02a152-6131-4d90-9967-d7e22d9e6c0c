package com.dongni.exam.report.pdf.controler;

import com.dongni.analysis.security.ExamIdScope;
import com.dongni.analysis.security.ExamSchoolIdScope;
import com.dongni.common.async.MyAsyncExecutor;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DataScopeBy;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.report.pdf.service.ExamReportPdfService;
import com.dongni.exam.report.ppt.service.PaperLectureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

import static com.dongni.basedata.enumeration.UserCategory.*;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/12/4 周三 下午 04:59
 * @Version 1.0.0
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/report/pdf")
public class ExamReportPdfController {
    @Autowired
    private ExamReportPdfService examReportPdfService;
    @Autowired
    public HttpServletRequest request;
    @Autowired
    private MyAsyncExecutor myAsyncExecutor;
    @Autowired
    private PaperLectureService paperLectureService;

    /**
     * 获取考试报告pdf下载链接
     */
    @GetMapping("/download/url/async")
    @DongniRequest(userCategory = {teacher, consultant, maintainer}, operationName = "AI报告获取pdf下载链接")
    @DataScopeBy({ExamIdScope.class, ExamSchoolIdScope.class})
    public Response getDownloadUrl(Map<String, Object> params) {
        params.put("token", request.getHeader("dongni-login"));
        return new Response(
                myAsyncExecutor.execute(request, params, () -> examReportPdfService.getDownloadUrl(params))
        );
    }

    /**
     * @Description: 导出pdf报告
     * @Param: examId paperId courseId statId schoolId classId
     */
    @DongniRequest(privilegeId = {"analysis:paperExplain:exportPDF"},
            userCategory = {teacher, government, consultant, maintainer},
            operationName = "导出pdf报告")
    @DataScopeBy({ExamIdScope.class, ExamSchoolIdScope.class})
    @PostMapping("/export/async")
    public Response exportPPTReportAsync(Map<String, Object> params){
        params.put("token", request.getHeader("dongni-login"));
        return new Response(myAsyncExecutor.execute("导出pdf报告", params,
                () -> paperLectureService.exportPDFReport(params)
        ));
    }
}
