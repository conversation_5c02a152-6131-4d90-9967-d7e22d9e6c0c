package com.dongni.exam.report.pdf;

import com.dongni.common.http.HttpTimeout;
import com.dongni.common.http.RestService;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.exception.DongniException;
import com.dongni.commons.utils.HttpUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.MimeType;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.StringJoiner;

@Component
public class PDFRenderHelper {
	private static final Logger logger = LoggerFactory.getLogger(PDFRenderHelper.class);

	@Autowired
	private RestService restService;

	@Autowired
	private RestTemplate restTemplate;

	@Value("${dongni.node.host:}")
	private String host;
	
	/**
	 * @param reportHost dongnireport报告页(https://www.dongni100.com/dongnireport/index.html)的host(https://www.dongni100.com)
	 *                   允许为空，如果为空，则使用dongni.node.host配置
	 */
	public String getPdfUrl(String fileName, ReportParam reportParam, String reportHost) {
		if(reportParam.examId == 1456113) {
			// 天壹联考，使用天壹网址
			reportHost = "https://tianyi.zhunjiaohuixue.com";
		} else if (StringUtils.isBlank(reportHost)) {
			reportHost = host;
		}

		HttpHeaders headers = new HttpHeaders();
        MimeType mimeType = MimeTypeUtils.parseMimeType("application/json");
        MediaType mediaType = new MediaType(mimeType.getType(), mimeType.getSubtype(), StandardCharsets.UTF_8);
        headers.setContentType(mediaType);

		String htmlUrl = restService.getUrl(reportHost+"/dongnireport/index.html", JSONUtil.parseToMap(JSONUtil.toJson(reportParam)));
		logger.info("渲染的url为{}", htmlUrl);
		// 文件名去除空格 否则无法下载文件
		PdfParam pdfParam = new PdfParam(htmlUrl, StringUtils.replace(fileName, " ", "_"));
		String url = host + "/api/export/base/html2pdf";
		URI uri = HttpUtil.getUri(url, true, null, null);
		ResponseEntity<String> responseEntity = HttpTimeout.execute(HttpTimeout.of(1000 * 60 * 10),
				() -> restTemplate.exchange(
						uri,
						HttpMethod.POST,
						new HttpEntity<>(JSONUtil.toJson(pdfParam), headers),
						String.class));
		HttpStatus.Series series = HttpStatus.Series.valueOf(responseEntity.getStatusCode());
		// 响应不是2xx,抛异常
		if (series != HttpStatus.Series.SUCCESSFUL) {
			throw new CommonException(ResponseStatusEnum.REST_REQUEST_ERROR, buildResponseErrorMessage(responseEntity, uri, pdfParam));
		} else {
			String body = responseEntity.getBody();
			// 直接转换为json
			Map<String, Object> bodyMap = JSONUtil.parse(body, new TypeReference<Map<String, Object>>() {});
			if (ObjectUtil.isValueEquals(bodyMap.get("status"), 0) && bodyMap.get("data") != null) {
				return bodyMap.get("data").toString();
			}

			String data = MapUtil.getStringNullable(bodyMap, "data");
			throw new CommonException(ResponseStatusEnum.REST_REQUEST_ERROR, String.format("渲染PDF失败，%s", data));
		}
	}


	public String getPdfUrl(String url, Map<String, Object> params) {

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(new MediaType(MediaType.APPLICATION_JSON, StandardCharsets.UTF_8));

		URI uri = HttpUtil.getUri(host + url, true, null, null);
		ResponseEntity<String> responseEntity = HttpTimeout.execute(HttpTimeout.of(1000 * 60 * 10),
				() -> restTemplate.exchange(
						uri,
						HttpMethod.POST,
						new HttpEntity<>(JSONUtil.toJson(params), headers),
						String.class));

		if (!responseEntity.getStatusCode().is2xxSuccessful()) {
			throw new CommonException(ResponseStatusEnum.REST_REQUEST_ERROR, buildResponseErrorMessage(responseEntity, uri, null));
		}

		Map<String, Object> bodyMap = JSONUtil.parse(responseEntity.getBody(), new TypeReference<Map<String, Object>>() {
		});

		if (!ObjectUtil.isValueEquals(bodyMap.get("status"), 0) || bodyMap.get("data") == null) {
			String data = MapUtil.getStringNullable(bodyMap, "data");
			throw new CommonException(ResponseStatusEnum.REST_REQUEST_ERROR, String.format("渲染PDF失败，%s", data));
		}

		return bodyMap.get("data").toString();
	}

	public static class  ReportParam implements Cloneable{
		private long examId;
		private String examName;
		/**
		 * statId
		 */
		private int reportId;
		/**
		 * 考试名称
		 */
		private String reportName;
		/**
		 * 考试时间
		 */
		private long diagnosisTime;
		/**
		 * 报告类型
		 */
		private String reportType;
		/**
		 * 班级Id,可选
		 */
		private Long classId;
		/**
		 * 课程Id,可选
		 */
		private Long courseId;
		/**
		 * 学校Id,可选
		 */
		private Long schoolId;
		/**
		 * 登录的token
		 */
		private String token;
		private Long userId;
		private String userName;
		private Integer userType;
		private Integer  stage;
		private boolean download = true;
		/**
		 * 宁夏、焦作、象山 -- 请求api使用 /dnserv/api
		 * 带上这个参数,前端会判断
		 */
		private String routerHashPath;

		// 前端需要的传参,原封不懂拼接到pdf下载的链接上
		private String customConfig;

		public ReportParam setExamId(long examId) {
			this.examId = examId;
			return this;
		}

		public ReportParam setExamName(String examName) {
			this.examName = examName;
			return this;
		}

		public ReportParam setReportId(int reportId) {
			this.reportId = reportId;
			return this;
		}

		public ReportParam setReportName(String reportName) {
			this.reportName = reportName;
			return this;
		}

		public ReportParam setDiagnosisTime(long diagnosisTime) {
			this.diagnosisTime = diagnosisTime;
			return this;
		}

		public ReportParam setReportType(String reportType) {
			this.reportType = reportType;
			return this;
		}

		public ReportParam setClassId(Long classId) {
			this.classId = classId;
			return this;
		}

		public ReportParam setCourseId(Long courseId) {
			this.courseId = courseId;
			return this;
		}

		public ReportParam setSchoolId(Long schoolId) {
			this.schoolId = schoolId;
			return this;
		}

		public ReportParam setToken(String token) {
			this.token = token;
			return this;
		}

		public long getExamId() {
			return examId;
		}

		public String getExamName() {
			return examName;
		}

		public int getReportId() {
			return reportId;
		}

		public String getReportName() {
			return reportName;
		}

		public long getDiagnosisTime() {
			return diagnosisTime;
		}

		public String getReportType() {
			return reportType;
		}

		public Long getClassId() {
			return classId;
		}

		public Long getCourseId() {
			return courseId;
		}

		public Long getSchoolId() {
			return schoolId;
		}

		public String getToken() {
			return token;
		}

		public boolean isDownload() {
			return download;
		}

		public Long getUserId() {
			return userId;
		}

		public ReportParam setUserId(Long userId) {
			this.userId = userId;
			return this;
		}

		public String getUserName() {
			return userName;
		}

		public ReportParam setUserName(String userName) {
			this.userName = userName;
			return this;
		}

		public Integer getUserType() {
			return userType;
		}

		public ReportParam setUserType(Integer userType) {
			this.userType = userType;
			return this;
		}

		public Integer getStage() {
			return stage;
		}

		public ReportParam setStage(Integer stage) {
			this.stage = stage;
			return this;
		}

		public ReportParam clone()  {
			try {
				return (ReportParam) super.clone();
			} catch (CloneNotSupportedException e) {
				throw new DongniException(e.getMessage(),e);
			}
		}

		public String getRouterHashPath() {
			return routerHashPath;
		}

		public ReportParam setRouterHashPath(String routerHashPath) {
			this.routerHashPath = routerHashPath;
			return this;
		}

		public String getCustomConfig() {
			return customConfig;
		}

		public ReportParam setCustomConfig(String customConfig) {
			this.customConfig = customConfig;
			return this;
		}

		@Override
		public String toString() {
			return new StringJoiner(", ", ReportParam.class.getSimpleName() + "[", "]")
				.add("examId=" + examId)
				.add("reportId=" + reportId)
				.add("reportName='" + reportName + "'")
				.add("diagnosisTime=" + diagnosisTime)
				.add("reportType='" + reportType + "'")
				.add("classId=" + classId)
				.add("courseId=" + courseId)
				.add("schoolId=" + schoolId)
				.add("token='" + token + "'")
				.add("userId=" + userId)
				.add("userType=" + userType)
				.add("stage=" + stage)
				.add("download=" + download)
				.add("routerHashPath=" + routerHashPath)
				.toString();
		}
	}

	public static class PdfParam{
		private String url;
		private String fileName;

		public PdfParam(String url, String fileName) {
			this.url = url;
			this.fileName = fileName;
		}

		public String getUrl() {
			return url;
		}

		public String getFileName() {
			return fileName;
		}
	}


	private String buildResponseErrorMessage(ResponseEntity<String> responseEntity, URI uri, PdfParam param) {
		HttpStatus statusCode = responseEntity.getStatusCode();
		int httpCode = statusCode.value();
		String reason = statusCode.getReasonPhrase();
		String body = responseEntity.getBody();

		return String.format("渲染PDF请求失败，状态码：%s，响应体：%s", httpCode, body);
	}
}
