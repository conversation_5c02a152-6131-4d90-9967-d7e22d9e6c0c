package com.dongni.exam.newcard.parse.bean;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.newcard.parse.constant.AnswerCardConstants;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/08/03 <br/>
 * 识别设置
 */
public class AnswerCardConfig {
    
    /** 缺考标记最低阈值 */
    private double absentLowThreshold = AnswerCardConstants.ABSENT_LOW_THRESHOLD_DEFAULT;
    
    /** 选择题识别阈值，如果小于0，则需要进行像素占比大小判断 */
    private double selectThreshold = AnswerCardConstants.SELECT_THRESHOLD_DEFAULT;
    
    /** 多选题识别默认最低阈值 */
    private double multiSelectLowThreshold = AnswerCardConstants.MULTI_SELECT_LOW_THRESHOLD_DEFAULT;
    
    
    /** 按照标准差计算的方案开启状态 */
    private boolean standardDeviationEnabled = AnswerCardConstants.STANDARD_DEVIATION_ENABLED_DEFAULT;
    
    /** 每道选择题计算标准差的阈值*/
    private double standardDeviationThreshold = AnswerCardConstants.STANDARD_DEVIATION_THRESHOLD_DEFAULT;
    
    /** 单个选项减去最小值后对比最小值的比例阈值 */
    private double selectMinRateThreshold = AnswerCardConstants.SELECT_MIN_RATE_THRESHOLD_DEFAULT;
    
    /** 选择题识别补偿值 */
    private double selectCompensateValue = AnswerCardConstants.SELECT_COMPENSATE_VALUE_DEFAULT;
    
    
    /** 黑色像素占比最低阈值，大于这个值才能算得分 */
    private double blackPixelThresholdLowRate = AnswerCardConstants.BLACK_PIXEL_THRESHOLD_LOW_RATE_DEFAULT;
    
    /** 黑色像素占比最大值，大于这个值则无有效填涂 */
    private double blackPixelValidMaxRate = AnswerCardConstants.BLACK_PIXEL_VALID_MAX_RATE_DEFAULT;
    
    /** 黑色像素占比最低有效值，大于这个值才算是有效填涂
     *  对于大于最低有效值，小于最低阈值的占比，由于系统不好确定这部分是异常还是正常，会把这部分抛出异常处理，也就是会产生阅卷任务重新线上阅卷
     */
    private double blackPixelValidMinRate = AnswerCardConstants.BLACK_PIXEL_VALID_MIN_RATE_DEFAULT;
    
    /**
     * 支持的上下定位点间最大坡度
     */
    private double topAndBottomMaxSlope = AnswerCardConstants.TOP_AND_BOTTOM_MAX_SLOPE;
    
    /** 选项大小规格 */
    private RecognitionChoiceRect recognitionChoiceRect = null;
    
    /**
     * 实例化对象
     * @param setting recognResultGroup.setting
     * @return answerCardConfig
     */
    public static AnswerCardConfig of(Map<String, Object> setting) {
        return new AnswerCardConfig().init(setting);
    }
    
    /**
     * 是否大于最小合理值
     */
    public boolean isValid(double rate) {
        return rate > blackPixelValidMinRate;
    }
    
    /**
     * 是否大于最低阈值
     */
    public boolean isNormal(double rate) {
        return rate > blackPixelThresholdLowRate;
    }
    
    /**
     * 是否超过最大合理值
     */
    public boolean isBeyond(double rate) {
        return rate > blackPixelValidMaxRate;
    }
    
    public double getAbsentLowThreshold() {
        return absentLowThreshold;
    }
    
    public double getSelectThreshold() {
        return selectThreshold;
    }
    
    public double getMultiSelectLowThreshold() {
        return multiSelectLowThreshold;
    }
    
    public boolean isStandardDeviationEnabled() {
        return standardDeviationEnabled;
    }
    
    public double getStandardDeviationThreshold() {
        return standardDeviationThreshold;
    }
    
    public double getSelectMinRateThreshold() {
        return selectMinRateThreshold;
    }
    
    public double getSelectCompensateValue() {
        return selectCompensateValue;
    }
    
    public double getBlackPixelThresholdLowRate() {
        return blackPixelThresholdLowRate;
    }
    
    public double getBlackPixelValidMaxRate() {
        return blackPixelValidMaxRate;
    }
    
    public double getBlackPixelValidMinRate() {
        return blackPixelValidMinRate;
    }
    
    public double getTopAndBottomMaxSlope() {
        return topAndBottomMaxSlope;
    }
    
    /**
     * 选项大小规格
     * 如果没设置 返回null 调用者自行判断
     */
    public RecognitionChoiceRect getRecognitionChoiceRect() {
        return recognitionChoiceRect;
    }
    
    /**
     * 选项大小规格的宽度
     * 如果没有会抛异常 未设置该参数
     */
    public int getChoiceRectWidth() {
        return getRecognitionChoiceRectThrowsWhenNotSet().getWidth();
    }
    
    /**
     * 选项大小规格的高度
     * 如果没有会抛异常 未设置该参数
     */
    public int getChoiceRectHeight() {
        return getRecognitionChoiceRectThrowsWhenNotSet().getHeight();
    }
    
    private RecognitionChoiceRect getRecognitionChoiceRectThrowsWhenNotSet() {
        if (recognitionChoiceRect == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未设置该参数");
        }
        return recognitionChoiceRect;
    }
    
    /** 设置 缺考标记最低阈值 */
    public AnswerCardConfig setAbsentLowThreshold(double absentLowThreshold) {
        this.absentLowThreshold = absentLowThreshold;
        return this;
    }
    
    /** 设置 TODO 不详 */
    public AnswerCardConfig setSelectThreshold(double selectThreshold) {
        this.selectThreshold = selectThreshold;
        return this;
    }
    
    /** 设置 多选题识别最低阈值 */
    public AnswerCardConfig setMultiSelectLowThreshold(double multiSelectLowThreshold) {
        this.multiSelectLowThreshold = multiSelectLowThreshold;
        return this;
    }
    
    /** 设置 按照标准差计算的方案是否开启 */
    public AnswerCardConfig setStandardDeviationEnabled(boolean standardDeviationEnabled) {
        this.standardDeviationEnabled = standardDeviationEnabled;
        return this;
    }
    
    /** 设置 每道选择题计算标准差的阈值 */
    public AnswerCardConfig setStandardDeviationThreshold(double standardDeviationThreshold) {
        this.standardDeviationThreshold = standardDeviationThreshold;
        return this;
    }
    
    /** 设置 单个选项减去最小值后对比最小值的比例阈值 */
    public AnswerCardConfig setSelectMinRateThreshold(double selectMinRateThreshold) {
        this.selectMinRateThreshold = selectMinRateThreshold;
        return this;
    }
    
    /** 设置 选择题识别补偿值 */
    public AnswerCardConfig setSelectCompensateValue(double selectCompensateValue) {
        this.selectCompensateValue = selectCompensateValue;
        return this;
    }
    
    /** 设置 黑色像素占比最低阈值，大于这个值才能算得分 */
    public AnswerCardConfig setBlackPixelThresholdLowRate(double blackPixelThresholdLowRate) {
        this.blackPixelThresholdLowRate = blackPixelThresholdLowRate;
        return this;
    }
    
    /** 设置 黑色像素占比最大值，大于这个值则无有效填涂 */
    public AnswerCardConfig setBlackPixelValidMaxRate(double blackPixelValidMaxRate) {
        this.blackPixelValidMaxRate = blackPixelValidMaxRate;
        return this;
    }
    
    /** 设置 黑色像素占比最低有效值，大于这个值才算是有效填涂 */
    public AnswerCardConfig setBlackPixelValidMinRate(double blackPixelValidMinRate) {
        this.blackPixelValidMinRate = blackPixelValidMinRate;
        return this;
    }
    
    /** 支持的上下定位点间最大坡度 */
    public AnswerCardConfig setTopAndBottomMaxSlope(double topAndBottomMaxSlope) {
        this.topAndBottomMaxSlope = topAndBottomMaxSlope;
        return this;
    }
    
    /** 设置 选项大小规格 */
    public AnswerCardConfig setRecognitionChoiceRect(RecognitionChoiceRect recognitionChoiceRect) {
        this.recognitionChoiceRect = recognitionChoiceRect;
        return this;
    }
    
    /**
     * 初始化设置 如果答题卡模板中没有该值，则默认见字段初始化
     * @param setting recognResultGroup.setting
     * @return this
     */
    private AnswerCardConfig init(Map<String, Object> setting) {
        if (MapUtils.isEmpty(setting)) {
            return this;
        }
        
        // 缺考标识识别阈值下限
        if (ObjectUtil.isNumeric(setting.get("absentLowThreshold"))) {
            this.setAbsentLowThreshold(Double.parseDouble(setting.get("absentLowThreshold").toString()));
        }
        
        // TODO 不详
        if (ObjectUtil.isNumeric(setting.get("selectedThreshold"))) {
            this.setSelectThreshold(Double.parseDouble(setting.get("selectedThreshold").toString()));
        }
        
        // 多选题识别最低阈值
        if (ObjectUtil.isNumeric(setting.get("multiSelectLowThreshold"))) {
            this.setMultiSelectLowThreshold(Double.parseDouble(setting.get("multiSelectLowThreshold").toString()));
        }
        
        // 按照标准差计算的方案是否开启  TODO 旧的字段为方差 做兼容而已
        if (setting.get("isVarianceOn") != null) {
            this.setStandardDeviationEnabled(ObjectUtil.isValueEquals("true", setting.get("isVarianceOn")));
        }
        
        // 每道选择题计算标准差的阈值 受开启状态影响  TODO 旧的字段为方差 做兼容而已
        if (ObjectUtil.isNumeric(setting.get("varianceThreshold"))) {
            this.setStandardDeviationThreshold(Double.parseDouble(setting.get("varianceThreshold").toString()));
        }
        
        // 按照标准差计算的方案是否开启
        if (setting.get("isStandardDeviationOn") != null) {
            this.setStandardDeviationEnabled(ObjectUtil.isValueEquals("true", setting.get("isStandardDeviationOn")));
        }
        
        // 每道选择题计算标准差的阈值 受开启状态影响
        if (ObjectUtil.isNumeric(setting.get("standardDeviationThreshold"))) {
            this.setStandardDeviationThreshold(Double.parseDouble(setting.get("standardDeviationThreshold").toString()));
        }
        
        // 单个选项减去最小值后对比最小值的比例阈值
        if (ObjectUtil.isNumeric(setting.get("minRateThreshold"))) {
            this.setSelectMinRateThreshold(Double.parseDouble(setting.get("minRateThreshold").toString()));
        }
        
        // 选择题识别补偿值
        if (ObjectUtil.isNumeric(setting.get("compensateValue"))) {
            this.setSelectCompensateValue(Double.parseDouble(setting.get("compensateValue").toString()));
        }
        
        // 黑色像素占比最低阈值
        if (ObjectUtil.isNumeric(setting.get("blackPixelThresholdLowRate"))) {
            this.setBlackPixelThresholdLowRate(Double.parseDouble(setting.get("blackPixelThresholdLowRate").toString()));
        }
        
        // 黑色像素占比最大值
        if (ObjectUtil.isNumeric(setting.get("blackPixelValidMaxRate"))) {
            this.setBlackPixelValidMaxRate(Double.parseDouble(setting.get("blackPixelValidMaxRate").toString()));
        }
        
        // 黑色像素占比最低有效
        if (ObjectUtil.isNumeric(setting.get("blackPixelValidMinRate"))) {
            this.setBlackPixelValidMinRate(Double.parseDouble(setting.get("blackPixelValidMinRate").toString()));
        }
        
        // 支持的上下定位点间最大坡度
        if (ObjectUtil.isNumeric(setting.get("topAndBottomMaxSlope"))) {
            this.setTopAndBottomMaxSlope(Double.parseDouble(setting.get("topAndBottomMaxSlope").toString()));
        }
        
        // 选项规格
        if (setting.containsKey("choiceRect")) {
            Map<String, Object> choiceRect = MapUtil.getCast(setting, "choiceRect");
            if (MapUtils.isNotEmpty(choiceRect)) {
                if (ObjectUtil.isInteger(choiceRect.get("width")) && ObjectUtil.isInteger(choiceRect.get("height"))) {
                    int width = Integer.parseInt(choiceRect.get("width").toString());
                    int height = Integer.parseInt(choiceRect.get("height").toString());
                    this.setRecognitionChoiceRect(new RecognitionChoiceRect(width, height));
                }
            }
        }
        
        
        return this;
    }
    
    
}
