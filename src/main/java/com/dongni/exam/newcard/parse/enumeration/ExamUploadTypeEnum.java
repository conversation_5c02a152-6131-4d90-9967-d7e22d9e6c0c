package com.dongni.exam.newcard.parse.enumeration;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;

/**
 * <AUTHOR> <br>
 * 2021/10/15 <br>
 *
 */
public enum ExamUploadTypeEnum {
    
    /** */
    NORMAL_BY_QUESTION(1, "按试题上传", ""),
    NORMAL_BY_CLASS(2, "按班级上传", ""),
    
    APPEND_BY_QUESTION(11, "按试题补传", ""),
    APPEND_BY_CLASS(12, "按班级补传", ""),
    
    ;
    
    
    private final int value;
    private final String description;
    private final String remark;
    
    ExamUploadTypeEnum(int value, String description, String remark) {
        this.value = value;
        this.description = description;
        this.remark = remark;
    }
    
    public static ExamUploadTypeEnum getByValue(int uploadType) {
        for (ExamUploadTypeEnum examUploadType : ExamUploadTypeEnum.values()) {
            if (uploadType == examUploadType.getValue()) {
                return examUploadType;
            }
        }
        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "答题卡上传类型不存在: " + uploadType);
    }
    
    public int getValue() {
        return value;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getRemark() {
        return remark;
    }

    public static boolean isAppendType(int uploadType) {
        return uploadType == APPEND_BY_QUESTION.getValue() || uploadType == APPEND_BY_CLASS.getValue();
    }
}
