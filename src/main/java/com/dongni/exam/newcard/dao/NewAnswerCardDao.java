package com.dongni.exam.newcard.dao;

import com.dongni.exam.card.bean.TemplateInfo;
import com.dongni.exam.card.bean.vo.ExamUploaderCompleteStatVO;
import com.dongni.exam.materials.bean.VO.ExceptionVO;
import com.dongni.exam.materials.bean.VO.ExpEduMaterialsVO;
import com.dongni.exam.newcard.bean.DTO.CardTemplate;
import com.dongni.exam.newcard.bean.DTO.RecCardDTO;
import com.dongni.exam.newcard.bean.DTO.StudentCardStatusDTO;
import com.dongni.exam.newcard.bean.VO.AnswerCardVO;
import com.dongni.exam.newcard.bean.VO.ExamStudentVO;
import com.dongni.exam.newcard.bean.VO.RelativeStudentVO;
import com.dongni.exam.newcard.bean.VO.UpdateTemplateVO;
import com.dongni.exam.plan.bean.ExamUploaderBO;
import com.dongni.exam.plan.bean.bo.CardStudentInfoBO;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.bean.dto.ReassociateCardInfo;
import com.dongni.exam.plan.bean.dto.StudentCardDTO;
import com.dongni.exam.plan.bean.vo.ExamPaperCardStatVO;
import com.dongni.exam.plan.bean.vo.ExamSchoolVO;
import com.dongni.exam.plan.bean.vo.ExamStudentResultVO;
import com.dongni.exam.plan.bean.vo.UpdateResultCardVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/7/25
 */
public interface NewAnswerCardDao {
    int getExamPaperStudentCount(@Param("relativeStudentVO") RelativeStudentVO relativeStudentVO,
                                 @Param("examUploaderBo") NewExamUploaderBO examUploaderBO,
                                 @Param("questionNumber") Long questionNumber);



    List<ExamStudentVO> getExamPaperStudent(@Param("relativeStudentVO") RelativeStudentVO relativeStudentVO,
                                            @Param("examUploaderBo") NewExamUploaderBO examUploaderBO,
                                            @Param("questionNumber") Long questionNumber);

    void updateExamUploaderIds2ExamUploaderId(@Param("idList") List<Long> idList, @Param("id") long examUploaderId);

    List<Long> getStudentIdsByExamUploaderId(@Param("examUploaderId") long examUploaderId);

    List<StudentCardDTO> getStudentCardCount(@Param("examId") long examId,
                                             @Param("paperId") long paperId,
                                             @Param("list") List<ExamStudentResultVO> studentResultVOList);

    void deleteAnswerCardByExamUploaderId(@Param("examUploaderId") long examUploaderId);

    List<StudentCardDTO> getStudentUploadedStatusByExamUploaderAndStudentIds(@Param("examUploaderBo") NewExamUploaderBO examUploaderBO, @Param("studentIds") List<Long> studentIds);

    List<StudentCardStatusDTO> getStudentCardStatus(@Param("examUploaderBo") NewExamUploaderBO newExamUploaderBO, @Param("studentIds") List<Long> studentIds);

    int getForecastCardCount(NewExamUploaderBO newExamUploaderBO);

    List<StudentCardDTO> getStudentCardCountBySchool(@Param("examId") long examId,
                                                     @Param("paperId") long paperId,
                                                     @Param("schoolId") long schoolId,
                                                     @Param("studentIds") List<Long> studentIds);

    void updateAnswerCardRepeatByStudentIds(@Param("examId")long examId,
                                            @Param("paperId")long paperId,
                                            @Param("schoolId")long schoolId,
                                            @Param("studentIds")List<Long> studentIds);

    void updateAnswerCardUnRepeatByStudentIds(@Param("examId")long examId,
                                              @Param("paperId")long paperId,
                                              @Param("schoolId")long schoolId,
                                              @Param("studentIds")List<Long> studentIds);

    List<ExamUploaderBO> getExamUploaderWithoutError(@Param("examId") long examId, @Param("paperId") long paperId);

    ExamPaperCardStatVO getExamPaperCardStat(@Param("examId") long examId, @Param("paperId")long paperId,
                                             @Param("schoolIds") List<Long> schoolIds);

    List<Long> getSchoolIds(@Param("examId") long examId, @Param("paperId") long paperId,
                            @Param("examUploaderIds") List<Long> examUploaderIds);

    void resetAnswerCard(@Param("examUploaderId") long examUploaderId,
                         @Param("batchId") long batchId,
                         @Param("templateType") int templateType,
                         @Param("templateCode") long templateCode);

    List<ExamSchoolVO> getExamUploaderUncareSchools(NewExamUploaderBO newExamUploaderBO);

    ExamUploaderCompleteStatVO getExamUploaderCompleteStatistic(NewExamUploaderBO bo);

    void updateExamUploaderTemplateInfo(UpdateTemplateVO updateTemplateVO);

    int getExamUploaderStudentCount(@Param("examUploaderId") long examUploaderId);

    List<AnswerCardVO> getBatchExceptionCards(long examUploaderId, long batchId);

    void updateStudentAnswerCardNoRelative(@Param("examId") long examId, @Param("paperId") long paperId, @Param("studentIdList") List<Long> studentIdList);

    List<CardStudentInfoBO> getMisMatchCardStudentList(UpdateResultCardVO updateResultCardVO);

    ReassociateCardInfo getCardSameNameStudentIds(UpdateResultCardVO updateResultCardVO);

    void updateStudentWithNewExamUploader(UpdateResultCardVO updateResultCardVO);

    void removeRedundancyCards(@Param("examUploaderId") long examUploaderId, @Param("deleteStudentIds") List<Long> deleteStudentIds);

    List<String> getAbsentAnswerCardCodes(@Param("examUploaderIds") List<Long> examUploaderIds);

    List<CardTemplate> getCardTemplateInfos(@Param("examUploaderIds") List<Long> examUploaderIds);

    int isStudentCardAbsent(Long examUploaderId, Long studentId);

    Long getExistsExamAndExamUploaderCards(@Param("examId")long examId, @Param("examUploaderId")long examUploaderId);

    List<Map<String, Object>> getCardListByExamUploaderId(long examUploaderId, List<Long> studentIds);

    TemplateInfo getStudentCardTemplate(long examUploaderId, long studentId);

    List<Long> getStudentAnswerCardIdsByStudentId(@Param("examUploaderId") long examUploaderId, @Param("studentId") long studentId);

	List<StudentCardDTO> getTemplateCodes(@Param("examId") long examId, @Param("paperId") long paperId, @Param("studentIds") List<Long> studentIds);

    List<Long> getCardRepeatGroupStudentIds(@Param("examUploaderIds") List<Long> examUploaderIds);

    int getCardRepeatGroup(@Param("examUploaderIds") List<Long> examUploaderIds, @Param("newExamUploaderBO") NewExamUploaderBO newExamUploaderBO, @Param("studentIds") List<Long> studentIds);

    List<AnswerCardVO> getAnswerCardVOList(@Param("answerCardIds") List<Long> answerCardIds);

    void removeRedundancyCorrectStudentCards(UpdateResultCardVO updateResultCardVO);

    void updateSyncAnswerCardCode(UpdateResultCardVO updateResultCardVO);

    List<Long> getExceptionExamUploaderIds(@Param("examId") long examId, @Param("paperId") long paperId, @Param("errorCode") int errorCode);

    List<AnswerCardVO> getAnswerCardVOListByExamUploaderId(@Param("examUploaderId") long examUploaderId);

    void updateCorrectPath(AnswerCardVO cardVO);

    List<Long> getCompleteExamUploaderIds(@Param("examId") long examId);

    List<Long> getExamUploaderClassIds(@Param("examUploaderId") long examUploaderId);

    List<RecCardDTO> getWaitingCardList(@Param("examUploaderId") long examUploaderId, @Param("size") int size);

    List<String> getBatchFileNames(@Param("examUploaderId") long examUploaderId, @Param("batchId") long batchId);

    void updateAppendExamUploaderToNormalOfFile(@Param("appendExamUploaderIds") List<Long> appendExamUploaderIds, @Param("examUploaderId") long examUploaderId);

    void updateStudentIdByRecognitionId(long recognitionId, List<Long> studentIds);

    Map<String, Object> getEducationExpStatistics(@Param("examUploaderIds") List<Long> examUploaderIds, @Param("errorCodes") List<ExceptionVO> errorCodes);

    List<Long> getAnswerCardStudentIds(long examId, long paperId, List<Long> studentIds);
}
