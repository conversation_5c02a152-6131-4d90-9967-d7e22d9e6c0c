package com.dongni.exam.newcard.dao;

import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.bean.bo.UpdateExamPaperBO;
import com.dongni.exam.plan.bean.dto.StudentCardDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/9
 */
public interface NewAnswerCardRecycleDao {
    void updateExamUploaderIds2ExamUploaderId(@Param("idList")List<Long> ids,
                                              @Param("id") long id);

    List<Long> getAbsentAnswerCardStudentIdsByExamUploader(NewExamUploaderBO newExamUploaderBO);

    List<StudentCardDTO> getAbsentAnswerCardStudentDTOsByExamUploaderAndStudentIds(
            @Param("examUploaderBo") NewExamUploaderBO examUploaderBO,
            @Param("studentIds") List<Long> studentIds);

    void updateExamPaper(UpdateExamPaperBO updateExamPaperBO);

    List<String> getBatchFileNames(@Param("examUploaderId") long examUploaderId, @Param("batchId") long batchId);
}
