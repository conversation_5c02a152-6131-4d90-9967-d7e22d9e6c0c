package com.dongni.exam.newcard.dao;

import com.dongni.exam.health.check.bean.bo.ExamItemQuestionNumberBO;
import com.dongni.exam.item.bean.entity.ExamItem;
import com.dongni.exam.mark.ai.bean.dto.ExamItemDTO;
import com.dongni.exam.newcard.bean.DTO.CoursePaperReadDTO;
import com.dongni.exam.newcard.bean.DTO.ExamItemErrorCodeDTO;
import com.dongni.exam.newcard.bean.DTO.StudentEIDTO;
import com.dongni.exam.newcard.bean.IntelligenceExamCourse;
import com.dongni.exam.newcard.bean.IntelligenceItem;
import com.dongni.exam.newcard.bean.VO.SuspectExamItemVO;
import com.dongni.exam.plan.bean.dto.StudentCardDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/8/9
 */
public interface NewExamItemDao {
    void rollbackExamItems(@Param("examId") long examId, @Param("paperId") long paerId,
                           @Param("questionNumbers") List<Long> questionNumbers,
                           @Param("studentIds") List<Long> studentIds);

    List<Long> getStudentWithoutCardIds(@Param("examId") long examId,
                                        @Param("paperId") long paperId,
                                        @Param("schoolId") long schoolId,
                                        @Param("classIdList") List<Long> classIdList,
                                        @Param("questionNumber") Long questionNumber);

    void deleteExamItemsByExamIdAndPaperIdAndStudentIds(@Param("examId") long examId,
                                                        @Param("paperId") long paperId,
                                                        @Param("studentIds") List<Long> studentIds,
                                                        @Param("questionNumbers")List<Long> questionNumbers);

    Collection<String> getItemSaveImgUrls(@Param("examId") long examId,
                                          @Param("paperId") long paperId,
                                          @Param("studentIds") List<Long> studentIds,
                                          @Param("questionNumbers")List<Long> questionNumbers);

    List<StudentCardDTO> getStudentExamItemByExamUploader(@Param("examId") long examId,
                                                          @Param("paperId") long paperId,
                                                          @Param("questionNumber")long questionNumber,
                                                          @Param("studentIds") List<Long> studentIds);

    List<Long> getStudentIdsByExamAndPaperAndStudentIds(@Param("examId") long examId, @Param("paperId") long paperId,
                                                        @Param("questionNumber") long questionNumber,
                                                        @Param("studentIds") List<Long> unuploadedStudentIds);

    List<Long> getQuestionNumbers(@Param("examId") long examId, @Param("paperId") long paperId, @Param("examUploaderId") long examUploaderId);

    List<ExamItemQuestionNumberBO> getQuestionNumberBOList(@Param("examId") long examId,
                                                           @Param("paperId") long paperId,
                                                           @Param("questionNumbers") List<Long> qns,
                                                           @Param("examUploaderId") long examUploaderId);

    void incrementExamItemReadStatus(@Param("examId") long examId, @Param("paperId") long paperId,
                                     @Param("questionNumbers") List<Long> qns,  @Param("studentIds")List<Long> ids);

    void batchUpdateObjectiveItems(@Param("itemList") List<SuspectExamItemVO> itemVOList, @Param("answer") int answer);

    List<StudentEIDTO> getStudentEIList(@Param("examId") long examId, @Param("paperId") long paperId,
                                        @Param("studentIds") Set<Long> studentIds,
                                        @Param("questionNumbers") List<Long> qns);

    void updateStudentExamItemNoRelative(@Param("examId") long examId, @Param("paperId") long paperId,
                                         @Param("qns") List<Long> qns,
                                         @Param("studentIdList") List<Long> studentIdList);

    void updateStudentIdWithMisMatch(@Param("examId") long examId,
                                     @Param("paperId") long paperId,
                                     @Param("correctExamId") long correctExamId,
                                     @Param("correctPaperId") long correctPaperId);

    void updateExamItemStudentId2Positive(@Param("examId")long examId, @Param("paperId")long paperId, @Param("questionNumbers") List<Long> qnList);

    List<ExamItemErrorCodeDTO> getExamUploaderExamItemList(@Param("examUploaderId") long examUploaderId, @Param("qns") List<Long> qns);

    void batchUpdateExamItemsErrorCode(@Param("list") List<ExamItemErrorCodeDTO> x);

    void updateMisMatchExamId(@Param("examUploaderId") long examUploaderId);

    void updateExamItemInvalid(@Param("examId") long examId, @Param("paperId") long paperId);

    List<ExamItem> getExamItemInvalid(long examId, long paperId);

	List<CoursePaperReadDTO> getIntelligencePRIds(@Param("courseNames") List<String> courseNames);

    List<IntelligenceItem> getExamItemListByPaperReadId(Long paperReadId);

    List<IntelligenceExamCourse> getIntelligenceExamCourse(@Param("paperReadIds") List<Long> paperReadIds);

	List<ExamItemDTO> getExamItemListByEPQIds(@Param("examId") long examId, @Param("paperId") long paperId,
                                              @Param("questionNumbers") List<Integer> questionNumbers);

    List<ExamItemDTO> getExamItemListByIds(@Param("examItemIds") List<Long> examItemIds);

    void updateCardExamItemStatus(List<Long> studentIds, long paperId, long examId, int status);

    Integer getOneObjectiveExamItem(@Param("examId") long examId, @Param("paperId") long paperId, @Param("objectiveQns") List<Long> objectiveQns);

    void updateReadStatus(@Param("examId") long examId, @Param("paperId") long paperId,
                          @Param("qns") List<Long> qns, @Param("stuIds") List<Long> stuIds,
                          @Param("readStatus") int readStatus);

    void updateExamItemStudentIdToNegative(@Param("recognitionId")long recognitionId, @Param("studentIds")List<Long> studentIds,@Param("qns") List<Long> qns);

    void updateExamItemStudentIdToPositive(@Param("recognitionId") long recognitionId, @Param("studentIds") List<Long> studentIds, @Param("qns") List<Long> qns);
}
