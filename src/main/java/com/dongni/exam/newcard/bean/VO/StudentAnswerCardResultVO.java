package com.dongni.exam.newcard.bean.VO;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/09
 */
@Data
public class StudentAnswerCardResultVO {
    private long batchId;
    private String batchName;
    private String fileName;
    private float confidentValue;
    private String filePath;
    private String className;
    private long classId;
    private long templateCode;
    private long studentId;
    private String studentExamNum;
    private String studentName;
    private String studentNum;
    private long schoolId;
    private String schoolName;
    private long examId;
    private long examUploaderId;
    // 补全的信息
    private int index;
    private String templateName;
    private String featurePath;
    //
    private int exceptionCount;
    private int objectiveCount;
    private String structureNumbers;
    private float scoreRate;
}
