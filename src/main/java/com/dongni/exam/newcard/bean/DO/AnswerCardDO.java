package com.dongni.exam.newcard.bean.DO;


import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 答题卡
 *
 * <AUTHOR>
 * @date 2023/12/7 16:52
 */
@Data
@Entity()
@Table(name = "t_answer_card")
public class AnswerCardDO implements Serializable {


    private static final long serialVersionUID = 1089272996067078972L;

    @Id
    @Column(name = "answer_card_id")
    private Long answerCardId;
    /**
     * 修改时间
     */
    @Column(name = "modify_date_time")
    private Date modifyDateTime;

    /**
     * 答题卡上传人id
     */
    @Column(name = "exam_uploader_id")
    private Long examUploaderId;

    /**
     * 考试ID
     */
    @Column(name = "exam_id")
    private Long examId;

    /**
     * 答题卡识别学生班级ID
     */
    @Column(name = "class_id")
    private Long classId;

    /**
     * 答题卡识别学生ID
     */
    @Column(name = "student_id")
    private Long studentId;

    /**
     * 答题卡识别学生学号
     */
    @Column(name = "student_num")
    private String studentNum;


    @Column(name = "student_exam_num")
    private String studentExamNum;

    /**
     * 识别类型（0：线上批改，1：线下批改，2：绘制模板）
     */
    @Column(name = "template_type")
    private Integer templateType;

    /**
     * 识别模版编号
     */
    @Column(name = "template_code")
    private Long templateCode;

    /**
     * 图片文件名称
     */
    @Column(name = "file_name")
    private String fileName;

    /**
     * 图片文件相对路径，用于前台访问
     */
    @Column(name = "file_path")
    private String filePath;

    /**
     * 答题卡识别结果
     */
    @Column(name = "recognition_result")
    private Integer recognitionResult;

    /**
     * 答题卡识别异常信息
     */
    @Column(name = "recognition_result_info")
    private String recognitionResultInfo;

    /**
     * 答题卡页码
     */
    @Column(name = "page_number")
    private Integer pageNumber;

    /**
     * 错误类型编码（0：默认，1：定位点识别异常， 2：答题卡编号识别异常，3：学号识别异常，4：答题卡编号和学号识别异常）
     */
    @Column(name = "error_type_code")
    private Integer errorTypeCode;

    /**
     * 错误类型。前两位为主异常 00 正常 01 答题卡缺失 10 定位异常 11 疑似定位异常
     */
    @Column(name = "error_code")
    private Boolean errorCode;

    /**
     * 错误类型名称
     */
    @Column(name = "error_type_name")
    private String errorTypeName;

    /**
     * 答题卡编号，用于将正反面的答题卡组合标识
     */
    @Column(name = "answer_card_code")
    private String answerCardCode;

    /**
     * 用于标识某组答题卡中某张答题卡的正反面
     */
    @Column(name = "answer_card_pair")
    private String answerCardPair;

    /**
     * 答题卡定位点识别结果JSON文本，用于第二次跳过定位点识别
     */
    @Column(name = "mark_point_json")
    private String markPointJson;

    /**
     * 矫正图像的参数
     */
    @Column(name = "correct_param")
    private String correctParam;

    /**
     * 矫正状态（0：未矫正，1：已矫正，2：已合成分数）
     */
    @Column(name = "correct_status")
    private Integer correctStatus;

    /**
     * 合成的带分数信息的答题卡路径
     */
    @Column(name = "correct_path")
    private String correctPath;

    /**
     * 合成分数的答题卡图片
     */
    @Column(name = "score_path")
    private String scorePath;

    /**
     * blank_count
     */
    @Column(name = "blank_count")
    private Integer blankCount;

    /**
     * 修改状态字段（0：默认，1：异常处理中修改过，需要重新切割，2：异常处理继续上传）
     */
    @Column(name = "modify_status")
    private Integer modifyStatus;

    /**
     * 小任务ID
     */
    @Column(name = "task_id")
    private String taskId;

    /**
     * 小任务执行状态(0-默认， 1-执行中，2-处理完成，3-处理异常)
     */
    @Column(name = "task_status")
    private Integer taskStatus;

    /**
     * 来源渠道(0-云端，1-网络客户端)
     */
    @Column(name = "source")
    private Integer source;

    /**
     * 识别定位可信度
     */
    @Column(name = "recog_confidence")
    private Float recogConfidence;

    /**
     * 客观题低可信度详细信息
     */
    @Column(name = "low_confident_info")
    private String lowConfidentInfo;

    /**
     * 创建人ID
     */
    @Column(name = "creator_id")
    private Long creatorId;

    /**
     * 创建人
     */
    @Column(name = "creator_name")
    private String creatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_date_time")
    private Date createDateTime;

    /**
     * 修改人ID
     */
    @Column(name = "modifier_id")
    private Long modifierId;

    /**
     * 修改人
     */
    @Column(name = "modifier_name")
    private String modifierName;

    /**
     * 扫描批次Id批次id
     */
    @Column(name = "batch_id")
    private Long batchId;

    /**
     * 扫描时间
     */
    @Column(name = "scan_date_time")
    private Date scanDateTime;


    private Long paperId;

    private String studentName;

    public Long getPaperId() {
        return paperId;
    }

    public void setPaperId(Long paperId) {
        this.paperId = paperId;
    }
}
