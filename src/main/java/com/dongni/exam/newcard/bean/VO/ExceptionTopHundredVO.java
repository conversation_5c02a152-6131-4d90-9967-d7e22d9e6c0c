package com.dongni.exam.newcard.bean.VO;

import com.dongni.common.utils.DictUtil;
import com.dongni.exam.newcard.enums.StudentExamNumErrorTypeEnum;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/25
 */
@Data
public class ExceptionTopHundredVO<T> {
    private String desc;
    List<T> exceptions;
    private int totalCount;
    private int studentExamNumErrorType;

    public void handleDesc(int errorCode) {
        int studentNumExp = DictUtil.getDictValue("recognitionAnswerCardStatus", "studentNumExp");
        if (errorCode == studentNumExp) {
            desc = StudentExamNumErrorTypeEnum.getDesc(studentExamNumErrorType);
        }
    }
}
