package com.dongni.exam.newcard.bean.VO;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/30
 */
@Data
public class AnswerCardVO {
    private String answerCardCode;
    private long answerCardId;
    private String answerCardPair;
    private long batchId;
    private String batchName;
    private Date batchStartTime;
    private String className;
    private long examUploaderId;
    private String fileName;
    private String filePath;
    private boolean isBackRotated180;
    private int rotation;
    private Date scanDateTime;
    private String examUploaderName;
    private String studentExamNum;
    private long studentId;
    private String studentNum;
    private String studentName;
    private String correctParam;
    private String schoolName;
    private long examId;
    private long paperId;
    private String taskId;
    private long creatorId;
    private String creatorName;
    private Date creatorDateTime;
    private String recognitionTypeMsg;
    private String cardStudentName;
    private int templateType;
    private long templateCode;
    private int pageNumber;
    private long scannerId;
    private String scanner;
    private String correctPath;
    private int correctStatus;

    private long extStudentId;
    private boolean usedExamItem;
    private int needErrorCodeStatus;
    private int unRepeatErrorCode;
    private int errorCodeStatus;
    private int onlyRepeat;

    public void handleRotated() {
        String correctParam = this.getCorrectParam();
        if (StringUtils.isEmpty(correctParam))
            return;
        this.correctParam = null;
        Map<String, Object> data = JSONUtil.parseToMap(correctParam);
        Map<String, Object> setting = MapUtil.getMap(data, "setting");
        if (MapUtils.isEmpty(setting))
            return;
        Map<String, Object> frontCorrectionParam = MapUtil.getMap(setting, "frontCorrectionParam");
        if (MapUtils.isEmpty(frontCorrectionParam))
            return;
        this.isBackRotated180 = MapUtil.getBoolean(frontCorrectionParam, "isBackRotated180", false);

        int  rotationAkaze = MapUtil.getInt(frontCorrectionParam, "rotationAkaze", -1);
        if(rotationAkaze!=-1){
            this.rotation = rotationAkaze;
        }else{
            this.rotation = MapUtil.getInt(frontCorrectionParam, "rotation", 0);
        }

    }

    public Map<String, Object> handleImageMap() {
        Map<String, Object> it = new HashMap<>();
        it.put("templateType", this.getTemplateType());
        it.put("templateCode", this.getTemplateCode());
        it.put("answerCardId", getAnswerCardId());
        return it;
    }

    public static int sortedCard(AnswerCardVO first, AnswerCardVO second) {
        long f = first.getScanDateTime().getTime();
        long s = second.getScanDateTime().getTime();
        long disMilliTimes = f - s;
        int value = 0;
        if (disMilliTimes == 0) {
            long disBatchId = first.getBatchId() - second.getBatchId();
            if (disBatchId == 0) {
                value = first.getFileName().compareTo(second.getFileName());
            } else {
                return disBatchId < 0 ? -1 : 1;
            }
        } else {
            value = disMilliTimes < 0 ? -1 : 1;
        }
        return value;
    }

    public String getAnswerCardKey() {
        return this.examUploaderId + "_" + this.answerCardCode;
    }

    public boolean isRecognized() {
        return this.getStudentId() == this.getExtStudentId();
    }
}
