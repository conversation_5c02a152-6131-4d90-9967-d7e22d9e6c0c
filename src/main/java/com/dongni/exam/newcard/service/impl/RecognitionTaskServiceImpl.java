package com.dongni.exam.newcard.service.impl;

import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.newcard.bean.RecognitionTaskBean;
import com.dongni.exam.newcard.service.IRecognitionTaskService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
public class RecognitionTaskServiceImpl implements IRecognitionTaskService {

    @Autowired
    private ExamRepository examRepository;

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void insertRecognitionTask(RecognitionTaskBean taskBean) {
        if(((taskBean.getExamUploaderId() != null && taskBean.getExamUploaderId() > 0L)  || (
                taskBean.getRecognitionTemplateId() != null && taskBean.getRecognitionTemplateId() > 0L))
                && StringUtils.isNotEmpty(taskBean.getTaskId())) {
            examRepository.insert("RecognitionTaskMapper.insertRecognitionTask", taskBean);
        }
    }

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void deleteRecognitionTask(RecognitionTaskBean taskBean) {
        if(taskBean.getExamUploaderId() > 0L) {
            examRepository.delete("RecognitionTaskMapper.deleteRecognitionTask", taskBean);
        }
    }

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void deleteRecognitionTaskByCancel(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isNumeric("abandonType")
                .verify();

        examRepository.update("RecognitionTaskMapper.updateRecognitionTaskAbandonType", params);
        examRepository.delete("RecognitionTaskMapper.deleteRecognitionTaskByCancel", params);
    }
}
