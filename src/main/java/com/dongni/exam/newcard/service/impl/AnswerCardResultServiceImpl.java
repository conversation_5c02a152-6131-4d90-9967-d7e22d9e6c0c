package com.dongni.exam.newcard.service.impl;

import com.dongni.common.report.excel.ExcelReport;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.common.report.excel.simple.SimpleExcelReport;
import com.dongni.commons.annotation.DistributeReadWriteLock;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.lock.LockMode;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.bean.TemplateInfo;
import com.dongni.exam.card.service.AnswerCardService;
import com.dongni.exam.card.util.AnswerCardUtils;
import com.dongni.exam.common.mark.serivice.mark.IQnMappingClientService;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.QnMappingVO;
import com.dongni.exam.common.mark.vo.QuestionStructureVO;
import com.dongni.exam.mark.bean.dto.ExamPaperQnScoreDTO;
import com.dongni.exam.mark.service.PaperObjItemScoreService;
import com.dongni.exam.newcard.bean.DTO.LowFeatureHeaderDTO;
import com.dongni.exam.newcard.bean.DTO.StudentEIDTO;
import com.dongni.exam.newcard.bean.VO.*;
import com.dongni.exam.newcard.dao.AnswerCardResultDao;
import com.dongni.exam.newcard.dao.NewExamItemDao;
import com.dongni.exam.newcard.service.AnswerCardServiceV3;
import com.dongni.exam.newcard.service.IAnswerCardResultService;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.bean.dto.ExamPaperDTO;
import com.dongni.exam.plan.bean.vo.ResultListVO;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.exam.plan.service.NewExamPaperService;
import com.dongni.exam.plan.service.NewPlanExamUploaderService;
import com.dongni.exam.plan.service.PlanExamUploaderService;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.enumeration.PaperAnswerStatus;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.AnswerCardTemplateManager;
import com.dongni.tiku.manager.impl.AnswerCardTemplateManualManager;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.own.service.OwnAnswerCardService;
import com.dongni.tiku.own.service.OwnPaperService;
import com.google.common.util.concurrent.AtomicDouble;
import com.limou.answercard.AnswerCard;
import jodd.log.Logger;
import jodd.log.LoggerFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @date 2024/10/09
 */
@Service
public class AnswerCardResultServiceImpl implements IAnswerCardResultService {

    private static final Logger logger = LoggerFactory.getLogger(AnswerCardResultServiceImpl.class);
    @Resource
    private AnswerCardResultDao answerCardResultDao;

    @Autowired
    private AnswerCardService answerCardService;

    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;

    @Autowired
    private PlanExamUploaderService planExamUploaderService;

    @Autowired
    private OwnPaperService ownPaperService;

    @Autowired
    private IQnMappingClientService qnMappingClientService;

    public AnswerCardResultVO getAnswerCardVO(Map<String, Object> params, int errorCode) {
        params.put("errorCode", errorCode);
        List<Long> examUploaderIds = planExamUploaderService.getExamUploaderIds(params);
        params.put("examUploaderIds", examUploaderIds);
        AnswerCardResultVO answerCardResultVO = JSONUtil.parse(JSONUtil.toJson(params), AnswerCardResultVO.class);
        answerCardResultVO.setExamUploaderIds(examUploaderIds);
        return answerCardResultVO;
    }

    /**
     * @param answerCardResultVO
     * @return 特征区低匹配接口
     */
    @Override
    public ResultListVO<StudentAnswerCardResultVO> getLowFeatureCard(AnswerCardResultVO answerCardResultVO) {
        ResultListVO<StudentAnswerCardResultVO> resultListVO = new ResultListVO<>();
        String featureImage = getManualFeatureImage(answerCardResultVO);
        resultListVO.setFeatureImage(featureImage);
        int count = answerCardResultDao.getLowFeatureCardCount(answerCardResultVO);
        resultListVO.setTotalCount(count);
        if (count < 1) {
            return resultListVO;
        }
        List<StudentAnswerCardResultVO> resultVOList = answerCardResultDao.getLowFeatureCardList(answerCardResultVO);

        Map<String, Object> params = MapUtil.of("examId", answerCardResultVO.getExamId(), "paperId", answerCardResultVO.getPaperId());
        List<TemplateInfo> templateInfos = answerCardService.getTemplateInfosByExamIdAndPaperId(params);
        Map<Long, TemplateInfo> code2TemplateInfo = templateInfos.stream().collect(toMap(l -> l.getTemplateCode(), l -> l));
        AtomicInteger index = new AtomicInteger(1);
        resultVOList.forEach(it -> {
            it.setIndex(index.get());
            index.getAndIncrement();
            long templateCode = it.getTemplateCode();
            if (code2TemplateInfo.containsKey(templateCode)) {
                it.setTemplateName(code2TemplateInfo.get(templateCode).getTemplateName());
            } else if (templateCode == 0) {
                it.setTemplateName("系统模板");
            }
            String filePath = it.getFilePath();
            String featurePath = filePath.substring(0, filePath.lastIndexOf(".")) + "/feature.png";
            it.setFeaturePath(featurePath);
        });
        resultListVO.setList(resultVOList);
        return resultListVO;
    }

    @Autowired
    private AnswerCardTemplateManualManager answerCardTemplateManualManager;

    @Autowired
    private AnswerCardTemplateManager answerCardTemplateManager;


    @Autowired
    private TikuMongodb tikuMongodb;

    public Document getRecognitionDocument(long examId, long paperId, List<Long> answerCardIds) {
        String str_answerCardIds = AnswerCardUtils.getAnswerCardIds(answerCardIds);
        Bson query = and(eq("examId", examId),
                eq("paperId", paperId),
                eq("answerCardIds", str_answerCardIds));
        Document frontTimelyProduceParam = tikuMongodb.getMongoDatabase().getCollection("frontTimelyProduceParam")
                .find(query)
                .first();

        return frontTimelyProduceParam;
    }

    private String getManualFeatureImage(AnswerCardResultVO answerCardResultVO) {
        if (answerCardResultVO.getExamId() < 1 || answerCardResultVO.getPaperId() < 1) {
            NewExamUploaderBO newExamUploaderBO = newPlanExamUploaderService.getExamUploader(answerCardResultVO.getExamUploaderIds().get(0));
            answerCardResultVO.setExamId(newExamUploaderBO.getExamId());
            answerCardResultVO.setPaperId(newExamUploaderBO.getPaperId());
        }
        if (answerCardResultVO.getTemplateCode() == null || answerCardResultVO.getTemplateCode() < 1L) {
            return null;
        }

        Bson query = and(eq("examId", answerCardResultVO.getExamId()),
                eq("paperId", answerCardResultVO.getPaperId()),
                eq("templateCode", answerCardResultVO.getTemplateCode()));
        Document document = answerCardTemplateManualManager.getFirst(query);
        if (document == null) {
            return null;
        }
        Document answerCardTemplate = document.get("answerCardTemplate", Document.class);
        if (answerCardTemplate == null) {
            return null;
        }
        List<Document> templates = answerCardTemplate.get("template", List.class);
        if (CollectionUtils.isEmpty(templates) || templates.size() < answerCardResultVO.getPageNumber()) {
            return null;
        }
        Document template = templates.get(answerCardResultVO.getPageNumber() - 1);
        Document featureRecognition = template.get("featureRecognition", Document.class);
        if (featureRecognition == null) {
            return null;
        }
        Document rect = featureRecognition.get("rect", Document.class);
        int x = ((Number) rect.get("x")).intValue();
        int y = ((Number) rect.get("y")).intValue();
        int w = ((Number) rect.get("width")).intValue();
        int h = ((Number) rect.get("height")).intValue();
        String imageUrl = "";
        if( featureRecognition.get("imageUrl") !=null) {
            imageUrl = featureRecognition.get("imageUrl", String.class);
        }else if( featureRecognition.get("imagePath") !=null) {
            imageUrl = featureRecognition.get("imagePath", String.class);
        }
        String pageNumberFeature = imageUrl.substring(0, imageUrl.lastIndexOf("."));
        pageNumberFeature += "/" + answerCardResultVO.getPageNumber() + ".png";

        if (FileStorageTemplate.exists(pageNumberFeature)) {
            return pageNumberFeature;
        }

        String featureImage = pageNumberFeature;
        FileStorageTemplate.get(imageUrl, file -> {
            String parent = file.getParent();
            String outputImage = parent + File.separator + answerCardResultVO.getPageNumber() + ".png";
            AnswerCard.getTemplate(file.getAbsolutePath(), outputImage, x, y, w, h);
            FileStorageTemplate.put(fileStoragePut -> {
                try {
                    String templateFile = fileStoragePut.getRootDir() + File.separator + answerCardResultVO.getPageNumber() + ".png";
                    File f = new File(templateFile);
                    FileUtils.copyFile(new File(outputImage), f);
                    fileStoragePut.setCustomizedFilePath(featureImage);
                    fileStoragePut.setLocalFile(f);
                    fileStoragePut.setAutoExpire(false);
                } catch (IOException e) {
                    logger.error("getManualFeatureImage method copy file error: {}", e);
                }
            });
        });
        return pageNumberFeature;
    }

    /**
     * 问题卷
     *
     * @param answerCardResultVO
     * @return
     */
    @Override
    public ResultListVO<StudentAnswerCardResultVO> getSuspectStudentPaper(AnswerCardResultVO answerCardResultVO) {
        ResultListVO<StudentAnswerCardResultVO> resultListVO = new ResultListVO<>();
        handlePrepareSuspect(answerCardResultVO, resultListVO);
        handleForceIndex(answerCardResultVO);
        if (resultListVO.getTotalCount() < 1) {
            return resultListVO;
        }
        List<StudentAnswerCardResultVO> studentAnswerCardResultVOS = answerCardResultDao.getErrorStudentPaper(answerCardResultVO);
        resultListVO.setList(studentAnswerCardResultVOS);
        return resultListVO;
    }

    private void handleForceIndex(AnswerCardResultVO answerCardResultVO) {
        int size = newPlanExamUploaderService.getQuestionNumbers(answerCardResultVO.getPaperId()).size();
        int qnsize = answerCardResultVO.getQns() != null ? answerCardResultVO.getQns().size() : 0;
        if (qnsize * 1.0 / size > 0.7) {
            answerCardResultVO.setForceIndex(1);
        }
    }

    public ResultListVO<OptionResultVO> getSuspectOptionPaper(AnswerCardResultVO answerCardResultVO) {
        ResultListVO<OptionResultVO> resultListVO = new ResultListVO<>();
        handlePrepareSuspect(answerCardResultVO, resultListVO);
        handleForceIndex(answerCardResultVO);
        if (resultListVO.getTotalCount() < 1) {
            return resultListVO;
        }
        List<OptionResultVO> optionResultVOS = answerCardResultDao.getSuspectOptionPaper(answerCardResultVO);
        if (optionResultVOS.size() > 0) {
            Set<Long> qns = new HashSet<>();
            AtomicDouble fscore = new AtomicDouble(0);
            AtomicDouble score = new AtomicDouble(0);
            AtomicInteger count = new AtomicInteger(0);
            optionResultVOS.forEach(item -> {
                item.handleRecognitionValue();
                List<Long> iqns = item.getQNs();
                item.setQuestionCount(iqns.size());
                fscore.addAndGet(item.getTotalFinallyScoreValue());
                score.addAndGet(item.getTotalScoreValue());
                if (null != iqns) {
                    qns.addAll(iqns);
                }
                if (item.getTotalScoreValue() > 0) {
                    item.setScoreRate(item.getTotalFinallyScoreValue() / item.getTotalScoreValue());
                }
                count.getAndAdd(item.getSuspectCount());
            });
            OptionResultVO vo = new OptionResultVO();
            vo.setRecognitionValue("全部异常选项");
            vo.setQuestionCount(qns.size());
            vo.setSuspectCount(count.get());
            if (score.get() > 0) {
                vo.setScoreRate(fscore.get() / score.get());
            }
            optionResultVOS.add(0, vo);
        }
        resultListVO.setList(optionResultVOS);
        return resultListVO;
    }

    private void handlePrepareSuspect(AnswerCardResultVO answerCardResultVO, ResultListVO<?> resultListVO) {
        int count = answerCardResultDao.getErrorCardCount(answerCardResultVO);
        resultListVO.setTotalCount(count);
        if (count < 1) {
            return;
        }
        handleExamIdAndPaperId(answerCardResultVO);
        List<Long> qns = newPlanExamUploaderService.getObjectiveQuestionNumbers(answerCardResultVO.getPaperId());
        answerCardResultVO.setQns(qns);
    }

    private void handleExamIdAndPaperId(AnswerCardResultVO answerCardResultVO) {
        long paperId = answerCardResultVO.getPaperId();
        long examId = answerCardResultVO.getExamId();
        if (paperId == 0 || examId == 0) {
            long examUploaderId = answerCardResultVO.getExamUploaderIds().get(0);
            NewExamUploaderBO examUploaderBO = newPlanExamUploaderService.getExamUploader(examUploaderId);
            answerCardResultVO.setExamId(examUploaderBO.getExamId());
            answerCardResultVO.setPaperId(examUploaderBO.getPaperId());
        }
    }

    @Override
    public ResultListVO<SuspectExamItemVO> getSuspectOptionList(AnswerCardResultVO answerCardResultVO) {
        ResultListVO<SuspectExamItemVO> resultListVO = new ResultListVO<>();
        handleExamIdAndPaperId(answerCardResultVO);
        handleForceIndex(answerCardResultVO);
        List<Long> qns = newPlanExamUploaderService.getObjectiveQuestionNumbers(answerCardResultVO.getPaperId());
        answerCardResultVO.setQns(qns);
        int count = answerCardResultDao.getSuspectOptionListCount(answerCardResultVO);
        resultListVO.setTotalCount(count);
        if (count < 1) {
            return resultListVO;
        }

        // 根据总条数调整currentIndex, pageNo字段
        answerCardResultVO.handlePageInfo(count);
        List<SuspectExamItemVO> rsList = answerCardResultDao.getSuspectOptionList(answerCardResultVO);
        List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(answerCardResultVO.getPaperId());

        List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(answerCardResultVO.getPaperId());
        Map<Integer, List<QnMappingVO>> markQn2PaperQn = qnMappingList.stream().collect(groupingBy(QnMappingVO::getMarkQn));
        Map<Integer, List<SuspectExamItemVO>> rsMap = rsList.stream().collect(groupingBy(SuspectExamItemVO::getQuestionNumber));

        for (Map.Entry<Integer, List<SuspectExamItemVO>> entry : rsMap.entrySet()) {
            Integer qn = entry.getKey();
            List<SuspectExamItemVO> itemVOs = entry.getValue();
            QuestionStructureVO questionStructureVO = questionStructureVOS.stream().filter(q -> q.getQuestionNumber() == qn).findFirst().orElse(null);
            if (null != questionStructureVO) {
                List<Map<String, Object>> structureNumberList = ownPaperService.getPaperStructureById(answerCardResultVO.getPaperId());
                Map<Integer,Integer> qn2OptionsCountMap = structureNumberList
                        .stream()
                        .filter(item -> ObjectUtil.isValueEquals(item.get("readType"), 1))
                        .collect(Collectors.toMap(item-> Integer.valueOf(item.get("questionNumber").toString()) ,
                                item->  Integer.valueOf(item.get("optionsCount").toString()) ));
                int optionsCount;
                int paperQuestionNumber;
                if(markQn2PaperQn.get(questionStructureVO.getQuestionNumber()) !=null){
                    paperQuestionNumber = markQn2PaperQn.get(questionStructureVO.getQuestionNumber()).get(0).getPaperQn();
                }else{
                    paperQuestionNumber = questionStructureVO.getQuestionNumber();
                }
                if(qn2OptionsCountMap.get(paperQuestionNumber)!=null){
                    optionsCount = qn2OptionsCountMap.get(paperQuestionNumber);
                }else{
                    optionsCount = questionStructureVO.getOptionsCount();
                }
                itemVOs.forEach(vo -> {
                    vo.setUnitType(questionStructureVO.getUnitType());
                    vo.setOptionsCount(optionsCount);
                });
            }
        }
        resultListVO.setList(rsList);
        return resultListVO;
    }

    @Autowired
    private PaperObjItemScoreService paperObjQusScoreService;

    @Autowired
    private PaperManager paperManager;

    /**
     * 批量修改逻辑.
     *
     * @param batchExamItemVO
     */
    @Override
    @DistributeReadWriteLock(name = "EXAM:RECOGNITION:STATUS", argValueKeys = {"[0].examId", "[0].paperId"}, expireTime = 60, lockMode = LockMode.EXCLUSIVE_MODE)
    @Transactional(ExamRepository.TRANSACTION)
    public void batchUpdateObjectiveItems(BatchExamItemVO batchExamItemVO) {
        List<SuspectExamItemVO> itemVOList = batchExamItemVO.getItems();
        if (null == itemVOList || itemVOList.size() < 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "提交修改的items集合为空！");
        }

        long paperId = itemVOList.get(0).getPaperId();
        Document paper = paperManager.getPaper(paperId);
        // 算分.
        int answer = 0;
        if (ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS.getStatus())) {
            answer = 1;
            ExamPaperQnScoreDTO examPaperQnScoreDTO = new ExamPaperQnScoreDTO();
            examPaperQnScoreDTO.setExamId(itemVOList.get(0).getExamId());
            examPaperQnScoreDTO.setPaperId(paperId);
            examPaperQnScoreDTO.setQnScores(new ArrayList<>());
            Map<Integer, List<SuspectExamItemVO>> qn2ListMap = itemVOList.stream().collect(groupingBy(SuspectExamItemVO::getQuestionNumber));

            for (Map.Entry<Integer, List<SuspectExamItemVO>> entry : qn2ListMap.entrySet()) {
                ExamPaperQnScoreDTO.QnScore qnScore = new ExamPaperQnScoreDTO.QnScore();
                qnScore.setQuestionNumber(entry.getKey());
                List<SuspectExamItemVO> values = entry.getValue();
                Map<String, List<SuspectExamItemVO>> rvListInfo = values.stream().collect(groupingBy(it -> it.getRecognitionValue()));
                List<ExamPaperQnScoreDTO.QnScore.RecognitionValueScore> rvScores = new ArrayList<>();
                for (Map.Entry<String, List<SuspectExamItemVO>> rv : rvListInfo.entrySet()) {
                    ExamPaperQnScoreDTO.QnScore.RecognitionValueScore recognitionValueScore = new ExamPaperQnScoreDTO.QnScore.RecognitionValueScore();
                    recognitionValueScore.setRecognitionValue(rv.getKey());
                    rvScores.add(recognitionValueScore);
                }
                qnScore.setRecognitionValueScores(rvScores);
                examPaperQnScoreDTO.getQnScores().add(qnScore);
            }
            examPaperQnScoreDTO = paperObjQusScoreService.getExamPaperQnScore(examPaperQnScoreDTO);

            // 赋分
            Map<Integer, List<SuspectExamItemVO>> qn2ItemMap = itemVOList.stream().collect(groupingBy(SuspectExamItemVO::getQuestionNumber));
            for (Map.Entry<Integer, List<SuspectExamItemVO>> entry : qn2ItemMap.entrySet()) {
                ExamPaperQnScoreDTO.QnScore qnScore = examPaperQnScoreDTO.getQnScores().stream().filter(it -> it.getQuestionNumber().equals(entry.getKey())).findFirst().orElse(null);
                if (qnScore == null) {
                    throw new CommonException(ResponseStatusEnum.DATA_ERROR, "questionNumber " + entry.getKey() + "not found!");
                }
                List<ExamPaperQnScoreDTO.QnScore.RecognitionValueScore> scores = qnScore.getRecognitionValueScores();
                Map<String, List<SuspectExamItemVO>> rv2ItemsMap = entry.getValue().stream().collect(groupingBy(SuspectExamItemVO::getRecognitionValue));
                for (Map.Entry<String, List<SuspectExamItemVO>> rv : rv2ItemsMap.entrySet()) {
                    String recv = rv.getKey();
                    ExamPaperQnScoreDTO.QnScore.RecognitionValueScore score = scores.stream().filter(it -> it.getRecognitionValue().equals(recv)).findFirst().orElse(null);
                    if (score == null) {
                        throw new CommonException(ResponseStatusEnum.DATA_ERROR, "questionNumber " + entry.getKey()
                                + ", value = " + recv + " not found in scores list");
                    }
                    rv.getValue().forEach(it -> {
                        it.setFinallyScore(score.getScore());
                    });
                }
            }

            Set<Long> examUploaderIds = itemVOList.stream().filter(x -> x.getExamUploaderId() > 0L).map(SuspectExamItemVO::getExamUploaderId).collect(toSet());
            if (examUploaderIds.size() > 0) {
                List<NewExamUploaderBO> uploaderBOList = newPlanExamUploaderService.getExamUploaderListByIds(examUploaderIds);
                Map<Long, NewExamUploaderBO> examUploaderBOMap = uploaderBOList.stream().collect(toMap(l -> l.getExamUploaderId(), l -> l));
                itemVOList.forEach(it -> {
                    NewExamUploaderBO uploaderBO = examUploaderBOMap.get(it.getExamUploaderId());
                    if (uploaderBO != null) {
                        it.setReadStatus(uploaderBO.isCompleted() ? 1 : 11);
                    } else {
                        it.setReadStatus(10);
                    }
                });
            }
        }

        newExamItemDao.batchUpdateObjectiveItems(itemVOList, answer);
        Set<Long> studentIds = itemVOList.stream().map(SuspectExamItemVO::getStudentId).collect(toSet());
        List<Long> qns = newPlanExamUploaderService.getObjectiveQuestionNumbers(paperId);
        long examId = itemVOList.get(0).getExamId();
        List<StudentEIDTO> studentEIDTOList = newExamItemDao.getStudentEIList(examId, paperId, studentIds, qns);
        studentEIDTOList.forEach(StudentEIDTO::handleErrorCode);
        if (batchExamItemVO.getExamUploaderId() > 0) {
            answerCardResultDao.batchUpdateStudentByExamUploaderId(batchExamItemVO.getExamUploaderId(), studentEIDTOList);
        } else {
            answerCardResultDao.batchUpdateStudent(examId, paperId, studentEIDTOList);
        }
    }

    @Resource
    private NewExamItemDao newExamItemDao;

    @Autowired
    private ExamService examService;

    @Autowired
    private NewExamPaperService newExamPaperService;

    @Override
    public String dlExceptionObjectiveItems(AnswerCardResultVO answerCardResultVO) {
        if (answerCardResultVO.getExamId() < 1 || answerCardResultVO.getPaperId() < 1) {
            NewExamUploaderBO newExamUploaderBO = newPlanExamUploaderService.getExamUploader(answerCardResultVO.getExamUploaderIds().get(0));
            answerCardResultVO.setExamId(newExamUploaderBO.getExamId());
            answerCardResultVO.setPaperId(newExamUploaderBO.getPaperId());
        }
        List<Long> singleQNs = newPlanExamUploaderService.getSingleObjectiveQNs(answerCardResultVO.getPaperId());
        List<Long> qns = newPlanExamUploaderService.getObjectiveQuestionNumbers(answerCardResultVO.getPaperId());
        answerCardResultVO.setQns(qns);
        handleForceIndex(answerCardResultVO);
        if (qns.size() < 1) {
            return null;
        }
        // 加上一个任意的值，
        if (singleQNs.size() < 1) {
            singleQNs.add(0L);
        }
        List<StudentExceptionObjectiveVO> students = answerCardResultDao.dlExceptionObjectiveItems(answerCardResultVO.getExamId(), answerCardResultVO.getPaperId(),
                answerCardResultVO.getExamUploaderIds(), qns, singleQNs, answerCardResultVO.getForceIndex());
        List<String> headers = Arrays.asList("schoolName", "studentName", "studentExamNum", "className", "missedCount", "muchCount", "missedStructureNumbers", "muchStructureNumbers");
        List<String> headerNames = Arrays.asList("学校名称", "考生姓名", "考号", "考生班级", "漏填数", "多填数", "漏填题号", "多填题号");
        SimpleExcelHeader header = new SimpleExcelHeader(headers, headerNames);
        ExcelReport report = new SimpleExcelReport(students.stream().map(StudentExceptionObjectiveVO::getStudentInfo).collect(toList()), header);
        Map<String, Object> examInfo = examService.getExamDetail(MapUtil.of("examId", answerCardResultVO.getExamId()));
        ExamPaperDTO examPaperDTO = newExamPaperService.getExamPaperDTOOfExamIdAndPaperId(answerCardResultVO.getExamId(), answerCardResultVO.getPaperId());
        String fileName = MapUtil.getString(examInfo, "examName", "");
        fileName += "-" + examPaperDTO.getCourseName();
        fileName += "-客观题漏填多填学生名单";
        return report.exportToFileStorage(fileName);
    }

    @Autowired
    private IQsClientService qsClientService;

    /**
     * 返回异常选项对应的模板数据.
     *
     * @param answerCardResultVO
     * @return
     */
    @Override
    public List<TemplateQNVO> getSuspectOptionTemplates(AnswerCardResultVO answerCardResultVO) {
        handleExamIdAndPaperId(answerCardResultVO);
        List<Long> qns = newPlanExamUploaderService.getObjectiveQuestionNumbers(answerCardResultVO.getPaperId());
        answerCardResultVO.setQns(qns);
        handleForceIndex(answerCardResultVO);
        List<TemplateQNVO> list = answerCardResultDao.getSuspectOptionTemplates(answerCardResultVO);
        Bson query = and(eq("examId", answerCardResultVO.getExamId()),
                eq("paperId", answerCardResultVO.getPaperId()));

        long count = list.stream().filter(it -> it.getTemplateCode() > 0).count();
        List<Map<String, Object>> templates = null;
        if (count > 0) {
            templates = answerCardTemplateManualManager.getListMap(query);
        }
        List<Map<String, Object>> finalTemplates = templates;
        list.forEach(x -> {
            x.handleQns(qsClientService.listQuestionStructure(answerCardResultVO.getPaperId()));
            Map<String, Object> item = count > 0 ? finalTemplates.stream().filter(it -> MapUtil.getLong(it, "templateCode") == (x.getTemplateCode())).findFirst().orElse(null) : null;
            if (item != null) {
                x.setTemplateName(MapUtil.getString(item, "templateName", String.valueOf(x.getTemplateCode())));
            } else {
                x.setTemplateName("系统模板");
            }
        });

        return list;
    }

    @Autowired
    private OwnAnswerCardService ownAnswerCardService;

    @Override
    public LowFeatureHeaderVO getLowFeatureHeader(AnswerCardResultVO answerCardResultVO) {
        List<LowFeatureHeaderDTO> headerDTOList = answerCardResultDao.getLowFeatureHeader(answerCardResultVO);
        handleExamIdAndPaperId(answerCardResultVO);
        Bson query = and(eq("examId", answerCardResultVO.getExamId()),
                eq("paperId", answerCardResultVO.getPaperId()));
        List<Map<String, Object>> templates = answerCardTemplateManualManager.getListMap(query);
        Map<String, Object> answerCardTemplate = ownAnswerCardService.getAnswerCardTemplate(MapUtil.of("paperId", answerCardResultVO.getPaperId()));
        LowFeatureHeaderVO lowFeatureHeaderVO = new LowFeatureHeaderVO();
        lowFeatureHeaderVO.setTemplates(new ArrayList<>());
        Map<Long, List<LowFeatureHeaderDTO>> template2ListInfo = headerDTOList.stream().collect(Collectors.groupingBy(LowFeatureHeaderDTO::getTemplateCode));
        Map<Long, List<LowFeatureHeaderDTO>> batch2ListInfo = headerDTOList.stream().collect(Collectors.groupingBy(LowFeatureHeaderDTO::getBatchId));
        for (Long templateCode : template2ListInfo.keySet()) {
            LowFeatureHeaderVO.TemplateInfo it = new LowFeatureHeaderVO.TemplateInfo();
            it.setTemplateCode(templateCode);
            Map<String, Object> item = templates.stream().filter(x -> MapUtil.getLong(x, "templateCode") == templateCode).findFirst().orElse(null);
            if (item != null) {
                it.setTemplateName(MapUtil.getString(item, "templateName", ""));
                Document cardTemplate = (Document) item.get("answerCardTemplate");
                if (Objects.nonNull(cardTemplate)) {
                    List<Document> tpls = Optional.ofNullable((List<Document>) cardTemplate.get("template")).orElse(Collections.emptyList());
                    it.setTemplateNumber(tpls.size());
                }
            } else {
                it.setTemplateName("系统模板");
                if (Objects.nonNull(answerCardTemplate)) {
                    List<Document> tpls = Optional.ofNullable((List<Document>) answerCardTemplate.get("template")).orElse(Collections.emptyList());
                    it.setTemplateNumber(tpls.size());
                }
            }
            it.setPageNumbers(new ArrayList<>(template2ListInfo.get(templateCode).stream().map(x -> x.getPageNumber()).collect(toSet())));
            lowFeatureHeaderVO.getTemplates().add(it);
        }
        lowFeatureHeaderVO.setBatches(batch2ListInfo.entrySet().stream().map(x -> {
            LowFeatureHeaderVO.BatchInfo it = new LowFeatureHeaderVO.BatchInfo();
            it.setBatchId(x.getKey());
            it.setBatchName(x.getValue().get(0).getBatchName());
            return it;
        }).collect(toList()));
        return lowFeatureHeaderVO;
    }

    public void handleStudentErrorCode(long examId, long paperId, List<Long> studentIds) {
        List<Long> qns = newPlanExamUploaderService.getObjectiveQuestionNumbers(paperId);
        List<StudentEIDTO> studentEIDTOList = newExamItemDao.getStudentEIList(examId, paperId, new HashSet<>(studentIds), qns);
        studentEIDTOList.forEach(StudentEIDTO::handleErrorCode);
        answerCardResultDao.batchUpdateStudent(examId, paperId, studentEIDTOList);
    }

    @Autowired
    private AnswerCardServiceV3 answerCardServiceV3;

    public List<QuestionNumberTemplateVO> getQNTVOList(long examUploaderId, long studentId, List<Integer> questionNumbers) {
        List<QuestionNumberTemplateVO> rets = getQNTVOList2(examUploaderId, studentId, questionNumbers);
        Map<Integer, List<QuestionNumberTemplateVO>> qnInfo = rets.stream().collect(groupingBy(QuestionNumberTemplateVO::getQuestionNumber));

        List<Long> answerCardIds = answerCardServiceV3.getStudentAnswerCardIdsByStudentId(examUploaderId, studentId);
        NewExamUploaderBO newExamUploaderBO = newPlanExamUploaderService.getExamUploader(examUploaderId);
        Document doc = getRecognitionDocument(newExamUploaderBO.getExamId(), newExamUploaderBO.getPaperId(), answerCardIds);
        if (doc != null) {
            List<Document> templates = doc.get("frontTimelyProduceGroup", List.class);
            for (int i = 0; i < templates.size(); i++) {
                List<Document> recognitionContent = templates.get(i).get("recognitionContent", List.class);
                if (CollectionUtils.isEmpty(recognitionContent)) {
                    continue;
                }
                for (int j = 0; j < recognitionContent.size(); j++) {
                    Integer qn = recognitionContent.get(j).get("questionNumber", Integer.class);
                    if (qn != null && questionNumbers.contains(qn)) {
                        if (qnInfo.containsKey(qn)) {
                            List<QuestionNumberTemplateVO> voList = qnInfo.get(qn);
                            List<Document> blocks = voList.get(0).getInfo();
                            if (voList.get(0).getPageNumber() == 0) {
                                int pageNumber = i + 1;
                                voList.forEach(x -> x.setPageNumber(pageNumber));
                            }
                            for (int k = 0; blocks!=null && k < blocks.size(); k++) {
                                Document block = blocks.get(k);
                                Integer updateRect = block.getInteger("updateRect", 0);
                                if (updateRect == 0) {
                                    block.put("rect", recognitionContent.get(j).get("rect", Document.class));
                                    block.put("updateRect", 1);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        return rets;
    }


    public List<QuestionNumberTemplateVO> getQNTVOList2(long examUploaderId, long studentId, List<Integer> questionNumbers) {
        TemplateInfo templateInfo = answerCardServiceV3.getStudentCardTemplate(examUploaderId, studentId);
        if (null == templateInfo) {
            return new ArrayList<>();
        }

        NewExamUploaderBO newExamUploaderBO = newPlanExamUploaderService.getExamUploader(examUploaderId);
        long templateCode = templateInfo.getTemplateCode();
        List<Document> templates;
        Document choiceRect = null;
        if (templateCode == 0) {
            Bson query = eq("paperId", newExamUploaderBO.getPaperId());
            Document document = answerCardTemplateManager.getFirst(query);
            Document setting = document.get("setting", Document.class);
            choiceRect = setting.get("choiceRect", Document.class);
            templates = document.get("template", List.class);
        } else {
            Bson query = and(eq("examId", newExamUploaderBO.getExamId()),
                    eq("paperId", newExamUploaderBO.getPaperId()),
                    eq("templateCode", templateCode));
            Document document = answerCardTemplateManualManager.getFirst(query);
            Document answerCardTemplate = document.get("answerCardTemplate", Document.class);
            if (answerCardTemplate == null) {
                return null;
            }
            templates = answerCardTemplate.get("template", List.class);
        }

        List<Document> result = new ArrayList<>();
        for (int i = 0; i < templates.size(); i++) {
            List<Document> cardContent = templates.get(i).get("cardContent", List.class);
            for (int j = 0; j < cardContent.size(); j++) {
                if (cardContent.get(j).containsKey("questionNumber")) {
                    Integer qn = cardContent.get(j).get("questionNumber", Integer.class);
                    if (qn != null && questionNumbers.contains(qn)) {
                        if (choiceRect != null) {
                            cardContent.get(j).put("choiceRect", choiceRect);
                        }
                        result.add(cardContent.get(j));
                    }
                }
            }
        }

        List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(newExamUploaderBO.getPaperId());

        List<Map<String, Object>> structureNumberList = ownPaperService.getPaperStructureById(newExamUploaderBO.getPaperId());

        List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(newExamUploaderBO.getPaperId());
        Map<Integer, List<QnMappingVO>> markQn2PaperQn = qnMappingList.stream().collect(groupingBy(QnMappingVO::getMarkQn));


        Map<Integer,Integer> qn2OptionsCountMap = structureNumberList
                .stream()
                .filter(item -> ObjectUtil.isValueEquals(item.get("readType"), 1))
                .collect(Collectors.toMap(item-> Integer.valueOf(item.get("questionNumber").toString()) ,
                        item->  Integer.valueOf(item.get("optionsCount").toString()) ));

        Map<Integer, QuestionStructureVO> qnInfo = questionStructureVOS.stream().collect(toMap(l -> l.getQuestionNumber(), l -> l));
        List<QuestionNumberTemplateVO> rets = new ArrayList<>();
        Map<Integer, List<Document>> qnListInfo = result.stream().collect(groupingBy(it -> it.get("questionNumber", Integer.class)));
        for (Integer markQn : questionNumbers) {
            QuestionNumberTemplateVO it = new QuestionNumberTemplateVO();
            int paperQn;
            if(markQn2PaperQn.get(markQn) !=null){
                paperQn = markQn2PaperQn.get(markQn).get(0).getPaperQn();
            }else{
                paperQn = markQn;
            }
            it.setQuestionNumber(markQn);
            it.setInfo(qnListInfo.get(markQn));
            int optionsCount;
            if(qn2OptionsCountMap.get(paperQn)!=null){
                optionsCount = qn2OptionsCountMap.get(paperQn);
            }else{
                optionsCount = qnInfo.get(markQn).getOptionsCount();
            }
            it.setOptionsCount(optionsCount);
            rets.add(it);
        }
        return rets;
    }
}
