package com.dongni.exam.newcard.service.impl;

import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.service.AnswerCardService;
import com.dongni.exam.newcard.service.IAnswerCardExtraService;
import com.dongni.tiku.common.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Deprecated
public class AnswerCardExtraServiceImpl implements IAnswerCardExtraService {

    private final static Logger log = LoggerFactory.getLogger(AnswerCardService.class);

    @Autowired
    private ExamRepository commonRepository;

    @Override
    public void deleteCardsByZip(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();

        commonRepository.delete("AnswerCardExtraMapper.deleteAllAnswerCards", params);
    }

    @Override
    public void deleteCardsByCards(Map<String, Object> params) {
        Verify.of(params).isValidId("examUploaderId").verify();
        List<Long> answerCardIds = (List<Long>) params.get("answerCardIds");
        if (answerCardIds != null && answerCardIds.size() > 0){
            commonRepository.delete("AnswerCardExtraMapper.deleteAnswerCardsByIds", params);
        }
    }

    @Override
    public void cancelRecognition(Map<String, Object> params) {
        Verify.of(params).isValidId("examUploaderId").verify();
        commonRepository.update("AnswerCardExtraMapper.resetEmptyRecogInfo", params);
    }

    @Override
    public void deleteCompleteExamUploader(Map<String, Object> params) {
        Verify.of(params).isValidId("examUploaderId").verify();
        commonRepository.delete("AnswerCardExtraMapper.deleteNormalAnswerCards", params);
    }
}
