package com.dongni.exam.newcard.service.impl;

import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.common.UploaderSchedulerMapper;
import com.dongni.exam.card.constant.AnswerCardConstants;
import com.dongni.exam.card.service.AnswerCardBatchService;
import com.dongni.exam.newcard.bean.BO.FileContinueErrCountBO;
import com.dongni.exam.newcard.service.ICardRecognitionService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 首次识别的答题卡；
 */
@Service
public class FirstCardImpl implements ICardRecognitionService {
    protected final Logger log = LoggerFactory.getLogger(FirstCardImpl.class);

    @Autowired
    private AnswerCardBatchService answerCardBatchService;

    @Autowired
    private ExamRepository commonRepository;

    @Autowired
    private UploaderSchedulerMapper schedulerMapper;

    @Override
    public boolean getCardInfo(Map<String, Object> params, Map<String, Object> cardInfo) {
        long examUploaderId = MapUtil.getLong(cardInfo, "examUploaderId");
        Timestamp taskTime = (Timestamp) params.get("taskTime");
        Timestamp scanTime = (Timestamp) params.get("scanTimeDate");
        long disTime = taskTime != null ? taskTime.getTime() : scanTime.getTime();
        disTime = (new Date().getTime() - disTime) / 1000 / 60;
        int templateNumber = MapUtil.getInt(cardInfo, "templateNumber");
        int maxSize = templateNumber * AnswerCardConstants.MAX_CARD_QUERY_SIZE;
        List<Map<String, Object>> cardList = schedulerMapper.queryAnswerCards(examUploaderId, "FR", maxSize);
        if (CollectionUtils.isEmpty(cardList)) {
            return false;
        }
        List<Map<String, Object>> batchInfos = commonRepository.selectList("NewAnswerCardMapper.getExamUploaderBatches", params);
        Map<Long, List<Map<String, Object>>> batchCardsMap = cardList.stream().collect(groupingBy(item -> MapUtil.getLong(item, "batchId")));
        Map<Integer, List<Map<String, Object>>> templateCardsMap = new HashMap<>();
        //获取批次对应的文件连续性错误数量
        List<FileContinueErrCountBO> errLists = commonRepository.selectList("AnswerCardMapper.getFileContinueErrCount", Collections.singletonMap("examUploaderId", examUploaderId));
        Map<Long, Long> errFileContinueMap = errLists.stream().collect(Collectors.toMap(FileContinueErrCountBO::getBatchId, FileContinueErrCountBO::getErrCount));
        for (long batchId : batchCardsMap.keySet()) {
            params.put("batchId", batchId);
            List<Map<String, Object>> batchCards = batchCardsMap.get(batchId);
            //文件连续性可能存在问题跳过
            Long errCount = errFileContinueMap.getOrDefault(batchId, 0L);
            if (errCount.compareTo(0L) > 0) {
                continue;
            }
            // 如果批次是重组中，那么就需要跳过
            boolean isNeedRecombineCard = batchInfos.stream().anyMatch(item -> {
                long id = MapUtil.getLong(item, "batchId");
                int needRecombineCard = MapUtil.getInt(item, "needRecombineCard", 0);
                return id == batchId && needRecombineCard == 1;
            });
            if (isNeedRecombineCard) {
                continue;
            }
            Map<Integer, List<Map<String, Object>>> templateTypeCardsMap = batchCards.stream().collect(groupingBy(item -> MapUtils.getInteger(item, "templateType")));
            for (Integer templateType : templateTypeCardsMap.keySet()) {
                List<Map<String, Object>> cards = templateTypeCardsMap.get(templateType);
                int total = cards.size();
                if (total >= AnswerCardConstants.CARD_TASK_GROUP_SIZE * templateNumber
                        || disTime >= 1
                        || answerCardBatchService.isBatchFinished(params)
                ) {
                    if (CollectionUtils.isNotEmpty(cards)) {
                        log.info("初次识别的答题卡： {}, cards size {}, templateType: {}" , batchId, cards.size(), templateType);
                        cardInfo.put("phase", "start");
                        List<Map<String, Object>> templateCardList = templateCardsMap.computeIfAbsent(templateType,k->new ArrayList<>());
                        if(CollectionUtils.isNotEmpty(templateCardList)) {
                            boolean isFileNameRepeat = cards.stream().anyMatch(card -> {
                                String fileName = MapUtil.getString(card, "fileName", "");
                                return templateCardList.stream().anyMatch(tcard -> MapUtil.getString(tcard,"fileName").equals(fileName));
                            });
                            if(isFileNameRepeat) {
                                continue;
                            } else {
                                templateCardList.addAll(cards);
                            }
                        } else {
                            templateCardList.addAll(cards);
                        }
                    }
                }
            }
        }

        if (MapUtils.isEmpty(templateCardsMap)) {
            return false;
        }

        Set<Integer> set = templateCardsMap.keySet();
        Integer type = set.stream().findFirst().orElse(null);
        cardInfo.put("cards", templateCardsMap.get(type));
        return true;
    }

    @Override
    public int getWeight() {
        return 2;
    }
}
