package com.dongni.exam.newcard.service.impl;

import com.dongni.basedata.export.todo.service.CommonTodoService;
import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.service.AnswerCardBatchService;
import com.dongni.exam.common.mark.serivice.exam.IExamPaperClientService;
import com.dongni.exam.common.mark.vo.ExamPaperInfoVO;
import com.dongni.exam.common.mark.vo.ExamVO;
import com.dongni.exam.mark.bean.vo.req.ClassMarkPaperReq;
import com.dongni.exam.mark.service.ExamMarkTodoService;
import com.dongni.exam.mark.service.IMarkCompleteService;
import com.dongni.exam.newcard.parse.enumeration.AnswerCardErrorTypeEnum;
import com.dongni.exam.newcard.service.*;
import com.dongni.exam.newcard.service.cache.base.RecognitionDataService;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.service.*;
import com.dongni.exam.recognize.service.RecognitionControlService;
import com.dongni.newmark.manager.ExamManager;
import com.dongni.newmark.manager.ExamSchoolPaperManager;
import com.dongni.tiku.common.service.TikuManualService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <br>
 * 2021/12/08 <br>
 *
 */
@Service
public class NewAnswerCardProcessServiceImpl implements INewAnswerCardProcessService {

    private final static Logger LOGGER = LoggerFactory.getLogger(NewAnswerCardProcessServiceImpl.class);

    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private ExamUploaderService examUploaderService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private ExamMarkTodoService examMarkTodoService;

    @Autowired
    private RecognitionDataService recognitionDataService;
    @Autowired
    private NewAnswerCardConfigService newAnswerCardConfigService;
    @Autowired
    private NewAnswerCardZipService newAnswerCardZipService;
    @Autowired
    private ExamService examService;

    @Autowired
    private RecognitionControlService recognitionControlService;

    @Autowired
    private TikuManualService tikuManualService;

    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;

    @Autowired
    private IAnswerCardExtraService answerCardExtraService;
    // ----------------------------------------------------------------------------------- 取消识别 (游离操作，随时取消)

    // ---------------------------------------------------------------- 客户端取消识别


    @Autowired
    private AnswerCardBatchService answerCardBatchService;

    @Autowired
    private CommonTodoService commonTodoService;



    @Autowired
    private IMarkCompleteService markCompleteService;

    @Autowired
    private IExamPaperClientService examPaperClientService;

    @Autowired
    private AnswerCardServiceV3 answerCardServiceV3;

    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;

    @Autowired
    private NewExamResultService newExamResultService;

    @Autowired
    private ExamManager examManager;

    @Autowired
    private ExamSchoolPaperManager examSchoolPaperManager;

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void cancelRecognizeForClient(Map<String, Object> params) {
        Verify.of(params)
                    .isValidId("examUploaderId")
                .isValidId("userId")
                .verify();
        int count = examRepository.selectOne("NewAnswerCardMapper.getExamUploaderCardCount", params);
        if(count > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "存在答题卡，无法终止扫描，如果需要终止扫描，请在批次列表删除批次！");
        }

        long examUploaderId = MapUtil.getLong(params, "examUploaderId");
        newExamResultService.checkExamResultUploadedStatus(examUploaderId);
        params.put("action", "lock");
        params.put("abandonType", 1);
        answerCardBatchService.handleActionExamUploader(params);
        // 主要需要 answerCardPath 用来删除文件
        NewExamUploaderBO newExamUploaderBO = newPlanExamUploaderService.getExamUploader(examUploaderId);
        if (newExamUploaderBO == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "获取不到答题卡上传人信息");
        }
        Map<String, Object> examPaper = examPaperService.getExamPaperByExamUploaderId(params);
        List<Map<String, Object>> examUploaderFileList = newAnswerCardZipService.getUploadZip(MapUtil.of("examUploaderId", examUploaderId));
        String answerCardPath = newExamUploaderBO.getAnswerCardPath();
        int uploadType = newExamUploaderBO.getUploadType();
        // 终止某个扫描任务.
        long examId = MapUtil.getLong(examPaper, "examId");
        long paperId = MapUtil.getLong(examPaper, "paperId");
        long userId = MapUtil.getLong(params, "userId");
        String userName = MapUtil.getString(params, "userName");
        answerCardServiceV3.deleteAnswerCardByExamUploaderId(examUploaderId);
        boolean isSaveExamUploader = isDeletedByAbsent(examUploaderId);
        if(!isSaveExamUploader) {
            answerCardBatchService.deleteBatchesForCancel(params);
            params.put("uploadStatus", DictUtil.getDictValue("examUploadStatus", "notUploaded"));
            examRepository.delete("NewAnswerCardProcessMapper.deleteExamUploader", params);
            // 把终止扫描的图片给转到正常扫描任务，这样会导致丢图
            if(!answerCardServiceV3.isCardTransform(newExamUploaderBO)) {
                myAsyncConfigurer.getAsyncExecutor().execute(() -> asyncDeleteCancelFiles(
                        answerCardPath, examUploaderFileList)
                );
            }
        } else {
            params.put("uploadStatus", DictUtil.getDictValue("examUploadStatus", "processingComplete"));
            params.put("currentTime", DateUtil.getCurrentDateTime());
            examRepository.update("NewAnswerCardProcessMapper.updateExamUploader", params);
            // 只处理联考按校的场景，某个单校的
            ExamVO exam = examManager.findByExamId(newExamUploaderBO.getExamId());
            boolean isUnion = exam.getExamType() == DictUtil.getDictValue("examType", "union") ||
                    exam.getExamType() == DictUtil.getDictValue("examType", "area");
            int readArrange = DictUtil.getDictValue("examPaperStatus", "readArrange");
            if (isUnion && newExamUploaderBO.getSchoolId() > 0) {
                long schoolId = newExamUploaderBO.getSchoolId();
                Integer schoolPaperStatus = examSchoolPaperManager.getSomeSchoolPaperStatus(examId, paperId, schoolId);
                if (schoolPaperStatus != null && schoolPaperStatus <= readArrange) {
                    int readPaper = DictUtil.getDictValue("examPaperStatus", "readPaper");
                    examSchoolPaperManager.updateStatus(examId, paperId, Collections.singletonList(schoolId), readPaper, userId, userName);
                }
            }
        }

        params.put("action", "unlock");
        answerCardBatchService.handleActionExamUploader(params);
        // 尝试结束阅卷
        ExamPaperInfoVO examPaperInfo = examPaperClientService.getExamPaperInfo(examId, paperId);
        ClassMarkPaperReq examPaperBaseReq = new ClassMarkPaperReq();
        examPaperBaseReq.setExamPaperId(String.valueOf(examPaperInfo.getExamPaperId()));
        examPaperBaseReq.setUserId(userId);
        examPaperBaseReq.setUserName(userName);
        markCompleteService.finishMark(examPaperBaseReq);
    }

    private boolean isDeletedByAbsent(long examUploaderId) {
        int count = examRepository.selectOne("AnswerCardRecycleMapper.getAbsentCount", examUploaderId);
        return count > 0;
    }


    private void asyncDeleteCancelFiles(String answerCardPath, List<Map<String, Object>> examUploaderFileList) {


        // 删除文件
        try {
            FileStorageTemplate.deleteByDirPath(answerCardPath);
        } catch (Exception e) {
            LOGGER.warn("删除文件失败: deleteByPrefix({}), cause by: {}", answerCardPath, e.getMessage());
        }
        if (CollectionUtils.isEmpty(examUploaderFileList)) {
            return;
        }


        examUploaderFileList.stream()
                .map(item -> MapUtil.getStringNullable(item, "filePath"))
                .filter(StringUtils::isNotBlank)
                .forEach(item -> {
                    try {
                        FileStorageTemplate.delete(item);
                    } catch (Exception e) {
                        LOGGER.warn("删除文件失败: delete({}), cause by: {}", item, e.getMessage());
                    }
                });
    }

    // ---------------------------------------------------------------- 服务端取消识别

    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void cancelRecognizeForServer(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .verify();
        Map<String, Object> examPaper = examPaperService.getExamPaperByExamUploaderId(params);
        ParamsUtil.setCurrentTime(params);
        long examUploaderId = MapUtil.getLong(params, "examUploaderId");

        // 如果是补传的 会返回本次需要处理的学生 这些学生都是缺考的 需要恢复现场
        List<Map<String, Object>> examStudentAbsentList = getExamStudentAbsentList(examUploaderId);

        // 先清理缓存 防止识别又过来插入数据 TODO 扫描客户端
        // cacheRecognitionService.deleteRedisEpAcInfo(examUploaderId);

        // 不删文件 文件太多，可能引起超时，如果异步删除，可能用户又重新进入识别，导入重新上传的文件被删除
        //                  重新进入识别后会覆盖到同一个位置
        //         其删除应由删除zip操作时删除整个文件夹
        // 删item  <- examUploaderId
        examRepository.delete("NewAnswerCardProcessMapper.deleteExamItemByExamUploaderId", params);

        // 更新answerCard状态为上传时的初始状态
        params.put("errorTypeCode", AnswerCardErrorTypeEnum.LOCATION_IDENTIFIER.getTypeCode());
        params.put("errorTypeName", AnswerCardErrorTypeEnum.LOCATION_IDENTIFIER.getTypeName());
        params.put("errorCode", 1<<7 );
        params.put("modifyStatus", 1);
        params.put("correctStatus", 0);
        examRepository.update("NewAnswerCardProcessMapper.updateAnswerCardToInitByExamUploaderId", params);

        // 更新t_exam_uploader t_exam_uploader_class 状态为 uploadedAndUnrecognized
        params.put("uploadStatus", DictUtil.getDictValue("examUploadStatus", "uploadedAndUnrecognized"));
        examRepository.update("NewAnswerCardProcessMapper.updateExamUploaderStatusByExamUploaderId", params);

        // 删除扫描设置
        // newAnswerCardConfigService.deleteAnswerCardConfig(params);

//        answerCardExtraService.cancelRecognition(params);
        // 恢复现场
        setExamStudentAbsent(params, examPaper, examStudentAbsentList);

        if (!examService.useNewScanVersion(examPaper)) {
            // 修改待办信息 102 examUploaderId=原来的
            examMarkTodoService.updateExamUploaderIdUploaderTodoTaskByUserId(
              MapUtil.getLong(examPaper, "examPaperId"),
              MapUtil.getLong(examPaper, "uploaderId"),
              examUploaderId
            );
        }
    }

    /**
     * 获取需要设置为缺考时学生信息
     *    如果本次识别是补传，则除之前已经完成的examUploader外的学生都是缺考的
     *    需要进行恢复现场操作
     * @param examUploaderId examUploaderId
     * @return 需要设置为缺考的学生信息
     */
    private List<Map<String, Object>> getExamStudentAbsentList(long examUploaderId) {
        // -> examId schoolId paperId [classIds]
        Map<String, Object> examUploaderInfo = recognitionDataService.getExamUploaderInfo(examUploaderId);
        int uploadType = MapUtil.getInt(examUploaderInfo, "uploadType");
        // 如果是补传的 需要剔除examUploader已经是完成的学生
        List<Map<String, Object>> examStudentAbsentList = null;
        if (DictUtil.isEquals(uploadType, "examUploadType", "appendByQuestion", "appendByClass")) {
            examStudentAbsentList = recognitionDataService.getExamStudentList(examUploaderId);
        }
        return examStudentAbsentList;
    }

    /**
     * 设置学生缺考
     *    如果本次识别是补传，则除之前已经完成的examUploader外的学生都是缺考的
     *    需要进行恢复现场操作
     * @param params userId userName
     * @param examPaperInfo examId paperId
     * @param examStudentAbsentList 需要设置为缺考的学生信息
     */
    private void setExamStudentAbsent(Map<String, Object> params,
                                      Map<String, Object> examPaperInfo,
                                      List<Map<String, Object>> examStudentAbsentList) {
        if (CollectionUtils.isEmpty(examStudentAbsentList)) {
            return;
        }
        List<Long> studentIdList = examStudentAbsentList.stream()
                .map(item -> MapUtil.getLong(item, "studentId"))
                .collect(Collectors.toList());
        ParamsUtil.setCurrentTimeIfAbsent(params);
        params.put("examId", MapUtil.getLong(examPaperInfo, "examId"));
        params.put("paperId", MapUtil.getLong(examPaperInfo, "paperId"));
        params.put("studentIdList", studentIdList);
        params.put("resultStatusAbsent", DictUtil.getDictValue("resultStatus", "absent"));

        examRepository.update("NewAnswerCardProcessMapper.setExamStudentAbsent", params);

        long paperId = MapUtil.getLong(params, "paperId");
        List<Long> questionNumbers = newPlanExamUploaderService.getQuestionNumbers(paperId);
        if(CollectionUtils.isNotEmpty(questionNumbers)) {
            params.put("questionNumbers", questionNumbers);
            examRepository.delete("ExamItemMapper.removeExamItemStudentAbsent", params);
        }
    }

    // ----------------------------------------------------------------------------------- 结束扫描

    /**
     * 结束扫描
     *   仅客户端独有 适用于客户端扫描完答题卡后直接结束扫描 其异常处理的操作交由web端进行
     * @param params examUploaderId
     */
    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void finishScanAnswerCardForClient(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isValidId("userId")
                .isNotBlank("userName")
                .isInteger("userType")
                .verify();
        // long examUploaderId = MapUtil.getLong(params, "examUploaderId");

        // 解锁 examUploaderId不会删除
        recognitionControlService.unlock(params);

        // 打包answerCard -> zip   考试工具箱要下载
        // newAnswerCardZipService.packageZip(params);

        // 删除识别缓存信息 TODO 扫描客户端
        // cacheRecognitionService.deleteRedisEpAcInfo(examUploaderId);
    }

    /**
     * 结束扫描
     *   仅客户端独有 适用于客户端扫描完答题卡后直接结束扫描 其异常处理的操作交由web端进行
     * @param params examUploaderId
     */
    @Override
    @Transactional(ExamRepository.TRANSACTION)
    public void finishScanAnswerCardForServer(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examUploaderId")
                .isValidId("userId")
                .isNotBlank("userName")
                .isInteger("userType")
                .verify();
        // 解锁 examUploaderId不会删除
        recognitionControlService.unlock(params);
    }
}
