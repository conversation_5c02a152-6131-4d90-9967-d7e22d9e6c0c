package com.dongni.exam.newcard.service.impl;

import com.dongni.exam.newcard.bean.ExceptionCardPropertyBo;
import com.dongni.exam.newcard.service.IPropertyParams;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
public class AnswerCardCodePropertyParams implements IPropertyParams {
    @Override
    public List<ExceptionCardPropertyBo> getParams(Map<String, Object> params) {
        if(!params.containsKey("list")) {
            return null;
        }
        List<Map<String, Object>> list = MapUtil.getListMap(params, "list");
        Map<Long, List<Map<String, Object>>> examUploaderListMap = list.stream()
                .collect(groupingBy(item -> MapUtil.getLong(item, "examUploaderId")));

        List<ExceptionCardPropertyBo> examUploaderCodeList = new ArrayList<>();
        for(Long examUploaderId : examUploaderListMap.keySet()) {
            ExceptionCardPropertyBo bo = new ExceptionCardPropertyBo();
            bo.setExamUploaderId(examUploaderId);
            List<Map<String, Object>> examUploaderList = examUploaderListMap.get(examUploaderId);
            if(examUploaderList.get(0).containsKey("answerCardCode")) {
                Set<String> codes = examUploaderList.stream().map(item -> MapUtil.getString(item, "answerCardCode")).collect(Collectors.toSet());
                List<String> codeList = new ArrayList<>(codes);
                bo.setAnswerCardCodes(codeList);
                examUploaderCodeList.add(bo);
            }
        }
        return examUploaderCodeList;
    }
}
