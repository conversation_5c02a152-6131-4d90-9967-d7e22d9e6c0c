package com.dongni.exam.newcard.manager.impl;

import com.dongni.exam.newcard.dao.NewAnswerCardDao;
import com.dongni.exam.newcard.manager.IAnswerCardManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Component
public class AnswerCardManagerImpl implements IAnswerCardManager {

    @Resource
    private NewAnswerCardDao newAnswerCardDao;
    @Override
    public void updateStudentIdByRecognitionId(long recognitionId, List<Long> studentIds) {
        newAnswerCardDao.updateStudentIdByRecognitionId(recognitionId, studentIds);
    }

    @Override
    public List<Long> getAnswerCardStudentIds(long examId, long paperId, List<Long> studentIds) {
        return newAnswerCardDao.getAnswerCardStudentIds(examId, paperId, studentIds);
    }
}
