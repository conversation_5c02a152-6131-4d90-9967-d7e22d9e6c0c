package com.dongni.exam.newcard.manager.impl;

import com.dongni.exam.newcard.dao.NewExamItemDao;
import com.dongni.exam.newcard.manager.ICardExamItemManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Component
public class CardExamItemManagerImpl implements ICardExamItemManager {
    @Resource
    private NewExamItemDao newExamItemDao;

    @Override
    public void updateExamItemStudentIdToNegative(long recognitionId, List<Long> studentIds, List<Long> qns) {
        newExamItemDao.updateExamItemStudentIdToNegative(recognitionId,studentIds, qns);
    }

    @Override
    public void updateExamItemStudentIdToPositive(long recognitionId,List<Long> studentIds, List<Long> qns) {
        newExamItemDao.updateExamItemStudentIdToPositive(recognitionId,studentIds, qns);
    }

    @Override
    public void updateCardExamItemStatus(List<Long> studentIds, long paperId, long examId, int status) {
        if(CollectionUtils.isEmpty(studentIds)) {
            return ;
        }

        newExamItemDao.updateCardExamItemStatus(studentIds, paperId, examId, status);
    }
}
