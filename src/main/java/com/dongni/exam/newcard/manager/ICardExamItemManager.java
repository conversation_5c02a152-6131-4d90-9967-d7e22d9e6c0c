package com.dongni.exam.newcard.manager;

import java.util.List;

public interface ICardExamItemManager {
    void updateExamItemStudentIdToNegative(long recognitionId, List<Long> studentIds, List<Long> qns);

    void updateExamItemStudentIdToPositive(long recognitionId, List<Long> studentIds, List<Long> qns);

    void updateCardExamItemStatus(List<Long> normalStudentIds, long paperId, long examId, int i);
}
