package com.dongni.exam.studyguide.manager;

import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.studyguide.bean.dto.HomeworkWeeklyReportDTO;
import com.dongni.exam.studyguide.bean.dto.HomeworkWeeklyReportIdStatusDTO;
import com.dongni.exam.studyguide.bean.dto.WeeklyReportHomeworkDetail;
import com.dongni.exam.studyguide.bean.dto.WeeklyReportHomeworkSimple;
import com.dongni.exam.studyguide.bean.entity.HomeworkWeeklyReportStudentKnowledgeEntity;
import com.dongni.exam.studyguide.bean.param.StudyGuideWeeklyReportKnowledgeParam;
import com.dongni.exam.studyguide.bean.param.StudyGuideWeeklyReportParam;
import com.dongni.exam.studyguide.bean.query.StuStudyGuideWeeklyReportQuery;
import com.dongni.exam.studyguide.bean.vo.StudyGuideKnowledgeVO;
import com.dongni.tiku.common.util.MapUtil;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: hzw
 * @date: 2025/9/15
 * @description:
 */
@Component
public class StudyGuideWeeklyReportManager {

	@Autowired
	private ExamRepository examRepository;

	@Autowired
	private CommonCourseService commonCourseService;
	
	/**
	 * 获取学生的已生成的作业周报数量
	 * @param param studentId
	 * @return 作业周报数量
	 */
	public int getWeeklyReportCountByQuery(StudyGuideWeeklyReportParam param) {
		StuStudyGuideWeeklyReportQuery query = new StuStudyGuideWeeklyReportQuery();
		query.setStudentId(param.getStudentId());
		return examRepository.selectOne("StudyGuideWeeklyReportMapper.getWeeklyReportCountByQuery", query);
	}
	
	/**
	 * 获取学生的已生成的作业周报列表
	 * @param param studentId currentIndex pageSize
	 * @return 作业周报列表信息
	 */
	public List<HomeworkWeeklyReportDTO> getWeeklyReportListByQuery(StudyGuideWeeklyReportParam param) {
		StuStudyGuideWeeklyReportQuery query = new StuStudyGuideWeeklyReportQuery();
		query.setStudentId(param.getStudentId());
		query.setCurrentIndex(param.getCurrentIndex());
		query.setPageSize(param.getPageSize());
		return examRepository.selectList("StudyGuideWeeklyReportMapper.getWeeklyReportListByQuery", query);
	}
	
	/**
	 * 获取学生的已生成的作业周报信息
	 * @param param homeworkWeeklyReportId studentId
	 * @return 作业周报信息
	 */
	public HomeworkWeeklyReportDTO getWeeklyReportByQuery(StudyGuideWeeklyReportParam param) {
		StuStudyGuideWeeklyReportQuery query = new StuStudyGuideWeeklyReportQuery();
		query.setHomeworkWeeklyReportId(param.getHomeworkWeeklyReportId());
		query.setStudentId(param.getStudentId());
		return examRepository.selectOne("StudyGuideWeeklyReportMapper.getWeeklyReportByQuery", query);
	}

	/**
	 * 获取学生的已生成的作业周报信息
	 * @param param homeworkWeeklyReportId studentId
	 * @return 作业周报信息
	 */
	public HomeworkWeeklyReportDTO getPreviousWeeklyReportByQuery(StudyGuideWeeklyReportParam param) {
		StuStudyGuideWeeklyReportQuery query = new StuStudyGuideWeeklyReportQuery();
		query.setEpochWeek(param.getEpochWeek());
		query.setStudentId(param.getStudentId());
		return examRepository.selectOne("StudyGuideWeeklyReportMapper.getPreviousWeeklyReportByQuery", query);
	}

	/**
	 * 获取学生的已生成的作业周报列表
	 * @param param homeworkWeeklyReportId studentId
	 * @return 作业周报列表信息
	 */
	public List<WeeklyReportHomeworkSimple> getWeeklyReportHomeworkSimpleListByQuery(StudyGuideWeeklyReportParam param) {
		StuStudyGuideWeeklyReportQuery query = new StuStudyGuideWeeklyReportQuery();
		query.setHomeworkWeeklyReportId(param.getHomeworkWeeklyReportId());
		query.setStudentId(param.getStudentId());
		return examRepository.selectList("StudyGuideWeeklyReportMapper.getWeeklyReportHomeworkSimpleListByQuery", query);
	}

	/**
	 * 获取学生的已生成的作业周报列表
	 * @param param homeworkWeeklyReportId studentId
	 * @return 作业周报列表信息
	 */
	public List<WeeklyReportHomeworkDetail> getWeeklyReportHomeworkDetailListByQuery(StudyGuideWeeklyReportParam param) {
		StuStudyGuideWeeklyReportQuery query = new StuStudyGuideWeeklyReportQuery();
		query.setHomeworkWeeklyReportId(param.getHomeworkWeeklyReportId());
		query.setStudentId(param.getStudentId());
		return examRepository.selectList("StudyGuideWeeklyReportMapper.getWeeklyReportHomeworkDetailListByQuery", query);
	}

	/**
	 * 根据epochWeek获取作业周报
	 */
	public HomeworkWeeklyReportIdStatusDTO getHomeworkWeeklyReportByEpochWeek(long epochWeek) {
		return examRepository.selectOne("StudyGuideWeeklyReportMapper.getHomeworkWeeklyReportByEpochWeek", epochWeek);
	}

	/**
	 * 插入作业周报
	 */
	public void insertWeeklyReport(Map<String, Object> params) {
		examRepository.insert("StudyGuideWeeklyReportMapper.insertHomeworkWeeklyReport", params);
	}

	/**
	 * 更新作业周报的状态
	 */
	public void updateWeeklyReportStatus(Map<String, Object> params) {
		examRepository.insert("StudyGuideWeeklyReportMapper.updateWeeklyReportStatus", params);
	}

	/**
	 * 根据作业周报id获取一个学生id
	 */
	public Long getOneWeeklyReportStuIdByWeeklyReportId(long homeworkWeeklyReportId) {
		return examRepository.selectOne("StudyGuideWeeklyReportMapper.getOneWeeklyReportStuIdByWeeklyReportId", homeworkWeeklyReportId);
	}

	/**
	 * 根据作业周报id和状态获取对应的学生ids
	 */
	public List<Long> getWeeklyReportStuIdByWeeklyReportIdAndStatus(long homeworkWeeklyReportId, int status) {
		return examRepository.selectList("StudyGuideWeeklyReportMapper.getWeeklyReportStuIdByWeeklyReportIdAndStatus",
			MapUtil.of("homeworkWeeklyReportId", homeworkWeeklyReportId, "status", status));
	}

	/**
	 * 插入作业周报的学生
	 */
	public void insertWeeklyReportStudentList(Map<String, Object> params) {
		examRepository.insert("StudyGuideWeeklyReportMapper.insertHomeworkWeeklyReportStudentList", params);
	}

	/**
	 * 更新作业周报的学生状态
	 */
	public void updateWeeklyReportStudentStatus(Map<String, Object> params) {
		examRepository.update("StudyGuideWeeklyReportMapper.updateWeeklyReportStudentStatus", params);
	}

	/**
	 * 获取作业周报的学生
	 */
	public Long getWeeklyReportStudentId(long homeworkWeeklyReportId, long studentId) {
		return examRepository.selectOne("StudyGuideWeeklyReportMapper.getWeeklyReportStudentId",
			MapUtil.of("homeworkWeeklyReportId", homeworkWeeklyReportId, "studentId", studentId));
	}

	/**
	 * 删除作业周报的学生的数据
	 */
	public void deleteWeeklyReportStudent(Long homeworkWeeklyReportStudentId) {
		examRepository.update("StudyGuideWeeklyReportMapper.deleteWeeklyReportStudent", homeworkWeeklyReportStudentId);
	}

	/**
	 * 删除作业周报的学生的数据
	 */
	public void deleteWeeklyReportStudentHomework(Long homeworkWeeklyReportStudentId) {
		examRepository.delete("StudyGuideWeeklyReportMapper.deleteWeeklyReportStudentHomework", homeworkWeeklyReportStudentId);
	}

	/**
	 * 插入作业周报的学生的作业列表
	 */
	public void insertWeeklyReportStudentHomework(Map<String, Object> params) {
		examRepository.insert("StudyGuideWeeklyReportMapper.insertHomeworkWeeklyReportStudentHomework", params);
	}

	/**
	 * 插入作业周报的学生知识点列表
	 */
	public void insertWeeklyReportStudentKnowledge(List<HomeworkWeeklyReportStudentKnowledgeEntity> entities) {
		if (CollectionUtils.isEmpty(entities)) {
			return;
		}
		examRepository.insert("StudyGuideWeeklyReportMapper.insertWeeklyReportStudentKnowledge", entities);
	}

	/**
	 * 删除作业周报的学生的数据
	 */
	public void deleteWeeklyReportStudentKnowledge(Long homeworkWeeklyReportStudentId) {
		examRepository.delete("StudyGuideWeeklyReportMapper.deleteWeeklyReportStudentKnowledge", homeworkWeeklyReportStudentId);
	}

	/**
	 * 获取周报知识点-不传courseId就获取全部课程
	 */
	public List<StudyGuideKnowledgeVO> getStudyGuideWeeklyReportKnowledge(StudyGuideWeeklyReportKnowledgeParam param) {
		List<StudyGuideKnowledgeVO> result =
				examRepository.selectList("StudyGuideWeeklyReportMapper.getStudyGuideWeeklyReportKnowledge", param);
		// 补充课程名称
		List<Long> courseIds = result.stream()
				.map(StudyGuideKnowledgeVO::getCourseId)
				.distinct()
				.collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(courseIds)) {
			Map<Long, Map<String, Object>> courseInfoMap = commonCourseService.getCourseById(courseIds);
			result.forEach(vo -> {
				Long courseId = vo.getCourseId();
				String courseName = Optional.ofNullable(courseInfoMap.get(courseId))
						.map(course -> MapUtil.getString(course, "courseName"))
						.orElse("未知课程");
				vo.setCourseName(courseName);
			});
		}
		return result;
	}
}
