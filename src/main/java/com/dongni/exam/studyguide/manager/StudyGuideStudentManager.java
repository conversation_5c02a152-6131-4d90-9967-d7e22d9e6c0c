package com.dongni.exam.studyguide.manager;

import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.enumeration.ExamTypeEnum;
import com.dongni.exam.studyguide.bean.dto.ExamInfoDTO;
import com.dongni.exam.studyguide.bean.dto.ExamInfoStudentIdDTO;
import com.dongni.exam.studyguide.bean.dto.StuStudyGuideDTO;
import com.dongni.exam.studyguide.bean.param.StudyGuideStudentParam;
import com.dongni.exam.studyguide.bean.query.StuStudyGuideQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: hzw
 * @date: 2025/9/10
 * @description:
 */
@Component
public class StudyGuideStudentManager {

	@Autowired
	private ExamRepository examRepository;
	
	/**
	 * 获取学生的已公布的作业数量
	 * @param param studentId
	 * @return 作业列表信息
	 */
	public int getExamCountByQuery(StudyGuideStudentParam param) {
		StuStudyGuideQuery query = new StuStudyGuideQuery();
		query.setStudentId(param.getStudentId());
		query.setExamType(ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode());
		return examRepository.selectOne("StudyGuideStudentMapper.getExamCountByQuery", query);
	}
	
	/**
	 * 获取学生的已公布的作业列表
	 * @param param studentId currentIndex pageSize
	 * @return 作业列表信息
	 */
	public List<ExamInfoDTO> getExamListByQuery(StudyGuideStudentParam param) {
		StuStudyGuideQuery query = new StuStudyGuideQuery();
		query.setStudentId(param.getStudentId());
		query.setExamType(ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode());
		query.setCurrentIndex(param.getCurrentIndex());
		query.setPageSize(param.getPageSize());
		return examRepository.selectList("StudyGuideStudentMapper.getExamListByQuery", query);
	}
	
	/**
	 * 获取学生的已公布的作业信息
	 * @param param studentId examId
	 * @return 作业信息
	 */
	public ExamInfoDTO getExamByQuery(StudyGuideStudentParam param) {
		StuStudyGuideQuery query = new StuStudyGuideQuery();
		query.setExamId(param.getExamId());
		query.setStudentId(param.getStudentId());
		query.setExamType(ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode());
		return examRepository.selectOne("StudyGuideStudentMapper.getExamByQuery", query);
	}

	public List<StuStudyGuideDTO> getStuStudyGuideListByQuery(List<Long> examIds, long studentId) {
		StuStudyGuideQuery query = new StuStudyGuideQuery();
		query.setExamIds(examIds);
		query.setStudentId(studentId);
		return examRepository.selectList("StudyGuideStudentMapper.getStuStudyGuideListByQuery", query);
	}

	/**
	 * 获取学生的已公布的作业列表
	 * @return 作业列表信息
	 */
	public List<ExamInfoStudentIdDTO> getExamListForWeeklyReport(String startDate, String endDate, List<Long> studentIds) {
		StuStudyGuideQuery query = new StuStudyGuideQuery();
		query.setExamType(ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode());
		query.setStartDate(startDate);
		query.setEndDate(endDate);
		query.setStudentIds(studentIds);
		return examRepository.selectList("StudyGuideStudentMapper.getExamListForWeeklyReport", query);
	}
}
