package com.dongni.exam.studyguide.bean.param;

import com.dongni.commons.utils.verify.Verify2;
import lombok.Getter;
import lombok.Setter;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/16 周二 下午 03:27
 * @Version 1.0.0
 */
@Getter
@Setter
public class StudyGuideWrongQuestionExamDownloadParam {
    private Long studentId;
    private Long courseId;
    private Long examId;

    public void verify() {
        Verify2.of(this)
                .isValidId(StudyGuideWrongQuestionExamDownloadParam::getStudentId)
                .isValidId(StudyGuideWrongQuestionExamDownloadParam::getCourseId)
                .isValidId(StudyGuideWrongQuestionExamDownloadParam::getExamId)
                .verify();
    }
}
