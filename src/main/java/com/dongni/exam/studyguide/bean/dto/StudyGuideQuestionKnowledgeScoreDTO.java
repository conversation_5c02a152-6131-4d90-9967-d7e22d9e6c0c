package com.dongni.exam.studyguide.bean.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/09/16
 */
@Data
public class StudyGuideQuestionKnowledgeScoreDTO {
    
    /** 试题id question._id */
    private String questionId;
    /** 试题分值 */
    private Double questionScore;
    /** 试题得分 学生得分/班级平均分 */
    private Double score;
    
    /** 统计的知识点Id 当其不为空时才进行统计 */
    private String analysisKnowledgeId;
    /** 统计的知识点TreeCode */
    private String analysisKnowledgeTreeCode;
    /** 统计的知识点名称 */
    private String analysisKnowledgeName;
    
    /** 知识点id */
    private String knowledgeId;
    /** 知识点树形码 */
    private String knowledgeTreeCode;
    /** 知识点名称 */
    private String knowledgeName;
    
    /** 尝试统计的知识点树形码 */
    private String tryAnalysisKnowledgeTreeCode;
}
