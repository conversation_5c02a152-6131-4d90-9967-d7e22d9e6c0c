package com.dongni.exam.studyguide.bean.param;

import com.dongni.commons.entity.BaseRequestParams;
import com.dongni.commons.utils.verify.Verify2;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/18 周四 下午 07:06
 * @Version 1.0.0
 */
@Getter
@Setter
public class StudyGuideWeeklyReportKnowledgeParam extends BaseRequestParams {
    private Long studentId;

	private Long homeworkWeeklyReportId;

    private @Nullable Long courseId;

    public void verify() {
        Verify2.of(this)
                .isValidId(StudyGuideWeeklyReportKnowledgeParam::getStudentId)
                .isValidId(StudyGuideWeeklyReportKnowledgeParam::getHomeworkWeeklyReportId)
                .verify();
    }
}
