package com.dongni.exam.studyguide.bean.vo;

import com.dongni.exam.studyguide.bean.dto.WeeklyReportHomeworkSimple;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2025/9/15
 * @description: 首页的作业周报卡片
 */
@Getter
@Setter
public class WeeklyReportCardVO {

	/**
	 * 作业周报id
	 */
	private Long homeworkWeeklyReportId;

	/**
	 * 周报开始时间
	 */
	private Long startDate;

	/**
	 * 周报结束时间
	 */
	private Long endDate;

	/**
	 * 第几周
	 */
	private Long epochWeek;

	/**
	 * 统计时间段内已发布的作业列表
	 */
	List<WeeklyReportHomeworkSimple> homeworkList;

	/**
	 * 上一周的已发布作业列表
	 */
	List<WeeklyReportHomeworkSimple> previousWeekHomeworkList;

	/**
	 * 本周知识点列表
	 */
	private List<StudyGuideKnowledgeVO> knowledgeList;
}
