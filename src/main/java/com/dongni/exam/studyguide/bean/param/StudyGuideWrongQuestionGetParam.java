package com.dongni.exam.studyguide.bean.param;

import com.dongni.commons.utils.verify.Verify2;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/15 周一 下午 07:06
 * @Version 1.0.0
 */
@Getter
@Setter
public class StudyGuideWrongQuestionGetParam {
    private Long studentId;
    private Long courseId;
    private @Nullable Double difficultyLeft;
    private @Nullable Double difficultyRight;
    private String startDate;
    private String endDate;

    public void verify() {
        Verify2.of(this)
                .isValidId(StudyGuideWrongQuestionGetParam::getStudentId)
                .isValidId(StudyGuideWrongQuestionGetParam::getCourseId)
                .isNotBlank(StudyGuideWrongQuestionGetParam::getStartDate)
                .isNotBlank(StudyGuideWrongQuestionGetParam::getEndDate)
                .verify();
    }
}
