package com.dongni.exam.studyguide.bean.enumeration;

/**
 * @author: hzw
 * @date: 2025/9/17
 * @description: 教辅作业学生周报状态枚举
 */
public enum WeeklyReportStatusEnum {

	INITIAL(0, "初始化"),
	SUCCESS(1, "成功"),
	FAIL(2, "失败");

	private int value;

	private String description;

	WeeklyReportStatusEnum(int value, String description) {
		this.value = value;
		this.description = description;
	}

	public int getValue() {
		return value;
	}
}
