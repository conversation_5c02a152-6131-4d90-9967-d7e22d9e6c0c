package com.dongni.exam.studyguide.bean.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2025/9/10
 * @description:
 */
@Setter
@Getter
public class StuStudyGuideVO {

	/**
	 * 作业id
	 */
	private long examId;

	/**
	 * 作业名称
	 */
	private String examName;
	
	/**
	 * 课程id
	 */
	private long courseId;
	
	/**
	 * 课程名称
	 */
	private String courseName;
	
	/**
	 * 作业打分模式 true不打分模式 false打分模式
	 */
	private boolean scoreMode;

	/**
	 * 试卷结构试题数
	 */
	private Integer questionCount;

	/**
	 * 试卷总分
	 */
	private double fullMark;

	/**
	 * 扫描时间 时间戳毫秒
	 */
	private long scanTime;

	/**
	 * 提交状态 0为提交，1为未提交
	 */
	private int resultStatus;

	/**
	 * 试卷结构错题数
	 */
	private Integer studentWrongQuestionCount;

	/**
	 *得分
	 */
	private Double totalScore;

	/**
	 * 得分率
	 */
	private Double scoreRate;
	
}
