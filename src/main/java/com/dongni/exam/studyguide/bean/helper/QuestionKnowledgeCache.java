package com.dongni.exam.studyguide.bean.helper;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.service.OwnQuestionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/16 周二 下午 05:46
 * @Version 1.0.0
 */
@Service
@Scope("prototype")
public class QuestionKnowledgeCache {
    @Autowired
    private OwnQuestionService ownQuestionService;

    // questionId ==> knowledgeIds
    private final Cache<String, List<String>> cache = CacheUtil.newLFUCache(2000);

    public Map<String, List<String>> getKnowledgeByQuestionIds(List<String> questionIds) {
        if (CollectionUtil.isEmpty(questionIds)) {
            return Collections.emptyMap();
        }

        Map<String, List<String>> result = Maps.newHashMapWithExpectedSize(questionIds.size());
        List<String> noCacheQuestionIds = Lists.newArrayList();

        // 遍历找出未缓存的
        for (String questionId : questionIds) {
            List<String> value = cache.get(questionId);
            if (value == null) {
                noCacheQuestionIds.add(questionId);
            }
            result.put(questionId, value);
        }

        // 查询没有缓存的
        if (CollectionUtils.isNotEmpty(noCacheQuestionIds)) {
            List<ObjectId> noCacheObjectIds = noCacheQuestionIds.stream()
                    .map(ObjectId::new)
                    .collect(Collectors.toList());
            List<Document> questions = ownQuestionService.getQuestions(noCacheObjectIds);
            Map<String, List<String>> questionId2Knowledge = questions.stream()
                    .collect(Collectors.toMap(i -> MapUtil.getString(i, "_id"), i -> MapUtil.getCast(i, "knowledgeIdList")));
            for (String noCacheQuestionId : noCacheQuestionIds) {
                // 试题不存在了默认给空知识点也缓存下来
                List<String> knowledge = questionId2Knowledge.getOrDefault(noCacheQuestionId, new ArrayList<>(0));
                cache.put(noCacheQuestionId, knowledge);
                result.put(noCacheQuestionId, knowledge);
            }
        }

        return result;
    }
}
