package com.dongni.exam.studyguide.bean.entity;

import com.dongni.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/17 周三 下午 02:28
 * @Version 1.0.0
 */
@Getter
@Setter
public class HomeworkWeeklyReportStudentKnowledgeEntity extends BaseEntity {
    private Long homeworkWeeklyReportStudentId;
    private Long courseId;
    private String knowledgeId;
    private String knowledgeName;
    private String knowledgeTreeCode;
    private BigDecimal studentScoreRate;
    private BigDecimal classScoreRate;
    private Integer studentWrongQuestionCount;
}
