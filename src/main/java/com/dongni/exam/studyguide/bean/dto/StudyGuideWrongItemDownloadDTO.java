package com.dongni.exam.studyguide.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/16 周二 上午 11:30
 * @Version 1.0.0
 */
@Getter
@Setter
public class StudyGuideWrongItemDownloadDTO {
    private Long studyGuideWrongItemId;
    private Integer belongType;
    private String questionId;
    private Integer wrongItemQuestionFrom;
    private Long courseId;
    private Long examId;
    private String examName;
    private LocalDateTime examClassCreateDateTime;
}
