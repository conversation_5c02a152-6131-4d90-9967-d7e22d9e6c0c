package com.dongni.exam.studyguide.bean.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2025/9/15
 * @description: 作业周报中的作业信息-简单信息
 */
@Getter
@Setter
public class WeeklyReportHomeworkSimple {

	/**
	 * 作业id
	 */
	private long examId;

	/**
	 * 课程id
	 */
	private long courseId;

	/**
	 * 课程名称
	 */
	private String courseName;

	/**
	 * 试卷结构试题数
	 */
	private Integer questionCount;

	/**
	 * 试卷总分
	 */
	private double fullMark;


	/**
	 * 提交状态 0为提交，1为未提交
	 */
	private int resultStatus;

	/**
	 * 试卷结构错题数
	 */
	private Integer studentWrongQuestionCount;

	/**
	 * 学生得分
	 */
	private Double totalScore;

}
