package com.dongni.exam.studyguide.bean.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/09/15
 */
@Data
public class StudyGuideKnowledgeRateDTO {
    
    private String knowledgeId;
    
    private String knowledgeName;
    
    private String knowledgeTreeCode;
    
    private Integer wrongQuestionCount;
    
    private Double scoreRate;
    
    private List<QuestionScore> questionScoreList;
    
    @Data
    public static class QuestionScore {
        /** 试题id */
        private String questionId;
        /** 满分 */
        private Double questionScore;
        /** 得分 */
        private Double score;
    }
    
}
