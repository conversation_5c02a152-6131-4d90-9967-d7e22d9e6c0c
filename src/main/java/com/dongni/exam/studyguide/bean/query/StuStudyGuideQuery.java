package com.dongni.exam.studyguide.bean.query;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2025/9/10
 * @description:
 */
@Getter
@Setter
public class StuStudyGuideQuery {

	private long studentId;

	private Long examId;

	private int examType;

	private Integer currentIndex;

	private Integer pageSize;

	private List<Long> examIds;

	private String startDate;

	private String endDate;

	private List<Long> studentIds;

}
