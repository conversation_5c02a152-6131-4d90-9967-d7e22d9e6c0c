package com.dongni.exam.studyguide.bean.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/17 周三 上午 11:24
 * @Version 1.0.0
 */
@Getter
@Setter
public class StudyGuideStudentKnowledgeDTO {
    private Long courseId;
    private String knowledgeId;
    private String knowledgeName;
    private String knowledgeTreeCode;
    private Double studentScoreRate; // 学生得分率-学生缺考为null
    private Double classScoreRate; // 班级得分率-班级缺考没有这条数据所以不为null
    private int studentWrongQuestionCount; // 学生错题数-学生缺考为null
}
