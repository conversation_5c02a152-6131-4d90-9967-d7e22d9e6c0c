package com.dongni.exam.studyguide.bean.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2025/9/15
 * @description: 作业周报中的作业信息-完整信息
 */
@Getter
@Setter
public class WeeklyReportHomeworkDetail {

	/**
	 * 作业id
	 */
	private long examId;

	/**
	 * 作业名称
	 */
	private String examName;

	/**
	 * 作业打分模式 true不打分模式 false打分模式
	 */
	private boolean scoreMode;

	/**
	 * 扫描时间 时间戳毫秒
	 */
	private long scanTime;

	/**
	 * 课程id
	 */
	private long courseId;

	/**
	 * 课程名称
	 */
	private String courseName;

	/**
	 * 试卷结构试题数
	 */
	private Integer questionCount;

	/**
	 * 试卷总分
	 */
	private double fullMark;

	/**
	 * 提交状态 0为提交，1为未提交
	 */
	private int resultStatus;

	/**
	 * 试卷结构错题数
	 */
	private Integer studentWrongQuestionCount;

	/**
	 * 学生得分
	 */
	private Double totalScore;

	/**
	 * 学生得分率
	 */
	private Double scoreRate;

	/**
	 * 学生班级排名 同分同名次
	 */
	private Integer classRanking;

	/**
	 * 班级平均错题数
	 */
	private Double classWrongQuestionCount;

	/**
	 * 班级平均得分
	 */
	private Double classAverageScore;

	/**
	 * 班级平均得分率
	 */
	private Double classScoreRate;

	/**
	 * 班级总人数 (参考+缺考) 即 (提交+未提交)
	 */
	private int classTotalStudent;

	/**
	 * 班级参考(提交)人数
	 */
	private int classParticipationNumber;

	/**
	 * 班级缺考(未提交)人数
	 */
	private int classAbsentNumber;

	/**
	 * 班级最后一名名次 同分同名次
	 */
	private Integer classLastRanking;

}
