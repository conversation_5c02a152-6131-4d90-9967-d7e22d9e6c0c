package com.dongni.exam.studyguide.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/16 周二 下午 06:33
 * @Version 1.0.0
 */
@Getter
@Setter
public class WeeklyReportStudentQuestionInfoParam {
    private Long studentId;
    private Long examId;
    private Long classId;
    private Long courseId;

    private Stat studentStat; // 学生维度的统计数据
    private Stat classStat; // 班级维度的统计数据

    @Getter
    @Setter
    public static class Stat {
        private int resultStatus;
        private List<QuestionInfo> questionInfos;
    }

    @Getter
    @Setter
    public static class QuestionInfo {
        private String questionId;
        private Double scoreValue; // 分值，班级维度是全部为缺考学生数量 * 试题得分
        private Double finallyValue; // 得分，班级维度是全部未缺考学生得分之和
    }
}
