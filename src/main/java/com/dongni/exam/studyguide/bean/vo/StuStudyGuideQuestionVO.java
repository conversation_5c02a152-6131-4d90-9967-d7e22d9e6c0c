package com.dongni.exam.studyguide.bean.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/15 周一 下午 04:40
 * @Version 1.0.0
 */
@Getter
@Setter
public class StuStudyGuideQuestionVO {
    private Double questionScore; // 试题分值
    private Double studentScore; // 学生得分 - 缺考学生为null,缺考学生试题在交互上为"正确"试题
    private String questionId; // 试题ID
    private Long studyGuideWrongItemId; // 错题明细ID - 仅错题存在
    private Integer belongType;
    private Map<String, Object> question; // 试题详情
}
