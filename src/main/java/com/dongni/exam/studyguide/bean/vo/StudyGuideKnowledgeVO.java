package com.dongni.exam.studyguide.bean.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/18 周四 下午 07:11
 * @Version 1.0.0
 */
@Getter
@Setter
public class StudyGuideKnowledgeVO {
    private Long courseId;
    private String courseName;
    private String knowledgeId;
    private String knowledgeName;
    private BigDecimal studentScoreRate; // 学生得分率 - 学生缺考为null
    private Integer studentWrongQuestionCount; // 当前知识点的错题数 - 学生缺考为null
    private BigDecimal classScoreRate; // 班级平均得分率
}
