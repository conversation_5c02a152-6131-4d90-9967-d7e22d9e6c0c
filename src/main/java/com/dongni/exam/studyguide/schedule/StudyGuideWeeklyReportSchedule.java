package com.dongni.exam.studyguide.schedule;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static java.util.stream.Collectors.toMap;

import com.dongni.basedata.export.student.bean.StudentIdNameDTO;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.exam.studyguide.bean.entity.HomeworkWeeklyReportStudentKnowledgeEntity;
import com.dongni.exam.studyguide.bean.enumeration.WeeklyReportStatusEnum;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.exam.newcard.parse.enumeration.ExamResultStatusEnum;
import com.dongni.exam.newcard.service.AnswerCardServiceV3;
import com.dongni.exam.studyguide.bean.dto.*;
import com.dongni.exam.studyguide.bean.helper.QuestionKnowledgeCache;
import com.dongni.exam.studyguide.manager.StudyGuideStudentManager;
import com.dongni.exam.studyguide.manager.StudyGuideWeeklyReportManager;
import com.dongni.exam.studyguide.service.StudyGuideKnowledgeService;
import com.dongni.exam.studyguide.util.EpochDayUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Sorts;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @author: hzw
 * @date: 2025/9/16
 * @description: 作业周报定时任务
 */
@Service
public class StudyGuideWeeklyReportSchedule {

	/**
	 * 获取Mongodb数据库对象
	 */
	private final MongoDatabase mongo;
	@Autowired
	private StudyGuideWeeklyReportManager studyGuideWeeklyReportManager;
	@Autowired
	private StudyGuideStudentManager studyGuideStudentManager;
	@Autowired
	private CommonStudentService commonStudentService;
	@Autowired
	private AnswerCardServiceV3 answerCardServiceV3;
	@Autowired
	private StudyGuideKnowledgeService studyGuideKnowledgeService;
	@Autowired
	private ObjectProvider<QuestionKnowledgeCache> questionKnowledgeCacheObjectProvider;
	@Autowired
	private PaperManager paperManager;

	@Autowired
	public StudyGuideWeeklyReportSchedule(AnalysisMongodb analysisMongodb) {
		this.mongo = analysisMongodb.getMongoDatabase();
	}

	/**
	 * 每周六0、1、2、3、4、5点执行，生成周报
	 * 理论上在0点就能生成完，因为各种原因导致0点没生成完时，后面几个整点再去尝试生成
	 */
	@Scheduled(cron = "0 0 0,1,2,3,4,5 ? * SAT")
	@DistributeLock(moduleName="EXAM", name="generateStudyGuideWeeklyReport", expireTime = -1)
	public void generateStudyGuideWeeklyReport() {
		long startDateEpochDay = EpochDayUtil.getStartDateEpochDay();
		long endDateEpochDay = EpochDayUtil.getEndDateEpochDay(startDateEpochDay);
		long epochWeek = EpochDayUtil.getEpochWeek(endDateEpochDay);
		Map<String, Object> params = new HashMap<>(7);
		params.put("userId", 1);
		params.put("userName", "定时任务");
		Long homeworkWeeklyReportId;
		List<Long> studentIds = null;
		HomeworkWeeklyReportIdStatusDTO homeworkWeeklyReport = studyGuideWeeklyReportManager.getHomeworkWeeklyReportByEpochWeek(epochWeek);
		if (homeworkWeeklyReport != null) {
			if (homeworkWeeklyReport.getStatus() == WeeklyReportStatusEnum.SUCCESS.getValue()){
				// 这一周的周报已经成功生成过了，直接返回
				return;
			}
			homeworkWeeklyReportId = homeworkWeeklyReport.getHomeworkWeeklyReportId();
			params.put("homeworkWeeklyReportId", homeworkWeeklyReportId);
			if (studyGuideWeeklyReportManager.getOneWeeklyReportStuIdByWeeklyReportId(homeworkWeeklyReportId) != null) {
				// 这一周的周报的学生数据已经保存过了，则只需要查询状态仍为初始状态的学生
				studentIds = studyGuideWeeklyReportManager.getWeeklyReportStuIdByWeeklyReportIdAndStatus(homeworkWeeklyReportId,
					WeeklyReportStatusEnum.INITIAL.getValue());
				if (studentIds.isEmpty()) {
					// 没有状态仍为初始状态的学生，则更新周报状态为成功，并结束
					params.put("status", WeeklyReportStatusEnum.SUCCESS.getValue());
					studyGuideWeeklyReportManager.updateWeeklyReportStatus(params);
					return;
				}
			}
		}

		// 知识点缓存数据 - 缓存作业范围是每次统计
		QuestionKnowledgeCache questionKnowledgeCache = questionKnowledgeCacheObjectProvider.getObject();
		Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache = new HashMap<>();
		Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache = new HashMap<>();

		params.put("epochWeek", epochWeek);
		String startDate = EpochDayUtil.epochDayToString(startDateEpochDay) + " 00:00:00";
		params.put("startDate", startDate);
		String endDate = EpochDayUtil.epochDayToString(endDateEpochDay) + " 23:59:59";
		params.put("endDate", endDate);
		//插入作业周报
		studyGuideWeeklyReportManager.insertWeeklyReport(params);
		homeworkWeeklyReportId = MapUtils.getLong(params, "homeworkWeeklyReportId");

		//获取班级扫描时间在上周六0点~这周五24点之间并且有班级已经公布了报告的学生-作业列表
		List<ExamInfoStudentIdDTO> examListForWeeklyReport = studyGuideStudentManager.getExamListForWeeklyReport(startDate, endDate, studentIds);
		if (examListForWeeklyReport.isEmpty()) {
			// 更新周报状态为成功
			params.put("status", WeeklyReportStatusEnum.SUCCESS.getValue());
			studyGuideWeeklyReportManager.updateWeeklyReportStatus(params);
			return;
		}

		// 从基础数据获取学生姓名
		Map<Long, List<ExamInfoStudentIdDTO>> stuId2ExamList = examListForWeeklyReport.stream()
			.collect(Collectors.groupingBy(ExamInfoStudentIdDTO::getStudentId));
		List<StudentIdNameDTO> stuIdNameList = commonStudentService.getStudentNameByStudentIds(
			new ArrayList<>(stuId2ExamList.keySet()));
		if (!stuIdNameList.isEmpty()) {
			BatchDataUtil.execute(stuIdNameList, (x) -> {
				Map<String, Object> stuParams = new HashMap<>(params);
				stuParams.put("weeklyReportStudentList", x);
				studyGuideWeeklyReportManager.insertWeeklyReportStudentList(stuParams);
			});
		}
		Set<Long> stuIdSet = stuIdNameList.stream().map(StudentIdNameDTO::getStudentId).collect(Collectors.toSet());

		for (Entry<Long, List<ExamInfoStudentIdDTO>> entry : stuId2ExamList.entrySet()) {
			long studentId = entry.getKey();
			Long weeklyReportStudentId = studyGuideWeeklyReportManager.getWeeklyReportStudentId(homeworkWeeklyReportId, studentId);
			try {
				if (!stuIdSet.contains(studentId)) {
					// 学生在基础数据不存在，不生成周报，且删除可能有的相关的脏数据
					deleteWeeklyReportStudentData(weeklyReportStudentId);
					continue;
				}
				if (weeklyReportStudentId == null) {
					// 理论上这个不可能为null
					continue;
				}
				WeeklyReportStudentData weeklyReportStudentData = getWeeklyReportStudentData(studentId, entry.getValue(),
						questionKnowledgeCache, knowledgeId2InfoCache, knowledgeTreeCode2InfoCache);
				// 先清除可能存在的旧数据
				deleteWeeklyReportStudentHomeworkAndKnowledge(weeklyReportStudentId);
				// 保存作业数据
				Map<String, Object> stuParams = new HashMap<>(params);
				stuParams.put("homeworkWeeklyReportStudentId", weeklyReportStudentId);
				stuParams.put("homeworkList", weeklyReportStudentData.getHomeworkList());
				studyGuideWeeklyReportManager.insertWeeklyReportStudentHomework(stuParams);
				stuParams.remove("homeworkList");
				// 保存知识点数据
				List<StudyGuideStudentKnowledgeDTO> knowledgeList = weeklyReportStudentData.getKnowledgeList();
				insertWeeklyReportStudentKnowledge(knowledgeList, MapUtil.getLong(stuParams, "homeworkWeeklyReportStudentId"));
				stuParams.put("status", WeeklyReportStatusEnum.SUCCESS.getValue());
				studyGuideWeeklyReportManager.updateWeeklyReportStudentStatus(stuParams);
			} catch (Exception e) {
				// 生成失败了
				Map<String, Object> stuParams = new HashMap<>(params);
				stuParams.put("homeworkWeeklyReportStudentId", weeklyReportStudentId);
				stuParams.put("status", WeeklyReportStatusEnum.FAIL.getValue());
				studyGuideWeeklyReportManager.updateWeeklyReportStudentStatus(stuParams);
			}
		}

		// 更新周报状态为成功
		params.put("status", WeeklyReportStatusEnum.SUCCESS.getValue());
		studyGuideWeeklyReportManager.updateWeeklyReportStatus(params);

	}

	private WeeklyReportStudentData getWeeklyReportStudentData(Long studentId,
															   List<ExamInfoStudentIdDTO> examList,
															   QuestionKnowledgeCache questionKnowledgeCache,
                                                               Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache,
                                                               Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache) {
		List<StuStudyGuideDTO> stuStudyGuideList = studyGuideStudentManager.getStuStudyGuideListByQuery(
			examList.stream().map(ExamInfoStudentIdDTO::getExamId).collect(Collectors.toList()), studentId);
		if (stuStudyGuideList.isEmpty()) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业数据出现异常，请联系管理员处理！");
		}
		Map<Long, StuStudyGuideDTO> examId2StuStudyGuide = stuStudyGuideList.stream()
			.collect(toMap(StuStudyGuideDTO::getExamId, x -> x, (v1, v2) -> {
				throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业报告数据出现异常，请联系管理员处理！");
			}));

		List<Bson> studentFilter = stuStudyGuideList.stream()
			.map(x -> and(eq("examId", x.getExamId()), eq("courseId", x.getCourseId()), eq("paperId", x.getPaperId())))
			.collect(Collectors.toList());
		Map<Long, Document> examId2StuCourseStat = mongo.getCollection("examStudentCourseStat")
			.find(and(or(studentFilter), eq("statId", 0), eq("studentId", studentId)))
			.projection(fields(include("examId", "totalScore", "fullMark", "scoreRate", "classRanking", "paperQuestionCount",
					"paperWrongQuestionCount", "resultStatus", "paperQuestions"),
				excludeId()))
			.into(new ArrayList<>())
			.stream().collect(toMap(x -> MapUtils.getLong(x, "examId"), x -> x, (v1, v2) -> {
				throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业报告数据出现异常，请联系管理员处理！");
			}));

		List<Bson> classFilter = stuStudyGuideList.stream().map(
				x -> and(eq("examId", x.getExamId()), eq("classId", x.getClassId()), eq("courseId", x.getCourseId()), eq("paperId", x.getPaperId())))
			.collect(Collectors.toList());
		Map<Long, Document> examId2ClassCourseStat = mongo.getCollection("examClassCourseStat")
			.find(and(or(classFilter), eq("statId", 0)))
			.projection(fields(include("examId", "totalStudent", "participationNumber", "absentNumber", "paperWrongQuestionCount",
					"averageScore", "resultStatus", "paperQuestions"), excludeId()))
			.into(new ArrayList<>())
			.stream().collect(toMap(x -> MapUtils.getLong(x, "examId"), x -> x, (v1, v2) -> {
				throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业报告数据出现异常，请联系管理员处理！");
			}));

		List<WeeklyReportHomeworkDetailBO> homeworkList = new ArrayList<>();
		List<WeeklyReportStudentQuestionInfoParam> knowledgeParamList = new ArrayList<>();

		// 作业
		examList.forEach(x -> {
			long examId = x.getExamId();
			StuStudyGuideDTO stuStudyGuideDTO = Optional.ofNullable(examId2StuStudyGuide.get(examId))
					.orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_ERROR, "作业报告数据出现异常，请联系管理员处理！examId：" + examId));
			Document stuCourseStatDoc = Optional.ofNullable(examId2StuCourseStat.get(examId))
					.orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_ERROR, "作业报告数据出现异常，请联系管理员处理！examId：" + examId));
			Document classCourseStatDoc = Optional.ofNullable(examId2ClassCourseStat.get(examId))
					.orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_ERROR, "作业报告数据出现异常，请联系管理员处理！examId：" + examId));
			// 班级最后一名
			Document classLastStuCourseStatDoc = mongo.getCollection("examStudentCourseStat")
					.find(and(eq("examId", examId), eq("statId", 0), eq("courseId", stuStudyGuideDTO.getCourseId()),
							eq("paperId", stuStudyGuideDTO.getPaperId()), eq("classId", stuStudyGuideDTO.getClassId()),
							eq("resultStatus", ExamResultStatusEnum.NORMAL.getCode())))
					.projection(fields(include("classRanking"), excludeId()))
					.sort(Sorts.descending("classRanking"))
					.first();

			// 作业
			WeeklyReportHomeworkDetailBO vo = new WeeklyReportHomeworkDetailBO();
			vo.setExamId(examId);
			vo.setExamName(x.getExamName());
			vo.setScoreMode(answerCardServiceV3.getPaperScoreMissMode(examId, stuStudyGuideDTO.getPaperId()));
			vo.setScanTime(x.getScanTime());
			vo.setCourseId(stuStudyGuideDTO.getCourseId());
			vo.setCourseName(stuStudyGuideDTO.getCourseName());
			vo.setQuestionCount(MapUtils.getInteger(stuCourseStatDoc, "paperQuestionCount"));
			if (vo.getQuestionCount() == null) {
				vo.setQuestionCount(PaperUtil.getPaperStructure(paperManager.getPaper(stuStudyGuideDTO.getPaperId())).size());
			}
			vo.setFullMark(MapUtils.getDouble(stuCourseStatDoc, "fullMark"));
			vo.setResultStatus(stuStudyGuideDTO.getResultStatus());
			vo.setStudentWrongQuestionCount(MapUtils.getInteger(stuCourseStatDoc, "paperWrongQuestionCount"));
			vo.setTotalScore(MapUtils.getDouble(stuCourseStatDoc, "totalScore"));
			vo.setScoreRate(MapUtils.getDouble(stuCourseStatDoc, "scoreRate"));
			vo.setClassRanking(MapUtils.getInteger(stuCourseStatDoc, "classRanking"));
			vo.setClassWrongQuestionCount(MapUtils.getDouble(classCourseStatDoc, "paperWrongQuestionCount"));
			vo.setClassAverageScore(MapUtils.getDouble(classCourseStatDoc, "averageScore"));
			vo.setClassScoreRate(vo.getClassAverageScore() / vo.getFullMark());
			vo.setClassTotalStudent(MapUtils.getInteger(classCourseStatDoc, "totalStudent"));
			vo.setClassParticipationNumber(MapUtils.getInteger(classCourseStatDoc, "participationNumber"));
			vo.setClassAbsentNumber(MapUtils.getInteger(classCourseStatDoc, "absentNumber"));
			vo.setClassLastRanking(classLastStuCourseStatDoc == null ? 0 : MapUtils.getInteger(classLastStuCourseStatDoc, "classRanking"));
			homeworkList.add(vo);

			// 知识点
			WeeklyReportStudentQuestionInfoParam questionInfoParam = new WeeklyReportStudentQuestionInfoParam();
			questionInfoParam.setStudentId(studentId);
			questionInfoParam.setExamId(examId);
			questionInfoParam.setClassId(stuStudyGuideDTO.getClassId());
			questionInfoParam.setCourseId(stuStudyGuideDTO.getCourseId());

			WeeklyReportStudentQuestionInfoParam.Stat studentStat = new WeeklyReportStudentQuestionInfoParam.Stat();
			studentStat.setResultStatus(MapUtils.getInteger(stuCourseStatDoc, "resultStatus"));
			List<WeeklyReportStudentQuestionInfoParam.QuestionInfo> studentPaperQuestions = Optional
					.<List<Document>>ofNullable(MapUtil.getCast(stuCourseStatDoc, "paperQuestions"))
					.orElseGet(ArrayList::new).stream()
					.map(i -> {
						WeeklyReportStudentQuestionInfoParam.QuestionInfo questionInfo = new WeeklyReportStudentQuestionInfoParam.QuestionInfo();
						questionInfo.setQuestionId(MapUtil.getString(i, "questionId"));
						questionInfo.setScoreValue(MapUtil.getDouble(i, "scoreValue"));
						questionInfo.setFinallyValue(MapUtil.getDoubleNullable(i, "finallyScore"));
						return questionInfo;
					}).collect(Collectors.toList());
			studentStat.setQuestionInfos(studentPaperQuestions);
			questionInfoParam.setStudentStat(studentStat);

			WeeklyReportStudentQuestionInfoParam.Stat classStat = new WeeklyReportStudentQuestionInfoParam.Stat();
			classStat.setResultStatus(MapUtils.getInteger(classCourseStatDoc, "resultStatus"));
			List<WeeklyReportStudentQuestionInfoParam.QuestionInfo> classPaperQuestions = Optional
					.<List<Document>>ofNullable(MapUtil.getCast(classCourseStatDoc, "paperQuestions"))
					.orElseGet(ArrayList::new).stream()
					.map(i -> {
						WeeklyReportStudentQuestionInfoParam.QuestionInfo questionInfo = new WeeklyReportStudentQuestionInfoParam.QuestionInfo();
						questionInfo.setQuestionId(MapUtil.getString(i, "questionId"));
						questionInfo.setScoreValue(MapUtil.getDoubleNullable(i, "scoreValue"));
						questionInfo.setFinallyValue(MapUtil.getDoubleNullable(i, "finallyScore"));
						return questionInfo;
					}).collect(Collectors.toList());
			classStat.setQuestionInfos(classPaperQuestions);
			questionInfoParam.setClassStat(classStat);

			knowledgeParamList.add(questionInfoParam);
		});

		// 知识点
		List<StudyGuideStudentKnowledgeDTO> studyGuideStudentKnowledge = studyGuideKnowledgeService.computeStudentWeeklyReportKnowledge(
				knowledgeParamList, questionKnowledgeCache, knowledgeId2InfoCache, knowledgeTreeCode2InfoCache);

		WeeklyReportStudentData result = new WeeklyReportStudentData();
		result.setHomeworkList(homeworkList);
		result.setKnowledgeList(studyGuideStudentKnowledge);
		return result;
	}

	/**
	 * 保存学生周报计算后的知识点数据
	 */
	private void insertWeeklyReportStudentKnowledge(List<StudyGuideStudentKnowledgeDTO> knowledgeList,
													Long homeworkWeeklyReportStudentId) {
		LocalDateTime now = LocalDateTime.now();
		List<HomeworkWeeklyReportStudentKnowledgeEntity> entities = knowledgeList.stream()
				.map(i -> {
					HomeworkWeeklyReportStudentKnowledgeEntity entity = new HomeworkWeeklyReportStudentKnowledgeEntity();
					entity.setHomeworkWeeklyReportStudentId(homeworkWeeklyReportStudentId);
					entity.setCourseId(i.getCourseId());
					entity.setKnowledgeId(i.getKnowledgeId());
					entity.setKnowledgeName(i.getKnowledgeName());
					entity.setKnowledgeTreeCode(i.getKnowledgeTreeCode());
					entity.setStudentScoreRate(Optional.ofNullable(i.getStudentScoreRate()).map(BigDecimal::valueOf).orElse(null));
					entity.setClassScoreRate(BigDecimal.valueOf(i.getClassScoreRate()));
					entity.setStudentWrongQuestionCount(i.getStudentWrongQuestionCount());
					entity.setCreatorId(1L);
					entity.setCreatorName("定时任务统计");
					entity.setCreateDateTime(now);
					entity.setModifierId(1L);
					entity.setModifierName("定时任务统计");
					entity.setModifyDateTime(now);
					return entity;
				}).collect(Collectors.toList());
		studyGuideWeeklyReportManager.insertWeeklyReportStudentKnowledge(entities);
	}

	/**
	 * 删除学生在周报的所有数据
	 */
	private void deleteWeeklyReportStudentData(Long weeklyReportStudentId) {
		if(weeklyReportStudentId != null){
			studyGuideWeeklyReportManager.deleteWeeklyReportStudent(weeklyReportStudentId);
			studyGuideWeeklyReportManager.deleteWeeklyReportStudentHomework(weeklyReportStudentId);
			studyGuideWeeklyReportManager.deleteWeeklyReportStudentKnowledge(weeklyReportStudentId);
		}
	}

	/**
	 * 删除学生在周报的作业和知识点数据
	 */
	private void deleteWeeklyReportStudentHomeworkAndKnowledge(Long weeklyReportStudentId) {
		studyGuideWeeklyReportManager.deleteWeeklyReportStudentHomework(weeklyReportStudentId);
		studyGuideWeeklyReportManager.deleteWeeklyReportStudentKnowledge(weeklyReportStudentId);
	}
}
