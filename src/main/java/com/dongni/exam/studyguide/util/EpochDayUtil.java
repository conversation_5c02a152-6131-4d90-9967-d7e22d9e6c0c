package com.dongni.exam.studyguide.util;

import com.dongni.commons.utils.DateUtil;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * @author: hzw
 * @date: 2025/9/16
 * @description:
 */
public class EpochDayUtil {

	private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	public static long getEpochDay() {
		LocalDate createLocalDate = LocalDate.parse(DateUtil.getCurrentDate(), DF);
		return createLocalDate.toEpochDay();
	}

	/**
	 * 获取上一周的周报开始时间的epochDay
	 */
	public static long getStartDateEpochDay() {
		long epochDay = getEpochDay();
		int dayNumber = LocalDate.now().getDayOfWeek().getValue();
		if (dayNumber <= 5) {
			// 周一到周五
			return epochDay - 8 - dayNumber;
		} else {
			return epochDay - 1 - dayNumber;
		}
	}

	/**
	 * 获取上一周的周报结束时间的epochDay
	 */
	public static long getEndDateEpochDay(long startDateEpochDay) {
		return startDateEpochDay + 6;
	}

	/**
	 * 获取epochDay是第几周
	 * epochDay 为从1970-01-01算第几天，1970-01-01为第0天，epochWeek = (epochDay + 4) / 7 为从1970-01-01算第几周，周日为一周的第一天
	 *
	 * @return 第几周
	 */
	public static long getEpochWeek(long epochDay) {
		return (epochDay + 4) / 7;
	}

	/**
	 * epochDay转日期 yyyy-MM-dd
	 */
	public static String epochDayToString(long epochDay) {
		return LocalDate.ofEpochDay(epochDay).format(DF);
	}
}
