package com.dongni.exam.studyguide.service;

import com.dongni.commons.entity.BaseRequestParams;
import com.dongni.commons.utils.verify.Verify2;
import com.dongni.exam.studyguide.bean.dto.HomeworkWeeklyReportDTO;
import com.dongni.exam.studyguide.bean.param.StudyGuideWeeklyReportKnowledgeParam;
import com.dongni.exam.studyguide.bean.param.StudyGuideWeeklyReportParam;
import com.dongni.exam.studyguide.bean.vo.StudyGuideKnowledgeVO;
import com.dongni.exam.studyguide.bean.vo.WeeklyReportCardVO;
import com.dongni.exam.studyguide.bean.vo.WeeklyReportDetailVO;
import com.dongni.exam.studyguide.bean.vo.WeeklyReportPageVO;
import com.dongni.exam.studyguide.manager.StudyGuideWeeklyReportManager;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: hzw
 * @date: 2025/9/15
 * @description:
 */
@Service
public class StudyGuideWeeklyReportService {

	@Autowired
	private StudyGuideWeeklyReportManager studyGuideWeeklyReportManager;

	/**
	 * 获取最新的周报信息
	 */
	public WeeklyReportCardVO getStuStudyGuideWeeklyReportLatest(StudyGuideWeeklyReportParam param) {
		Verify2.of(param)
			.isValidId(StudyGuideWeeklyReportParam::getStudentId)
			.verify();

		param.setCurrentIndex(0);
		param.setPageSize(1);
		WeeklyReportCardVO weeklyReportCardVO = new WeeklyReportCardVO();
		List<HomeworkWeeklyReportDTO> homeworkWeeklyReportList = studyGuideWeeklyReportManager.getWeeklyReportListByQuery(param);
		if (homeworkWeeklyReportList.isEmpty()) {
			return weeklyReportCardVO;
		}
		HomeworkWeeklyReportDTO homeworkWeeklyReport = homeworkWeeklyReportList.get(0);
		weeklyReportCardVO.setHomeworkWeeklyReportId(homeworkWeeklyReport.getHomeworkWeeklyReportId());
		weeklyReportCardVO.setStartDate(homeworkWeeklyReport.getStartDate());
		weeklyReportCardVO.setEndDate(homeworkWeeklyReport.getEndDate());
		weeklyReportCardVO.setEpochWeek(homeworkWeeklyReport.getEpochWeek());
		param.setHomeworkWeeklyReportId(homeworkWeeklyReport.getHomeworkWeeklyReportId());
		weeklyReportCardVO.setHomeworkList(studyGuideWeeklyReportManager.getWeeklyReportHomeworkSimpleListByQuery(param));
		param.setEpochWeek(homeworkWeeklyReport.getEpochWeek());
		HomeworkWeeklyReportDTO previousWeeklyReport = studyGuideWeeklyReportManager.getPreviousWeeklyReportByQuery(param);
		if (previousWeeklyReport != null) {
			param.setHomeworkWeeklyReportId(previousWeeklyReport.getHomeworkWeeklyReportId());
			weeklyReportCardVO.setPreviousWeekHomeworkList(studyGuideWeeklyReportManager.getWeeklyReportHomeworkSimpleListByQuery(param));
		}
		// 知识点
		StudyGuideWeeklyReportKnowledgeParam knowledgeParam = new StudyGuideWeeklyReportKnowledgeParam();
		knowledgeParam.setStudentId(param.getStudentId());
		knowledgeParam.setHomeworkWeeklyReportId(param.getHomeworkWeeklyReportId());
		List<StudyGuideKnowledgeVO> knowledgeList = getStudyGuideWeeklyReportKnowledge(knowledgeParam);
		weeklyReportCardVO.setKnowledgeList(knowledgeList);

		return weeklyReportCardVO;
	}

	/**
	 * 获取周报列表
	 */
	public WeeklyReportPageVO getStuStudyGuideWeeklyReportList(StudyGuideWeeklyReportParam param) {
		Verify2.of(param)
			.isValidId(StudyGuideWeeklyReportParam::getStudentId)
			.isPositive(BaseRequestParams::getPageSize)
			.isNatural(BaseRequestParams::getCurrentIndex)
			.verify();

		int totalCount = studyGuideWeeklyReportManager.getWeeklyReportCountByQuery(param);
		List<HomeworkWeeklyReportDTO> homeworkWeeklyReportList =
			totalCount == 0 || param.getCurrentIndex() >= totalCount ? Collections.emptyList()
				: studyGuideWeeklyReportManager.getWeeklyReportListByQuery(param);

		WeeklyReportPageVO weeklyReportPageVO = new WeeklyReportPageVO();
		weeklyReportPageVO.setTotalCount(totalCount);
		weeklyReportPageVO.setList(homeworkWeeklyReportList);
		return weeklyReportPageVO;
	}

	/**
	 * 获取周报详情
	 */
	public WeeklyReportDetailVO getStuStudyGuideWeeklyReportDetail(StudyGuideWeeklyReportParam param) {
		Verify2.of(param)
			.isValidId(StudyGuideWeeklyReportParam::getStudentId)
			.isValidId(StudyGuideWeeklyReportParam::getHomeworkWeeklyReportId)
			.verify();

		WeeklyReportDetailVO weeklyReportDetailVO = new WeeklyReportDetailVO();
		HomeworkWeeklyReportDTO homeworkWeeklyReport = studyGuideWeeklyReportManager.getWeeklyReportByQuery(param);
		if (homeworkWeeklyReport == null) {
			return weeklyReportDetailVO;
		}
		weeklyReportDetailVO.setHomeworkWeeklyReportId(homeworkWeeklyReport.getHomeworkWeeklyReportId());
		weeklyReportDetailVO.setStartDate(homeworkWeeklyReport.getStartDate());
		weeklyReportDetailVO.setEndDate(homeworkWeeklyReport.getEndDate());
		weeklyReportDetailVO.setEpochWeek(homeworkWeeklyReport.getEpochWeek());
		param.setHomeworkWeeklyReportId(homeworkWeeklyReport.getHomeworkWeeklyReportId());
		weeklyReportDetailVO.setHomeworkList(studyGuideWeeklyReportManager.getWeeklyReportHomeworkDetailListByQuery(param));
		param.setEpochWeek(homeworkWeeklyReport.getEpochWeek());
		HomeworkWeeklyReportDTO previousWeeklyReport = studyGuideWeeklyReportManager.getPreviousWeeklyReportByQuery(param);
		if (previousWeeklyReport != null) {
			param.setHomeworkWeeklyReportId(previousWeeklyReport.getHomeworkWeeklyReportId());
			weeklyReportDetailVO.setPreviousWeekHomeworkList(studyGuideWeeklyReportManager.getWeeklyReportHomeworkSimpleListByQuery(param));
		}
		return weeklyReportDetailVO;
	}

	/**
	 * 获取周报知识点-不传courseId就获取全部课程
	 */
	public List<StudyGuideKnowledgeVO> getStudyGuideWeeklyReportKnowledge(StudyGuideWeeklyReportKnowledgeParam param) {
		param.verify();
		return studyGuideWeeklyReportManager.getStudyGuideWeeklyReportKnowledge(param);
	}
}
