package com.dongni.exam.studyguide.service;

import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.MongoUtil;
import com.dongni.common.utils.StreamUtil;
import com.dongni.exam.studyguide.bean.dto.*;
import com.dongni.exam.studyguide.bean.helper.QuestionKnowledgeCache;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.service.OwnKnowledgeService;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2025/09/15
 */
@Service
public class StudyGuideKnowledgeService {
    
    @Autowired
    private OwnKnowledgeService ownKnowledgeService;

    /**
     * 计算学生周报一批作业的知识点数据
     */
    public List<StudyGuideStudentKnowledgeDTO> computeStudentWeeklyReportKnowledge(List<WeeklyReportStudentQuestionInfoParam> weeklyReportStudentQuestionInfoParams,
                                                                                   QuestionKnowledgeCache questionKnowledgeCache,
                                                                                   Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache,
                                                                                   Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache) {
        if (CollectionUtils.isEmpty(weeklyReportStudentQuestionInfoParams)) {
            return Collections.emptyList();
        }

        List<StudyGuideStudentKnowledgeDTO> result = new ArrayList<>();

        Map<Long, List<WeeklyReportStudentQuestionInfoParam>> examGroupByCourse = weeklyReportStudentQuestionInfoParams.stream()
                .collect(groupingBy(WeeklyReportStudentQuestionInfoParam::getCourseId));
        for (Map.Entry<Long, List<WeeklyReportStudentQuestionInfoParam>> entry : examGroupByCourse.entrySet()) {
            Map<String, ScoreAdder> studentScoreAdder = new HashMap<>(); // questionId => scoreAdder
            Map<String, ScoreAdder> classScoreAdder = new HashMap<>(); // questionId => scoreAdder
            for (WeeklyReportStudentQuestionInfoParam weeklyReportStudentQuestionInfoParam : entry.getValue()) {
                WeeklyReportStudentQuestionInfoParam.Stat classStat = weeklyReportStudentQuestionInfoParam.getClassStat();
                // 学生所在班级整个缺考的考试不用考虑
                if (DictUtil.isEquals(classStat.getResultStatus(), "resultStatus", "absent")) {
                    continue;
                }

                // 学生数据 - 不缺考才统计
                WeeklyReportStudentQuestionInfoParam.Stat studentStat = weeklyReportStudentQuestionInfoParam.getStudentStat();
                if (DictUtil.isEquals(studentStat.getResultStatus(), "resultStatus", "attend")) {
                    List<WeeklyReportStudentQuestionInfoParam.QuestionInfo> studentQuestionInfos = studentStat.getQuestionInfos();
                    if (CollectionUtils.isNotEmpty(studentQuestionInfos)) {
                        studentQuestionInfos.forEach(qn ->
                                studentScoreAdder.merge(qn.getQuestionId(), new ScoreAdder(qn.getScoreValue(), qn.getFinallyValue()), ScoreAdder::merge));
                    }
                }

                // 班级数据
                List<WeeklyReportStudentQuestionInfoParam.QuestionInfo> classQuestionInfos = classStat.getQuestionInfos();
                if (CollectionUtils.isNotEmpty(classQuestionInfos)) {
                    classQuestionInfos.forEach(qn -> {
                        classScoreAdder.merge(qn.getQuestionId(), new ScoreAdder(qn.getScoreValue(), qn.getFinallyValue()), ScoreAdder::merge);
                    });
                }
            }

            // 以班级数据为准
            if (MapUtils.isEmpty(classScoreAdder)) {
                return Collections.emptyList();
            }
            // 试题知识点
            Set<String> questionIds = classScoreAdder.keySet();
            Map<String, List<String>> questionIds2Knowledge = questionKnowledgeCache.getKnowledgeByQuestionIds(new ArrayList<>(questionIds));

            List<StudyGuideQuestionScoreDTO> classStudyGuideQuestionScore = new ArrayList<>();
            List<StudyGuideQuestionScoreDTO> studentStudyGuideQuestionScore = new ArrayList<>();
            for (String questionId : questionIds) {
                // 班级
                StudyGuideQuestionScoreDTO classItem = new StudyGuideQuestionScoreDTO();
                classItem.setQuestionId(questionId);
                classItem.setKnowledgeIdList(questionIds2Knowledge.get(questionId));
                ScoreAdder scoreAdder = classScoreAdder.get(questionId);
                classItem.setQuestionScore(scoreAdder.getScoreValue());
                classItem.setScore(scoreAdder.getFinallyValue());
                classStudyGuideQuestionScore.add(classItem);

                // 学生
                if (studentScoreAdder.containsKey(questionId)) {
                    StudyGuideQuestionScoreDTO studentItem = new StudyGuideQuestionScoreDTO();
                    studentItem.setQuestionId(questionId);
                    studentItem.setKnowledgeIdList(questionIds2Knowledge.get(questionId));
                    scoreAdder = studentScoreAdder.get(questionId);
                    studentItem.setQuestionScore(scoreAdder.getScoreValue());
                    studentItem.setScore(scoreAdder.getFinallyValue());
                    studentStudyGuideQuestionScore.add(studentItem);
                }
            }

            // 计算
            List<StudyGuideKnowledgeRateDTO> classKnowledgeRate = computeKnowledgeRate(classStudyGuideQuestionScore, knowledgeId2InfoCache, knowledgeTreeCode2InfoCache);
            List<StudyGuideKnowledgeRateDTO> studentKnowledgeRate = computeKnowledgeRate(studentStudyGuideQuestionScore, knowledgeId2InfoCache, knowledgeTreeCode2InfoCache);
            Map<String, StudyGuideKnowledgeRateDTO> studentKnowledgeRateMap = studentKnowledgeRate.stream()
                    .collect(toMap(StudyGuideKnowledgeRateDTO::getKnowledgeId, Function.identity()));

            // 拼装数据返回
            for (StudyGuideKnowledgeRateDTO classKnowledgeRateItem : classKnowledgeRate) {
                StudyGuideStudentKnowledgeDTO resultItem = new StudyGuideStudentKnowledgeDTO();

                String knowledgeId = classKnowledgeRateItem.getKnowledgeId();
                resultItem.setKnowledgeId(knowledgeId);
                resultItem.setCourseId(entry.getKey());
                resultItem.setKnowledgeName(classKnowledgeRateItem.getKnowledgeName());
                resultItem.setKnowledgeTreeCode(classKnowledgeRateItem.getKnowledgeTreeCode());
                resultItem.setClassScoreRate(classKnowledgeRateItem.getScoreRate());

                StudyGuideKnowledgeRateDTO studentKnowledgeRateItem = studentKnowledgeRateMap.get(knowledgeId);
                if (studentKnowledgeRateItem != null) {
                    resultItem.setStudentScoreRate(studentKnowledgeRateItem.getScoreRate());
                    resultItem.setStudentWrongQuestionCount(studentKnowledgeRateItem.getWrongQuestionCount());
                }
                result.add(resultItem);
            }
        }

        return result;
    }

    @Getter
    private static class ScoreAdder {
        private double scoreValue;
        private double finallyValue;

        public ScoreAdder(double scoreValue, double finallyValue) {
            this.scoreValue = scoreValue;
            this.finallyValue = finallyValue;
        }

        public ScoreAdder merge(ScoreAdder other) {
            this.scoreValue += other.scoreValue;
            this.finallyValue += other.finallyValue;
            return this;
        }
    }

    /**
     * 计算知识点得分率
     *   1. 汇集所有的知识点id 用于查询知识点信息
     *   2. 缓存知识点信息 见 {@link #handlerCacheKnowledgeInfo}
     *   3. 对于每一个试题，其知识点信息需要进行转换为统计的知识点信息(向上取三级)
     *      知识点1   1.2.3.4.5.6 -> 取analysisTreeCode -> 1.2.3
     *      知识点2   1.2.3.4     -> 取analysisTreeCode -> 1.2.3
     *      知识点3   1.2.4.3     -> 取analysisTreeCode -> 1.2.4
     *      知识点4   1.2         -> 取analysisTreeCode -> 1.2
     *      知识点5   1.3.4.5     -> 已删除的知识点不参与计算
     *      知识点6   1.4.2.3.4   -> 取analysisTreeCode -> 1.4.2 -> 已删除的知识点不参与计算 回退到原知识点
     *      去重后得该题的知识点为 1.2, 1.2.3, 1.2.4, 1.4.2.3.4
     *      最终将 试题1 知识点列表[知识点1~知识点6] 转换为 6个item
     *         试题1 知识点1 试题分数10分 得分5分 知识点treeCode:1.2.3.4.5.6 需要分析的知识点treeCode 1.2.3
     *         试题1 知识点2 试题分数10分 得分5分 知识点treeCode:1.2.3.4     需要分析的知识点treeCode 1.2.3
     *         试题1 知识点3 试题分数10分 得分5分 知识点treeCode:1.2.4.3     需要分析的知识点treeCode 1.2.4
     *         试题1 知识点4 试题分数10分 得分5分 知识点treeCode:1.2         需要分析的知识点treeCode 1.2
     *         试题1 知识点5 试题分数10分 得分5分 知识点treeCode:1.3.4.5     需要分析的知识点treeCode null
     *         试题1 知识点6 试题分数10分 得分5分 知识点treeCode:1.4.2.3.4   需要分析的知识点treeCode 1.4.2.3.4
     *   4. 留下需要统计的数据
     *      过滤掉 需要分析的知识点信息为空的item
     *      同一题同一分析知识点仅能统计一次 unique(analysisKnowledgeId - questionId)
     *   5. 按照需要分析的知识点id进行分组
     *   6. 对于每个分组，整理返回的信息 {@link StudyGuideKnowledgeRateDTO}
     *      统计 wrongQuestionCount 与 scoreRate
     * @param studyGuideQuestionScoreDTOList 待计算的试题信息
     * @param knowledgeId2InfoCache       知识点缓存 knowledgeId -> knowledgeCacheInfo
     * @param knowledgeTreeCode2InfoCache 知识点缓存 knowledgeTreeCode -> knowledgeCacheInfo
     * @return 知识点得分率信息
     */
    public List<StudyGuideKnowledgeRateDTO> computeKnowledgeRate(List<StudyGuideQuestionScoreDTO> studyGuideQuestionScoreDTOList,
                                                                 Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache,
                                                                 Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache) {
        // 1. 汇集所有的知识点id 用于查询知识点信息
        studyGuideQuestionScoreDTOList = studyGuideQuestionScoreDTOList.stream()
                .filter(Objects::nonNull)
                .filter(item -> CollectionUtils.isNotEmpty(item.getKnowledgeIdList()))
                .collect(Collectors.toList());
        List<String> allKnowledgeIdList = studyGuideQuestionScoreDTOList.stream()
                .map(StudyGuideQuestionScoreDTO::getKnowledgeIdList)
                .flatMap(List::stream)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        // 2. 缓存知识点信息
        handlerCacheKnowledgeInfo(allKnowledgeIdList, knowledgeId2InfoCache, knowledgeTreeCode2InfoCache);
        
        // 3. 对于每一个试题，其知识点信息需要进行转换为统计的知识点信息(向上取三级)
        List<StudyGuideQuestionKnowledgeScoreDTO> questionKnowledgeScoreDTOList = new ArrayList<>();
        for (StudyGuideQuestionScoreDTO questionScoreDTO : studyGuideQuestionScoreDTOList) {
            for (String knowledgeId : questionScoreDTO.getKnowledgeIdList()) {
                if (StringUtils.isBlank(knowledgeId)) {
                    continue;
                }
                StudyGuideQuestionKnowledgeScoreDTO questionKnowledgeScoreDTO = new StudyGuideQuestionKnowledgeScoreDTO();
                questionKnowledgeScoreDTOList.add(questionKnowledgeScoreDTO);
                
                questionKnowledgeScoreDTO.setQuestionId(questionScoreDTO.getQuestionId());
                questionKnowledgeScoreDTO.setQuestionScore(questionScoreDTO.getQuestionScore());
                questionKnowledgeScoreDTO.setScore(questionScoreDTO.getScore());
                questionKnowledgeScoreDTO.setKnowledgeId(knowledgeId);
                StudyGuideKnowledgeCacheDTO originKnowledge = knowledgeId2InfoCache.get(knowledgeId);
                if (originKnowledge.isDeleted()) {
                    continue;   // 已删除的知识点无法获取其他信息 不参与计算
                }
                questionKnowledgeScoreDTO.setKnowledgeName(originKnowledge.getKnowledgeName());
                questionKnowledgeScoreDTO.setKnowledgeTreeCode(originKnowledge.getTreeCode());
                
                String tryAnalysisTreeCode = originKnowledge.getTryAnalysisTreeCode();
                questionKnowledgeScoreDTO.setTryAnalysisKnowledgeTreeCode(tryAnalysisTreeCode);
                StudyGuideKnowledgeCacheDTO tryAnalysisKnowledge = knowledgeTreeCode2InfoCache.get(tryAnalysisTreeCode);
                
                // 如果统计的知识点被删除，则回退到原知识点 理论上是不存在这种情况的 做下兼容处理
                StudyGuideKnowledgeCacheDTO analysisKnowledge = tryAnalysisKnowledge.isDeleted() ? originKnowledge : tryAnalysisKnowledge;
                questionKnowledgeScoreDTO.setAnalysisKnowledgeId(analysisKnowledge.getKnowledgeId());
                questionKnowledgeScoreDTO.setAnalysisKnowledgeName(analysisKnowledge.getKnowledgeName());
                questionKnowledgeScoreDTO.setAnalysisKnowledgeTreeCode(analysisKnowledge.getTreeCode());
            }
        }
        
        // System.out.println(" ---------------- 试题-分析知识点-分数 信息 ");
        // questionKnowledgeScoreDTOList.forEach(item -> System.out.println(JSONUtil.toJson(item)));
        
        // 4. 留下需要统计的数据
        List<StudyGuideQuestionKnowledgeScoreDTO> analysisQuestionKnowledgeScoreDTOList = questionKnowledgeScoreDTOList.stream()
                // 留下分析知识点信息不为空的数据
                .filter(item -> StringUtils.isNotBlank(item.getAnalysisKnowledgeId()))
                // 同一题同一分析知识点仅能统计一次 unique(analysisKnowledgeId - questionId)
                .filter(StreamUtil.distinctByKey(item -> item.getAnalysisKnowledgeId() + "__" + item.getQuestionId()))
                .collect(Collectors.toList());
        
        return analysisQuestionKnowledgeScoreDTOList.stream()
                // 按知识点id进行分组
                .collect(groupingBy(StudyGuideQuestionKnowledgeScoreDTO::getAnalysisKnowledgeId))
                .values()
                .stream()
                .map(list -> {
                    StudyGuideQuestionKnowledgeScoreDTO first = list.get(0);
                    int wrongCount = 0;
                    double totalFullScore = 0D;
                    double totalScore = 0D;
                    List<StudyGuideKnowledgeRateDTO.QuestionScore> questionScoreList = new ArrayList<>();
                    for (StudyGuideQuestionKnowledgeScoreDTO questionKnowledgeScoreDTO : list) {
                        String questionId = questionKnowledgeScoreDTO.getQuestionId();
                        double fullScore = questionKnowledgeScoreDTO.getQuestionScore();
                        double score = questionKnowledgeScoreDTO.getScore();
                        totalFullScore += fullScore;
                        totalScore += score;
                        if (score < fullScore) {
                            wrongCount++;  // 错了
                        }
                        StudyGuideKnowledgeRateDTO.QuestionScore questionScore = new StudyGuideKnowledgeRateDTO.QuestionScore();
                        questionScore.setQuestionId(questionId);
                        questionScore.setQuestionScore(fullScore);
                        questionScore.setScore(score);
                        questionScoreList.add(questionScore);
                    }
                    StudyGuideKnowledgeRateDTO knowledgeRateDTO = new StudyGuideKnowledgeRateDTO();
                    knowledgeRateDTO.setKnowledgeId(first.getAnalysisKnowledgeId());
                    knowledgeRateDTO.setKnowledgeName(first.getAnalysisKnowledgeName());
                    knowledgeRateDTO.setKnowledgeTreeCode(first.getAnalysisKnowledgeTreeCode());
                    knowledgeRateDTO.setWrongQuestionCount(wrongCount);
                    knowledgeRateDTO.setScoreRate(totalFullScore > 0 ? totalScore / totalFullScore : 0);
                    knowledgeRateDTO.setQuestionScoreList(questionScoreList);
                    return knowledgeRateDTO;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 缓存知识点信息
     * @param knowledgeIdList  知识点id
     * @param knowledgeId2InfoCache       知识点缓存 knowledgeId -> knowledgeCacheInfo
     * @param knowledgeTreeCode2InfoCache 知识点缓存 knowledgeTreeCode -> knowledgeCacheInfo
     */
    public void handlerCacheKnowledgeInfo(List<String> knowledgeIdList,
                                          Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache,
                                          Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache) {
        // 没有缓存的知识点id
        List<String> nocacheKnowledgeIdList = knowledgeIdList.stream()
                .filter(knowledgeId -> !knowledgeId2InfoCache.containsKey(knowledgeId))
                .collect(Collectors.toList());
        // 没有缓存的知识点ObjectId
        List<ObjectId> nocacheKnowledgeObjectIdList = nocacheKnowledgeIdList.stream()
                .map(MongoUtil::tryGetMongoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 获取知识点信息
        List<Document> knowledgeInfoListByIdList = ownKnowledgeService.getKnowledgeByObjectIds(nocacheKnowledgeObjectIdList);
        Map<String, Document> knowledgeId2KnowledgeInfo = knowledgeInfoListByIdList.stream()
                .collect(toMap(knowledge -> MapUtil.getTrim(knowledge, "_id"), Function.identity()));
        Set<String> knowledgeTreeCodeSet = knowledgeInfoListByIdList.stream()
                .map(knowledge -> MapUtil.getTrim(knowledge, "treeCode"))
                .collect(Collectors.toSet());
        // 收集未被缓存的treeCode知识点信息
        Set<String> nocacheTreeCodeSet = new HashSet<>();
        for (String nocacheKnowledgeId : nocacheKnowledgeIdList) {
            Document knowledgeInfo = knowledgeId2KnowledgeInfo.get(nocacheKnowledgeId);
            StudyGuideKnowledgeCacheDTO knowledgeCacheDTO = new StudyGuideKnowledgeCacheDTO();
            knowledgeCacheDTO.setKnowledgeId(nocacheKnowledgeId);
            knowledgeId2InfoCache.put(nocacheKnowledgeId, knowledgeCacheDTO);
            
            if (MapUtils.isEmpty(knowledgeInfo)) {
                knowledgeCacheDTO.setDeleted(true);  // 已经被删除了 查不到知识点信息了
                continue;
            }
            
            knowledgeCacheDTO.setDeleted(false);
            knowledgeCacheDTO.setKnowledgeName(MapUtil.getTrim(knowledgeInfo, "knowledgeName"));
            String treeCode = MapUtil.getTrim(knowledgeInfo, "treeCode");
            knowledgeCacheDTO.setTreeCode(treeCode);
            knowledgeTreeCode2InfoCache.put(treeCode, knowledgeCacheDTO);
            
            String[] treeCodeSplit = treeCode.split("\\.");
            if (treeCodeSplit.length <= 3) {
                knowledgeCacheDTO.setTryAnalysisTreeCode(treeCode);
                continue;  // 小于三级知识点的不处理
            }
            // 留三级
            String analysisTreeCode = Arrays.stream(treeCodeSplit)
                    .limit(3)
                    .collect(Collectors.joining("."));
            knowledgeCacheDTO.setTryAnalysisTreeCode(analysisTreeCode);
            if (knowledgeTreeCodeSet.contains(analysisTreeCode)) {
                // 知识点列表中已经存在被分析的treeCode 不需要再去查询
                continue;
            }
            if (!knowledgeTreeCode2InfoCache.containsKey(analysisTreeCode)) {
                // 缓存不存在 需要后续获取treeCode对应的知识点信息
                nocacheTreeCodeSet.add(analysisTreeCode);
            }
        }
        
        // 处理 treeCode -> knowledgeInfo
        List<Document> knowledgeInfoListByTreeCodes = ownKnowledgeService.getKnowledgeByTreeCodes(nocacheTreeCodeSet);
        Map<String, Document> treeCode2KnowledgeInfo = knowledgeInfoListByTreeCodes.stream()
                .collect(toMap(knowledge -> MapUtil.getTrim(knowledge, "treeCode"), Function.identity()));
        for (String nocacheTreeCode : nocacheTreeCodeSet) {
            Document knowledgeInfo = treeCode2KnowledgeInfo.get(nocacheTreeCode);
            StudyGuideKnowledgeCacheDTO knowledgeCacheDTO = new StudyGuideKnowledgeCacheDTO();
            knowledgeCacheDTO.setTreeCode(nocacheTreeCode);
            knowledgeTreeCode2InfoCache.put(nocacheTreeCode, knowledgeCacheDTO);
            if (MapUtils.isEmpty(knowledgeInfo)) {
                knowledgeCacheDTO.setDeleted(true);
            } else {
                String knowledgeId = MapUtil.getTrim(knowledgeInfo, "_id");
                knowledgeCacheDTO.setDeleted(false);
                knowledgeCacheDTO.setKnowledgeId(knowledgeId);
                knowledgeCacheDTO.setKnowledgeName(MapUtil.getTrim(knowledgeInfo, "knowledgeName"));
                knowledgeCacheDTO.setTryAnalysisTreeCode(nocacheTreeCode);
                knowledgeId2InfoCache.putIfAbsent(knowledgeId, knowledgeCacheDTO);
            }
        }
    }
    
}
