package com.dongni.exam.studyguide.service;

import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.basedata.UserInfoUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongCourseCountDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDownloadDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongTagItemDTO;
import com.dongni.exam.studyguide.bean.entity.StudyGuideWrongTagItemEntity;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongItemGetParam;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongQuestionExamDownloadParam;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongTagInsertParam;
import com.dongni.exam.wrong.bean.vo.WrongQuestionGroupVO;
import com.dongni.exam.wrong.bean.vo.WrongQuestionItemVO;
import com.dongni.exam.wrong.bean.vo.WrongQuestionVO;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongQuestionGetParam;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/15 周一 下午 05:15
 * @Version 1.0.0
 */
@Service
public class StudyGuideWrongService {
    @Autowired
    private ExamRepository examRepository;

    /**
     * 获取教辅作业错题明细
     */
    public List<StudyGuideWrongItemDTO> getStudyGuideWrongItem(StudyGuideWrongItemGetParam param) {
        param.verify();

        return examRepository.selectList("StudyGuideWrongMapper.getStudyGuideWrongItem", param);
    }

    /**
     * 获取学生教辅作业错题 - 错题本列表使用
     */
    public WrongQuestionVO getStudyGuideWrongQuestion(StudyGuideWrongQuestionGetParam param) {
        param.verify();

        List<WrongQuestionItemVO > wrongQuestionItemVOS
                = examRepository.selectList("StudyGuideWrongMapper.getStudyGuideWrongQuestion", param);
        wrongQuestionItemVOS.forEach(i -> {
            i.setWrongItemQuestionFrom(DictUtil.getDictValue("wrongItemQuestionFrom", "studyGuideWrongQuestion"));
            i.setBelongType(DictUtil.getDictValue("questionBankBelongType", "dongni"));
        });

        Map<Long, List<WrongQuestionItemVO>> homeworkId2WrongQuestionItemVO = wrongQuestionItemVOS.stream()
                .sorted(Comparator.comparing(WrongQuestionItemVO::getHomeworkCreateDateTime).reversed())
                .collect(groupingBy(WrongQuestionItemVO::getHomeworkId, LinkedHashMap::new, toList()));
        List<WrongQuestionGroupVO> wrongQuestionGroupVOS = new ArrayList<>();
        for (Map.Entry<Long, List<WrongQuestionItemVO>> entry : homeworkId2WrongQuestionItemVO.entrySet()) {
            WrongQuestionGroupVO wrongQuestionGroupVO = getWrongQuestionGroupVO(entry);
            wrongQuestionGroupVOS.add(wrongQuestionGroupVO);
        }
        WrongQuestionVO wrongQuestionVO = new WrongQuestionVO();
        wrongQuestionVO.setWrongQuestionGroups(wrongQuestionGroupVOS);
        return wrongQuestionVO;
    }

    /**
     * 根据错题ID获取错题 - 错题本列表下载错题使用
     */
    public List<StudyGuideWrongItemDownloadDTO> getStudyGuideWrongQuestion4Download(List<Long> studyGuideWrongItemIds) {
        if (CollectionUtils.isEmpty(studyGuideWrongItemIds)) {
            return Collections.emptyList();
        }
        List<StudyGuideWrongItemDownloadDTO> studyGuideWrongItemDownloadDTOS
                = examRepository.selectList("StudyGuideWrongMapper.getStudyGuideWrongQuestion4Download", studyGuideWrongItemIds);

        studyGuideWrongItemDownloadDTOS.forEach(i -> {
            i.setWrongItemQuestionFrom(DictUtil.getDictValue("wrongItemQuestionFrom", "studyGuideWrongQuestion"));
            i.setBelongType(DictUtil.getDictValue("questionBankBelongType", "dongni"));
        });
        return studyGuideWrongItemDownloadDTOS;
    }

    /**
     * 根据考试ID获取学生错题 - 考试报错错题整理使用
     */
    public List<Map<String, Object>> getStudyGuideWrongQuestionByExamId4Download(StudyGuideWrongQuestionExamDownloadParam param) {
        param.verify();

        List<Map<String, Object>> studyGuideWrongItem
                = examRepository.selectList("StudyGuideWrongMapper.getStudyGuideWrongQuestionByExamId4Download", param);

        studyGuideWrongItem.forEach(i -> {
            i.put("wrongItemQuestionFrom", DictUtil.getDictValue("wrongItemQuestionFrom", "studyGuideWrongQuestion"));
            i.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni"));
        });
        return studyGuideWrongItem;
    }

    /**
     * 统计学生不同课程的错题数量 - 试题不去重
     */
    public List<StudyGuideWrongCourseCountDTO> countStudentGuideWrongGroupByCourse(Long studentId) {
        if (!ObjectUtil.isValidId(studentId)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "studentId为空");
        }
        return examRepository.selectList("StudyGuideWrongMapper.countStudentGuideWrongGroupByCourse", studentId);
    }

    /**
     * 根据ID获取教辅作业错题
     */
    public StudyGuideWrongItemDTO getStudyGuideWrongItemById(Long studentId, Long studyGuideWrongItemId) {
        Map<String, Object> params = MapUtil.of("studentId", studentId, "studyGuideWrongItemId", studyGuideWrongItemId);
        StudyGuideWrongItemDTO studyGuideWrongItemDTO = examRepository.selectOne("StudyGuideWrongMapper.getStudyGuideWrongItemById", params);
        if (studyGuideWrongItemDTO == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "错题不存在!");
        }
        return studyGuideWrongItemDTO;
    }

    /**
     * 获取教辅作业错题的标记的错因
     */
    public List<StudyGuideWrongTagItemDTO> getStudyGuideWrongTagItemById(Long studyGuideWrongItemId) {
        if (studyGuideWrongItemId == null) {
            return Collections.emptyList();
        }
        return examRepository.selectList("StudyGuideWrongMapper.getStudyGuideWrongTagItemById", studyGuideWrongItemId);
    }

    /**
     * 删除指定错题的错因标签
     */
    public void deleteTags(Long studyGuideWrongItemId) {
        if (studyGuideWrongItemId == null) {
            return;
        }
        examRepository.delete("StudyGuideWrongMapper.deleteTags", studyGuideWrongItemId);
    }

    /**
     * 保存标签
     */
    public void insertTags(StudyGuideWrongTagInsertParam studyGuideWrongTagInsertParam) {
        if (CollectionUtils.isEmpty(studyGuideWrongTagInsertParam.getTagList())) {
            return;
        }
        if (studyGuideWrongTagInsertParam.getStudyGuideWrongItemId() == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "studyGuideWrongItemId为空");
        }

        DongniUserInfoContext userInfoContext = DongniUserInfoContext.get();
        LocalDateTime now = LocalDateTime.now();

        List<StudyGuideWrongTagInsertParam.Tag> tagList = studyGuideWrongTagInsertParam.getTagList();
        List<StudyGuideWrongTagItemEntity> entities = tagList.stream()
                .map(i -> {
                    StudyGuideWrongTagItemEntity entity = new StudyGuideWrongTagItemEntity();
                    entity.setStudyGuideWrongItemId(studyGuideWrongTagInsertParam.getStudyGuideWrongItemId());
                    entity.setWrongTagId(i.getWrongTagId());
                    entity.setTagName(i.getTagName());
                    UserInfoUtil.copyUserInfo(entity, userInfoContext, now);
                    return entity;
                })
                .collect(toList());

        examRepository.insert("StudyGuideWrongMapper.insertTags", entities);
    }

    private WrongQuestionGroupVO getWrongQuestionGroupVO(Map.Entry<Long, List<WrongQuestionItemVO>> entry) {
        Long homeworkId = entry.getKey();
        String homeworkName = entry.getValue().get(0).getHomeworkName();
        LocalDateTime homeworkCreateDateTime = entry.getValue().get(0).getHomeworkCreateDateTime();
        WrongQuestionGroupVO wrongQuestionGroupVO = new WrongQuestionGroupVO();
        wrongQuestionGroupVO.setGroupId(homeworkId);
        wrongQuestionGroupVO.setGroupName(homeworkName);
        wrongQuestionGroupVO.setStartDate(homeworkCreateDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        wrongQuestionGroupVO.setWrongQuestionItems(entry.getValue());
        return wrongQuestionGroupVO;
    }
}
