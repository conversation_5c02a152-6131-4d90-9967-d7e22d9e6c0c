package com.dongni.exam.arrange.third.enumerate;

/**
 * <AUTHOR>
 * @date 2021年11月25日
 */
public enum ThirdMarkCompanyEnum {

    /**
     *
     */
    TENCENT(0, "腾讯"),

    YUE_SHEN(1, "阅神"),

    ;

    private final Integer thirdMarkCompanyType;

    private final String thirdMarkCompanyName;

    ThirdMarkCompanyEnum(Integer thirdMarkCompanyType, String thirdMarkCompanyName) {
        this.thirdMarkCompanyType = thirdMarkCompanyType;
        this.thirdMarkCompanyName = thirdMarkCompanyName;
    }

    public Integer thirdMarkCompanyType() {
        return thirdMarkCompanyType;
    }

    public String thirdMarkCompanyName() {
        return thirdMarkCompanyName;
    }
}
