package com.dongni.exam.wrong.bean.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 该类设计有问题了，再添加新的内容需要拆开
 *
 * <AUTHOR>
 * @Date 2025/6/3 周二 上午 11:44
 * @Version 1.0.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WrongQuestionItemVO {
    //----------------------共有的字段-------------------
    private int wrongItemQuestionFrom; // 试题来源考试错题还是其他错题
    private int belongType; // 试题来源题库
    private String questionId; // 试题id
    private double difficulty; // 难度
    private Map<String, Object> question; // 试题详情，列表返回时为空需要展示时再调接口获取详情

    //----------------------考试错题需要的字段-------------------
    private Long examId;
    private String examName;
    private Long examItemId;
    private Integer questionNumber;
    private Long wrongItemId;
    private LocalDateTime createDateTime;
    private Long courseId;
    @JsonIgnore
    private Long startDate; // t_exam.start_date


    //----------------------其他错题需要的字段-------------------
    private Long studentQuestionMarkId;
    private String studentQuestionMarkName;
    private Long studentQuestionMarkItemId;
    private Long studentQuestionMarkWrongItemId;

    //----------------------作业错题需要的字段-------------------
    @JsonIgnore
    private Long homeworkId; // 考试id
    @JsonIgnore
    private String homeworkName; // 考试名称
    @JsonIgnore
    private LocalDateTime homeworkCreateDateTime; // t_exam_class创建时间
    private Long studyGuideWrongItemId;
    private String markQuestionNumbers;

    List<WrongQuestionItemVO> similarQuestions;
}
