package com.dongni.exam.wrong.bean.params;

import com.dongni.commons.entity.BaseRequestParams;
import com.dongni.commons.utils.verify.Verify2;
import lombok.Data;

import java.util.List;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/6/3 周二 下午 05:50
 * @Version 1.0.0
 */
@Data
public class DownloadWrongQuestionParam extends BaseRequestParams {
    private String fileName;

    private Integer outputType;

    private String reviewType;

    private Long studentId;

    private String gulouToken;

    private String uid; // 夸克的uid

    private List<DownloadItem> downloadItemList;

    public void verify() {
        Verify2.of(this)
                .isNotBlank(DownloadWrongQuestionParam::getFileName)
                .isValidId(DownloadWrongQuestionParam::getStudentId)
                .isNotEmpty(DownloadWrongQuestionParam::getDownloadItemList)
                .verify();
    }

    @Data
    public static class DownloadItem {
        private int wrongItemQuestionFrom;
        private int belongType;
        private Long wrongItemId;
        private Long studentQuestionMarkWrongItemId;
        private Long studyGuideWrongItemId;
    }
}
