package com.dongni.exam.wrong.serevice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.dongni.analysis.report.utils.ReportMethodUtil;
import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.FileStoragePathUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.spring.SpringContextUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.exercise.gulou.GuLouDocumentService;
import com.dongni.exam.plan.service.ExamItemService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.exam.student.paper.service.StudentKnowledgeGraspService;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongCourseCountDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDownloadDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongTagItemDTO;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongQuestionExamDownloadParam;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongQuestionGetParam;
import com.dongni.exam.studyguide.service.StudyGuideWrongService;
import com.dongni.exam.wrong.bean.dto.WrongDocumentDTO;
import com.dongni.exam.wrong.bean.dto.WrongDocumentItemDTO;
import com.dongni.exam.wrong.bean.dto.WrongDocumentQuestionAttributeDTO;
import com.dongni.exam.wrong.bean.dto.WrongDocumentQuestionDTO;
import com.dongni.exam.wrong.bean.params.CreatePushTaskParam;
import com.dongni.exam.wrong.bean.params.DownloadWrongQuestionParam;
import com.dongni.exam.wrong.bean.params.WrongQuestionDetailParam;
import com.dongni.exam.wrong.bean.vo.WrongQuestionDetailVO;
import com.dongni.exam.wrong.bean.vo.WrongQuestionVO;
import com.dongni.exam.wrong.mq.WrongExamQuestionMessage;
import com.dongni.exam.wrong.mq.WrongExamQuestionProducer;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.ExamQuestionDocxExport;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.QuestionDocxExport;
import com.dongni.tiku.common.util.QuestionDocxExportUtil;
import com.dongni.tiku.common.util.question.QuestionRenderHtmlUtil;
import com.dongni.tiku.manager.impl.QuestionManager;
import com.dongni.tiku.own.service.IntelligentDongniPaperService;
import com.dongni.tiku.student.question.mark.bean.dto.StudentQuestionMarkWrongItemDownloadDTO;
import com.dongni.tiku.student.question.mark.bean.dto.WrongDownloadQuestionDTO;
import com.dongni.tiku.student.question.mark.service.StudentQuestionMarkItemService;
import com.dongni.tiku.student.question.mark.service.StudentQuestionMarkService;
import com.dongni.tiku.student.question.mark.service.StudentQuestionMarkWrongDownloadHelperService;
import com.dongni.tiku.wrong.book.service.RecommendationSimilarityService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * Created by JinJinDong
 * time:2019/3/11 14:43
 * description: 错题下载
 **/
@Service
public class WrongExamService {

    private static final int step = 50;

    private static final String url = "/api/export/paper/question/mml";

    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private TikuRepository tikuRepository;
    @Autowired
    private StudentQuestionMarkService studentQuestionMarkService;


    @Autowired
    private CourseServiceImpl courseService;

    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;

    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private IntelligentDongniPaperService intelligentDongniPaperService;
    
    @Autowired
    private WrongExamQuestionProducer wrongExamQuestionProducer;

    @Autowired
    private WrongExamQuestionService wrongExamQuestionService;
    @Autowired
    private WrongOtherQuestionService wrongOtherQuestionService;

    @Autowired
    private QuestionManager questionManager;
    @Autowired
    private ExamItemService examItemService;
    @Autowired
    private GuLouDocumentService guLouDocumentService;
    @Autowired
    private RecommendationSimilarityService recommendationSimilarityService;
    @Autowired
    private StudentQuestionMarkItemService studentQuestionMarkItemService;
    @Autowired
    private StudentQuestionMarkWrongDownloadHelperService studentQuestionMarkWrongDownloadHelperService;
    @Autowired
    private StudentKnowledgeGraspService studentKnowledgeGraspService;
    @Autowired
    @Lazy
    private WrongExamService _self;
    @Autowired
    private WrongDocumentPDFRenderService wrongDocumentPDFRenderService;
    @Autowired
    private WrongDocumentQuarkService wrongDocumentQuarkService;
    @Autowired
    private WrongDocumentQuarkPushService wrongDocumentQuarkPushService;
    @Autowired
    private StudyGuideWrongService studyGuideWrongService;
    @Autowired
    private ExamService examService;

    /**
     * @param params courseId startDate endDate studentId gradeType
     * @return 根据周期查询错题
     */
    public Integer getStudentWrongCount(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("schoolId")
                .isValidId("studentId")
                .isNumeric("gradeYear")
                .isNumeric("gradeType")
                .isNotBlank("startDate")
                .isNotBlank("endDate")
                .verify();
        params.put("sourceType", 1);
        params.put("examSchoolStatus", 3);
        //查询错题总数
        return examRepository.selectOne("WrongMapper.getStudentExamWrongCount", params);
    }

    /**
     * 根据考试查询错题
     *
     * @param params studentId courseId gradeType  gradeYear
     * @return 查询所有考试以及错题
     */
    public Map<String, Object> getStudentWrongExam(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .isNumeric("gradeType")
                .isValidId("gradeYear")
                .verify();

        // 0710 需求说需要放开线上
        Integer[] examType = {DictUtil.getDictValue("examType", "offline")};
        params.put("examType", examType);
        params.put("classStatus", DictUtil.getDictValue("examPaperStatus","readComplete"));
        params.put("readComplete", DictUtil.getDictValue("examPaperStatus","readComplete"));
        Long courseId = Long.valueOf(params.get("courseId").toString());
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> exam = examRepository.selectList("WrongMapper.getStudentWrongExam", params);
        List<Map<String, Object>> examClass = examRepository.selectList("WrongMapper.getStudentWrongExamByClass", params);
        exam.addAll(examClass);
        if (exam.size() == 0) {
            result.put("list", Collections.emptyList());
            result.put("totalCount", 0);
            return result;
        }
        List<Map<String,Object>> resultList = new ArrayList<>();
        dealCourse(exam, resultList,courseId);

        //过滤错题为 0 的考试
        if (CollectionUtils.isEmpty(resultList)){
            result.put("list", Collections.emptyList());
            result.put("totalCount", 0);
            return result;
        }
        List<Long> examIds = resultList.stream().map(e -> Long.valueOf(e.get("examId").toString())).collect(Collectors.toList());
        params.put("examIds", examIds);
        params.put("sourceType", 1);
        List<Long> existExam = examRepository.selectList("WrongMapper.getStudentExamExistWrong", params);
        HashSet<Long> exist = new HashSet<>(existExam);
        List<Map<String, Object>> rs = new ArrayList<>();
        for (Map<String, Object> e : resultList) {
            if (exist.contains(Long.valueOf(e.get("examId").toString()))){
                rs.add(e);
            }
        }

        rs = rs.stream()
                .sorted(Comparator.comparing(m -> ((Date) ((Map) m).get("startDate")).getTime()).reversed())
                .collect(Collectors.toList());
        result.put("list", rs);
        result.put("totalCount", rs.size());
        return result;
    }

    public void dealCourse(List<Map<String, Object>> list, List<Map<String, Object>> resultList,Long courseId) {
        List<Map<String, Object>> comprehensiveCourse = courseService.getAllComprehensiveCourse();
        Map<Long, Map<String, Object>> comprehensiveCourseMap = comprehensiveCourse
                .stream()
                .collect(toMap(s -> Long.valueOf(s.get("courseId").toString()), s -> s));
        for (Map<String, Object> map : list) {
            Long courseIdInMap = Long.valueOf(map.get("courseId").toString());
            Map<String, Object> courseDetail = comprehensiveCourseMap.get(courseIdInMap);
            if (!ObjectUtil.isBlank(courseDetail)) {
                Integer memberCount = Integer.valueOf(courseDetail.get("memberCount").toString());
                if (memberCount > 1) {
                    String memberStr = courseDetail.get("memberStr").toString();
                    List<Long> courseIds = StringUtil.strToList(memberStr, ",", Long.class);
                    if (courseIds.contains(courseId)) {
                        resultList.add(map);
                    }
                }
            } else if (courseId.equals(courseIdInMap)) {
                resultList.add(map);
            }
        }
    }
    public void dealCourse(List<Map<String, Object>> list, List<Map<String, Object>> resultList,List<Long> courseIds) {
        List<Map<String, Object>> comprehensiveCourse = courseService.getAllComprehensiveCourse();
        Map<Long, Map<String, Object>> comprehensiveCourseMap = comprehensiveCourse
                .stream()
                .collect(toMap(s -> Long.valueOf(s.get("courseId").toString()), s -> s));
        for (Map<String, Object> map : list) {
            Long courseIdInMap = Long.valueOf(map.get("courseId").toString());
            Map<String, Object> courseDetail = comprehensiveCourseMap.get(courseIdInMap);
            if (!ObjectUtil.isBlank(courseDetail)) {
                Integer memberCount = Integer.valueOf(courseDetail.get("memberCount").toString());
                if (memberCount > 1) {
                    String memberStr = courseDetail.get("memberStr").toString();
                    List<Long> memberIds = StringUtil.strToList(memberStr, ",", Long.class);
                    for (Long id:courseIds){
                        if (memberIds.contains(id)){
                            resultList.add(map);
                            break;
                        }
                    }
                }
            } else if (courseIds.contains(courseIdInMap)) {
                resultList.add(map);
            }
        }
    }

    /**
     * @param params studentId courseId  examId
     * @return 取考试错题数量
     */
    public Integer getStudentWrongExamCount(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("studentId")
                .isValidId("examId")
                .verify();
        params.put("sourceType", 1);
        //查询错题总数
        return examRepository.selectOne("WrongMapper.getStudentExamCount", params);
    }

    /**
     * @param params studentId courseId [examId,startDate,endDate,gradeType,gradeYear]
     * @return 获取试题详情
     */
    public Map<String,Object> getExamQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isValidId("studentId")
                .isValidId("courseId")
                .isNotBlank("fileName")
                .verify();

        List<Map<String, Object>> wrongItems;

        Long examId = MapUtil.getLongNullable(params, "examId");
        // 按周期查询
        if (examId == null) {
            params.put("sourceType", 1);
            params.put("examSchoolStatus", 3);
            Verify.of(params)
                    .isNotBlank("startDate")
                    .isNotBlank("endDate")
                    .isNumeric("gradeType")
                    .isNumeric("gradeYear")
                    .verify();
            wrongItems = getExamQuestionWithPeriodOrExam(params);
        } else {
            Map<String, Object> exam = examService.getExamDetail(MapUtil.of("examId", examId));
            boolean studyGuideHomework = examService.isStudyGuideHomework(exam);
            // 考试查询
            if (!studyGuideHomework) {
                params.put("sourceType", 1);
                params.remove("gradeType");
                params.remove("gradeYear");
                wrongItems = getExamQuestionWithPeriodOrExam(params);
            }
            // 教辅作业查询
            else {
                StudyGuideWrongQuestionExamDownloadParam studyGuideWrongQuestionExamDownloadParam = new StudyGuideWrongQuestionExamDownloadParam();
                studyGuideWrongQuestionExamDownloadParam.setStudentId(MapUtil.getLong(params, "studentId"));
                studyGuideWrongQuestionExamDownloadParam.setCourseId(MapUtil.getLong(params, "courseId"));
                studyGuideWrongQuestionExamDownloadParam.setExamId(examId);
                wrongItems = studyGuideWrongService.getStudyGuideWrongQuestionByExamId4Download(studyGuideWrongQuestionExamDownloadParam);
            }
        }

        // 开始下载
        params.put("wrongItems", wrongItems);
        return _self.downloadWrongQuestion(params);
    }

    /**
     * 按周期或者按ID查询考试错题
     */
    private List<Map<String, Object>> getExamQuestionWithPeriodOrExam(Map<String, Object> params) {
        List<Map<String, Object>> wrongItems = examRepository.selectList("WrongMapper.getStudentExamWrongItem", params);
        wrongItems.forEach(i -> {
            i.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni"));
            i.put("wrongItemQuestionFrom", DictUtil.getDictValue("wrongItemQuestionFrom", "examWrongQuestion"));
        });
        return wrongItems;
    }

    /**
     * 获取试题详情 - 错题列表调用
     */
    public Map<String,Object> getQuestion(DownloadWrongQuestionParam downloadWrongQuestionParam) {
        downloadWrongQuestionParam.verify();

        List<DownloadWrongQuestionParam.DownloadItem> downloadItemList = downloadWrongQuestionParam.getDownloadItemList();
        Map<Long, Map<String, Object>> wrongItemId2QuestionInfo = new HashMap<>();
        Map<Long, Map<String, Object>> studentQuestionMarkWrongItemId2QuestionInfo = new HashMap<>();
        Map<Long, Map<String, Object>> studyGuideWrongItemId2QuestionInfo = new HashMap<>();

        // 考试的错题
        List<Long> wrongItemIds = downloadItemList.stream()
                .filter(i -> DictUtil.isEquals(i.getWrongItemQuestionFrom(), "wrongItemQuestionFrom", "examWrongQuestion"))
                .map(DownloadWrongQuestionParam.DownloadItem::getWrongItemId)
                .collect(toList());
        if (CollectionUtils.isNotEmpty(wrongItemIds)) {
            List<Map<String, Object>> wrongItems = examRepository.selectList("WrongMapper.getStudentExamWrongItemByIds", wrongItemIds);
            wrongItems.forEach(i -> {
                i.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni"));
                i.put("wrongItemQuestionFrom", DictUtil.getDictValue("wrongItemQuestionFrom", "examWrongQuestion"));

                long wrongItemId = MapUtil.getLong(i, "wrongItemId");
                wrongItemId2QuestionInfo.put(wrongItemId, i);
            });
        }

        // 其他错题
        List<Long> studentQuestionMarkWrongItemIds = downloadItemList.stream()
                .filter(i -> DictUtil.isEquals(i.getWrongItemQuestionFrom(), "wrongItemQuestionFrom", "similarQuestion"))
                .map(DownloadWrongQuestionParam.DownloadItem::getStudentQuestionMarkWrongItemId)
                .collect(toList());
        if (CollectionUtils.isNotEmpty(studentQuestionMarkWrongItemIds)) {
            List<StudentQuestionMarkWrongItemDownloadDTO> studentQuestionMarkWrongItem4DownloadDTOList
                    = studentQuestionMarkItemService.getStudentQuestionMarkWrongItem4Download(studentQuestionMarkWrongItemIds);
            studentQuestionMarkWrongItem4DownloadDTOList.forEach(i -> {
                        Long studentQuestionMarkWrongItemId = i.getStudentQuestionMarkWrongItemId();
                        Map<String, Object> map = BeanUtil.beanToMap(i);
                        studentQuestionMarkWrongItemId2QuestionInfo.put(studentQuestionMarkWrongItemId, map);
                    });
        }

        // 教辅作业错题
        List<Long> studyGuideWrongItemIds = downloadItemList.stream()
                .filter(i -> DictUtil.isEquals(i.getWrongItemQuestionFrom(), "wrongItemQuestionFrom", "studyGuideWrongQuestion"))
                .map(DownloadWrongQuestionParam.DownloadItem::getStudyGuideWrongItemId)
                .collect(toList());
        if (CollectionUtils.isNotEmpty(studyGuideWrongItemIds)) {
            List<StudyGuideWrongItemDownloadDTO> studyGuideWrongQuestion4Download
                    = studyGuideWrongService.getStudyGuideWrongQuestion4Download(studyGuideWrongItemIds);
            studyGuideWrongQuestion4Download.forEach(i -> {
                Long studyGuideWrongItemId = i.getStudyGuideWrongItemId();
                Map<String, Object> map = BeanUtil.beanToMap(i);
                studyGuideWrongItemId2QuestionInfo.put(studyGuideWrongItemId, map);
            });
        }

        // 保持入参顺序
        List<Map<String, Object>> questionList = new ArrayList<>();
        for (DownloadWrongQuestionParam.DownloadItem downloadItem : downloadItemList) {
            int wrongItemQuestionFrom = downloadItem.getWrongItemQuestionFrom();
            if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "examWrongQuestion")) {
                Long wrongItemId = downloadItem.getWrongItemId();
                Map<String, Object> questionInfo = wrongItemId2QuestionInfo.get(wrongItemId);
                questionList.add(questionInfo);
            } else if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "similarQuestion")) {
                Long studentQuestionMarkWrongItemId = downloadItem.getStudentQuestionMarkWrongItemId();
                Map<String, Object> questionInfo = studentQuestionMarkWrongItemId2QuestionInfo.get(studentQuestionMarkWrongItemId);
                questionList.add(questionInfo);
            } else if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "studyGuideWrongQuestion")) {
                Long studyGuideWrongItemId = downloadItem.getStudyGuideWrongItemId();
                Map<String, Object> questionInfo = studyGuideWrongItemId2QuestionInfo.get(studyGuideWrongItemId);
                questionList.add(questionInfo);
            } else {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "wrongItemQuestionFrom=" + wrongItemQuestionFrom + "字典值不存在!");
            }
        }

        // 兼容原来的代码还是转为map
        Map<String, Object> params = BeanUtil.beanToMap(downloadWrongQuestionParam, false, true);
        params.put("wrongItems", questionList);
        return _self.downloadWrongQuestion(params);
    }

    /**
     * params.wrongItems来源于考试错题和其他错题，俩者字段不一致
     * 下载错题发送到mq
     */
    @Transactional(transactionManager = ExamRepository.TRANSACTION, rollbackFor = Exception.class)
    public Map<String, Object> downloadWrongQuestion(Map<String, Object> params) {
        List<Map<String, Object>> wrongItems = MapUtil.getCast(params, "wrongItems");
        if (CollectionUtils.isEmpty(wrongItems)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "本次任务无错题数据");
        }

        params.put("documentName",params.get("fileName") + ".pdf");
        addWrongDocument(params);
        long wrongDocumentId = MapUtil.getLong(params, "wrongDocumentId");
        // 如果是从夸克进来的
        String uid = MapUtil.getStringNullable(params, "uid");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(uid)) {
            CreatePushTaskParam createPushTaskParam = new CreatePushTaskParam();
            createPushTaskParam.setUid(uid);
            createPushTaskParam.setWrongDocumentId(wrongDocumentId);
            createPushTaskParam.setType(MapUtil.getInt(params, "type"));
            createPushTaskParam.setDocumentName(MapUtil.getString(params, "documentName"));
            wrongDocumentQuarkService.createTask(createPushTaskParam);
        }

        // 如果是鼓楼过来的
        if (params.containsKey("gulouToken")) {
            guLouDocumentService.insertPushRecord(params);
        }

        Map<String, Object> questionDocxParams = QuestionDocxExportUtil.getQuestionDocxParams(params);

        // // 异步下载任务
        // myAsyncConfigurer.getAsyncExecutor().execute(()->dealDocxFile(questionDocxParams,wrongItems));

//        // 通过消息队列执行下载任务
//        WrongExamQuestionMessage wrongExamQuestionMessage = new WrongExamQuestionMessage();
//        wrongExamQuestionMessage.setWrongDocumentId(MapUtil.getLong(params, "wrongDocumentId"));
//        wrongExamQuestionMessage.setQuestionDocxParams(questionDocxParams);
//        wrongExamQuestionMessage.setWrongItems(wrongItems);
//        wrongExamQuestionProducer.send(wrongExamQuestionMessage);

        dealDocxFile(questionDocxParams, wrongItems);

        Map<String, Object> rs = new HashMap<>(2);
        rs.put("wrongDocumentId",params.get("wrongDocumentId"));
        rs.put("fileName",params.get("documentName"));
        return rs;
    }

    public void dealDocxFile(Map<String, Object> params, List<Map<String, Object>> wrongItems) {
//        // 目前只支持进了校本题库的试题可以下载
//        wrongItems = wrongItems.stream()
//                .filter(i -> DictUtil.isEquals(MapUtil.getInt(i, "belongType"),
//                        "questionBankBelongType", "dongni", "yiqi", "wusanInside"))
//                .collect(Collectors.toList());

        // 可以举一反三的试题-校本题库的题才可以
        List<String> canOne2MoreQuestionIds = wrongItems.stream()
                .filter(i -> DictUtil.isEquals(MapUtil.getInt(i, "belongType"),
                        "questionBankBelongType", "dongni", "yiqi", "wusanInside"))
                .map(item -> item.get("questionId").toString()).collect(Collectors.toList());

        // 判断前端是否勾选了举一反三
        Object reviewTypeObj = params.get("reviewType");
        int[] reviewTypes;
        if (reviewTypeObj instanceof List) {
            // 序列化反序列化之后会变成List
            List<Integer> reviewTypeList = MapUtil.getCast(reviewTypeObj);
            reviewTypes = reviewTypeList.stream().mapToInt(Integer::intValue).toArray();
        } else {
            reviewTypes = MapUtil.getCast(reviewTypeObj);
        }
        Integer reviewTypeExtend = DictUtil.getDictValue("reviewType", "extend");
        boolean hasOne2Three = Arrays.stream(reviewTypes).anyMatch(reviewTypeExtend::equals);

        // 举一反三-只有前端勾选了才需要执行
        Map<String, List<Document>> one2ThreeMap = new HashMap<>();
        if (hasOne2Three && CollectionUtils.isNotEmpty(canOne2MoreQuestionIds)) {
            Map<String, Object> one2ThreeParams = new HashMap<>(params);
            one2ThreeParams.put("questionIds", canOne2MoreQuestionIds);
            one2ThreeMap = intelligentDongniPaperService.one2Three(one2ThreeParams);
        }

//        Map<String, Map<String, Object>> allData = new HashMap<>();
//        //每次发50个
//        renderQuestion(params, new ArrayList<>(allQuestionIds), allData);
        for (Map<String, Object> map : wrongItems) {
//            Map<String,Object> data = allData.get(map.get("questionId").toString());
//            if (MapUtils.isNotEmpty(data)){
//                map.putAll(data);
//            }

            if (hasOne2Three) {
                List<Document> one2ThreeQuestions = one2ThreeMap.get(map.get("questionId").toString());
                if (CollectionUtils.isNotEmpty(one2ThreeQuestions)) {
                    map.put("one2MoreList", one2ThreeQuestions);
                    map.put("one2MoreDesc", "举一反三");
                }
            }
        }

        WrongDownloadQuestionDTO wrongDownloadQuestionDTO = new WrongDownloadQuestionDTO();
        wrongDownloadQuestionDTO.setQuestionList(wrongItems);
        wrongDownloadQuestionDTO.setStudentId(MapUtil.getLong(params, "studentId"));
        wrongDownloadQuestionDTO.setWrongDocumentId(MapUtil.getLong(params, "wrongDocumentId"));
        boolean fromGulou = params.containsKey("gulouToken");
        String redirectUrl = studentQuestionMarkWrongDownloadHelperService.getRedirectUrlWithWrongDownload(fromGulou, wrongDownloadQuestionDTO);

        long wrongDocumentId = MapUtil.getLong(params, "wrongDocumentId");
        WrongDocumentItemDTO wrongDocumentItemDTO = getWrongDocumentItemDTO(wrongItems, redirectUrl, params, false);
        WrongDocumentDTO wrongDocumentDTO = new WrongDocumentDTO();
        wrongDocumentDTO.setZipFileName(null); // 非PC端下载错题，不需要打包
        wrongDocumentDTO.setDocuments(Collections.singletonList(wrongDocumentItemDTO));
        wrongDocumentPDFRenderService.createTask(wrongDocumentId, wrongDocumentDTO);

//                (redirectUrl) -> {
//
//                    QuestionDocxExport export = new ExamQuestionDocxExport(reviewTypes, (int) params.get("outputType"));
//                    boolean xl = false;
//                    if(!finalWrongItems.isEmpty()) {
//                        if(finalWrongItems.get(0).get("courseId") != null) {
//                            Long courseId = MapUtil.getLong(finalWrongItems.get(0).get("courseId"));
//                            String courseName = courseService.getCourseDetail(MapUtil.of("courseId", courseId)).get("courseName").toString();
//                            if(courseName.equals("物理") || courseName.equals("数学") || courseName.equals("化学") || courseName.equals("生物")){
//                                xl = true;
//                            }
//                        }
//                    }
//                    return export.generateFileToFileStorage(fileName + ".docx", fileName, finalWrongItems, redirectUrl,xl);
//                });

//        //更新记录表
//        updateWrongDocument(params, link);
    }

    public void renderQuestion(Map<String, Object> params, List<String> questionIds, Map<String, Map<String, Object>> allData) {
        for (int i = 0; i < questionIds.size(); i += step) {
            List<String> once = questionIds.stream().skip(i).limit(step).collect(Collectors.toList());
            params.put("questionIds", once);
            Map result = restTemplate.postForObject(
                    SpringContextUtil.getProperty("dongni.node.host") + url, params, Map.class);
            Map<String, Map<String, Object>> data = (Map) result.get("data");
            allData.putAll(data);
        }
    }

    /**
     * @param params studentId courseId
     * @return 获取试题id
     */
    public List<Map<String, Object>> getSimpleExamQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .isValidId("examId")
                .verify();
        params.put("sourceType", 1);
        //按考试
        List<Map<String, Object>> wrongItems = examRepository.selectList("WrongMapper.getStudentExamWrongItem", params);
        if (CollectionUtils.isEmpty(wrongItems)){
            return Collections.EMPTY_LIST;
        }
        return wrongItems;
    }


    /**
     * 错题文档保存
     *
     * @param params name status
     */
    public void insertExamWrongDocument(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("documentName")
                .isNumeric("status")
                .isNumeric("type")
                .verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        examRepository.insert("WrongMapper.insertWrongDocument", params);
    }

    /**
     * 错题文档保存
     *
     * @param params wrongDocumentId
     */
    public void updateExamWrongDocument(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongDocumentId")
                .isNumeric("status")
                .isNotBlank("finishTime")
                .verify();
        examRepository.update("WrongMapper.updateWrongDocument", params);
    }

    /**
     * 错题文档批量更新失败
     *
     * @param params wrongDocumentIds finishTime currentTime status
     */
    public void batchUpdateExamWrongDocumentFailed(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("wrongDocumentIds")
                .isNotNull("finishTime")
                .isNotBlank("currentTime")
                .isNumeric("status")
                .verify();
        examRepository.update("WrongMapper.batchUpdateExamWrongDocumentFailed", params);
    }

    /**
     * 错题文档列表
     *
     * @param params userId
     * @return 错题文档列表
     */
    public Map<String, Object> getExamWrongDocument(Map<String, Object> params) {
        Verify.of(params).verify();
        //当前时间减一天
        LocalDateTime now = LocalDateTime.now().minusDays(1);
        params.put("nowTime", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        Integer totalCount = examRepository.selectOne("WrongMapper.getExamDocumentCount", params);
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount",totalCount);
        if(totalCount==0){
            result.put("list", new ArrayList<>());
            return result;
        }

        List<Map<String, Object>> wrongDocument = examRepository.selectList("WrongMapper.getExamDocument", params);
        result.put("list", wrongDocument);
        publicWrongDocumentLink(wrongDocument);
        return result;

    }

    public Map<String,Object> getStudentWrongExamList(Map<String,Object> params) {
        Verify.of(params).isValidId("studentId").verify();

        Map<String, Object> resultMap = new HashMap<>();

        params.put("examType", params.get("examType").toString().split(","));
        params.put("readComplete", DictUtil.getDictValue("examPaperStatus", "readComplete"));
        params.put("classStatus", DictUtil.getDictValue("examPaperStatus", "readComplete"));

        List<Map<String, Object>> examList;
        List<Map<String, Object>> examByClassList;
        //判断查询全部还是单科
        if (ObjectUtil.isBlank(params.get("courseIds"))) {
            examList = examRepository.selectList("ExamResultMapper.getExamStudent", params);
            examByClassList = examRepository.selectList("ExamResultMapper.getExamStudentByClass", params);
            examList.addAll(examByClassList);
            if (examList.size() == 0) {
                resultMap.put("exam", Collections.emptyList());
                resultMap.put("totalCount", 0);
                return resultMap;
            }
            //过滤错题为零的考试
            List<Long> examIds = examList.stream().map(e -> Long.valueOf(e.get("examId").toString())).collect(Collectors.toList());
            Set<Long> examIdSet = new HashSet<>(examIds);
            params.put("examIds", examIds);
            params.put("sourceType", 1);
            List<Long> existExam = examRepository.selectList("WrongMapper.getStudentExamExistWrongByExam", params);
            existExam = existExam.stream().filter(examIdSet::contains).collect(Collectors.toList());
            HashSet<Long> exist = new HashSet<>(existExam);
            List<Map<String, Object>> rs = new ArrayList<>();
            for (Map<String, Object> e : examList) {
                if (exist.contains(Long.valueOf(e.get("examId").toString()))) {
                    rs.add(e);
                }
            }
            examList = rs;
        } else {
            List<Long> courseIds = StringUtil.strToList(params.get("courseIds").toString(),",",Long.class);
            params.put("courseIds",courseIds);
            examList = examRepository.selectList("ExamResultMapper.getStudentExam", params);
            examByClassList = examRepository.selectList("ExamResultMapper.getStudentExamByClass", params);
            examList.addAll(examByClassList);
            List<Map<String, Object>> resultList = new ArrayList<>();
            dealCourse(examList, resultList, courseIds);
            examList = resultList;
            if (examList.size() == 0) {
                resultMap.put("exam", Collections.emptyList());
                resultMap.put("totalCount", 0);
                return resultMap;
            }
            //过滤错题为零的考试
            List<Long> examIds = examList.stream().map(e -> Long.valueOf(e.get("examId").toString())).collect(Collectors.toList());
            params.put("examIds", examIds);
            params.put("sourceType", 1);
            List<Long> existExam = examRepository.selectList("WrongMapper.getStudentExamExistWrongIn", params);
            HashSet<Long> exist = new HashSet<>(existExam);
            List<Map<String, Object>> rs = new ArrayList<>();
            for (Map<String, Object> e : examList) {
                if (exist.contains(Long.valueOf(e.get("examId").toString()))) {
                    rs.add(e);
                }
            }
            examList = rs;
        }

        if (examList.size() == 0) {
            resultMap.put("exam", Collections.emptyList());
            resultMap.put("totalCount", 0);
            return resultMap;
        }
        //按时间过滤
        if (!ObjectUtil.isBlank(params.get("queryStartDate")) && !ObjectUtil.isBlank(params.get("queryEndDate"))) {
            Long queryStartDate = Long.valueOf(params.get("queryStartDate").toString());
            Long queryEndDate = Long.valueOf(params.get("queryEndDate").toString());
            examList = examList.stream().filter(s -> {
                Date date = (Date) s.get("startDate");
                return date.getTime() >= queryStartDate && date.getTime() <= queryEndDate;
            }).collect(Collectors.toList());
        }
        //查询错题数量
        List<Long> examIds = examList.stream().map(e -> Long.valueOf(e.get("examId").toString())).collect(Collectors.toList());
        params.put("examIds",examIds);
        if (CollectionUtils.isEmpty(examIds)){
            resultMap.put("exam", Collections.emptyList());
            resultMap.put("totalCount", 0);
            return resultMap;
        }
        if (ObjectUtil.isBlank(params.get("courseIds"))) {
            params.remove("courseIds");
        }
        Map<Long,List<Map<String,Object>>> examMap = examList.stream().collect(Collectors.groupingBy(s->Long.valueOf(s.get("examId").toString())));
        List<Map<String,Object>> rs2 = new ArrayList<>();
        Set<Long> examIdSet = new HashSet<>(examIds);
        List<Map<String,Object>> wrongItemCountList =
                examRepository.selectList("WrongMapper.getStudentExamCountIn",params);
        wrongItemCountList = wrongItemCountList.stream()
                        .filter(x -> examIdSet.contains((Long) x.get("examId"))).collect(Collectors.toList());
        Map<Long,List<Map<String,Object>>> wrongItemCountMap = wrongItemCountList.stream()
                .collect(Collectors.groupingBy(s->Long.valueOf(s.get("examId").toString())));
        for (Map.Entry<Long,List<Map<String,Object>>> entry:examMap.entrySet()){
            Long examId = entry.getKey();
            Map<String,Object> map = entry.getValue().get(0);
            List<Map<String,Object>> wrongItemCount = new ArrayList<>();
            wrongItemCount.addAll(wrongItemCountMap.get(examId));
            map.put("wrongItemCount",wrongItemCount);
            rs2.add(map);
        }
        examList = rs2;
        //分页
        if (!ObjectUtil.isBlank(params.get("currentIndex")) && !ObjectUtil.isBlank(params.get("pageSize"))) {
            Integer currentIndex = Integer.valueOf(params.get("currentIndex").toString());
            Integer pageSize = Integer.valueOf(params.get("pageSize").toString());
            List<Map<String, Object>> exam = examList.stream()
                    .sorted(Comparator.comparing(m -> ((Date) ((Map) m).get("startDate")).getTime()).reversed())
                    .skip(currentIndex).limit(pageSize)
                    .collect(Collectors.toList());
            resultMap.put("totalCount", examList.size());
            resultMap.put("exam", exam);
        } else {
            //时间降序
            List<Map<String, Object>> exam = examList.stream()
                    .sorted(Comparator.comparing(m -> ((Date) ((Map) m).get("startDate")).getTime()).reversed())
                    .collect(Collectors.toList());
            resultMap.put("totalCount", exam.size());
            resultMap.put("exam", exam);
        }
        return resultMap;
    }

    //根据examId和studentId获取错题
    public Map<Long,List<Map<String,Object>>> getWrongItem(Map<String,Object> params){
        Verify.of(params)
                .isNotBlank("studentId")
                .isNotBlank("examId")
                .verify();
        Map<Long,List<Map<String,Object>>> rs = new HashMap<>();
        if (ObjectUtil.isBlank(params.get("courseIds"))){
            //根据examId查询有错题的科目
            List<Long> courseIds = examRepository.selectList("WrongMapper.getStudentExamCourse",params);
            dealEach(params, rs, courseIds);
            return rs;
        }else {
            //根据前端传来的courseIds
            List<Long> courseIds = StringUtil.strToList(params.get("courseIds").toString(),",",Long.class);
            dealEach(params,rs,courseIds);
            return rs;
        }
    }

    private void dealEach(Map<String, Object> params, Map<Long, List<Map<String, Object>>> rs, List<Long> courseIds) {
        for (Long courseId:courseIds){
            params.put("courseId",courseId);
            rs.put(courseId,getSimpleExamQuestion(params));
        }
    }

    /**
     * 错题下载
     * @param params
     *            "reviewType":2,    //reviewType  内容类型，1 题目，2 解析，3 作答， 4 举一反三
     *            "outputType":1,		//outputType  格式类型，1 公式，2 图片
     * @return      下载文件的信息
     */
    public Map<String,Object> downloadWrongItem(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("studentId")
                .verify();

        List<Long> examIds = StringUtil.strToList(params.get("examIds").toString(), ",", Long.class);
        Map<String,Object> rs = new HashMap<>();
        params.put("examIds",examIds);
        Map<String,Object> studentInfo = examRepository.selectOne("WrongMapper.getExamStudent",params);
        if(MapUtils.isNotEmpty(studentInfo)){
            params.putAll(studentInfo);
        }
        //分科目放错题
        Map<Long,List<Map<String,Object>>> courseWrongMap = new HashMap<>();
        for (Long examId:examIds){
            params.put("examId",examId);
            Map<Long,List<Map<String,Object>>> wrong = getWrongItem(params);
            for (Map.Entry<Long,List<Map<String,Object>>> entry:wrong.entrySet()){
                Long courseId = entry.getKey();
                List<Map<String,Object>> wrongItem = entry.getValue();
                if (CollectionUtils.isNotEmpty(wrongItem)){
                    if (courseWrongMap.keySet().contains(courseId)){
                        List<Map<String,Object>> exist = courseWrongMap.get(courseId);
                        exist.addAll(wrongItem);
                    }else {
                        courseWrongMap.put(courseId,wrongItem);
                    }
                }
            }
        }
        if (MapUtils.isNotEmpty(courseWrongMap)){
            getFileName(courseWrongMap,params);
            //生成下载记录
            addWrongDocument(params);

            //执行生成任务
            Map<String, Object> p = QuestionDocxExportUtil.getQuestionDocxParams(params);
            dealDocxFile(p,courseWrongMap);

            //返回主键id前端查询
            rs.put("wrongDocumentId",params.get("wrongDocumentId"));
            rs.put("fileName",params.get("documentName"));
            return rs;
        }
        return rs;
    }

    private void getFileName(Map<Long,List<Map<String,Object>>> courseWrongMap,Map<String,Object> params) {
        String fileName;
        if (courseWrongMap.size() == 1) {
            Map.Entry<Long, List<Map<String, Object>>> entry = courseWrongMap.entrySet().iterator().next();
            Long courseId = entry.getKey();
            List<Map<String, Object>> list = entry.getValue();
            params.put("courseId", courseId);
            String courseName = courseService.getCourseDetail(params).get("courseName").toString();
            Set<String> examNameSet = list.stream().map(s -> s.get("examName").toString()).collect(Collectors.toSet());
            //单场考试
            if (examNameSet.size() == 1) {
                fileName = examNameSet.iterator().next() +"-"+ courseName +"-"+ "错题.zip";
            } else {
                fileName = courseName +"-"+ "错题.zip";
            }
        } else {
            fileName = "错题下载.zip";
        }
        params.put("documentName",fileName);
    }

    private void addWrongDocument(Map<String,Object> params) {
        params.put("status",0);
        params.put("type",1);
        params.put("currentTime",DateUtil.getCurrentDateTime());
        examRepository.insert("WrongMapper.insertWrongDocument",params);
    }

    private void dealDocxFile(Map<String, Object> params, Map<Long, List<Map<String, Object>>> courseWrongMap) {
        List<WrongDocumentItemDTO> wrongDocumentItemDTOList = new ArrayList<>();
        for (Map.Entry<Long, List<Map<String, Object>>> entry : courseWrongMap.entrySet()) {
            //获取单个文件名称
            Long courseId = entry.getKey();
            List<Map<String, Object>> wrongItemList = entry.getValue();
            wrongItemList.forEach(i -> i.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni")));
            params.put("courseId", courseId);
            String courseName = courseService.getCourseDetail(params).get("courseName").toString();
            Set<String> examNameSet = wrongItemList.stream().map(s -> s.get("examName").toString()).collect(Collectors.toSet());
            String fileName;
            //单场考试
            if (examNameSet.size() == 1) {
                fileName = examNameSet.iterator().next() + courseName + "错题";
            } else {
                fileName = courseName + "错题";
            }
            params.put("fileName", fileName);
            //发请求渲染试题
            List<String> questionIds = wrongItemList.stream().map(s -> s.get("questionId").toString()).collect(Collectors.toList());

            // 判断前端是否勾选了举一反三
            boolean hasOne2Three = false;
            int[] reviewType = MapUtil.getCast(params, "reviewType");
            for (int i : reviewType) {
                if (DictUtil.isEquals(i, "reviewType", "extend")) {
                    hasOne2Three = true;
                    break;
                }
            }
            // 举一反三-只有前端勾选了才需要执行
            Map<String, List<Document>> one2ThreeMap = new HashMap<>();
            if (hasOne2Three) {
                Map<String, Object> one2ThreeParams = new HashMap<>(params);
                one2ThreeParams.put("questionIds", questionIds);
                one2ThreeMap = intelligentDongniPaperService.one2Three(one2ThreeParams);
            }

//            Map<String, Map<String, Object>> allData = new HashMap<>();
//            //每次发50个
//            renderQuestion(params, new ArrayList<>(allQuestionIds), allData);
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Map<Integer, String> examTypeMap = getExamType();
//            List<Map<String, Object>> questionList = new ArrayList<>();
            for (Map<String, Object> map : wrongItemList) {
                //给每道题加上tagName，如：(2019-07-10 期末考 试卷题号1)
                Integer examType = Integer.valueOf(map.get("examType").toString());
                String tagName = format.format(map.get("startDate")) + " " + examTypeMap.get(examType) + "，考试题号" + map.get("structureNumber");
                map.put("tagName", tagName);

                if (hasOne2Three) {
                    List<Document> one2ThreeQuestionIds = one2ThreeMap.get(map.get("questionId").toString());
                    if (CollectionUtils.isNotEmpty(one2ThreeQuestionIds)) {

                        map.put("one2MoreList", one2ThreeQuestionIds);
                        map.put("one2MoreDesc", "举一反三");
                    }
                }
            }
            WrongDocumentItemDTO wrongDocumentItemDTO = getWrongDocumentItemDTO(wrongItemList, null, params, true);
            wrongDocumentItemDTOList.add(wrongDocumentItemDTO);
        }
        WrongDocumentDTO wrongDocumentDTO = new WrongDocumentDTO();
        wrongDocumentDTO.setZipFileName(MapUtil.getString(params, "documentName"));
        wrongDocumentDTO.setDocuments(wrongDocumentItemDTOList);
        wrongDocumentPDFRenderService.createTask(MapUtil.getLong(params, "wrongDocumentId"), wrongDocumentDTO);
    }

    private Map<Integer,String> getExamType() {
        //试题信息放入wrongItemList
        Map<String,Object> examTypes = DictUtil.getDict("examType");
        //以value-label做map
        Map<Integer,String> examTypesMap = new HashMap<>();
        for (Map.Entry<String,Object> entry:examTypes.entrySet()){
            Map<String,Object> map = (Map)entry.getValue();
            examTypesMap.put(Integer.valueOf(map.get("value").toString()),map.get("label").toString());
        }
        return examTypesMap;
    }

    private void updateWrongDocument(Map<String, Object> params, String link) {
        params.put("link",link);
        params.put("status",1);
        params.put("finishTime",DateUtil.getCurrentDateTime());
        params.put("currentTime",DateUtil.getCurrentDateTime());
        examRepository.update("WrongMapper.updateWrongDocument",params);
    }

    private File generateDocxFile(int[] reviewType, int outputType, List<Map<String,Object>> wrongItemList, String fileName,String path) {
        QuestionDocxExport export = new ExamQuestionDocxExport(reviewType, outputType);
        File file = export.generateFileToLocalPath(fileName + ".docx", fileName,wrongItemList,path);
        return file;
    }

    public Map<String,Object> getExamDocumentById(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("wrongDocumentId")
                .verify();
        return examRepository.selectOne("WrongMapper.getExamDocumentById",params);
    }

    public Map<String,Object> getExamDocumentInfoById(long wrongDocumentId) {
        Map<String, Object> doc = examRepository.selectOne("WrongMapper.getExamDocumentInfoById", wrongDocumentId);
        if (MapUtils.isEmpty(doc)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "wrongDocumentId：" + wrongDocumentId + "下载记录不存在");
        }
        return doc;
    }

    /**
     * 删除下载记录
     *
     * @param params wrongDocumentId
     */
    public void deleteExamWrongDocument(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongDocumentId")
                .verify();

        Map<String, Object> examWrongDocument = getExamWrongDocument(params);
        String link = MapUtil.getStringNullable(examWrongDocument, "link");

        // 删除记录
        examRepository.delete("WrongMapper.deleteWrongDocument",params);

        // 删除文件-虽然会自动过期
        if (StringUtils.isNotBlank(link)) {
            try {
                FileStorageTemplate.delete(link);
            } catch (Exception ignored) {
                // ...
            }
        }
    }

    /**
     * 根据id获取生成状态
     *
     * @param params wrongDocumentIds
     * @return wrongDocumentId status link
     */
    public List<Map<String, Object>> checkWrongDocument(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("wrongDocumentIds")
                .verify();

        String wrongDocumentIdsStr = MapUtil.getString(params, "wrongDocumentIds");
        List<Long> wrongDocumentIds = StringUtil.strToList(wrongDocumentIdsStr, StrUtil.COMMA, Long.class);
        List<Map<String, Object>> wrongDocumentList = examRepository.selectList("WrongMapper.checkWrongDocument", wrongDocumentIds);
        publicWrongDocumentLink(wrongDocumentList);
        return wrongDocumentList;
    }

    /**
     * 根据id获取生成状态
     *
     * @return wrongDocumentId status link
     */
    public List<Map<String, Object>> getWrongDocumentByIds(List<Long> wrongDocumentIds) {
        if (CollectionUtils.isEmpty(wrongDocumentIds)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> wrongDocumentList = examRepository.selectList("WrongMapper.checkWrongDocument", wrongDocumentIds);
        publicWrongDocumentLink(wrongDocumentList);
        return wrongDocumentList;
    }

    public Map<String, Object> getWrongDocumentById(Long wrongDocumentId) {
        Map<String, Object> wrongDocument = examRepository.selectOne("WrongMapper.getWrongDocumentById", wrongDocumentId);
        FileStoragePathUtil.publicPath(wrongDocument, "link", "publicLink");
        return wrongDocument;
    }
    
    /**
     * 获取错题列表的课程参数
     *   页面: 小力讲错题 最上面的课程筛选项
     * @param params studentId
     * @return [{courseId, courseName}]
     */
    public List<Map<String, Object>> getStudentWrongWechatParamCourseList(Map<String, Object> params) {
        return studentKnowledgeGraspService.getWechatParamCourseList(params);
    }

    /**
     * 获取错题列表的课程参数 - 带每个课程的错题数量
     *   页面: 小力讲错题 最上面的课程筛选项
     * @param params studentId schoolId
     * @return [{courseId, courseName, wrongCount}]
     */
    public List<Map<String, Object>> getStudentWrongWechatParamCourseWithCountList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isValidId("studentId")
                .verify();

        // 可展示的课程
        List<Map<String, Object>> courseList = studentKnowledgeGraspService.getWechatParamCourseList(params);
        // 填充每个课程的错题数量
        List<Map<String, Object>> examWrong = wrongExamQuestionService.countStudentWrongGroupByCourseId(params); // 考试错题(不含类题)
        long studentId = MapUtil.getLong(params, "studentId");
        List<Map<String, Object>> otherWrong = studentQuestionMarkItemService.countWrongItemGroupByCourseId(studentId); // 其他错题
        List<StudyGuideWrongCourseCountDTO> studyGuideWrong = studyGuideWrongService.countStudentGuideWrongGroupByCourse(studentId);

        Map<Long, Integer> courseId2Count = new HashMap<>(courseList.size());
        examWrong.forEach(i -> {
            long courseId = MapUtil.getLong(i, "courseId");
            int count = MapUtil.getInt(i, "count");
            courseId2Count.merge(courseId, count, Integer::sum);
        });
        otherWrong.forEach(i -> {
            long courseId = MapUtil.getLong(i, "courseId");
            int count = MapUtil.getInt(i, "count");
            courseId2Count.merge(courseId, count, Integer::sum);
        });
        studyGuideWrong.forEach(i -> {
            long courseId = i.getCourseId();
            int count = i.getCount();
            courseId2Count.merge(courseId, count, Integer::sum);
        });

        for (Map<String, Object> course : courseList) {
            long courseId = MapUtil.getLong(course, "courseId");
            Integer count = courseId2Count.getOrDefault(courseId, 0);
            course.put("wrongCount", count);
        }

        return courseList;
    }
    
    /**
     * 学生获取错题本试题列表
     *
     * @param params studentId schoolId courseId
     *               [difficultyType=easy middle difficulty]
     *               wrongItemQuestionFrom（字典值wrongItemQuestionFrom） 1-考试错题 2-其他试题
     *               startDate endDate 时间范围
     */
    public WrongQuestionVO getStudentWrongList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .isValidId("courseId")
                .isValidId("userId")
                .verify();

        // 处理难度
        if (ObjectUtil.isNotBlank(params.get("difficultyType"))) {
            String difficultyType = MapUtil.getString(params, "difficultyType");
            switch (difficultyType) {
                case "difficulty":
                    params.put("difficultyLeft", 0);
                    params.put("difficultyRight", 0.4);
                    break;
                case "middle":
                    params.put("difficultyLeft", 0.4);
                    params.put("difficultyRight", 0.7);
                    break;
                case "easy":
                    params.put("difficultyLeft", 0.7);
                    params.put("difficultyRight", 1);
                    break;
                default:
            }
        }

        // 时间筛选项兼容处理
        if (ObjectUtil.isBlank(params.get("startDate")) || ObjectUtil.isBlank(params.get("endDate"))) {
            if (ObjectUtil.isBlank(params.get("examStartDate"))) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "无时间筛选");
            }
            String examStartDate = MapUtil.getString(params, "examStartDate");
            params.put("startDate", examStartDate);
            params.put("endDate", cn.hutool.core.date.DateUtil.now());
        }
        DateTime startDate = cn.hutool.core.date.DateUtil.parse(MapUtil.getString(params, "startDate"));
        DateTime endDate = cn.hutool.core.date.DateUtil.parse(MapUtil.getString(params, "endDate"));
        long days = cn.hutool.core.date.DateUtil.betweenDay(startDate, endDate, true);
        if (days > 180) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "时间范围超过180天，请缩小筛选范围");
        }

        int wrongItemQuestionFrom = MapUtil.getInt(params, "wrongItemQuestionFrom");
        // 考试错题
        if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "examWrongQuestion")) {
            return wrongExamQuestionService.getStudentWrongList(params);
        }
        // 其他错题
        else if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "similarQuestion")) {
            return wrongOtherQuestionService.getStudentWrongList(params);
        }
        // 教辅作业错题
        else if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "studyGuideWrongQuestion")) {
            StudyGuideWrongQuestionGetParam studyGuideWrongQuestionGetParam = new StudyGuideWrongQuestionGetParam();
            studyGuideWrongQuestionGetParam.setStudentId(MapUtil.getLong(params, "studentId"));
            studyGuideWrongQuestionGetParam.setCourseId(MapUtil.getLong(params, "courseId"));
            studyGuideWrongQuestionGetParam.setDifficultyLeft(MapUtil.getDoubleNullable(params, "difficultyLeft"));
            studyGuideWrongQuestionGetParam.setDifficultyRight(MapUtil.getDoubleNullable(params, "difficultyRight"));
            studyGuideWrongQuestionGetParam.setStartDate(MapUtil.getString(params, "startDate"));
            studyGuideWrongQuestionGetParam.setEndDate(MapUtil.getString(params, "endDate"));
            return studyGuideWrongService.getStudyGuideWrongQuestion(studyGuideWrongQuestionGetParam);
        } else {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "wrongItemQuestionFrom=" + wrongItemQuestionFrom + "字典值不存在!");
        }
    }

    /**
     * 学生获取错题本试题详情
     *
     * @param params studentId wrongItemId
     * @return studentId wrongItemId examItemId
     *         question -- 试题详情
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    public Map<String, Object> getStudentWrongDetail(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("wrongItemId")
                .verify();

        // 查询错题是否还存在
        Map<String, Object> wrongItem = examRepository.selectOne("WrongMapper.getWrongItemDetail", params);
        if (MapUtils.isEmpty(wrongItem)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "错题不存在,请刷新列表!");
        }

        Map<String, Object> result = MapUtil.copy(params, "studentId", "wrongItemId");
        result.put("examItemId", wrongItem.get("examItemId"));
        result.put("questionNumber", wrongItem.get("questionNumber"));

        // 获取试题详情
        String questionId = MapUtil.getString(wrongItem, "questionId");
        Document question = questionManager.get(new ObjectId(questionId));
        QuestionRenderHtmlUtil.render(question);     // 处理样式问题
        result.put("question", question);

        // 根据examItemId获取识别结果
        long examItemId = MapUtil.getLong(wrongItem, "examItemId");
        Map<String, Object> examItem = examItemService.getByExamItemId(examItemId);
        result.put("recognitionValue", MapUtil.getStringNullable(examItem, "recognitionValue"));
        result.put("readType", MapUtil.getInt(examItem, "readType"));

        // 获取标记的错因
        Map<String, Object> tmp = MapUtil.of("wrongItemIds", Collections.singleton(params.get("wrongItemId")));
        List<Map<String, Object>> tags = examRepository.selectList("WrongMapper.selectWrongTagItems", tmp);
        result.put("tags", tags);

        // belongType前端获取小力图片使用 - 这里的接口是专门给校本题库使用的
        result.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni"));

        return result;
    }

    /**
     * 学生获取教辅主页错题详情
     *
     * @param params studentId studyGuideWrongItemId
     * @return question
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    public Map<String, Object> getStudentHomeworkWrongDetail(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("studyGuideWrongItemId")
                .verify();

        long studentId = MapUtil.getLong(params, "studentId");
        long studyGuideWrongItemId = MapUtil.getLong(params, "studyGuideWrongItemId");

        // 查询错题
        StudyGuideWrongItemDTO studyGuideWrongItem = studyGuideWrongService.getStudyGuideWrongItemById(studentId, studyGuideWrongItemId);

        Map<String, Object> result = MapUtil.copy(params, "studentId", "studyGuideWrongItemId");

        // 试题详情
        String questionId = studyGuideWrongItem.getQuestionId();
        Document question = questionManager.get(new ObjectId(questionId));
        QuestionRenderHtmlUtil.render(question);  // 处理样式问题
        result.put("question", question);

        // 获取识别结果 - 只对应一个阅卷试题时（客观题一对一）才尝试获取
        String markQuestionNumbers = studyGuideWrongItem.getMarkQuestionNumbers();
        if (!markQuestionNumbers.contains(",")) {
            Map<String, Object> examItemParams = MapUtil.of("examId", studyGuideWrongItem.getExamId(),
                    "paperId", studyGuideWrongItem.getPaperId(),
                    "questionNumber", markQuestionNumbers,
                    "studentId", studentId);
            Map<String, Object> examItem = examItemService.getByExamItemLogicId(examItemParams);
            if (MapUtils.isNotEmpty(examItem)) {
                result.put("recognitionValue", MapUtil.getStringNullable(examItem, "recognitionValue"));
                result.put("readType", MapUtil.getInt(examItem, "readType"));
            }
            // 作答明细不存在了，当作主观题处理，不会展示我的答案
            else {
                result.put("recognitionValue", null);
                result.put("readType", DictUtil.getDictValue("readType", "subjective"));
            }
        } else {
            result.put("recognitionValue", null);
            result.put("readType", DictUtil.getDictValue("readType", "subjective"));
        }

        // 获取标记的错因
        List<StudyGuideWrongTagItemDTO> studyGuideWrongTagItem = studyGuideWrongService.getStudyGuideWrongTagItemById(studyGuideWrongItemId);
        result.put("tags", studyGuideWrongTagItem);

        // belongType前端获取小力图片使用
        result.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni"));

        return result;
    }

    /**
     * 学生获取错题本试题详情(其他试题)
     *
     * @param params studentId wrongItemId
     * @return studentId wrongItemId examItemId
     *         question -- 试题详情
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    public Map<String, Object> getStudentWrongDetailOther(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("studentQuestionMarkWrongItemId")
                .verify();

        // 查询错题是否还存在
        Map<String, Object> wrongItem = tikuRepository.selectOne("StudentQuestionMarkItemMapper.getMarkWrongItemDetail", params);
        if (MapUtils.isEmpty(wrongItem)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "错题不存在,请刷新列表!");
        }

        Map<String, Object> result = MapUtil.copy(params, "studentId","studentQuestionMarkWrongItemId");
        result.put("structureNumber", wrongItem.get("structureNumber"));
        result.put("belongType", wrongItem.get("belongType"));
        result.put("courseId", wrongItem.get("courseId"));

        // 获取试题详情
        String questionId = MapUtil.getString(wrongItem, "questionId");
        Map<String, Object> map = MapUtil.of("similarQuestionId", questionId, "belongType", wrongItem.get("belongType"));
        List<Map<String, Object>> maps = new ArrayList<>();
        maps.add(map);
        Map<String, Map<String, Object>> questionInfoList = recommendationSimilarityService.getQuestionInfoList(maps);
        if(questionInfoList != null && !questionInfoList.isEmpty()) {
            String singleKey = questionInfoList.keySet().iterator().next();
            Map<String, Object> question = questionInfoList.get(singleKey);
            QuestionRenderHtmlUtil.render(question);     // 处理样式问题
            result.put("question", question);
        }

        return result;
    }

    /**
     * 学生获取错题本试题详情(试题列表用)
     *
     * @param params studentId wrongItemId
     * @return studentId wrongItemId examItemId
     *         question -- 试题详情
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    public Map<String, Object> getStudentItemDetail(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("studentQuestionMarkItemId")
                .verify();

        // 查询错题是否还存在
        Map<String, Object> wrongItem = tikuRepository.selectOne("StudentQuestionMarkItemMapper.getMarkItemDetail", params);
        if (MapUtils.isEmpty(wrongItem)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "错题不存在,请刷新列表!");
        }

        Map<String, Object> result = MapUtil.copy(params, "studentId","studentQuestionMarkItemId");
        result.put("structureNumber", wrongItem.get("structureNumber"));
        result.put("belongType", wrongItem.get("belongType"));
        result.put("courseId", wrongItem.get("courseId"));

        // 获取试题详情
        String questionId = MapUtil.getString(wrongItem, "questionId");
        Map<String, Object> map = MapUtil.of("similarQuestionId", questionId, "belongType", wrongItem.get("belongType"));
        List<Map<String, Object>> maps = new ArrayList<>();
        maps.add(map);
        Map<String, Map<String, Object>> questionInfoList = recommendationSimilarityService.getQuestionInfoList(maps);

        if(questionInfoList != null && !questionInfoList.isEmpty()) {
            String singleKey = questionInfoList.keySet().iterator().next();
            result.put("question", questionInfoList.get(singleKey));
        }
       // result.put("question", questionInfoList);

        return result;
    }



    public List<WrongQuestionDetailVO> getStudentWrongDetailList(WrongQuestionDetailParam wrongQuestionDetailParam) {
        List<WrongQuestionDetailParam.WrongQuestionDetailItemParam> wrongQuestionDetailItemParams
                = wrongQuestionDetailParam.getWrongQuestionDetailItemParams();
        if (CollectionUtils.isEmpty(wrongQuestionDetailItemParams)) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> convertParams = wrongQuestionDetailItemParams.stream()
                .map(i -> {
                    Map<String, Object> item = new HashMap<>(2);
                    item.put("belongType", i.getBelongType());
                    item.put("similarQuestionId", i.getQuestionId());
                    return item;
                })
                .collect(toList());
        Map<String, Map<String, Object>> questionInfoMap = recommendationSimilarityService.getQuestionInfoList(convertParams);

        List<WrongQuestionDetailVO> result = new ArrayList<>();
        for (WrongQuestionDetailParam.WrongQuestionDetailItemParam item : wrongQuestionDetailItemParams) {
            WrongQuestionDetailVO wrongQuestionDetailVO = new WrongQuestionDetailVO();
            String questionId = item.getQuestionId();
            wrongQuestionDetailVO.setQuestionId(questionId);
            wrongQuestionDetailVO.setBelongType(item.getBelongType());
            Map<String, Object> question = questionInfoMap.get(questionId);
            QuestionRenderHtmlUtil.render(question);     // 处理样式问题
            wrongQuestionDetailVO.setQuestion(question); // 返回值没区分belongType...
            result.add(wrongQuestionDetailVO);
        }

        return result;
    }
    
    /**
     * 将下载链接link拼上cdn地址(publicLink)，使外部的用户可以直接使用
     * @param wrongDocumentList 错题文档list
     */
    private void publicWrongDocumentLink(List<Map<String, Object>> wrongDocumentList) {
        if (CollectionUtils.isEmpty(wrongDocumentList)) {
            return;
        }
        // 链接拼上cdn使其他人能够直接访问
        for (Map<String, Object> wrongDocument : wrongDocumentList) {
            FileStoragePathUtil.publicPath(wrongDocument, "link", "publicLink");
        }
    }

    /**
     * 获取用于PDF渲染任务创建对象
     *
     * @param wrongItems 错题数据
     * @param redirectUrl 二维码跳转链接
     * @param params 基础参数
     * @param fromPCWrongDownload 是否PC端下载错题
     */
    private WrongDocumentItemDTO getWrongDocumentItemDTO(List<Map<String, Object>> wrongItems,
                                                         String redirectUrl,
                                                         Map<String, Object> params,
                                                         boolean fromPCWrongDownload) {
        if (CollectionUtils.isEmpty(wrongItems)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "没有需要渲染的试题");
        }
        int type = DictUtil.getDictValue("wrongDocumentRenderTaskType", "wrongQuestion"); // 错题下载的
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Map<Integer, String> examTypeMap = getExamType();

        // 试题信息
        List<WrongDocumentQuestionDTO> wrongDocumentQuestionDTOS = new ArrayList<>();
        for (Map<String, Object> wrongItem : wrongItems) {
            WrongDocumentQuestionDTO wrongDocumentQuestionDTO = new WrongDocumentQuestionDTO();
            wrongDocumentQuestionDTO.setQuestionId(MapUtil.getString(wrongItem, "questionId"));
            wrongDocumentQuestionDTO.setBelongType(MapUtil.getInt(wrongItem, "belongType"));

            if (DictUtil.isEquals(MapUtil.getIntNullable(wrongItem, "wrongItemQuestionFrom"), "wrongItemQuestionFrom", "examWrongQuestion")
                    || fromPCWrongDownload) {
                Integer examType = Integer.valueOf(wrongItem.get("examType").toString());
                String tagName = format.format(wrongItem.get("startDate")) + " " + examTypeMap.get(examType);
                WrongDocumentQuestionAttributeDTO wrongDocumentQuestionAttributeDTO = new WrongDocumentQuestionAttributeDTO();
                wrongDocumentQuestionAttributeDTO.setExamName(tagName);
                wrongDocumentQuestionAttributeDTO.setStructureNumber(MapUtil.getString(wrongItem, "structureNumber"));
                wrongDocumentQuestionDTO.setAttribute(wrongDocumentQuestionAttributeDTO);
            }
            List<Map<String, Object>> one2MoreList = MapUtil.getCast(wrongItem, "one2MoreList");
            if (CollectionUtils.isNotEmpty(one2MoreList)) {
                List<WrongDocumentQuestionDTO> collect = one2MoreList.stream()
                        .map(i -> {
                            WrongDocumentQuestionDTO one2MoreQuestion = new WrongDocumentQuestionDTO();
                            one2MoreQuestion.setQuestionId(MapUtil.getString(i, "_id"));
                            one2MoreQuestion.setBelongType(DictUtil.getDictValue("questionBankBelongType", "dongni"));
                            return one2MoreQuestion;
                        }).collect(toList());
                wrongDocumentQuestionDTO.setOne2MoreList(collect);
            }
            wrongDocumentQuestionDTOS.add(wrongDocumentQuestionDTO);
        }

        WrongDocumentItemDTO wrongDocumentItemDTO = new WrongDocumentItemDTO();
        wrongDocumentItemDTO.setType(type);
        wrongDocumentItemDTO.setTypeName(DictUtil.getDictLabel("wrongDocumentRenderTaskType", type));
        wrongDocumentItemDTO.setFileName(MapUtil.getString(params, "fileName") + ".pdf");
        wrongDocumentItemDTO.setTitle(MapUtil.getString(params, "title", MapUtil.getString(params, "fileName"))); // 看起来是取的文件名
        wrongDocumentItemDTO.setRedirectUrl(redirectUrl);
        wrongDocumentItemDTO.setQuestions(wrongDocumentQuestionDTOS);

        return wrongDocumentItemDTO;
    }

    /**
     * 重试下载错题文档
     *
     * @param params wrongDocumentId
     */
    @Transactional(transactionManager = ExamRepository.TRANSACTION, rollbackFor = Exception.class)
    public void retryDownloadWrongDocument(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongDocumentId")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();

        long wrongDocumentId = MapUtil.getLong(params, "wrongDocumentId");
        Map<String, Object> examWrongDocument = getExamDocumentInfoById(wrongDocumentId);
        if (ObjectUtil.isValueEquals(MapUtil.getInt(examWrongDocument, "status"), 2)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "下载任务非失败状态，不能重试");
        }

        // t_wrong_document 状态改为0，删除link + finish_time
        resetWrongDocumentStatus(params);

        // 渲染任务重新改为待执行-可能出现改为待执行后，被上一次渲染应答修改了（暂不考虑，时间间隔很短）
        wrongDocumentPDFRenderService.resetTask(wrongDocumentId);

        // 推送任务重新改为文档生成中-可能出现改为文档生成中，被上一次渲染应答修改为待推送（没关系，推送的时候会再回查文档状态）
        wrongDocumentQuarkPushService.restTask(wrongDocumentId);
    }

    private void resetWrongDocumentStatus(Map<String, Object> params) {
        Map<String, Object> copy = MapUtil.copy(params, "wrongDocumentId", "userId", "userName");
        copy.put("status", 0); // 重置状态为0
        copy.put("link", null); // 清除链接
        copy.put("finishTime", null); // 清除完成时间
        copy.put("currentTime", DateUtil.getCurrentDateTime());
        examRepository.update("WrongMapper.resetWrongDocumentStatus", copy);
    }
}
