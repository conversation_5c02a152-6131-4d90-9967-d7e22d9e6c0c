package com.dongni.exam.wrong.serevice;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.wrong.bean.vo.WrongQuestionGroupVO;
import com.dongni.exam.wrong.bean.vo.WrongQuestionItemVO;
import com.dongni.exam.wrong.bean.vo.WrongQuestionVO;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.student.question.mark.bean.entity.StudentQuestionMarkWrongItemTagEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/6/3 周二 下午 03:25
 * @Version 1.0.0
 */
@Service
public class WrongOtherQuestionService {

    @Autowired
    TikuRepository tikuRepository;
     /**
     * 学生获取错题本试题列表 - 不返回试题详情，下划展示时再调接口获取
     *
     * @param params studentId schoolId courseId
     *               [difficultyType=easy middle difficulty]
     *               wrongItemQuestionFrom（字典值wrongItemQuestionFrom） 1-考试错题 2-其他试题
     *               startDate endDate 时间范围
     */
    public WrongQuestionVO getStudentWrongList(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("studentId")
                .verify();
        List<WrongQuestionGroupVO> studentQuestionMarkAndItem = tikuRepository.selectList("StudentQuestionMarkMapper.getStudentQuestionMarkAndItemByStudent",params);

        Map<String, LocalDateTime> map = new HashMap<>();

        for (WrongQuestionGroupVO wrongQuestionGroupVO : studentQuestionMarkAndItem) {
            List<WrongQuestionItemVO> wrongQuestionItems = wrongQuestionGroupVO.getWrongQuestionItems();
            for (WrongQuestionItemVO wrongQuestionItem : wrongQuestionItems) {

                wrongQuestionItem.setWrongItemQuestionFrom(DictUtil.getDictValue("wrongItemQuestionFrom", "similarQuestion"));
                if(!map.containsKey(wrongQuestionItem.getQuestionId())){
                    map.put(wrongQuestionItem.getQuestionId(), wrongQuestionItem.getCreateDateTime());
                }else {
                    LocalDateTime createDateTimeMap = map.get(wrongQuestionItem.getQuestionId());
                    LocalDateTime createDateTimeItem = wrongQuestionItem.getCreateDateTime();
                    int i = createDateTimeMap.compareTo(createDateTimeItem);
                    if(i < 0 ){
                        map.put(wrongQuestionItem.getQuestionId(),createDateTimeItem);
                    }
                }
            }
        }

        for (WrongQuestionGroupVO wrongQuestionGroupVO : studentQuestionMarkAndItem) {
            List<WrongQuestionItemVO> wrongQuestionItems = wrongQuestionGroupVO.getWrongQuestionItems();
            List<WrongQuestionItemVO> newWrongQuestionItems = new ArrayList<WrongQuestionItemVO>();
            for (WrongQuestionItemVO wrongQuestionItem : wrongQuestionItems) {
                if (map.containsKey(wrongQuestionItem.getQuestionId())) {
                    LocalDateTime localDateTime = map.get(wrongQuestionItem.getQuestionId());
                    if (localDateTime.equals(wrongQuestionItem.getCreateDateTime())) {
                        newWrongQuestionItems.add(wrongQuestionItem);
                    }
                }
            }
            wrongQuestionGroupVO.setWrongQuestionItems(newWrongQuestionItems);
        }



        WrongQuestionVO wrongQuestionVO = new WrongQuestionVO();
        wrongQuestionVO.setWrongQuestionGroups(studentQuestionMarkAndItem);

        return wrongQuestionVO;
    }
    public List<StudentQuestionMarkWrongItemTagEntity> updateWrongItemAllTag(Map<String,Object> params){
        StudentQuestionMarkWrongItemTagEntity studentQuestionMarkWrongItemTagEntity = new StudentQuestionMarkWrongItemTagEntity();
        List<StudentQuestionMarkWrongItemTagEntity> objects = new ArrayList<>();
        return objects;
    }
}
