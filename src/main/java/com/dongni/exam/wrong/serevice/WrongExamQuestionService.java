package com.dongni.exam.wrong.serevice;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.common.utils.spring.SpringProfilesActiveUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.wrong.bean.vo.WrongQuestionGroupVO;
import com.dongni.exam.wrong.bean.vo.WrongQuestionItemVO;
import com.dongni.exam.wrong.bean.vo.WrongQuestionVO;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.student.question.mark.bean.dto.StudentQuestionMarkItemSimpleDTO;
import com.dongni.tiku.student.question.mark.bean.param.SimilarQuestionParam;
import com.dongni.tiku.student.question.mark.service.StudentQuestionMarkItemService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/6/3 周二 下午 03:24
 * @Version 1.0.0
 */
@Service
public class WrongExamQuestionService {
    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private StudentQuestionMarkItemService studentQuestionMarkItemService;

    /**
     * 学生获取错题本试题列表 - 不返回试题详情，下划展示时再调接口获取
     *
     * @param params studentId schoolId courseId
     *               [difficultyType=easy middle difficulty]
     *               wrongItemQuestionFrom（字典值wrongItemQuestionFrom） 1-考试错题 2-其他试题
     *               startDate endDate 时间范围
     */
    public WrongQuestionVO getStudentWrongList(Map<String, Object> params) {
        // 教辅作业的错题不会在考试列表显示了
//        // 非教辅环境才要求报告已公布
//        if (!SpringProfilesActiveUtil.isProductJiaofu()) {
//            params.put("notFromJiaofu", true);
//        }

        long count = examRepository.selectOne("WrongMapper.countStudentWrong", params);
        if (count == 0) {
            return new WrongQuestionVO();
        }

        // 再查询数据-按考试时间(desc)+questionNumber排序,相同questionId保留第一条记录
        List<Map<String, Object>> wrongList = examRepository.selectList("WrongMapper.getStudentWrong", params);
        wrongList.sort(Comparator.<Map<String, Object>, Date>comparing(i -> MapUtil.getCast(i, "startDate"))
                .reversed()
                .thenComparingLong(i -> MapUtil.getLong(i, "examId"))
                .thenComparingInt(i -> MapUtil.getInt(i, "questionNumber")));

        Set<String> questionIds = new HashSet<>(wrongList.size());
        wrongList = wrongList.stream()
                .filter(i -> {
                    String questionId = MapUtil.getString(i, "questionId");
                    return questionIds.add(questionId);
                })
                .collect(Collectors.toList());

        // 查询类题信息
        SimilarQuestionParam similarQuestionParam = new SimilarQuestionParam();
        similarQuestionParam.setStudentId(MapUtil.getLong(params, "studentId"));
        similarQuestionParam.setQuestionIds(new ArrayList<>(questionIds));
        List<StudentQuestionMarkItemSimpleDTO> similarQuestions = studentQuestionMarkItemService.getSimilarQuestions(similarQuestionParam);
        Map<String, List<StudentQuestionMarkItemSimpleDTO>> sourceQuestionId2SimilarQuestion = similarQuestions.stream()
                .collect(groupingBy(StudentQuestionMarkItemSimpleDTO::getSourceQuestionId));
        wrongList.forEach(i -> {
            String questionId = MapUtil.getString(i, "questionId");
            List<StudentQuestionMarkItemSimpleDTO> similarQuestionList = sourceQuestionId2SimilarQuestion.get(questionId);
            i.put("similarQuestions", similarQuestionList);
        });

        // 转换数据并返回
        List<WrongQuestionItemVO> wrongQuestionItemVOS = convert2WrongQuestionItemVO(wrongList);
        Map<Long, List<WrongQuestionItemVO>> examId2WrongQuestionItemVO = wrongQuestionItemVOS.stream()
                .collect(groupingBy(WrongQuestionItemVO::getExamId, LinkedHashMap::new, toList()));

        List<WrongQuestionGroupVO> wrongQuestionGroupVOS = new ArrayList<>();
        for (Map.Entry<Long, List<WrongQuestionItemVO>> entry : examId2WrongQuestionItemVO.entrySet()) {
            Long examId = entry.getKey();
            String examName = entry.getValue().get(0).getExamName();
            Long startDate = entry.getValue().get(0).getStartDate();
            WrongQuestionGroupVO wrongQuestionGroupVO = new WrongQuestionGroupVO();
            wrongQuestionGroupVO.setGroupId(examId);
            wrongQuestionGroupVO.setGroupName(examName);
            wrongQuestionGroupVO.setStartDate(startDate);
            wrongQuestionGroupVO.setWrongQuestionItems(entry.getValue());
            wrongQuestionGroupVOS.add(wrongQuestionGroupVO);
        }
        WrongQuestionVO wrongQuestionVO = new WrongQuestionVO();
        wrongQuestionVO.setWrongQuestionGroups(wrongQuestionGroupVOS);
        return wrongQuestionVO;
    }

    private List<WrongQuestionItemVO> convert2WrongQuestionItemVO(List<Map<String, Object>> wrongList) {
        List<WrongQuestionItemVO> result = new ArrayList<>();
        for (Map<String, Object> wrongItem : wrongList) {
            // 原题
            WrongQuestionItemVO wrongQuestionItemVO = new WrongQuestionItemVO();
            wrongQuestionItemVO.setWrongItemQuestionFrom(DictUtil.getDictValue("wrongItemQuestionFrom", "examWrongQuestion"));
            wrongQuestionItemVO.setBelongType(DictUtil.getDictValue("questionBankBelongType", "dongni"));
            wrongQuestionItemVO.setQuestionId(MapUtil.getString(wrongItem, "questionId"));
            wrongQuestionItemVO.setDifficulty(MapUtil.getDouble(wrongItem, "difficulty"));
            wrongQuestionItemVO.setExamId(MapUtil.getLong(wrongItem, "examId"));
            wrongQuestionItemVO.setExamName(MapUtil.getString(wrongItem, "examName"));
            wrongQuestionItemVO.setExamItemId(MapUtil.getLong(wrongItem, "examItemId"));
            wrongQuestionItemVO.setQuestionNumber(MapUtil.getInt(wrongItem, "questionNumber"));
            wrongQuestionItemVO.setWrongItemId(MapUtil.getLong(wrongItem, "wrongItemId"));
            Date startDate = MapUtil.getCast(wrongItem, "startDate");
            wrongQuestionItemVO.setStartDate(startDate.getTime());

            // 类题
            List<StudentQuestionMarkItemSimpleDTO> sq = MapUtil.getCast(wrongItem, "similarQuestions");
            if (CollectionUtils.isNotEmpty(sq)) {
                List<WrongQuestionItemVO> similarQuestionWrongQuestionItemVOS = sq.stream().map(x -> {
                    WrongQuestionItemVO similarWrongQuestionItemVO = new WrongQuestionItemVO();
                    similarWrongQuestionItemVO.setWrongItemQuestionFrom(DictUtil.getDictValue("wrongItemQuestionFrom", "similarQuestion"));
                    similarWrongQuestionItemVO.setBelongType(x.getBelongType());
                    similarWrongQuestionItemVO.setQuestionId(x.getQuestionId());
                    similarWrongQuestionItemVO.setDifficulty(x.getDifficulty());
                    similarWrongQuestionItemVO.setStudentQuestionMarkId(x.getStudentQuestionMarkId());
                    similarWrongQuestionItemVO.setStudentQuestionMarkName(x.getStudentQuestionMarkName());
                    similarWrongQuestionItemVO.setStudentQuestionMarkItemId(x.getStudentQuestionMarkItemId());
                    similarWrongQuestionItemVO.setStudentQuestionMarkWrongItemId(x.getStudentQuestionMarkWrongItemId());
                    return similarWrongQuestionItemVO;
                }).collect(Collectors.toList());
                wrongQuestionItemVO.setSimilarQuestions(similarQuestionWrongQuestionItemVOS);
            }

            result.add(wrongQuestionItemVO);
        }

        return result;
    }

    /**
     * 查询学生考试错题数量-按课程分组
     */
    public List<Map<String, Object>> countStudentWrongGroupByCourseId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .verify();

        return examRepository.selectList("WrongMapper.countStudentWrongGroupByCourseId", params);
    }
}
