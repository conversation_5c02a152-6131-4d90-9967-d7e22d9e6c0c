package com.dongni.exam.wrong.serevice;

import cn.hutool.core.util.RandomUtil;
import com.dongni.analysis.view.monitor.service.ExamClassQuestionStatService;
import com.dongni.analysis.view.myclass.bean.WrongExplainStudentDetailVO;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.QuestionStructureVO;
import com.dongni.exam.question.service.ExamQuestionKnowledgeService;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongTagInsertParam;
import com.dongni.exam.studyguide.service.StudyGuideWrongService;
import com.dongni.exam.wrong.enums.WrongQuestionFrom;
import com.dongni.tiku.common.service.TikuCommonService;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.common.util.question.QuestionUtil;
import com.dongni.tiku.own.service.OwnExamPaperService;
import com.dongni.tiku.own.service.OwnKnowledgeService;
import com.dongni.tiku.own.service.OwnPaperService;
import com.dongni.tiku.own.service.OwnQuestionService;
import com.dongni.tiku.wrong.book.service.RecommendationSimilarityService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * 错题明细接口
 *
 * <AUTHOR>
 * @date 2018/12/27 15:05
 */
@Service
public class WrongService {

    private static final Logger log = LoggerFactory.getLogger(WrongService.class);

    @Autowired
    private ExamRepository repository;

    @Autowired
    private CommonCourseService commonCourseService;

    @Autowired
    private OwnQuestionService ownQuestionService;

    @Autowired
    private OwnExamPaperService ownExamPaperService;

    @Autowired
    private ExamClassQuestionStatService questionStatService;

    @Autowired
    private OwnPaperService ownPaperService;

    @Autowired
    private TikuCommonService tikuCommonService;

    @Autowired
    WrongClassService wrongClassService;

    @Autowired
    private OwnKnowledgeService ownKnowledgeService;

    @Autowired
    private ExamQuestionKnowledgeService examQuestionKnowledgeService;

    @Autowired
    private RecommendationSimilarityService recommendationSimilarityService;

    @Autowired
    private IQsClientService qsClientService;

    @Autowired
    private StudyGuideWrongService studyGuideWrongService;

    /**
     * 获取指定错题的所有标签
     * @param: studentId
     * @param: questionId
     * @param: questionNumber
     * @return
     */
    public List<Map<String, Object>> getItemAllTag(Map<String, Object> params){
        Verify.of(params)
                .isValidId("studentId")
                .isNotBlank("questionId")
                .isNumeric("questionNumber")
                .verify();
        return repository.selectList("WrongMapper.selectItemAllTagByUniqueKey",params);
    }


    /**
     * 只保留这些错题标签
     * @param: studentId
     * @param: questionId
     * @param: questionNumber
     * @param: tagList [{wrongTagId,tagName}]
     * @return
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void keepOnlyTheseItemTags(Map<String, Object> params){
        Verify.of(params)
                .isValidId("studentId")
                .isNotBlank("questionId")
                .isNumeric("questionNumber")
                .isNotNull("tagList")
                .verify();

        repository.delete("WrongMapper.deleteItemAllTagByUniqueKey",params);

        List<Map<String,Object>> tagList =(List<Map<String,Object>>) params.get("tagList");
        if (CollectionUtils.isNotEmpty(tagList)){
            Long wrongItemId = repository.selectOne("WrongMapper.selectWrongItemIdByUniqueKey", params);
            if (ObjectUtil.isBlank(wrongItemId)){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"不存在指定错题");
            }
            params.put("wrongItemId", wrongItemId);
            initTagList(tagList, params);

            repository.insert("WrongMapper.batchInsertItemTag",MapUtil.of("tagList",tagList ));
        }

    }

    private List<Map<String,Object>> initTagList(List<Map<String,Object>> tagList,Map<String,Object> params){
        Object wrongItemId = params.get("wrongItemId");
        Object studentId = params.get("studentId");
        Object userId = params.get("userId");
        Object userName = params.get("userName");
        Object dateTime=DateUtil.getCurrentDateTime();
        tagList.forEach(map->{
            map.put("studentId", studentId);
            map.put("wrongItemId", wrongItemId);
            map.put("userId",userId );
            map.put("userName",userName);
            map.put("createDateTime",dateTime);
            map.put("modifyDateTime", dateTime);
        });
        return tagList;
    }


    /**
     * 获取指定课程的所有标签
     * @param: courseId
     * @return
     */
    public List<Map<String, Object>> getCourseAllTag(Map<String, Object> params){
        Verify.of(params)
                .isValidId("courseId")
                .verify();
        List<Map<String, Object>> courseTagList = repository.selectList("WrongMapper.selectCourseAllTag", params);
        if (CollectionUtils.isNotEmpty(courseTagList)) {
            return courseTagList;
        }
        // 没获取到，使用降级处理，初始化错题标签
        long courseId = MapUtil.getLong(params, "courseId");
        String initLockKey = "EXAM:WRONG:TAG:INIT:COURSE_ID:" + courseId;
        return JedisTemplate.lockExecute(initLockKey, 5_000L,
                "初始化课程错因标签失败，请稍后再试", () -> {
                    List<Map<String, Object>> innerCourseTagList = repository.selectList("WrongMapper.selectCourseAllTag", params);
                    if (CollectionUtils.isNotEmpty(innerCourseTagList)) {
                        return innerCourseTagList;
                    }
                    initWrongTag(params);
                    return repository.selectList("WrongMapper.selectCourseAllTag", params);
        });
    }


    /**
     * 获取错题订正的图片路径
     *
     * @params: wrongItemId sourceId sourceType
     */
    public Map<String, Object> getFileURL(Map<String, Object> params){
        Verify.of(params)
                .isValidId("wrongItemId")
                .isValidId("sourceId")
                .isNumeric("sourceType")
                .verify();
        Map<String, Object> result = repository.selectOne("WrongMapper.getWrongItemFileURL", params);
        return Optional.ofNullable(result).orElse(Collections.emptyMap());
    }


    /**
     * 修改错题图片路径及订正状态
     *
     * 可以修改制定题目的fileURL与reviseStatus其中之一，也可以两者都修改
     *
     * @params: wrongItemId sourceId sourceType [fileURL] [reviseStatus]
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateReviseStatusOrFileURL(Map<String, Object> params){
        Verify.of(params)
                .isValidId("wrongItemId")
                .isValidId("sourceId")
                .isNumeric("sourceType")
                .verify();
        if (!ObjectUtil.isBlank(params.get("fileURL")) || !ObjectUtil.isNumeric("reviseStatus")) {

            //提交了错题图片路径，表示已订正
            if (!ObjectUtil.isNumeric("reviseStatus")){
                params.put("reviseStatus",DictUtil.getDictValue("reviseStatus", "revise"));
            }

            params.put("currentTime", DateUtil.getCurrentDateTime());
            repository.update("WrongMapper.updateReviseStatusOrFileURL", params);
        }else {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "fileURL和reviseStatus两个至少需要一个");
        }
    }

    /**
     * 考试错题订正状态更新
     * @param params  wrongItemId examId reviseStatus
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateExamReviseStatus(Map<String,Object> params){
        Verify.of(params)
                .isNotBlank("wrongItemId")
                .isValidId("examId")
                .isNumeric("reviseStatus")
                .verify();
        Map<String,Object> updateParam=new HashMap<>();
        updateParam.put("wrongItemId", params.get("wrongItemId"));
        updateParam.put("sourceId", params.get("examId"));
        updateParam.put("reviseStatus", params.get("reviseStatus"));
        updateParam.put("sourceType",DictUtil.getDictValue("sourceType", "exam"));
        updateParam.put("userId", params.get("userId"));
        updateParam.put("userName", params.get("userName"));
        updateParam.put("currentTime", DateUtil.getCurrentDateTime());
        repository.update("WrongMapper.updateReviseStatus",updateParam);//
    }


    /**
     * 获取学生错题统计
     *
     * @param params studentId
     * @return
     */
    @Transactional(transactionManager = ExamRepository.TRANSACTION, rollbackFor = Exception.class)
    public List<Map<String, Object>> getStudentWrongItemCount(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();

        Map<String, Date> currentWeek = DateUtil.getCurrentWeek();
        params.putAll(currentWeek);

        // 查询数量
        List<Map<String, Object>> totalCountList = repository.selectList("WrongMapper.selectStudentWrongItemTotalCount", params);
        List<Map<String, Object>> addCountList = repository.selectList("WrongMapper.selectStudentWrongItemAddCount", params);
        List<Map<String, Object>> delCountList = repository.selectList("WrongMapper.selectStudentWrongItemDelCount", params);
        List<Map<String, Object>> exerciseCountList = repository.selectList("WrongMapper.selectStudentWrongItemExerciseCount", params);

        Map<Long, Map<String, Object>> totalCountMap = totalCountList.stream()
                .collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));

        Map<Long, Map<String, Object>> addCountMap = addCountList.stream()
                .collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));

        Map<Long, Map<String, Object>> delCountMap = delCountList.stream()
                .collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));

         Map<Long, Map<String, Object>> exerciseCountMap = exerciseCountList.stream()
                .collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));


        // 获取所有课程
        List<Map<String, Object>> courseList = commonCourseService.getCourse(params);
        Map<Long, Map<String, Object>> courseMap = courseList.stream()
                .collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));

        // 取课程并集
        Set<Long> courseIds = new HashSet<>();
        totalCountList.forEach(item -> courseIds.add(Long.valueOf(item.get("courseId").toString())));
        addCountList.forEach(item -> courseIds.add(Long.valueOf(item.get("courseId").toString())));
        delCountList.forEach(item -> courseIds.add(Long.valueOf(item.get("courseId").toString())));

        List<Map<String, Object>> result = new ArrayList<>();

        // 组装数据
        courseIds.forEach(courseId -> {
            Map<String, Object> item = new HashMap<>();
            item.put("courseId", courseId);
            item.put("courseName", courseMap.get(courseId).get("courseName"));
            item.put("totalCount", totalCountMap.getOrDefault(courseId, new HashMap<>()).getOrDefault("totalCount", 0));
            item.put("addCount", addCountMap.getOrDefault(courseId, new HashMap<>()).getOrDefault("addCount", 0));
            item.put("delCount", delCountMap.getOrDefault(courseId, new HashMap<>()).getOrDefault("delCount", 0));
            item.put("exerciseCount", exerciseCountMap.getOrDefault(courseId, new HashMap<>()).getOrDefault("exerciseCount", 0));
            result.add(item);
        });

        return result;
    }

    /**
     * 获取学生错题知识点
     *
     * @param params studentId courseId [parentId]
     * @return
     */
    public List<Map<String, Object>> getWrongKnowledge(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .verify();
        if (!ObjectUtil.isBlank(params.get("startDate"))) {
            params.put("startDate", DateUtil.formatDateTime(new Date(Long.valueOf(params.get("startDate").toString()))));
        }

        if (!ObjectUtil.isBlank(params.get("endDate"))) {
            params.put("endDate", DateUtil.formatDateTime(new Date(Long.valueOf(params.get("endDate").toString()))));
        }

        // 获取班级课程错题的knowledgeIdSet
        List<String> knowledgeIdList = repository.selectList("WrongMapper.selectWrongKnowledge", params);
        Set<String> knowledgeIdSet = new HashSet<>(knowledgeIdList);

        // 根据学生课程错题知识点构造知识点树
        return wrongClassService.createTree(knowledgeIdSet, params);
    }

    /**
     * 获取学生的知识点错题信息
     * @param params studentId courseId
     *               difficultyType:easy/middle/difficulty
     *               {@link ExamQuestionKnowledgeService#getKnowledgeGraspByStudentTop10ForNanjingWaiguoyu(Map)}
     * @return knowledgeId    知识点id
     *         questionIdSet    知识点对应的错题ids
     */
    public List<Map<String, Object>> getStudentWrongItemsByDifficultyType(Map<String, Object> params){
        Verify.of(params).isValidId("studentId").isValidId("courseId").isNotBlank("difficultyType").verify();
        List<Map<String, Object>> knowledgeList = examQuestionKnowledgeService.getKnowledgeGraspByStudentTop10ForNanjingWaiguoyu(params);
        if(CollectionUtils.isEmpty(knowledgeList)){
            return Collections.emptyList();
        }
        params.put("knowledgeList", knowledgeList);
        List<Map<String, Object>> wrongItems= repository.selectList("WrongMapper.getStudentWrongItemsByKnowledgeIds", params);
        Map<String, Set<String>> knowledgeId2QuestionIds = wrongItems.stream()
                .collect(groupingBy(x -> MapUtils.getString(x, "knowledgeId"), mapping(x -> MapUtils.getString(x, "questionId"), toSet())));
        knowledgeList.forEach(x -> x.put("questionIdSet", knowledgeId2QuestionIds.getOrDefault(MapUtils.getString(x, "knowledgeId"), Collections.emptySet())));
        return knowledgeList;
    }

    /**
     * 获取学生难度等级的错题 20道 南京外国语学校定制
     * @param params studentId courseId
     *               difficultyType:easy/middle/difficulty
     *               {@link ExamQuestionKnowledgeService#getKnowledgeGraspByStudentTop10ForNanjingWaiguoyu(Map)}
     * @return 试题idSet
     */
    public Set<String> getStudentWrongQuestionIdSetTop20ByKnowledgeGraspStudentTop10ForNanjingWaiguoyu(Map<String, Object> params) {
        List<Map<String, Object>> knowledgeWrongQuestionInfoList = getStudentWrongItemsByDifficultyType(params);
        List<String> knowledgeIdList = new ArrayList<>();
        Map<String, Set<String>> knowledgeId2QuestionIdSet = new HashMap<>();
        Set<String> allQuestionIdSet = new HashSet<>();
        for (Map<String, Object> knowledgeWrongQuestionInfo : knowledgeWrongQuestionInfoList) {
            String knowledgeId = MapUtil.getString(knowledgeWrongQuestionInfo, "knowledgeId");
            Set<String> questionIdSet = MapUtil.getCast(knowledgeWrongQuestionInfo, "questionIdSet");
            knowledgeIdList.add(knowledgeId);
            knowledgeId2QuestionIdSet.put(knowledgeId, questionIdSet);
            allQuestionIdSet.addAll(questionIdSet);
        }
        // 如果知识点对应的错题数量小于20，就不用挑选了，直接返回
        if (allQuestionIdSet.size() <= 20) { return allQuestionIdSet; }

        Set<String> top20questionIdSet = new HashSet<>();
        // 对knowledgeIdList不断循环，每次循环都从知识点对应的错题中取出一个错题，直到取出20个错题或者知识点中的错题都取完了
        while (top20questionIdSet.size() < 20 && !allQuestionIdSet.isEmpty() && !knowledgeIdList.isEmpty()) {
            // 循环knowledgeIdList 每次处理一个知识点， 用迭代器知识点为了移除掉没有错题的知识点，避免重复循环
            Iterator<String> knowledgeIdIterator = knowledgeIdList.iterator();
            while (top20questionIdSet.size() < 20 && knowledgeIdIterator.hasNext()) {
                String knowledgeId = knowledgeIdIterator.next();
                Set<String> knowledgeQuestionIdSet = knowledgeId2QuestionIdSet.get(knowledgeId);
                if (CollectionUtils.isEmpty(knowledgeQuestionIdSet)) {
                    // 如果知识点没有错题，移除掉该知识点
                    knowledgeIdIterator.remove();
                } else {
                    boolean selected = false;
                    Iterator<String> knowledgeQuestionIdIterator = knowledgeQuestionIdSet.iterator();
                    // 从知识点的错题中取出一个错题，如果该错题不在top20中，则加入top20中
                    while (!selected && knowledgeQuestionIdIterator.hasNext()) {
                        String questionId = knowledgeQuestionIdIterator.next();
                        knowledgeQuestionIdIterator.remove();
                        allQuestionIdSet.remove(questionId);
                        if (!top20questionIdSet.contains(questionId)) {
                            top20questionIdSet.add(questionId);
                            selected = true;
                        }
                    }
                    if (CollectionUtils.isEmpty(knowledgeQuestionIdSet)) {
                        knowledgeIdIterator.remove();
                    }
                }
            }
        }

        return top20questionIdSet;
    }

    /**
     * 查询学生错题列表
     *
     * @param params studentId courseId wrongItemStatus [pageNo] [pageSize] [questionType] [knowledgeIds] [startDate] [endDate]
     * @return
     */
    public Map<String, Object> getStudentWrongItems(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .isNumeric("wrongItemStatus")
                .verify();

        // 查询知识点
        List<String> knowledgeIdList = new ArrayList<>();
        if (!ObjectUtil.isBlank(params.get("knowledgeIds"))) {
            Arrays.asList(params.get("knowledgeIds").toString().split(",")).forEach(a -> {
                String[] split = a.split("-");
                params.put("treeCode", split[0]);
                List<Document> childKnowledgeList = ownKnowledgeService.getChildKnowledgeList(params);
                List<String> knowledgeId = childKnowledgeList.stream().map(b -> b.get("_id").toString()).collect(Collectors.toList());
                knowledgeId.add(split[1]);
                knowledgeIdList.addAll(knowledgeId);
            });
            params.put("knowledgeIds", knowledgeIdList);
        }

        // 查询时间
        if (!ObjectUtil.isBlank(params.get("startDate"))) {
            params.put("startDate", DateUtil.formatDateTime(new Date(Long.parseLong(params.get("startDate").toString()))));
        }
        if (!ObjectUtil.isBlank(params.get("endDate"))) {
            params.put("endDate", DateUtil.formatDateTime(new Date(Long.parseLong(params.get("endDate").toString()))));
        }

        // 试题来源
        Integer wrongQuestionFromParam = MapUtil.getIntNullable(params, "wrongQuestionFrom");
        if (wrongQuestionFromParam != null) {
            WrongQuestionFrom wrongQuestionFrom = WrongQuestionFrom.getWrongQuestionFrom(wrongQuestionFromParam);
            List<Integer> examTypes = wrongQuestionFrom.getExamTypes();
            params.put("examTypes", examTypes);
        }

        // 查询错题
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> wrongItems = repository.selectList("WrongMapper.selectStudentWrongItems", params);
        if (CollectionUtils.isEmpty(wrongItems)) {
            result.put("totalCount", 0);
            result.put("list", Collections.emptyList());
            return result;
        }

        // 获取错题详情
        wrongItems = getWrongQuestions(params, result, wrongItems);

        // 填充是否有错题标签
        addIsHasTagField(wrongItems);

        // 填充学生得分率
        addStudentScoreRate2(wrongItems);
        return result;
    }

    /**
     * 填充学生得分率(scoreRate字段)
     *
     * 通过该试题关联的examItem计算, 一个学生1道题目可能关联多个examItem
     * 得分率 = 多个examItem.finallyScore之和 / 多个examItem.scoreValue之和
     *
     * ps:因错题表结构未确定,暂不使用此方法
     *
     * @param wrongItems 学生的错题列表
     */
    private void addStudentScoreRate(List<Map<String, Object>> wrongItems) {
        if (CollectionUtils.isEmpty(wrongItems)) {
            return;
        }
        List<Long> wrongItemIds = wrongItems.stream().map(i -> MapUtil.getLong(i, "wrongItemId")).collect(toList());
        // 查询这些item对应的scoreValue 和 finallyScore
        List<Map<String, Object>> examItemList = repository.selectList("WrongMapper.getExamItemByWrongItemIds",
                MapUtil.of("wrongItemIds", wrongItemIds));

        Map<Long, List<Map<String, Object>>> wrongItemId2ExamItem = examItemList.stream()
                .collect(groupingBy(i -> MapUtil.getLong(i, "wrongItemId")));
        wrongItems.forEach(i -> {
            long wrongItemId = MapUtil.getLong(i, "wrongItemId");
            List<Map<String, Object>> examItemOfWrongItemId = wrongItemId2ExamItem.get(wrongItemId);
            // 该错题没有找到对应的考试
            if (CollectionUtils.isEmpty(examItemOfWrongItemId)) {
                i.put("scoreRate", 0.00);
                return;
            }
            BigDecimal scoreValueSum = BigDecimal.ZERO;
            BigDecimal finallyScoreSum = BigDecimal.ZERO;
            for (Map<String, Object> x : examItemOfWrongItemId) {
                scoreValueSum = scoreValueSum.add(MapUtil.getCast(x, "scoreValue"));
                finallyScoreSum = finallyScoreSum.add(MapUtil.getCast(x, "finallyScore"));
            }
            if (scoreValueSum.compareTo(BigDecimal.ZERO) == 0) {
                i.put("scoreRate", 0.00);
            } else {
                i.put("scoreRate", finallyScoreSum.divide(scoreValueSum, RoundingMode.HALF_UP));
            }
        });
    }

    /**
     * 填充学生得分率(scoreRate字段)
     *
     * 通过t_wrong_item的exam_item_id获取关联的t_exam_item记录
     * scoreRate = finallyScore / scoreValue
     *
     * @param wrongItems 学生的错题列表
     */
    private void addStudentScoreRate2(List<Map<String, Object>> wrongItems) {
        if (CollectionUtils.isEmpty(wrongItems)) {
            return;
        }
        List<Long> wrongItemIds = wrongItems.stream().map(i -> MapUtil.getLong(i, "wrongItemId")).collect(toList());
        // 查询这些item对应的scoreValue 和 finallyScore
        List<Map<String, Object>> examItemList = repository.selectList("WrongMapper.getExamItemByWrongItemIds2",
                MapUtil.of("wrongItemIds", wrongItemIds));
        // 查询试题的分值
        List<Long> paperIds = examItemList.stream().map(i -> MapUtil.getLong(i, "paperId"))
                .distinct()
                .collect(toList());

        Map<Long, Map<Integer, BigDecimal>> paperId2Qn2ScoreValue = new HashMap<>(paperIds.size());
        for (Long paperId : paperIds) {
            List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(paperId);
            Map<Integer, BigDecimal> qn2ScoreValue = questionStructureVOS.stream()
                    .collect(toMap(QuestionStructureVO::getQuestionNumber, QuestionStructureVO::getScoreValue));
            paperId2Qn2ScoreValue.put(paperId, qn2ScoreValue);
        }

        Map<Long, Map<String, Object>> wrongItemId2ExamItem = examItemList.stream()
                .collect(toMap(i -> MapUtil.getLong(i, "wrongItemId"), i -> i, (i1, i2) -> i1));
        wrongItems.forEach(i -> {
            long wrongItemId = MapUtil.getLong(i, "wrongItemId");
            Map<String, Object> examItemOfWrongItemId = wrongItemId2ExamItem.get(wrongItemId);
            // 该错题没有找到对应的考试
            if (MapUtils.isEmpty(examItemOfWrongItemId)) {
                i.put("scoreRate", BigDecimal.ZERO);
                return;
            }
            long paperId = MapUtil.getLong(examItemOfWrongItemId, "paperId");
            int questionNumber = MapUtil.getInt(examItemOfWrongItemId, "questionNumber");
            BigDecimal scoreValueSum = paperId2Qn2ScoreValue.getOrDefault(paperId, new HashMap<>(0))
                    .getOrDefault(questionNumber, null);
            if (scoreValueSum == null) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                        "试卷:" + paperId + "，questionNumber:" + questionNumber + "找不到分值");
            }

            BigDecimal finallyScoreSum = MapUtil.getCast(examItemOfWrongItemId, "finallyScore");
            if (scoreValueSum.compareTo(BigDecimal.ZERO) == 0) {
                i.put("scoreRate", BigDecimal.ZERO);
            } else {
                i.put("scoreRate", finallyScoreSum.divide(scoreValueSum, 4, RoundingMode.HALF_UP));
            }
        });
    }

    /**
     * 获取错题详情
     *
     * @param params
     * @param result
     * @param wrongItems
     */
    public List<Map<String, Object>> getWrongQuestions(Map<String, Object> params, Map<String, Object> result, List<Map<String, Object>> wrongItems) {
        List<String> questionIds = wrongItems.stream().map(item -> item.get("questionId").toString()).collect(Collectors.toList());
        params.put("_ids", StringUtils.join(questionIds, ","));

        List<Document> questions = ownQuestionService.getQuestionBySearch(params);
        Map<String, Document> questionMap = questions.stream().collect(toMap(item -> item.get("_id").toString(), item -> item));

        Integer belongType = DictUtil.getDictValue("questionBankBelongType", "dongni");
        wrongItems = wrongItems.stream()
                .filter(item -> questionMap.get(item.get("questionId").toString()) != null)
                .peek(item -> {
                    item.put("question", questionMap.get(item.get("questionId").toString()));
                    item.put("belongType", belongType);
                })
                .collect(Collectors.toList());

        int totalCount = wrongItems.size();
        wrongItems = wrongItems.stream()
                .skip(Integer.valueOf(params.get("currentIndex").toString()))
                .limit(Integer.valueOf(params.get("pageSize").toString()))
                .collect(Collectors.toList());

        result.put("totalCount", totalCount);
        result.put("list", wrongItems);

        return wrongItems;
    }

    /**
     * 添加是否有标签的响应字段
     * @param wrongItemList 错题列表
     */
    private void addIsHasTagField(List<Map<String, Object>> wrongItemList){
        /*查询考试错题的错题标签列表*/
        List<String> wrongItemIdList = wrongItemList
                .stream()
                .map(item -> item.get("wrongItemId").toString())
                .collect(Collectors.toList());

        //每道错题的标签数（以错题id为key，以该题的标签数为value）
        Map<String, Long> wrongItemTagCountMap=new HashMap<>();
        if (!CollectionUtils.isEmpty(wrongItemIdList)){

            //错题的标签计数列表
            List<Map<String, Long>> wrongItemTagCountList=repository.selectList("WrongMapper.selectTagCountGroupByWrongItemId", MapUtil.of("wrongItemIdList", wrongItemIdList));

            wrongItemTagCountList.forEach(itemTagCount-> wrongItemTagCountMap.put(itemTagCount.get("wrongItemId").toString(),itemTagCount.get("tagCount")));
        }

        wrongItemList.forEach(wrongItem->{
            //这题的标签数
            Long tagCount = wrongItemTagCountMap.get(wrongItem.get("wrongItemId").toString());

            //这题是否有标签
            Boolean isHasTag = Optional.ofNullable(tagCount).map(count -> count>0).orElse(false);
            wrongItem.put("isHasTag", isHasTag);
        });
    }


    /**
     * 获取错题详情
     *
     * @param params studentId questionId [wrongItemId]
     * @return
     */
    public Map<String, Object> getWrongItemDetail(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isNotBlank("questionId")
                .verify();

        // 查询当前试题下的所有错误小问
        List<Map<String, Object>> wrongItems = repository.selectList("WrongMapper.selectWrongItemByQuestionId", params);

        // 查询试题详情
        params.put("_ids", params.get("questionId"));
        Map<String, Document> questionMap = ownQuestionService.getQuestionMap(params);
        Map<String, Object> question = questionMap.get(params.get("questionId").toString());

        Map<Object, Object> map = new HashMap<>();
        map.put("Id", params.get("questionId"));
        map.put("studentId", params.get("studentId"));
        List<Map> PaperId = repository.selectList("WrongMapper.selectWrongPaperId", map);
        if(PaperId != null){
            Map paperDetail = ownPaperService.getPaperDetail(PaperId.get(0));
            List<Document> questions = PaperUtil.getQuestions(paperDetail);
            Map<Object, Map> IdMap = questions.stream()
                    .collect(Collectors.toMap(t -> t.get("_id"), t -> t));
            if(params.get("questionId") != null){
                Map questionComplete = IdMap.get(params.get("questionId").toString());
                if(questionComplete != null){
                    question.put("structures",questionComplete.get("structures"));
                }
            }
        }
        question.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni"));

        params.put("questionTypes", question.get("questionType"));
        Map<Integer, Object> questionUnitType = tikuCommonService.getQuestionUnitType(params);
        question.put("unitType", ((Map)((Map)questionUnitType.get(Integer.valueOf(question.get("questionType").toString()))).get("defaultUnitType")).get("unitType"));

        // 查询试卷结构
        List<Map<String, Object>> paperStructure = ownExamPaperService.getPaperStructure(wrongItems.get(0));
        Map<Integer, Integer> questionIndexMap = paperStructure.stream()
                .collect(toMap(item -> Integer.valueOf(item.get("questionNumber").toString()),
                        item -> Integer.valueOf(item.get("questionIndex").toString())));

        Map<String, Object> result = new HashMap<>();

        List<Long> wrongItemIds = wrongItems.stream()
                .map(item -> Long.valueOf(item.get("wrongItemId").toString()))
                .collect(Collectors.toList());
        params.put("wrongItemIds", wrongItemIds);

        //获取试题来源
        List<Map<String, Object>> examItemSource = repository.selectList("WrongMapper.selectWrongExamItemsSource", params);
        if (ObjectUtil.isBlank(params.get("sourceType"))){
            List<Map<String, Object>> tempItem = new ArrayList<>();
            //错题可能同时来源考试和作业, 需求优先取考试
            Map<String, List<Map<String, Object>>> wrongItemIdGroup = examItemSource.stream()
                    .collect(groupingBy(e -> e.get("wrongItemId").toString()));
            wrongItemIdGroup.forEach((key, items)-> {
                if (items.size() > 1){
                    Map<String, List<Map<String, Object>>> sourceTypeGroup = items.stream().collect(groupingBy(e -> e.get("sourceType").toString()));
                    if (sourceTypeGroup.containsKey("1")){
                        tempItem.add(sourceTypeGroup.get("1").get(0));
                    }else {
                        tempItem.add(sourceTypeGroup.get("2").get(0));
                    }
                }else {
                    tempItem.add(items.get(0));
                }
            } );
            examItemSource = tempItem;
        } else {
            examItemSource = examItemSource.stream()
                    .filter(i -> ObjectUtil.isValueEquals(i.get("sourceType"), params.get("sourceType")))
                    .collect(toList());
        }
        List<Map<String, Object>> examItems = new ArrayList<>();
        Map<String, List<Map<String, Object>>> sourceTypeGroup = examItemSource.stream()
                .collect(groupingBy(e -> e.get("sourceType").toString()));
        //考试
        if (sourceTypeGroup.containsKey("1")){
            List<Map<String, Object>> sourceExam = sourceTypeGroup.get("1");
            Map<Long, Long> collect = sourceExam.stream()
                    .collect(toMap(s -> Long.valueOf(s.get("sourceItemId").toString()), s -> Long.valueOf(s.get("wrongItemId").toString())));
            List<Long> examItemId = sourceExam.stream().map(i -> Long.valueOf(i.get("sourceItemId").toString())).collect(toList());
            List<Map<String, Object>> examItemList = repository.selectList("WrongMapper.selectExamItems", examItemId);
            for (Map<String, Object> examItem : examItemList) {
                examItem.put("wrongItemId", collect.get(Long.valueOf(examItem.get("examItemId").toString())));
            }
            examItems.addAll(examItemList);
        }
        //作业
        if (sourceTypeGroup.containsKey("2")){
            List<Map<String, Object>> sourceExercise = sourceTypeGroup.get("2");
            Map<Long, Long> collect = sourceExercise.stream()
                    .collect(toMap(s -> Long.valueOf(s.get("sourceItemId").toString()), s -> Long.valueOf(s.get("wrongItemId").toString())));
            List<Long> examItemId = sourceExercise.stream().map(i -> Long.valueOf(i.get("sourceItemId").toString())).collect(toList());
            List<Map<String, Object>> exerciseItemList = repository.selectList("WrongMapper.selectExerciseItems", examItemId);
            for (Map<String, Object> examItem : exerciseItemList) {
                examItem.put("wrongItemId", collect.get(Long.valueOf(examItem.get("exerciseResultItemId").toString())));
            }
            examItems.addAll(exerciseItemList);
        }

        //
        // 获取得分和答案
       // List<Map<String, Object>> examItems = repository.selectList("WrongMapper.selectExamItems", params);
        Map<Long, Map<String, Object>> examItemMap = examItems.stream().collect(toMap(item -> Long.valueOf(item.get("wrongItemId").toString()), item -> item));

        // 获取标签
        List<Map<String, Object>> tags = repository.selectList("WrongMapper.selectWrongTagItems", params);
        Map<Long, List<Map<String, Object>>> tagGroup = tags.stream().collect(groupingBy(item -> Long.valueOf(item.get("wrongItemId").toString())));

        // 获取练习统计次数
        List<Map<String, Object>> exerciseCountList = repository.selectList("WrongMapper.selectWrongExerciseCount", params);
        Map<Long, Map<String, Object>> exerciseCountGroup = exerciseCountList.stream().collect(toMap(item -> Long.valueOf(item.get("wrongItemId").toString()), item -> item));

        // 组装数据
        wrongItems.forEach(item -> {
            item.put("questionIndex", questionIndexMap.get(Integer.valueOf(item.get("questionNumber").toString())));
            item.put("tags", tagGroup.getOrDefault(Long.valueOf(item.get("wrongItemId").toString()), Collections.emptyList()));
            Map<String, Object> examItem = examItemMap.get(Long.valueOf(item.get("wrongItemId").toString()));
            item.put("examItem", examItem);
            Map<String, Object> exercise = exerciseCountGroup.get(Long.valueOf(item.get("wrongItemId").toString()));
            if (exercise == null) {
                exercise = new HashMap<>();
                exercise.put("totalCount", 0);
                exercise.put("rightCount", 0);
            }
            item.put("exercise", exercise);

            // 获取试题得分率
            Map<String, Object> query = new HashMap<>(item);
            query.put("schoolId", examItem.get("schoolId"));
            query.put("classId", examItem.get("classId"));
            Map<String, Object> scoreRate = new HashMap<>();
            if (1 == Integer.valueOf(item.get("sourceType").toString())){
                query.put("examId", item.get("sourceId"));
                scoreRate = questionStatService.getScoreRate(query);

            }
            item.put("classScoreRate", scoreRate.getOrDefault("classScoreRate", "-"));
            item.put("schoolScoreRate", scoreRate.getOrDefault("schoolScoreRate", "-"));
        });

        result.put("question", question);
        result.put("wrongItems", wrongItems);

        return result;

    }

    /**
     * 查询错题明细
     *
     * com.dongni.exam.wrong.controller.WrongController#getWrongItemDetail()拿questionId去查t_wrong_item表
     * 会从t_wrong_item里同个questionId随机挑选一条，导致列表数据（老师角色-学生学情-错题再做）与查看详情数据不是同一道错题（其实列表数据也是随机的）
     *
     * 本接口传参使用wrongItemId，保证列表与查看详情是同一道题目，同时取t_wrong_item.exam_item_id直接查询t_exam_item
     * 而不联表t_wrong_exam_item，保证不会出现t_wrong_exam_item没有插入考试数据(统计的问题，t_wrong_exam_item没有正确维护错题的考试来源)
     *
     * @param params studentId questionId wrongItemId sourceType
     * @return question wrongItems
     */
    public Map<String, Object> getWrongItemDetailNew(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isNotBlank("questionId")
                .isValidId("wrongItemId")
                .isInteger("sourceType")
                .verify();

        // 查询错题
        List<Map<String, Object>> wrongItems = repository.selectList("WrongMapper.selectWrongItemByQuestionId", params);

        // 查询试题详情
        params.put("_ids", params.get("questionId"));
        Map<String, Document> questionMap = ownQuestionService.getQuestionMap(params);
        Map<String, Object> question = questionMap.get(params.get("questionId").toString());
        question.put("belongType", DictUtil.getDictValue("questionBankBelongType", "dongni"));

        params.put("questionTypes", question.get("questionType"));
        Map<Integer, Object> questionUnitType = tikuCommonService.getQuestionUnitType(params);
        question.put("unitType", ((Map) ((Map) questionUnitType.get(Integer.valueOf(question.get("questionType").toString()))).get("defaultUnitType")).get("unitType"));

        // 查询试卷结构
        List<Map<String, Object>> paperStructure = ownExamPaperService.getPaperStructure(wrongItems.get(0));
        Map<Integer, Integer> questionIndexMap = paperStructure.stream()
                .collect(toMap(item -> Integer.valueOf(item.get("questionNumber").toString()),
                        item -> Integer.valueOf(item.get("questionIndex").toString())));

        Map<String, Object> result = new HashMap<>();

        List<Long> examItemId = wrongItems.stream()
                .map(item -> Long.valueOf(item.get("examItemId").toString()))
                .collect(Collectors.toList());

        List<Long> wrongItemIds = wrongItems.stream()
                .map(item -> Long.valueOf(item.get("wrongItemId").toString()))
                .collect(Collectors.toList());
        params.put("wrongItemIds", wrongItemIds);

        //获取试题来源
        List<Map<String, Object>> examItemList = repository.selectList("WrongMapper.selectExamItems", examItemId);
        for (Map<String, Object> examItem : examItemList) {
            examItem.put("wrongItemId", params.get("wrongItemId"));
        }
        List<Map<String, Object>> examItems = new ArrayList<>(examItemList);


        // 获取得分和答案
        Map<Long, Map<String, Object>> examItemMap = examItems.stream().collect(toMap(item -> Long.valueOf(item.get("wrongItemId").toString()), item -> item));

        // 获取标签
        List<Map<String, Object>> tags = repository.selectList("WrongMapper.selectWrongTagItems", params);
        Map<Long, List<Map<String, Object>>> tagGroup = tags.stream().collect(groupingBy(item -> Long.valueOf(item.get("wrongItemId").toString())));

        // 获取练习统计次数
        List<Map<String, Object>> exerciseCountList = repository.selectList("WrongMapper.selectWrongExerciseCount", params);
        Map<Long, Map<String, Object>> exerciseCountGroup = exerciseCountList.stream().collect(toMap(item -> Long.valueOf(item.get("wrongItemId").toString()), item -> item));

        // 组装数据
        wrongItems.forEach(item -> {
            item.put("questionIndex", questionIndexMap.get(Integer.valueOf(item.get("questionNumber").toString())));
            item.put("tags", tagGroup.getOrDefault(Long.valueOf(item.get("wrongItemId").toString()), Collections.emptyList()));
            Map<String, Object> examItem = examItemMap.get(Long.valueOf(item.get("wrongItemId").toString()));
            item.put("examItem", examItem);
            Map<String, Object> exercise = exerciseCountGroup.get(Long.valueOf(item.get("wrongItemId").toString()));
            if (exercise == null) {
                exercise = new HashMap<>();
                exercise.put("totalCount", 0);
                exercise.put("rightCount", 0);
            }
            item.put("exercise", exercise);

            // 获取试题得分率
            Map<String, Object> query = new HashMap<>(item);
            query.put("schoolId", examItem.get("schoolId"));
            query.put("classId", examItem.get("classId"));
            Map<String, Object> scoreRate = new HashMap<>();
            if (1 == Integer.parseInt(item.get("sourceType").toString())) {
                query.put("examId", item.get("sourceId"));
                scoreRate = questionStatService.getScoreRate(query);

            }
            item.put("classScoreRate", scoreRate.getOrDefault("classScoreRate", "-"));
            item.put("schoolScoreRate", scoreRate.getOrDefault("schoolScoreRate", "-"));
        });

        result.put("question", question);
        result.put("wrongItems", wrongItems);

        return result;
    }

    /**
     * 更新错题明细状态（移出移入错题本）
     *
     * @param params studentId questionId wrongItemStatus [wrongItemId]
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateWrongItemStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isNotBlank("questionId")
                .isNumeric("wrongItemStatus")
                .verify();

        params.put("currentTime", DateUtil.getCurrentDateTime());
        repository.update("WrongMapper.updateWrongItem", params);

    }

    /**
     * 获取练习试题ID集合
     *
     * @param params studentId
     * @return
     */
    public List<String> getExerciseQuestionIds(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();

        if (!ObjectUtil.isBlank(params.get("startDate"))) {
            params.put("startDate", DateUtil.formatDateTime(new Date(Long.valueOf(params.get("startDate").toString()))));
        }

        if (!ObjectUtil.isBlank(params.get("endDate"))) {
            params.put("endDate", DateUtil.formatDateTime(new Date(Long.valueOf(params.get("endDate").toString()))));
        }

        params.put("wrongItemStatus", ObjectUtil.isBlank(params.get("wrongItemStatus")) ? 1 : Integer.valueOf(params.get("wrongItemStatus").toString()));
        return repository.selectList("WrongMapper.selectExerciseIds", params);

    }

    /**
     * 保存练习记录
     *
     * @param params wrongItemId currentAnswer correctAnswer
     */
    public void insertExerciseItem(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongItemId")
                .verify();

        params.put("currentTime", DateUtil.getCurrentDateTime());
        /*
         * 0722优化 需求提出主观题也要记录订正记录 触发条件为点击查看解析
         **/

        //获取错题 readType
        int readType = repository.selectOne("WrongMapper.getWrongItemReadType", params);
        if (1 == readType){
            //客观题
            Verify.of(params)
                    .isNotBlank("currentAnswer", "缺少作答情况")
                    .isNotBlank("correctAnswer")
                    .verify();
            params.put("resultType", ObjectUtil.isValueEquals(params.get("currentAnswer"), params.get("correctAnswer")) ? 1 : 2);
        }else {
            //主观题没有作答  字段给默认值
            params.put("currentAnswer", "1");
            params.put("correctAnswer", "1");
            params.put("resultType", "1");
        }
        repository.insert("WrongMapper.insertWrongExerciseItem", params);
    }

    /**
     * 获取学生的错题标签
     *
     * @param params studentId
     * @return
     */
    public List<Map<String, Object>> getWrongTag(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();

        return repository.selectList("WrongMapper.selectWrongTag", params);
    }

    /**
     * 获取错题明细标签,如果有参数wrongItemId，则查询指定错题的所有标签，否则查询该学生的所有标签
     *
     * @param params [wrongItemId] [studentId]  二选一
     * @return
     */
    public List<Map<String, Object>> getWrongTagItem(Map<String, Object> params) {

        if (params.containsKey("wrongItemId")){
            //查询指定错题的所有标签
            return repository.selectList("WrongMapper.selectWrongItemTag", params);
        }
        Verify.of(params).isNumeric("studentId").verify();
        //查询该学生的所有标签
        return repository.selectList("WrongMapper.selectStudentWrongItemTagList",params);

    }




    /**
     * 删除标签
     *
     * @param params [wrongTagItemId] [wrongTagItemIdList]
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void deleteWrongTagItem(Map<String, Object> params) {
        if (params.containsKey("wrongTagItemId") || params.containsKey("wrongTagItemIdList")) {
            repository.delete("WrongMapper.deleteWrongTagItem", params);
        }
    }



    /**
     * 获取回收站课程
     *
     * @param params studentId
     * @return
     */
    public List<Map<String, Object>> getTrashCourse(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();

        // 查询回收站课程ID
        List<Long> courseIds = repository.selectList("WrongMapper.selectTrashCourse", params);

        // 获取所有课程
        List<Map<String, Object>> courseList = commonCourseService.getCourse(params);
        Map<Long, Map<String, Object>> courseMap = courseList.stream()
                .collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));

        List<Map<String, Object>> result = new ArrayList<>();
        courseIds.forEach(courseId -> {
            Map<String, Object> resultItem = new HashMap<>(courseMap.get(courseId));
            result.add(resultItem);
        });

        return result;

    }


    /**
     * 获取本周新增错题数
     * params courseId studentId
     *
     * @return 错题数
     */
    public Long getWrongTopicNew(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .verify();
        //本周
        params.putAll(DateUtil.getCurrentWeek());
        return repository.selectOne("WrongMapper.getWrongTopicCount", params);
    }

    /**
     * 获取本周消灭错题
     * params classId studentId
     *
     * @return 消灭错题数
     */
    public Long getWrongTopicDestroy(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .verify();
        //本周
        params.putAll(DateUtil.getCurrentWeek());
        return repository.selectOne("WrongMapper.getDestroyWrongTopic", params);
    }

    /**
    * 获取本学期 已掌握错题数/待复习错题数
    * params classId studentId
    *
    * @return 错题数
    */
    public Map<String, Object> getWrongTopicCount(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .verify();

        Map<String, Object> rs = new HashMap<>();
        //本学期
        params.putAll(DateUtil.getCurrentTerm());
        Long master = repository.selectOne("WrongMapper.getDestroyWrongTopic", params);
        Long notMaster = repository.selectOne("WrongMapper.getWrongTopicCount", params);
        rs.put("masterCount", master);
        rs.put("notMasterCount", notMaster+master);
        return rs;
    }

    /**
     * 获取班级课程错题总数
     * @param params classId courseId startDate endDate
     * @return 班级课程错题总数
     */
    public Long getWrongTopicCount2(Map<String,Object> params){
        // 参数校验
//        if(params == null || !VerifyCodeUtil.isValidId(params.get("studentId"))){
//            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
//        }

        Verify.of(params)
                .isValidId("studentId")
                .verify();

        // 查询所有课程错题总数
        if(!ObjectUtil.isValidId(params.get("courseId"))){
            params.remove("courseId");
        }

        if (!ObjectUtil.isBlank(params.get("startDate"))) {
            if(params.get("startDate") instanceof Date){
                String startDate = DateUtil.formatDateTime((Date) params.get("startDate"));
                params.put("startDate", startDate);
            }else {
                String startDate = DateUtil.formatDateTime(new Date(Long.parseLong(params.get("startDate").toString())));
                params.put("startDate", startDate);
            }
        }
        if (!ObjectUtil.isBlank(params.get("endDate"))) {
            if(params.get("endDate") instanceof Date){
                String endDate = DateUtil.formatDateTime((Date) params.get("endDate"));
                params.put("endDate", endDate);
            }else {
                String endDate = DateUtil.formatDateTime(new Date(Long.parseLong(params.get("endDate").toString())));
                params.put("endDate", endDate);
            }
        }

        // 数据查询
        Long total = repository.selectOne("WrongMapper.getWrongTopicCount",params);
        return total;
    }

    /**
     * 获取班级课程错题本周新增总数
     * @param params classId courseId
     * @return 班级课程错题总数
     */
    public Long getThisWeekWrongTopicCount(Map<String,Object> params){
//        // 参数校验
//        if(params == null || !VerifyCodeUtil.isValidId(params.get("studentId"))){
//            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
//        }

        Verify.of(params)
                .isValidId("studentId")
                .verify();

        // 查询所有课程错题总数
        if(!ObjectUtil.isValidId(params.get("courseId"))){
            params.remove("courseId");
        }

        // 获取本周
        params.putAll(DateUtil.getCurrentWeek());
        return getWrongTopicCount2(params);
    }

    /**
     * 保存学生讲错题记录
     */
    public void saveStudentWrongExplain(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("studentId")
          .isValidId("courseId")
          .isNotBlank("questionId")
          .isValidId("sourceType")
          .verify();

        repository.insert("WrongMapper.saveStudentWrongExplain", params);
    }

    /**
     * 获取学生的讲错题数量
     */
    public WrongExplainStudentDetailVO getWrongExplainCountDetail(Map<String,Object> params){
        Verify.of(params)
          .isValidId("studentId")
          .verify();

        // 查询所有课程错题总数
        if(!ObjectUtil.isValidId(params.get("courseId"))){
            params.remove("courseId");
        }

        // 本周的条件
        Map<String, Date> currentWeek = DateUtil.getCurrentWeek();
        String startDate = DateUtil.formatDateTime(currentWeek.get("startDate"));
        params.put("startDate", startDate);
        String endDate = DateUtil.formatDateTime(currentWeek.get("endDate"));
        params.put("endDate", endDate);

        // 数据查询
        return repository.selectOne("WrongMapper.getWrongStudentExplainCountDetail", params);
    }

    /**
     * 获取学生错题已练习数量
     */
    public int getStudentExerciseCount(Map<String,Object> params){
        Verify.of(params)
                .isValidId("studentId")
                .verify();
       return repository.selectOne("WrongMapper.selectWrongExerciseFinish", params);
    }
    /**
     * 初始化错因
     * @param params [courseId] 初始化指定的courseId的标签
     */
    public void initWrongTag(Map<String,Object> params){
        params.put("currentTime", DateUtil.getCurrentDateTime());

        List<Map<String, Object>> allCourseList = commonCourseService.getCommonCourse(params);
        Map<String, List<Map<String, Object>>> courseName2courseList = allCourseList.stream()
                .collect(groupingBy(m -> m.get("courseName").toString()));
        
        
        List<Map<String, Object>> insertList = new ArrayList<>();
        
        // tagA
        List<String> tagACoueseNameList = Arrays.asList("数学", "物理", "化学", "生物");
        for (String courseName : tagACoueseNameList) {
            List<Map<String, Object>> tagACourseList = courseName2courseList.remove(courseName);
            if (CollectionUtils.isNotEmpty(tagACourseList)) {
                List<String> tagA = Arrays.asList("审题不清", "思路错误", "概念模糊", "运算错误","不会做");
                for (Map<String, Object> course : tagACourseList) {
                    initWrongTagParams(params, tagA, insertList, course, 1);
                }
            }
        }
        
        // tagB
        List<String> tagBCoueseNameList = Arrays.asList("英语");
        for (String courseName :tagBCoueseNameList){
            List<Map<String, Object>> tagBCourseList = courseName2courseList.remove(courseName);
            if (CollectionUtils.isNotEmpty(tagBCourseList)) {
                List<String> tagB = Arrays.asList("单词拼写错误", "语法错误", "语境理解偏差", "审题不清");
                for (Map<String, Object> course : tagBCourseList) {
                    initWrongTagParams(params, tagB, insertList, course, 1);
                }
            }
        }
        
        // tagOthers
        if (MapUtils.isNotEmpty(courseName2courseList)) {
            List<String> tagOthers = Arrays.asList("审题不清", "思路错误", "概念模糊","不会做");
            for (List<Map<String, Object>> courseList : courseName2courseList.values()) {
                for (Map<String, Object> course : courseList) {
                    initWrongTagParams(params, tagOthers, insertList, course, 1);
                }
            }
        }

        repository.batchInsert("WrongMapper.initWrongTag", insertList);
    }

    private void initWrongTagParams(Map<String, Object> params, List<String> tagB, List<Map<String, Object>> insertList, Map<String, Object> course, int i) {
        String currentTime = DateUtil.getCurrentDateTime();
        for (String tag : tagB) {
            Map<String, Object> m = new HashMap<>();
            m.put("tagName", tag);
            m.put("courseId", course.get("courseId"));
            m.put("courseName", course.get("courseName"));
            m.put("tagSort", i);
            m.put("userId", params.get("userId"));
            m.put("userName", params.get("userName"));
            m.put("currentTime", currentTime);
            insertList.add(m);
            i++;
        }
    }

    /**
    * @Description  新增课程初始化课程标签
     *  courseId, courseName
    **/
    public void addCourseWrongTag(Map<String, Object> params){
        Verify.of(params).isValidId("courseId","初始化课程错因标签失败")
                .isNotBlank("courseName", "初始化课程错因标签失败").verify();

        List<String> tags = Arrays.asList("审题不清", "思路错误", "概念模糊","不会做");
        String currentTime = DateUtil.getCurrentDateTime();
        List<Map<String, Object>> paramsList = new ArrayList<>();
        int i = 1;
        for (String tag : tags) {
            Map<String, Object> m = new HashMap<>();
            m.put("tagName", tag);
            m.put("courseId", params.get("courseId"));
            m.put("tagSort", i);
            m.put("courseName", params.get("courseName"));
            m.put("userId", params.get("userId"));
            m.put("userName", params.get("userName"));
            m.put("currentTime", currentTime);
            paramsList.add(m);
            i++;
        }
        repository.batchInsert("WrongMapper.initWrongTag", paramsList);
    }



    /**
     * 获取原题列表详情
     * @param params  studentId courseId difficultyType
     * @return 试题详情
     */
    public List<Map<String, Object>> getStudentWrongQuestionDetailFilterByKnowledge(Map<String, Object> params) {
        log.info("【南京外国语下载错题入参】:{}", params);
        Set<String> questionIdSet = getStudentWrongQuestionIdSetTop20ByKnowledgeGraspStudentTop10ForNanjingWaiguoyu(params);
        return getStudentWrongQuestionDetailByKnowledge(questionIdSet);
    }

    /**
     * 获取类题列表详情
     * @param params  studentId courseId difficultyType
     * @return 类题详情
     */
    public List<Map<String, Object>> getStudentWrongSimilarQuestionDetailFilterByKnowledge(Map<String, Object> params) {
        log.info("【南京外国语下载类题入参】:{}", params);
        Set<String> questionIdSet = getStudentWrongQuestionIdSetTop20ByKnowledgeGraspStudentTop10ForNanjingWaiguoyu(params);
        return getStudentWrongSimilarQuestionDetailByKnowledge(questionIdSet);
    }


    /**
     * 获取原题列表详情
     * @param questionIds 试题ID列表
     * @return 试题详情
     */
    public List<Map<String, Object>> getStudentWrongQuestionDetailByKnowledge(Collection<String> questionIds) {
        log.info("【南京外国语获取试题详情入参】：{}", questionIds);
        if (CollectionUtils.isEmpty(questionIds)) {
            log.info("【南京外国语获取试题详情入参为空】：{}", questionIds);
            return Lists.newArrayList();
        }

        // 获取原题
        List<ObjectId> questionObjIds = questionIds.stream().map(ObjectId::new).collect(toList());
        List<Document> questionDocList = ownQuestionService.getQuestions(questionObjIds);
        List<Map<String, Object>> questionList = setUnitType(Lists.newArrayList(questionDocList));
        return questionList;
    }

    /**
     * 设置试题的unitType
     * @param questionList 试题列表
     * @return
     */
    private List<Map<String, Object>> setUnitType(List<Map<String, Object>> questionList) {
        // 获取questionType和unitType对应map
        List<Integer> questionTypeList = questionList.stream()
                .filter(item -> ObjectUtil.isNotBlank(item.get("questionType")))
                .map(item -> MapUtil.getInt(item, "questionType"))
                .distinct()
                .collect(toList());
        log.info("【南京外国语获取试题详情试题类型入参】: {}", questionTypeList);
        Map<Integer, Map<String, Object>> questionTypeToUnitMap = tikuCommonService.getQuestionUnitType(questionTypeList);
        log.info("【南京外国语获取试题详情试题类型数据】: {}", questionTypeToUnitMap);
        // 设置试题的unitType
        questionList.forEach(question -> {
            Integer questionType = MapUtil.getIntNullable(question, "questionType");
            if (questionType != null) {
                Map<String, Object> questionTypeToUnit = questionTypeToUnitMap.get(questionType);
                if (MapUtils.isNotEmpty(questionTypeToUnit)) {
                    Map<String, Object> defaultUnitType = MapUtil.getMap(questionTypeToUnit, "defaultUnitType");
                    if (MapUtils.isNotEmpty(defaultUnitType)) {
                        question.put("unitType", defaultUnitType.get("unitType"));
                        question.put("unitTypeName", defaultUnitType.get("unitTypeName"));
                    }
                }
            }
            List<Map<String, Object>> knowledgeList = QuestionUtil.getKnowledgeList(question);
            question.put("knowledgeTotalList", knowledgeList);
        });

        // 根据unitType排序
        questionList.sort(Comparator.comparing(item ->
                        Optional.ofNullable(item)
                                .map(questionDoc -> MapUtil.getIntNullable(questionDoc, "unitType"))
                                .orElse(null),
                Comparator.nullsLast(Comparator.naturalOrder())
        ));
        return questionList;
    }

    /**
     * 获取类题列表详情
     * @param questionIds 原题ID列表
     * @return 类题详情
     */
    public List<Map<String, Object>> getStudentWrongSimilarQuestionDetailByKnowledge(Collection<String> questionIds) {
        log.info("【南京外国语获取类题详情入参】：{}", questionIds);
        if (CollectionUtils.isEmpty(questionIds)) {
            log.info("【南京外国语获取类题详情入参为空】：{}", questionIds);
            return Lists.newArrayList();
        }

        // 获取类题
        Map<String, Object> similarParams = MapUtil.of("questionIdList", questionIds);
        List<Map<String, Object>> similarQuestionList = recommendationSimilarityService.getSimilarInfoListByQuestionIdList(similarParams);

        // 每道原题选一个类题
        List<Map<String, Object>> randomSimilarQuestionList = randomSimilarQuestion(similarQuestionList);

        // 将学科网的questionType转换为系统内置的questionType
        transferQuestionType(randomSimilarQuestionList);

        // 设置unitType
        List<Map<String, Object>> similarQuestionInfoList = setUnitType(randomSimilarQuestionList);

        return similarQuestionInfoList;
    }


    /**
     * 随机挑选一道原题对应的类题
     * @param similarQuestionList 类题列表
     * @return
     */
    private List<Map<String, Object>> randomSimilarQuestion(List<Map<String, Object>> similarQuestionList) {
        Map<String, List<Map<String, Object>>> similarListMapGroupByQuestionId = similarQuestionList.stream()
                .collect(groupingBy(item -> MapUtil.getString(item, "questionId")));

        List<Map<String, Object>> resultList = Lists.newArrayList();
        similarListMapGroupByQuestionId.forEach((questionId, similarItemList) -> {
            Map<String, Object> randomSimilarQuestion = RandomUtil.randomEle(similarItemList);
            resultList.add(randomSimilarQuestion);
        });
        return resultList;
    }

    /**
     * 将学科网的questionType转换为系统内置的questionType
     * @param similarQuestionList 类题列表
     */
    private void transferQuestionType(List<Map<String, Object>> similarQuestionList) {
        Map<String, Object> xkwParams = new HashMap<>(4);
        Map<Long, List<Map<String, Object>>> xkwQuestionTypeMap =  tikuCommonService.getXkwQuestionType2RelativeInfo(xkwParams);

        similarQuestionList.forEach(similarQuestion -> {
            int belongType = MapUtil.getInt(similarQuestion, "belongType");
            boolean xuekewang = DictUtil.isEquals(belongType, "questionBankBelongType", "xuekewang");
            if (xuekewang) {
                Map<String, Object> questionInfo = MapUtil.getMap(similarQuestion, "questionInfo");
                if (MapUtils.isNotEmpty(questionInfo)) {
                    Long xkwQuestionType = MapUtil.getLongNullable(questionInfo, "questionType");
                    String xkwQuestionTypeName = MapUtil.getStringNullable(questionInfo, "questionTypeName");

                    if (xkwQuestionType != null) {
                        List<Map<String, Object>> xkwQuestionTypeItemList = xkwQuestionTypeMap.get(xkwQuestionType);
                        if (CollectionUtils.isNotEmpty(xkwQuestionTypeItemList)) {
                            similarQuestion.put("xkwQuestionType", xkwQuestionType);
                            similarQuestion.put("xkwQuestionTypeName", xkwQuestionTypeName);

                            similarQuestion.put("questionType", xkwQuestionTypeItemList.get(0).get("questionType"));
                            similarQuestion.put("questionTypeName", xkwQuestionTypeItemList.get(0).get("questionTypeName"));
                        }
                    }
                }
            }
        });
    }

    /**
     * 保存教辅作业错题 错因标签
     *
     * @param params studentId studyGuideWrongItemId 教辅作业错题id
     *               tagList [{wrongTagId,tagName}]
     */
    public void updateStudyGuideWrongTagItem(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studyGuideWrongItemId")
                .isValidId("studentId")
                .verify();

        long studentId = MapUtil.getLong(params, "studentId");
        long studyGuideWrongItemId = MapUtil.getLong(params, "studyGuideWrongItemId");
        studyGuideWrongService.getStudyGuideWrongItemById(studentId, studyGuideWrongItemId);

        // 先删除
        studyGuideWrongService.deleteTags(studyGuideWrongItemId);

        // 再插入
        List<Map<String, Object>> tagList = MapUtil.getCast(params, "tagList");
        if (CollectionUtils.isNotEmpty(tagList)) {
            List<StudyGuideWrongTagInsertParam.Tag> tags = tagList.stream()
                    .map(i -> {
                        StudyGuideWrongTagInsertParam.Tag tag = new StudyGuideWrongTagInsertParam.Tag();
                        tag.setWrongTagId(MapUtil.getLong(i, "wrongTagId"));
                        tag.setTagName(MapUtil.getString(i, "tagName"));
                        return tag;
                    }).collect(toList());
            StudyGuideWrongTagInsertParam studyGuideWrongTagInsertParam = new StudyGuideWrongTagInsertParam();
            studyGuideWrongTagInsertParam.setStudyGuideWrongItemId(studyGuideWrongItemId);
            studyGuideWrongTagInsertParam.setTagList(tags);
            studyGuideWrongService.insertTags(studyGuideWrongTagInsertParam);
        }
    }
}
