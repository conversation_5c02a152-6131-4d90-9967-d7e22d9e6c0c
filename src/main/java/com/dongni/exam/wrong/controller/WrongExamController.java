package com.dongni.exam.wrong.controller;

import cn.hutool.core.bean.BeanUtil;
import com.dongni.basedata.log.aspect.DongniNotAccessLog;
import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.wrong.bean.params.DownloadWrongQuestionParam;
import com.dongni.exam.wrong.bean.params.WrongQuestionDetailParam;
import com.dongni.exam.wrong.serevice.WrongExamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by JiJinDong
 * time:2019/3/11  14:41
 * description: 错题下载
 **/
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/own/wrong")
public class WrongExamController extends BaseController {

    @Autowired
    private WrongExamService wrongExamService;

    /**
     *
     * @param params courseId startDate endDate studentId
     * @return 按周期获取错题信息
     */
    @GetMapping("/cycle/count")
    public Response getStudentWrongCount(Map<String,Object> params){
        return new Response(wrongExamService.getStudentWrongCount(params));
    }

    /**
     *
     * @param params studentId courseId gradeType  gradeYear
     * @return 按考试获取错题信息
     */
    @GetMapping("/exam")
    public Response getStudentWrongExam(Map<String,Object> params){
        return new Response(wrongExamService.getStudentWrongExam(params));
    }

    /**
     *
     * @param params studentId courseId examId
     * @return 获取考试错题数量
     */
    @GetMapping("/exam/count")
    public Response getStudentWrongExamCount(Map<String,Object> params){
        return new Response(wrongExamService.getStudentWrongExamCount(params));
    }

    /**
     *
     * @param params userId
     * @return  查询错题文档列表
     */
    @GetMapping("/document")
    public Response getExamWrongItems(Map<String,Object> params){
        return new Response(wrongExamService.getExamWrongDocument(params));
    }

    /**
     * 删除下载记录
     *
     * @param params wrongDocumentId
     */
    @PostMapping("/document/delete")
    public Response deleteExamWrongDocument(Map<String,Object> params) {
        wrongExamService.deleteExamWrongDocument(params);
        return new Response();
    }

    /**
     * 学生下载错题--从错题再练下载
     *
     * @return  查询错题试题 下载用
     */
    @PostMapping("/question")
    public Response getQuestion(Map<String, Object> params){
        DownloadWrongQuestionParam downloadWrongQuestionParam
                = BeanUtil.mapToBean(params, DownloadWrongQuestionParam.class, true, null);
        return new Response(wrongExamService.getQuestion(downloadWrongQuestionParam));
    }

    /**
     * 学生下载错题--从报告下载
     */
    @PostMapping("/question/exam")
    public Response getExamQuestion(Map<String,Object> params){
        return new Response(wrongExamService.getExamQuestion(params));
    }

    /**
     *
     * @return 按考试获取错题考试信息
     */
    @GetMapping("/exam/list")
    public Response getStudentWrongExamList(Map<String,Object> params){
        return new Response(wrongExamService.getStudentWrongExamList(params));
    }

    /**
     *
     * @return 下载选择的考试的错题
     */
    @PostMapping("/download")
    public Response downloadWrongItem(Map<String,Object> params){
        return new Response(wrongExamService.downloadWrongItem(params));
    }

    /**
     * 根据id获取一条下载纪录的状态
     * @return
     */
    @GetMapping("/one/document")
    public Response getExamDocumentById(){
        return new Response(wrongExamService.getExamDocumentById(getParameterMap()));
    }

    /**
     * 根据id获取生成状态
     *
     * @param params wrongDocumentIds
     * @return wrongDocumentId status link
     */
    @GetMapping("/document/check")
    public Response checkWrongDocument(Map<String,Object> params){
        return new Response(wrongExamService.checkWrongDocument(params));
    }
    
    
    /**
     * 获取错题列表的课程参数
     *   页面: 小力讲错题 最上面的课程筛选项
     * @param params studentId
     * @return [{courseId, courseName}]
     */
    @GetMapping("/student/wechat/param/course")
    public Response getStudentWrongWechatParamCourseList(Map<String,Object> params) {
        return new Response(wrongExamService.getStudentWrongWechatParamCourseList(params));
    }

    /**
     * 获取错题列表的课程参数 - 带每个课程的错题数量
     *   页面: 小力讲错题 最上面的课程筛选项
     * @param params studentId schoolId
     * @return [{courseId, courseName, wrongCount}]
     */
    @GetMapping("/student/wechat/param/course/with/count")
    public Response getStudentWrongWechatParamCourseWithCountList(Map<String,Object> params) {
        return new Response(wrongExamService.getStudentWrongWechatParamCourseWithCountList(params));
    }
    
    /**
     * 学生获取错题本试题列表 - 不包含试题详情信息
     *
     * @param params studentId schoolId courseId [examStartDate] [difficultyType]
     *               wrongItemQuestionFrom（字典值wrongItemQuestionFrom） 1-考试错题 2-其他试题
     */
    @GetMapping("/student/list")
    public Response getStudentWrongList(Map<String,Object> params) {
        return new Response(wrongExamService.getStudentWrongList(params));
    }

    /**
     * 学生获取错题本试题列表 - 试题详情信息
     */
    @PostMapping("/student/question/detail")
    @DongniNotAccessLog
    public Response getStudentWrongDetailList(WrongQuestionDetailParam wrongQuestionDetailParam) {
        return new Response(wrongExamService.getStudentWrongDetailList(wrongQuestionDetailParam));
    }

    /**
     * 学生获取错题本试题详情
     *
     * @param params studentId wrongItemId
     * @return studentId wrongItemId
     *         question -- 试题详情
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    @GetMapping("/student/detail")
    public Response getStudentWrongDetail(Map<String,Object> params) {
        return new Response(wrongExamService.getStudentWrongDetail(params));
    }

    /**
     * 学生获取教辅主页错题详情
     *
     * @param params studentId studyGuideWrongItemId
     * @return question
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    @GetMapping("/student/homework/detail")
    public Response getStudentHomeworkWrongDetail(Map<String, Object> params) {
        return new Response(wrongExamService.getStudentHomeworkWrongDetail(params));
    }

    /**
     * 学生获取错题本试题详情(其他试题)
     *
     * @param params studentId wrongItemId
     * @return studentId wrongItemId
     *         question -- 试题详情
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    @GetMapping("/student/other/detail")
    public Response getStudentWrongDetailOther(Map<String,Object> params) {
        return new Response(wrongExamService.getStudentWrongDetailOther(params));
    }

    /**
     * 学生获取错题本试题详情(扫码获取错题列表用)
     *
     * @param params studentId wrongItemId
     * @return studentId wrongItemId
     *         question -- 试题详情
     *         recognitionValue -- 识别结果(客观题)
     *         tags -- 错因
     */
    @GetMapping("/student/mark/detail")
    public Response getStudentMarkItemDetailOther(Map<String,Object> params) {
        return new Response(wrongExamService.getStudentItemDetail(params));
    }

    /**
     * 重试下载错题文档
     *
     * @param params wrongDocumentId
     */
    @PostMapping("/document/download/retry")
    public Response retryDownloadWrongDocument(Map<String, Object> params) {
        wrongExamService.retryDownloadWrongDocument(params);
        return new Response();
    }
}
