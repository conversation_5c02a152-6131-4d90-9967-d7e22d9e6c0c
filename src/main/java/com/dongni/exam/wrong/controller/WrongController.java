package com.dongni.exam.wrong.controller;

import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.question.service.ExamQuestionKnowledgeService;
import com.dongni.exam.wrong.serevice.WrongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 错题明细接口
 *
 * <AUTHOR>
 * @date 2018/12/27 15:05
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/wrong")
public class WrongController extends BaseController {

    @Autowired
    private WrongService wrongService;

    /**
     * 考试错题订正状态更新
     * @param:  wrongItemId examId reviseStatus
     */
    @PostMapping("/exam/item/revise")
    public Response updateExamReviseStatus(@RequestBody Map<String,Object> params){
        wrongService.updateExamReviseStatus(params);
        return new Response();
    }

    /**
     * 获取学生错题统计（微信）
     *
     * @return
     */
    @GetMapping("/student/count")
    public Response getStudentWrongItemCount() {
        return new Response(wrongService.getStudentWrongItemCount(getParameterMap()));
    }

    /**
     * 查询学生错题列表
     * @param: studentId courseId wrongItemStatus [pageNo] [pageSize] [questionType] [knowledgeIds] [startDate] [endDate] [examId]
     * @return
     */
    @GetMapping("/student/items")
    public Response getStudentWrongItems() {
        return new Response(wrongService.getStudentWrongItems(getParameterMap()));
    }

    /**
     * 获取学生错题知识点树
     *
     * @return
     */
    @GetMapping("/knowledge")
    public Response getWrongKnowledge() {
        return new Response(wrongService.getWrongKnowledge(getParameterMap()));
    }
    
    /**
     * 获取学生的知识点错题信息
     * @param params studentId courseId
     *               difficultyType:easy/middle/difficulty
     *               {@link ExamQuestionKnowledgeService#getKnowledgeGraspByStudentTop10ForNanjingWaiguoyu(Map)}
     * @return knowledgeId    知识点id
     *         questionIdSet    知识点对应的错题ids
     */
    @GetMapping("/student/knowledgeId2questionIdSet/byDifficultyType")
    public Response getStudentWrongItemsByDifficultyType(Map<String, Object> params) {
        return new Response(wrongService.getStudentWrongItemsByDifficultyType(params));
    }
    
    /**
     * 获取学生难度等级的错题 20道 南京外国语学校定制
     * @param params studentId courseId
     *               difficultyType:easy/middle/difficulty
     *               {@link ExamQuestionKnowledgeService#getKnowledgeGraspByStudentTop10ForNanjingWaiguoyu(Map)}
     * @return 试题idSet
     */
    @GetMapping("/student/wrongQuestionIdSet/top20/nanjingWaiguoyu")
    public Response getStudentWrongQuestionIdSetTop20ByKnowledgeGraspStudentTop10ForNanjingWaiguoyu(Map<String, Object> params) {
        return new Response(wrongService.getStudentWrongQuestionIdSetTop20ByKnowledgeGraspStudentTop10ForNanjingWaiguoyu(params));
    }

    /**
     * 查询错题明细
     *
     * @return
     */
    @GetMapping("/item/detail")
    public Response getWrongItemDetail() {
        return new Response(wrongService.getWrongItemDetail(getParameterMap()));
    }

    /**
     * 查询错题明细
     *
     * com.dongni.exam.wrong.controller.WrongController#getWrongItemDetail()拿questionId去查t_wrong_item表
     * 会从t_wrong_item里同个questionId随机挑选一条，导致列表数据（老师角色-学生学情-错题再做）与查看详情数据不是同一道错题（其实列表数据也是随机的）
     *
     * 本接口传参使用wrongItemId，保证列表与查看详情是同一道题目，同时取t_wrong_item.exam_item_id直接查询t_exam_item
     * 而不联表t_wrong_exam_item，保证不会出现t_wrong_exam_item没有插入考试数据(统计的问题，t_wrong_exam_item没有正确维护错题的考试来源)
     *
     * @param params studentId questionId wrongItemId sourceType=1
     * @return question wrongItems
     */
    @GetMapping("/item/detail/new")
    public Response getWrongItemDetailNew(Map<String, Object> params) {
        return new Response(wrongService.getWrongItemDetailNew(params));
    }

    /**
     * 更新错题明细状态（移出移入错题本）
     *
     * @params: studentId questionId wrongItemStatus [wrongItemId]
     */
    @PostMapping("/item/status")
    public Response updateWrongItemStatus(@RequestBody Map<String, Object> params) {
        wrongService.updateWrongItemStatus(params);
        return new Response();
    }

    /**
     * 修改错题图片路径及订正状态
     *
     * 可以修改制定题目的fileURL与reviseStatus其中之一，也可以两者都修改
     *
     * @params: wrongItemId sourceId sourceType [fileURL] [reviseStatus]
     */
    @PostMapping("/item/fileURL")
    public Response updateReviseStatusOrFileURL(@RequestBody Map<String, Object> params){
        wrongService.updateReviseStatusOrFileURL(params);
        return new Response();
    }

    /**
     * 获取错题订正的图片路径
     *
     * @params: wrongItemId sourceId sourceType
     */
    @GetMapping("/item/fileURL")
    public Response getFileURL(){
        return new Response(wrongService.getFileURL(getParameterMap()));
    }


    /**
     * 获取练习的ID集合
     *
     * @return
     */
    @GetMapping("/exercise/ids")
    public Response getExerciseQuestionIds() {
        return new Response(wrongService.getExerciseQuestionIds(getParameterMap()));
    }

    /**
     * 保存类型记录
     *
     * @return
     */
    @PostMapping("/exercise")
    public Response insertExerciseItem(@RequestBody Map<String, Object> params) {
        wrongService.insertExerciseItem(params);
        return new Response();
    }

    /**
     * 获取指定课程的所有标签
     * @param: courseId
     * @return
     */
    @GetMapping("/course/tag/all")
    public Response getCourseAllTag(){
        return new Response(wrongService.getCourseAllTag(getParameterMap()));
    }

    /**
     * 获取指定错题的所有标签
     * @param: studentId
     * @param: questionId
     * @param: questionNumber
     * @return
     */
    @GetMapping("/item/tag/all")
    public Response getItemAllTag(){
        return new Response(wrongService.getItemAllTag(getParameterMap()));
    }

    /**
     * 仅保留指定的错题标签
     * @param: studentId
     * @param: questionId
     * @param: questionNumber
     * @param: tagList [{wrongTagId,tagName}]
     * @return
     */
    @PostMapping("/item/tag/keep/only")
    public Response keepOnlyTheseItemTags(@RequestBody Map<String, Object> params){
        wrongService.keepOnlyTheseItemTags(params);
        return new Response();
    }

    /**
     * 保存教辅作业错题 错因标签
     *
     * @param params studentId studyGuideWrongItemId 教辅作业错题id
     *               tagList [{wrongTagId,tagName}]
     */
    @PostMapping("/homework/tag/update")
    public Response updateStudyGuideWrongTagItem(Map<String, Object> params) {
        wrongService.updateStudyGuideWrongTagItem(params);
        return new Response();
    }


    /**
     * 获取标签
     *
     * @return
     */
    @GetMapping("/tag")
    public Response getWrongTag() {
        return new Response(wrongService.getWrongTag(getParameterMap()));
    }

    /**
     * 获取明细标签
     * @param: [wrongItemId] [studentId]
     * @return
     */
    @GetMapping("/tag/item")
    public Response getWrongTagItem() {
        return new Response(wrongService.getWrongTagItem(getParameterMap()));
    }



    /**
     * 删除明细标签
     *
     * @param params [wrongTagItemId] [wrongTagItemIdList]
     */
    @PostMapping("/tag/item")
    public Response deleteWrongTagItem(@RequestBody Map<String, Object> params) {
        wrongService.deleteWrongTagItem(params);
        return new Response();
    }


    /**
     * 获取回收站课程
     *
     * @return
     */
    @GetMapping("/trash/course")
    public Response getTrashCourse() {
        return new Response(wrongService.getTrashCourse(getParameterMap()));
    }

    /**
     * 获取本学期 已掌握错题数/待复习错题数
     * params classId studentId
     *
     * @return 错题数
     */
    @GetMapping("/student/wrongTopic/count")
    public Response getWrongTopicCount() {
        return new Response(wrongService.getWrongTopicCount(getParameterMap()));
    }

    /**
     * 获取本周新增错题数
     * params classId studentId
     *
     * @return 错题数
     */
    @GetMapping("/student/wrongTopic/new/count")
    public Response getWrongTopicNew() {
        return new Response(wrongService.getWrongTopicNew(getParameterMap()));
    }

    /**
     * 获取本周消灭错题
     * params classId studentId
     *
     * @return 消灭错题数
     */
    @GetMapping("/student/wrongTopic/destroy/count")
    public Response getWrongTopicDestroy() {
        return new Response(wrongService.getWrongTopicDestroy(getParameterMap()));
    }

    @GetMapping("/student/wrongTopicCount")
    public Response getWrongTopicCount2(){
        return new Response(wrongService.getWrongTopicCount2(getParameterMap()));
    }

    @GetMapping("/student/thisWeekWrongTopicCount")
    public Response getThisWeekWrongTopicCount(){
        return new Response(wrongService.getThisWeekWrongTopicCount(getParameterMap()));
    }

    /**
     * 保存学生讲错题记录
     */
    @PostMapping("/student/wrongExplain")
    public Response saveStudentWrongExplain(){
        wrongService.saveStudentWrongExplain(getParameterMap());
        return new Response();
    }

    /**
     * 获取学生的讲错题数量
     */
    @GetMapping("/student/wrongExplainDetail")
    public Response getWrongExplainCountDetail(){
        return new Response(wrongService.getWrongExplainCountDetail(getParameterMap()));
    }

    @GetMapping("/student/exercise/finish")
    public Response getStudentExerciseCount(){
        return new Response(wrongService.getStudentExerciseCount(getParameterMap()));
    }
    @GetMapping("/student/wrong/tag/init")
    public Response initWrongTag(){
        wrongService.initWrongTag(getParameterMap());
        return new Response();
    }


    /**
     * 获取原题列表详情
     * @param params  studentId courseId difficultyType
     * @return 试题详情
     */
    @GetMapping("/student/wrong/knowledge/question")
    public Response getStudentWrongQuestionDetailFilterByKnowledge(Map<String, Object> params) {
        return new Response(wrongService.getStudentWrongQuestionDetailFilterByKnowledge(params));
    }

    /**
     * 获取类题列表详情
     * @param params  studentId courseId difficultyType
     * @return 类题详情
     */
    @GetMapping("/student/wrong/knowledge/similar/question")
    public Response getStudentWrongSimilarQuestionDetailFilterByKnowledge(Map<String, Object> params) {
        return new Response(wrongService.getStudentWrongSimilarQuestionDetailFilterByKnowledge(params));
    }
}
