package com.dongni.exam.wrong.controller;

import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.wrong.serevice.WrongClassService;
import com.dongni.exam.wrong.serevice.WrongGradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 年级错题明细接口
 *
 * <AUTHOR>
 * @date 2018/12/27 15:05
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/wrong/grade")
public class WrongGradeController extends BaseController {

    @Autowired
    private WrongGradeService wrongGradeService;

    /**
     * 查询年级错题列表
     *
     * @return
     */
    @GetMapping("/items")
    public Response getWrongClassItems() {
        return new Response(wrongGradeService.getWrongGradeItems(getParameterMap()));
    }

    /**
     * 获取年级错题知识点
     *
     * @return
     */
    @GetMapping("/knowledge")
    public Response getWrongKnowledge() {
        return new Response(wrongGradeService.getWrongGradeKnowledge(getParameterMap()));
    }

    /**
     * 删除年级错题明细
     *
     * @param params
     * @return
     */
    @PostMapping("/items")
    public Response deleteWrongGradeItems(@RequestBody Map<String, Object> params) {
        wrongGradeService.deleteWrongGradeItems(params);
        return new Response();
    }

}
