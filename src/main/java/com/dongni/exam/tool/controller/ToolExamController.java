package com.dongni.exam.tool.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.tool.service.ToolExamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH+"/tool/exam")
public class ToolExamController extends BaseController {

    @Autowired
    private ToolExamService toolExamService;

    @GetMapping("")
    public Response getToolExamList(){
        return new Response(toolExamService.getToolExamList(getParameterMap()));
    }



    /**
     * 用户考试功能权限范围确定的实现
     * toolIds   需要插入或更新的toolId     例:  1 或 1,2,3
     * examId   需要插入或更新的examId
     * userId   用户Id
     * userName 用户名
     * userType 用户类型
     * @return
     */
    @PostMapping("")
    public Response saveOrUpdateToolExam(){
        toolExamService.saveOrUpdateToolExam(getParameterMap());
        return new Response();
    }
}
