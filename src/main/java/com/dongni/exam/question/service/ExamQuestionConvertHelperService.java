package com.dongni.exam.question.service;

import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.plan.service.ExamPaperService;
import com.dongni.exam.plan.service.ExamUploaderService;
import com.dongni.exam.recognition.service.RecognitionCardService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

/**
 * 试题题型转换
 * <p>
 * 逻辑:
 * 3.1 控制产生客观题重新识别任务的条件
 * ---目标考试试卷存在已完成的扫描任务 ---examId+paperId 查到任何一条t_exam_uploader.uploaderStatus = 7 并且有试题被标注为需要产生客观题重新识别（需要产生客观题重新识别的转换类型：单选、多选、判断题间的互转或选项个数的变更）
 * <p>
 * 3.2 控制产生客观题重新识别任务的时机
 * ---阅卷前转换
 * 对转换的试题打标记，
 * - 需要阅卷的
 * 模版考试识别处理完成时产生，- 非模版考试提交阅卷安排时（场景2.1）；
 * - 无需阅卷的
 * 调用阅卷完成前产生 （场景2.2,2.3）
 * <p>
 * ---阅卷中转换
 * 转换时直接产生（场景2.1）
 * <p>
 * ---科目阅卷完成后转换
 * 转换时直接产生（所有考试场景）
 *
 * <AUTHOR>
 * @Date 2024/5/31 周五 下午 04:22
 * @Version 1.0.0
 */
@Service
public class ExamQuestionConvertHelperService {
    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private ExamUploaderService examUploaderService;

    @Autowired
    private ExamPaperService examPaperService;

    @Autowired
    private RecognitionCardService recognitionCardService;

    @Autowired
    private ExamQuestionStructureService examQuestionStructureService;

    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;

    private static final Logger logger = LoggerFactory.getLogger(ExamQuestionConvertHelperService.class);

    /**
     * 判断前端是否需提示 处理重新识别任务
     * 根据examId + paperId查询examUploader表，只有有一条记录 uploadStatus > 2返回true
     *
     * @param params examId paperId
     * @return true需要提示处理重新识别任务 false不需要提示
     */
    public boolean showTip(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();

        params.put("examUploadStatus", DictUtil.getDictValue("examUploadStatus", "notUploaded"));
        return examUploaderService.hasAfterGivenStatus(params);
    }

    /**
     * 试题转换后置处理
     *
     * @param params examId paperId questionNumberList
     */
    public void questionConvertPostProcess(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotEmptyCollections("questionNumberList")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();

        Map<String, Object> examPaperInfo = examPaperService.getExamPaperInfoByExamIdAndPaperId(params);
        Long examId = MapUtils.getLong(params, "examId");
        Long paperId = MapUtils.getLong(params, "paperId");
        int examPaperStatus = MapUtils.getInteger(examPaperInfo, "examPaperStatus");

        if (MapUtils.isEmpty(examPaperInfo)) {
            logger.info("当前考试:{}, 试卷:{} 没有找到对应的examPaper记录!", examId, paperId);
            return;
        }

        // 过滤出客观题
        List<Map<String, Object>> questionStructureList = examQuestionStructureService.getQuestionListByPaperId(params);
        Set<Integer> objectiveQn = questionStructureList.stream()
                .filter(i -> DictUtil.isEquals(MapUtil.getInt(i, "readType"), "readType", "objective"))
                .map(i -> MapUtil.getInt(i, "questionNumber"))
                .collect(toSet());
        List<Integer> questionNumberList = MapUtil.getListInteger(params, "questionNumberList");
        questionNumberList = questionNumberList.stream().filter(objectiveQn::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(questionNumberList)) {
            return;
        }
        params.put("questionNumberList", questionNumberList);

        // 阅卷前转换 - 试题打标记
        if (DictUtil.isEquals(examPaperStatus, "examPaperStatus", "answerUpload", "answerCardUpload", "readArrange")) {
            // 有上传任务打标记
            if (showTip(params)) {
                markQuestionConvert(params);
            }
            return;
        }

        // 阅卷中或阅卷完成转换 - 直接产生识别任务
        if (DictUtil.isEquals(examPaperStatus, "examPaperStatus", "trialReadComplete", "readPaper", "readComplete")) {
            insertRecognitionTask(params);
        }
    }

    /**
     * 阅卷前转换客观题题型的,前面标记了试题,在此处处理标记的试题。
     * 此接口识别处理完成、提交阅卷安排都会调用
     *
     * @param params examId paperId
     */
    public void processRecognitionBeforePaperRead(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();

        // 如果没有标记的试题 - 退出
        List<Integer> markQuestionList = getMarkQuestionConvert(params);
        if (CollectionUtils.isEmpty(markQuestionList)) {
            return;
        }

        Map<String, Object> copy = MapUtil.copy(params, "examId", "paperId", "userId", "userName");
        copy.put("questionNumberList", markQuestionList);
        insertRecognitionTask(copy);

        unmarkQuestionConvert(copy);
    }

    /**
     * 插入客观题重新识别任务
     *
     * @param params examId paperId questionNumberList userId userName
     */
    private void insertRecognitionTask(Map<String, Object> params) {
        myAsyncConfigurer.executeWithContext(() -> {
            // 如果没有需要重新识别的学生 - 退出
            List<Map<String, Object>> recognitionStudentInfoList = getRecognitionStudentIdList(params);
            if (CollectionUtils.isEmpty(recognitionStudentInfoList)) {
                return;
            }

            Long paperId = MapUtils.getLong(params, "paperId");
            List<Integer> questionNumberList = MapUtil.getListInteger(params, "questionNumberList");
            List<Map<String, Object>> questionStructureList = examQuestionStructureService.getQuestionListByPaperId(params);
            Map<Integer, String> qn2Sn = questionStructureList.stream().collect(toMap(i -> MapUtil.getInt(i, "questionNumber"),
                    i -> MapUtil.getString(i, "structureNumber")));

            Map<String, Object> copy = MapUtil.copy(params, "examId", "paperId", "userId", "userName");
            copy.put("recognitionType", DictUtil.getDictValue("recognitionType", "Objective"));
            copy.put("studentList", recognitionStudentInfoList);
            copy.put("templateStatus", 1);  // 不知道啥,照着传参

            // 构建questionList参数 -- 补充structureNumber
            List<Map<String, Object>> questionListParams = new ArrayList<>();
            for (Integer qn : questionNumberList) {
                // questionStructure表数据不正确,直接退出吧。需要的话手动去生成重新识别任务
                if (!qn2Sn.containsKey(qn)) {
                    logger.error("questionNumber={}不存在paperId={}的questionStructure表中", qn, paperId);
                    return;
                }
                questionListParams.add(MapUtil.of("questionNumber", qn, "structureNumber", qn2Sn.get(qn)));
            }
            copy.put("questionList", questionListParams);

            try {
                recognitionCardService.insertRecognition(copy);
            } catch (Exception e) {
                // 忽略异常
                logger.error("客观题转换产生识别任务识别异常", e);
            }
        });
    }

    /**
     * 标记客观题试题做过题型转换 (包括题型变化、选项数量变化)
     *
     * @param params examId paperId questionNumberList
     */
    private void markQuestionConvert(Map<String, Object> params) {
        examRepository.insert("ExamQuestionConvertHelperMapper.markQuestionConvert", params);
    }

    /**
     * 取消标记客观题试题做过题型转换
     *
     * @param params examId paperId questionNumberList
     */
    private void unmarkQuestionConvert(Map<String, Object> params) {
        examRepository.insert("ExamQuestionConvertHelperMapper.unmarkQuestionConvert", params);
    }

    /**
     * 判断哪些试题有比较过题型转换
     *
     * @param params examId paperId
     * @return questionNumberList 标记过试题转换的题型
     */
    private List<Integer> getMarkQuestionConvert(Map<String, Object> params) {
        return examRepository.selectList("ExamQuestionConvertHelperMapper.getMarkQuestionConvert", params);
    }

    /**
     * 查询重新识别的学生id列表
     * <p>
     * 调用和页面相同的接口 获取已经处理完成的examUploader下的有答题卡的学生
     *
     * @param params examId paperId
     * @return 学生id列表
     */
    private List<Map<String, Object>> getRecognitionStudentIdList(Map<String, Object> params) {
        Map result = recognitionCardService.getCompletedAnswerCardAllStudentList(params);
        return (List<Map<String, Object>>) result.get("studentInfoList");
    }
}
