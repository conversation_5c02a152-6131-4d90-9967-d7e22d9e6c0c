package com.dongni.exam.question.service;

import com.dongni.basedata.system.account.service.impl.UserMembershipService;
import com.dongni.common.utils.ComparatorEx;
import com.dongni.common.utils.NewTreeUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.TreeUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.vo.PaperQuestionStructureInfoDTO;
import com.dongni.exam.common.mark.serivice.mark.IQnMappingClientService;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.QnMappingVO;
import com.dongni.exam.common.mark.vo.QuestionStructureVO;
import com.dongni.exam.knowledge.service.KnowledgeMapperConvertService;
import com.dongni.exam.question.enumeration.DifficultyType3;
import com.dongni.exam.question.enumeration.KnowledgeGraspLevel4;
import com.dongni.exam.question.utils.KnowledgeGraspUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.common.util.TreeUtil2;
import com.dongni.tiku.own.service.OwnAnswerCardService;
import com.dongni.tiku.own.service.OwnKnowledgeService;
import com.dongni.tiku.own.service.OwnPaperService;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * 考试知识点掌握度
 *
 * <AUTHOR>
 * @date 2019/01/07 09:51
 */
@Service
public class ExamQuestionKnowledgeService {
    
    private final static Logger LOGGER = LoggerFactory.getLogger(ExamQuestionKnowledgeService.class);

    @Autowired
    private ExamRepository commonRepository;

    @Autowired
    private OwnKnowledgeService ownKnowledgeService;

    @Autowired
    private OwnPaperService ownPaperService;

    @Autowired
    private OwnAnswerCardService ownAnswerCardService;

    @Autowired
    private UserMembershipService userMembershipService;

    @Autowired
    private KnowledgeMapperConvertService knowledgeMapperConvertService;

    @Autowired
    private IQnMappingClientService qnMappingClientService;

    @Autowired
    private IQsClientService qsClientService;

    /**
     * 获取知识点掌握情况
     *
     * @param params examId paperId courseId [classId]
     * @return 知识点掌握情况
     */
    public List<Map<String, Object>> getKnowledgeGrasp(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("courseId")
                .verify();

        if(!userMembershipService.getIsVip(params)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"非付费用户无权查看！");
        }

        boolean isQueryByClass = ObjectUtil.isValidId(params.get("classId"));

        // 查询对应困难度的题号 (注意：需要根据试题的整体困难度来区分)
        List<Integer> qns = commonRepository.selectList("ExamQuestionKnowledgeMapper.getQuestionByDifficultyCoefficient",params);

        params.put("qns",qns);
        if(org.springframework.util.CollectionUtils.isEmpty(qns)){
            return Collections.emptyList();
        }

        // 查询知识点掌握度
        List<Map<String, Object>> data = isQueryByClass
                ? commonRepository.selectList("ExamQuestionKnowledgeMapper.getKnowledgeGraspByClass", params)
                : commonRepository.selectList("ExamQuestionKnowledgeMapper.getKnowledgeGraspByGrade", params);
        if (org.springframework.util.CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        Map<String, HashMap<String, Object>> knowledgeMap = data.stream()
                .map(HashMap::new)
                .collect(toMap(item -> item.get("knowledgeId").toString(), item -> item));

        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);

        // 重新设置，防止覆盖
        data = TreeUtil2.getAllParentList(allKnowledgeList, data, "knowledgeId", "parentId");
        for (Map<String, Object> item : data) {
            HashMap<String, Object> knowledge = knowledgeMap.get(item.get("knowledgeId").toString());
            if (knowledge != null) {
                item.putAll(knowledge);
            }
        }

        return TreeUtil.list2Tree(data, "knowledgeId", "parentId", "children");
    }

    /**
     * 查询班级知识点综合掌握度
     *
     * @param params courseId classId [parentId]
     * @return
     */
    public List<Map<String, Object>> getKnowledgeGraspByClass(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("classId")
                .verify();

        if(!userMembershipService.getIsVip(params)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"非付费用户无权查看！");
        }
        
        // 获取当前课程的知识点
        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);
        Map<Object, Map<String, Object>> allKnowledgeMap = allKnowledgeList.stream()
                .collect(toMap(item -> item.get("knowledgeId"), item -> item));

        // 获取当前知识点
        List<Map<String, Object>> currentKnowledgeList = ownKnowledgeService.getKnowledgeByParent(params);
        Map<String, Map<String, Object>> currentKnowledgeMap = currentKnowledgeList.stream()
                .collect(toMap(item -> item.get("knowledgeId").toString(), item -> item));

        // 查询所有班级知识点综合掌握度
        List<Map<String, Object>> allKnowledgeGraspList = commonRepository.selectList(knowledgeMapperConvertService
          .convertKnowledgeMapperByClass(params,"ExamQuestionKnowledgeMapper.getClassKnowledgeGrasp"), params);
        allKnowledgeGraspList = allKnowledgeGraspList
                .stream()
                .filter(item -> allKnowledgeMap.get(item.get("knowledgeId")) != null)
                .peek(item -> item.putAll(allKnowledgeMap.get(item.get("knowledgeId"))))
                .collect(Collectors.toList());

        List<Map<String, Object>> rightChildTreeList = TreeUtil2.getRightChildTreeList(allKnowledgeList, allKnowledgeGraspList, "knowledgeId", "parentId");

        //  计算数量和知识点掌握度
        allKnowledgeGraspList = NewTreeUtil.computeNode(rightChildTreeList, "knowledgeId", "parentId", "checkCount", "checkCount2");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "grasp", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeGrasp", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "easyScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "middleScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "difficultyScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeEasyScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeMiddleScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeDifficultyScoreRate", "checkCount");

        allKnowledgeGraspList.forEach(this::replaceField);

        allKnowledgeGraspList = allKnowledgeGraspList.stream()
                .filter(item -> currentKnowledgeMap.get(item.get("knowledgeId").toString()) != null)
                .collect(Collectors.toList());

        // 获取有含有错题的知识点
        if (!ObjectUtil.isBlank(params.get("treeCode"))) {
            Map<String, Object> wrongInfo = getWrongInfo(allKnowledgeGraspList,params);
            allKnowledgeGraspList.forEach(a-> a.put("wrong", wrongInfo.getOrDefault(a.get("knowledgeId"),0)));
        }

        return allKnowledgeGraspList;
    }

    /**
    * @Description: 获取有含有错题的知识点
    */
    private Map<String, Object> getWrongInfo(List<Map<String, Object>> allKnowledgeGraspList,Map<String,Object> params) {
        // 计算子节点是否有错题
        Map<String, Object> wrongInfo = new HashMap<>();

        for (Map<String, Object> allKnowledgeGrasp : allKnowledgeGraspList) {
            // 获取子节点
            params.put("treeCode", allKnowledgeGrasp.get("treeCode"));
            List<Document> knowledgeList = ownKnowledgeService.getChildKnowledgeList(params);

            List<String> knowledgeIdList = new ArrayList<>();
            knowledgeIdList.add(allKnowledgeGrasp.get("knowledgeId").toString());
            knowledgeList.forEach(b->knowledgeIdList.add(b.get("_id").toString()));

            // 查询是否有错题
            Long count = null;
            params.put("knowledgeIdList", knowledgeIdList);
            if (ObjectUtil.isBlank(params.get("studentId"))) {
                count = commonRepository.selectOne("WrongClassMapper.getWrongClassKnowledgeByKnowledge", params);
            }else {
                count = commonRepository.selectOne("WrongClassMapper.getWrongStudentKnowledgeByKnowledge", params);
            }

            if (count > 0) {
                wrongInfo.put(allKnowledgeGrasp.get("_id").toString(), 1);
            } else {
                wrongInfo.put(allKnowledgeGrasp.get("_id").toString(), 0);
            }
        }

        return wrongInfo;
    }

    /**
    * @Description: 获取某个节点下的所有子节点
    * @Param: knowledgeId
    * @Param: rootId 根节点
    */
    public List<Map<String, Object>> getChildKnowledgeIds(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("rootId")
                .isNotBlank("parentId")
                .verify();

        // 获取知识点树
        List<Map<String, Object>> trees = TreeUtil.list2Tree(ownKnowledgeService.getKnowledgeByCourseId(params), "knowledgeId", "parentId", "child");
        Map<Object, List<Map<String, Object>>> treesById = trees.stream().collect(groupingBy(a -> a.get("_id")));
        Map<String, Object> tree = treesById.get(params.get("rootId")).get(0);
        // 获取子树
        Map<String, Object> knowledgeId = TreeUtil.getTreeChild(tree, "knowledgeId", params.get("parentId").toString());

        // 叶子节点直接返回
        if (knowledgeId.get("child") == null) {
            List<Map<String, Object>> knowledgeIdList = new ArrayList<>();
            knowledgeIdList.add(knowledgeId);
            return knowledgeIdList;
        }

        // 获取树下子节点的knowledgeId
        List<Map<String, Object>> knowledgeIdList = TreeUtil2.treeMenuList((List<Map<String, Object>>) knowledgeId.get("child"), "child");

        return knowledgeIdList;
    }

    /**
     * 获取班级叶子知识点综合掌握度（所有知识点，不止叶子知识点 20191009）
     *
     * @param params classId courseId gradeId
     * @return 返回查询结果
     */
    public Map<String, Object> getLeafKnowledgeGraspByClass(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("classId")
                .isValidId("courseId")
                .verify();

        Set<String> validSortFieldSet = Sets.newHashSet("easyScoreRate", "middleScoreRate", "difficultyScoreRate");
        String sortFieldParam = MapUtil.getStringNullable(params, "sortField");
        if (StringUtils.isNotBlank(sortFieldParam) && !validSortFieldSet.contains(sortFieldParam)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "sortField传参错误!");
        }

        DateUtil.formatDateTime(params,"startDate");
        DateUtil.formatDateTime(params,"endDate");

        Map<String, Object> resultMap = new HashMap<>();

        // 先获取学生综合知识点数量
        int iCount = commonRepository.selectOne(knowledgeMapperConvertService.convertKnowledgeMapperByClass(params,
          "ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspCountByClass"), params);

        if (iCount == 0) {
            resultMap.put("totalCount", 0);
            resultMap.put("knowledgeGraspList", Collections.emptyList());
            return resultMap;
        }

        // 获取当前课程的知识点
        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);
        Map<Object, Map<String, Object>> allKnowledgeMap = allKnowledgeList.stream()
                .collect(toMap(item -> item.get("knowledgeId"), item -> item));

        // 数据查询
        List<Map<String, Object>> list = commonRepository.selectList(knowledgeMapperConvertService.convertKnowledgeMapperByClass(params,
          "ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspByClass"), params);
        list = list
                .stream()
                .filter(item -> allKnowledgeMap.get(item.get("knowledgeId")) != null)
                .peek(item -> item.putAll(allKnowledgeMap.get(item.get("knowledgeId"))))
                .skip(Long.parseLong(params.get("currentIndex").toString()))
                .limit(Long.parseLong(params.get("pageSize").toString()))
                .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(list) && !ObjectUtil.isBlank(params.get("sortField"))){
            List<Map<String, Object>> sortField = list.stream().filter(l -> ObjectUtil.isBlank(l.get(params.get("sortField").toString()))).collect(Collectors.toList());
//            list.removeAll(sortField);
//            list.addAll(sortField);
        }

//        List<Map<String, Object>> data = TreeUtil.list2Tree(list, "knowledgeId", "parentId", "children");

        resultMap.put("totalCount", iCount);
        resultMap.put("knowledgeGraspList", list);

        return resultMap;
    }

    /**
     * 获取年级叶子知识点综合掌握度
     *
     * @param params courseId gradeId
     * @return 返回查询结果
     */
    public Map<String, Object> getLeafKnowledgeGraspByGrade(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("gradeId")
                .verify();

        DateUtil.formatDateTime(params,"startDate");
        DateUtil.formatDateTime(params,"endDate");

        Map<String, Object> resultMap = new HashMap<>();

        // 先获取年级综合知识点数量
        int iCount = commonRepository.selectOne(knowledgeMapperConvertService.convertKnowledgeMapperByGrade(params,
          "ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspCountByGrade"), params);

        if (iCount == 0) {
            resultMap.put("totalCount", 0);
            resultMap.put("knowledgeGraspList", Collections.emptyList());
            return resultMap;
        }

        // 获取当前课程的知识点
        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);
        Map<Object, Map<String, Object>> allKnowledgeMap = allKnowledgeList.stream()
                .collect(toMap(item -> item.get("knowledgeId"), item -> item));

        // 数据查询
        List<Map<String, Object>> list = commonRepository.selectList(knowledgeMapperConvertService.convertKnowledgeMapperByGrade(params,
          "ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspByGrade"), params);
        list = list.stream()
                .filter(item -> allKnowledgeMap.get(item.get("knowledgeId")) != null)
                .peek(item -> item.putAll(allKnowledgeMap.get(item.get("knowledgeId"))))
                .collect(Collectors.toList());

        List<Map<String, Object>> data = TreeUtil.list2Tree(list, "knowledgeId", "parentId", "children");

        resultMap.put("totalCount", iCount);
        resultMap.put("knowledgeGraspList", data);

        return resultMap;
    }

    /**
     * 查询学生知识点综合掌握度
     *
     * @param params courseId studentId [parentId]
     * @return
     */
    public List<Map<String, Object>> getKnowledgeGraspByStudent(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("studentId")
                .verify();

        if(!userMembershipService.getIsVip(params)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"非付费用户无权查看！");
        }

        // 获取当前课程的知识点
        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);
        Map<Object, Map<String, Object>> allKnowledgeMap = allKnowledgeList.stream()
                .collect(toMap(item -> item.get("knowledgeId"), item -> item));

        // 获取当前知识点
        List<Map<String, Object>> currentKnowledgeList = ownKnowledgeService.getKnowledgeByParent(params);
        Map<String, Map<String, Object>> currentKnowledgeMap = currentKnowledgeList.stream()
                .collect(toMap(item -> item.get("knowledgeId").toString(), item -> item));

        // 查询当前学生所有知识点综合掌握度
        List<Map<String, Object>> allKnowledgeGraspList = commonRepository.selectList(knowledgeMapperConvertService
          .convertKnowledgeMapperByStudent(params,"ExamQuestionKnowledgeMapper.getStudentKnowledgeGrasp"), params);
        allKnowledgeGraspList = allKnowledgeGraspList.stream()
                .filter(item -> allKnowledgeMap.get(item.get("knowledgeId")) != null)
                .peek(item -> item.putAll(allKnowledgeMap.get(item.get("knowledgeId"))))
                .collect(Collectors.toList());

        List<Map<String, Object>> rightChildTreeList = TreeUtil2.getRightChildTreeList(allKnowledgeList, allKnowledgeGraspList, "knowledgeId", "parentId");

        //  计算数量和知识点掌握度
        allKnowledgeGraspList = NewTreeUtil.computeNode(rightChildTreeList, "knowledgeId", "parentId", "checkCount", "checkCount2");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeGrasp", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "easyScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "middleScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "difficultyScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeEasyScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeMiddleScoreRate", "checkCount");
        allKnowledgeGraspList = NewTreeUtil.computeWeightAvgNode(allKnowledgeGraspList, "knowledgeId", "parentId", "gradeDifficultyScoreRate", "checkCount");

        // 替换字段
        allKnowledgeGraspList.forEach(this::replaceField);

        allKnowledgeGraspList = allKnowledgeGraspList.stream()
                .filter(item -> currentKnowledgeMap.get(item.get("knowledgeId").toString()) != null)
                .collect(Collectors.toList());

        // 获取有含有错题的知识点
        if (!ObjectUtil.isBlank(params.get("treeCode"))) {
            Map<String, Object> wrongInfo = getWrongInfo(allKnowledgeGraspList,params);
            allKnowledgeGraspList.forEach(a-> a.put("wrong", wrongInfo.getOrDefault(a.get("knowledgeId"),0)));
        }

        return allKnowledgeGraspList;

    }

    /**
     * 替换字段
     *
     * @param item
     */
    private void replaceField(Map<String, Object> item) {
        item.put("checkCount", Integer.parseInt(item.getOrDefault("checkCount2", 0).toString().split("\\.")[0]));
        item.put("grasp", item.get("grasp2"));
        item.put("gradeGrasp", item.get("gradeGrasp2"));
        item.put("easyScoreRate", item.get("easyScoreRate2"));
        item.put("middleScoreRate", item.get("middleScoreRate2"));
        item.put("difficultyScoreRate", item.get("difficultyScoreRate2"));
        item.put("gradeEasyScoreRate", item.get("gradeEasyScoreRate2"));
        item.put("gradeMiddleScoreRate", item.get("gradeMiddleScoreRate2"));
        item.put("gradeDifficultyScoreRate", item.get("gradeDifficultyScoreRate2"));

        item.remove("checkCount2");
        item.remove("grasp2");
        item.remove("gradeGrasp2");
        item.remove("easyScoreRate2");
        item.remove("middleScoreRate2");
        item.remove("difficultyScoreRate2");
        item.remove("gradeEasyScoreRate2");
        item.remove("gradeMiddleScoreRate2");
        item.remove("gradeDifficultyScoreRate2");
    }

    /**
     * 获取学生叶子知识点综合掌握度数量 (掌握度)
     *
     * @param params 查询参数 studentId(必选) courseId(必选),
     * @return 返回查询结果
     */
    public Map<String, Object> getLeafKnowledgeGraspByStudent(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("courseId")
                .verify();

        Set<String> invalidSortFieldSet = Sets.newHashSet("easyScoreRate", "middleScoreRate", "difficultyScoreRate");
        String sortField = MapUtil.getStringNullable(params, "sortField");
        if (StringUtils.isNotBlank(sortField) && !invalidSortFieldSet.contains(sortField)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "sortField传参错误!");
        }

        DateUtil.formatDateTime(params,"startDate");
        DateUtil.formatDateTime(params,"endDate");

        Map<String, Object> resultMap = new HashMap<>();

        // 先获取学生综合知识点数量
        int iCount = commonRepository.selectOne(knowledgeMapperConvertService.convertKnowledgeMapperByStudent(params,
          "ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspCountByStudent"), params);

        if (iCount == 0) {
            resultMap.put("totalCount", 0);
            resultMap.put("knowledgeGraspList", Collections.emptyList());
            return resultMap;
        }

        // 获取当前课程的知识点
        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);
        Map<Object, Map<String, Object>> allKnowledgeMap = allKnowledgeList.stream()
                .collect(toMap(item -> item.get("knowledgeId"), item -> item));

        // 数据查询
        List<Map<String, Object>> list = commonRepository.selectList(knowledgeMapperConvertService.convertKnowledgeMapperByStudent(params,
          "ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspByStudent"), params);
        list = list.stream()
                .filter(item -> allKnowledgeMap.get(item.get("knowledgeId")) != null)
                .peek(item -> item.putAll(allKnowledgeMap.get(item.get("knowledgeId"))))
                .skip(Long.parseLong(params.get("currentIndex").toString()))
                .limit(Long.parseLong(params.get("pageSize").toString()))
                .collect(Collectors.toList());
        
        resultMap.put("totalCount", iCount);
        resultMap.put("knowledgeGraspList", list);

        return resultMap;
    }
    
    /**
     * 获取学生知识点综合掌握度Top10 南京外国语学校定制
     *    1. 获取所有的知识点掌握度
     *    2. 排序截取top10
     *
     * @param params studentId courseId [startDate endDate]
     *               difficultyType:easy/middle/difficulty
     * @return knowledgeId      知识点id
     *         checkCount studentGrasp=gradeGrasp   知识点考核次数总和 知识点掌握度
     *         easyCheckCount       easyScoreRate       简单题考核次数 简单题掌握度
     *         middleCheckCount     middleScoreRate     中等题考核次数 中等题掌握度
     *         difficultyCheckCount difficultyScoreRate 困难题考核次数 困难题掌握度
     *         wrong    是否错了??
     */
    public List<Map<String, Object>> getKnowledgeGraspByStudentTop10ForNanjingWaiguoyu(Map<String, Object> params) {
        Verify.of(params).isValidId("studentId").isValidId("courseId").isNotBlank("difficultyType").verify();
        String difficultyType = MapUtil.getString(params, "difficultyType");
        DifficultyType3 difficultyType3 = DifficultyType3.getByCode(difficultyType);
        
        DateUtil.formatDateTime(params,"startDate");
        DateUtil.formatDateTime(params,"endDate");
        
        // 非高中      ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspByStudent
        // 高中  ExamQuestionKnowledgeSeniorMapper.getLeafKnowledgeGraspByStudent
        String statement = knowledgeMapperConvertService.convertKnowledgeMapperByStudent(params,
                "ExamQuestionKnowledgeMapper.getLeafKnowledgeGraspByStudent");
        List<Map<String, Object>> list = commonRepository.selectList(statement, params);
        if (CollectionUtils.isEmpty(list)) { return list; }
        
        // 获取当前课程的知识点
        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);
        Map<String, Map<String, Object>> knowledgeId2Info = allKnowledgeList.stream()
                .collect(toMap(item -> MapUtil.getTrim(item, "knowledgeId"), item -> item));
        
        return  list.stream()
                // 过滤掉不在当前课程的知识点
                .filter(item -> knowledgeId2Info.get(MapUtil.getTrim(item, "knowledgeId")) != null)
                // 排序 order by score, xxxCheckCount, lastCheckTime, id desc
                .sorted(ComparatorEx
                        .<Map<String, Object>, Integer>desc(item -> getKnowledgeGraspScore(item, difficultyType3))
                        .thenDesc(item -> MapUtil.getInt(item, difficultyType3.getCode() + "CheckCount"))
                        .thenDesc(item -> MapUtil.getCast(item, "lastCheckTime"))
                        .thenDesc(item -> MapUtil.getLong(item, "studentKnowledgeStatisticsId"))
                )
                // top 10
                .limit(10)
                // 插入知识点信息
                .peek(item -> item.putAll(knowledgeId2Info.get(MapUtil.getTrim(item, "knowledgeId"))))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取难度对应的知识点掌握度的分数
     *     难度 -> 难度掌握度 难度考核次数
     *          -> 难度掌握度 -> 难度掌握等级 -> 难度掌握等级权重
     *          -> 难度掌握等级权重 * 难度考核次数
     * 会加入一些辅助测试的数据
     * @param studentKnowledgeGrasp 学生知识点掌握度
     * @param difficultyType3       难度
     * @return 难度对应的知识点掌握度的分数
     */
    private int getKnowledgeGraspScore(Map<String, Object> studentKnowledgeGrasp, DifficultyType3 difficultyType3) {
        String difficultyType3Code = difficultyType3.getCode();
        String difficultyTypeScoreRateField = difficultyType3Code + "ScoreRate";
        String difficultyTypeCheckCountField = difficultyType3Code + "CheckCount";
        double difficultyTypeScoreRate = MapUtil.getDouble(studentKnowledgeGrasp, difficultyTypeScoreRateField);
        KnowledgeGraspLevel4 knowledgeGraspLevel = KnowledgeGraspUtil.getKnowledgeGraspLevel(difficultyType3, difficultyTypeScoreRate);
        int difficultyTypeCheckCount = MapUtil.getInt(studentKnowledgeGrasp, difficultyTypeCheckCountField);
        int knowledgeGraspScore = knowledgeGraspLevel.getWeight() * difficultyTypeCheckCount;
        studentKnowledgeGrasp.put("__difficultyTypeCode", difficultyType3Code);
        studentKnowledgeGrasp.put("__difficultyTypeName", difficultyType3.getName());
        studentKnowledgeGrasp.put("__difficultyTypeScoreRateField", difficultyTypeScoreRateField);
        studentKnowledgeGrasp.put("__difficultyTypeScoreCountField", difficultyTypeCheckCountField);
        studentKnowledgeGrasp.put("__difficultyTypeScoreRate", difficultyTypeScoreRate);
        studentKnowledgeGrasp.put("__difficultyTypeCheckCount", difficultyTypeCheckCount);
        studentKnowledgeGrasp.put("__knowledgeGraspLevelName", knowledgeGraspLevel.getName());
        studentKnowledgeGrasp.put("__knowledgeGraspLevelWeight", knowledgeGraspLevel.getWeight());
        studentKnowledgeGrasp.put("__knowledgeGraspScore", knowledgeGraspScore);
        return knowledgeGraspScore;
    }
    
    
    /**
     * 根据试卷维护知识点信息及试题难度信息
     *   t_question_knowledge    可能没有
     *   t_question_difficulty   肯定有
     *   试卷中的题目知识点包括两处
     *     parts[] -> categories[] -> questions[] -> question -> knowledgeList[]
     *     parts[] -> categories[] -> questions[] -> question -> questions[] -> knowledgeList[]
     *     需要累加做去重处理
     * @param params paperId
     */
    public void insertKnowledgeAndDiffByPaperId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();
        Map<String, Object> paperDetail = ownPaperService.getPaperDetailNotKnowledge(params);
        if (paperDetail == null || paperDetail.isEmpty()) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "没有找到该试卷");
        }
        List<Map<String, Object>> questionList = new ArrayList<>();
        // 是否有知识点 试卷中可能还没有知识点
        AtomicBoolean hasKnowledge = new AtomicBoolean(false);
        // parts[] -> categories[] -> questions[]
        PaperUtil.forEachQuestion(paperDetail, item -> {
            Map<String, Object> question = MapUtil.getMap(item, "question");
            String questionId = question.get("_id").toString();
            if (questionId != null) {
                long courseId = MapUtil.getLong(question, "courseId");
                // knowledgeId -> knowledgeInfo  用于去重处理/去掉无效知识点
                Map<String, Map<String, Object>> validKnowledgeIdMapKnowledgeInfo = new HashMap<>();
                
                // parts[] -> categories[] -> questions[] -> question -> knowledgeList[]
                List<Map<String, Object>> questionKnowledgeList = MapUtil.getListMap(question, "knowledgeList");
                // 使用懂你推题后，有些knowledgeList为null
                if (questionKnowledgeList == null) {
                    questionKnowledgeList = new ArrayList<>();
                }
                List<Map<String, Object>> subQuestionList = MapUtil.getListMap(question, "questions");
                // parts[] -> categories[] -> questions[] -> question -> questions[] -> knowledgeList[]
                for (Map<String, Object> subQuestion : subQuestionList) {
                    List<Map<String, Object>> subQuestionKnowledgeList = MapUtil.getListMap(subQuestion, "knowledgeList");
                    if (CollectionUtils.isNotEmpty(subQuestionKnowledgeList)) {
                        questionKnowledgeList.addAll(subQuestionKnowledgeList);
                    }
                }
                
                // 去重 并填充parentId信息
                for (Map<String, Object> questionKnowledge : questionKnowledgeList) {
                    String knowledgeId = questionKnowledge.get("_id").toString();
                    if (!validKnowledgeIdMapKnowledgeInfo.containsKey(knowledgeId)) {
                        questionKnowledge.put("courseId", courseId);
                        // 获取知识点信息 可能获取不到，
                        // 比如题目原来有3个知识点，有一个已经被废弃了，此时是获取不到知识点信息的，不需要维护
                        Map<String, Object> knowledge;
                        try {
                            knowledge = ownKnowledgeService.getKnowledgeById(questionKnowledge);
                        } catch (CommonException e) {
                            LOGGER.warn("获取知识点失败: courseId: {}; _id: {}; message:{}", courseId, knowledgeId, e.getMessage());
                            continue;
                        }
                        questionKnowledge.put("parentId", knowledge.get("parentId"));
                        questionKnowledge.put("leaf", knowledge.get("leaf"));
                        validKnowledgeIdMapKnowledgeInfo.put(knowledgeId, questionKnowledge);
                    }
                }
                List<Map<String, Object>> validKnowledgeList = new ArrayList<>(validKnowledgeIdMapKnowledgeInfo.values());
                Map<String, Object> questionMap = new HashMap<>();
                questionMap.put("questionId", questionId);
                questionMap.put("courseId", courseId);
                questionMap.put("difficulty", new BigDecimal(question.get("difficulty").toString()));
                if (CollectionUtils.isNotEmpty(validKnowledgeList)) {
                    hasKnowledge.set(true);
                    questionMap.put("knowledgeList", validKnowledgeList);
                }
                questionList.add(questionMap);
            }
        });
        if (!questionList.isEmpty()) {
            params.put("questionList", questionList);
            if (hasKnowledge.get()) {
                insertPaperQuestionKnowledge(params);
            }
            insertQuestionDifficulty(params);
        }
    }

    /**
     * 插入试卷的知识点信息
     * @param params
     *  - questionList []
     *    - courseId    科目id
     *    - questionId  试题id
     *    - knowledgeList []
     *      - _id         知识点id
     *      - name        知识点名称
     *      - parentId    知识点父id 可能为空，即该知识点为最上层的节点
     *  - userId
     *  - userName
     */
    private void insertPaperQuestionKnowledge(Map<String, Object> params) {
        params.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.insert("ExamQuestionKnowledgeMapper.updateQuestionKnowledge", params);
    }

    /**
     * 插入题目难度信息
     * @param params
     *  - questionList []
     *    - questionId  试题id
     *    - difficulty  难度
     *  - userId
     *  - userName
     */
    private void insertQuestionDifficulty(Map<String, Object> params) {
        params.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.insert("ExamQuestionKnowledgeMapper.updateQuestionDifficulty", params);
    }

    /**
     * 获取年级单场考试单个科目的知识点掌握度
     *
     * @param params examId paperId courseId gradeId
     * @return 年级单场考试单个科目的知识点掌握度
     */
    public List<Map<String, Object>> getExamKnowledgeGraspByGrade(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("courseId")
                .isValidId("gradeId")
                .isValidId("studentId")
                .isValidId("classId")
                .verify();
        List<Map<String, Object>> schoolData = commonRepository.selectList("ExamQuestionKnowledgeMapper.getExamKnowledgeGraspByGrade", params);
        List<Map<String, Object>> classData = commonRepository.selectList("ExamQuestionKnowledgeMapper.getExamKnowledgeGraspByClass", params);
        List<Map<String, Object>> questionInfo = ownAnswerCardService.getAnswerCardStructure(params);
        List<String> questionIdList = questionInfo.stream()
                .filter(i -> i.get("questionId") != null)
                .filter(i -> ObjectUtil.isValueEquals(i.get("courseId"), MapUtil.getLong(params, "courseId")))
                .map(i -> MapUtil.getString(i, "questionId"))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(questionIdList)) {
            return Collections.emptyList();
        }
        params.put("questionIdList", questionIdList);
        List<Map<String, Object>> studentData = commonRepository.selectList("ExamQuestionKnowledgeMapper.getExamKnowledgeGraspByStudent", params);
        List<Map<String, Object>> resultData = new ArrayList<>();
        if (CollectionUtils.isEmpty(schoolData) || CollectionUtils.isEmpty(studentData) || CollectionUtils.isEmpty(classData)) {
            return Collections.emptyList();
        }
        Map<String, Map<String, Object>> schoolDataMap = schoolData.stream().collect(toMap(m -> m.get("knowledgeId").toString(), m -> m));
        Map<String, Map<String, Object>> classDataMap = classData.stream().collect(toMap(m -> m.get("knowledgeId").toString(), m -> m));
        Map<String, List<Map<String, Object>>> studentDataMap = studentData.stream().collect(groupingBy(m->m.get("knowledgeId").toString()));
        for (Map.Entry<String, List<Map<String, Object>>> entry : studentDataMap.entrySet()) {
            Map<String, Object> schoolMap = schoolDataMap.get(entry.getKey());
            Map<String, Object> classMap = classDataMap.get(entry.getKey());
            Map<String,Object> map = entry.getValue().get(0);
            if (MapUtils.isNotEmpty(schoolMap)) {
                Double schoolKnowledgeGrasp = Double.valueOf(schoolMap.get("knowledgeGrasp").toString());
                map.put("schoolKnowledgeGrasp", schoolKnowledgeGrasp);
            }
            if (MapUtils.isNotEmpty(classMap)) {
                Double classKnowledgeGrasp = Double.valueOf(classMap.get("knowledgeGrasp").toString());
                map.put("classKnowledgeGrasp", classKnowledgeGrasp);
            }
            resultData.add(map);
        }
        return resultData;
    }

    /**
     * 获取学生单场考试单个科目的知识点掌握度
     *
     * @param params examId paperId courseId studentId
     * @return 学生单场考试单个科目的知识点掌握度
     */
    public List<Map<String, Object>> getExamKnowledgeGraspByStudent(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("courseId")
                .isValidId("studentId")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        List<Map<String, Object>> questionInfo = ownAnswerCardService.getAnswerCardStructure(params);
        List<String> questionIdList = questionInfo.stream()
                .filter(i -> i.get("questionId") != null)
                .filter(i -> ObjectUtil.isValueEquals(i.get("courseId"), MapUtil.getLong(params, "courseId")))
                .map(i -> MapUtil.getString(i, "questionId"))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(questionIdList)) {
            return Collections.emptyList();
        }

        // 当前试卷questionId + knowledgeId
        params.put("questionIdList", questionIdList);
        List<Map<String, Object>> data = commonRepository.selectList("ExamQuestionKnowledgeMapper.getExamKnowledgeGraspByStudent", params);
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        // 根据阅卷映射关系得出 questionId -> 阅卷试题结构的关系
        Map<String, List<Integer>> questionId2PaperQuestionNumbers = questionInfo.stream()
                .filter(i -> i.get("questionId") != null)
                .collect(groupingBy(i -> MapUtil.getString(i, "questionId"), mapping(i -> MapUtil.getInt(i, "questionNumber"), toList())));
        List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(paperId);
        Map<Integer, List<Integer>> paperQn2MarkQns = qnMappingList.stream()
                .collect(groupingBy(QnMappingVO::getPaperQn, mapping(QnMappingVO::getMarkQn, toList())));
        List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(paperId);
        Map<Integer, QuestionStructureVO> markQn2Question = questionStructureVOS.stream().collect(toMap(QuestionStructureVO::getQuestionNumber, i -> i));
        Map<String, List<QuestionStructureVO>> questionId2MarkQuestion = new HashMap<>();
        for (Map.Entry<String, List<Integer>> entry : questionId2PaperQuestionNumbers.entrySet()) {
            String questionId = entry.getKey();
            List<Integer> paperQns = entry.getValue();
            List<QuestionStructureVO> markQuestions = paperQns.stream()
                    .filter(paperQn2MarkQns::containsKey)
                    .map(paperQn2MarkQns::get)
                    .flatMap(Collection::stream)
                    .map(markQn2Question::get)
                    .collect(toList());
            questionId2MarkQuestion.put(questionId, markQuestions);
        }


        Map<String, List<Map<String, Object>>> dataMap = data.stream().collect(groupingBy(m -> (String) m.get("knowledgeId")));
        List<Map<String, Object>> resultData = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : dataMap.entrySet()) {
            List<Map<String, Object>> list = entry.getValue();
            Double questionScore = 0D;
            StringBuilder sb = new StringBuilder();
            for (Map<String, Object> map : list) {
                List<QuestionStructureVO> markQuestions = questionId2MarkQuestion.get(map.get("questionId").toString());
                if (CollectionUtils.isNotEmpty(markQuestions)){
                    for (QuestionStructureVO question:markQuestions){
                        Double score = question.getScoreValue().doubleValue();
                        questionScore += score;
                        sb.append(question.getStructureNumber() + ",");
                    }
                }
            }
            Map<String,Object> resultMap=list.get(0);
            resultMap.put("structureNumber",sb.deleteCharAt(sb.lastIndexOf(",")).toString());
            resultMap.put("questionTotalScore",questionScore);
            resultData.add(resultMap);
        }
        resultData.sort(Comparator.comparing(s->Double.valueOf(s.get("knowledgeGrasp").toString())));
        return resultData;
    }

    public List<Map<String, Object>> getExamKnowledgeChildGraspByStudent(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("courseId")
                .isValidId("studentId")
                .verify();

        // 获取试卷的试题ID
        long paperId = MapUtil.getLong(params, "paperId");
        long courseId = MapUtil.getLong(params, "courseId");
        List<PaperQuestionStructureInfoDTO> questionStructureInfo = ownPaperService.getQuestionStructureInfo(paperId);
        List<String> questionIdList = questionStructureInfo.stream()
                .filter(item -> ObjectUtil.isValueEquals(item.getCourseId(), courseId))
                .filter(item -> ObjectUtil.isNotBlank(item.getQuestionId()))
                .map(PaperQuestionStructureInfoDTO::getQuestionId).distinct().collect(Collectors.toList());
        params.put("questionIdList", questionIdList);

        List<Map<String, Object>> questionInfo = ownAnswerCardService.getAnswerCardStructure(params);
        if (CollectionUtils.isEmpty(questionIdList)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> data = commonRepository.selectList("ExamQuestionKnowledgeMapper.getExamKnowledgeGraspByStudentForWrongBook", params);
        if (org.springframework.util.CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        // 根据阅卷映射关系得出 questionId -> 阅卷试题结构的关系
        Map<String, List<Integer>> questionId2PaperQuestionNumbers = questionInfo.stream().filter(item -> ObjectUtil.isNotBlank(item.get("questionId")))
                .collect(groupingBy(i -> MapUtil.getString(i, "questionId"), mapping(i -> MapUtil.getInt(i, "questionNumber"), toList())));
        List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(paperId);
        Map<Integer, List<Integer>> paperQn2MarkQns = qnMappingList.stream()
                .collect(groupingBy(QnMappingVO::getPaperQn, mapping(QnMappingVO::getMarkQn, toList())));
        List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(paperId);
        Map<Integer, QuestionStructureVO> markQn2Question = questionStructureVOS.stream().collect(toMap(QuestionStructureVO::getQuestionNumber, i -> i));
        Map<String, List<QuestionStructureVO>> questionId2MarkQuestion = new HashMap<>();
        for (Map.Entry<String, List<Integer>> entry : questionId2PaperQuestionNumbers.entrySet()) {
            String questionId = entry.getKey();
            List<Integer> paperQns = entry.getValue();
            List<QuestionStructureVO> markQuestions = paperQns.stream()
                    .filter(paperQn2MarkQns::containsKey)
                    .map(paperQn2MarkQns::get)
                    .flatMap(Collection::stream)
                    .map(markQn2Question::get)
                    .collect(toList());
            questionId2MarkQuestion.put(questionId, markQuestions);
        }

        Map<String, List<Map<String, Object>>> dataMap = data.stream().collect(groupingBy(m -> (String) m.get("knowledgeId")));
        List<Map<String, Object>> resultData = new ArrayList<>();
        for (Map.Entry<String, List<Map<String, Object>>> entry : dataMap.entrySet()) {
            List<Map<String, Object>> list = entry.getValue();
            String structureNumber = "";
            for (Map<String, Object> map : list) {
                List<QuestionStructureVO> markQuestions = questionId2MarkQuestion.get(map.get("questionId").toString());
                if (CollectionUtils.isNotEmpty(markQuestions)){
                    structureNumber = markQuestions.stream().sorted(Comparator.comparing(QuestionStructureVO::getQuestionNumber)).map(item -> item.getStructureNumber()).collect(joining(","));
                }
            }
            Map<String,Object> resultMap=list.get(0);
            resultMap.put("structureNumber",structureNumber);
            resultData.add(resultMap);
        }

        Map<String, HashMap<String, Object>> knowledgeMap = resultData.stream()
                .map(HashMap::new)
                .collect(toMap(item -> item.get("knowledgeId").toString(), item -> item));

        List<Map<String, Object>> allKnowledgeList = ownKnowledgeService.getKnowledgeByCourseId(params);

        // 重新设置，防止覆盖
        resultData = TreeUtil2.getAllParentList(allKnowledgeList, resultData, "knowledgeId", "parentId");
        for (Map<String, Object> item : resultData) {
            HashMap<String, Object> knowledge = knowledgeMap.get(item.get("knowledgeId").toString());
            if (knowledge != null) {
                item.putAll(knowledge);
            }
        }

        return TreeUtil.list2Tree(resultData, "knowledgeId", "parentId", "children");

    }

    /**
     * 获取学生知识点掌握度
     * @param params studentId [knowledgeIdList]
     * @return
     */
    public List<Map<String, Object>> getStudentKnowledgeGraspList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();
        return commonRepository.selectList(knowledgeMapperConvertService.convertKnowledgeMapperByStudent(params,
          "ExamQuestionKnowledgeMapper.getStudentKnowledgeGraspList"), params);
    }
}
