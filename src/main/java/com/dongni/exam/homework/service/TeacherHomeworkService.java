package com.dongni.exam.homework.service;

import com.alibaba.fastjson.JSON;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.todo.service.CommonTodoService;
import com.dongni.basedata.export.user.service.CommonUserService;
import com.dongni.common.bean.Pair;
import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.NumberUtil;
import com.dongni.common.wechat.service.WeChatService;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.exception.DongniException;
import com.dongni.commons.exception.ErrorCode;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.NumberFormatUtil;
import com.dongni.commons.utils.arithmetic.RateArithmetic;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.enums.UserTypeEnum;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolTeacherService;
import com.dongni.exam.common.mark.serivice.exam.IExamService;
import com.dongni.exam.common.mark.serivice.item.IExamItemService;
import com.dongni.exam.common.mark.vo.ClassTeacherVO;
import com.dongni.exam.common.mark.vo.ExamVO;
import com.dongni.exam.enumeration.ExamTypeEnum;
import com.dongni.exam.mark.service.ExamMarkMonitorService;
import com.dongni.exam.plan.service.ExamListService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.newmark.bean.entity.ExamClass;
import com.dongni.newmark.bean.entity.ExamPaper;
import com.dongni.newmark.manager.IExamClassManager;
import com.dongni.newmark.manager.IExamPaperManager;
import com.dongni.open.mq.producer.HomeworkCorrectChangeProducer;
import com.dongni.open.mq.service.WeChatNotifyMqService;
import com.dongni.tiku.common.enumeration.PaperCreationType;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.service.OwnAnswerCardService;
import com.dongni.tiku.own.service.OwnExamPaperService;
import com.dongni.tiku.own.service.OwnPaperService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;
import static java.util.stream.Collectors.*;

/**
 * Created by scott
 * time: 20:41 2018/12/11
 * description:作业
 */
@Service
public class TeacherHomeworkService {

    // 日志记录
    private static final Logger log = LogManager.getLogger(TeacherHomeworkService.class);

    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private CommonTodoService commonTodoService;
    @Autowired
    private WeChatService weChatService;
    @Autowired
    private HomeworkHandleService homeworkHandleService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private HomeworkPreparationService homeworkPreparationService;
    @Autowired
    private ExamListService examListService;
    @Autowired
    private ISchoolTeacherService schoolTeacherService;
    @Autowired
    private IExamClassManager examClassManager;
    @Autowired
    private IExamItemService examItemService;
    @Autowired
    private IExamPaperManager examPaperManager;
    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private OwnExamPaperService ownExamPaperService;
    @Autowired
    private ExamMarkMonitorService examMarkMonitorService;
    @Autowired
    private WeChatNotifyMqService weChatNotifyMqService;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private HomeworkCommonService homeworkCommonService;
    @Autowired
    private ExamService examService;
    @Autowired
    private ExamConfigService examConfigService;
    @Autowired
    private OwnAnswerCardService ownAnswerCardService;
    @Autowired
    private IExamService newExamService;

    /**
     * 获取作业详情
     *
     * @param parameterMap examId classId
     * @return 作业详情
     */
    public Map<String, Object> getHomeworkDetail(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("classId").verify();

        ExamVO examDetail = newExamService.getExamDetail(MapUtil.getLong(parameterMap, "examId"));
        if (examDetail == null) {
            throw new DongniException(ErrorCode.USER_EXCEPTION, "作业不存在或已删除");
        }
        // 数据查询
        List<Map<String, Object>> detailList = commonRepository.selectList("TeacherHomeworkMapper.getHomeworkDetail", parameterMap);

        List<Map<String, Object>> teacherList = new ArrayList<>();
        for (Map<String, Object> detail : detailList) {
            Map<String, Object> teacher = MapUtil.of(
                    "teacherId", detail.get("teacherId"),
                    "teacherName", detail.get("teacherName")
            );
            teacherList.add(teacher);
        }

        Map<String, Object> detail = MapUtil.copy(detailList.stream().findFirst().orElse(Collections.emptyMap()),
                "classId", "className", "correctMode", "examPaperId", "paperId", "courseId", "courseName",
                "examId", "examName", "examType", "startDate", "endDate", "creatorId", "createDateTime");
        detail.put("teacherList", teacherList);
        // 使用 Redis 补充 totalStudent submitted
        String cacheId = homeworkCommonService.getCacheId(parameterMap);
        String hkStudentUnSubmitKey = JedisUtil.getKey("hkStudentUnSubmit",cacheId);
        String hkStudentSubmitKey = JedisUtil.getKey("hkStudentSubmit",cacheId);
        long total = JedisTemplate.execute(jedis -> {
           long submitted = jedis.scard(hkStudentSubmitKey);
           long unSubmitted = jedis.scard(hkStudentUnSubmitKey);
           long totalStudent = submitted + unSubmitted;
           detail.put("totalStudent",totalStudent);
           detail.put("submitted",submitted);
           return totalStudent;
        });

        // 历史数据处理
        if(total == 0){
            detail.putAll(commonRepository.selectOne("TeacherHomeworkMapper.getExamStudentTotal", parameterMap));
        }

        Map<String, Object> paperDetail = ownPaperService.getPaperDetailNotKnowledge(detail);
        detail.put("creationType", paperDetail.get("creationType"));
        detail.put("paperName", paperDetail.get("paperName"));

        if (ObjectUtil.isValueEquals(paperDetail.get("creationType"), PaperCreationType.SELF_EDIT.getType())) {
            // 自主编辑的答题卡，查询上传的作业试卷
            detail.put("fileUrl", ownPaperService.getHomeworkFileUrlByPaperId(paperDetail));
        }

        return detail;
    }

    /**
     * 获取未交卷/待批改/已批改学生
     *
     * @param parameterMap examId courseId classId resultStatus（1 0 2）
     * @return 学生
     */
    public List<Map<String, Object>> getExamResult(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("classId").isValidId("paperId").isNumeric("resultStatus").verify();

        List<Map<String,Object>> result = commonRepository.selectList("TeacherHomeworkMapper.getExamResult", parameterMap);

        if(CollectionUtils.isNotEmpty(result)) {
            // 分两步，先查result，再查item表，防止关联查询item表造成查询缓慢
            List<Long> studentIdList = result.stream().map(e -> Long.valueOf(e.get("studentId").toString())).collect(Collectors.toList());
            parameterMap.put("studentIdList", studentIdList);
            List<Map<String,Object>> itemList = commonRepository.selectList("TeacherHomeworkMapper.getItemByStudentIdList", parameterMap);

            if(CollectionUtils.isNotEmpty(itemList)) {
                Map<Long, List<Map<String, Object>>> studentItemMap = itemList.stream().collect(groupingBy(e -> Long.valueOf(e.get("studentId").toString())));

                result.forEach(e -> {
                    Long studentId = Long.parseLong(e.get("studentId").toString());
                    List<Map<String, Object>> studentItemList = studentItemMap.get(studentId);
                    if(studentItemList!=null) {

                        // 主观题数量
                        int count = 0;
                        // 没有满分的题目数量
                        int incorrectCount = 0;
                        // 0分的题目数量
                        int zeroCount = 0;
                        double totalScore = 0;
                        // 批改所用时间，单位秒
                        int time = 0;

                        for (Map<String, Object> item : studentItemList) {
                            if (Long.parseLong(item.get("readType").toString()) == 2) {
                                count++;
                            }
                            // fix feedback 9451
                            // 因item支持两位小数后，判断造成错题计数出错
                            if (!NumberUtil.bigDecimalEquals(item.get("finallyScore"), item.get("scoreValue"))) {
                                incorrectCount++;
                                if (ObjectUtil.isValueEquals(MapUtils.getDouble(item, "finallyScore"), 0.0)) {
                                    zeroCount++;
                                }
                            }
                            time += (DateUtil.getDateLongValue(item.get("modifyDateTime").toString()) - DateUtil.getDateLongValue(item.get("createDateTime").toString())) / 1000;
                            totalScore +=Double.parseDouble(item.get("finallyScore").toString());
                        }

                        e.put("count", count);
                        e.put("incorrectCount", incorrectCount);
                        e.put("totalScore", NumberFormatUtil.forMatScore(totalScore));
                        e.put("correctRate", 1 - ((incorrectCount - zeroCount) * 1.0 / 2 + zeroCount) / studentItemList.size());
                        e.put("time", time);
                        if (count == 0) {
                            e.put("corrected", "");
                        }
                    }
                });
            }
        }

        return result;
    }

    /**
     * 获取作答数量情况 白了少年头
     *
     * @param parameterMap examId courseId classId
     * @return 作答数量
     */
    public List<Map<String, Object>> getExamResultCount(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("classId").verify();
        Map<String,Object> paper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",parameterMap);
        parameterMap.put("paperId",paper.get("paperId"));
        return commonRepository.selectList("TeacherHomeworkMapper.getExamResultCount", parameterMap);
    }

    /**
     * 学生作答得分率
     *
     * @param parameterMap classId examBaseSubjectId
     * @return 学生作答得分率
     */
    public List<Map<String, Object>> getScoreRate(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("classId").isValidId("courseId").verify();
        //年级  班级 得分率
        Map<String,Object> paper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",parameterMap);
        parameterMap.put("paperId",paper.get("paperId"));
        List<Map<String, Object>> items = commonRepository.selectList("TeacherHomeworkMapper.getResultItem", parameterMap);
        if (null == items || items.size() == 0) { return new ArrayList<>(); }
        Long classId = MapUtils.getLong(parameterMap, "classId");
        Map<Integer, List<Map<String, Object>>> questions = items.stream().collect(groupingBy(i -> (Integer) i.get("questionNumber")));
        List<Map<String, Object>> rs = new ArrayList<>();
        boolean showCorrectRate = examConfigService.showCorrectRate(parameterMap);
        // 不展示分数时要展示正确率
        String classRateField = showCorrectRate ? "classCorrectRate" : "classScoreRate";
        String schoolRateField = showCorrectRate ? "schoolCorrectRate" : "schoolScoreRate";
        for (Integer num : questions.keySet()) {
            Map<String, Object> map = new HashMap<>();
            List<Map<String, Object>> list = questions.get(num);
            map.put("questionNumber", list.get(0).get("questionNumber"));
            map.put("structureNumber", list.get(0).get("structureNumber"));
            map.put("readType", list.get(0).get("readType"));
            List<Map<String, Object>> classList =
              list.stream().filter(x -> ObjectUtil.isValueEquals(MapUtils.getLong(x, "classId"), classId)).collect(toList());

            if(showCorrectRate){
                map.put("classCorrectRate", classList.size() == 0 ? 0 :
                  classList.stream().mapToDouble(
                    x -> MapUtils.getDouble(x, "finallyScore", 0.0).equals(MapUtils.getDouble(x, "scoreValue")) ? 1
                      : (MapUtils.getDouble(x, "finallyScore", 0.0).equals(0.0) ? 0 : 0.5)).sum() / classList.size());
                map.put("schoolCorrectRate",
                  list.stream().mapToDouble(
                    x -> MapUtils.getDouble(x, "finallyScore", 0.0).equals(MapUtils.getDouble(x, "scoreValue")) ? 1
                      : (MapUtils.getDouble(x, "finallyScore", 0.0).equals(0.0) ? 0 : 0.5)).sum() / list.size());
            }else {
                map.put("classScoreRate", classList.size() == 0 ? 0 : classList.stream()
                  .mapToDouble(x -> MapUtils.getDouble(x, "finallyScore") / MapUtils.getDouble(x, "scoreValue")).sum() / classList.size());
                map.put("schoolScoreRate", list.stream()
                  .mapToDouble(x -> MapUtils.getDouble(x, "finallyScore") / MapUtils.getDouble(x, "scoreValue")).sum() / list.size());
            }
            map.put("count", classList.stream().filter(x -> ObjectUtil.isValueEquals(x.get("readStatus"), 0)).count());
            String subtractRate = new RateArithmetic().subtract(map.get(classRateField), map.get(schoolRateField));
            map.put("paperId", parameterMap.get("paperId"));
            map.put("subtractRate", subtractRate);
            rs.add(map);
        }
        return rs;
    }


    /**
     * 修改批发方式
     *
     * @param params examId classId
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateCorrectMode(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isValidId("examId").isValidId("examPaperId").isValidId("classId").isValidId("courseId").isNumeric("correctMode").verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        homeworkHandleService.checkExamType(params);
        examMarkMonitorService.asynSaveMonitor(params,"开始修改批发方式");

        Map<String,Object> paper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",params);
        params.put("paperId",paper.get("paperId"));

        // 一个考试只能一个人修改
        String examId = params.get("examId").toString();
        String lockKey = "distributed:homework:correctMode:"+examId;
        JedisTemplate.lockExecute(lockKey,120,0,()-> {

            int correctMode = Integer.parseInt(params.get("correctMode").toString());

            //如果要切换成互评，但只剩一个学生没有被批改，则提示不支持
            if(4 == correctMode){
                //截止时间
                Date date = commonRepository.selectOne("TeacherHomeworkMapper.getExamStartDate", params);
                if (null != date && date.compareTo(new Date())<0) {
                    Integer count = commonRepository.selectOne("TeacherHomeworkMapper.getUnCorrectStudentCount", params);
                    if(count==1){
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"只剩余一个学生未被批改，不支持互评");
                    }
                }else {
                    Integer count = commonRepository.selectOne("TeacherHomeworkMapper.getUnCorrectStudent", params);
                    if(count==1){
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"只剩余一个学生未被批改，不支持互评");
                    }
                }
            }

            //更新correctMode
            commonRepository.update("TeacherHomeworkMapper.updateExamClass", params);
            commonRepository.update("TeacherHomeworkMapper.updateExamClassPaper", params);
            //删除班级学生批改待办
            Document doc = new Document().append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                    .append("displayContent.classId", Long.valueOf(params.get("classId").toString()))
                    .append("todoType", DictUtil.getDictValue("todoType", "homeworkCorrectStu"));
            commonTodoService.deleteAllTodoTask(doc);
            if (3 == correctMode) {
                //获取已经提交的学生
                List<Map<String,Object>> student = commonRepository.selectList("TeacherHomeworkMapper.getSubmitStudent", params);
                if(CollectionUtils.isNotEmpty(student)){
                    //生成学生批改待办
                    params.put("student",student);
                    params.put("todoType", DictUtil.getDictValue("todoType", "homeworkCorrectStu"));
                    homeworkHandleService.createStudentHomeworkTodo(params);
                }
            }else if(4 == correctMode){//互评
                //先为examResultEvaluation中未批改完的生成待办
                List<Map<String,Object>> list = commonRepository.selectList("TeacherHomeworkMapper.getExamResultEvaluation", params);
                //生成互评关系
                for(Map<String,Object> map : list){
                    Map<String, Object> correctMap = new HashMap<>(map);
                    correctMap.put("studentId",map.get("evaluationStudentId"));
                    Map<String,Object> p = new HashMap<>(params);
                    List<Map<String,Object>> stu = new ArrayList<>();
                    stu.add(correctMap);
                    p.put("student",stu);
                    Map<String, Object> student = commonRepository.selectOne("HomeworkHandleMapper.getExamStudent", p);
                    map.put("userId",1);
                    map.put("userName","admin");
                    map.put("currentTime",DateUtil.getCurrentDateTime());
                    map.put("exist",1);
                    homeworkHandleService.createTodo(map,student);
                }

                //再分配未有批改任务的
                homeworkHandleService.assignTask(params);
            }

            return null;
        });
        examMarkMonitorService.asynSaveMonitor(params,"成功修改批发方式");
    }

    /**
     * 更改结束时间
     *
     * @param params examId  examPaperId  endDate
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateHomeworkEndDate(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isValidId("examId").isValidId("examPaperId").isNotBlank("endDate").verify();
        homeworkHandleService.checkExamType(params);
        params.put("currentTime", DateUtil.getCurrentDateTime());
        boolean cancelSchedule = ObjectUtil.isValueEquals(params.get("cancelSchedule"),true);

        examMarkMonitorService.asynSaveMonitor(params,"开始更改结束时间");

        Map<String,Object> examPaper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",params);
        if(MapUtils.isEmpty(examPaper)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"数据不存在或已经被删除");
        }
        params.putAll(examPaper);

        // 一个考试只能一个人修改
        String examId = params.get("examId").toString();
        String lockKey = "distributed:homework:correctMode:"+examId;
        JedisTemplate.lockExecute(lockKey,120,0,()-> {
            //时间不能晚于开始时间
            Date date = commonRepository.selectOne("TeacherHomeworkMapper.getExamStartDate", params);
            try {
                if (null != date && DateUtil.parseDateTime(params.get("endDate").toString()).getTime() <= date.getTime()) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "截止时间不能晚于开始时间！");
                }
            } catch (ParseException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }
            commonRepository.update("TeacherHomeworkMapper.updateExam", params);

            //作业提交结束时间-异步进行
            myAsyncConfigurer.getAsyncExecutor().execute(()->{
                String queue = JedisUtil.getKey("homeworkSubmitQueue");
                try {
                    if(cancelSchedule){
                        JedisTemplate.execute(jedis -> jedis.zrem(queue,params.get("examPaperId").toString()));
                    }else {
                        String examPaperId = params.get("examPaperId").toString();
                        long timeout = DateUtil.parseDateTime(params.get("endDate").toString()).getTime();
                        boolean isExist = JedisTemplate.execute(jedis -> {
                            boolean hasKey = jedis.zscore(queue,examPaperId) != null;
                            double score = timeout * 1.0;
                            jedis.zadd(queue, score, examPaperId);
                            return hasKey;
                        });
                        // 可能会手动终止，所以这里不需要判断
                        //if(!isExist){
                            homeworkHandleService.resubmitHomeworkForUnSubmitStudent(params);
                        //}
                    }
                } catch (ParseException e) {
                    LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                }

                //更新mongodb中相关待办的截止时间
                if(!cancelSchedule){
                    Bson filter = eq("displayContent.examPaperId", Long.parseLong(params.get("examPaperId").toString()));
                    Bson combine = combine(set("displayContent.endDate", params.get("endDate"))
                            , set("modifierId", params.get("userId"))
                            , set("modifyDateTime", new Date()));
                    commonTodoService.updateMany(filter, combine, params.get("userId").toString());
                }
            });

            return null;
        });
        examMarkMonitorService.asynSaveMonitor(params,"成功更改结束时间");
    }


    /**
     * 提醒交作业
     *
     * @param params examId classId
     * 支持提醒多个班级
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void remindSubmit(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isValidId("examId").isNotBlank("classId")
                .isNotBlank("teacherName").isNotBlank("examName").isNotBlank("endDate")
                .isValidId("examPaperId").isNotBlank("courseName").verify();

        String[] classIds = params.get("classId").toString().split(",");
        params.put("classId",classIds);

        //如果作业未开始，不允许操作
        Date date = commonRepository.selectOne("TeacherHomeworkMapper.getExamStartDate", params);
        if(date.compareTo(new Date())>0){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"作业未开始！");
        }

        //查找还可提醒次数
        for (String classId:classIds
        ) {
            String key = JedisUtil.getKey("remindSubmit", params.get("examPaperId").toString(), classId);
            String time = JedisTemplate.execute(jedis -> jedis.get(key));
            if (ObjectUtil.isBlank(time) || Integer.parseInt(time)<=0) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "每场考试只有2次提醒交作业的次数，您已经超过上限啦！");
            }
        }
        //获取未作答学生
        params.put("resultStatus", DictUtil.getDictValue("resultStatus", "absent"));
        List<Map<String, Object>> student = commonRepository.selectList("HomeworkHandleMapper.getExamStudentInfo", params);
        if (CollectionUtils.isEmpty(student)) {
            return;
        }

        Long paperId = commonRepository.selectOne("HomeworkHandleMapper.getPaperId", params);

        params.put("relativeList", student);
        params.put("relativeUserType", DictUtil.getDictValue("userType", "student") );
        //调用基础库取openId
        List<Map<String, Object>> user = commonUserService.getWeChatUser(params);

        if(CollectionUtils.isEmpty(user)){
            user = new ArrayList<>();
        }else {
            Map<String, List<Map<String, Object>>> studentMap = student.stream().collect(groupingBy(s -> s.get("studentId").toString()));
            user.forEach(u->{
                u.put("studentId",u.get("relativeId"));
                List<Map<String, Object>> list = studentMap.get(u.get("studentId").toString());
                if (CollectionUtils.isNotEmpty(list)) {
                    u.put("classId", list.get(0).get("classId"));
                    u.put("paperId", list.get(0).get("paperId"));
                }
            });
        }

        params.put("relativeUserType", DictUtil.getDictValue("userType", "parent"));
        //调用基础库取openId
        List<Map<String, Object>> parent = commonUserService.getParentWeChatUser(params);

        if(CollectionUtils.isNotEmpty(parent)){
            //学生id
            Map<String, List<Map<String, Object>>> studentMap = student.stream().collect(groupingBy(s -> s.get("studentId").toString()));
            parent.forEach(p-> {
                List<Map<String, Object>> list = studentMap.get(p.get("studentId").toString());
                if (CollectionUtils.isNotEmpty(list)) {
                    p.put("classId", list.get(0).get("classId"));
                    p.put("paperId", list.get(0).get("paperId"));
                }
            });

            user.addAll(parent);
        }
        //第三方
        List<Map<String, Object>> thirdWeChatUser = commonUserService.getThirdWeChatUser(params);
        if(CollectionUtils.isNotEmpty(thirdWeChatUser)){
            //学生id
            Map<String, List<Map<String, Object>>> studentMap = student.stream().collect(groupingBy(s -> s.get("studentId").toString()));
            thirdWeChatUser.forEach(p-> {
                List<Map<String, Object>> list = studentMap.get(p.get("studentId").toString());
                if (CollectionUtils.isNotEmpty(list)) {
                    p.put("classId", list.get(0).get("classId"));
                    p.put("paperId", list.get(0).get("paperId"));
                }
            });
            user.addAll(thirdWeChatUser);
        }

        user = user.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(Comparator.comparing(u->u.get("studentId").toString()+u.get("weChatOpenId")))), ArrayList::new));


        if (null != user && user.size() > 0) {
            for (Map<String, Object> u : user) {
                u.put("courseName",params.get("courseName"));
                u.put("examName",params.get("examName"));
                u.put("endDate",params.get("endDate"));
                u.put("examId",params.get("examId"));
                u.put("paperId",paperId);
                u.put("operatorId",params.get("userId"));
                u.put("operatorName",params.get("userName"));
                u.put("teacherName",params.get("teacherName"));
                //mq 提醒交作业
//                HomeworkTodoProducer.send(u);
                try {
                    weChatNotifyMqService.sendHomeworkTodoMessage(u);
                } catch (Exception e) {
                    log.error("提醒交作业:通知微信用户时失败: {}", u, e);
                    // TODO ali sls
                }
            }
            //提醒次数减少1次
            JedisTemplate.execute(jedis -> {
                for (String classId:classIds
                ) {
                    String key = JedisUtil.getKey("remindSubmit", params.get("examPaperId").toString(), classId);
                    String t = jedis.get(key);
                    jedis.set(key, String.valueOf(new Integer(t) - 1));
                }
                return null;
            });

        }else {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"找不到要提醒的学生用户！");
        }
    }

    /**
     * 结束作业 未评分的置为零分 0：正常（已评分），1：缺考（未提交），2：已提交（未评分）
     *
     * @param params examId classId examPaperId
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void endHomework(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isValidId("examId").isValidId("paperId").isValidId("classId").verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());

        examMarkMonitorService.asynSaveMonitor(params,"开始结束作业-teacher");

        String examId = params.get("examId").toString();
        String paperId = params.get("paperId").toString();
        String lockKey = "distributed:homework:read:"+examId+":"+paperId;
        JedisTemplate.lockExecute(lockKey,120,0,()-> {

            //删除未作答学生的item
            List<Map<String, Object>> absentStudent = commonRepository.selectList("HomeworkHandleMapper.getAbsentStudent", params);
            params.put("absentStudent", absentStudent);
            if (CollectionUtils.isNotEmpty(absentStudent)) {
                params.put("currentTime",DateUtil.getCurrentDateTime());
                commonRepository.insert("HomeworkHandleMapper.backupUnfinishedExamResultItem",params);
                commonRepository.delete("HomeworkHandleMapper.deleteItem", params);
                log.info("用户{}删除考试{}的学生明细：{}",params.get("userId"),params.get("examId"),absentStudent);
            }

            //将有提交的学生exam_result置为已评分
            List<Map<String, Object>> stu = commonRepository.selectList("TeacherHomeworkMapper.getExamItemStu", params);
            params.put("student", stu);
            params.put("resultStatus", 0);

            if (CollectionUtils.isNotEmpty(stu)) {
                commonRepository.update("TeacherHomeworkMapper.updateExamResult", params);
                //将exam_item置为已阅
                commonRepository.update("TeacherHomeworkMapper.updateExamItem", params);
            }

            //将exam_class置为阅卷完成
            params.put("classStatus", DictUtil.getDictValue("examPaperStatus", "readComplete"));
            commonRepository.update("TeacherHomeworkMapper.updateExamClass", params);
            commonRepository.update("TeacherHomeworkMapper.updateExamClassPaper", params);

            //删除待办
            myAsyncConfigurer.getAsyncExecutor().execute(()->{

                //如果所有班级都完成，则将整个考试都置为已完成
                Integer count = commonRepository.selectOne("TeacherHomeworkMapper.getExamClassCount", params);
                homeworkHandleService.readComplete(params, count);
                //删除该班级待办
                Document docClass = new Document().append("displayContent.paperId", Long.valueOf(params.get("paperId").toString()))
                        .append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                        .append("displayContent.classId", Long.valueOf(params.get("classId").toString()));
                commonTodoService.deleteAllTodoTask(docClass);
                //如果该老师的所有班级都已完成
                Map m  = homeworkHandleService.getExamUnFinishClassCount(params);
                if (Integer.valueOf(m.get("unSubmitCount").toString())==0){
                    Document docTeacher = new Document().append("displayContent.paperId", Long.valueOf(params.get("paperId").toString()))
                            .append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                            .append("relativeId", Long.parseLong(m.get("teacherId").toString()));
                    long start = System.currentTimeMillis();
                    commonTodoService.deleteAllTodoTask(docTeacher);
                    log.info("删除作业{}老师{}的待办耗时{}毫秒",params.get("examId"),m.get("teacherId"),System.currentTimeMillis()-start);
                }

            });

            return null;
        });

        examMarkMonitorService.asynSaveMonitor(params,"成功结束作业-teacher");
    }


    /**
     * 获取学生item
     *
     * @param parameterMap examId studentId paperId
     * @return 学生item
     */
    public List<Map<String, Object>> getExamItem(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("studentId").isValidId("paperId").verify();
        List<Map<String, Object>> stuItems = commonRepository.selectList("TeacherHomeworkMapper.getExamItem", parameterMap);
        List<Map<String, Object>> paperStructure = ownAnswerCardService.getAnswerCardStructure(parameterMap);
        Map<Integer, Integer> qnMap = paperStructure.stream()
          .collect(toMap(item -> MapUtils.getInteger(item, "questionNumber"), item -> MapUtils.getInteger(item, "unitType")));
        for (Map<String, Object> item : stuItems) {
            Integer qn = MapUtils.getInteger(item, "questionNumber");
            if (qnMap.containsKey(qn)) {
                item.put("unitType", qnMap.get(qn));
            }
        }
        return stuItems;
    }


    /**
     * 获取未批改学生
     *
     * @param parameterMap examId paperId classId
     * @return 学生
     */
    public List<Map<String, Object>> getUnReadStudent(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("paperId").isValidId("classId").verify();

        // 2020年2月19日 learnmore 做一个额外的判断，如果无需阅卷或阅卷完成的则直接更新状态
        List<Map<String,Object>> all = commonRepository.selectList("TeacherHomeworkMapper.getUnReadStudent", parameterMap);

        myAsyncConfigurer.getAsyncExecutor().execute(()->{
            List<Map<String,Object>> students = commonRepository.selectList("TeacherHomeworkMapper.getErrorUnReadStudent", parameterMap);
            if(CollectionUtils.isNotEmpty(students)){
                examMarkMonitorService.asynSaveMonitor(parameterMap,"发现有未归类到已批改的学生，开始自动更新");
                parameterMap.put("student",students);
                parameterMap.put("currentTime",DateUtil.getCurrentDateTime());
                parameterMap.put("resultStatus",0);
                parameterMap.put("userId",1);
                parameterMap.put("userName","admin");
                commonRepository.update("TeacherHomeworkMapper.updateExamResult",parameterMap);
                examMarkMonitorService.asynSaveMonitor(parameterMap,"自动完成未归类到已批改的学生处理，共处理："+students.size());
            }

        });

        if(CollectionUtils.isEmpty(all) && ObjectUtil.isBlank(parameterMap.get("search"))){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,"恭喜您，已经没有待批改的学生");
        }

        return all;
    }

    /**
     * 获取未批改题号
     *
     * @param parameterMap examId paperId classId
     * @return 未批改题号
     */
    public List<Map<String, Object>> getQuestionNumber(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("paperId").isValidId("classId").verify();
        //学生评
        if(!ObjectUtil.isBlank(parameterMap.get("studentIds"))){
            parameterMap.put("studentIds",parameterMap.get("studentIds").toString().split(","));
        }
        return commonRepository.selectList("TeacherHomeworkMapper.getQuestionNumber", parameterMap);
    }


    /**
     * 获取老师的考试列表
     *
     * @param params schoolId userId userName
     * @return 考试列表
     */
    public Map<String, Object> getHomework(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isNotBlank("classId").isNotBlank("classStatus").verify();
        // 参数解析
        params.put("classId", params.get("classId").toString().split(","));
        params.put("classStatus", params.get("classStatus").toString().split(","));
        params.put("examType", params.get("examType").toString().split(","));

        Map<String, Object> rs = new HashMap<>();
        Integer count = commonRepository.selectOne("TeacherHomeworkMapper.getExamCount", params);
        rs.put("totalCount", count);
        if (count == 0) {
            rs.put("exam", new ArrayList<>());
            return rs;
        }
        List<Map<String, Object>> exam = commonRepository.selectList("TeacherHomeworkMapper.getExam", params);
        exam.forEach(e ->{
            StringBuilder courseName = new StringBuilder();
            List<Map<String, Object>> courseInfo = commonRepository.selectList("TeacherHomeworkMapper.getHomeworkCourseInfo", e);
            for (Map<String, Object> course:courseInfo
            ) {
                courseName.append(course.get("courseName")+" ");
            }
            e.put("courseName",courseName);
        });
        rs.put("exam", exam);
        return rs;
    }

    public Map<String, Object> getHomeworkList(Map<String, Object> params){
        //params.put("examType", 8);
        Map<String, Object> examList = examListService.getExamList(params);
        List<Map<String, Object>> exams = (List<Map<String, Object>>) examList.get("exam");
        boolean isTch = UserTypeEnum.isTeacher(DongniUserInfoContext.get().getUserType());
        Set<Pair<Long, Long>> clsCourseSet = getClsCourseSet(params, isTch);
        for (Map<String, Object> exam : exams) {
            checkIsStudyGuideAndGetInfo(isTch, clsCourseSet, exam);
            //获取提交数量，作为前端判断删除的依据
            Integer count = commonRepository
                    .selectOne("TeacherHomeworkMapper.getExamAllResultCount", MapUtil.of("examId", exam.get("examId")));
            exam.put("submitNumber", count);
        }
        return examList;
    }

    /**
     * 获取执教班级和 班主任班级
     */
    private Set<Pair<Long, Long>> getClsCourseSet(Map<String, Object> params, boolean isTch) {
        Set<Pair<Long, Long>> clsCourseSet = new HashSet<>();
        if (isTch) {
            List<Map<String, Object>> teachingClass = commonClassService.getTeachingClass(params);
            teachingClass.forEach(x->clsCourseSet.add(Pair.of(MapUtil.getLong(x, "classId"), MapUtil.getLong(x, "courseId"))));
            List<Map<String, Object>> headerClass = commonClassService.getHeaderClass(params);
            headerClass.forEach(x-> clsCourseSet.add(Pair.of(MapUtil.getLong(x, "classId"), 0L)));
        }
        return clsCourseSet;
    }

    /**
     * 判断是否是线上作业, 是 获取是否需要批阅
     */
    private void checkIsStudyGuideAndGetInfo(boolean isTch, Set<Pair<Long, Long>> clsCourseSet, Map<String, Object> exam) {
        if (!isTch || MapUtil.getInt(exam, "examType") != ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode()) {
            return;
        }
        long examId = MapUtil.getLong(exam, "examId");
        long courseId = MapUtil.getLong(exam, "courseId");
        List<ExamClass> examClasses = examClassManager.findByExamId(examId);
        List<String> clsNames = new ArrayList<>();
        List<Long> clsIds = new ArrayList<>();
        examClasses.forEach(x -> {
            if (clsCourseSet.contains(Pair.of(x.getClassId(), courseId))) {
                clsNames.add(x.getClassName());
                clsIds.add(x.getClassId());
            } else if (clsCourseSet.contains(Pair.of(x.getClassId(), 0L))) {
                clsNames.add(x.getClassName());
            }
        });
        // 没有执教班级则无批阅权限 needMark -1 无批阅权限，0 已批阅，1 需要批阅
        int needMark = -1;
        if (!clsIds.isEmpty()) {
            List<ExamPaper> examPapers = examPaperManager.findByExamId(examId);
            if (CollectionUtils.isNotEmpty(examPapers)) {
                needMark = examItemService.markComplete(examId, examPapers.get(0).getPaperId(), clsIds) ? 0 : 1;
            }
        }
        exam.put("needMark", needMark);
        exam.put("classNames", clsNames);
    }


    /**
     * 获取小题批改进度
     *
     * @param parameterMap examId paperId questionNumber classId
     * @return 批改进度
     */
    public Map<String, Object> getQuestionTrace(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").isValidId("paperId").isValidId("classId").isNumeric("questionNumber").verify();
        return commonRepository.selectOne("TeacherHomeworkMapper.getQuestionTrace", parameterMap);
    }

    /**
     * 获取小题  (按小题批改)
     * params classId examId paperId questionNumber
     *
     * @return 小题
     */
    public List<Map<String, Object>> getQuestion(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").isValidId("paperId").isValidId("classId").isNumeric("questionNumber").verify();
        //学生评
        if(!ObjectUtil.isBlank(params.get("studentIds"))){
            params.put("studentIds",params.get("studentIds").toString().split(","));
        }
        List<Map<String, Object>> quItems = commonRepository.selectList("TeacherHomeworkMapper.getQuestion", params);
        List<Map<String, Object>> paperStructure = ownAnswerCardService.getAnswerCardStructure(params);
        List<Map<String, Object>> question = paperStructure.stream()
          .filter(x -> ObjectUtil.isValueEquals(params.get("questionNumber"), x.get("questionNumber"))).collect(toList());
        if (CollectionUtils.isNotEmpty(question)) {
            quItems.forEach(x -> x.put("unitType", question.get(0).get("unitType")));
        }
        return quItems;
    }

    /**
     * 更新学生小题得分
     * examId paperId classId questionNumber scoreValue finallyScore
     * examItemInfo(examItemId studentId studentName finallyScore)
     */
    public void updateExamItem(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("classId")
                .isNotBlank("questionNumber")
                .isNumeric("finallyScore")
                .isNumeric("scoreValue")
                .verify();

        homeworkHandleService.checkExamType(params);
        String examId = params.get("examId").toString();
        String paperId = params.get("paperId").toString();
        String classId = params.get("classId").toString();
        String questionNumber = params.get("questionNumber").toString();
        float scoreValue = Float.parseFloat(params.get("scoreValue").toString());
        float finallyScore = Float.parseFloat(params.get("finallyScore").toString());

        List<Map<String, Object>> examItemInfo = (List<Map<String, Object>>) params.get("examItemInfo");
        String examItemIds = examItemInfo.stream().map(a -> a.get("examItemId").toString()).collect(Collectors.joining(","));

        if (scoreValue < finallyScore || finallyScore < 0) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "给分不合理");
        }

        params.put("finallyScore", finallyScore);
        params.put("examItemIds", examItemInfo.stream().map(a->a.get("examItemId")).collect(Collectors.toList()));
        commonRepository.update("TeacherHomeworkMapper.updateExamItemFinallyScore", params);

        // 设置20天后过期
        int expire = 60 * 60 * 24 * 20;

        // 新增批改记录list
        insertRecordList(examItemIds,examId,paperId,classId,questionNumber,expire);

        // 新增历史批改记录list
        examItemInfo.forEach(a -> a.put("finallyScore", finallyScore));
        insertHistoryRecordList(examItemInfo,examId,paperId,classId,questionNumber,expire);
    }


    /**
     * 更新学生小题得分
     * params examItemIds finallyScore
     */
    public void correctionExamItem(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("examItemIds")
                .isNumeric("finallyScore")
                .isValidId("examId")
                .verify();
        String[] examItemIds = params.get("examItemIds").toString().split(",");
        params.put("examItemIds", examItemIds);
        params.put("examItemId", examItemIds[0]);
        Float scoreValue = commonRepository.selectOne("TeacherHomeworkMapper.getExamItemScore", params);
        if (scoreValue < Float.parseFloat(params.get("finallyScore").toString())
                || Double.parseDouble(params.get("finallyScore").toString()) < 0) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "给分不合理");
        }
        commonRepository.update("TeacherHomeworkMapper.updateExamItemFinallyScore", params);
    }


    /**
    * @Description: 获取批改记录
    * @Param:  examId paperId classId classId
    */
    public List<String> getRecordList(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("classId")
                .isNotBlank("questionNumber")
                .verify();

        String examId = params.get("examId").toString();
        String paperId = params.get("paperId").toString();
        String classId = params.get("classId").toString();
        String questionNumber = params.get("questionNumber").toString();

        Map<String,Object> examPaper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",examId);
        String cacheId = examPaper.get("examPaperId").toString()+":"+classId;
        String recordListName = JedisUtil.getKey("hkRecord",cacheId,questionNumber);
        return JedisTemplate.execute(jedis -> jedis.lrange(recordListName, 0, -1));
    }


    /**
     * @Description: 获取历史批改记录
     * @Param:  examId paperId classId classId
     */
    public Map<String,Object> getHistoryRecordList(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("classId")
                .isNotBlank("questionNumber")
                .verify();

        String examId = params.get("examId").toString();
        String paperId = params.get("paperId").toString();
        String classId = params.get("classId").toString();
        String questionNumber = params.get("questionNumber").toString();

        Map<String,Object> examPaper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",examId);
        String cacheId = examPaper.get("examPaperId").toString()+":"+classId;
        String recordListName = JedisUtil.getKey("hkHistoryRecord",cacheId,questionNumber);
        Long totalCount = JedisTemplate.execute(jedis -> jedis.zcard(recordListName));
        if (totalCount == null || totalCount == 0) {
            return MapUtil.of("totalCount", 0, "list", Collections.EMPTY_LIST);
        }

        long pageSize = ObjectUtil.isNumeric(params.get("pageSize")) ? Integer.parseInt(params.get("pageSize").toString()) : 0;
        long currentIndex = ObjectUtil.isNumeric(params.get("currentIndex")) ? Integer.parseInt(params.get("currentIndex").toString()) : 0;
        Set<String> historyRecordJsonStringList = JedisTemplate.execute(jedis -> jedis.zrange(recordListName, currentIndex, currentIndex + pageSize -1));

        List<Map<String, Object>> list = new ArrayList<>();
        historyRecordJsonStringList.forEach(a -> list.add(JSON.parseObject(a, Map.class)));

        return MapUtil.of("totalCount", totalCount, "list",list);
    }


    /**
     * 批改单个学生完成
     *
     * @param params examId paperId studentId classId correctMode examType
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateHomeworkResult(Map<String, Object> params) {
        //参数判断
        Verify.of(params).isValidId("examId").isValidId("paperId").isValidId("studentId").isValidId("classId").isNumeric("correctMode").verify();

        // 避免重复提交
        String examId = params.get("examId").toString();
        String paperId = params.get("paperId").toString();
        String studentId = params.get("studentId").toString();
        String lockKey = "distributed:homework:result:"+examId+":"+paperId+":"+studentId;
        JedisTemplate.lockExecute(lockKey,120,0,()-> {

            //判断学生是否还有未被批改的item
            int count = commonRepository.selectOne("TeacherHomeworkMapper.getUnReadItemCount", params);

            if (count == 0) {
                //更新examResult中的学生状态
                params.put("resultStatus", 0);
                params.put("studentIds", new Long[]{Long.valueOf(params.get("studentId").toString())});
                commonRepository.update("TeacherHomeworkMapper.updateExamResultStatus", params);
            }

            return null;
        });
    }

    public void executeComplete(Map<String,Object> params){
        //获取考试截止时间
        Map<String, Object> exam = commonRepository.selectOne("TeacherHomeworkMapper.getExamInfo", params);
        Date endDate = (Date) exam.get("endDate");

        int stu;
        if (examService.checkExamTypeOnline(exam)) {
            //线上作业已经到了截止时间,查询是否还有未批改学生，没有则调阅卷完成
            if (endDate.getTime() <= System.currentTimeMillis()) {
                stu = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkUnCorrect", params);
            } else {//作业未到截止时间，查询是否还有未提交与未批改的学生，没有则调阅卷完成
                stu = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkUnfinished", params);
            }
        }else {
            params.put("homeworkFinish", DictUtil.isEquals(MapUtils.getInteger(examService.getExamDetail(params), "examStatus"),
              "examStatus", "published"));
            stu = commonRepository.selectOne("TeacherHomeworkMapper.getOfflineHomeworkUnfinished", params);
        }

        params.put("currentTime", DateUtil.getCurrentDateTime());

        //如果没有  执行班级阅卷完成
        if (stu == 0) {
            params.put("classStatus", DictUtil.getDictValue("examPaperStatus", "readComplete"));
            commonRepository.update("TeacherHomeworkMapper.updateExamClass", params);
            commonRepository.update("TeacherHomeworkMapper.updateExamClassPaper", params);

            //如果所有班级都完成，则将整个考试都置为已完成
            Integer classCount = commonRepository.selectOne("TeacherHomeworkMapper.getExamClassCount", params);
            homeworkHandleService.readComplete(params, classCount);
            //清除该班级的待办
            Document docClass = new Document().append("displayContent.paperId", Long.valueOf(params.get("paperId").toString()))
                    .append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                    .append("displayContent.classId", Long.valueOf(params.get("classId").toString()));
            commonTodoService.deleteAllTodoTask(docClass);
            //如果该老师的所有班级都已完成，删除老师待办
            Map m = homeworkHandleService.getExamUnFinishClassCount(params);
            if (Integer.valueOf(m.get("unSubmitCount").toString())==0){
                Document docTeacher = new Document()
                        .append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                        .append("relativeId", Long.parseLong(m.get("teacherId").toString()));
                 commonTodoService.deleteAllTodoTask(docTeacher);
            }

        } else {
            //删除学生批改待办
            Document doc = new Document()
                    .append("todoType", DictUtil.getDictValue("todoType", "homeworkCorrectStu"))
                    .append("displayContent.studentId", Long.parseLong(params.get("studentId").toString()))
                    .append("displayContent.examId", Long.valueOf(params.get("examId").toString()));

            myAsyncConfigurer.getAsyncExecutor().execute(() -> commonTodoService.deleteAllTodoTask(doc));

            //生成学生查看作业代办
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> result = new HashMap<>();
            result.put("examId", params.get("examId"));
            result.put("classId", params.get("classId"));
            result.put("studentId", params.get("studentId"));
            result.put("paperId", params.get("paperId"));
            list.add(result);
            params.put("student", list);
            params.put("todoType", DictUtil.getDictValue("todoType", "homeworkViewStu"));
            myAsyncConfigurer.getAsyncExecutor().execute(() -> homeworkHandleService.createStudentHomeworkTodo(params));

        }
        //是否还有要批改的作业，没有的话，状态需要变更
        Integer currentCount = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkUnCorrect", params);
        if (currentCount == 0) {
            params.put("teacherUserId", exam.get("creatorId"));
            myAsyncConfigurer.getAsyncExecutor().execute(() -> HomeworkCorrectChangeProducer.send(params));
        }
    }

    /**
     * 批改所有试题完成（按小题改）
     *
     * @param params examId paperId correctMode
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateHomework(Map<String, Object> params) {
        //参数判断
        Verify.of(params).isValidId("examId").isValidId("paperId").isNotBlank("student").isNumeric("correctMode").verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());

        examMarkMonitorService.asynSaveMonitor(params,"开始执行批改所有试题完成（按小题改）");

        String examId = params.get("examId").toString();
        String paperId = params.get("paperId").toString();
        String lockKey = "distributed:homework:read:"+examId+":"+paperId;
        JedisTemplate.lockExecute(lockKey,120,0,()->{
            //examResultId判断是否还有item没有批改，如果没有更新examResultId
            List<Map<String, Object>> student = (List) params.get("student");
            List<Long> studentIds = new ArrayList<>();

            //还有未批改item的result
            List<Long> unReadStudentIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(student)) {
                unReadStudentIds = commonRepository.selectList("TeacherHomeworkMapper.getUnReadStudentId", params);
            }
            if (CollectionUtils.isNotEmpty(unReadStudentIds)) {
                for (Map<String, Object> stu : student) {
                    if (!unReadStudentIds.contains(Long.parseLong(stu.get("studentId").toString()))) {
                        studentIds.add(Long.parseLong(stu.get("studentId").toString()));
                    }
                }
            } else {
                for (Map<String, Object> stu : student) {
                    studentIds.add(Long.parseLong(stu.get("studentId").toString()));
                }
            }

            params.put("studentIds", studentIds);

            //如果有已经批改完的学生，则更新其它result状态
            if (studentIds.size() > 0) {
                //更新班级所有examResult的状态
                params.put("resultStatus", 0);
                commonRepository.update("TeacherHomeworkMapper.updateExamResultStatus", params);
            }

            return null;
        });

        examMarkMonitorService.asynSaveMonitor(params,"成功执行批改所有试题完成（按小题改）");

    }

    public void executeComplete2(Map<String,Object> params){

        List<Long> studentIds = (List) params.get("studentIds");

        params.put("currentTime", DateUtil.getCurrentDateTime());

        examMarkMonitorService.asynSaveMonitor(params,"开始执行executeComplete2");

        if(CollectionUtils.isNotEmpty(studentIds)){
            //获取考试截止时间
            Map<String, Object> exam = commonRepository.selectOne("TeacherHomeworkMapper.getExamInfo", params);
            Date endDate = (Date) exam.get("endDate");

            int stu;
            if (examService.checkExamTypeOnline(exam)) {
                //线上作业已经到了截止时间,查询是否还有未批改学生，没有则调阅卷完成
                if (endDate.getTime() <= System.currentTimeMillis()) {
                    stu = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkUnCorrect", params);
                } else {//作业未到截止时间，查询是否还有未提交与未批改的学生，没有则调阅卷完成
                    stu = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkUnfinished", params);
                }
            }else {
                params.put("homeworkFinish", DictUtil.isEquals(MapUtils.getInteger(examService.getExamDetail(params), "examStatus"),
                  "examStatus", "published"));
                stu = commonRepository.selectOne("TeacherHomeworkMapper.getOfflineHomeworkUnfinished", params);
            }

            //如果没有  执行班级阅卷完成
            if (stu == 0) {
                params.put("classStatus", DictUtil.getDictValue("examPaperStatus", "readComplete"));
                commonRepository.update("TeacherHomeworkMapper.updateExamClass", params);
                commonRepository.update("TeacherHomeworkMapper.updateExamClassPaper", params);

                //如果所有班级都完成，则将整个考试都置为已完成
                params.put("classStatus", DictUtil.getDictValue("examPaperStatus", "readComplete"));
                Integer classCount = commonRepository.selectOne("TeacherHomeworkMapper.getExamClassCount", params);
                homeworkHandleService.readComplete(params, classCount);

                Document docClass = new Document().append("displayContent.paperId", Long.valueOf(params.get("paperId").toString()))
                        .append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                        .append("displayContent.classId", Long.valueOf(params.get("classId").toString()));
                commonTodoService.deleteAllTodoTask(docClass);
                //如果该老师的所有班级都已完成
                Map m = homeworkHandleService.getExamUnFinishClassCount(params);
                if (Integer.valueOf(m.get("unSubmitCount").toString())==0){
                    Document docTeacher = new Document()
                            .append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                            .append("relativeId", Long.parseLong(m.get("teacherId").toString()));
                    commonTodoService.deleteAllTodoTask(docTeacher);
                }

            } else {
                List<Map<String, Object>> list = new ArrayList<>();
                Integer homeworkCorrectStu = DictUtil.getDictValue("todoType", "homeworkCorrectStu");
                for(Long id : studentIds){
                    //删除已经批改完成的学生的批改待办
                    Document doc = new Document()
                            .append("todoType", homeworkCorrectStu)
                            .append("displayContent.examId", Long.valueOf(params.get("examId").toString()))
                            .append("displayContent.studentId", id);
                    commonTodoService.deleteAllTodoTask(doc);
                    //生成学生查看作业代办
                    Map<String,Object> result = new HashMap<>();
                    result.put("examId",params.get("examId"));
                    result.put("classId",params.get("classId"));
                    result.put("studentId",id);
                    result.put("paperId",params.get("paperId"));
                    list.add(result);
                }
                params.put("student", list);
                params.put("todoType", DictUtil.getDictValue("todoType", "homeworkViewStu"));
                homeworkHandleService.createStudentHomeworkTodo(params);
            }
        }else {
            examMarkMonitorService.asynSaveMonitor(params,"错误执行executeComplete2，参数没有学生");
        }

        //是否还有要批改的作业，没有的话，状态需要变更
        Integer currentCount = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkAllUnCorrect", params);
        if(currentCount == 0){
            Map<String, Object> exam = commonRepository.selectOne("TeacherHomeworkMapper.getExamInfo", params);
            params.put("teacherUserId", exam.get("creatorId"));
            HomeworkCorrectChangeProducer.send(params);
        }

        examMarkMonitorService.asynSaveMonitor(params,"成功执行executeComplete2");
    }



    /**
     * 获取批改过的item
     * params classId examId paperId questionNumber
     *
     * @return item
     */
    public Map<String, Object> getResultItem(Map<String, Object> params) {
        //参数判断
        Verify.of(params).isValidId("examId").isValidId("paperId").isValidId("classId").isNumeric("questionNumber").verify();
        Map<String, Object> rs = new HashMap<>();
        // 数据操作
        int totalCount = commonRepository.selectOne("TeacherHomeworkMapper.getExamStudentItemCount", params);
        rs.put("totalCount", totalCount);
        if (totalCount == 0) {
            rs.put("item", new ArrayList<>());
            return rs;
        }
        List<Map<String, Object>> list = commonRepository.selectList("TeacherHomeworkMapper.getExamStudentItem", params);
        rs.put("item", list);
        return rs;
    }

    /**
     * 获取某一批改过的item
     * params examResultItemId
     *
     * @return item
     */
    public Map<String, Object> getCorrectedStudentItem(Map<String, Object> params) {
        //参数判断
        Verify.of(params)
                .isValidId("examItemId")
                .isValidId("examId")
                .verify();

        return commonRepository.selectOne("TeacherHomeworkMapper.getCorrectedStudentItem", params);
    }


    /**
     *
     * @param params examId
     * @return 1有批阅0没批阅
     */
    public Integer getCorrectStatus(Map<String,Object> params){
        //参数判断
        Verify.of(params)
                .isValidId("examId")
                .verify();
        Integer currentCount = commonRepository.selectOne("TeacherHomeworkMapper.getTeacherHomeworkUnCorrect", params);
        return currentCount>0?1:0;
    }


    /**
     * 班级概览
     * @param params
     * @return
     */
    public Map<String, Object> getClassOverview(Map<String, Object> params) {
        //参数判断
        Verify.of(params)
                .isValidId("examId")
                .verify();

        int userType = Integer.valueOf(params.get("userType").toString());
        List<Map<String, Object>> list = commonRepository.selectList("TeacherHomeworkMapper.getHomeworkClassInfo", params);
        Map<String,Object> result = new HashMap<>();
        StringBuilder notExistClass = new StringBuilder();
        for (Map<String, Object> l:list) {
            List<Map<String, Object>> classInfo = commonClassService.getClassInfo(MapUtil.of("classIds",l.get("classId")));
            if(CollectionUtils.isEmpty(classInfo)){
                notExistClass.append(l.get("className").toString()).append(",");
            }else {
                Map<String, Object> map = classInfo.get(0);
                l.put("headerId",map.get("headerId"));
            }
            List<Map<String, Object>> examCountClassInfo = commonRepository.selectList
                    ("TeacherHomeworkMapper.getExamCountClassInfo", l);
            Map<String, Object> examCountClass = examCountClassInfo.get(0);
            l.putAll(examCountClass);
        }
        if(notExistClass.length()>0){
            result.put("error", "请注意：此作业中的" + notExistClass + "这些班级在基础数据中已被删除");
        }

        //考试发布人
        if (CollectionUtils.isEmpty(list)){
            return new HashMap<>();
        }
        Map<String, Object> classInfo = list.get(0);
        Long creatorId = (Long) classInfo.get("creatorId");
        if (Long.valueOf(params.get("userId").toString()).equals(creatorId)){
            result.put("data",list);
            return result;
        }

        // 班主任和任课教师
        if (userType==DictUtil.getDictValue("userType","teacher" )){
            List<Map<String, Object>> rs = new ArrayList<>();
            Map<String, Object> user = commonUserService.getUserById(params);
            String teacherId = user.get("relativeId").toString();
            for (Map<String, Object> l:list) {
                String teacherId1 = Optional.ofNullable(l.get("teacherId")).orElse(StringUtils.EMPTY).toString();
                String headerId = Optional.ofNullable(l.get("headerId")).orElse(StringUtils.EMPTY).toString();
                if (StringUtils.equals(teacherId1, teacherId) || StringUtils.equals(headerId, teacherId)) {
                    rs.add(l);
                }
            }
            result.put("data",rs);
            return result;
        }
        //其他角色
        result.put("data",list);
        return result;
    }


    /**
     * 获取作答情况
     * params examId paperId classId
     *
     * @return result
     */
    public Map<String, Object> getExamStudentResult(Map<String, Object> params) {
        //参数判断
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();

        // 参数初始化
        if(ObjectUtil.isBlank(params.get("sortField"))){
            params.put("sortField","submitTime");
        }
        if(ObjectUtil.isBlank(params.get("sortType"))){
            params.put("sortType","DESC");
        }
        if(ObjectUtil.isBlank(params.get("resultStatus"))){
            params.remove("resultStatus");
        }else {
            params.put("resultStatus",params.get("resultStatus").toString().split(","));
        }

        Integer count = commonRepository.selectOne("TeacherHomeworkMapper.getExamStudentResultCount", params);
        Map<String, Object> rs = new HashMap<>();
        rs.put("totalCount",count);
        if(count==0){
            rs.put("list",new ArrayList<>());
            return rs;
        }
        //学校名称
        Map<String, Object> schoolName = commonRepository.selectOne("TeacherHomeworkMapper.getExamSchoolName", params);
        List<Map<String, Object>> list = commonRepository.selectList("TeacherHomeworkMapper.getExamStudentResult", params);
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(l->l.putAll(schoolName));
        }

        //未作答个数
        /*
        params.put("student",list);
        List<Map<String, Object>> item = commonRepository.selectList("TeacherHomeworkMapper.getExamUndoItem", params);
        Map<String, List<Map<String, Object>>> studentMap = item.stream().collect(groupingBy(i -> i.get("studentId").toString()));
        list.forEach(l->{
            l.putAll(schoolName);
            List<Map<String, Object>> ll = studentMap.get(l.get("studentId").toString());
            int sum = 0;
            if(CollectionUtils.isNotEmpty(ll)){
                for(Map<String, Object> m : ll){
                    if(ObjectUtil.isValueEquals(m.get("readType"),2) && ObjectUtil.isBlank(m.get("saveFileUrl"))){
                        sum++;
                    }else if(ObjectUtil.isValueEquals(m.get("readType"),1) && ObjectUtil.isBlank(m.get("recognitionValue"))){
                        sum++;
                    }
                }
            }
            l.put("unSubmitCount",sum);
        });
        */


        rs.put("list",list);
        return rs;
    }

    /**
     * 获取作业学生
     * params examId classId
     *
     * @return examStudent
     */
    public Map<String, Object> getExamStudent(Map<String, Object> params) {
        //参数判断
        Verify.of(params)
                .isValidId("examId")
                .verify();
        Integer count = commonRepository.selectOne("TeacherHomeworkMapper.getExamStudentCount", params);
        Map<String, Object> rs = new HashMap<>();
        rs.put("totalCount",count);
        if(count==0){
            rs.put("list",new ArrayList<>());
            return rs;
        }
        List<Map<String, Object>> list = commonRepository.selectList("TeacherHomeworkMapper.getExamStudent", params);
        rs.put("list",list);
        return rs;
    }

    /**
     * 添加作业学生
     * params examId  student
     *
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void insetExamStudent(Map<String, Object> params) {
        //参数判断
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .isValidId("courseId")
                .isValidId("paperId")
                .isNotEmptyCollections("students")
                .verify();
        Integer count = commonRepository.selectOne("TeacherHomeworkMapper.getExistExamStudentCount", params);
        if(count>0){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"学生已经存在作业中！");
        }

        examMarkMonitorService.asynSaveMonitor(params,"开始添加作业学生");

        params.put("currentTime",DateUtil.getCurrentDateTime());
        //exam_student
        commonRepository.insert("TeacherHomeworkMapper.insertExamStudent", params);
        //exam_result
        params.put("resultStatus",1);
        commonRepository.insert("TeacherHomeworkMapper.insertExamResult", params);
        //t_exam_item和阅卷的redis数据只有线上作业才会生成，线下作业不需要生成
        boolean isOnline = examService.checkExamTypeOnline(params);
        List<Map<String, Object>> student = (List<Map<String, Object>>) params.get("students");
        if (isOnline) {
            //exam_item
            List<Map<String, Object>> structure = ownExamPaperService.getPaperStructure(params);
            List<Map<String, Object>> item = new ArrayList<>();
            for (Map<String, Object> s : structure) {
                student.forEach(stu -> {
                    Map<String, Object> rs = new HashMap<>();
                    rs.putAll(s);
                    rs.putAll(stu);
                    item.add(rs);
                });
            }
            params.put("examItem", item);
            commonRepository.insert("TeacherHomeworkMapper.insertExamItem", params);
        }
        //生成学生，家长作答待办
        params.put("student",student);
        params.put("todoType", DictUtil.getDictValue("todoType", "homeworkDo"));
        homeworkHandleService.createStudentHomeworkTodo(params);

        if (isOnline) {
            //初始化学生redis数据
            Map<String,Object> p = new HashMap<>();
            p.put("examId",params.get("examId"));
            p.put("userId",params.get("userId"));
            p.put("userName",params.get("userName"));
            List<Long> studentIds = new ArrayList<>();
            for (Map<String,Object> stu : student){
                String studentId = stu.get("studentId").toString();
                studentIds.add(Long.valueOf(studentId));
            }
            p.put("studentIds",studentIds);
            homeworkPreparationService.executeExamMarkPreparation(p);
        }

        examMarkMonitorService.asynSaveMonitor(params,"成功添加作业学生");
    }

    /**
    * @Description: 判断班级阅卷是否完成
    * @Param:  examId classId
    */
    public boolean isExamClassReadComplete(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("classId")
                .isValidId("examId")
                .verify();

        Integer classStatus = commonRepository.selectOne("TeacherHomeworkMapper.isExamClassComplete", params);
        return classStatus != null && classStatus == 20;
    }

    /**
    * @Description: 获取班级待批改信息
    * @Param:  examId teacherId
    */
    public List<Map<String, Object>> getClassReadInfo(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("teacherId")
                .isValidId("examId")
                .verify();

        List<Long> examTeacherClassId = commonRepository.selectList("TeacherHomeworkMapper.getExamTeacherClassId", params);

        if (CollectionUtils.isEmpty(examTeacherClassId)) {
            return Collections.EMPTY_LIST;
        }
        params.put("classIdList", examTeacherClassId);
        return commonRepository.selectList("TeacherHomeworkMapper.getExamResultByClassIdAndExamId", params);
    }

    /**
    * @Description: 查看学生作答（主观题）
    * @Param:  examId studentId
    */
    public List<Map<String, Object>> getStudentExamResult(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("examId")
                .verify();

        return commonRepository.selectList("TeacherHomeworkMapper.getStudentExamResult", params);
    }

    /**
     * @Description: 新增批改记录list 维护上一题下一题的list
     */
    private void insertRecordList(String examItemIds,
                                  String examId,
                                  String paperId,
                                  String classId,
                                  String questionNumber,
                                  int expire ) {

        Map<String,Object> examPaper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",examId);
        String cacheId = examPaper.get("examPaperId").toString()+":"+classId;
        String recordListName = JedisUtil.getKey("hkRecord",cacheId,questionNumber);

        // 在历史批改中修改成绩，需要重新维护recordList
        List<String> newRecordList = new ArrayList<>();
        List<String> recordList = JedisTemplate.execute(jedis -> jedis.lrange(recordListName, 0, -1));
        recordList.forEach(a->{
            Set<String> set = new HashSet<>(Arrays.asList(a.split(",")));
            if (set.contains(examItemIds)) {
                set.remove(examItemIds);
                if (CollectionUtils.isNotEmpty(set)) {
                    String newExamItemIds = set.stream().collect(Collectors.joining(","));
                    newRecordList.add(newExamItemIds);
                }
            } else {
                newRecordList.add(a);
            }
        });

        newRecordList.add(0,examItemIds);
        Collections.reverse(newRecordList);
        String[] array = new String[newRecordList.size()];
        newRecordList.toArray(array);
        JedisTemplate.execute(jedis -> {
            jedis.del(recordListName);
            jedis.lpush(recordListName,array);
            return null;
        });
    }

    /**
    * @Description: 新增历史批改记录 维护历史批改记录
    */
    private void insertHistoryRecordList(List<Map<String,Object>> examItemInfo,
                                         String examId,
                                         String paperId,
                                         String classId,
                                         String questionNumber,
                                         int expire) {

        Map<String,Object> examPaper = commonRepository.selectOne("TeacherHomeworkMapper.getHomeworkPaper",examId);
        String cacheId = examPaper.get("examPaperId").toString()+":"+classId;
        String historyRecordListName = JedisUtil.getKey("hkHistoryRecord",cacheId,questionNumber);

        examItemInfo.forEach(map->{
            double studentId = Double.parseDouble(map.get("studentId").toString());
            JedisTemplate.execute(jedis -> {
                jedis.zremrangeByScore(historyRecordListName, studentId, studentId);
                jedis.zadd(historyRecordListName,studentId,JSON.toJSONString(map));
                return null;
            });
        });
    }

}
