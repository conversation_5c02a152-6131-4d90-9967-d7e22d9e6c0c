package com.dongni.exam.homework.service;

import com.dongni.exam.homework.bean.req.*;
import com.dongni.exam.homework.bean.res.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 * @desc
 */
public interface IHomeworkMarkService {


    /**
     * 获取作业提交状态
     */
    ClsSubmitStatusRes getSubmitStatus(ClsSubmitReq clsSubmitReq);

    /**
     * 获取作业批阅进度
     */
    List<HomeWorkMarkQnRes> getQnMarkProcess(QnProcessReq qnProcessReq);

    /**
     * 获取要批阅的小题列表
     */
    List<ExamItemRes>  getMarkItems(MarkItemReq markItemReq);

    /**
     * 批阅作业
     */
    void submitItemScores(SubmitItemReq submitItemReq);

    /**
     * 获取批阅记录
     */
    MarkHistoryRes readHistory(ReadHistoryReq readHistoryReq);

    /**
     * 获取上一份/下一份批阅记录
     */
    ItemRecordRes getPreOrNextRecord(PreRecordReq preRecordReq);

    /**
     * 获取学生成绩
     */
    StuScoreRes pageStuScore(StuScoreReq stuScoreReq);
}
