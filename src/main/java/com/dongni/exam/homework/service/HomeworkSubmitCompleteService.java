package com.dongni.exam.homework.service;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.homework.service.handle.HomeworkSubmitHandle;
import com.dongni.exam.mark.service.ExamMarkMonitorService;
import com.dongni.exam.mark.service.ExamUpdateAnswerService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.tiku.common.enumeration.PaperAnswerStatus;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.*;


/**
 * Created by Heweipo on 2020/3/6.
 *
 * 学生提交作业、老师终止提交
 * 更新已完成的item数据
 */
@Service
public class HomeworkSubmitCompleteService {

    // 日志记录
    private static final Logger log = LogManager.getLogger(HomeworkSubmitCompleteService.class);
    // 每次获取个数
    private static final int count = 500;
    // 临时名称分割符号
    private static final String splitStr = "-";

    @Autowired
    private ExamMarkMonitorService monitorService;
    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private HomeworkCommonService homeworkCommonService;
    @Autowired
    private HomeworkSubmitHandle homeworkSubmitHandle;
    @Autowired
    private ExamService examService;
    @Autowired
    private ExamUpdateAnswerService examUpdateAnswerService;
    @Autowired
    private PaperManager paperManager;

    /**
     * 提交作业
     *
     * @param params examResultId examId  paperId studentId
     */
    @Transactional(ExamRepository.TRANSACTION)
    public Map<String, Object> updateExamResult(Map<String, Object> params) {
        Verify.of(params).isValidId("examResultId").isValidId("examId").isValidId("paperId").isValidId("studentId").verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        String cacheId = homeworkCommonService.getCacheId(params);
        String studentId = params.get("studentId").toString();

        // 是否已经提交
        String hkStudentUnSubmitKey = JedisUtil.getKey("hkStudentUnSubmit",cacheId);
        String hkStudentSubmitKey = JedisUtil.getKey("hkStudentSubmit",cacheId);
        boolean isSubmit = JedisTemplate.execute(jedis -> jedis.sismember(hkStudentSubmitKey,studentId));
        if(isSubmit){
            log.info("学生{}在作业{}中已经提交过了，不能重复提交",studentId,cacheId);
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "作业已提交,不能重复提交");
        }

        // 是否已经停止作答
        if(homeworkSubmitHandle.isEndSubmit(params)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"作业已经截止作答");
        }

        // 数据库校验是否已经提交
        Map<String, Object> result = examRepository.selectOne("HomeworkSubmitCompleteMapper.getExamResult", params);
        if (MapUtils.isEmpty(result)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "数据不存在或已被删除");
        }
        if (!ObjectUtil.isValueEquals("1", result.get("resultStatus"))){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "作业已提交,不能重复提交");
        }

        long r = JedisTemplate.execute(jedis -> jedis.smove(hkStudentUnSubmitKey,hkStudentSubmitKey,studentId));
        if(r == 0){
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE,"请勿重复提交");
        }

        try{
            submitExamResult(params, cacheId, studentId);
            log.info("学生{}提交作业{}成功",studentId,cacheId);
            result.put("userId",params.get("userId"));
            result.put("userName",params.get("userName"));
            result.put("currentTime",params.get("currentTime"));
        }catch (Exception e){
            JedisTemplate.execute(jedis -> jedis.smove(hkStudentSubmitKey,hkStudentUnSubmitKey,studentId));
            log.error("学生{}提交作业{}失败：{}",studentId,cacheId,e.getMessage(),e);
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,"作业提交报错，请稍后提交");
        }

        return result;
    }

    private void submitExamResult(Map<String, Object> params,
                                                 String cacheId,
                                                 String studentId) {
        // 提交作答，这个接口返回值不准确，切勿使用
        updateSubmitItemFromRedis(params);

        // 从 redis 判断哪些未作答，是否需要老师批改
        int objective = DictUtil.getDictValue("readType","objective");
        List<String> unSubmitItems = new ArrayList<>();
        boolean hasReadTodo = JedisTemplate.execute(jedis -> {
            boolean hasSubmitSubjective = false;
            Set<String> itemIds = jedis.smembers(JedisUtil.getKey("hkStudentItem",cacheId,studentId));
            for (String itemId : itemIds){
                Map<String,String> item = jedis.hgetAll(JedisUtil.getKey("hkItem",cacheId,itemId));
                int readType = Integer.parseInt(item.get("readType"));
                if(readType == objective){
                    if(ObjectUtil.isBlank(item.get("recognitionValue"))){
                        unSubmitItems.add(itemId);
                    }
                }else {
                    if(ObjectUtil.isBlank(item.get("saveFileUrl"))){
                        unSubmitItems.add(itemId);
                    }else {
                        hasSubmitSubjective = true;
                    }
                }
            }
            return hasSubmitSubjective;
        });

        // 确认未提交，那么更新为 0 分和已批改
        if(CollectionUtils.isNotEmpty(unSubmitItems)){
            params.put("itemIds",unSubmitItems);
            examRepository.update("HomeworkSubmitCompleteMapper.updateUnSubmitItemReadStatus", params);
        }

        // 正常完成，无需阅卷
        params.put("resultStatus", 0);
        if (hasReadTodo) { // 需要阅卷
            params.put("resultStatus", 2);
        }
        params.put("currentTime", DateUtil.getCurrentDateTime());
        examRepository.update("HomeworkSubmitCompleteMapper.updateExamResult", params);
    }


    /**
     * 更新已作答的item
     * @param params examId classId studentId
     * @return 被更新的 item 列表
     */
    @Transactional(transactionManager = ExamRepository.TRANSACTION,propagation = Propagation.REQUIRED)
    public List<Map<String,String>> updateSubmitItemFromRedis(Map<String,Object> params){

        Verify.of(params).isValidId("examId").verify();

        Map<String,Object> exam = examRepository.selectOne("HomeworkSubmitCompleteMapper.getExam", params);
        if(MapUtils.isEmpty(exam)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"数据不存在或已被删除");
        }
        if (examService.isNotHomework(exam)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"只允许作业操作");
        }

        monitorService.asynSaveMonitor(params,"开始执行更新已作答的item");
        int objective = DictUtil.getDictValue("readType","objective");
        String paperId = exam.get("paperId").toString();
        String examPaperId = exam.get("examPaperId").toString();

        // 查询班级
        params.put("paperId",paperId);
        params.put("examPaperId",examPaperId);
        List<Map<String,Object>> cs = examRepository.selectList("HomeworkSubmitCompleteMapper.getExamClass",params);
        if(CollectionUtils.isEmpty(cs)){
            log.info("没有找到对应的班级数据，程序退出");
            monitorService.asynSaveMonitor(params,"执行更新已作答的item时,未找到合适的班级,程序退出");
            return null;
        }

        // 是否操作单个学生
        boolean isStudent = ObjectUtil.isValidId(params.get("studentId"));
        String suffix = getUniqueTempSuffix();
        List<String> tempDoneKeys = new ArrayList<>();
        List<Map<String,String>> rs = new ArrayList<>();
        Map<String,Map<String,String>> qnMap = new HashMap<>();

        Map<String,Object> msg = new HashMap<>();
        msg.put("tempDoneKeys",tempDoneKeys);
        params.putAll(msg);
        params.put("suffix",suffix);

        // 返回待更新的 itemId 总数
        int sum = JedisTemplate.execute(jedis -> {
            Set<String> keys = new HashSet<>();
            for (Map<String,Object> c : cs){
                String cacheId = examPaperId+":"+c.get("classId");

                // 补充试题信息
                if(MapUtils.isEmpty(qnMap)){
                    String qns = jedis.hget(JedisUtil.getKey("hkTrace",cacheId),"qns");
                    if(ObjectUtil.isBlank(qns)){
                        continue;
                    }
                    for (String qn : qns.split(",")){
                        qnMap.put(qn,jedis.hgetAll(JedisUtil.getKey("hkQuestionTrace",cacheId,qn)));
                    }
                }

                // 获取作答的
                String hkSubmitItemDoneFather = JedisUtil.getKey("hkSubmitItemDone",cacheId);
                String hkSubmitItemDoneSon = JedisUtil.getKey("hkSubmitItemDone",cacheId)+suffix;
                tempDoneKeys.add(hkSubmitItemDoneSon);

                // 获取待更新的 itemId
                Set<String> itemIds;
                if(isStudent){
                    String hkStudentItemKey = JedisUtil.getKey("hkStudentItem",cacheId,params.get("studentId").toString());
                    itemIds = getItemByStudent(jedis,hkStudentItemKey);
                }else {
                    itemIds = getItemByClass(jedis, hkSubmitItemDoneFather);
                }

                for (String itemId : itemIds){
                    if(jedis.smove(hkSubmitItemDoneFather,hkSubmitItemDoneSon,itemId) == 1){
                        keys.add(JedisUtil.getKey("hkItem",cacheId,itemId));
                    }else {
                        log.info("暂未提交{}，所以不加入",itemId);
                    }
                }
            }

            if(CollectionUtils.isEmpty(keys)){
                return 0;
            }

            for (String key : keys){
                Map<String,String> r = jedis.hgetAll(key);
                if(MapUtils.isNotEmpty(r)){
                    rs.add(r);
                }else {
                    log.error("未获取到已作答的{}详细信息",key);
                    msg.put("notExist",msg.getOrDefault("notExist","").toString()+","+key);
                }
            }

            return keys.size();
        });

        if(sum == 0){
            log.info("未获取到{}已作答的item，程序退出",examPaperId);
            monitorService.asynSaveMonitor(params,"执行更新已作答的item时,未获取到已作答的item");
            return null;
        }

        // 验证数据
        if(CollectionUtils.isEmpty(rs)){
            log.info("虽获取到{}已作答的item，但没有有效的数据，程序退出",examPaperId);
            params.putAll(msg);
            monitorService.asynSaveMonitor(params,"执行更新已作答的item时,未获取到已作答的有效item");
            return null;
        }

        // 验证数据
        String currentTime = DateUtil.getCurrentDateTime();
        List<Map<String,String>> insertList = new ArrayList<>();
        for (Map<String,String> item : rs){
            int readType = Integer.parseInt(item.get("readType"));
            if(readType == objective){
                if(ObjectUtil.isBlank(item.get("recognitionValue"))){
                    log.error("客观题未作答：{}",item);
                    msg.put("unSubmit",msg.getOrDefault("unSubmitObjective","").toString()+","+item.get("examItemId"));
                    continue;
                }
            }else {
                if(ObjectUtil.isBlank(item.get("saveFileUrl"))){
                    log.error("主观题未作答：{}",item);
                    msg.put("unSubmit",msg.getOrDefault("unSubmitSubjective","").toString()+","+item.get("examItemId"));
                    continue;
                }
            }

            // 时间信息补全
            if(ObjectUtil.isNumeric(item.get("currentTime"))){
                item.put("currentTime", DateUtil.formatDateTime(new Date(Long.valueOf(item.get("currentTime")))));
            }else {
                item.put("currentTime",currentTime);
            }
            item.putIfAbsent("userId",params.get("userId").toString());
            item.putIfAbsent("userName",params.get("userName").toString());

            insertList.add(item);
        }

        // 验证数据
        if(CollectionUtils.isEmpty(insertList)){
            log.info("虽获取到{}已作答的item，但作答数据不完整，程序退出",examPaperId);
            monitorService.asynSaveMonitor(params,"执行更新已作答的item时,作答的item数据不完整");
            JedisTemplate.executePipeline(pipeline -> tempDoneKeys.forEach(pipeline::del));
            return null;
        }

        // 执行更新
        try{
            updateSubmitItem(MapUtils.getLong(params, "examId"), Long.parseLong(paperId), insertList, params);
            monitorService.asynSaveMonitor(params,"成功执行更新已作答的item，获取到"+sum+"个，实际更新"+insertList.size()+"个");
            JedisTemplate.executePipeline(pipeline -> tempDoneKeys.forEach(pipeline::del));
        }catch (Exception e){
            log.error("失败执行更新已作答的item,item有：{},报错：{}",insertList,e.getMessage(),e);
            JedisTemplate.execute(jedis -> {
                for (String key : tempDoneKeys){
                    String hk = removeTempSuffix(key);
                    Set<String> ms = jedis.smembers(key);
                    jedis.sadd(hk,ms.toArray(new String[]{}));
                    jedis.del(key);
                }
                return null;
            });
            params.put("insertList",insertList);
            monitorService.asynSaveMonitor(params,"更新已作答的item 失败，报错："+e.getMessage());
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,"提交失败，请勿关闭页面，稍后提交");
        }

        return insertList;
    }

    /**
     * 获取临时文件唯一后缀
     * @return 一后缀字符串
     */
    private String getUniqueTempSuffix(){
        return splitStr+"temp_"+Thread.currentThread().getId()+"_"+System.currentTimeMillis()+"_"+RandomUtils.nextInt(1000,10000);
    }

    /**
     * 去掉临时文件后缀
     */
    private String removeTempSuffix(String str){
        return str.substring(0,str.lastIndexOf(splitStr));
    }

    /**
     * 计算客观题得分以及更新已作答item的 saveFileUrl recognitionValue finallyScore modify
     */
    public void updateSubmitItem(Long examId, Long paperId, List<Map<String, String>> insertList, Map<String,Object> userInfo) {
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }

        // 临时表会出现  the --read-only option so it cannot execute this statement
//        Map<String,Object> params = new HashMap<>();
//        params.put("suffix",getUniqueTempSuffix());
//        examRepository.insert("HomeworkSubmitCompleteMapper.createTempTable",params);
//        params.put("list",insertList);
//        examRepository.batchInsert("HomeworkSubmitCompleteMapper.insertSubmitItem",params);
//        examRepository.update("HomeworkSubmitCompleteMapper.updateSubmitItem",params);
//        examRepository.delete("HomeworkSubmitCompleteMapper.dropTemporarySubmitItem",params);

        // 采用 replace into 的方式
        Map<String,Object> params = new HashMap<>();
        String uuid = UUID.randomUUID().toString().replace("-","");
        params.put("uuid",uuid);
        params.put("list",insertList);
        examRepository.batchInsert("HomeworkSubmitCompleteMapper.replaceSubmitItem",params);
        examRepository.update("HomeworkSubmitCompleteMapper.updateExamItemByUUID",params);
        Document paper = paperManager.getPaperSimple(paperId);
        if (!ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS.getStatus())) {
            return;
        }
        Map<String, Object> map = MapUtil.of("examId", examId, "paperId", paperId);
        map.put("userId", userInfo.getOrDefault("userId", 1));
        map.put("userName", userInfo.getOrDefault("userName", "admin"));
        List<Long> studentIds = insertList.stream().map(x -> MapUtils.getLong(x, "studentId")).distinct().collect(Collectors.toList());
        map.put("studentIds", studentIds);
        map.put("readStatus", DictUtil.getDictValue("readStatus", "finished"));
        examUpdateAnswerService.updateExamItemScore(map, examUpdateAnswerService.getKey2UpdateParams(examId, paperId));
        // examRepository.delete("HomeworkSubmitCompleteMapper.deleteExamItemByUUID",params);
    }

    /**
     * 获取学生所有的 item
     */
    private Set<String> getItemByStudent(Jedis jedis, String hkStudentItemKey) {
        return jedis.smembers(hkStudentItemKey);
    }

    /**
     * 获取班级的 item
     */
    private Set<String> getItemByClass(Jedis jedis, String hkSubmitItemDoneFather) {
        Set<String> itemIds = new HashSet<>();

        ScanParams scanParams = new ScanParams().count(count);
        String cur = ScanParams.SCAN_POINTER_START;
        boolean cycleIsFinished = false;
        while(!cycleIsFinished) {
            ScanResult<String> scanResult = jedis.sscan(hkSubmitItemDoneFather,cur, scanParams);
            List<String> result = scanResult.getResult();
            cur = scanResult.getStringCursor();
            if (cur.equals("0")) {
                cycleIsFinished = true;
            }
            itemIds.addAll(result);
        }
        return itemIds;
    }


}
