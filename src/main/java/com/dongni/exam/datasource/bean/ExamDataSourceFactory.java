package com.dongni.exam.datasource.bean;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.exam.datasource.service.IExamDataSourceService;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: hzw
 * @date: 2024/1/31
 * @description: 考试数据源工厂
 */
@Component
public class ExamDataSourceFactory {

	@Autowired
	private List<IExamDataSourceService> examStudentDataSourceServiceList;

	/**
	 * 数据源
	 */
	private static Map<Integer, IExamDataSourceService> DATA_SOURCE = Maps.newHashMap();

	@PostConstruct
	public void init(){
		DATA_SOURCE = examStudentDataSourceServiceList.stream()
			.collect(Collectors.toMap(x -> x.getDateSource().getType(), Function.identity()));
	}

	public static IExamDataSourceService getInstance(int type) {
		return Optional.ofNullable(DATA_SOURCE.get(type))
			.orElseThrow(() -> new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未知的数据源类型"));
	}
}
