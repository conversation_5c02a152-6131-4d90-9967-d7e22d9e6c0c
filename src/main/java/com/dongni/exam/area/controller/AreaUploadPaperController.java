package com.dongni.exam.area.controller;

import com.dongni.commons.annotation.DongniAuth;
import com.dongni.commons.annotation.DongniNotCheckUserInfo;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.area.service.AreaUploadPaperService;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * Created by scott
 * time: 15:06 2019/8/1
 * description:区域题库 子系统上传
 */
@RestController
@RequestMapping("/exam/area/upload")
public class AreaUploadPaperController extends BaseController {


    @Autowired
    private AreaUploadPaperService areaAdminService;

    /**
     * 新增子系统上传  系统间内部调用
     * params paperName examName source  questionCount fullMark paperStatus  sendTime paper
     *
     * @return 结果
     */
    @PostMapping
    public Response insertAreaUploadPaper() {
        areaAdminService.insertAreaUploadPaper(getParameterMap());
        return new Response();
    }

    /**
     * 新增子系统上传  系统间内部调用
     * params paperName examName source  questionCount fullMark paperStatus  sendTime paper
     *
     * @return 结果
     */
    @PostMapping("system")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response insertAreaUploadPaperForAuth() {
        areaAdminService.insertAreaUploadPaper(getParameterMap());
        return new Response();
    }

    /**
     * 获取子系统上传
     * params userId userName
     *
     * @return 结果
     */
    @GetMapping
    public Response getAreaUploadPaper() {
        return new Response(areaAdminService.getAreaUploadPaper(getParameterMap()));
    }

    /**
     * 非GET/POST治理 考试
     * 更新子系统上传
     * params userId userName areaUploadPaperId paperStatus
     *
     * @return 结果
     */
    @PutMapping
    @Deprecated
    public Response updateAreaUploadPaper() {
        areaAdminService.updateAreaUploadPaper(getParameterMap());
        return new Response();
    }

    /**
     * 更新子系统上传
     * params userId userName areaUploadPaperId paperStatus
     *
     * @return 结果
     */
    @PostMapping("update")
    public Response updateAreaUploadPaper1() {
        areaAdminService.updateAreaUploadPaper(getParameterMap());
        return new Response();
    }

    /**
     * 获取子系统上传试卷详情
     * params userId userName paperId
     *
     * @return 结果
     */
    @GetMapping("/paper")
    public Response getAreaUploadPaperDetail() {
        return new Response(areaAdminService.getAreaUploadPaperDetail(getParameterMap()));
    }

    /**
     * 获取子系统上传试卷试题
     * params userId userName paperId
     *
     * @return 结果
     */
    @GetMapping("/question")
    public Response getAreaUploadPaperQuestion() {
        return new Response(areaAdminService.getAreaUploadPaperQuestion(getParameterMap()));
    }

}
