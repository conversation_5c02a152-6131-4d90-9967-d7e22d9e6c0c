package com.dongni.exam.declaration.absent.controller;

import com.dongni.commons.entity.Response;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.declaration.absent.service.ExamAbsentDeclarationStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2021/05/26 <br/>
 *
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/declaration/absent/student")
public class ExamAbsentDeclarationStudentController {
    
    @Autowired
    private ExamAbsentDeclarationStudentService examAbsentDeclarationStudentService;
    
    /**
     * 获取缺考学生清单
     * 教育局长用的
     * @param params examId
     *               [absentDeclareStudent4AllStatus] 未申报 未提交 未审核 审核不通过 审核通过
     *               分页支持
     * @return totalCount
     *         list []
     *             studentId studentName studentNum classId className schoolId schoolName
     *             absentDeclareStudent4AllStatus 1未申报 2未提交 3未审核 4审核不通过 5审核通过
     *             courseList: [{courseId courseName}]
     *             [absentDeclarationId declareStatus absentDeclarationStudentId studentDeclareStatus]
     */
    @GetMapping("/exam/absent/list")
    public Response getStudentWithDeclareInfoList(Map<String, Object> params) {
        return new Response(examAbsentDeclarationStudentService.getStudentWithDeclareInfoList(params));
    }
    
    /**
     * 获取申报学生列表
     *
     * @param params [absentDeclarationId] 缺考申报id 教务必须携带
     *               教育局长可以不携带，不携带自己查审核中最先创建的申报
     * @return absentDeclarationId examId examName schoolId schoolName declareStatus申报状态
     *         studentCount
     *         studentList: [{}]缺考学生列表 分页
     *             studentId studentName studentNum classId className
     *             studentDeclareStatus 申报状态 未填写 已填写 未审核 审核不通过 审核通过
     *             courseList: [{absentDeclarationCourseId courseId courseName}] 申报的缺考科目
     */
    @GetMapping("/list")
    public Response getDeclareStudentList(Map<String, Object> params) {
        return new Response(examAbsentDeclarationStudentService.getDeclareStudentList(params));
    }
    
    /**
     * 获取学生详情
     *     接getStudentListForDeclare
     * @param params absentDeclarationId 缺考申报id
     *               [studentId]         如果不提供 获取未审核的随机一个
     * @return studentId studentName studentNum
     *         schoolId schoolName
     *         classId className
     *         [absentDeclarationId] 申报表id 仅限本单 如果本单已经有该学生了，会返回该字段
     *         courseList: [] 考试的所有科目都要返回
     *             courseId courseName
     *             [absentDeclarationId absentDeclarationCreatorId absentDeclarationCreatorName]
     *             如果已经在申报单中的话
     *             如果absentDeclarationId与当前操作的不一致，则证明在别的申报表中，置灰加浮窗提示
     *             [absentDeclarationCourseId] 如果已经在申报课程(本单/其他单)的话 如果是本单前端要勾选
     *             examResultStatus  是否缺考 非缺考的前端要标红
     *         [absentReason]            缺考原因 如果已经存在于本单会有
     *         [studentDeclareStatus]    审核状态 如果已经存在于本单会有
     *         [reviewComment]           审核原因 如果已经存在于本单会有
     *         [certificationList]: [{}] 佐证材料 如果已经存在于本单会有
     *             absentDeclarationCertificationId  材料id
     *             fileName       材料名
     *             fileUrl        材料路径
     *             createDateTime 创建时间
     */
    @GetMapping("/detail")
    public Response getStudentDetail(Map<String, Object> params) {
        return new Response(examAbsentDeclarationStudentService.getStudentDetail(params));
    }
    
    /**
     * 新建 申报学生信息
     * 保存 申报学生信息
     * 科目 缺考原因 佐证材料
     * 已经审核通过的做更改需要将审核标志去除 相当于删除该学生后又添加该学生
     *
     * @param params absentDeclarationId 缺考申报id
     *               studentId
     *               declareCourseList: []
     *                   courseId courseName
     *               absentReason
     *               certificationList: []
     *                   [absentDeclarationCertificationId] 已经存在的会有 新上传的不会有
     *                                                      记得与数据库里的对比，因为可能被操作删除了
     *                   fileName
     *                   fileUrl
     */
    @PostMapping("")
    public Response saveDeclareStudent(Map<String, Object> params) {
        examAbsentDeclarationStudentService.saveDeclareStudent(params);
        return new Response();
    }
    
    /**
     * 删除申报学生
     *     需要判断是否可以删除
     * @param params absentDeclarationId 缺考申报id
     *               absentDeclarationStudentIdList 缺考申报学生id [1,2,3,4]
     */
    @PostMapping("/delete/list")
    public Response deleteDeclareStudentList(Map<String, Object> params) {
        examAbsentDeclarationStudentService.deleteDeclareStudentList(params);
        return new Response();
    }
    
    /**
     * 审核 申报学生
     * @param params absentDeclarationId 缺考申报id
     *               absentDeclarationStudentId: 缺考申报学生id
     *               studentId
     *               studentDeclareStatus  通过/不通过
     *               [reviewComment] 不通过要给
     */
    @PostMapping("/review")
    public Response reviewDeclareStudent(Map<String, Object> params) {
        examAbsentDeclarationStudentService.reviewDeclareStudent(params);
        return new Response();
    }
}
