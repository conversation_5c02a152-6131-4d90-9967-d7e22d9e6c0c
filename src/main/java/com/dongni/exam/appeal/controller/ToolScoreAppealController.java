package com.dongni.exam.appeal.controller;

import com.dongni.commons.entity.Response;
import com.dongni.exam.appeal.service.ToolScoreAppealService;
import com.dongni.exam.config.ExamConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @ Author     ：guo zhengming
 * @ Date       ：Created in 20:08 2020/1/15
 * @ Description：成绩申诉(考试工具箱)
 * @ Modified By：
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/tool/score/appeal")
public class ToolScoreAppealController {
    @Autowired
    private ToolScoreAppealService toolScoreAppealService;

    /**
     * 默认开关为关闭(mongodb exam表中)
     * @param params
     *  - examId  考试id
     * @return
     *
     */
    @PostMapping("set/switch")
    public Response setToolSwitch(Map<String, Object> params) {
        return new Response(toolScoreAppealService.setToolSwitch(params));
    }

    /**
     * 查询成绩申诉的列表
     * @param params
     * @return
     */
    @GetMapping("/info")
    public Response getAppealInfo(Map<String, Object> params) {
        return new Response(toolScoreAppealService.getAppealInfo(params));
    }

    /**
     * 设置成绩申诉状态
     * @param params
     * @return
     */
    @PostMapping("set/status")
    public Response setStatus(Map<String, Object> params) {
        toolScoreAppealService.setStatus(params);
        return new Response();
    }

    /**
     * 设置成绩申诉状态
     * @param params
     * @return
     */
    @PostMapping("status")
    public Response setStatusByInfo(Map<String, Object> params) {
        toolScoreAppealService.setStatusByInfo(params);
        return new Response();
    }

    /**
     * 微信答题卡信息查看时，查询是否打开了成绩申诉，如果打开了，查询该同学的成绩申诉
     * @param params
     * @return
     */
    @GetMapping("/get/appealStatus")
    public Response getAppealStatus(Map<String, Object> params) {
        return new Response(toolScoreAppealService.getAppealStatus(params));
    }

    /**
     * 增加成绩申诉
     * @param params
     * @return
     */
    @PostMapping("/add/appeal")
    public Response addAppeal(Map<String, Object> params) {
        toolScoreAppealService.addAppeal(params);
        return new Response();
    }

    /**
     * 删除成绩申诉
     * @param params
     * @return
     */
    @PostMapping("")
    public Response delAppeal(Map<String, Object> params) {
        toolScoreAppealService.delAppeal(params);
        return new Response();
    }

}
