package com.dongni.exam.plan.dao;

import com.dongni.exam.materials.bean.VO.ExpEduMaterialsVO;
import com.dongni.exam.newcard.bean.DTO.CardTemplate;
import com.dongni.exam.plan.bean.bo.ExamCourseBO;
import com.dongni.exam.plan.bean.bo.ExamUploaderExpBO;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.bean.dto.ExamUploaderDTO;
import com.dongni.exam.plan.bean.dto.ExamUploaderScanStatusDTO;
import com.dongni.exam.plan.bean.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
public interface NewExamUploaderDao {
    ExamUploaderVO getMinUploadStatusExamUploaderByUploader(ExamUploaderDTO dto);

    void insertExamUploader(ExamUploaderDTO dto);

    ExamUploaderVO getNormalExamUploaderByScanner(ExamUploaderDTO dto);

    void updateExamUploaderOfAnswerCardPath(ExamUploaderVO vo);

    NewExamUploaderBO getExamUploaderByExamUploaderId(@Param("examUploaderId") Long examUploaderId);

    int getExamUploaderCountByExamIdAndUploaderId(@Param("examId") long examId, @Param("uploaderId") long uploaderId);

    List<ExamUploaderProgressVO> getExamUploaderByGetUploaderProgressVO(GetUploaderProgressVO getUploaderProgressVO);

    List<ExamUploaderProgressVO> getExamUploaderProgressList(@Param("list") List<ExamUploaderProgressVO> resultList,
                                                             @Param("examId") long examId);

    List<PaperCardVO> getUploaderStats(@Param("examId") long examId, @Param("courseId") long courseId, @Param("uploaderId") long uploaderId);

    UploaderStatsVO getUploaderStatsByCareSchools(@Param("examId") long examId,
                                                  @Param("courseId") long courseId,
                                                  @Param("uploaderId") long uploaderId);

    List<NewExamUploaderVO> getCompletedExamUploaderGroupByUploaderId(GetUploaderStatusVO getUploaderStatusVO);

    List<NewExamUploaderVO> getCompletedExamUploaderGroupBySchoolId(GetUploaderStatusVO getUploaderStatusVO);

    int getCompletedExamUploaderGroupByUploaderIdCount(GetUploaderStatusVO getUploaderStatusVO);

    int getCompletedExamUploaderGroupBySchoolIdCount(GetUploaderStatusVO getUploaderStatusVO);

    List<ExamUploaderDTO> getExamUploaderListByUploaderId(@Param("examId") long examId,
                                                          @Param("paperId") long paperId,
                                                          @Param("uploaderId") long uploaderId);

    List<ExamUploaderDTO> getExamUploaderListBySchoolId(long examId, long paperId, long schoolId);

    void deleteExamUploaderByIds(@Param("idList") List<Long> idList);

    void updateUploadStatus(@Param("examUploaderId") long examUploaderId);

    List<ExamUploaderVO> getProcessingUploaderList(ExamPaperVO examPaperVO);

    List<ExamUploaderVO> getExamUploaderList(ExamPaperVO examPaperVO);

    List<ExamUploaderCoursePaperVO> getExamUploaderByUploader(@Param("examId") long examId,
                                                              @Param("uploaderId") long uploaderId);

    List<Long> getProcessExamUploaderIds(@Param("examId") long examId, @Param("paperId") long paperId);

    int getCompletedExamUploaderCount(@Param("examId") long examId, @Param("paperId") long paperId);

    List<ExamUploaderScanStatusDTO> getSchoolScanStatusStatistic(ExamCourseBO examCourseBO);

    List<ExamUploaderScanStatusDTO> getClassScanStatusStatistic(ExamCourseBO examCourseBO);

    List<NewExamUploaderBO> getClassExamUploaderByExamIdAndPaperIds(@Param("examId") long examId, @Param("paperIds") List<Long> paperIds);

    List<Long> getProcessingExamUploaderIdsIn5Days();

    List<NewExamUploaderBO> getExamUploaderListByIds(@Param("examUploaderIds") Set<Long> examUploaderIds);

    List<ExamUploaderProgressVO> getUploaderExamUploaderList(@Param("list") List<ExamUploaderProgressVO> resultList, @Param("examId") long examId);

    void batchUpdateTemplates(@Param("cardTemplates") List<CardTemplate> items);

    List<NewExamUploaderVO> getHistoryExamUploaders(@Param("examUploaderBO") NewExamUploaderBO examUploaderBO, @Param("type") int type);

    NewExamUploaderBO getEasyExamUploaderByExamUploaderId(@Param("examUploaderId") Long examUploaderId);

    List<Long> getRecognizedPaperIds(@Param("examId") long examId);

    List<NewExamUploaderVO> getMixExamUploaderListByExamId(@Param("examId") long examId);

    List<ExamUploaderExpBO> getExamUploaderExpInfo(@Param("scanningExamUploaderIds") List<Long> scanningExamUploaderIds);

    int getProgressingExamUploaderCount(long examId, long paperId);

    List<NewExamUploaderBO> getEducationExamUploaderIds(ExpEduMaterialsVO expEduMaterialsVO);

    ExamUploaderDTO getProgressExamUploader(ExamUploaderDTO examUploaderDTO);

    void updateRecognizedStatus(@Param("examUploaderIds") List<Long> examUploaderIds, @Param("status") int status);

    int getEduMaterialsUnFinishedTasks(@Param("userId") long userId);

    void handleClassExamUploaderUploadStatus(long examUploaderId, int uploadStatus);

    void handleUploadStatus(long examUploaderId, int uploadStatus);
}
