package com.dongni.exam.plan.dao;

import com.dongni.exam.plan.bean.entity.ExamStudentExt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * @desc
 */
public interface ExamStudentExtDao {

    void batchInsert(@Param("examStudentExts") List<ExamStudentExt> examStudentExts);

    List<ExamStudentExt> findByExamAndStus(@Param("examId") long examId, @Param("studentIds") List<Long> studentIds);


}
