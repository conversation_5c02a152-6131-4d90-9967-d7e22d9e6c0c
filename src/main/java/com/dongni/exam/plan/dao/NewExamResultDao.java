package com.dongni.exam.plan.dao;

import com.dongni.exam.card.bean.vo.AbsentStudentVO;
import com.dongni.exam.newcard.bean.ExamResultStudentVO;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.bean.dto.PaperSchoolDTO;
import com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO;
import com.dongni.exam.plan.bean.dto.QuestionPageNumberDTO;
import com.dongni.exam.plan.bean.dto.StudentCardDTO;
import com.dongni.exam.plan.bean.vo.*;
import com.dongni.exam.recognition.bean.vo.RecognitionStuVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStudentVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/29
 */
public interface NewExamResultDao {
    // 通过examUploader，获取对应的参考学生数量
    int getExamResultCountByExamUploaderBO(NewExamUploaderBO examUploaderBO);

    // 全部科目
    int getExamSchoolCount(SchoolProgressVO schoolProgressVO);

    List<PaperSchoolStatDTO> getSchoolProgressStat(SchoolProgressVO schoolProgressVO);

    List<PaperSchoolStatDTO> getSchoolProgressStat2(SchoolProgressVO schoolProgressVO);

    int  getSchoolPaperProgressStatCount(SchoolProgressVO schoolProgressVO);


    List<PaperSchoolStatDTO> getPaperProgressStat(SchoolProgressVO schoolProgressVO);

    List<PaperSchoolStatDTO> getPaperProgressStatInfo(SchoolProgressVO schoolProgressVO);

    List<PaperSchoolStatDTO> getPaperSchoolStatDetail(@Param("list") List<PaperSchoolStatDTO> list, @Param("type") int type,
                                                      @Param("examId") long examId);

    StudentStatVO getScopeStudentStatVO(NewExamUploaderBO scope);

    int getExamStudentResultCountByExamResultVO(GetExamResultVO getExamResultVO);

    List<ExamStudentResultVO> getExamStudentResultByExamResultVO(GetExamResultVO getExamResultVO);

    void updateUploadedStatus(@Param("examId") long examId, @Param("paperId") long paperId,
                              @Param("studentIds") List<Long> studentIds,
                              @Param("uploadedStatus") int uploadedStatus);

    ExamStudentResultVO getExamStudentInfo(@Param("examId") long examId, @Param("paperId") long paperId, @Param("schoolId") long schoolId, @Param("studentExamNum") String studentExamNum);

    void updateExamResultStatusByExamAndPaperAndStudentIds(@Param("examId") long examId,
                                                           @Param("paperId") long paperId,
                                                           @Param("resultStatus") int resultStatus,
                                                           @Param("resultStudentIds") List<Long> resultStudentIds);

    int getStudentCount(long examId, long paperId, List<Long> schoolIds);

    void updateUploadedStatusExamIdAndPaperIdAndSchoolAndStudents(@Param("examId") long examId,
                                                                  @Param("paperId")long paperId,
                                                                   @Param("uploadedStatus")int uploadedStatus,
                                                                   @Param("schoolIds")List<Long> schoolIds,
                                                                   @Param("studentIds") List<Long> x);

    List<AbsentStudentVO> downloadAbsentStudentList(NewExamUploaderBO newExamUploaderBO);

    List<AbsentStudentVO> getStudentAnswerCardStatus(ExamCardStatusVO examCardStatusVO);

    List<PaperSchoolDTO> getUploadedPaperSchoolIds(ExamCardStatusVO examCardStatusVO);

    UploaderStatsVO getExamStudentStatByExamIdAndCourseId(@Param("examId") long examId, @Param("courseId") long courseId);

    List<Map<String, Object>> getExamStudentInfoById(@Param("examId")long examId, @Param("paperId") long paperId, @Param("studentId") long studentId);

    int getUnuploadedStudentCount(NewExamUploaderBO bo);

    List<StudentCardDTO> getExamPaperStudents(@Param("examId") long examId, @Param("paperId") long paperId);

    List<Long> getUpgradeExamIds(@Param("start") int start, @Param("end") int end);

    long getMaxExamId();

    List<Long> getNewUpgradeExamIds(long examId);

	int getUploadedStatusCount(@Param("examId") long examId, @Param("paperId") long paperId);

    List<QuestionPageNumberDTO> getQuestionList(long paperId);

    List<Long> getStudentIdsByClassIds(@Param("examId") long examId, @Param("paperId") long paperId, @Param("classIds") List<Long> classIds);

    List<ExamStudentResultVO> getWrongCardRelativeStudents(@Param("examId") long examId, @Param("paperId") long paperId, @Param("studentIds") List<Long> deleteStudentIds);

    List<Long> getSchoolProgressStatSchoolIds(SchoolProgressVO schoolProgressVO);

    List<Long> getMissCardStudentIds(@Param("examId") long examId, @Param("paperId") long paperId, @Param("schoolId") long schoolId);

    List<PaperSchoolStatDTO> getNewSchoolProgressStat(@Param("examId") long examId, @Param("paperIds") List<Long> paperIds);

    List<PaperSchoolStatDTO> getNewClassProgressStat(@Param("examId") long examId, @Param("paperIds") List<Long> paperIds, @Param("schoolId") long schoolId);

    List<ExamResultStudentVO> getExamResultStudentsByExamIdAndPaperIdAndStuIds(@Param("examId") long examId, @Param("paperId") long paperId, @Param("studentIds") List<Long> studentIds);

    int getExamResultCount(RecognitionStuVO recognitionStuVO);

    List<RecognitionStudentVO> getExamResultList(RecognitionStuVO recognitionStuVO);

    void updateExamResultStatus(@Param("examId") long examId, @Param("paperId") long paperId, @Param("resultStatus") int resultStatus, @Param("studentIds") List<Long> studentIds);
}
