package com.dongni.exam.plan.manager.impl;

import com.dongni.exam.plan.bean.bo.ExamUploaderExpBO;
import com.dongni.exam.plan.bean.dto.ExamUploaderDTO;
import com.dongni.exam.plan.bean.vo.ExamUploaderProgressVO;
import com.dongni.exam.plan.bean.vo.ExamUploaderVO;
import com.dongni.exam.plan.bean.vo.NewExamUploaderVO;
import com.dongni.exam.plan.dao.NewExamUploaderDao;
import com.dongni.exam.plan.manager.INewExamUploaderManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/7
 */
@Component
public class NewExamUploaderManagerImpl implements INewExamUploaderManager {
    @Resource
    private NewExamUploaderDao newExamUploaderDao;

    @Override
    public List<NewExamUploaderVO> getMixExamUploaderListByExamId(long examId) {
        return newExamUploaderDao.getMixExamUploaderListByExamId(examId);
    }

    @Override
    public List<ExamUploaderProgressVO> getExamUploaderProgress(List<ExamUploaderProgressVO> examUploaderProgressVOList, long examId) {
        return newExamUploaderDao.getExamUploaderProgressList(examUploaderProgressVOList, examId);
    }

    @Override
    public List<ExamUploaderExpBO> getExamUploaderExceptionInfo(List<Long> scanningExamUploaderIds) {
        if (CollectionUtils.isEmpty(scanningExamUploaderIds)) {
            return null;
        }
        return newExamUploaderDao.getExamUploaderExpInfo(scanningExamUploaderIds);
    }

    @Override
    public int getProgressingExamUploaderCount(long examId, long paperId) {
        return newExamUploaderDao.getProgressingExamUploaderCount(examId,paperId);
    }

    @Override
    public void createExamUploader(ExamUploaderDTO examUploaderDTO) {
        newExamUploaderDao.insertExamUploader(examUploaderDTO);
    }

    @Override
    public void updateExamUploaderOfAnswerCardPath(ExamUploaderVO examUploaderVO) {
        newExamUploaderDao.updateExamUploaderOfAnswerCardPath(examUploaderVO);
    }

    @Override
    public ExamUploaderDTO getProgressExamUploader(ExamUploaderDTO examUploaderDTO) {
        return newExamUploaderDao.getProgressExamUploader(examUploaderDTO);
    }
}
