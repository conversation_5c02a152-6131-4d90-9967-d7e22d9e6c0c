package com.dongni.exam.plan.manager.impl;

import com.dongni.exam.newcard.bean.ExamResultStudentVO;
import com.dongni.exam.plan.bean.dto.ExamStudentDTO;
import com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO;
import com.dongni.exam.plan.dao.NewExamResultDao;
import com.dongni.exam.plan.dao.NewExamStudentDao;
import com.dongni.exam.plan.manager.INewExamResultManager;
import com.dongni.exam.recognition.bean.vo.RecognitionStuVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStudentVO;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/3
 */
@Component
public class NewExamResultManagerImpl implements INewExamResultManager {
    @Resource
    private NewExamResultDao newExamResultDao;

    @Resource
    private NewExamStudentDao newExamStudentDao;
    @Override
    public List<PaperSchoolStatDTO> getSchoolProgressStat(long examId, List<Long> paperIds) {
        return newExamResultDao.getNewSchoolProgressStat(examId, paperIds);
    }

    @Override
    public List<PaperSchoolStatDTO> getClassProgressStat(long examId, List<Long> paperIds, long schoolId) {
        return newExamResultDao.getNewClassProgressStat(examId,  paperIds, schoolId);
    }

    @Override
    public List<ExamResultStudentVO> getExamResultStudentsByExamIdAndPaperIdAndStuIds(long examId, long paperId, List<Long> studentIds) {
        return newExamResultDao.getExamResultStudentsByExamIdAndPaperIdAndStuIds(examId, paperId, studentIds);
    }

    @Override
    public ExamStudentDTO getStudentInfo(long examId, long studentId) {
        return newExamStudentDao.getExamStudent(examId, studentId);
    }

    @Override
    public int getExamResultCount(RecognitionStuVO recognitionStuVO) {
        return newExamResultDao.getExamResultCount(recognitionStuVO);
    }

    @Override
    public List<RecognitionStudentVO> getExamResultList(RecognitionStuVO recognitionStuVO) {
        return newExamResultDao.getExamResultList(recognitionStuVO);
    }

    @Override
    public void updateExamResultStatus(long examId, long paperId, int resultStatus, List<Long> studentIds) {
        newExamResultDao.updateExamResultStatus(examId, paperId, resultStatus, studentIds);
    }
}
