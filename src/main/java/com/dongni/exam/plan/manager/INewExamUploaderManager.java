package com.dongni.exam.plan.manager;

import com.dongni.exam.plan.bean.bo.ExamUploaderExpBO;
import com.dongni.exam.plan.bean.dto.ExamUploaderDTO;
import com.dongni.exam.plan.bean.vo.ExamUploaderProgressVO;
import com.dongni.exam.plan.bean.vo.ExamUploaderVO;
import com.dongni.exam.plan.bean.vo.NewExamUploaderVO;

import java.util.List;

public interface INewExamUploaderManager {
    /**
     * 根据考试ID获取上传人员列表
     * @param examId
     * @return
     */
    List<NewExamUploaderVO> getMixExamUploaderListByExamId(long examId);

    List<ExamUploaderProgressVO> getExamUploaderProgress(List<ExamUploaderProgressVO> examUploaderProgressVOList, long examId);

    /**
     * 获取examUploader异常信息
     * @param scanningExamUploaderIds
     * @return
     */
    List<ExamUploaderExpBO> getExamUploaderExceptionInfo(List<Long> scanningExamUploaderIds);

    int getProgressingExamUploaderCount(long examId, long paperId);

    /**
     * 创建examUploaderId
     * @param examUploaderDTO
     */
    void createExamUploader(ExamUploaderDTO examUploaderDTO);

    void updateExamUploaderOfAnswerCardPath(ExamUploaderVO examUploaderVO);

    ExamUploaderDTO getProgressExamUploader(ExamUploaderDTO examUploaderDTO);
}
