package com.dongni.exam.plan.manager;

import com.dongni.exam.newcard.bean.ExamResultStudentVO;
import com.dongni.exam.plan.bean.dto.ExamStudentDTO;
import com.dongni.exam.plan.bean.dto.PaperSchoolStatDTO;
import com.dongni.exam.recognition.bean.vo.RecognitionStuVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStudentVO;

import java.util.List;

public interface INewExamResultManager {
    List<PaperSchoolStatDTO> getSchoolProgressStat(long examId, List<Long> paperIds);

    List<PaperSchoolStatDTO> getClassProgressStat(long examId,  List<Long> paperIds, long schoolId);

    List<ExamResultStudentVO> getExamResultStudentsByExamIdAndPaperIdAndStuIds(long examId, long paperId, List<Long> studentIds);

    ExamStudentDTO getStudentInfo(long examId, long studentId);

    int getExamResultCount(RecognitionStuVO recognitionStuVO);

    List<RecognitionStudentVO> getExamResultList(RecognitionStuVO recognitionStuVO);

    void updateExamResultStatus(long examId, long paperId, int resultStatus, List<Long> outRelativeStuIds);
}
