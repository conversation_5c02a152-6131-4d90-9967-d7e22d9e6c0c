package com.dongni.exam.plan.score.imports.controller;

import com.dongni.commons.entity.Response;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.exam.plan.score.imports.service.ScoreImportExamPaperService;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2021/04/06 <br/>
 *
 */
@RestController
@RequestMapping("/exam/plan/score/import/exam/paper")
public class ScoreImportExamPaperController {
    
    @Autowired
    private ScoreImportExamPaperService scoreImportExamPaperService;
    
    /**
     * 获取考试课程导入模式分数信息及状态
     *  @param params examId [courseId]
     */
    @GetMapping("")
    public Response getExamPaperScoreImportList(Map<String, Object> params) {
        return new Response(scoreImportExamPaperService.getExamPaperScoreImportList(params));
    }
    
    /**
     * 添加课程
     * @param params examId   考试id
     *               courseIdList 课程idList  区域考只能是公共课程  校级允许添加公共课程或校本课程
     * @return nothing
     */
    @PostMapping("")
    public Response addExamPaperScoreImport(Map<String, Object> params) {
        scoreImportExamPaperService.addExamPaperScoreImportList(params);
        return new Response();
    }
    
    /**
     * 修改课程信息
     *    试卷名称
     *    试卷分数信息
     * @param params examId     考试id
     *               courseId   课程id
     *               examPaperScoreImportId  考试试卷导入模式id
     *               [paperName] 试卷名称             需要更新试卷时传递
     *               [fullMark] 总分 单课程时提供      需要更新总分时传递
     *               [subScoreList] 多课程时提供       需要更新总分时传递
     *                   子课程总分信息 [{}, {}, ...]
     *                   courseId    子课程id
     *                   courseName  子课程名称
     *                   fullMark    子课程分数
     * @return nothing
     */
    @PostMapping("/update")
    public Response updateExamPaperScoreImport(Map<String, Object> params) {
        scoreImportExamPaperService.updateExamPaperScoreImport(params);
        return new Response();
    }
    
    /**
     * 删除课程
     * @param params examId     考试id
     *               courseId   课程id
     *               examPaperScoreImportId 考试试卷导入模式id
     * @return nothing
     */
    @PostMapping("/delete")
    public Response deleteExamPaperScoreImport(Map<String, Object> params) {
        scoreImportExamPaperService.deleteExamPaperScoreImport(params);
        return new Response();
    }
}
