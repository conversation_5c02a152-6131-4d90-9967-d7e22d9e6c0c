package com.dongni.exam.plan.service;

import com.dongni.analysis.view.monitor.bean.SchExamListParam;
import com.dongni.analysis.view.monitor.service.ExamSchoolStudentStatService;
import com.dongni.analysis.view.report.service.ExamStatReportService;
import com.dongni.basedata.admin.enumeration.QueryConditionEnum;
import com.dongni.basedata.admin.service.IBaseSchoolService;
import com.dongni.basedata.export.area.service.CommonAreaService;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.export.school.service.CommonSchoolService;
import com.dongni.basedata.school.grade.service.impl.GradeServiceImpl;
import com.dongni.basedata.task.service.TaskService;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.constant.RequestUtil;
import com.dongni.exam.common.mark.enums.UserTypeEnum;
import com.dongni.exam.common.mark.serivice.basedata.IUserService;
import com.dongni.exam.common.mark.vo.RequestVO;
import com.dongni.exam.common.mark.vo.SysUserVO;
import com.dongni.exam.dispiay.service.DisplayExamService;
import com.dongni.exam.dispiay.service.ExamExtraVisibleAccountService;
import com.dongni.exam.enumeration.ExamTypeEnum;
import com.dongni.exam.plan.bean.dto.ExamInfoWithoutSchoolDTO;
import com.dongni.exam.plan.bean.dto.ExamInfoDTO;
import com.dongni.exam.plan.bean.dto.ThirdSchoolInfoDTO;
import com.dongni.exam.tool.service.ToolUserService;
import com.dongni.newmark.bean.ExamGradeVO;
import com.dongni.newmark.manager.IExamManager;
import com.dongni.third.base.service.table.ThirdSchoolService;
import com.dongni.third.haozhuanye.util.HaozhuanyeDataUtils;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.wrong.book.bean.param.WrongBookEffectivenessExamQuery;
import com.dongni.tiku.wrong.book.bean.vo.WrongBookExamVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * Created by Heweipo on 2018/11/26.
 * <p>
 * 考试列表
 */
@Service
public class ExamListService {

    @Autowired
    private ExamRepository commonRepository;

    @Autowired
    private CommonClassService commonClassService;

    @Autowired
    private ToolUserService toolUserService;

    @Autowired
    private GradeServiceImpl gradeService;

    @Autowired
    private IBaseSchoolService iBaseSchoolService;

    @Autowired
    private CommonAreaService commonAreaService;

    @Autowired
    private ExamService examService;

    @Autowired
    private ThirdSchoolService thirdSchoolService;

    @Autowired
    private CommonSchoolService commonSchoolService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ExamStatReportService examStatReportService;
    
    @Autowired
    private DisplayExamService displayExamService;

    @Autowired
    private CommonCourseService commonCourseService;

    @Autowired
    private ExamPaperService examPaperService;

    @Autowired
    private PaperManager paperManager;

    @Autowired
    private ExamExtraVisibleAccountService examExtraVisibleAccountService;

    @Autowired
    private ExamSchoolStudentStatService examSchoolStudentStatService;

    @Autowired
    private IExamManager examManager;

    @Autowired
    private IUserService userService;

    /**
     * 一年的毫秒数 = 365天 * 24小时 * 60分钟 * 60秒 * 1000毫秒
     */
    private static final long ONE_YEAR_IN_MILLIS = 365L * 24 * 60 * 60 * 1000;

    /**
     * 获取老师的考试列表
     *
     * @return 考试列表
     */
    public Map<String, Object> getExamList(Map<String, Object> params) {

        // 参数解析
        splitSearchParams(params, "examStatus", params.remove("examStatus"));
        splitSearchParams(params, "examTypes", params.remove("examType"));
        splitSearchParams(params, "gradeIds", params.remove("gradeId"));
        splitSearchParams(params, "courseIds", params.remove("courseId"));
        Object classId = params.get("classId");
        if (classId != null) {
            splitSearchParams(params, "classIds", params.get("classId"));
            if (classId.toString().contains(",")) {
                params.remove("classId");
            }
        }

        Map<String,Object> result=new HashMap<>();
        int userType = MapUtils.getInteger(params, "userType");

        // 联考员
        if (DictUtil.isEquals(userType, "userType", "examiner")) {
            result= getExamByExaminer(params);
        }

        // 校长principal9
        // 教导主任director10
        // 教务管理员schoolInstructor15
        // 运营-审核员operatorReviewer18
        // 教研组长schoolCourseDirector20
        // 学校教研员instructor8
        // 产品顾问product1
        if (DictUtil.isEquals(userType, "userType", "principal", "director", "schoolInstructor", "operatorReviewer",
          "schoolCourseDirector", "product")) {
            result= getExamBySchool(params);
        }

        // 年级主任|备课组长
        if (DictUtil.isEquals(userType, "userType", "gradeDirector", "prepareLeader")) {
            result= getExamByDirector(params);
        }

        // 普通老师
        if (DictUtil.isEquals(userType, "userType", "teacher")) {
            result= getExamByTeacher(params);
        }
        result = Optional.ofNullable(result).orElse(MapUtil.of("exam",Collections.emptyList(),"totalCount",0));
        if (ObjectUtil.isValidId(params.get("schoolId"))){
            setExamStatListForExamList(Long.valueOf(params.get("schoolId").toString()),(List<Map<String, Object>>) result.get("exam"));
        }

        // 处理权限 公布成绩按钮的显示
        List<Map<String, Object>> exam = (List<Map<String, Object>>) result.get("exam");

        List<Long> examIdList = null;
        if (CollectionUtils.isNotEmpty(exam)) {
            examIdList= exam.stream().map(m -> Long.valueOf(m.get("examId").toString()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(examIdList)) {
            Map<Long, List<Map<String, Object>>> schoolUserToolList = toolUserService.getSchoolUserToolListByExamIdList(MapUtil.of(
                    "examIdList", examIdList,
                    "userId", params.get("userId"),
                    "userName", params.get("userName"),
                    "userType", params.get("userType")
            ));
            exam.forEach(o -> {
                o.put("publishScoreStatus", Optional
                        .ofNullable(schoolUserToolList.get(Long.parseLong(o.get("examId").toString())))
                        .map(list -> list.stream().filter(o1 -> o1.get("code").equals("publish-score")).count() == 1 ? 1 : 0)
                        .orElse(0));
            });

            //获取考试科目最大科目状态， 用于前端判断该考试是否能够删除（学校维度）
            if (!ObjectUtil.isBlank(params.get("schoolId"))){
                Map<String, Object> paramsMap = new HashMap<>();
                paramsMap.put("examIdList", examIdList);
                paramsMap.put("schoolId", params.get("schoolId"));
                List<Map<String, Object>> examMaxPaperStatusList = commonRepository.selectList("ExamSchoolMapper.getExamSchoolPaperStatus", paramsMap);
                Map<String, Integer> examIdMap = examMaxPaperStatusList.stream()
                        .collect(toMap(l -> l.get("examId").toString(), l -> Integer.valueOf(l.get("examSchoolPaperStatus").toString())));
                for (Map<String, Object> e : exam) {
                    e.put("examSchoolMaxPaperStatus", examIdMap.get(e.get("examId").toString()));
                }
            }

            //获取考试是否有原始报告，有说明考试至少有一张试卷是已发布的的状态，此时才可以去点击查看报告进入报告、工具箱等界面
            //这个参数目前只对同步联考2.0中的流程改动有效，其他类型的考试这个参数暂时没有意义
            List<Long> examIdsHavingDefaultStat = commonRepository
              .selectList("ExamUnionNewMapper.getExamHavingDefaultStat", MapUtil.of("examIds", examIdList));
            exam.forEach(x -> x.put("havingDefaultStat", examIdsHavingDefaultStat.contains(MapUtils.getLong(x, "examId"))));
        }
        
        // result { totalCount, exam:[{examId,examType}] }
        // 考试工具箱-考试可见性-联考/区域报告是否可见(在考试列表是否显示区域报告/联考报告按钮)字段MultiExamReportVisible
        displayExamService.fillExamConfigMultiExamReportVisibleForExamList(MapUtil.getCast(result, "exam"));

			//增加参数needExamTeacherDeclare,判断阅卷安排前是否需要进行阅卷老师申报
			if (CollectionUtils.isNotEmpty(examIdList)) {
			    params.put("examIds",examIdList);
			    //查询开启了阅卷申报的考试的申报状态
          List<Map<String,Object>> declareTaskStatus = commonRepository.selectList("ExamTeacherDeclareTaskMapper.getDeclareTaskStatus", params);
          //过滤获得开启了阅卷申报并且阅卷申报还未完成的考试
          List<Long> declareUnfinishedExamIds = declareTaskStatus.stream().filter(x -> MapUtils.getInteger(x, "declareStatus") == 0)
            .map(x -> MapUtils.getLong(x, "examId")).collect(Collectors.toList());
          exam.forEach(e -> e.put("needExamTeacherDeclare", declareUnfinishedExamIds.contains(MapUtils.getLong(e, "examId"))));
			}

        return result;
    }
    
    /**
     * @Description: 为考试列表的每个考试添加他们的考试报告
     * @param: userId,userType,userName,gradeId,artsScience,examType,schoolId,pageNo,pageSize,teacherId
     * @return: 返回添加了考试报告后的考试列表
     */
    private List<Map<String, Object>> setExamStatListForExamList(long schoolId,List<Map<String, Object>> examList) {

        if (CollectionUtils.isEmpty(examList)){
            return examList;
        }
        List<String> examIdList = examList
                .stream()
                .map(examMap->examMap.get("examId").toString())
                .collect(Collectors.toList());

        //获取处于开启状态的学校考试报告
        Map<String, Object> param= MapUtil.of("examIdList", examIdList, "isDisplay",DictUtil.getDictValue("isDisplay","on"),"schoolId",schoolId);
        List<Map<String,Object>> examStatList = commonRepository.selectList("ExamStatReportMapper.getAllExamStatByExamIdList",param);

        //按照examStatList每个元素的examId值进行分组
        Map<String, List<Map<String, Object>>> examStatListMap = examStatList
                .stream()
                .collect(Collectors.groupingBy(examStatMap -> examStatMap.get("examId").toString()));

        int executing = DictUtil.getDictValue("examStatus", "executing");

        //获取指定学校的考试信息
        List<Map<String,Object>> schoolExamList = commonRepository.selectList("ExamSchoolMapper.selectSchoolExamList",param);

        //以examId为key
        Map<String, Map<String, Object>> schoolExamListMap = schoolExamList.stream()
                .collect(Collectors.toMap(schoolExam -> schoolExam.get("examId").toString(), Function.identity()));

        examList.forEach(examMap->{
            String examId = examMap.get("examId").toString();
            Map<String, Object> schoolExam = schoolExamListMap.get(examId);

            //学校考试状态
            Integer examSchoolStatus = Optional.ofNullable(schoolExam)
                    .map(map -> Integer.valueOf(map.get("examSchoolStatus").toString()))
                    .orElse(executing);

            examMap.put("examSchoolStatus", examSchoolStatus);

            //指定考试(examId)的所有考试报告
            List<Map<String, Object>> schoolExamStatByExamIdList = examStatListMap.get(examId);
            schoolExamStatByExamIdList=Optional.ofNullable(schoolExamStatByExamIdList).orElse(Collections.emptyList());
            examMap.put("examStatList", schoolExamStatByExamIdList);
        });

        return examList;
    }

    /**
     * 通过学校的权限查询考试列表
     *
     * @param params schoolId
     * @return 考试列表。
     */
    public Map<String, Object> getExamByExaminer(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("examinerId").verify();

        Map<String, Object> rs = new HashMap<>();

        //根据userId获取联考包含的学校Id
        List<Map<String,Object>> schoolList = commonRepository.selectList("ExamListMapper.getSchoolIdByExaminerId",params);

        if (CollectionUtils.isEmpty(schoolList)) {
            return MapUtil.of("totalCount", 0, "exam", Collections.emptyList());
        }

        //查询学校下的年级毕业情况
        params.put("schoolList", schoolList);
        List<Map<String,Object>> gradeInfo = gradeService.getGradeInfo(params);

        List<Long> gradeIdList;
        if (ObjectUtil.isValueEquals(params.get("graduateStatus"), 0)) {
            gradeIdList = gradeInfo.stream()
                    .filter(a -> ObjectUtil.isValueEquals(a.get("graduateStatus"), 0))
                    .map(b -> Long.parseLong(b.get("gradeId").toString()))
                    .collect(Collectors.toList());
        } else {
            gradeIdList = gradeInfo.stream()
                    .filter(a -> ObjectUtil.isValueEquals(a.get("graduateStatus"), 1)
                            &&ObjectUtil.isValueEquals(a.get("stage"), params.get("stage")))
                    .map(b -> Long.parseLong(b.get("gradeId").toString()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(gradeIdList)) {
            return MapUtil.of("totalCount", 0, "exam", Collections.emptyList());
        }
        params.put("gradeIdList", gradeIdList);

        Integer totalCount = commonRepository.selectOne("ExamListMapper.getExamByExaminerCount", params);
        rs.put("totalCount", totalCount);
        if (totalCount == null || totalCount == 0) {
            rs.put("exam", Collections.emptyList());
            return rs;
        }

        // 单科目、多科目筛选项
        if(ObjectUtil.isValidId(params.get("examCourseCount"))){
            List<Long> examIdListByCourseCount = filterExamByCourseCount(params, "ExamListMapper.getExamIdsByExaminer");
            if (CollectionUtils.isEmpty(examIdListByCourseCount)){
                return MapUtil.of("totalCount", 0, "exam", Collections.emptyList());
            }
            rs.put("totalCount", examIdListByCourseCount.size());
            params.put("examIdListByCourseCount", examIdListByCourseCount);
        }

        // 校本录题筛选项
        if (ObjectUtil.isNotBlank(params.get("schoolPaperStatus"))) {
            Map<String, Object> schoolPaperFilterParams = new HashMap<>(params);
            schoolPaperFilterParams.remove("pageNo");
            schoolPaperFilterParams.remove("pageSize");
            List<Long> list = commonRepository.selectList("ExamListMapper.getExamIdsByExaminer", schoolPaperFilterParams);
            List<Long> examIdList = schoolQuestionBankFilter(params, list);
            // 过滤后examIdList不为空则加上examIdList条件再次查询，否则返回空列表
            if (CollectionUtils.isNotEmpty(examIdList)) {
                params.put("examIdList", examIdList);
                totalCount = commonRepository.selectOne("ExamListMapper.getExamByExaminerCount", params);
                rs.put("totalCount", totalCount);
                if (totalCount == null || totalCount == 0) {
                    rs.put("exam", Collections.emptyList());
                    return rs;
                }
                List<Map<String, Object>> examList = commonRepository.selectList("ExamListMapper.getExamByExaminer", params);
                rs.put("exam", examList);
                return rs;
            } else {
                return MapUtil.of("totalCount", 0, "exam", Collections.emptyList());
            }
        }

        List<Map<String, Object>> list = commonRepository.selectList("ExamListMapper.getExamByExaminer", params);

        rs.put("exam", list);

        return rs;
    }

    /**
     * 单科目、多科目筛选项过滤
     * @param params 已有的其他查询条件
     * @param listMapper 根据已有的其他查询条件查询examId列表的查询语句
     * @return 已有的其他查询条件 + 单科目、多科目筛选项 过滤后的examIdList
     */
    public List<Long> filterExamByCourseCount(Map<String, Object> params, String listMapper){
        Map<String, Object> examCourseFilterParams = new HashMap<>(params);
        examCourseFilterParams.remove("pageNo");
        examCourseFilterParams.remove("pageSize");
        List<Long> list = commonRepository.selectList(listMapper, examCourseFilterParams);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        return commonRepository.selectList("ExamListMapper.getExamIdByIdAndCourseCount",
          MapUtil.of("examIdList", list, "examCourseCount", params.get("examCourseCount")));
    }

    /**
     * 校本题库过滤
     *
     * @param params schoolPaperStatus
     * @param examIdList 考试id列表
     * @return examIdList
     */
    public List<Long> schoolQuestionBankFilter(Map<String, Object> params,
                                               List<Long> examIdList) {

        if (CollectionUtils.isEmpty(examIdList)) {
            return Lists.newArrayList();
        }

        // 查询对应的考试paper列表
        List<Map<String, Object>> examItemList = examPaperService.getExamPaper4FilterByExamIds(MapUtil.of("examIdList", examIdList));

        List<Long> paperIdList = examItemList.stream()
                .flatMap(item -> MapUtil.getListMap(item, "examPaperList").stream())
                .map(item -> MapUtil.getLong(item, "paperId"))
                .collect(Collectors.toList());

        // 查询mongo.paper对应的creationType
        List<Document> paperDocList = paperManager.getPaperSimple(paperIdList);
        Map<Long, Document> paperMap = paperDocList.stream().collect(toMap(item -> MapUtil.getLong(item, "paperId"), item -> item));

        // 过滤校本题库状态
        int schoolPaperStatus = MapUtil.getInt(params, "schoolPaperStatus");
        boolean completedFlag = DictUtil.isEquals(schoolPaperStatus, "schoolPaperStatus", "completed");
        boolean partiallyCompletedFlag = DictUtil.isEquals(schoolPaperStatus, "schoolPaperStatus", "partiallyCompleted");
        boolean undoneFlag = DictUtil.isEquals(schoolPaperStatus, "schoolPaperStatus", "undone");
        Integer normalFlag = DictUtil.getDictValue("paperCreationType", "normal");
        Integer selfEditFlag = DictUtil.getDictValue("paperCreationType", "selfEdit");

        List<Long> examIdList4SchoolPaperFilter = Lists.newArrayList();

        // 设置examPaper的entrustStatus
        examItemList.forEach(examItem -> {
            List<Map<String, Object>> paperList = MapUtil.getListMap(examItem, "examPaperList");

            paperList.forEach(paper -> {
                long paperId = MapUtil.getLong(paper, "paperId");
                Document paperDocItem = paperMap.get(paperId);
                if (MapUtils.isNotEmpty(paperDocItem)) {
                    paper.put("creationType", MapUtil.getInt(paperDocItem, "creationType"));
                } else {
                    // 未录题
                    paper.put("creationType", selfEditFlag);
                }
            });
        });

        // 比较完成数量，全部试卷录题完成为已完成 部分试卷录题完成为部分完成 没有试卷录题完成为未完成
        for (Map<String, Object> examItem : examItemList) {

            long examId = MapUtil.getLong(examItem, "examId");
            List<Map<String, Object>> paperList = MapUtil.getListMap(examItem, "examPaperList");

            // 完成数量
            long creationType4CompleteCount = paperList.stream().filter(examPaper -> ObjectUtil.isValueEquals(normalFlag, examPaper.get("creationType"))).count();

            int examPaperSize = paperList.size();

            // 统计已完成考试
            if (completedFlag && examPaperSize > 0) {
                if (creationType4CompleteCount == examPaperSize) {
                    examIdList4SchoolPaperFilter.add(examId);
                }
            }

            // 统计未完成考试
            if (undoneFlag) {
                if (creationType4CompleteCount == 0 || examPaperSize == 0) {
                    examIdList4SchoolPaperFilter.add(examId);
                }
            }

            // 统计部分完成考试
            if (partiallyCompletedFlag) {
                if (creationType4CompleteCount > 0 && creationType4CompleteCount < examPaperSize) {
                    examIdList4SchoolPaperFilter.add(examId);
                }
            }
        }
        return examIdList4SchoolPaperFilter;
    }

    /**
     * 通过学校的权限查询考试列表
     * @param params schoolId
     * @return 考试列表
     */
    public Map<String, Object> getExamBySchool(Map<String, Object> params){
        Long schoolId = MapUtils.getLong(params, "schoolId");
        Set<Long> examIdsBySchool = getExamIdsBySchool(params, schoolId);
        // 被设置了单独可见账号的考试id
        Set<Long> examIds = examExtraVisibleAccountService.getVisibleExamIds(MapUtils.getLong(params, "userId"),
                MapUtils.getInteger(params, "userType"));
        //过滤掉本来就能看见的考试
        examIds = examIds.stream().filter(x -> !examIdsBySchool.contains(x)).collect(Collectors.toSet());
        if (!examIds.isEmpty()) {
            if (DictUtil.isEquals(MapUtils.getInteger(params, "userType"), "userType", "schoolCourseDirector")) {
                //学校教研组长，可能本来因为课程的原因看不到某场考试，设置了单独可见账号后，就不需要再受课程限制
                params.remove("courseIds");
            }
            params.put("examIds", examIds);
            examIds = getExamIdsBySchool(params, schoolId);
        }
        if (examIds.isEmpty()) {
            examIds = examIdsBySchool;
        } else {
            examIds.addAll(examIdsBySchool);
        }
        examIds = displayExamService.filterInvisibleExam(params, examIds);
        Map<String, Object> rs = new HashMap<>(2);
        rs.put("totalCount", examIds.size());
        if(examIds.isEmpty()){
            rs.put("exam", Collections.emptyList());
            return rs;
        }
        params.put("examIds", examIds);
        List<Map<String, Object>> examList = commonRepository.selectList("ExamListMapper.getExamBySchool", params);
        rs.put("exam", fillExamExtraVisibleFlag(examList, examIdsBySchool));
        return rs;
    }

    /**
     * 通过学校的权限查询考试id列表
     *
     * @param paramsMap schoolId
     * @return 考试列表。
     */
    public Set<Long> getExamIdsBySchool(Map<String, Object> paramsMap, Long schoolId) {

        // 参数校验
        Verify.of(paramsMap).isValidId("schoolId").verify();
        Map<String, Object> params = new HashMap<>(paramsMap);

        if (ObjectUtil.isValueEquals(params.get("graduateStatus"), 0)) {
            //根据schoolId获取未毕业的grade_id
            List<Long> notGraduateGradeId = gradeService.getNotGraduateGradeId(params);
            params.put("notGraduateGradeId", notGraduateGradeId);
        } else if (ObjectUtil.isValueEquals(params.get("graduateStatus"), 1)) {
            //根据schoolId获取已毕业的grade_id
            List<Long> graduateGradeIdList = gradeService.getGraduateGradeId(params);
            params.put("graduateGradeIdList", graduateGradeIdList);
        } else {}

        if (ObjectUtil.isNotBlank(params.get("courseIds"))) {
            params.put("courseIds", commonCourseService.getParentCourseByExam(Arrays.asList((String[])params.get("courseIds")), schoolId)
                .stream().map(x->MapUtils.getLong(x,"courseId")).collect(Collectors.toList()));
        }

        Integer totalCount = commonRepository.selectOne("ExamListMapper.getExamBySchoolCount", params);
        if (totalCount == null || totalCount == 0) {
            return Collections.emptySet();
        }

        // 单科目、多科目筛选项
        if (ObjectUtil.isValidId(params.get("examCourseCount"))) {
            // 单科目、多科目筛选项
            List<Long> examIdListByCourseCount = filterExamByCourseCount(params, "ExamListMapper.getExamIdsBySchool");
            if (CollectionUtils.isEmpty(examIdListByCourseCount)) {
                return Collections.emptySet();
            }
            params.put("examIdListByCourseCount", examIdListByCourseCount);

        }

        // 校本录题筛选项
        if (ObjectUtil.isNotBlank(params.get("schoolPaperStatus"))) {
            Map<String, Object> schoolPaperFilterParams = new HashMap<>(params);
            schoolPaperFilterParams.remove("pageNo");
            schoolPaperFilterParams.remove("pageSize");

            List<Long> list = commonRepository.selectList("ExamListMapper.getExamIdsBySchool", schoolPaperFilterParams);
            List<Long> examIdList = schoolQuestionBankFilter(params, list);
            // 过滤后examIdList不为空则加上examIdList条件再次查询，否则返回空列表
            if (CollectionUtils.isNotEmpty(examIdList)) {
                params.put("examIdList", examIdList);
                totalCount = commonRepository.selectOne("ExamListMapper.getExamBySchoolCount", params);
                if (totalCount == null || totalCount == 0) {
                    return Collections.emptySet();
                }
                return new HashSet<>(commonRepository.selectList("ExamListMapper.getExamIdsBySchool", params));
            } else {
                return Collections.emptySet();
            }
        }

        return new HashSet<>(commonRepository.selectList("ExamListMapper.getExamIdsBySchool", params));
    }

    /**
     * 年级主任|科目主任查询考试列表
     *
     * @param params gradeId
     * @return 考试列表
     */
    public Map<String, Object> getExamByDirector(Map<String, Object> params){
        Set<Long> examIdsByDirector = getExamIdsByDirector(params);
        // 被设置了单独可见账号的考试id
        Set<Long> examIds = examExtraVisibleAccountService.getVisibleExamIds(MapUtils.getLong(params, "userId"),
                MapUtils.getInteger(params, "userType"));
        //过滤掉本来就能看见的考试
        examIds = examIds.stream().filter(x -> !examIdsByDirector.contains(x)).collect(Collectors.toSet());
        if (!examIds.isEmpty()) {
            if (DictUtil.isEquals(MapUtils.getInteger(params, "userType"), "userType", "prepareLeader")) {
                //备课组长，可能本来因为课程的原因看不到某场考试，设置了单独可见账号后，就不需要再受课程限制
                params.remove("courseIds");
            }
            params.put("examIds", examIds);
            examIds = getExamIdsByDirector(params);
        }
        if (examIds.isEmpty()) {
            examIds = examIdsByDirector;
        } else {
            examIds.addAll(examIdsByDirector);
        }
        examIds = displayExamService.filterInvisibleExam(params, examIds);
        Map<String, Object> rs = new HashMap<>(2);
        rs.put("totalCount", examIds.size());
        if(examIds.isEmpty()){
            rs.put("exam", Collections.emptyList());
            return rs;
        }
        params.put("examIds", examIds);
        List<Map<String, Object>> examList = commonRepository.selectList("ExamListMapper.getExamPlanByDirector", params);
        rs.put("exam", fillExamExtraVisibleFlag(examList, examIdsByDirector));
        return rs;
    }

    /**
     * 年级主任|科目主任查询考试id列表
     *
     * @param params gradeId
     * @return 考试列表
     */
    public Set<Long> getExamIdsByDirector(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("schoolId").isNotBlank("gradeIds").verify();

        if (ObjectUtil.isValueEquals(params.get("graduateStatus"), 0)) {
            //根据schoolId获取未毕业的grade_id
            List<Long> notGraduateGradeId = gradeService.getNotGraduateGradeId(params);
            params.put("notGraduateGradeId", notGraduateGradeId);
        }

        Integer totalCount = commonRepository.selectOne("ExamListMapper.getExamByDirectorCount", params);
        if (totalCount == null || totalCount == 0) {
            return Collections.emptySet();
        }


        // 单科目、多科目筛选项
        if(ObjectUtil.isValidId(params.get("examCourseCount"))){
            List<Long> examIdListByCourseCount = filterExamByCourseCount(params, "ExamListMapper.getExamIdsByDirector");
            if (CollectionUtils.isEmpty(examIdListByCourseCount)){
                return Collections.emptySet();
            }
            params.put("examIdListByCourseCount", examIdListByCourseCount);
        }

        // 校本录题筛选项
        if (ObjectUtil.isNotBlank(params.get("schoolPaperStatus"))) {
            Map<String, Object> schoolPaperFilterParams = new HashMap<>(params);
            schoolPaperFilterParams.remove("pageNo");
            schoolPaperFilterParams.remove("pageSize");

            List<Long> list = commonRepository.selectList("ExamListMapper.getExamIdsByDirector", schoolPaperFilterParams);
            List<Long> examIdList = schoolQuestionBankFilter(params, list);
            // 过滤后examIdList不为空则加上examIdList条件再次查询，否则返回空列表
            if (CollectionUtils.isNotEmpty(examIdList)) {
                params.put("examIdList", examIdList);
                totalCount = commonRepository.selectOne("ExamListMapper.getExamByDirectorCount", params);
                if (totalCount == null || totalCount == 0) {
                    return Collections.emptySet();
                }
                return new HashSet<>(commonRepository.selectList("ExamListMapper.getExamIdsByDirector", params));
            } else {
                return Collections.emptySet();
            }
        }

        return new HashSet<>(commonRepository.selectList("ExamListMapper.getExamIdsByDirector", params));
    }

    /**
     * 查询普通老师的考试列表
     *
     * @param params teacherId userId
     * @return 考试列表
     */
    public Map<String, Object> getExamByTeacher(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("schoolId").verify();

        Set<Long> examIds = new HashSet<>();
        params.put("examIds", examIds);


        Set<Long> examPlanIds = getVisibleExamIds(params, examIds);

        // 被设置了单独可见账号的考试id
        Set<Long> extraExamIds = examExtraVisibleAccountService.getVisibleExamIds(MapUtils.getLong(params, "userId"),
                MapUtils.getInteger(params, "userType"));
        examIds.addAll(extraExamIds);

        examIds = displayExamService.filterInvisibleExam(params, examIds);
        Map<String, Object> rs = new HashMap<>();
        if (examIds.isEmpty()) {
            rs.put("totalCount", 0);
            rs.put("exam", Collections.emptyList());
            return rs;
        }
        params.put("examIds", examIds);

        if (ObjectUtil.isValueEquals(params.get("graduateStatus"), 0)) {
            //根据schoolId获取未毕业的grade_id
            List<Long> notGraduateGradeId = gradeService.getNotGraduateGradeId(params);
            params.put("notGraduateGradeId", notGraduateGradeId);
        }

        //获取考试数量
        Integer totalCount = commonRepository.selectOne("ExamListMapper.getExamByIdCount", params);
        rs.put("totalCount", totalCount);
        if (totalCount == null || totalCount == 0) {
            rs.put("exam", Collections.emptyList());
            return rs;
        }

        // 单科目、多科目筛选项
        if(ObjectUtil.isValidId(params.get("examCourseCount"))){
            List<Long> examIdListByCourseCount = filterExamByCourseCount(params, "ExamListMapper.getExamIdsById");
            if (CollectionUtils.isEmpty(examIdListByCourseCount)){
                return MapUtil.of("totalCount", 0, "exam", Collections.emptyList());
            }
            rs.put("totalCount", examIdListByCourseCount.size());
            params.put("examIdListByCourseCount", examIdListByCourseCount);
        }

        // 校本录题筛选项
        if (ObjectUtil.isNotBlank(params.get("schoolPaperStatus"))) {
            Map<String, Object> schoolPaperFilterParams = new HashMap<>(params);
            schoolPaperFilterParams.remove("pageNo");
            schoolPaperFilterParams.remove("pageSize");

            List<Long> list = commonRepository.selectList("ExamListMapper.getExamIdsById", schoolPaperFilterParams);
            List<Long> examIdList = schoolQuestionBankFilter(params, list);
            // 过滤后examIdList不为空则加上examIdList条件再次查询，否则返回空列表
            if (CollectionUtils.isNotEmpty(examIdList)) {
                params.put("examIdList", examIdList);
                totalCount = commonRepository.selectOne("ExamListMapper.getExamByIdCount", params);
                rs.put("totalCount", totalCount);
                if (totalCount == null || totalCount == 0) {
                    rs.put("exam", Collections.emptyList());
                    return rs;
                }
                List<Map<String, Object>> examList = commonRepository.selectList("ExamListMapper.getExamById", params);
                rs.put("exam", fillExamExtraVisibleFlag(examList, examPlanIds));

                return rs;
            } else {
                return MapUtil.of("totalCount", 0, "exam", Collections.emptyList());
            }
        }

        //获取考试(已毕业的考试按gradeType查询)
        List<Map<String, Object>> list = commonRepository.selectList("ExamListMapper.getExamById", params);
        rs.put("exam", fillExamExtraVisibleFlag(list, examPlanIds));

        return rs;
    }

    public Set<Long> getVisibleExamIds(Map<String, Object> params, Set<Long> examIds) {

        // 如果是顾问，查询符合条件的所有考试
        long userId = MapUtil.getLong(params, "userId");
        SysUserVO sysUserVO = userService.findByUserId(userId);
        if (UserTypeEnum.isConsultant(sysUserVO.getUserType())) {
            List<Long> allExamIds = commonRepository.selectList("ExamListMapper.getExamIdForConsultant", params);
            examIds.addAll(allExamIds);
            return examIds;
        }

        // 作为考试创建人可以查看的考试ID
        List<Long> ids = commonRepository.selectList("ExamListMapper.getExamIdForCreator", params);
        Set<Long> examPlanIds = new HashSet<>(ids);

        // 作为任课老师可以查看的考试ID
        List<Map<String, Object>> classCourse = commonClassService.getTeacherClassCourse(params);
        if (CollectionUtils.isNotEmpty(classCourse)) {
            if (!ObjectUtil.isBlank(params.get("courseIds"))) {
                Set<String> courseIds = new HashSet<>(Arrays.asList((String[]) params.get("courseIds")));
                classCourse.removeIf(next -> !courseIds.contains(next.get("courseId").toString()));
            }
            if (!ObjectUtil.isBlank(params.get("classIds"))) {
                List<String> classIds = Arrays.asList((String[]) params.get("classIds"));
                classCourse.removeIf(next -> !classIds.contains(next.get("classId").toString()));
            }
            if (CollectionUtils.isNotEmpty(classCourse)) {
                params.put("classCourse", classCourse);
                //根据t_exam_teacher获取
                ids = commonRepository.selectList("ExamListMapper.getExamIdForClassTeacher", params);
                examPlanIds.addAll(ids);
                //根据t_exam_class_paper获取
                ids = commonRepository.selectList("ExamListMapper.getExamIdForClassPaper", params);
                examPlanIds.addAll(ids);
            }
        }

        // 作为班主任可以查看的考试ID
        List<Map<String, Object>> headerClass = commonClassService.getHeaderClass(params);
        if (CollectionUtils.isNotEmpty(headerClass)) {
            if (!ObjectUtil.isBlank(params.get("classIds"))) {
                List<String> classIds = Arrays.asList((String[]) params.get("classIds"));
                headerClass.removeIf(next -> !classIds.contains(next.get("classId").toString()));
            }
            if (CollectionUtils.isNotEmpty(headerClass)) {
                params.put("headerClass", headerClass);
                //根据t_exam_teacher获取
                ids = commonRepository.selectList("ExamListMapper.getExamIdForClassHeader", params);
                examPlanIds.addAll(ids);
                //根据t_exam_class获取
                ids = commonRepository.selectList("ExamListMapper.getExamIdForClass", params);
                examPlanIds.addAll(ids);
            }
        }
        examIds.addAll(examPlanIds);
        return examPlanIds;
    }


    public List<ExamGradeVO> listTchHomework(long schoolId, long teacherId) {
        RequestVO user = RequestUtil.getUser();
        long userId = user.getUserId();
        Map<String, Object> params = MapUtil.of("schoolId", schoolId, "teacherId", teacherId, "userId", userId);
        params.put("examType", ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode());
        Set<Long> examIds = new HashSet<>();
        Set<Long> visibleExamIds = getVisibleExamIds(params, examIds);
        if (CollectionUtils.isEmpty(visibleExamIds)) {
            return Collections.emptyList();
        }
        return examManager.findByIds(new ArrayList<>(visibleExamIds));
    }

    public List<ExamInfoDTO> getExamListByAreaId(Long regionId) {
        if (!ObjectUtil.isValidId(regionId)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "regionId参数异常");
        }
        List<Map<String, Object>> areaList = commonAreaService.getAreaByAreaId(MapUtil.of("areaId", Collections.singletonList(regionId)));
        if (areaList.isEmpty()) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "获取区域信息失败！areaId：" + regionId);
        }

        // 查询校内考（根据区域获取区域下面的学校，再根据学校去查询）+区域考（只查询当前区域发布的考试，和中台区域数据源那边一样）
        List<Map<String, Object>> examList = new ArrayList<>();
        Map<String, Object> query = MapUtil.copy(areaList.get(0), "areaId", "areaCode");
        query.put("hasChild", QueryConditionEnum.HAS_CHILD.getCode());
        query.put("schoolStatus", DictUtil.getDictValue("schoolStatus", "enable"));
        List<Map<String, Object>> schoolList = MapUtil.getListMap(iBaseSchoolService.findSchools(query), "schools");
        if (CollectionUtils.isNotEmpty(schoolList)) {
            query.put("examTypes", examService.getSchoolUsualExamType());
            query.put("schoolIds", schoolList.stream().map(x -> MapUtils.getLong(x, "schoolId")).collect(Collectors.toList()));
            examList.addAll(commonRepository.selectList("ExamListMapper.getSchoolExamByArea", query));
        }

        List<Long> unionExamIds = commonRepository.selectList("ExamListMapper.getUnionExamIdsByArea", query);
        if (!unionExamIds.isEmpty()) {
            examList.addAll(commonRepository.selectList("ExamListMapper.getUnionExamByArea", unionExamIds));
        }

        if (examList.isEmpty()) {
            return Collections.emptyList();
        }

        // 找到schoolId -> 好专业的schoolId
        Set<Integer> thirdPartyIds = DictUtil.getDictValues("thirdParty", "nicezhuanye", "nicezhuanye2");
        List<Long> schoolIds = examList.stream().map(x -> MapUtils.getLong(x, "schoolId")).distinct().collect(Collectors.toList());
        Map<Long, String> schoolId2ThirdPrimaryKey = new HashMap<>(schoolIds.size());
        commonSchoolService.getSchoolThirdInfo(schoolIds).stream()
          .collect(Collectors.groupingBy(x -> MapUtils.getInteger(x, "thirdPartyId")))
          .forEach((thirdPartyId, thirdSchoolInfoList) -> {
                if (thirdPartyIds.contains(thirdPartyId)) {
                    Map<Long, Long> schoolId2ThirdBizId = thirdSchoolInfoList.stream()
                      .collect(toMap(x -> MapUtils.getLong(x, "schoolId"), x -> MapUtils.getLong(x, "thirdBizId")));
                    Map<Long, String> thirdBizId2ThirdPrimaryKey = thirdSchoolService.getThirdSchoolByIdList(
                        MapUtil.of("thirdPartyId", thirdPartyId, "thirdSchoolIdList", schoolId2ThirdBizId.values())).stream()
                      .collect(toMap(x -> MapUtils.getLong(x, "thirdSchoolId"), x -> MapUtils.getString(x, "thirdPrimaryKey")));
                    schoolId2ThirdBizId.forEach((schoolId, thirdBizId) -> {
                        if (thirdBizId2ThirdPrimaryKey.containsKey(thirdBizId)) {
                            schoolId2ThirdPrimaryKey.put(schoolId, thirdBizId2ThirdPrimaryKey.get(thirdBizId));
                        }
                    });
                }
            }
          );

        List<ExamInfoDTO> examInfoDTOList = new ArrayList<>(examList.size());
        Map<Integer, String> examTypeValue2Name = getExamTypeValue2Name();
        examList.stream().filter(x -> schoolId2ThirdPrimaryKey.containsKey(MapUtils.getLong(x, "schoolId")))
          .collect(Collectors.groupingBy(x -> MapUtils.getLong(x, "examId")))
          .forEach((examId, list) -> {
              Map<String, Object> exam = list.get(0);
              ExamInfoDTO examInfoDTO = new ExamInfoDTO();
              examInfoDTO.setExamId(examId);
              examInfoDTO.setExamName(MapUtils.getString(exam, "examName"));
              examInfoDTO.setExamType(examTypeValue2Name.get(MapUtils.getInteger(exam, "examType")));
              examInfoDTO.setExamDate(MapUtils.getString(exam, "startDate"));
              examInfoDTO.setSchools(list.stream().sorted(Comparator.comparingLong(x -> MapUtils.getLong(x, "schoolId"))).map(x -> {
                  ThirdSchoolInfoDTO thirdSchoolInfoDTO = new ThirdSchoolInfoDTO();
                  thirdSchoolInfoDTO.setSchoolId(schoolId2ThirdPrimaryKey.get(MapUtils.getLong(x, "schoolId")));
                  thirdSchoolInfoDTO.setSchoolName(MapUtils.getString(x, "schoolName"));
                  return thirdSchoolInfoDTO;
              }).collect(Collectors.toList()));
              examInfoDTOList.add(examInfoDTO);
          });
        examInfoDTOList.sort(Comparator.comparing(ExamInfoDTO::getExamDate).reversed().thenComparing(ExamInfoDTO::getExamId).reversed());
        return examInfoDTOList;
    }

    public List<ExamInfoWithoutSchoolDTO> getExamListBySchoolAndTime(SchExamListParam param) {
        String schoolEId = param.getSchoolEId();
        Long startTime = param.getStartTime();
        Long endTime = param.getEndTime();
        if (StringUtils.isEmpty(schoolEId) || !ObjectUtil.isValidId(startTime) || !ObjectUtil.isValidId(endTime)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
              "请求参数异常！schoolEId：" + schoolEId + "，startTime：" + startTime + "，endTime：" + endTime);
        }
        // 时间跨度最长不能超过一年
        if (isTimeUnavailable(startTime, endTime)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "时间参数异常！startTime：" + startTime + "，endTime：" + endTime);
        }

        Long thirdPartyId = HaozhuanyeDataUtils.getThirdPartyId();
        Long schoolId = examSchoolStudentStatService.convertSchoolId(schoolEId, thirdPartyId);
        String startDate = DateUtil.formatDateTime(new Date(startTime));
        String endDate = DateUtil.formatDateTime(new Date(endTime));
        List<Map<String, Object>> examList = commonRepository.selectList("ExamListMapper.getExamListBySchoolAndDate",
          MapUtil.of("schoolId", schoolId, "startDate", startDate, "endDate", endDate));
        return convertExamList(examList);
    }

    /**
     * 根据学校、报告刷新时间获取学校的考试列表
     */
    public List<ExamInfoWithoutSchoolDTO> getExamListBySchoolAndStatTime(SchExamListParam param) {
        String schoolEId = param.getSchoolEId();
        Long startTime = param.getStartTime();
        if (StringUtils.isEmpty(schoolEId) || !ObjectUtil.isValidId(startTime)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
              "请求参数异常！schoolEId：" + schoolEId + "，startTime：" + startTime);
        }
        // 最多只能获取近一年的数据
        if (isTimeUnavailable(startTime, System.currentTimeMillis())) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "时间参数异常！startTime：" + startTime);
        }

        Long thirdPartyId = HaozhuanyeDataUtils.getThirdPartyId();
        Long schoolId = examSchoolStudentStatService.convertSchoolId(schoolEId, thirdPartyId);
        List<Long> examIds = taskService.getExamIdsByTaskCompleteTime(startTime);
        if (examIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> examList = new ArrayList<>(examIds.size());
        Map<String, Object> params = new HashMap<>(2);
        params.put("schoolId", schoolId);
        BatchDataUtil.execute(examIds, x -> {
            params.put("examIds", x);
            examList.addAll(commonRepository.selectList("ExamListMapper.getExamListBySchoolAndExamIds", params));
        });
        return convertExamList(examList);
    }

    private List<ExamInfoWithoutSchoolDTO> convertExamList(List<Map<String, Object>> examList) {
        if (examList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Integer, String> examTypeValue2Name = getExamTypeValue2Name();
        return examList.stream().map(x -> {
            ExamInfoWithoutSchoolDTO examInfoDTO = new ExamInfoWithoutSchoolDTO();
            examInfoDTO.setExamId(MapUtils.getLong(x, "examId"));
            examInfoDTO.setExamName(MapUtils.getString(x, "examName"));
            examInfoDTO.setExamType(examTypeValue2Name.get(MapUtils.getInteger(x, "examType")));
            examInfoDTO.setExamDate(MapUtils.getString(x, "startDate"));
            return examInfoDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 判断时间参数是否不可用 时间跨度最长不能超过一年
     * @return true：不可用  false：可用
     */
    private boolean isTimeUnavailable(long startTime, long endTime) {
        return (endTime - startTime) > ONE_YEAR_IN_MILLIS;
    }

    /**
     * 根据学校+年级+课程+开始时间获取考试数量（只取考试状态是已完成的，且不包含异步联考）
     */
    public int getExamCountBySchoolGradeAndCourseAndStartDate(WrongBookEffectivenessExamQuery query) {
        if (CollectionUtils.isEmpty(query.getCourseIds())) {
            return 0;
        }
        return commonRepository.selectOne("ExamListMapper.getExamCountBySchoolGradeAndCourseAndStartDate", query);
    }

    /**
     * 根据学校+年级+课程+开始时间获取考试列表（只取考试状态是已完成的，且不包含异步联考）
     */
    public List<WrongBookExamVO> getExamListBySchoolGradeAndCourseAndStartDate(WrongBookEffectivenessExamQuery query) {
        if (CollectionUtils.isEmpty(query.getCourseIds())) {
            return Collections.emptyList();
        }
        return commonRepository.selectList("ExamListMapper.getExamListBySchoolGradeAndCourseAndStartDate", query);
    }

    private Map<Integer, String> getExamTypeValue2Name() {
        Map<Integer, String> examTypeValue2Name = new HashMap<>();
        Map<String, Object> examTypeDict = DictUtil.getDict("examType");
        if (MapUtils.isNotEmpty(examTypeDict)) {
            for (Map.Entry<String, Object> entry : examTypeDict.entrySet()) {
                Map<String, Object> map = MapUtil.getMap(entry.getValue());
                examTypeValue2Name.put(MapUtils.getInteger(map, "value"), MapUtils.getString(map, "label"));
            }
        }
        return examTypeValue2Name;
    }

    /**
     * 为考试列表填充字段，用来判断考试是原本就能看到，还是因为设置了单独可见账号才能看到
     * @param examList 考试列表
     * @param examIds 原本就能看到的考试ids
     * @return 填充了字段后的考试列表
     */
    private List<Map<String, Object>> fillExamExtraVisibleFlag(List<Map<String, Object>> examList, Set<Long> examIds){
        examList.forEach(exam -> {
            if (!examIds.contains(MapUtils.getLong(exam, "examId"))) {
                exam.put("isExtraVisible", true);
            }
        });
        return examList;
    }

    /**
     * 解析使用逗号隔开的参数
     *
     * @param params 被赋值的参数
     * @param key    key
     * @param value  value
     */
    private void splitSearchParams(Map<String, Object> params, String key, Object value) {
        if (!ObjectUtil.isBlank(value)) {
            params.put(key, value.toString().split(","));
        }
    }

}
