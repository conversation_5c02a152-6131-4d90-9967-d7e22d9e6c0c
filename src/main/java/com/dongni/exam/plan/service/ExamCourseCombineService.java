package com.dongni.exam.plan.service;

import com.dongni.basedata.admin.service.IBaseAreaService;
import com.dongni.basedata.school.classes.service.impl.ClassTeacherServiceImpl;
import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by l<PERSON><PERSON><PERSON>
 * time: 10:32 2019/08/20
 * description:组合排名
 */
@Service
public class ExamCourseCombineService {

    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private CourseServiceImpl courseService;
    @Autowired
    private IBaseAreaService baseAreaService;
    @Autowired
    private ClassTeacherServiceImpl classTeacherService;


    /**
     * 获取组合排名列表
     * @return 组合排名列表
     */
    public Map<String,Object> getCourseCombine(Map<String,Object> params){
        Verify.of(params)
                .isNotBlank("examId")
                .isNotBlank("statId")
                .verify();
        Map<String,Object> result = new HashMap<>();
        List<Long> schoolAreaIdList = commonRepository.selectList("ExamMapper.getExamSchoolAreaId", params);
        Long areaId = schoolAreaIdList.get(0);
        params.put("areaId", areaId);
        Map<String, Object> area = baseAreaService.getArea(params);
        String[] areaCode = area.get("areaCode").toString().split(",");
        //取省份的areaCode
        params.put("areaCode", areaCode[0]);
        Map<String, Object> areaObj = baseAreaService.getAreaId(params);
        //查询所有课程组合
        params.put("areaId", areaObj.get("areaId"));
        List<Map<String, Object>> courseCombineList = courseService.getCourseCombine(params);
        result.put("list",commonRepository.selectList("ExamCourseCombineMapper.getCourseCombine",params));
        if (courseCombineList.size()==12){
            result.put("selectType",1);
        }else if (courseCombineList.size()==20){
            result.put("selectType",2);
        }else {
            result.put("selectType",3);
        }
        return result;
    }


    public void deleteCourseCombine(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("examCourseGroupId")
                .verify();
        commonRepository.delete("ExamCourseCombineMapper.deleteCourseCombine",params);
    }

    /**
     * 根据examId+statId删除报告组合排名的配置
     */
    public void deleteCourseCombineByExamIdAndStatId(Map<String,Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isNumeric("statId")
          .verify();
        commonRepository.delete("ExamCourseCombineMapper.deleteCourseCombineByExamIdAndStatId",params);
    }

    public void batchInsertCourseCombine(Map<String,Object> params){
        commonRepository.insert("ExamCourseCombineMapper.batchInsertCourseCombine",params);
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void addCourseCombine(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("list")
                .verify();
        List<Map<String, Object>> list = (List) params.get("list");
        for (Map<String, Object> map : list) {
            Verify.of(map)
                    .isNotBlank("examId")
                    .isNotBlank("statId")
                    .isNotBlank("courseName")
                    .isNotBlank("memberStr")
                    .verify();
            map.put("userId", params.get("userId"));
            map.put("userName", params.get("userName"));
            map.put("currentTime", DateUtil.getCurrentDateTime());
            //先查询有无重名的组合
            Integer count1 = commonRepository.selectOne("ExamCourseCombineMapper.getExistCourseCombineByCourseName", map);
            if (!count1.equals(0)) {
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "组合课程名重复");
            }
            List<Long> courseIds = StringUtil.strToList(map.get("memberStr").toString(), ",", Long.class);
            int foreignLang = MapUtils.getInteger(map, "foreignLang", 0);
            //单个科目不允许有组合
            if (courseIds.size() + foreignLang < 2){
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "至少要选择两个科目");
            }
            //从小到大排序
            courseIds = courseIds.stream().sorted(Comparator.comparing(s -> s)).collect(Collectors.toList());
            map.put("memberStr", StringUtils.join(courseIds, ","));
            //在这里不对以前用户自己添加的数据进行处理
            Integer count2 = commonRepository.selectOne("ExamCourseCombineMapper.getExistCourseCombineByMemberStr", map);
            if (!count2.equals(0)) {
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "组合课程已存在");
            }
            //查询是否有该组合
            Map<String, Object> course = courseService.getCourseByMemberStrAndForeignLang(map);
            Long courseId;
            if (MapUtils.isEmpty(course)) {
                //初始化组合，插入course表
                Map<String, Object> newCourseCombine = new HashMap<>();
                newCourseCombine.put("courseName", map.get("courseName"));
                newCourseCombine.put("courseSort", courseService.getMaxCourseSort() + 1);
                newCourseCombine.put("courseType", 3);
                newCourseCombine.put("stage", 3);
                newCourseCombine.put("artsScience", 0);
                newCourseCombine.put("memberCount", courseIds.size());
                newCourseCombine.put("memberStr", map.get("memberStr"));
                newCourseCombine.put("foreignLang", foreignLang);
                newCourseCombine.put("userId",map.get("userId"));
                newCourseCombine.put("userName",map.get("userName"));
                courseId = courseService.insertSimpleCourse(newCourseCombine);
            } else {
                courseId = Long.valueOf(course.get("courseId").toString());
            }
            map.put("courseId", courseId);
            map.putIfAbsent("foreignLang", foreignLang);
            commonRepository.insert("ExamCourseCombineMapper.addCourseCombine", map);
        }
    }

    public Map<String,Object> getCourseRemark(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("examId")
                .verify();
        Map<String,Object> result = new HashMap<>();
        List<Long> classIds = commonRepository.selectList("ExamCourseCombineMapper.getExamClass",params);
        params.put("classIds",classIds);
        Integer count = classTeacherService.getCourseRemarkCount(params);
        if (count.equals(0)){
            result.put("courseRemark",false);
        }else {
            result.put("courseRemark",true);
        }
        return result;
    }

    public List<Long> getExamSchoolAreaId(Map<String,Object> params){
        return commonRepository.selectList("ExamMapper.getExamSchoolAreaId", params);
    }

    public List<Map<String,Object>> getScoreLevelChange(Map<String,Object> params){
        List<Map<String, Object>> list = commonRepository.selectList("ExamCourseCombineMapper.getScoreLevelChange", params);
        //比例转成字符串
        for (Map<String,Object> map : list){
            map.put("openRatio",map.get("openRatio").toString());
            map.put("closeRatio",map.get("closeRatio").toString());
            map.put("totalRatio",map.get("totalRatio").toString());
        }
        return list;
    }

    /**
     * 根据t_exam_school中的学校的区域获取考试的省份
     * @param params examId
     * @return
     */
    public Map<String,Object> getProvince(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("examId")
                .verify();
        List<Long> schoolAreaIdList = commonRepository.selectList("ExamMapper.getExamSchoolAreaId", params);
        if (CollectionUtils.isEmpty(schoolAreaIdList) || baseAreaService.getAreaListByIds(MapUtil.of("areaIds", schoolAreaIdList)).stream()
          .map(x -> x.get("areaCode").toString().split(",")[0]).collect(Collectors.toSet()).size() > 1) {
            //考试的学校不存在 或者 考试的学校分布在两个省份，那就没法给出准确的省份
            return Collections.emptyMap();
        }
        Long areaId = schoolAreaIdList.get(0);
        params.put("areaId", areaId);
        Map<String, Object> area = baseAreaService.getArea(params);
        String[] areaCode = area.get("areaCode").toString().split(",");
        //取省份的areaCode
        params.put("areaCode", areaCode[0]);
        return baseAreaService.getAreaId(params);
    }

    public List<Map<String,Object>> getScoreLevelChangeCourse(Map<String,Object> params) {
        return commonRepository.selectList("ExamCourseCombineMapper.getScoreLevelChangeCourse",params);
    }
}
