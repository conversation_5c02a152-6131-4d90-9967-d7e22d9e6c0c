package com.dongni.exam.plan.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.view.monitor.service.ExamClassStatService;
import com.dongni.analysis.view.report.service.ExamStatReportService;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.clazz.bean.ClassInfoDTO;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.export.grade.service.CommonGradeService;
import com.dongni.basedata.export.school.service.CommonSchoolService;
import com.dongni.basedata.export.user.service.CommonUserService;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataClassStudentService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.ExamSchoolCustomSortService;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.FullMarkVO;
import com.dongni.exam.plan.bean.ExamWorkerDTO;
import com.dongni.exam.plan.score.imports.service.ScoreImportExamPaperService;
import com.dongni.exam.question.service.ExamQuestionStructureService;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.entrust.service.dongni.EntrustQueryService;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.pugwoo.wooutils.json.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;

import static com.dongni.common.utils.DictUtil.getDictValue;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

/**
 * Created by Heweipo on 2018/12/11.
 *
 * 考试信息跟踪
 */
@Service
public class ExamTraceService {

    private final Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private BaseDataRepository baseDataRepository;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private ExamClassStatService examClassStatService;
    @Autowired
    private ExamStatReportService examStatReportService;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private CommonGradeService commonGradeService;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private PaperManager paperManager;
    @Autowired
    private ScoreImportExamPaperService scoreImportExamPaperService;
    @Autowired
    private ExamQuestionStructureService questionStructureService;
    @Autowired
    private EntrustQueryService entrustQueryService;
    @Autowired
    private ExamConfigService examConfigService;
    @Autowired
    private ExamService examService;
    @Autowired
    private ExamUnionNewService examUnionNewService;
    @Autowired
    private NewExamWorkerService newExamWorkerService;
    @Autowired
    private IQsClientService qsClientService;
    @Autowired
    private ExamClassService examClassService;
    @Autowired
    private ExamStudentService examStudentService;
    @Autowired
    private IBaseDataClassStudentService baseDataClassStudentService;

    private final MongoDatabase mongoDatabase;

    private final MongoDatabase analysisMongodb;
    @Autowired
    private ExamSchoolCustomSortService examSchoolCustomSortService;


    @Autowired
    public ExamTraceService(TikuMongodb tikuMongodb,AnalysisMongodb analysisMongodb){
        this.mongoDatabase = tikuMongodb.getMongoDatabase();
        this.analysisMongodb = analysisMongodb.getMongoDatabase();
    }

    /**
     * 获取考试跟踪的详细信息，包括关联信息（科目，班级，试卷，状态）
     *
     * @param params examId schoolId
     * @return 考试跟踪
     */
    @Transactional(ExamRepository.TRANSACTION)
    public Map<String,Object> getExamPlanTrace(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("examId").isValidId("schoolId").verify();

        // 获取考试基本信息
        Map<String, Object> exam = examService.getExamDetail(params);

        Long examId = MapUtils.getLong(params, "examId");

        // 判断阅卷方式（班级阅卷、试题阅卷）
        int correctMode = Integer.parseInt(exam.get("correctMode").toString());
        boolean isReadByClass = DictUtil.getDictValue("correctMode","readByClass") == correctMode;

        // 查询考试学校、班级和考试科目
        List<Map<String, Object>> examSchool = commonRepository.selectList("ExamTraceMapper.getExamSchool", params);
        List<Map<String, Object>> examClass = commonRepository.selectList("ExamTraceMapper.getExamClass", params);
        List<ExamWorkerDTO> subjectLeaders = newExamWorkerService.getSubjectLeaderByExamId(examId);
        String principle = StringUtils.join(subjectLeaders.stream().map(ExamWorkerDTO::getWorkerName).collect(toSet()), ",");
        List<Map<String, Object>> examPaper = commonRepository.selectList("ExamTraceMapper.getExamPaper",params);
        fillPrincipleToExamPaper(examPaper, examId);
        List<Long> paperIds = examPaper.stream().map(e -> (Long) e.get("paperId")).collect(Collectors.toList());
        List<Map<String, Object>> examCourse = commonRepository.selectList("ExamTraceMapper.getExamCourse",params);
        List<Map<String, Object>> examClassPaper = commonRepository.selectList("ExamTraceMapper.getExamClassPaper",params);

        //是否展示考试课程管理、答题卡预览等模块
        exam.put("enableEditPaper", !examUnionNewService.examSaveUnFinished(examId));
        List<Long> examStatPaperIds = examPaperService.getExamPaperIdsHavingResult(examId, paperIds);

        // 不知道为什么去basedata库用gradeId取stage t_exam里面有stage字段 保留原有逻辑
        // 如果gradeId存在拿得到stage就用t_grade的stage值
        // 如果拿不到就用t_exam里面的
//        Integer gradeId = commonRepository.selectOne("ExamTraceMapper.getExamGradeId", params);
//        Integer examStage = getExamStage(MapUtil.of("gradeId", gradeId));
//        if (examStage != null) {
//            exam.put("stage", examStage);
//        }

        //通过paperIds查询成绩导入模式的试卷
        Map<String, Map<String, Object>> paperScoreImportMap = new HashMap<>();
        Map<String, Integer> questionStructureCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(paperIds)) {

            Map<String, Object> paperParams = new HashMap<>();
            paperParams.put("paperIdList", paperIds);

            // 试卷信息
            List<Map<String, Object>> paperScoreImportList = scoreImportExamPaperService.getExamPaperScoreImportListByPaperIds(paperParams);
            paperScoreImportMap = paperScoreImportList
                    .stream()
                    .collect(toMap(item -> MapUtil.getString(item, "paperId"), item -> item));

            // 试题数量
            List<Map<String, Object>> questionStructureCountList = questionStructureService.getQuestionCountByPaperIds(paperParams);

            questionStructureCountMap = questionStructureCountList
                    .stream()
                    .collect(Collectors.toMap(item -> MapUtil.getString(item, "paperId"), item -> MapUtil.getInt(item, "questionCount")));
        }

        //查询mongo获得creationType
        List<Document> paperList=paperManager.getPaperSimple(paperIds);
        Map<Long,Document> paperMap=paperList.stream().collect(toMap(p->(Long) p.get("paperId"),p->p));

        // 获取报告配置
        if(ObjectUtil.isNotBlank(params.get("statId")) && MapUtils.getLong(params, "statId") != 0){
            Map<String,Object> stat = examStatReportService.getExamStatDetail(params);
            List<Map<String,Object>> statSchool = (List<Map<String, Object>>) stat.get("examSchool");
            List<Map<String,Object>> statCourse = (List<Map<String, Object>>) stat.get("examCourse");

            List<Map<String,Object>> statClass = extractKeyList(statSchool,"examClass","schoolId");
            List<Map<String,Object>> statPaper = extractKeyList(statCourse,"examPaper","courseId");
            List<Map<String,Object>> statSchoolPaper = extractKeyList(statPaper,"examSchoolPaper","paperId");
            List<Map<String,Object>> statClassPaper = extractKeyList(statSchoolPaper,"examClassPaper","paperId");

            removeKey(examSchool,statSchool,"schoolId");
            removeKey(examCourse,statCourse,"courseId");
            removeKey(examPaper,statPaper,"paperId");

            Long schoolId = MapUtils.getLong(params, "schoolId");
            statClassPaper = statClassPaper.stream().filter(s -> ObjectUtil.isValueEquals(s.get("schoolId"), schoolId))
              .collect(Collectors.toList());
            statClass = statClass.stream().filter(s -> ObjectUtil.isValueEquals(s.get("schoolId"), schoolId)).collect(Collectors.toList());

            int readComplete = DictUtil.getDictValue("examPaperStatus","readComplete");
            Map<String,Map<String,Object>> lsp = examClassPaper.stream().collect(toMap(c->c.get("paperId")+"_"+c.get("classId"), c->c));
            for (Map<String,Object> cs : statClassPaper){
                String key = cs.get("paperId")+"_"+cs.get("classId");
                if(lsp.containsKey(key)){
                    cs.put("classStatus",lsp.get(key).get("classStatus"));
                }else {
                    // 自定义报告里面默认班级状态为阅卷完成，因为教学班和行政班互换的话很难判断班级是否完成
                    cs.put("classStatus",readComplete);
                }
            }
            examClassPaper = statClassPaper;

            Map<Long, Integer> classId2ClassStatus = examClass.stream()
              .collect(toMap(c -> MapUtils.getLong(c, "classId"), c -> MapUtils.getInteger(c, "classStatus")));
            statClass.forEach(s -> s.put("classStatus", classId2ClassStatus.getOrDefault(MapUtils.getLong(s, "classId"), readComplete)));
            examClass = statClass;
        }

        if (CollectionUtils.isNotEmpty(examClass)) {
            List<Map<String, Object>> classList = commonClassService.getClassType(MapUtil.of("classIds",
              examClass.stream().map(m -> m.get("classId").toString()).collect(joining(","))));
            Map<Long, Map<String, Object>> classIdMap = classList.stream()
              .collect(toMap(o -> Long.parseLong(o.get("classId").toString()), o -> o));
            for (Map<String, Object> c : examClass) {
                Long classId = Long.valueOf(c.get("classId").toString());
                if (classIdMap.containsKey(classId)) {
                    c.putAll(classIdMap.get(classId));
                }
                c.put("principle", principle);
            }
        }

        //数据组装
        Map<String, List<Map<String, Object>>> pm = examClassPaper.stream().collect(Collectors.groupingBy(c -> c.get("paperId").toString()));

        List<String> classIdList = examClassPaper.stream().map(classMap -> classMap.get("classId").toString()).collect(Collectors.toList());
        List<Map<String, Object>> classSortList = CollectionUtils.isEmpty(classIdList)
                ? Collections.emptyList()
                : baseDataRepository.selectList("ClassesMapper.getClassSort", classIdList);
        Map<Long, Map<String,Object>> classIdMap = classSortList.stream().collect(toMap(o -> Long.parseLong(o.get("classId").toString()), o -> o));

        // 查询试卷委托录题状态
        Map<String, Object> entrustParams = new HashMap<>();
        entrustParams.put("paperIdList", paperIds);
        List<Map<String, Object>> entrustPaper = entrustQueryService.getEntrustByPaperIds(entrustParams);
        Map<String, Map<String, Object>> entrustPaperMap = entrustPaper
                .stream()
                .collect(toMap(item -> MapUtil.getString(item, "paperId"), item -> item));

        for (Map<String, Object> paper : examPaper) {
            String paperId = paper.get("paperId").toString();
            Document d = paperMap.get(Long.valueOf(paperId));

            Map<String, Object> entrustPaperItem = entrustPaperMap.get(paperId);

            Map<String, Object> paperScoreItem = paperScoreImportMap.get(paperId);
            Integer questionCount = questionStructureCountMap.get(paperId);
            paper.put("paperEntryType", 1);

            if (MapUtils.isNotEmpty(paperScoreItem) && ObjectUtil.isNotBlank(questionCount)) {

                if (questionCount <= 1) {
                    paper.put("paperEntryType", DictUtil.getDictValue("entryType", "importByPaper"));
                } else {
                    // 小题分模式判断是否已经录题，已录题则设置为-1，不显示上传文档录题按钮
                    if (MapUtils.isEmpty(entrustPaperItem)) {
                        paper.put("paperEntryType", DictUtil.getDictValue("entryType", "importByQuestion"));
                    } else {
                        paper.put("paperEntryType", -1);
                        int entrustStatus = MapUtil.getInt(entrustPaperItem, "entrustStatus");
                        boolean isDraft = DictUtil.isEquals(entrustStatus, "entrustStatus", "draft");
                        // 如果未开始录题则需要显示上传按钮（entrustId为空 || entrustId不为空但entrustStatus == 1）
                        if (ObjectUtil.isBlank(d.get("entrustId")) ||
                                (ObjectUtil.isNotBlank(d.get("entrustId")) && isDraft)) {
                            paper.put("paperEntryType", DictUtil.getDictValue("entryType", "importByQuestion"));
                        }
                    }
                }

            }


            if (d!=null){
                paper.put("answerCardStatus", d.get("answerCardStatus"));
                paper.put("entrustId", d.get("entrustId"));
                paper.put("creationType",d.get("creationType"));
            }
            List<Map<String, Object>> list = pm.get(paperId);
            if(CollectionUtils.isEmpty(list)){
                paper.put("classList", Collections.emptyList());
                continue;
            }

            list.forEach(l->{
                Long classId = Long.valueOf(l.get("classId").toString());
                if(classIdMap.containsKey(classId)){
                    l.putAll(classIdMap.get(classId));
                }
            });
            list.sort(Comparator.comparing(e->Long.valueOf(e.getOrDefault("classSort",0).toString())));
            paper.put("classList", list);

            paper.put("adjustClass", examStatPaperIds.contains(Long.parseLong(paperId)));
        }

        Map<String, List<Map<String, Object>>> ep = examPaper.stream().collect(Collectors.groupingBy(c -> c.get("courseId").toString()));

        //课程基础信息
        StringBuilder sb = new StringBuilder();
        examCourse.forEach(e-> sb.append(e.get("courseId")).append(","));
        params.put("courseId",sb.toString());
        List<Map<String, Object>> baseCourseList = commonCourseService.getCourseList(params);
        Map<String, Map<String, Object>> courseId2BaseInfo = baseCourseList.stream()
                .collect(Collectors.toMap(c -> c.get("courseId").toString(),c->c));
        
        // 课程不存在的错误信息 有些发布考试之后把课程删了 直接抛异常
        List<Map<String, Object>> examCourseNotExistInBaseList = new ArrayList<>();
        for (Map<String, Object> course : examCourse) {
            String courseId = course.get("courseId").toString();
            course.put("paperList", ep.get(courseId));
            Map<String, Object> baseCourseInfo = courseId2BaseInfo.get(courseId);
            if (MapUtils.isEmpty(baseCourseInfo)) {
                examCourseNotExistInBaseList.add(course);
            } else {
                course.putAll(baseCourseInfo);
            }
        }
        
        if (CollectionUtils.isNotEmpty(examCourseNotExistInBaseList)) {
            List<String> baseCourseNotExistErrMsgList = new ArrayList<>();
            baseCourseNotExistErrMsgList.add("考试科目在基础数据被删除: ");
            for (Map<String, Object> course : examCourseNotExistInBaseList) {
                baseCourseNotExistErrMsgList.add("　　" + course.get("courseName") + "(courseId:" + course.get("courseId") + ")");
            }
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, JSON.toJson(baseCourseNotExistErrMsgList));
        }

        //班级得分情况
        List<Map<String, Object>> classScore = examClassStatService.getClassScore(params);
        if (CollectionUtils.isNotEmpty(classScore)){
            //合并考试后同个班级会有多条数据,需要重新刷新报告
            Map<String, Map<String, Object>> cm = classScore.stream().collect(toMap(l -> l.get("classId").toString(), l -> l, (x, y) -> x));
            for (Map<String, Object> c : examClass) {
                if (cm.containsKey(c.get("classId").toString())){
                    c.putAll(cm.get(c.get("classId").toString()));
                }
            }
        }

        //添加classSort
        examClass.sort(Comparator.comparing(e->Long.valueOf(e.getOrDefault("classSort",0).toString())));

        // 数据返回
        exam.put("examSchool", examSchool);
        exam.put("examClass", examClass);
        exam.put("examCourse", examCourse);
        exam.put("isReadByClass", isReadByClass);
        //考试各个学校分别考了哪些试卷
        List<Map<String, Object>> examSchoolCoursePaper = commonRepository.selectList("ExamTraceMapper.getExamSchoolCoursePaper",params);
        exam.put("examSchoolCoursePaper", examSchoolCoursePaper);
        return exam;
    }

    /**
     * 校内考新建报告时，获取在第二步可以显示的班级列表（总分的统计班级）
     * @param examId 考试id
     * @return 班级列表
     */
    public List<Map<String, Object>> getExamPlanSchoolStatClass(Long examId) {
        Map<String, Object> examDetail = examService.getExamDetail(MapUtil.of("examId", examId));
        if (examService.checkExamTypeUnion(examDetail)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> examClassList = examClassService.getExamClassList(examDetail);
        Set<Long> examClassIdSet = examClassList.stream().map(x -> MapUtils.getLong(x, "classId")).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(examClassList)) {
            Map<Long, Integer> classId2Sort = commonClassService.getClassType(MapUtil.of("classIds", StringUtils.join(examClassIdSet, ",")))
              .stream().collect(toMap(x -> MapUtils.getLong(x, "classId"), x -> MapUtils.getInteger(x, "classSort")));
            examClassList.forEach(x -> x.put("classSort", classId2Sort.getOrDefault(MapUtils.getLong(x, "classId"), 0)));
        }
        List<Long> studentIdList = examStudentService.getExamStudentIdList(examDetail);
        Set<Long> adminClassIdsByStudentIds = new HashSet<>(baseDataClassStudentService.getAdminClassIdsByStudentIds(studentIdList));
        if (examClassIdSet.containsAll(adminClassIdsByStudentIds)) {
            examClassList.sort(Comparator.comparing(x -> MapUtils.getInteger(x, "classSort")));
            return examClassList;
        }
        List<ClassInfoDTO> classInfoByIds = commonClassService.getClassInfoByIds(new ArrayList<>(adminClassIdsByStudentIds));
        examClassList.addAll(classInfoByIds.stream().filter(x -> !examClassIdSet.contains(x.getClassId())).map(x -> {
            Map<String, Object> classInfo = new HashMap<>(8);
            classInfo.put("examId", examId);
            classInfo.put("schoolId", x.getSchoolId());
            classInfo.put("gradeId", x.getGradeId());
            classInfo.put("classId", x.getClassId());
            classInfo.put("className", x.getClassName());
            classInfo.put("classType", x.getClassType());
            classInfo.put("classSort", x.getClassSort());
            classInfo.put("artsScience", x.getArtsScience());
            return classInfo;
        }).collect(Collectors.toList()));
        examClassList.sort(Comparator.comparing(x -> MapUtils.getInteger(x, "classSort")));
        return examClassList;
    }

    public static List<Map<String,Object>> extractKeyList(List<Map<String,Object>> parent, String key, String store){
        List<Map<String,Object>> rs = new ArrayList<>();
        for (Map<String,Object> p : parent){
            List<Map<String,Object>> r = (List<Map<String, Object>>) p.get(key);
            if(store != null){
                r.forEach(k->k.put(store,p.get(store)));
            }
            rs.addAll(r);
        }
        return rs;
    }

    public static void removeKey(List<Map<String,Object>> target, List<Map<String,Object>> filter, String key){
        Iterator<Map<String, Object>> iterator = target.iterator();
        Set<Long> exist = filter.stream().map(s->Long.valueOf(s.get(key).toString())).collect(Collectors.toSet());
        while (iterator.hasNext()){
            Map<String, Object> next = iterator.next();
            if(!exist.contains(Long.valueOf(next.get(key).toString()))) {
                iterator.remove();
            }
        }
    }

    /**
     * 获取考试(联考)跟踪的详细信息，包括关联信息（科目，班级，试卷，状态）
     *
     * @param params examId
     * @return 考试跟踪
     */
    public Map<String,Object> getExamUnionPlanTrace(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("examId").verify();

        // 获取考试基本信息
        Map<String, Object> exam = examService.getExamDetail(params);
        Long examId = MapUtils.getLong(params, "examId");

        // 判断阅卷方式（班级阅卷、试题阅卷）
        int correctMode = Integer.parseInt(exam.get("correctMode").toString());
        boolean isReadByClass = DictUtil.getDictValue("correctMode","readByClass") == correctMode;

        // 查询考试学校、班级和考试科目
        List<Map<String, Object>> examSchool = commonRepository.selectList("ExamTraceMapper.getExamSchool", params);
        List<Map<String, Object>> examCourse = commonRepository.selectList("ExamTraceMapper.getExamCourse",params);
        List<Map<String, Object>> examPaper = commonRepository.selectList("ExamTraceMapper.getExamPaper",params);
        fillPrincipleToExamPaper(examPaper, examId);
        List<Long> paperIds = examPaper.stream().map(e -> MapUtil.getLong(e, "paperId")).collect(Collectors.toList());

        //是否展示考试课程管理、答题卡预览等模块  即是否所有试卷都走完了发布流程
        exam.put("enableEditPaper", !examUnionNewService.examSaveUnFinished(examId));
        List<Long> examStatPaperIds = examPaperService.getExamPaperIdsHavingResult(examId, paperIds);

        //获取联考是否有原始报告，有的话说明考试至少有一张试卷是走完了发布流程的状态，此时才可以去点击查看报告进入报告、工具箱等界面
        exam.put("havingDefaultStat", CollectionUtils.isNotEmpty(commonRepository.selectList("ExamUnionNewMapper.getExamHavingDefaultStat",
          MapUtil.of("examIds", Collections.singletonList(examId)))));

        //通过paperIds查询成绩导入模式的试卷
        Map<String, Map<String, Object>> paperScoreImportMap = new HashMap<>();
        Map<String, Integer> questionStructureCountMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(paperIds)) {

            Map<String, Object> paperParams = new HashMap<>();
            paperParams.put("paperIdList", paperIds);

            // 试卷信息
            List<Map<String, Object>> paperScoreImportList = scoreImportExamPaperService.getExamPaperScoreImportListByPaperIds(paperParams);
            paperScoreImportMap = paperScoreImportList
                    .stream()
                    .collect(toMap(item -> MapUtil.getString(item, "paperId"), item -> item));

            // 试题数量
            List<Map<String, Object>> questionStructureCountList = questionStructureService.getQuestionCountByPaperIds(paperParams);

            questionStructureCountMap = questionStructureCountList
                    .stream()
                    .collect(Collectors.toMap(item -> MapUtil.getString(item, "paperId"), item -> MapUtil.getInt(item, "questionCount")));

            // 查询试卷委托录题状态
            Map<String, Object> entrustParams = new HashMap<>();
            entrustParams.put("paperIdList", paperIds);
            List<Map<String, Object>> entrustPaper = entrustQueryService.getEntrustByPaperIds(entrustParams);
            Map<String, Map<String, Object>> entrustPaperMap = entrustPaper
                    .stream()
                    .collect(toMap(item -> MapUtil.getString(item, "paperId"), item -> item));

            //查询mongo获得creationType
            List<Document> paperList=paperManager.getPaperSimple(paperIds);
            Map<Long,Document> paperMap=paperList.stream().collect(toMap(p-> MapUtil.getLong(p, "paperId"),p->p));

            for (Map<String, Object> paper : examPaper) {
                String paperId = paper.get("paperId").toString();
                Document d = paperMap.get(Long.valueOf(paperId));

                Map<String, Object> entrustPaperItem = entrustPaperMap.get(paperId);

                Map<String, Object> paperScoreItem = paperScoreImportMap.get(paperId);
                Integer questionCount = questionStructureCountMap.get(paperId);
                paper.put("paperEntryType", 1);

                if (MapUtils.isNotEmpty(paperScoreItem) && ObjectUtil.isNotBlank(questionCount)) {

                    if (questionCount <= 1) {
                        paper.put("paperEntryType", DictUtil.getDictValue("entryType", "importByPaper"));
                    } else {
                        // 小题分模式判断是否已经录题，已录题则设置为-1，不显示上传文档录题按钮
                        if (MapUtils.isEmpty(entrustPaperItem)) {
                            paper.put("paperEntryType", DictUtil.getDictValue("entryType", "importByQuestion"));
                        } else {
                            paper.put("paperEntryType", -1);
                            int entrustStatus = MapUtil.getInt(entrustPaperItem, "entrustStatus");
                            boolean isDraft = DictUtil.isEquals(entrustStatus, "entrustStatus", "draft");
                            // 如果未开始录题则需要显示上传按钮（entrustId为空 || entrustId不为空但entrustStatus == 1）
                            if (ObjectUtil.isBlank(d.get("entrustId")) ||
                                    (ObjectUtil.isNotBlank(d.get("entrustId")) && isDraft)) {
                                paper.put("paperEntryType", DictUtil.getDictValue("entryType", "importByQuestion"));
                            }
                        }
                    }

                }

                if (d!=null){
                    paper.put("answerCardStatus", d.get("answerCardStatus"));
                    paper.put("creationType",d.get("creationType"));
                    paper.put("entrustId", d.get("entrustId"));
                }

                paper.put("adjustClass", examStatPaperIds.contains(Long.parseLong(paperId)));
            }
        }

        // 不知道为什么去basedata库用gradeId取stage t_exam里面有stage字段 保留原有逻辑
        // 如果gradeId存在拿得到stage就用t_grade的stage值
        // 如果拿不到就用t_exam里面的
//        Integer gradeId = commonRepository.selectOne("ExamTraceMapper.getExamGradeId", params);
//        Integer examStage = getExamStage(MapUtil.of("gradeId", gradeId));
//        if (examStage != null) {
//            exam.put("stage", examStage);
//        }

        // 考试的试卷状态(学校-试卷维度) 主要是要获取到状态，用于修正成绩判断
        List<Map<String, Object>> examSchoolCoursePaper = commonRepository.selectList("ExamTraceMapper.getExamSchoolCoursePaper",params);

        // 获取报告配置
        if(ObjectUtil.isNotBlank(params.get("statId")) && MapUtils.getLong(params, "statId") != 0){
            Map<String,Object> stat = examStatReportService.getExamStatDetail(params);
            List<Map<String,Object>> statSchool = (List<Map<String, Object>>) stat.get("examSchool");
            List<Map<String,Object>> statCourse = (List<Map<String, Object>>) stat.get("examCourse");

            List<Map<String,Object>> statPaper = extractKeyList(statCourse,"examPaper","courseId");
            List<Map<String,Object>> statSchoolPaper = extractKeyList(statPaper,"examSchoolPaper","paperId");

            removeKey(examSchool,statSchool,"schoolId");
            removeKey(examCourse,statCourse,"courseId");
            removeKey(examPaper,statPaper,"paperId");
            removeKey(examSchoolCoursePaper,statSchoolPaper,"paperId");

        }

        //课程基础信息
        StringBuilder sb = new StringBuilder();
        examCourse.forEach(e-> sb.append(e.get("courseId")).append(","));
        params.put("courseId",sb.toString());
        List<Map<String, Object>> courseList = commonCourseService.getCourseList(params);
        Map<String, Map<String, Object>> cl = courseList.stream().collect(Collectors.toMap(c -> c.get("courseId").toString(),c->c));

        for (Map<String, Object> course : examCourse) {
            String courseId = course.get("courseId").toString();
            course.putAll(cl.get(courseId));
        }

        Map<String, List<Map<String, Object>>> ep = examPaper.stream().collect(Collectors.groupingBy(c -> c.get("courseId").toString()));

        for (Map<String, Object> course : examCourse) {
            String courseId = course.get("courseId").toString();
            course.put("paperList", ep.get(courseId));
            course.putAll(cl.get(courseId));
        }

        //获取考试是否有原始报告，有就说明考试至少有一张试卷是已发布的状态
        //目前用来判断联考在诊断管理处是否需要将考生管理置灰
        List<Long> examIdsHavingDefaultStat = commonRepository
          .selectList("ExamUnionNewMapper.getExamHavingDefaultStat", MapUtil.of("examIds", Collections.singletonList(examId)));
        exam.put("havingDefaultStat", !examIdsHavingDefaultStat.isEmpty());

        // 对学校排个序
        examSchoolCustomSortService.sort(examSchool, examId);

        // 数据返回
        exam.put("examSchool", examSchool);
        exam.put("examCourse", examCourse);
        exam.put("isReadByClass", isReadByClass);
        exam.put("examSchoolCoursePaper", examSchoolCoursePaper);
        return exam;
    }

    /**
     * 向考试试卷中填充阅卷负责人信息
     * @param examPapers 考试试卷列表
     * @param examId 考试id
     */
    private void fillPrincipleToExamPaper(List<Map<String, Object>> examPapers, Long examId){
        List<ExamWorkerDTO> subjectLeaders = newExamWorkerService.getSubjectLeaderByExamId(examId);
        Map<Long, Set<String>> paperId2WorkNameSet = subjectLeaders.stream()
          .collect(Collectors.groupingBy(ExamWorkerDTO::getPaperId, mapping(ExamWorkerDTO::getWorkerName, toSet())));
        examPapers.forEach(paper -> {
            Long paperId = MapUtils.getLong(paper, "paperId");
            if (paperId2WorkNameSet.containsKey(paperId)) {
                paper.put("principle", StringUtils.join(paperId2WorkNameSet.get(paperId), ","));
            }
        });
    }

    /**
    * @Description  获取考试阶段 stage
    * @Param   schoolId
    * @return  int stage  nullable
     *
    **/
    private Integer getExamStage(Map<String, Object> params){
        Verify.of(params).isValidId("gradeId").verify();
        return commonGradeService.getStageByGradeIdNullable(params);
    }


    /**
     *
     * @param params examId
     * @return examCourseFullMark
     */
    public List<Map<String, Object>> getExamCourseFullMark(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .verify();

        List<Map<String, Object>> examPaper = examPaperService.getExamPaper(params);
        Set<Long> paperIds = examPaper.stream()
                .map(item -> Long.valueOf(item.get("paperId").toString()))
                .collect(toSet());

        List<Document> into = paperManager.getPaperSimple(paperIds);
        List<Map<String, Object>> rs = new ArrayList<>();
        into.forEach(item ->{
            Map<String, Object> data = new HashMap<>(4);
            MapUtil.copy(item, data, "courseId", "courseName");
            FullMarkVO fullMarkVO = qsClientService.getFullMark(MapUtils.getLong(item, "paperId"));
            data.put("fullMark", fullMarkVO.getFullMark());
            data.put("courseFullMark", fullMarkVO.getCourseFullMark());
            rs.add(data);
        });
        return rs;
    }
}
