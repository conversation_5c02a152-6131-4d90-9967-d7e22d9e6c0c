package com.dongni.exam.plan.service;

import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.view.report.service.ExamStatReportService;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.grade.service.CommonGradeService;
import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.basedata.export.teacher.service.ExamTeacherService;
import com.dongni.basedata.export.user.service.CommonUserService;
import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import com.dongni.basedata.school.teacher.service.ITeacherService;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.spring.SpringContextUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.arrange.service.PaperReadArrangeService;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.bean.bo.ExamNameBO;
import com.dongni.exam.card.service.AnswerCardService;
import com.dongni.exam.common.mark.serivice.basedata.IGrdDirService;
import com.dongni.exam.common.mark.serivice.exam.IExamService;
import com.dongni.exam.common.mark.serivice.mark.IPaperReadService;
import com.dongni.exam.common.mark.serivice.paper.IPaperService;
import com.dongni.exam.common.mark.vo.ExamSchoolVO;
import com.dongni.exam.common.mark.vo.GradeDirectorVO;
import com.dongni.exam.common.mark.vo.TeacherVO;
import com.dongni.exam.datasource.bean.enums.StudentDataSourceEnum;
import com.dongni.exam.enumeration.AutoPublishEnum;
import com.dongni.exam.enumeration.ExamScanTypeEnum;
import com.dongni.exam.enumeration.ExamTypeEnum;
import com.dongni.exam.homework.service.HomeworkHandleService;
import com.dongni.exam.homework.service.HomeworkPreparationService;
import com.dongni.exam.maintain.service.ExamDataCleanService;
import com.dongni.exam.mark.bean.enums.MarkWorkerEnum;
import com.dongni.exam.mark.bean.vo.ManageMarkWorkerVO;
import com.dongni.exam.mark.bean.vo.MarkWorkerInfo;
import com.dongni.exam.mark.service.ExamMarkTodoService;
import com.dongni.exam.mark.service.ExamUpdateAnswerService;
import com.dongni.exam.mark.service.IMarkWorkerService;
import com.dongni.exam.plan.score.imports.service.ScoreImportExamService;
import com.dongni.exam.question.service.ExamQuestionStructureService;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.individual.wrong.book.bean.dto.ExamQueryDTO;
import com.dongni.tiku.individual.wrong.book.bean.vo.ExamSelectionVO;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.own.service.OwnExamPaperService;
import com.dongni.tiku.own.service.OwnPaperService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.text.StrBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * Created by scott
 * time: 10:32 2018/11/14
 * description:考试发布
 */
@Service
public class ExamService {

    private final Logger logger = LoggerFactory.getLogger(ExamService.class);

    @Autowired
    ExamRepository commonRepository;

    @Autowired
    private CommonStudentService commonStudentService;
    @Autowired
    private ExamInitService examInitService;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private CommonUserService commonUserService;
    @Autowired
    private ExamTeacherService examTeacherService;
    @Autowired
    private CourseServiceImpl courseService;
    @Autowired
    private PaperReadArrangeService paperReadArrangeService;
    @Autowired
    private OwnExamPaperService ownExamPaperService;
    @Autowired
    private ExamPermissionService examPermissionService;
    @Autowired
    private ExamDataCleanService examDataCleanService;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private ExamStatReportService examStatReportService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private ExamUpdateAnswerService examUpdateAnswerService;
    @Autowired
    private PaperManager paperManager;
    @Autowired
    private IMarkWorkerService markWorkerService;
    @Autowired
    private ITeacherService teacherService;

    @Autowired
    private ExamTraceService examTraceService;
    @Autowired
    private HomeworkPreparationService homeworkPreparationService;
    @Autowired
    private CommonGradeService commonGradeService;
    @Autowired
    private ExamUnionNewService examUnionNewService;
    @Autowired
    private ExamClassService examClassService;
    @Autowired
    private NewExamWorkerService newExamWorkerService;
    @Autowired
    private ExamStudentService examStudentService;
    @Autowired
    private ExamQuestionStructureService examQuestionStructureService;
    @Autowired
    private ExamCourseService examCourseService;
    @Autowired
    private ExamConfigService examConfigService;

    @Autowired
    private ExamMarkTodoService markTodoService;

    @Autowired
    private AnswerCardService answerCardService;

    @Autowired
    private IPaperReadService paperReadService;

    @Autowired
    private IPaperService paperService;
    @Autowired
    private IGrdDirService grdDirService;
    @Autowired
    private IExamService newExamService;
    @Autowired
    private ExamService examService;
    @Autowired
    private HomeworkHandleService homeworkHandleService;


    /**
     * 插入考试 导入模式 见{@link ScoreImportExamService#insertExam(Map)}
     */
    @Deprecated
    private void insertExamForScoreImport() { }

    /**
     * 保存考试 先克隆试卷，再保存考试
     */
    public Long insertExamAndClonePaper(Map<String, Object> paramsMap) {
        examPaperService.clonePaperAndReplacePaperIdByCourseList(MapUtil.getListMap(paramsMap, "examCourse"));
        return SpringContextUtil.getBean(ExamService.class).insertExam(paramsMap);
    }

    /**
     * 保存考试
     *
     * @param params examClass examCourse...
     */
    @Transactional(value = ExamRepository.TRANSACTION)
    public Long insertExam(Map<String, Object> paramsMap) {
        Verify.of(paramsMap)
                .isNotBlank("examName")
                .isNumeric("gradeType")
                .isNumeric("examType")
                .isNotBlank("startDate")
                .isNotBlank("endDate")
                .isNotBlank("examClass")
                .isNumeric("gradeYear")
                .isValidId("gradeId")
                .isNotBlank("gradeName")
                .isNotBlank("schoolName")
                .isValidId("schoolId")
                .verify();

        Map<String, Object> params = new HashMap<>(paramsMap);
        int stage = commonGradeService.getStageByGradeId(params);
        params.put("stage",stage);
        Long userId = Long.valueOf(params.get("userId").toString());
        String userName = params.get("userName").toString();
        int examType = MapUtils.getInteger(params,"examType");
        boolean isUnionExam = examType == DictUtil.getDictValue("examType", "union");
        boolean isAreaExam = examType == DictUtil.getDictValue("examType", "area");
        if (isAreaExam || isUnionExam) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "只支持单科目和多科目考试");
        }
        params.put("currentTime", DateUtil.getCurrentDateTime());

        // 考试学校
        List<Map<String, Object>> examGrade = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("schoolId", params.get("schoolId"));
        map.put("schoolName", params.get("schoolName"));
        map.put("gradeId", params.get("gradeId"));
        map.put("gradeName", params.get("gradeName"));
        map.put("gradeType", params.get("gradeType"));
        map.put("areaId", params.get("areaId"));
        examGrade.add(map);
        params.put("examGrade", examGrade);

        // 考试课程
        List<Map<String, Object>> examCourse = MapUtil.getListMap(params, "examCourse");
        if (CollectionUtils.isEmpty(examCourse)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请选择答题卡！");
        }

        //如果考试是文理分科，默认生成 文科报告 和 理科报告
        boolean isGenerateArtsScienceStat = Optional.ofNullable(params.get("isArtsScienceSplit"))
          .filter(obj -> !"".equals(obj.toString()))
          .map(obj -> Boolean.valueOf(obj.toString()))
          .orElse(false);

        //保存考试
        params.putIfAbsent("version", 2.0);
        params.put("examStatus", DictUtil.getDictValue("examStatus", "executing"));
        commonRepository.insert("ExamMapper.insertExam", params);
        Long examId = MapUtils.getLong(params, "examId");

        //保存考试学校
        commonRepository.insert("ExamMapper.insertExamSchool", params);

        // 考试班级
        List<Map<String, Object>> examClass = (List<Map<String, Object>>) params.get("examClass");
        if (CollectionUtils.isEmpty(examClass)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请选择班级！");
        }
        Set<Long> classIds = examClass.stream().map(s -> Long.valueOf(s.get("classId").toString())).collect(toSet());

        String classIdsStr = StringUtils.join(classIds.toArray(), ",");
        params.put("classIds", classIdsStr);

        // 获取学生
        List<Map<String, Object>> student = commonStudentService.getClassStudentInfo(new ArrayList<>(classIds));
        if (CollectionUtils.isEmpty(student)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "班级下没有学生！");
        }

        //移除学生
        examInitService.removeStudent(params, student, false);

        Set<Long> studentIds = student.stream().map(s -> Long.valueOf(s.get("studentId").toString())).collect(toSet());
        Map<Long, List<Map<String, Object>>> classStudent = student.stream().collect(groupingBy(s -> Long.valueOf(s.get("classId").toString())));

        //校验班级（教学班没有班主任，不校验）
        if (ObjectUtil.isValueEquals(1, examClass.get(0).get("classType"))) {
            checkExamClass(params);
        }
        List<Map<String, Object>> clazz = new ArrayList<>();
        for (Map<String, Object> c : examClass) {
            if (classStudent.get(Long.valueOf(c.get("classId").toString())) != null) {
                clazz.add(c);
            }
        }

        classIds = clazz.stream().map(s -> Long.valueOf(s.get("classId").toString())).collect(toSet());

        //保存考试班级
        params.put("examClass", clazz);
        commonRepository.insert("ExamMapper.insertExamClass", params);

        examCourseService.checkExamCourseValid(examCourse);

        //校验班级课程
        StrBuilder csb = new StrBuilder();
        Map<Long, Map<String, Object>> courseMap = new HashMap<>();
        examCourse.forEach(c -> {
            csb.append(c.get("memberStr").toString()).append(",");
            courseMap.put(Long.valueOf(c.get("courseId").toString()), c);
        });
        params.put("courseIds", csb);
        //checkExamCourse(params);
        List<Map<String, Object>> ct = new ArrayList<>();
        //新高考校验规则: 一个课程下面学生只能存在一个班级
        for (Map<String, Object> course : examCourse) {
            String courseName = course.get("courseName").toString();
            Map<String, Object> checkMap = new HashMap<>();
            List<String> courseIdList = Arrays.asList(course.get("memberStr").toString().split(","));
            List<Long> classIdList = new ArrayList<>();
            List<Map<String, Object>> paperList = (List) course.get("paper");
            if (CollectionUtils.isEmpty(paperList)) {
                continue;
            }
            for (Map<String, Object> paper : paperList) {
                List<Map<String, Object>> classList = (List) paper.get("examClassPaper");
                if (CollectionUtils.isEmpty(classList)) {
                    continue;
                }
                for (Map<String, Object> c : classList) {
                    classIdList.add(Long.valueOf(c.get("classId").toString()));
                }
            }
            if (!CollectionUtils.isEmpty(classIdList)) {
                checkMap.put("courseIdList", courseIdList);
                checkMap.put("classIdList", classIdList);
                checkMap.put("courseName", courseName);
                checkMap.put("courseIds", StringUtils.join(courseIdList, ","));
                checkMap.put("classIds", StringUtils.join(classIdList, ","));
                //班级是否有开设这个课程
                checkExamCourse(checkMap);
                if (checkMap.containsKey("classTeacher") && checkMap.get("classTeacher") != null) {
                    ct.addAll((List) checkMap.get("classTeacher"));
                }
                List<Map<String, Object>> duplicateStudents = commonStudentService.getDuplicateClassCourseStudent(checkMap);
                if (!CollectionUtils.isEmpty(duplicateStudents)) {
                    logger.error("发布考试：{}时校验未通过，一个学生在一门课程下只能在一个班级：{}", params.get("examName"), duplicateStudents);
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "一个课程下面学生只能存在一个班级！  " + duplicateStudents.get(0).get("courseName") + "\t" + duplicateStudents.stream().map(m -> m.get("studentName").toString()).collect(joining(",")));
                }

            }
        }

        params.put("classTeacher", ct);
        // 解析答题卡和工作员
        List<Map<String, Object>> examPaper = new ArrayList<>();
        for (Map<String, Object> course : examCourse) {
            List<Map<String, Object>> papers = (List<Map<String, Object>>) course.get("paper");
            if (CollectionUtils.isEmpty(papers)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请选择答题卡！");
            }

            papers.forEach(p -> {
                p.put("examId", examId);
                p.put("artsScience", course.get("artsScience"));
                p.put("courseName", course.get("courseName"));
                p.put("userId", userId);
                p.put("userName", userName);
                p.put("currentTime", params.get("currentTime"));
            });
            examPaper.addAll(papers);
        }

        //examTeacher 班主任与科任老师
        List<Map<String, Object>> examTeacher = new ArrayList<>();
        List<Map<String, Object>> classHeader = (List<Map<String, Object>>) params.get("classHeader");
        List<Map<String, Object>> classTeacher = (List<Map<String, Object>>) params.get("classTeacher");
        if (!CollectionUtils.isEmpty(classHeader)) {
            classHeader.forEach(e -> {
                e.put("courseId", 0);
                e.put("teacherId", e.get("headerId"));
                e.put("teacherName", e.get("headerName"));
            });
            examTeacher.addAll(classHeader);
        }
        examTeacher.addAll(classTeacher);

        for (Map<String, Object> ep : examPaper) {
            ep.put("templateCode", 0);
            ep.put("isPrivOpen", 1);
            answerCardService.updateAnswerCardTemplateManualExamInfo(MapUtils.getLong(ep, "paperId"), examId, null);
            answerCardService.updateAnswerCardTemplateSwitch(ep);
        }

        //保存考试试卷
        examPaperService.replaceExamPaperListFullMark(examPaper);
        params.put("examPaper", examPaper);
        commonRepository.insert("ExamMapper.insertExamPaper", examPaper);
        commonRepository.insert("ExamMapper.insertExamSchoolPaper", params);
        examUpdateAnswerService.initExamPaperAdditionalAnswer(params, examPaper);


        //不区分文,理数
        initCourseName(examCourse);

        // 获取基础课程
        List<Map<String, Object>> baseCourse = courseService.getBaseCourse();
        Map<Long, Map<String, Object>> baseCourseMap = baseCourse.stream().collect(toMap(c -> Long.valueOf(c.get("courseId").toString()), c -> c));

        commonRepository.insert("ExamMapper.insertExamCourse", params);

        //保存考试学生科目
        List<Map<String, Object>> examWorker = new ArrayList<>();
        List<Map<String, Object>> todoExamWorker = new ArrayList<>();
        List<Map<String, Object>> examResult = new ArrayList<>();
        List<Map<String, Object>> examClassPaper = new ArrayList<>();
        for (Map<String, Object> paper : examPaper) {
            long courseId = Long.parseLong(paper.get("courseId").toString());
            boolean isMultiCourses = courseMap.get(courseId).get("memberStr").toString().contains(",");
            List<Map<String, Object>> worker = (List<Map<String, Object>>) paper.get("examWorker");
            List<Map<String, Object>> courseDirectors = getCourseDirectorAsWorker(isMultiCourses,
                    MapUtil.getLong(params, "gradeId"), courseId);
            worker.addAll(courseDirectors);

            List<Map<String, Object>> classPaper = (List) paper.get("examClassPaper");

            if (CollectionUtils.isEmpty(classPaper)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "答题卡未分配给班级！");
            }
            if (CollectionUtils.isEmpty(worker)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请选择扫描员和阅卷负责人");
            }

            // 试卷工作员
            Long schoolId = MapUtils.getLong(params, "schoolId");
            for (Map<String, Object> wk : worker) {
                wk.put("schoolId", schoolId);
                wk.put("courseId", courseId);
                wk.put("courseName", paper.get("courseName"));
                wk.put("paperId", paper.get("paperId"));
                wk.put("artsScience", paper.get("artsScience"));
                wk.put("workerPhone", wk.getOrDefault("teacherPhone", 0));
                if (wk.get("teacherId") != null) {
                    Map<String, Object> w = commonUserService.getUserInfoByTeacher(wk);
                    if(MapUtils.isEmpty(w)){
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,  "不存在扫描员/阅卷负责人的用户信息:"+wk.get("teacherName"));
                    }
                    wk.put("workerUserId", w.get("userId"));
                    wk.put("workerName", w.get("userName"));
                    todoExamWorker.add(wk);
                    if (isMultiCourses) {
                        for (String cid : courseMap.get(courseId).get("memberStr").toString().split(",")) {
                            Map<String, Object> p = new HashMap<>(wk);
                            p.put("courseId", cid);
                            p.put("courseName", baseCourseMap.getOrDefault(Long.valueOf(cid), new HashMap<>()).getOrDefault("courseName", "未知"));
                            examWorker.add(p);
                        }
                    }
                } else {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请指定扫描员和阅卷负责人");
                }
            }
            examWorker.addAll(worker);


            // 班级学生试卷
            filterClassStudentPaper(paper, studentIds, classIds, classStudent, classPaper, examClassPaper, examResult, examType);
            //移除学生
            Map<String, Object> p = new HashMap<>();
            p.put("tagIds", paper.get("tagIds"));
            p.put("studentIds", paper.get("studentIds"));
            StringBuilder sb = new StringBuilder();
            classPaper.forEach(c -> sb.append(c.get("classId").toString()).append(","));
            paper.put("classIds", sb.toString());
            examInitService.removeStudent(paper, examResult, true);

        }
        //移除没有examResult的examStudent
        examInitService.removeStudent(student, examResult);

        if (isGenerateArtsScienceStat && examClassPaper.stream().anyMatch(x -> MapUtils.getInteger(x, "artsScience") == 0)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "存在未区分文理属性的班级，【文理分开】不可用");
        }

        //保存考试学生
        checkExamResult(examResult);
        examStudentService.insertStudentInfoToExam(params, examId, student, examResult, false);

        //初始化考试学生标签
        initStudentTag(params, student);

        newExamWorkerService.insertMany(params, examWorker);

        params.put("examClassPaper", examClassPaper);
        commonRepository.insert("ExamMapper.insertExamClassPaper", params);

        //保存科任老师与班主任信息
        params.put("examTeacher", examTeacher);
        commonRepository.insert("ExamMapper.insertExamTeacher", params);

        // 单科目考试需初始化考试工作员
        boolean isTpl = params.containsKey("isTplExam") && Boolean.parseBoolean(params.get("isTplExam").toString());
        if (isTpl) {
            params.put("memberStr", examCourse.get(0).get("memberStr"));
            initPaperReadByTpl(params, examPaper.get(0), examWorker);
        }

        //插入创建人权限的用户权限信息
        params.put("permissionType", 1);
        params.put("permissionUserId", params.get("userId"));
        params.put("permissionUserName", params.get("userName"));
        examPermissionService.insertPermission(params);

        examConfigService.initOrResetSchoolSystemStat(params);

        // 更新试卷状态为已发布
        updatePaperStatus(examPaper);

        //按班级 只生成一份待办
        if (ObjectUtil.isNumeric(params.get("correctMode")) &&
                ObjectUtil.isValueEquals(params.get("correctMode"), DictUtil.getDictValue("correctMode", "readByClass"))) {
            Map<Integer, List<Map<String, Object>>> workerType = todoExamWorker.stream().collect(groupingBy(t -> Integer.valueOf(t.get("workerType").toString())));
            List<Map<String, Object>> w = new ArrayList<>();
            for (Integer t : workerType.keySet()) {
                List<Map<String, Object>> list = workerType.get(t);
                Map<Long, List<Map<String, Object>>> todoUserMap = list.stream().collect(groupingBy(l -> Long.valueOf(l.get("workerUserId").toString())));
                for (Long id : todoUserMap.keySet()) {
                    List<Map<String, Object>> l = todoUserMap.get(id);
                    w.add(l.get(0));
                }
            }
            todoExamWorker = w;
        }

        // 单科目考试的阅卷监测待办先不生成
        if (!isTpl) {
            markTodoService.insertProcessTodoTask(params);
        }

        // 生成待办
        examInitService.insertTodo(examPaper, todoExamWorker, params);

        return Long.valueOf(params.get("examId").toString());
    }

    /**
     * 查询科目备课组长作为阅卷负责人,综合课程不处理
     */
    public List<Map<String, Object>> getCourseDirectorAsWorker(boolean isMultiCourses, long gradeId, long courseId) {
        if (isMultiCourses) {
            return Collections.emptyList();
        }
        List<GradeDirectorVO> courseDirector = grdDirService.findGrdCourseDirector(gradeId, courseId);
        if (CollectionUtils.isEmpty(courseDirector)) {
            return Collections.emptyList();
        }
        List<Map<String, Object>> worker = new ArrayList<>();
        for (GradeDirectorVO gradeDirectorVO : courseDirector) {
            Map<String, Object> w = new HashMap<>();
            w.put("teacherId", gradeDirectorVO.getTeacherId());
            w.put("teacherPhone", gradeDirectorVO.getDirectorPhone());
            w.put("workerType", DictUtil.getDictValue("workerType", "subjectLeader"));
            worker.add(w);
        }
        return worker;
    }

    /**
     * @return 存在 返回 List<String>  否则 返回 emptyList
     * 前端根据数组是否>0校验是否进行下一步
     * @Description 发布考试校验班级课程学生
     * 新高考规则：一个课程下面学生只能存在一个班级
     **/
    public List checkStudent(Map<String, Object> params) {

        List<String> rs = new ArrayList<>();
        List<Map<String, Object>> examCourse = (List) params.get("examCourse");
        for (Map<String, Object> course : examCourse) {
            Map<String, Object> checkMap = new HashMap<>();
            List<String> courseIdList = Arrays.asList(course.get("memberStr").toString().split(","));
            List<Long> classIdList = new ArrayList<>();
            List<Map<String, Object>> paperList = (List) course.get("paper");
            if (CollectionUtils.isEmpty(paperList)) {
                continue;
            }
            for (Map<String, Object> paper : paperList) {
                List<Map<String, Object>> classList = (List) paper.get("examClassPaper");
                if (CollectionUtils.isEmpty(classList)) {
                    continue;
                }
                for (Map<String, Object> c : classList) {
                    classIdList.add(Long.valueOf(c.get("classId").toString()));
                }
            }
            if (!CollectionUtils.isEmpty(classIdList)) {
                checkMap.put("courseIdList", courseIdList);
                checkMap.put("classIdList", classIdList);
                List<Map<String, Object>> duplicateStudents = commonStudentService.getDuplicateClassCourseStudent(checkMap);
                if (!CollectionUtils.isEmpty(duplicateStudents)) {
                    for (Map<String, Object> s : duplicateStudents) {
                        rs.add("一个课程下面学生只能存在一个班级！" + "\t" + "学生:" + "\t" + s.get("studentName") + "\t" + "课程:" + s.get("courseName") + "班级:" + "\t" + s.get("className"));
                    }
                    return rs;
                }
            }
        }
        return Collections.emptyList();
    }

    /**
     * 生成文科报告和理科报告
     *
     * @desc: 把原始报告的追踪信息组成成文科、理科两部分，作为examStatReportService.saveExamStat的参数添加报告
     * @param: params
     */
    private void generateArtsScienceStat(Map<String, Object> params) {

        Map<String, Object> originalReportParam = MapUtil.of(
                "userId", params.get("userId"),
                "userType", params.get("userType"),
                "userName", params.get("userName"),
                "examId", params.get("examId").toString(),
                "statId", 0,
                "schoolId", params.get("schoolId"),
                "teacherId", params.get("teacherId")
        );

        //获取原始报告追踪信息
        Map<String, Object> originalReport = examTraceService.getExamPlanTrace(originalReportParam);

        //发布文科和理科考试报告
        planArtsScienceExamStat(params, originalReport);
    }


    /**
     * 发布文科和理科考试报告
     *
     * @return
     * @param: params
     * @param: originalReport 原始报告
     */
    private void planArtsScienceExamStat(Map<String, Object> params, Map<String, Object> originalReport) {

        List<Map<String, Object>> examCourseList = (List<Map<String, Object>>) originalReport.get("examCourse");
        List<Map<String, Object>> examClassList = (List<Map<String, Object>>) originalReport.get("examClass");
        List<Map<String, Object>> examSchoolList = (List<Map<String, Object>>) originalReport.get("examSchool");
        if (CollectionUtils.isEmpty(examSchoolList)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "原始报告不存在examSchool");
        }

        //初始化文科课程和理科课程
        List<Map<String, Object>> artsExamCourseList = new ArrayList<>();
        List<Map<String, Object>> scienceExamCourseList = new ArrayList<>();
        initArtsScienceExamCourseList(examCourseList, artsExamCourseList, scienceExamCourseList);

        //初始化文科和理科考试学校
        Map<String, Object> artsExamSchool = new HashMap<>(examSchoolList.get(0));
        Map<String, Object> scienceExamSchool = new HashMap<>(examSchoolList.get(0));
        initArtsScienceExamSchoolList(examClassList, artsExamSchool, scienceExamSchool);

        List<Map<String, Object>> artsExamSchoolList = Arrays.asList(artsExamSchool);
        List<Map<String, Object>> scienceExamSchoolList = Arrays.asList(scienceExamSchool);

        Map<String, Integer> artsScienceDict = DictUtil.getDictKeyValue("artsScience");
        Integer arts = artsScienceDict.get("arts");
        Integer science = artsScienceDict.get("science");

        Map<String, Object> commonParam = MapUtil.of(
                "userId", params.get("userId"),
                "userName", params.get("userName"),
                "userType", params.get("userType"),
                "teacherId", params.get("teacherId")
        );

        //添加文科报告
        if (!CollectionUtils.isEmpty(artsExamCourseList)) {
            commonParam.put("artsScience", arts);
            planExamStat(commonParam, originalReport, artsExamSchoolList, artsExamCourseList);
        }

        //添加理科报告
        if (!CollectionUtils.isEmpty(scienceExamCourseList)) {
            commonParam.put("artsScience", science);
            planExamStat(commonParam, originalReport, scienceExamSchoolList, scienceExamCourseList);
        }
    }


    /**
     * 发布考试报告
     *
     * @return
     * @param: commonParam {userId,userName,userType,teacherId,artsScience}
     * @param: originalReport 原始报告
     * @param: examSchoolList 考试学校
     * @param: examCourseList 考试课程
     */
    private void planExamStat(Map<String, Object> commonParam,
                              Map<String, Object> originalReport,
                              List<Map<String, Object>> examSchoolList,
                              List<Map<String, Object>> examCourseList) {

        int artsScience = Integer.valueOf(commonParam.get("artsScience").toString());
        commonParam.remove("artsScience");

        Map<String, Object> examStatReport = MapUtil.of(
                "examId", originalReport.get("examId"),
                "statId", null,
                "schoolId", examSchoolList.get(0).get("schoolId"),
                "statDesc", "",
                "examCourse", examCourseList,
                "examSchool", examSchoolList,
                "display", DictUtil.getDictValue("isDisplay", "on"),
                "excludeTagId", ""
        );

        Map<String, Integer> artsScienceDict = DictUtil.getDictKeyValue("artsScience");
        int arts = artsScienceDict.get("arts");
        int science = artsScienceDict.get("science");

        if (artsScience == arts) {
            examStatReport.put("statName", "文科报告");
        }

        if (artsScience == science) {
            examStatReport.put("statName", "理科报告");
        }

        Map<String, Object> scienceExamStatParam = MapUtil.of("examStatReport", examStatReport);
        scienceExamStatParam.putAll(commonParam);
        examStatReportService.saveExamStat(scienceExamStatParam);
    }


    /**
     * 初始化文科和理科考试学校
     *
     * @param: params 请求参数
     * @param: examClassList 原始报告的考试班级
     * @param: artsExamSchoolList 初始化该文科考试学校
     * @param: scienceExamSchoolList 初始化该理科考试学校
     */
    private void initArtsScienceExamSchoolList(List<Map<String, Object>> examClassList,
                                               Map<String, Object> artsExamSchool,
                                               Map<String, Object> scienceExamSchool) {
        if (CollectionUtils.isEmpty(examClassList)) {
            return;
        }

        Map<String, Integer> artsScienceDict = DictUtil.getDictKeyValue("artsScience");
        int arts = artsScienceDict.get("arts");
        int science = artsScienceDict.get("science");

        //按文理科分组的考试班级
        Map<Integer, List<Map<String, Object>>> examClassGroupByArtsScience = examClassList.stream()
                .map(examClass -> {
                    examClass.put("selected", true);
                    return examClass;
                })
                .collect(Collectors.groupingBy(examClass -> Integer.valueOf(examClass.get("artsScience").toString())));

        artsExamSchool.put("examClass", examClassGroupByArtsScience.get(arts));

        scienceExamSchool.put("examClass", examClassGroupByArtsScience.get(science));
    }

    /**
     * 文理综合paper根据班级的文理科分开
     *
     * @return 新的paperList
     * @param: oldPaperList 旧的paperList
     */
    private List<Map<String, Object>> organizingPaperList(List<Map<String, Object>> oldPaperList) {

        Map<String, Integer> artsScienceDict = DictUtil.getDictKeyValue("artsScience");
        int arts = artsScienceDict.get("arts");
        int science = artsScienceDict.get("science");
        int no = artsScienceDict.get("no");

        //根据文理科班级进行分组的paperList
        List<Map<String, Object>> newPaperList = new ArrayList<>();

        //文理综合paper根据班级的文理科进行分组
        oldPaperList.stream()
                .filter(paper -> no == Integer.valueOf(paper.get("artsScience").toString()))
                .forEach(paper -> {
                    List<Map<String, Object>> classList = (List<Map<String, Object>>) paper.get("classList");

                    //classList根据文理科分组
                    Map<Integer, List<Map<String, Object>>> classGroupByArtsScience = classList.stream()
                            .collect(Collectors.groupingBy(classMap -> Integer.valueOf(classMap.get("artsScience").toString())));
                    //文科
                    List<Map<String, Object>> artsClassList = classGroupByArtsScience.get(arts);
                    if (!CollectionUtils.isEmpty(artsClassList)) {
                        Map<String, Object> artsClassPaper = new HashMap<>(paper);
                        artsClassPaper.put("classList", artsClassList);
                        newPaperList.add(artsClassPaper);
                    }

                    //理科
                    List<Map<String, Object>> scienceClassList = classGroupByArtsScience.get(science);
                    if (!CollectionUtils.isEmpty(scienceClassList)) {
                        Map<String, Object> scienceClassPaper = new HashMap<>(paper);
                        scienceClassPaper.put("classList", scienceClassList);
                        newPaperList.add(scienceClassPaper);
                    }

                });

        oldPaperList = oldPaperList.stream()
                .filter(paper -> {
                    int artsScience = Integer.valueOf(paper.get("artsScience").toString());
                    return arts == artsScience || science == artsScience;
                })
                .collect(Collectors.toList());

        newPaperList.addAll(oldPaperList);
        return newPaperList;
    }

    /**
     * 初始化文科和理科课程
     *
     * @param: examCourseList 原始报告的考试课程
     * @param: artsCourseList 初始化该文科课程
     * @param: scienceCourseList 初始化该理科课程
     */
    private void initArtsScienceExamCourseList(List<Map<String, Object>> examCourseList,
                                               List<Map<String, Object>> artsCourseList,
                                               List<Map<String, Object>> scienceCourseList) {

        if (CollectionUtils.isEmpty(examCourseList)) {
            return;
        }

        Map<String, Integer> artsScienceDict = DictUtil.getDictKeyValue("artsScience");
        int arts = artsScienceDict.get("arts");
        int science = artsScienceDict.get("science");
        int no = artsScienceDict.get("no");

        examCourseList.forEach(tempExamCourse -> {
            List<Map<String, Object>> paperList = (List<Map<String, Object>>) tempExamCourse.get("paperList");
            if (CollectionUtils.isEmpty(paperList)) {
                return;
            }

            //整理paperList
            paperList = organizingPaperList(paperList);

            for (Map<String, Object> paper : paperList) {
                List<Map<String, Object>> classList = (List<Map<String, Object>>) paper.get("classList");
                if (CollectionUtils.isEmpty(classList)) {
                    continue;
                }

                int paperArtsScience = Integer.valueOf(paper.get("artsScience").toString());
                int classArtsScience = Integer.valueOf(classList.get(0).get("artsScience").toString());

                List<Map<String, Object>> examClassPaperList = classList.stream()
                        .map(classMap -> MapUtil.of(
                                "examId", classMap.get("examId"),
                                "classId", classMap.get("classId"),
                                "className", classMap.get("className"),
                                "schoolId", classMap.get("schoolId"),
                                "gradeId", classMap.get("gradeId"),
                                "artsScience", classMap.get("artsScience")
                                )
                        )
                        .collect(Collectors.toList());

                Map<String, Object> classMap = classList.get(0);
                Map<String, Object> examSchoolPaper = MapUtil.of(
                        "schoolId", classMap.get("schoolId"),
                        "examId", classMap.get("examId"),
                        "paperId", classMap.get("paperId"),
                        "examClassPaper", examClassPaperList
                );

                Map<String, Object> examPaper = MapUtil.of(
                        "courseId", paper.get("courseId"),
                        "courseName", paper.get("courseName"),
                        "artsScience", paper.get("artsScience"),
                        "examId", paper.get("examId"),
                        "paperId", paper.get("paperId"),
                        "paperName", paper.get("paperName"),
                        "fullMark", paper.get("fullMark"),
                        "examSchoolPaper", Arrays.asList(examSchoolPaper)
                );

                Map<String, Object> examCourse = MapUtil.of(
                        "courseId", paper.get("courseId"),
                        "courseName", paper.get("courseName"),
                        "artsScience", paper.get("artsScience"),
                        "examPaper", Arrays.asList(examPaper)
                );

                //文科paper
                if (arts == paperArtsScience) {
                    artsCourseList.add(examCourse);
                }


                //理科paper
                if (science == paperArtsScience) {
                    scienceCourseList.add(examCourse);
                }

                //文理综合paper，文科班级
                if (no == paperArtsScience && arts == classArtsScience) {
                    artsCourseList.add(examCourse);
                }

                //文理综合paper,理科班级
                if (no == paperArtsScience && science == classArtsScience) {
                    scienceCourseList.add(examCourse);
                }

            }


        });
    }


    /**
     * 不区分文理数
     **/
    public void initCourseName(List<Map<String, Object>> examCourse) {
        for (Map<String, Object> course : examCourse) {
            String courseName = course.get("courseName").toString();
            if (courseName.equals("数学") || courseName.equals("文数") || courseName.equals("理数")) {
                course.put("courseName", "数学");
                course.put("artsScience", DictUtil.getDictValue("artsScience", "no"));
            }
        }
    }

    private void checkExamResult(List<Map<String, Object>> examResult) {
        if (CollectionUtils.isEmpty(examResult)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请指定考生");
        }

        List<String> duplicate = new ArrayList<>();
        Map<Long, Map<Long, List<Map<String, Object>>>> paperStudentGroup = examResult.stream().collect(groupingBy(s -> Long.valueOf(s.get("courseId").toString()), groupingBy(p -> Long.valueOf(p.get("studentId").toString()))));
        for (Long paperId : paperStudentGroup.keySet()) {
            for (Long studentId : paperStudentGroup.get(paperId).keySet()) {
                List<Map<String, Object>> ls = paperStudentGroup.get(paperId).get(studentId);
                if (ls.size() > 1) {
                    Map<String, Object> r = ls.get(0);
                    String str = r.get("studentName") + "重复参考" + r.get("courseName") + "\n\r";
                    duplicate.add(str);
                }
            }
        }

        if (!CollectionUtils.isEmpty(duplicate)) {
            logger.error("发布考试校验未通过，相同课程不同答题卡的参考学生存在重复，如下学生：{}", duplicate);
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "相同课程不同答题卡的参考学生存在重复");
        }
    }

    /**
     * 只保留 t_exam_student 的学生，过滤掉没有学生的班级
     */
    private void filterClassStudentPaper(Map<String, Object> paper,
                                         Set<Long> studentIds,
                                         Set<Long> classIds,
                                         Map<Long, List<Map<String, Object>>> classStudent,
                                         List<Map<String, Object>> classPaper,
                                         List<Map<String, Object>> examClassPaper,
                                         List<Map<String, Object>> examResult,
                                         int examType) {

        int resultStatus;
        //假如是线上作业，初始化学生resultStatus为1，未提交
        if (examType == 8) {
            resultStatus = 1;
        } else {
            resultStatus = DictUtil.getDictValue("resultStatus", "attend");
        }
        List<Map<String, Object>> effectiveClass = new ArrayList<>();
        List<Map<String, Object>> effectiveStudent = new ArrayList<>();
        Long courseId = MapUtils.getLong(paper, "courseId");
        String courseName = MapUtils.getString(paper, "courseName");
        Long paperId = MapUtils.getLong(paper, "paperId");

        for (Map<String, Object> cs : classPaper) {
            Long classId = Long.valueOf(cs.get("classId").toString());
            if (classIds.contains(classId)) {
                effectiveClass.add(new HashMap<>(cs));
                for (Map<String, Object> m : classStudent.get(classId)) {
                    effectiveStudent.add(new HashMap<>(m));
                }
            } else {
                Map<String, Object> p = new HashMap<>();
                p.put("classIds", cs.get("classId"));
                List<Map<String, Object>> paperStudent = commonStudentService.getClassStudent(p);
                if (!CollectionUtils.isEmpty(paperStudent)) {
                    paperStudent = paperStudent.stream().filter(s -> studentIds.contains(Long.valueOf(s.get("studentId").toString()))).collect(toList());
                    if (!CollectionUtils.isEmpty(paperStudent)) {
                        effectiveClass.add(cs);
                        for (Map<String, Object> s : paperStudent) {
                            effectiveStudent.add(new HashMap<>(s));
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(effectiveClass)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, paper.get("paperName") + "所指定的班级里没有满足考试的学生");
        }

        effectiveClass.forEach(r -> {
            r.put("courseId", courseId);
            r.put("paperId", paperId);
            r.put("examClassPaperStatus", paper.get("examPaperStatus"));
        });

        effectiveStudent.forEach(s -> {
            s.put("courseId", courseId);
            s.put("courseName", courseName);
            s.put("paperId", paperId);
            s.put("resultStatus", resultStatus);
        });

        effectiveStudent.stream().collect(Collectors.groupingBy(x -> MapUtils.getLong(x, "studentId"))).forEach((k, v) -> {
            if (v.size() > 1) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷：" + paper.get("paperName") + "有学生存在于多个班级，不允许该项操作!");
            }
        });

        examClassPaper.addAll(effectiveClass);
        examResult.addAll(effectiveStudent);

    }

    /**
     * 不做任何过滤，只是解析班级学生，只要添加了班级则把所有学生添加
     */
    private void parseClassStudentPaper(Map<String, Object> paper, List<Map<String, Object>> classPaper, List<Map<String, Object>> examClassPaper, List<Map<String, Object>> examResult) {

        int resultStatus = DictUtil.getDictValue("resultStatus", "attend");
        List<String> classIds = new ArrayList<>();
        classPaper.forEach(c -> {
            c.put("paperId", paper.get("paperId"));
            c.put("examClassPaperStatus", paper.get("examPaperStatus"));
            classIds.add(c.get("classId").toString());
        });

        // 学生试卷
        Map<String, Object> p = new HashMap<>();
        p.put("classIds", StringUtils.join(classIds.toArray(), ","));
        List<Map<String, Object>> paperStudent = commonStudentService.getClassStudent(p);
        if (CollectionUtils.isEmpty(paperStudent)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "答题卡未分配给考生！");
        }
        paperStudent.forEach(s -> {
            s.put("courseId", paper.get("courseId"));
            s.put("paperId", paper.get("paperId"));
            s.put("resultStatus", resultStatus);
        });

        examClassPaper.addAll(classPaper);
        examResult.addAll(paperStudent);
    }

    /**
     * 插入考试学生标签
     * @param params examId
     */
    public void insertStudentTag(Map<String,Object> params){

        Verify.of(params).isValidId("examId").verify();

        Map<String,Object> p = new HashMap<>();
        p.put("examId",params.get("examId"));
        List<String> classIds = commonRepository.selectList("ExamMapper.getExamClassId",p);
        if(CollectionUtils.isEmpty(classIds)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"未找到考试班级");
        }

        p.put("classIds",StringUtils.join(classIds,","));
        List<Map<String, Object>> student = commonStudentService.getClassStudent(p);
        if (CollectionUtils.isEmpty(student)) {
            return;
        }

        initStudentTag(params,student);
    }

    /**
     * 初始化考试学生标签
     **/
    public void initStudentTag(Map<String, Object> params, List<Map<String, Object>> student) {
        List<Map<String, Object>> stuTag = new ArrayList<>();
        Map<Long, Map<String, Object>> tagMap = new HashMap<>();
        for (Map<String, Object> s : student) {
            if (!ObjectUtil.isBlank(s.get("tagId"))) {
                List<Long> tagIds = StringUtil.strToList(s.get("tagId").toString(), ",", Long.class);
                List<String> tagNames = StringUtil.strToList(s.get("tagName").toString(), ",", String.class);
                for (int i = 0; i < tagIds.size(); i++) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("tagId", tagIds.get(i));
                    m.put("tagName", tagNames.get(i));
                    m.put("studentId", s.get("studentId"));
                    m.put("schoolId", s.get("schoolId"));
                    m.put("examId", params.get("examId"));
                    m.put("userId", params.get("userId"));
                    m.put("userName", params.get("userName"));
                    m.put("currentTime", params.get("currentTime"));
                    if (!tagMap.containsKey(tagIds.get(i))) {
                        tagMap.put(tagIds.get(i), m);
                    }
                    stuTag.add(m);
                }
            }
        }
        if (!CollectionUtils.isEmpty(stuTag)) {
            List<Map<String, Object>> examTag = new ArrayList<>(tagMap.values());
            commonRepository.batchInsert("ExamMapper.batchInsertExamTag", examTag);
            Map<String, Map<String, Object>> tag = examTag.stream().collect(toMap(item -> item.get("tagId").toString(), item -> item));
            for (Map<String, Object> stu : stuTag) {
                stu.put("examTagId", tag.get(stu.get("tagId").toString()).get("examTagId"));
            }
            commonRepository.batchInsert("ExamMapper.batchInsertExamStudentTag", stuTag);
        }
    }

    private void initPaperReadByTpl(Map<String, Object> params, Map<String, Object> examPaper, List<Map<String, Object>> examWorker) {
        //按模板发布考试
        Map<String, Object> map = new HashMap<>(params);

        //初始化paper_read
        Long examPaperId = MapUtils.getLong(examPaper, "examPaperId");
        params.put("examPaperId", examPaperId);
        long examId = Long.parseLong(examPaper.get("examId").toString());
        long paperId = Long.parseLong(examPaper.get("paperId").toString());

        List<Map<String, Object>> examTplTeachers = (List<Map<String, Object>>) params.get("examTplTeacher");
        List<TeacherVO> teacherVOS = examTplTeachers.stream()
                .filter(x -> x.get("courseId") == null || Long.parseLong(x.get("courseId").toString()) != 0)
                .map(x -> {
                    TeacherVO teacherVO = new TeacherVO();
                    teacherVO.setTeacherId(Long.parseLong(x.get("teacherId").toString()));
                    teacherVO.setTeacherName(x.get("teacherName").toString());
                    return teacherVO;
                }).collect(toList());
        long userId = Long.parseLong(params.get("userId").toString());
        String userName = params.get("userName").toString();
        //保存阅卷老师
        ManageMarkWorkerVO manageMarkWorkerVO = new ManageMarkWorkerVO();
        manageMarkWorkerVO.setExamId(examId);
        manageMarkWorkerVO.setExamPaperIds(Collections.singletonList(examPaperId));
        manageMarkWorkerVO.setCurrentUserId(userId);
        manageMarkWorkerVO.setCurrentUserName(userName);
        List<Long> teacherIds = teacherVOS.stream().map(TeacherVO::getTeacherId).distinct().collect(Collectors.toList());
        Map<Long, Map<String, Object>> teacherId2UserInfo = teacherIds.isEmpty() ? Collections.emptyMap()
          : teacherService.getTeacherByTeacherIds(teacherIds).stream()
            .collect(Collectors.toMap(x -> MapUtils.getLong(x, "teacherId"), x -> x, (v1, v2) -> v1));
        List<MarkWorkerInfo> markWorkers = teacherIds.stream().map(teacherId -> {
            Map<String, Object> userInfo = Optional.ofNullable(teacherId2UserInfo.get(teacherId)).orElseThrow(() ->
              new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "未找到老师对应的用户信息！teacherId：" + teacherId));
            MarkWorkerInfo markWorkerInfo = new MarkWorkerInfo();
            markWorkerInfo.setUserId(MapUtils.getLong(userInfo, "userId"));
            markWorkerInfo.setUserName(MapUtils.getString(userInfo, "userName"));
            return markWorkerInfo;
        }).collect(toList());
        manageMarkWorkerVO.setMarkWorkers(markWorkers);
        manageMarkWorkerVO.setWorkerType(MarkWorkerEnum.Marker.getType());
        markWorkerService.addMarkWorkers(manageMarkWorkerVO);
        paperReadService.initialArrange(examId, paperId, teacherVOS, userId, userName);

        //按班级发布考试过滤重复答题卡负责人
        Map<String, List<Map<String, Object>>> workerType = examWorker.stream().collect(groupingBy(m -> m.get("workerType").toString()));

        Map<String, List<Map<String, Object>>> collect = workerType.get(DictUtil.getDictValue("workerType", "answerCardUploader").toString()).stream().collect(groupingBy(item -> item.get("teacherId").toString() + "-" + item.get("paperId")));
        List<Map<String, Object>> todoList = new ArrayList<>();
        for (String tp : collect.keySet()) {
            todoList.add(collect.get(tp).get(0));
        }
        examWorker.clear();
        examWorker.addAll(todoList);
        examWorker.add(workerType.get(DictUtil.getDictValue("workerType", "subjectLeader").toString()).get(0));

    }

    /**
     * 线上、线下作业初始化阅卷安排
     */
    private void initPaperReadByOnline(Map<String, Object> params, Map<String, Object> examPaper){
        //按模板发布考试
        Map<String, Object> map = new HashMap<>(params);

        // 初始化创建人信息，如果创建人不是老师，那就直接报错吧，谁知道又是哪传的不对
        Map<String,Object> rs = examTeacherService.getUserTeacher(params);
        if (MapUtils.isEmpty(rs)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "作业只能由老师发布");
        }
        Map<String,Object> charge = MapUtil.copy(rs, "teacherId", "teacherName", "teacherPhone");

        //初始化paper_read
        params.put("examPaperId", examPaper.get("examPaperId"));
        map.put("memberStr", examPaper.get("courseId"));
        paperReadArrangeService.initPaperRead(map, Collections.singletonList(charge));
    }


    /**
     * 班级是否有指定班主任
     *
     * @param params 考试班级
     */
    public void checkExamClass(Map<String, Object> params) {
        // 校验结果
        StringBuilder sb = new StringBuilder();
        // 班级是否有指定班主任
        Map<String, Object> baseMap = new HashMap<>(1);
        baseMap.put("classIds", params.get("classIds"));
        List<Map<String, Object>> classHeader = commonClassService.getClassListWithClassHeader(baseMap);
        classHeader = classHeader.stream().filter(x -> MapUtils.getInteger(x,"classType") == 1).collect(toList());
        for (Map<String, Object> c : classHeader) {
            if (!ObjectUtil.isValidId(c.get("headerId"))) {
                sb.append(c.get("className")).append("没有指定班主任、");
            }
        }
        if (!ObjectUtil.isBlank(sb.toString())) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, sb.toString());
        }
        params.put("classHeader", classHeader);
    }

    /**
     * 班级是否有开设这个课程
     * 班级开设了课程是否有指定任课老师
     *
     * @param params courseIds classIds
     */
    public void checkExamCourse(Map<String, Object> params) {
        // 校验结果
        StringBuilder sb = new StringBuilder();

        List<Map<String, Object>> classHeader;
        if (params.get("headTeacher") != null) {
            classHeader = (List<Map<String, Object>>) params.get("classHeader");
        } else {
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put("classIds", params.get("classIds"));
            classHeader = commonClassService.getClassListWithClassHeader(paramsMap);
        }
        Map<Long, Integer> classArtsMap = classHeader.stream()
          .collect(toMap(c -> MapUtils.getLong(c, "classId"), c -> MapUtils.getInteger(c, "artsScience"), (v1, v2) -> v1));
        // 班级是否有开设这个课程
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("classIds", params.get("classIds"));
        paramsMap.put("courseIds", params.get("courseIds"));
        List<Map<String, Object>> classTeacher = examTeacherService.getCourseTeacher(paramsMap);

        if (CollectionUtils.isEmpty(classTeacher)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, params.get("courseName") + "找不到任何任课老师！");
        }
        //过滤未开设课程班级
        List<String> validator = new ArrayList<>();
        Set<Long> existClassTeacher = classTeacher.stream().map(m -> Long.valueOf(m.get("classId").toString())).collect(Collectors.toSet());
        for (Map<String, Object> ch : classHeader) {
            Long classId = Long.valueOf(ch.get("classId").toString());
            if (!existClassTeacher.contains(classId)) {
                validator.add(ch.get("schoolName") + "的" + ch.get("className").toString() + params.get("courseName"));
            }
        }

        if (!CollectionUtils.isEmpty(validator)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, validator.toString() + " 找不到任何任课老师" + "\n");
        }

        List<Map<String, Object>> courseList = courseService.getCourseList(params.get("courseIds").toString());
        Map<Long, Map<String, Object>> courseMap = courseList.stream().collect(toMap(c -> Long.valueOf(c.get("courseId").toString()), c -> c));

        for (Map<String, Object> c : classTeacher) {
            Long classId = Long.valueOf(c.get("classId").toString());
            Long courseId = Long.valueOf(c.get("courseId").toString());
            String courseName = c.get("courseName").toString();
            if (classArtsMap.get(classId) == 1) {
                //文科班不需要理科课程的考试
                Map<String, Object> map = courseMap.get(courseId);
                Integer artsScience = Integer.valueOf(map.get("artsScience").toString());
                if (artsScience == 2) {
                    continue;
                }
            }
            if (classArtsMap.get(classId) == 2) {
                //理科班不需要文科课程的考试
                Map<String, Object> map = courseMap.get(courseId);
                Integer artsScience = Integer.valueOf(map.get("artsScience").toString());
                if (artsScience == 1) {
                    continue;
                }
            }

            if (Integer.valueOf(c.get("isOpen").toString()) == 0) {
                sb.append(c.get("schoolName")).append("的").append(c.get("className")).append("没有开设").append(courseName).append("、");
            }

            //班级开设了课程但是是否有指定任课老师
            if (c.get("teacherId") == null) {
                sb.append(c.get("schoolName")).append("的").append(c.get("className")).append("没有指定").append(courseName).append("的任课老师").append("、");
            }
        }

        if (!ObjectUtil.isBlank(sb.toString())) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, sb.toString());
        }
        params.put("classTeacher", classTeacher);
    }

    /**
     * 班级是否开设这个课程  有指定任课老师
     *
     * @param params examClassPaper
     */
    public void checkCurrentExamCourse(Map<String, Object> params) {
        // 校验结果
        StringBuilder sb = new StringBuilder();
        List<Map<String,Object>> examClassPaper = (List<Map<String, Object>>) params.get("examClassPaper");
        examClassPaper.forEach(e->sb.append(e.get("classId")).append(","));
        // 班级是否有开设这个课程
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("examId", params.get("examId"));
        paramsMap.put("classIds", sb.toString());
        paramsMap.put("courseIds", params.get("courseId"));
        List<Map<String, Object>> classTeacher = examTeacherService.getCourseTeacher(paramsMap);
        if (CollectionUtils.isEmpty(classTeacher)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, params.get("courseName") + "找不到任何任课老师！");
        }
        params.put("existExamClassTeacher",classTeacher);
        //同一班级不能参与同一课程下的多张试卷
        List<Map<String,Object>> existExamClassPaper = commonRepository.selectList("ExamMapper.getExamClassPaper", paramsMap);
        if (!CollectionUtils.isEmpty(existExamClassPaper)) {
            StringBuilder className = new StringBuilder();
            existExamClassPaper.forEach(e->className.append(e.get("className")).append(","));
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, className + "不能在同一课程"+params.get("courseName")+"中考多张试卷！");
        }
        //过滤未开设课程班级
        List<String> validator = new ArrayList<>();
        List<String> isOpenValidator = new ArrayList<>();
        Map<Long,List<Map<String,Object>>> existClassTeacher = classTeacher.stream().collect(groupingBy(m -> Long.valueOf(m.get("classId").toString())));
        for (Map<String, Object> ch : examClassPaper) {
            Long classId = Long.valueOf(ch.get("classId").toString());
            if (!existClassTeacher.keySet().contains(classId)) {
                validator.add(ch.get("schoolName") + "的" + ch.get("className").toString() + params.get("courseName"));
            }else {
                if (ObjectUtil.isValueEquals(0,existClassTeacher.get(classId).get(0).get("isOpen"))) {
                    isOpenValidator.add(ch.get("schoolName") + "的" + ch.get("className").toString() + params.get("courseName"));
                }
            }
        }
        if (!CollectionUtils.isEmpty(validator)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, validator.toString() + " 找不到任何任课老师" + "\n");
        }else if(!CollectionUtils.isEmpty(isOpenValidator)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, isOpenValidator.toString() + " 没有开启" + "\n");
        }
    }
    
    /**
     * 获取考试详情
     * @param parameterMap examId
     * @return nullable 考试详情
     *         examId, examName, examType, escalationId, correctMode, entryType, autoPublish, examStatus, startDate, endDate, homeworkType,
     *         gradeType, gradeYear, version, scanType, stage, creatorId, creatorName, createDateTime, examGroupId
     */
    public @Nullable Map<String, Object> getExamDetailNullable(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isValidId("examId").verify();
        return commonRepository.selectOne("ExamMapper.getExamDetail", parameterMap);
    }
    
    /**
     * 获取考试详情
     * @param parameterMap examId
     * @return 考试详情
     *         examId, examName, examType, correctMode, entryType, escalationId, autoPublish, examStatus, startDate, endDate, homeworkType,
     *         gradeType, gradeYear, version, scanType, stage, creatorId, creatorName, createDateTime, examGroupId
     */
    public Map<String, Object> getExamDetail(Map<String, Object> parameterMap) {
        return getExamDetail(parameterMap, null);
    }
    
    /**
     * 获取考试详情
     * @param parameterMap examId
     * @param notExistErrMsg 当获取不到考试信息时，抛异常的信息，如果为blank，则抛"考试不存在:考试id:${examId}"
     * @return 考试详情
     *         examId, examName, examType, correctMode, entryType, escalationId, autoPublish, examStatus, startDate, endDate, homeworkType,
     *         gradeType, gradeYear, version, scanType, stage, creatorId, creatorName, createDateTime, examGroupId
     */
    public Map<String, Object> getExamDetail(Map<String, Object> parameterMap, String notExistErrMsg) {
        Map<String, Object> examDetail = getExamDetailNullable(parameterMap);
        if (MapUtils.isEmpty(examDetail)) {
            if (ObjectUtil.isBlank(notExistErrMsg)) {
                notExistErrMsg = "考试不存在: 考试id:" + MapUtil.getLong(parameterMap, "examId");
            }
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, notExistErrMsg);
        }
        return examDetail;
    }

    /**
     * 新增考试课程 先克隆试卷，再新增考试课程
     */
    public Map<String, Object> insertExamCourseAndClonePaper(Map<String, Object> params) {
        examPaperService.clonePaperAndReplacePaperIdByCourseList(MapUtil.getListMap(params, "examCourse"));
        return SpringContextUtil.getBean(ExamService.class).insertExamCourse(params);
    }

    /**
     * 新增考试课程
     *
     * @param params examCourse
     */
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName="EXAM", name="AddExamCoursePaper", argValueKeys = {"[0].examId"})
    public Map<String, Object> insertExamCourse(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").isValidId("schoolId").isNotBlank("examCourse").isNumeric("examType")
                .verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        List<Map<String, Object>> examCourse = MapUtil.getListMap(params, "examCourse");

        //获取考试信息
        Map<String, Object> examInfo = getExamDetail(params);
        params.putAll(examInfo);
        Long examId = MapUtils.getLong(params, "examId");

        if (DictUtil.isEquals(MapUtils.getInteger(examInfo, "correctMode"), "correctMode", "readByClass")) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "按班级阅卷的诊断不支持添加答题卡！");
        }

        //获取考试课程
        List<Map<String, Object>> existCourse = commonRepository.selectList("ExamMapper.getExamCourse", params);
        if(CollectionUtils.isEmpty(existCourse)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "诊断课程数据有误，请联系管理员处理");
        }

        examCourseService.checkExamCourseValid(examId, examCourse);

        //获取考试总分班级
        List<Map<String, Object>> examClass = commonRepository.selectList("ExamMapper.getExamClass", params);
        if (CollectionUtils.isEmpty(examClass)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "诊断班级数据有误，请联系管理员处理！");
        }
        List<Long> examClassIds = examClass.stream().map(x -> MapUtils.getLong(x, "classId")).collect(toList());
        params.put("classIds", StringUtils.join(examClassIds, ","));

        //获取考试班级学生
        List<Map<String, Object>> student = commonStudentService.getClassStudentInfo(examClassIds);
        if (CollectionUtils.isEmpty(student)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
              "根据诊断中的参考班级[" + examClass.stream().map(x -> MapUtils.getString(x, "className")).collect(joining("、")) +
              "]在基础数据中未匹配到学生，可能是学生在基础数据中的班级信息已经变化但未同步到诊断中，请尝试在诊断中更新学生的班级信息或联系管理员处理！");
        }
        Set<Long> studentIds = student.stream().map(s -> Long.valueOf(s.get("studentId").toString())).collect(toSet());
        Map<Long, List<Map<String, Object>>> classStudent = student.stream().collect(groupingBy(s -> Long.valueOf(s.get("classId").toString())));

        List<Map<String, Object>> clazz = new ArrayList<>();
        for (Map<String, Object> c : examClass) {
            if (classStudent.get(Long.valueOf(c.get("classId").toString())) != null) {
                clazz.add(c);
            }
        }
        Set<Long> classIds = clazz.stream().map(s -> Long.valueOf(s.get("classId").toString())).collect(toSet());
        Set<Long> courseIds = existCourse.stream().map(s -> Long.valueOf(s.get("courseId").toString())).collect(toSet());

        //获取考试设置了扫描员的课程
        List<Long> courseIdsExistUploader = commonRepository.selectList("ExamMapper.getExamCourseIdsByExistUploader", params);
        if(CollectionUtils.isEmpty(courseIdsExistUploader)){
            //校内考目前在发布的时候必须要选扫描员、阅卷负责人，如果在这里没有查到有设置了扫描员的课程，说明数据有问题
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "诊断数据有误，请联系管理员处理");
        }
        //从已经存在的课程中取第一个courseId，查询其在t_exam_worker中的扫描员数据
        params.put("examWorkerCourseId", courseIdsExistUploader.get(0));
        List<Map<String, Object>> existExamUploader = commonRepository.selectList("ExamMapper.getExamExistUploaderByCourse",params);
        if (existExamUploader.stream().anyMatch(x -> !ObjectUtil.isValidId(x.get("userId")))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "诊断数据有误，请联系管理员处理！");
        }
        Map<Long, Map<String, Object>> userId2Info = commonUserService.getUserByIds(
            MapUtil.of("userIds", existExamUploader.stream().map(x -> MapUtils.getLong(x, "userId")).collect(toList())))
          .stream().collect(Collectors.toMap(y -> MapUtils.getLong(y, "userId"), y -> y));
        existExamUploader.forEach(x -> {
            Long userId = MapUtils.getLong(x, "userId");
            if (!userId2Info.containsKey(userId)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "用户信息不存在，userId：" + userId + "，请联系管理员处理！");
            }
            Map<String, Object> userInfo = userId2Info.get(userId);
            x.put("workerUserId", userInfo.get("userId"));
            x.put("workerName", userInfo.get("userName"));
            x.put("teacherId", userInfo.get("relativeId"));
        });

        ExamSchoolVO examSchoolVO = newExamService.listExamSchools(examId).stream()
          .filter(e -> MapUtil.getLong(params, "schoolId") == e.getSchoolId())
          .findFirst().orElseThrow(() -> new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
            "学校未参加当前考试！examId：" + examId + "，schoolId：" + params.get("schoolId")));

        // 获取基础课程
        List<Map<String, Object>> baseCourse = courseService.getBaseCourse();
        Map<Long, Map<String, Object>> baseCourseMap = baseCourse.stream().collect(toMap(c -> Long.valueOf(c.get("courseId").toString()), c -> c));

        List<Map<String, Object>> examPaper = new ArrayList<>();
        List<Map<String, Object>> examClassPaper = new ArrayList<>();
        List<Map<String, Object>> examWorker = new ArrayList<>();
        List<Map<String, Object>> examResult = new ArrayList<>();
        List<Map<String, Object>> notExistCourse = new ArrayList<>();
        List<Long> existCourseIds  =new ArrayList<>();
        List<Map<String, Object>> todoExamWorker = new ArrayList<>();
        List<Map<String, Object>> examTeacher = new ArrayList<>();
        Map<Long, String> courseId2Name = new HashMap<>();
        Map<Long, List<Long>> courseId2PaperIds = new HashMap<>();
        List<Long> statPaperId = new ArrayList<>();
        Map<String, Object> rs = new HashMap<>();
        for (Map<String, Object> course : examCourse) {
            Long courseId = MapUtils.getLong(course, "courseId");
            //课程是否已经存在
            if(!courseIds.contains(courseId)){
                notExistCourse.add(course);
            }else {
                existCourseIds.add(courseId);
            }
            courseId2Name.put(courseId, MapUtils.getString(course, "courseName"));
            List<Long> coursePaperIds = new ArrayList<>();
            List<Map<String, Object>> papers = MapUtil.getListMap(course, "paper");

            List<Map<String,Object>> currentExamClassPaper = new ArrayList<>();
            Integer examType = MapUtils.getInteger(examInfo, "examType");
            for (Map<String, Object> p : papers) {
                Long paperId = MapUtils.getLong(p, "paperId");
                p.put("schoolId",params.get("schoolId"));
                p.put("artsScience", course.get("artsScience"));
                List<Map<String, Object>> ecp = MapUtil.getListMap(p, "examClassPaper");
                if (CollectionUtils.isEmpty(ecp)) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, p.get("paperName") + "未设置参考班级");
                }
                getExamUploader(course,p,baseCourseMap,examWorker,todoExamWorker,existExamUploader);
                addCourseDirectorAsWorker(course, paperId, courseId, examSchoolVO.getGradeId(), examWorker);

                currentExamClassPaper.addAll(ecp);
                filterClassStudentPaper(p, studentIds, classIds, classStudent, ecp, examClassPaper, examResult, examType);
                //移除学生
                StringBuilder ids = new StringBuilder();
                ecp.forEach(c -> ids.append(c.get("classId").toString()).append(","));
                p.put("classIds", ids.toString());
                examInitService.removeStudent(p, examResult, true);
                p.put("userId", params.get("userId"));
                p.put("examId", examId);
                p.put("userName", params.get("userName"));
                p.put("currentTime", params.get("currentTime"));
                coursePaperIds.add(paperId);
                statPaperId.add(paperId);
                //更新当前三方模板的examId字段
                answerCardService.updateAnswerCardTemplateManualExamInfo(paperId, examId, null);

            }
            //校验考试班级是否开设课程
            Map<String,Object> map = new HashMap<>();
            map.put("examId", examId);
            map.put("courseId",course.get("courseId"));
            map.put("courseName",course.get("courseName"));
            map.put("examClassPaper",currentExamClassPaper);
            checkCurrentExamCourse(map);
            examPaper.addAll(papers);
            if(ObjectUtil.isNotEmptyCollections(map.get("existExamClassTeacher"))){
                examTeacher.addAll(MapUtil.getListMap(map, "existExamClassTeacher"));
            }
            if (courseId2PaperIds.containsKey(courseId)) {
                courseId2PaperIds.get(courseId).addAll(coursePaperIds);
            } else {
                courseId2PaperIds.put(courseId, coursePaperIds);
            }
        }

        //校验
        List<String> validator = new ArrayList<>();
        examUnionNewService.checkDuplicateCoursePaper(params, MapUtils.getLong(params, "examId"), validator, courseId2Name,
          courseId2PaperIds, examResult, statPaperId, examClassPaper);

        if(!CollectionUtils.isEmpty(validator)){
            rs.put("validator",validator);
            return rs;
        }

        //过滤掉所有试卷都不参加考试的学生
        examInitService.removeStudent(student, examResult);

        //更新考试、学校、班级的状态
        int executing = DictUtil.getDictValue("examStatus", "executing");
        params.put("examStatus", executing);
        updateExamStatus(params);
        //学校的状态使用考试的1,2,3 只改学校状态不改学校的微信消息推送状态
        params.put("examSchoolStatus", executing);
        commonRepository.update("ExamSchoolMapper.updateExamSchoolStatus",params);
        //班级的状态使用试卷的1,5,10,15,20
        params.put("examClassStatus", 1);
        examClassService.updateExamClassStatus(params);

        //更新考试课程状态
        if (!CollectionUtils.isEmpty(existCourseIds)) {
            params.put("examCourseStatus", 0);
            params.put("updateCourseIds", existCourseIds);
            commonRepository.update("ExamCourseMapper.updateExamCoursesStatus", params);
        }
        //更新考试所有报告的状态为未公布
        commonRepository.update("ExamSchoolMapper.closeExamAllStat", params);

        //保存考试课程
        if (!CollectionUtils.isEmpty(notExistCourse)) {
            Map<String, Object> p = new HashMap<>(params);
            initCourseName(notExistCourse);
            p.put("examCourse", notExistCourse);
            commonRepository.insert("ExamMapper.insertExamCourse", p);
        }

        //保存考试试卷
        examPaperService.replaceExamPaperListFullMark(examPaper);
        params.put("examPaper", examPaper);
        params.put("examClassPaper", examClassPaper);
        commonRepository.insert("ExamMapper.insertExamPaper", examPaper);
        commonRepository.insert("ExamMapper.insertExamSchoolPaper", params);
        commonRepository.insert("ExamMapper.insertExamClassPaper", params);
        examUpdateAnswerService.initExamPaperAdditionalAnswer(params, examPaper);
        //保存考试扫描员 阅卷负责人
        newExamWorkerService.insertMany(params, examWorker);
        //保存考试学生信息
        examStudentService.insertStudentInfoToExam(params, examId, student, examResult, true);
        //保存examTeacher
        if(ObjectUtil.isNotEmptyCollections(examTeacher)){
            params.put("examTeacher",examTeacher);
            commonRepository.insert("ExamMapper.insertExamTeacher", params);
        }

        // 更新试卷状态为已发布
        updatePaperStatus(examPaper);


        //生成待办
        examInitService.insertTodo(examPaper, examWorker, params);

        //更新examConfig examReportStat
        examInitService.resetExamConfigInfo(params);
        return rs;
    }

    private void addCourseDirectorAsWorker(Map<String, Object> course, Long paperId, Long courseId, long gradeId, List<Map<String, Object>> examWorker) {
        boolean isMultiCourses = course.get("memberStr").toString().contains(",");
        List<Map<String, Object>> courseDirector = getCourseDirectorAsWorker(isMultiCourses, gradeId, courseId);
        for (Map<String, Object> worker : courseDirector) {
            Map<String, Object> w = commonUserService.getUserInfoByTeacher(worker);
            worker.put("paperId", paperId);
            worker.put("courseId", courseId);
            worker.put("workerUserId", w.get("userId"));
            worker.put("workerName", w.get("userName"));
        }
        examWorker.addAll(courseDirector);
    }

    private void getExamUploader(Map<String, Object> course, Map<String, Object> paper, Map<Long, Map<String, Object>> baseCourseMap,
      List<Map<String, Object>> worker, List<Map<String, Object>> todoExamWorker, List<Map<String, Object>> existExamUploader) {
        List<Map<String,Object>> examWorker = new ArrayList<>();
        for (Map<String, Object> ww : existExamUploader) {
            long courseId = Long.parseLong(paper.get("courseId").toString());
            Map<String, Object> wk = new HashMap<>(ww);
            wk.put("courseId", courseId);
            MapUtil.copy(paper, wk, "courseName", "schoolId", "paperId", "artsScience");
            wk.put("workerPhone", wk.getOrDefault("teacherPhone", 0));
            todoExamWorker.add(wk);
            examWorker.add(wk);
            if (course.get("memberStr").toString().contains(",")) {
                for (String cid : course.get("memberStr").toString().split(",")) {
                    Map<String, Object> p = new HashMap<>(wk);
                    p.put("courseId", cid);
                    p.put("courseName", baseCourseMap.getOrDefault(Long.valueOf(cid), new HashMap<>()).getOrDefault("courseName", "未知"));
                    worker.add(p);
                }
            }
        }
        worker.addAll(examWorker);
    }


    /**
     * 更新考试信息
     *
     * @param params examId examName examType startDate endDate
     */
    public void updateExam(Map<String, Object> params) {
        Verify.of(params).isValidId("examId")
                .isNotBlank("examName").isNumeric("examType").isNotBlank("startDate").isNotBlank("endDate")
                .verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.update("ExamMapper.updateExam", params);
    }

    /**
     * 删除考试
     *
     * @param params examId
     */
    @Transactional(ExamRepository.TRANSACTION)
    public String deleteExam(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("examId").verify();

        // 数据校验,是否是自己的考试，是否有已经完成的考试
        Long examId = Long.valueOf(params.get("examId").toString());
        Map<String, Object> exam = commonRepository.selectOne("ExamMapper.getExam", examId);
        if (MapUtils.isEmpty(exam)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试不存在或已经被删除");
        }

        if (Integer.parseInt(exam.get("examStatus").toString()) > DictUtil.getDictValue("examStatus", "executing")) {
            throw new CommonException(ResponseStatusEnum.DATA_BE_USED, "不能删除已完成的考试");
        }

        if (isHomework(exam)) {
            Integer itemStatus = commonRepository.selectOne("ExamMapper.getExamResultCount", examId);
            if (itemStatus != null && itemStatus > 0) {
                throw new CommonException(ResponseStatusEnum.DATA_BE_USED, "部分学生已经提交,不能删除考试");
            }
        } else {
            Integer itemStatus = commonRepository.selectOne("ExamMapper.getExamItemStatus", examId);
            if (itemStatus != null && itemStatus > 0) {
                throw new CommonException(ResponseStatusEnum.DATA_BE_USED, "部分学生已经评分,不能删除考试");
            }
        }
        //记录删除的Exam信息,恢复用,暂时通过将删除数据保存在mongodb中,作为逻辑删除考试的临时方案
        params.putAll(exam);
        logger.warn("开始删除考试{}",exam);
        examDataCleanService.cleanMongo(params);
        //考试其他相关信息暂时留存,避免误删除
        commonRepository.delete("ExamDataDeleteMapper.deleteExam", examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamPaper", examId);
        logger.warn("删除考试完成{}",exam);
        return "成功删除";

        // 通知第三方考试已删除 todo
//        List<Map<String, Object>> data = new ArrayList<>();
//        Map<String, Object> notifyParams = new HashMap<>();
//        notifyParams.put("examId", examId);
//        notifyParams.put("examStatus", -1);
//        data.add(notifyParams);
//        DataNotification.sendAsyncExamNotify(data);
    }

    /**
     * 作业报告 删除线上作业
     * @param params examId
     * @return       message
     */
    public String deleteOnlineHomework(Map<String, Object> params) {
        // 参数校验
        Verify.of(params).isValidId("examId")
                .isValidId("userId")
                .isValidId("teacherId")
                .isNotBlank("userName")
                .verify();

        boolean isSuc = examDataCleanService.cleanAll(params);
        if(!isSuc){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,"删除失败，请联系技术员处理");
        }

        return "成功删除";
    }


    /**
     * 获取考试信息  examId
     */
    public Map<String, Object> getExamInfo(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").verify();

        Map<String, Object> exam = examService.getExamDetail(params);

        List<Map<String, Object>> list;
        Map<String, Object> rm = new HashMap<>(2);
        rm.put("userId", exam.get("creatorId"));
        if (!checkExamTypeNotUnion(exam)) {
            //获取联考学校信息
            list = commonRepository.selectList("ExamSchoolMapper.getExamSchool", params);
            String schoolIds = list.stream().map(m -> m.get("schoolId").toString()).collect(Collectors.joining(","));
            rm.put("schoolIds", schoolIds);
            Map<String, Object> result = commonStudentService.getClassStudentCount(rm);
            Map<Long, String> schoolIdMapping = (Map<Long, String>) result.get("schoolIdMapping");
            for (Map<String, Object> c : list) {
                String address = schoolIdMapping.get(Long.valueOf(c.get("schoolId").toString()));
                c.put("address", address);
            }
            exam.put("teacherPhone", result.get("teacherPhone"));

        } else {
            //获取非联考班级信息
            list = commonRepository.selectList("ExamClassMapper.getExamClassInfoWithStuCount", params);
            Map<String, Object> result = commonStudentService.getClassStudentCount(rm);
            exam.put("teacherPhone", result.get("teacherPhone"));
        }

        exam.put("examInfo", list);
        return exam;
    }

    /**
     * 保存作业
     *
     * @param params examCourse examPaper examClass examType gradeYear gradeType examName correctMode endDate startDate schoolId
     * @return examId
     */
    @Transactional(ExamRepository.TRANSACTION)
    public String insertHomework(Map<String, Object> params) {
        Verify.of(params).isNotBlank("examCourse").isNotBlank("examPaper").isNotBlank("examClass")
                .isNumeric("examType").isNumeric("gradeYear").isNumeric("gradeType").isNotBlank("examName")
                .isNumeric("correctMode").isNotBlank("startDate").isNotBlank("endDate").isValidId("schoolId")
                .isValidId("areaId");

        List<Map<String, Object>> examPaper = (List<Map<String, Object>>) params.get("examPaper");
        int stage = commonGradeService.getStageByGradeId(params);
        params.put("stage",stage);
        // 线上作业默认都是自动公布
        params.put("autoPublish",1);
        params.put("currentTime", DateUtil.getCurrentDateTime());
        boolean isOnline = ObjectUtil.isValueEquals(MapUtils.getInteger(params, "examType"), DictUtil.getDictValue("examType", "online"));
        //保存考试
        commonRepository.insert("ExamMapper.insertExam", params);
        Long examId = MapUtils.getLong(params, "examId");
        //保存考试学校
        commonRepository.insert("ExamSchoolMapper.insertExamSchool", params);
        //保存课程
        List<Map<String, Object>> examCourse = (List<Map<String, Object>>) params.get("examCourse");
        examCourse.forEach(c -> c.putAll(courseService.getCourseDetail(c)));
        commonRepository.insert("ExamMapper.insertExamCourse", params);
        //校验班级
        List<Map<String, Object>> examClass = MapUtil.getListMap(params, "examClass");
        List<Long> classIds = examClass.stream().map(x -> MapUtils.getLong(x, "classId")).collect(toList());
        String classIdStr = StringUtils.join(classIds, ",");
        params.put("classIds", classIdStr);
        checkExamClass(params);
        //保存考试班级
        commonRepository.insert("ExamMapper.insertExamClass", params);
        //保存试卷
        examPaper.forEach(p -> {
            p.put("currentTime", params.get("currentTime"));
            p.put("examId", examId);
            p.put("userId", params.get("userId"));
            p.put("userName", params.get("userName"));
        });
        examPaperService.replaceExamPaperListFullMark(examPaper);
        commonRepository.insert("ExamMapper.insertExamPaper", examPaper);
        commonRepository.insert("ExamMapper.insertExamSchoolPaper", params);
        examUpdateAnswerService.initExamPaperAdditionalAnswer(params, examPaper);

        // 保存班级试卷
        Map<String, Object> paper = examPaper.get(0);
        List<Map<String, Object>> examClassPaper = new ArrayList<>();
        for (Map<String, Object> cs : examClass) {
            cs.putAll(paper);
            cs.put("examClassPaperStatus", paper.get("examPaperStatus"));
            examClassPaper.add(cs);
        }
        params.put("examClassPaper", examClassPaper);
        commonRepository.insert("ExamMapper.insertExamClassPaper", params);

        //保存学生
        List<Map<String, Object>> student = commonStudentService.getClassStudentInfo(classIds);
        if (CollectionUtils.isEmpty(student)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "班级下没有学生！");
        }
        //移除学生
        Map<String, Object> p = new HashMap<>();
        p.put("tagIds", params.get("tagIds"));
        p.put("studentIds", params.get("studentIds"));
        p.put("classIds", classIdStr);
        examInitService.removeStudent(p, student, false);

        //保存学生课程
        Map<String, Object> paperMap = examPaper.get(0);
        Map<String, Object> courseMap = examCourse.get(0);
        Integer resultStatus = DictUtil.getDictValue("resultStatus", "absent");
        student.forEach(s -> {
            s.put("paperId", paperMap.get("paperId"));
            s.put("courseId", paperMap.get("courseId"));
            s.put("artsScience", courseMap.get("artsScience"));
            s.put("resultStatus", resultStatus);
        });
        examStudentService.insertStudentInfoToExam(params, examId, student, student, false);
        if (isOnline) {
            //线上作业 保存item
            List<Map<String, Object>> examItem = homeworkHandleService.initItemByMarkQn(paperMap, student);
            if(CollectionUtils.isEmpty(examItem)){
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"试卷或答题卡没有试题");
            }

            params.put("examItem", examItem);
            commonRepository.insert("ExamMapper.insertExamItem", params);
        }

        // 更新试卷状态为已发布
        updatePaperStatus(examPaper);

        // 上传答案待办
        Map<String,Object> paperDetail = ownPaperService.getSimplePaperDetail(paper);
        if (Integer.valueOf(paperDetail.get("answerStatus").toString()) == 0) { //0 未上传答案
            examInitService.createPaperAnswerUploadTodo(params,paper);
        }

        //待办，互评，定时发布
        params.putAll(paperMap);
        initHomework(params);

        //阅卷安排初始化 t_exam_worker t_exam_teacher
        initPaperReadByOnline(params, examPaper.get(0));

        examConfigService.initOrResetSchoolSystemStat(params);

        if (isOnline) {
            // 初始化作答Redis，异步执行
            Map<String,Object> p2 = new HashMap<>(params);
            p2.remove("studentIds");
            homeworkPreparationService.executeExamMarkPreparation(p2);
        }

        return params.get("examId").toString();
    }

    public void initItem(Map<String, Object> params){
        Verify.of(params).isValidId("examId").isValidId("paperId").verify();
        //保存item
        params.put("currentTime",DateUtil.getCurrentDateTime());
        List<Map<String, Object>> student = commonRepository.selectList("ExamMapper.getExamStudentList", params);
        List<Map<String, Object>> structure = ownExamPaperService.getPaperStructure(params);
        List<Map<String, Object>> item = new ArrayList<>();
        for (Map<String, Object> s : structure) {
            student.forEach(stu -> {
                Map<String, Object> rs = new HashMap<>();
                rs.putAll(params);
                rs.putAll(s);
                rs.putAll(stu);
                item.add(rs);
            });
        }
        params.put("examItem", item);
        commonRepository.insert("ExamMapper.insertExamItem", params);
    }

    /**
     * 更新试卷状态
     * @param examPaper 考试试卷
     */
    private void updatePaperStatus(List<Map<String, Object>> examPaper) {
        Map<String, Object> paperParams = new HashMap<>();
        List<Long> paperIds = examPaper.stream().map(item -> Long.valueOf(item.get("paperId").toString())).collect(toList());
        paperParams.put("paperIds", paperIds);
        ownPaperService.updatePaperAndQuestionStatusToUsed(paperParams);
    }

    /**
     * 线上作业生成互评数据
     *
     * @param params 考试参数
     */
    private void initHomework(Map<String, Object> params) {
        List<Map<String, Object>> examClass = (List<Map<String, Object>>) params.get("examClass");
        for (Map<String, Object> ec : examClass) {
            //生成自评互评关联数据
//            int correctMode = Integer.parseInt(ec.get("correctMode").toString());
//            ec.put("examId", params.get("examId"));
//            List<Map<String, Object>> examResult = commonRepository.selectList("ExamMapper.getExamResult", ec);
//            switch (correctMode) {
//                case 3: //自评 id对应id  提交时生成待办
//                    examResult.forEach(s -> {
//                        s.put("studentId", s.get("evaluationStudentId"));
//                        s.put("studentName", s.get("evaluationStudentName"));
//                        s.put("userId", params.get("userId"));
//                        s.put("userName", params.get("userName"));
//                        s.put("currentTime", new Date());
//                    });
//                    break;
//                case 4://互评  随机  将n个学生打乱  第一个取下一个，最后一个取第一个
//                    Collections.shuffle(examResult);// orderBy random
//                    for (int i = 0; i < examResult.size(); i++) {
//                        Map<String, Object> stu = examResult.get(i);
//                        Map<String, Object> nextStu;
//                        if (i == examResult.size() - 1) {
//                            nextStu = examResult.get(0);
//                        } else {
//                            nextStu = examResult.get(i + 1);
//                        }
//                        stu.put("studentId", nextStu.get("evaluationStudentId"));
//                        stu.put("studentName", nextStu.get("evaluationStudentName"));
//                        stu.put("userId", params.get("userId"));
//                        stu.put("userName", params.get("userName"));
//                        stu.put("currentTime", new Date());
//                    }
//                    break;
//            }
//            if (4 == correctMode) {
//                commonRepository.batchInsert("ExamMapper.insertExamResultEvaluation", examResult);
//            }
            //初始化提醒次数 2次
            String key = JedisUtil.getKey("remindSubmit", params.get("examPaperId").toString(), ec.get("classId").toString());
            JedisTemplate.execute(jedis -> jedis.setex(key, 60 * 60 * 24 * 7, "2"));
        }
        // 存储到任务队列中
        try {
            long timeout = DateUtil.parseDateTime(params.get("endDate").toString()).getTime();
            long startTime = DateUtil.parseDateTime(params.get("startDate").toString()).getTime();
            if (timeout <= System.currentTimeMillis()) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "作业时间间隔太短，请适当调整");
            }
            //作业提交
            double score = timeout * 1.0;
            JedisTemplate.execute(jedis -> jedis.zadd(JedisUtil.getKey("homeworkSubmitQueue"), score, params.get("examPaperId").toString()));
            //生成代办
            double start = startTime * 1.0;
            JedisTemplate.execute(jedis -> jedis.zadd(JedisUtil.getKey("homeworkTodoQueue"), start, (params.get("examId").toString() + "-" + params.get("examPaperId").toString())));
        } catch (ParseException e) {
            LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
        }
    }
    
    public Map<String, Object> getSimpleExamInfoNullable(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").verify();
        Long examId = Long.valueOf(params.get("examId").toString());
        return commonRepository.selectOne("ExamMapper.getExam", examId);
    }
    
    public Map<String, Object> getSimpleExamInfo(Map<String, Object> params) {
        return getSimpleExamInfo(params, null);
    }
    
    public Map<String, Object> getSimpleExamInfo(Map<String, Object> params, String notExistErrInfo) {
        Map<String, Object> exam = getSimpleExamInfoNullable(params);
        if (MapUtils.isEmpty(exam)) {
            if (notExistErrInfo == null) {
                notExistErrInfo = "考试不存在或已经被删除";
            }
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, notExistErrInfo);
        }
        return exam;
    }
    

    /**
     * 根据examIds获取考试详情
     *
     * @param params examIds
     * @return
     */
    public List<Map<String, Object>> getExamInId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("examIds")
                .verify();

        return commonRepository.selectList("ExamMapper.getExamInId", params);
    }

    /**
     * 获取区域最新N场考试
     *
     * @param params
     * @return
     */
    public List<Map<String, Object>> getExamListByArea(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("areaId")
                .isNumeric("gradeYear")
                .verify();

        return commonRepository.selectList("ExamMapper.getExamListByArea", params);
    }

    /**
     * 获取考试试卷
     *
     * @param params examId paperId
     * @return
     */
    public Map<String, Object> getExamPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();

        return commonRepository.selectOne("ExamMapper.getExamPaper", params);
    }

    /**
     * 获取考试试卷列表
     *
     * @param params examId courseId
     * @return
     */
    public List<Map<String, Object>> getExamPaperList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("courseId")
                .verify();

        return commonRepository.selectList("ExamMapper.getExamPaperList", params);
    }

    /**
     * 获取课程成绩已公布的考试（分页）
     *
     * @param params courseId [currentIndex] [pageSize]
     * @return
     */
    public Map<String, Object> getCourseExamByPage(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();

        Map<String, Object> result = new HashMap<>();

        Integer count = commonRepository.selectOne("ExamMapper.getExamByCourseIdCount", params);

        if (count == null || count == 0) {
            result.put("totalCount", 0);
            result.put("list", Collections.emptyList());
        }

        List<Map<String, Object>> examList = commonRepository.selectList("ExamMapper.getExamByCourseId", params);

        result.put("totalCount", count);
        result.put("list", examList);

        return result;
    }

    /**
     * 获取课程成绩已公布的考试
     *
     * @param params courseId
     * @return
     */
    public List<Map<String, Object>> getExamByCourseId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();

        return commonRepository.selectList("ExamMapper.getExamByCourseId", params);
    }

    /**
     * 获取试卷的基本信息
     */
    public Map<String, Object> getExamBaseInfo(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .verify();

        Map<String, Object> examBaseInfo = commonRepository.selectOne("ExamMapper.getExamBaseInfo", params);
        if (MapUtils.isEmpty(examBaseInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试不存在");
        }
        return examBaseInfo;
    }

    /**
     * 获取试卷的基本信息
     */
    public Map<String, Object> getExamBaseInfoNullable(Map<String, Object> params) {
        return commonRepository.selectOne("ExamMapper.getExamBaseInfo", params);
    }

    /**
     * 获取试卷的基本信息
     */
    public List<Map<String, Object>> getExamsBaseInfo(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("examIdList")
                .verify();

        return commonRepository.selectList("ExamMapper.getExamsBaseInfo", params);

    }

    /**
     * 根据courseId查询考试的学生成绩
     *
     * @param params examId courseId
     * @return
     */
    public List<Map<String, Object>> getExamResultByCourseId(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("examId")
                .isNumeric("courseId")
                .verify();

        return commonRepository.selectList("ExamMapper.getExamResultByCourseId", params);
    }

    public List<Map<String, Object>> getExamToPushNotice(Map<String, Object> parameterMap) {
        // 获取当前时间，减去一天作为时间参数
        return commonRepository.selectList("ExamMapper.getExamToPushNotice", parameterMap);
    }

    public void updateExamPushStatus(Map<String, Object> parameterMap) {
        commonRepository.selectList("ExamMapper.updateExamPushStatus", parameterMap);
    }

    public List<Map<String, Object>> getExamResultPushStudent(Map<String, Object> params) {
        return commonRepository.selectList("ExamMapper.getExamResultPushStudent", params);
    }

    /**
     * 查询关联这张试卷的所有考试
     *
     * @param params paperId
     * @return
     */
    public List<Map<String, Object>> getExamListByPaperId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();

        return commonRepository.selectList("ExamMapper.getExamListByPaperId", params);
    }

    /**
     * 更新item的questionNumberMapping
     *
     * @param params paperId questionNumberMapping
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateExamItemQuestionNumber(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotNull("questionNumberMapping")
                .verify();

        // 查询关联这张试卷的所有考试
        List<Map<String, Object>> examList = getExamListByPaperId(params);
        if (CollectionUtils.isEmpty(examList)) {
            return;
        }

        // 更新item的questionNumber
        params.put("currentTime", DateUtil.getCurrentDateTime());
        for (Map<String, Object> exam : examList) {
            Map<Integer, Integer> questionNumberMapping = (Map<Integer, Integer>) params.get("questionNumberMapping");
            questionNumberMapping.forEach((k, v) -> {
                params.put("examId", exam.get("examId"));
                params.put("oldQuestionNumber", k);
                params.put("newQuestionNumber", v);
                commonRepository.update("ExamMapper.updateExamItemQuestionNumber", params);
            });
        }
    }
    
    /**
     * 更新考试状态
     * @param params examId + 三件套
     */
    public void updateExamStatusForScoreImport(Map<String, Object> params) {
        Verify.of(params).isValidId("examId")
                .verify();
        ParamsUtil.setCurrentTimeIfAbsent(params);
        params.put("examStatus", DictUtil.getDictValue("examStatus", "unpublished"));
        commonRepository.update("ExamMapper.updateExam", params);
    }

    /**
     * 更新考试状态
     * @param params examId examStatus
     */
    public void updateExamStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isInteger("examStatus")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();

        params.put("currentTime", DateUtil.getCurrentDateTime());
        commonRepository.update("ExamMapper.updateExamStatus", params);
    }

    /**
     * 上传答案后，批量更新试卷状态和客观题分数，并删除待办
     */
    public void batchUpdateExamByUploadPaperAnswer(Map<String, Object> params, List<Map<String, Object>> additionalAnswer) {
        Verify.of(params)
          .isValidId("paperId")
          .verify();

        long paperId = MapUtil.getLong(params, "paperId");
        List<Map<String, Object>> questions = PaperUtil.getPaperStructure(paperManager.getPaper(paperId));
        examQuestionStructureService.changePaperQn2MarkQn(paperId, questions);
        Map<String, Map<String, Object>> questionMap = examUpdateAnswerService.getQuestionMap(questions, additionalAnswer);
        // 先获取考试，不获取考试类型是作业的，且考试的状态需要是进行中
        params.put("homeworkType", getHomeworkExamType());
        List<Map<String, Object>> examPapers = commonRepository.selectList("ExamPaperMapper.getExamByPaperByExam", params);
        // 客观题需要更新分值
        Map<String, Object> baseParam = MapUtil.copy(params, "userId", "userName", "fullMark");
        baseParam.put("currentTime", DateUtil.getCurrentDateTime());
        baseParam.put("readStatus", DictUtil.getDictValue("readStatus", "finished"));
        Map<String, List<Map<String, Object>>> key2UpdateParams = examUpdateAnswerService.getKey2UpdateParams(questionMap);
        for (Map<String, Object> examPaper : examPapers) {
            examPaper.putAll(baseParam);
            examPaperService.updateExamPaperStatus(examPaper, key2UpdateParams, questionMap);
        }

        // 再更新线上、线下作业的
        examPaperService.batchUpdateHomework(params, key2UpdateParams, questionMap);
    }

    /**
     * 判断当前角色是不是考试创建者
     * @param params examId、userId
     */
    public boolean isExamCreator(Map<String, Object> params) {
        return isExamCreator(params, getExamDetail(params));
    }

    /**
     * 判断当前角色是不是考试创建者
     * @param params examId、userId
     * @param examDetail 考试信息
     */
    public boolean isExamCreator(Map<String, Object> params, Map<String, Object> examDetail) {
        return ObjectUtil.isValueEquals(MapUtils.getLong(params, "userId"), MapUtils.getLong(examDetail, "creatorId"));
    }

    /**
     * 根据userId、examIds获取其中属于用户自己创建的examIds
     * @param userId 用户id
     * @param examIds 考试ids
     * @return examIds中属于用户自己创建的examIds
     */
    public Set<Long> getExamIdsCreateByOwn(Long userId, Set<Long> examIds) {
        if (examIds.isEmpty()) {
            return Collections.emptySet();
        }
        return new HashSet<>(commonRepository.selectList("ExamMapper.getExamIdsCreateByOwn",
          MapUtil.of("examIds", examIds, "userId", userId)));
    }

    /**
     * 判断考试类型是不是作业
     */
    public boolean isHomework(Map<String, Object> params) {
        return DictUtil.isEquals(MapUtils.getInteger(ObjectUtil.isNumeric(params.get("examType")) ? params : getExamDetail(params),
            "examType"), "examType", "online", "offline");
    }

    /**
     * 判断考试类型是不是教辅作业
     */
    public boolean isStudyGuideHomework(Long examId) {
        return isStudyGuideHomework(MapUtil.of("examId", examId));
    }

    /**
     * 判断考试类型是不是教辅作业
     */
    public boolean isStudyGuideHomework(Map<String, Object> params) {
        Integer examType = MapUtils.getInteger(params, "examType");
        if (!ObjectUtil.isNumeric(examType)) {
            examType = MapUtils.getInteger(getExamDetail(params), "examType");
        }
        return examType.equals(ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode());
    }

    /**
     * 判断考试类型是不是非作业
     * 考试和作业的有些展示项不同
     */
    public boolean isNotHomework(Map<String, Object> params) {
        return !isHomework(params);
    }

    /**
     * 获取作业的所有考试类型
     */
    public List<Integer> getHomeworkExamType(){
        return Arrays.asList(DictUtil.getDictValue("examType", "online"), DictUtil.getDictValue("examType", "offline"));
    }

    /**
     * 判断考试的类型是否是普通的校内考（不包括作业）
     * 具体包括：常规诊断 general、期中诊断 midterm、期末诊断 terminal、模拟训练 simulation
     */
    public boolean checkExamTypeSchoolUsualExam(Map<String, Object> params) {
        return DictUtil.isEquals(MapUtils.getInteger(ObjectUtil.isNumeric(params.get("examType")) ? params :
            getExamDetail(params), "examType"), "examType", "general", "midterm", "terminal", "simulation");
    }
    /**
     * 获取普通的校内考（不包括作业）的所有考试类型
     * 具体包括：常规诊断 general、期中诊断 midterm、期末诊断 terminal、模拟训练 simulation
     */
    public Set<Integer> getSchoolUsualExamType() {
        return DictUtil.getDictValues("examType", "general", "midterm", "terminal", "simulation");
    }

    /**
     * 获取普通的校内考（不包括作业）+联考、区域考的所有考试类型
     * 具体包括：常规诊断 general、期中诊断 midterm、期末诊断 terminal、模拟训练 simulation、跨校诊断 union、 区域诊断 area
     */
    public Set<Integer> getAllExamType() {
        return DictUtil.getDictValues("examType", "general", "midterm", "terminal", "simulation", "union", "area");
    }

    /**
     * 判断考试的类型是否是线上作业
     */
    public boolean checkExamTypeOnline(Map<String, Object> params) {
        Integer examType;
        if (ObjectUtil.isNumeric(params.get("eamType"))) {
            examType = MapUtils.getInteger(params, "examType");
        } else {
            Map<String, Object> exam = commonRepository.selectOne(ObjectUtil.isValidId(params.get("examPaperId"))
              ? "ExamMapper.getExamByExamPaperId" : "ExamMapper.getExamDetail", params);
            examType = MapUtils.getInteger(exam, "examType");
        }
        return ObjectUtil.isValueEquals(examType, DictUtil.getDictValue("examType", "online"));
    }

    /**
     * 判断考试的类型是否是线下作业
     */
    public boolean checkExamTypeOffline(Map<String, Object> params) {
        return DictUtil.isEquals(MapUtils.getInteger(ObjectUtil.isNumeric(params.get("examType")) ? params : getExamDetail(params),
                "examType"), "examType", "offline");
    }

    /**
     * 判断考试的类型是不是联考或区域考
     */
    public boolean checkExamTypeUnion(Map<String, Object> params) {
        return DictUtil.isEquals(MapUtils.getInteger(ObjectUtil.isNumeric(params.get("examType")) ? params : getExamDetail(params),
          "examType"), "examType", "union", "area");
    }

    /**
     * 判断考试的类型是不是非联考、区域考、异步联考
     */
    public boolean checkExamTypeNotUnion(Map<String, Object> params) {
        return !DictUtil.isEquals(MapUtils.getInteger(ObjectUtil.isNumeric(params.get("examType")) ? params : getExamDetail(params),
            "examType"), "examType", "union", "area", "asyncUnion");
    }

    /**
     * 判断考试的类型是不是区域考
     * @return true：是区域考  false：不是区域考
     */
    public boolean checkExamTypeArea(Map<String, Object> params) {
        return DictUtil.isEquals(MapUtils.getInteger(ObjectUtil.isNumeric(params.get("examType")) ? params : getExamDetail(params),
          "examType"), "examType", "area");
    }

    /**
     * 判断作业是否已经上传了答题卡
     */
    public boolean homeworkHavingAnswerCard(Map<String, Object> params) {
        Map<String, Object> answerCardSingle = commonRepository.selectOne("ExamMapper.getExamAnswerCardSingle", params);
        return MapUtils.isNotEmpty(answerCardSingle);
    }

    /**
     * 好专业-区域数据源中，根据区域id获取考试列表
     * 联考、异步联考：根据areaId从t_area_exam_mapping中查询
     * 区域考：根据areaId从t_exam_area中查询，只查询和t_exam_area中最小父节点的areaId相同的考试，即属于当前区域的考试
     * @param areaId    区域id
     * @return 考试列表
     */
    public List<ExamNameBO> getRegionExamByRegionId(Long areaId) {
        return commonRepository.selectList("ExamMapper.getRegionExamByRegionId", areaId);
    }

    /**
     * 判断考试是否使用的是新版的扫描识别流程
     * @param params examId
     */
    public boolean useNewScanVersion(Map<String, Object> params){
        return MapUtils.getFloat(getExamDetail(params), "version", 2.0f) == 3.0f;
    }

    /**
     * 获取考试的数据源类型
     * @param params examId 考试id
     * @return 1：基础数据 2：数据上报 目前只有联考可能是2，其他都是1，默认也是1
     */
    public int getExamDataSourceType(Map<String, Object> params){
        return getExamDataSourceType(MapUtils.getLong(getExamDetail(params), "escalationId"));
    }

    /**
     * 获取考试的数据源类型
     * @param escalationId 数据上报任务id，可以为null
     * @return 1：基础数据 2：数据上报 目前只有联考可能是2，其他都是1，默认也是1
     */
    public int getExamDataSourceType(Long escalationId){
        return (ObjectUtil.isValidId(escalationId) ? StudentDataSourceEnum.ESCALATION : StudentDataSourceEnum.BASE_DATA).getType();
    }

    /**
     * 判断考试的扫描方式是否是按多校混扫
     *
     * @param params examId
     */
    public boolean useMultiSchoolScanType(Map<String, Object> params) {
        return Objects.equals(MapUtils.getInteger(getExamDetail(params), "scanType"), ExamScanTypeEnum.MULTI_SCHOOL.getValue());
    }

    /**
     * 判断考试是否已经有课程已经走完了发布流程(通过考试是否存在t_exam_result数据来判断)
     * @param examId 考试id
     */
    public boolean checkExamHavingResult(long examId) {
        return commonRepository.selectOne("ExamMapper.checkExamHavingResult", examId) != null;
    }

    /**
     * 判断考试是不是阅完后自动公布成绩
     */
    public boolean isExamAutoPublish(long examId) {
        return ObjectUtil.isValueEquals(MapUtils.getInteger(getExamDetail(MapUtil.of("examId", examId)), "autoPublish"),
          AutoPublishEnum.YES.getValue());
    }

    /**
     * 获取状态是进行中的、没有任何课程走完了发布流程(通过考试是否存在t_exam_result数据来判断)的、发布方式不是成绩导入的联考、区域考
     * 网阅3.31-发布考试 修改了发布流程后，如果没有没有任何课程走完了发布流程，则此时就只会有发布诊断的待办，不再像以前那样直接就有诊断管理的待办
     * @return 考试ids
     */
    public List<Long> getUnionExamIdHasNotResult() {
        return commonRepository.selectList("ExamMapper.getUnionExamIdHasNotResult");
    }

    /**
     * 根据examId批量获取考试
     *
     * @param examIds examId列表
     * @return examId examName
     */
    public List<Map<String, Object>> getByExamIds(List<Long> examIds) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(examIds)) {
            return new ArrayList<>();
        }

        return commonRepository.selectList("ExamMapper.getByExamIds", examIds);
    }

    /**
     * 获取总分模式导入的考试
     * @param examIdSet 考试ID集合
     * @return
     */
    public List<Map<String, Object>> getTotalScoreImportExamByExamIds(List<Long> examIdSet) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(examIdSet)) {
            return Lists.newArrayList();
        }
        return commonRepository.selectList("ExamMapper.getTotalScoreImportExamByExamIds", examIdSet);
    }
    /**
     * 根据校级角色的过滤条件获取考试id
     */
    public Long getExamIdBySchUserFilter(Map<String, Object> params){
        return commonRepository.selectOne("ExamMapper.getExamIdBySchUserFilter", params);
    }

    /**
     * 判断是否还有阅卷未完成的考试
     *
     * @param examIds 考试id列表
     * @return 未完成的考试
     */
    public List<Long> getUnReadComplete(List<Long> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new ArrayList<>();
        }
        return commonRepository.selectList("ExamMapper.countUnReadComplete", examIds);
    }

    /**
     * 判断是否还有阅卷未完成的考试
     *
     * @param examIds 考试id列表
     * @return 未完成的考试
     */
    public List<String> getUnReadCompleteExamName(List<Long> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new ArrayList<>();
        }
        return commonRepository.selectList("ExamMapper.getUnReadCompleteExamName", examIds);
    }

    /**
     * 判断是否还有阅卷完成但未满一个月的考试
     * 需配合{@link #getUnReadComplete(List)}使用
     *
     * @param examIds 考试id列表
     * @return 未完成的考试
     */
    public List<Long> getReadComplete(List<Long> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new ArrayList<>();
        }
        return commonRepository.selectList("ExamMapper.countReadComplete", examIds);
    }

    /**
     * 判断是否还有阅卷完成但未满一个月的考试
     * 需配合{@link #getUnReadComplete(List)}使用
     *
     * @param examIds 考试id列表
     * @return 未完成的考试
     */
    public List<String> getReadCompleteExamName(List<Long> examIds) {
        if (CollectionUtils.isEmpty(examIds)) {
            return new ArrayList<>();
        }
        return commonRepository.selectList("ExamMapper.getReadCompleteExamName", examIds);
    }

    /**
     * 清理答题卡使用 获取考试信息
     * @param examId 考试id
     * @return null or {examId, modifyDateTime}
     */
    public Map<String, Object> getExamInfoForVacuum(long examId) {
        return commonRepository.selectOne("ExamMapper.getExamInfoForVacuum", examId);
    }

    public Map<String, Object> getDeleteStatus(Map<String, Object> params) {
        int count = commonRepository.selectOne("ExamCombineRecordMapper.getMergeExamCount", params);
        Map<String, Object> result = new HashMap<>();
        result.put("examCombined", count > 0 );
        return result;
    }

    public boolean isCorrectModeByClass(long examId) {
        Map<String, Object> exam = getExamDetail(MapUtil.of("examId", examId));
        boolean isScanningByClass = false;
        int correctMode = MapUtil.getInt(exam, "correctMode");
        return correctMode == DictUtil.getDictValue("correctMode", "readByClass");
}

    public List<ExamSelectionVO> getExamOfIndividualWrongBook(ExamQueryDTO examQueryDTO) {
        examQueryDTO.verify();

        return commonRepository.selectList("ExamMapper.getExamOfIndividualWrongBook", examQueryDTO);
    }
}
