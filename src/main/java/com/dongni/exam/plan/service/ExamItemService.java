package com.dongni.exam.plan.service;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.mark.ai.bean.ExamItem;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <br/>
 * @date 2021/04/12 <br/>
 *
 */
@Service
public class ExamItemService {

    @Autowired
    private ExamRepository examRepository;
    
    /**
     * 考试导入模式 重新导入时删除导入数据
     * @param params examId
     *               [paperId] 这里不能用courseId进行限制，因为item中的courseId是用的子课程的id
     */
    public void deleteExamItemForScoreImport(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .verify();
        Map<String, Object> deleteParams = new HashMap<>(params);
        if (!ObjectUtil.isValidId(deleteParams.get("paperId"))) {
            deleteParams.remove("paperId");
        }
        examRepository.delete("ExamItemMapper.deleteExamItemForScoreImport", deleteParams);
    }

    /**
     * 先删除后插入考试结果
     * 用于成绩导入
     * @param params 通过examItemList进行插入, 每个item包含所有需要插入的字段，每个item包含所有需要插入的字段，如：用户三件套和examId paperId等
     */
    public void insertExamItemForScoreImport(Map<String, Object> params) {
        // 数据处理，批量插入，防止item数据过多
        examRepository.batchInsert("ExamItemMapper.insertExamItemForScoreImport", MapUtil.of("list", params.get("examItemList")), 5000);
    }
    
    /**
     * 缺考申报审核通过后，对申报的学生科目进行删除明细操作
     *     因为正常的学生也可以设为缺考
     * @param params absentDeclarationId
     * @return 删除的数量
     */
    public int deleteExamItemForDeclare(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("absentDeclarationId")
                .verify();
        return examRepository.delete("ExamItemMapper.deleteExamItemForDeclare", params);
    }

    /**
     * 更新questionNumber
     * @param paramsList
     */
    public void updateQuestionNumber(List<Map<String, Object>> paramsList) {
        if (CollectionUtils.isEmpty(paramsList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "参数错误");
        }

        examRepository.batchUpdate("ExamItemMapper.updateQuestionNumber", paramsList);
    }

    /**
     * 删除学校中途不参与考试的学生item
     * @param params examId schoolId paperId
     */
    public void deleteExamItemForSchoolAbsent(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .isValidId("paperId")
                .verify();
        examRepository.delete("ExamItemMapper.deleteExamItemForSchoolAbsent", params);
    }
    
    
    /**
     * 清理答题卡使用 获取比examId大的examIdList
     * @param examId where exam_id > #{examId}
     * @param limit  n个 默认1000
     * @return examIdList
     */
    public List<Long> getExamIdListGtForVacuum(Long examId, Integer limit) {
        if (limit == null) { limit = 1000; }
        if (examId == null) { examId = 0L; }
        Map<String, Object> params = MapUtil.of(
                "examId", examId,
                "limit", limit
        );
        return examRepository.selectList("ExamItemMapper.getExamIdListGtForVacuum", params);
    }
    
    /**
     * 清理答题卡使用 获取考试的最后一个修改时间
     * @param examId examId
     * @return exam_id
     *         lastModifyDateTime   MAX(modify_date_time)
     */
    public Map<String, Object> getExamItemMaxModifyForVacuum(long examId) {
        return examRepository.selectOne("ExamItemMapper.getExamItemMaxModifyForVacuum", MapUtil.of("examId", examId));
    }
    
    /**
     * 清理答题卡使用 获取考试的item.save_file_url list
     * @param examId  考试id
     * @param answerCardDirPathList 排除掉的前缀 可以为空
     * @return [saveFileUrl]
     */
    public List<String> getExamItemSaveFileUrlListForVacuum(long examId, List<String> answerCardDirPathList) {
        if (CollectionUtils.isNotEmpty(answerCardDirPathList)) {
            answerCardDirPathList = answerCardDirPathList.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .distinct()
                    .collect(Collectors.toList());
        }
        Map<String, Object> params = MapUtil.of(
                "examId", examId,
                "answerCardDirPathList", answerCardDirPathList
        );
        
        int saveFileUrlCount = examRepository.selectOne("ExamItemMapper.getExamItemSaveFileUrlCountForVacuum", params);
        if (saveFileUrlCount > 99999) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "清理答题卡时获取考试的item.save_file_url超过10万条数据: " + saveFileUrlCount);
        }
        if (saveFileUrlCount == 0) {
            return new ArrayList<>();
        }
        return examRepository.selectList("ExamItemMapper.getExamItemSaveFileUrlListForVacuum", params);
    }

    public List<ExamItem> getTestExamItems(Map<String, Object> params) {
        return examRepository.selectList("ExamItemMapper.getAIItems", params);
    }

    /**
     * 根据examItemId获取其所在的学校id
     * @param examItemId 小题明细id
     * @return schoolId
     */
    public Long getSchoolIdByExamItemId(Long examItemId){
        return examRepository.selectOne("ExamItemMapper.getSchoolIdByExamItemId", examItemId);
    }

    /**
     * 根据examItemId获取其明细
     *
     * @param examItemId 小题明细id
     * @return examItem信息
     */
    public Map<String, Object> getByExamItemId(Long examItemId){
        return examRepository.selectOne("ExamItemMapper.getByExamItemId", examItemId);
    }

    /**
     * 根据唯一索引获取其明细
     *
     * @param params examId paperId questionNumber studentId
     * @return examItem信息
     */
    public Map<String, Object> getByExamItemLogicId(Map<String, Object> params){
        return examRepository.selectOne("ExamItemMapper.getByExamItemLogicId", params);
    }
}
