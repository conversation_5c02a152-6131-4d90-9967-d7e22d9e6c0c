package com.dongni.exam.plan.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.view.report.service.ExamStatReportService;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.common.utils.DataTypeConverter;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.dispiay.service.DisplayExamService;
import com.dongni.exam.dispiay.service.ExamExtraVisibleAccountService;
import com.dongni.tiku.common.util.MapUtil;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

/**
 * Created by Heweipo on 2019/1/28.
 * <p>
 * 最新考试接口
 */
@Service
public class ExamLatestService {

    private static final Logger log = LogManager.getLogger(ExamLatestService.class);

    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private CommonClassService commonClassService;
    private MongoDatabase mongo;

    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private DisplayExamService displayExamService;

    @Autowired
    private ExamStatReportService examStatReportService;

    @Autowired
    private ExamService examService;

    @Autowired
    private ExamListService examListService;
    @Autowired
    private ExamExtraVisibleAccountService examExtraVisibleAccountService;

    @Autowired
    public ExamLatestService(AnalysisMongodb mongodb) {
        this.mongo = mongodb.getMongoDatabase();
    }

    /**
     * 获取（班主任或任课老师）最新的考试或作业。
     *
     * @param params teacherId
     * @return 最新的考试或作业
     */
    @Transactional(ExamRepository.TRANSACTION)
    public List<Map<String, Object>> getLatestExamOrHomework(Map<String, Object> params) {

        // 参数校验
        if (params == null || !ObjectUtil.isValidId(params.get("teacherId")) || !ObjectUtil.isValidId(params.get("schoolId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }

        Map<String, Object> teacherParams = MapUtil.copy(params, "userId", "userType", "schoolId", "teacherId");
        //一帆说老师现在只能看到未毕业的年级的考试
        teacherParams.put("graduateStatus", 0);
        //前端现在会写死老师能看到的考试类型筛选项 2,3,4,5,7,10
        teacherParams.put("examTypes", DictUtil.getDictValues("examType", "general", "midterm", "terminal", "simulation", "union", "area"));
        //先查到所有的考试列表
        Map<String, Object> examByTeacher = examListService.getExamByTeacher(teacherParams);
        List<Map<String, Object>> examList = MapUtil.getListMap(examByTeacher, "exam");
        if (CollectionUtils.isEmpty(examList)) {
            return Collections.emptyList();
        }
        //只有考试状态是已完成，且有状态是打开的报告时才是有效的
        Map<String, Object> query = MapUtil.copy(params, "userId", "userType", "schoolId", "teacherId");
        query.put("display", 1);
        List<Map<String, Object>> filterExamList = new ArrayList<>();
        Map<Long, Long> examId2StatId = new HashMap<>(examList.size());
        for (Map<String, Object> examInfo : examList) {
            Map<String, Object> examDetail = examService.getExamDetail(examInfo);
            if (DictUtil.isEquals(MapUtils.getInteger(examDetail, "examStatus"), "examStatus", "executing")) {
                continue;
            }
            Long examId = MapUtils.getLong(examInfo, "examId");
            query.put("examId", examId);
            //状态是打开的报告
            List<Map<String, Object>> stats = examRepository.selectList("ExamStatReportMapper.getSchoolExamStat", query);
            if (stats.isEmpty()) {
                continue;
            }
            //当前用户可见的报告
            List<Map<String, Object>> userVisibleStats = examStatReportService.getExamStat(query);
            if (CollectionUtils.isEmpty(userVisibleStats)) {
                continue;
            }
            Set<Long> userVisibleStatIds = userVisibleStats.stream().map(x -> MapUtils.getLong(x, "statId")).collect(toSet());
            stats = stats.stream().filter(x -> userVisibleStatIds.contains(MapUtils.getLong(x, "statId"))).collect(Collectors.toList());
            if (!stats.isEmpty()) {
                filterExamList.add(examDetail);
                examId2StatId.put(examId, MapUtils.getLong(stats.get(0), "statId"));
            }
            if (filterExamList.size() == 2) {
                break;
            }
        }
        examList = filterExamList;
        if (examList.isEmpty()) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> examCourses = commonRepository.selectList("ExamLatestMapper.getExamCourseCount",
          MapUtil.of("exams", examList));
        Map<Long, Integer> examId2CourseCount = examCourses.stream()
          .collect(toMap(x -> MapUtils.getLong(x, "examId"), x -> MapUtils.getInteger(x, "courseCount")));
        examList.forEach(x -> x.put("courseCount", examId2CourseCount.getOrDefault(MapUtils.getLong(x, "examId"), 0)));

        //是班主任的班级
        Set<Long> headerClassIds = commonClassService.getHeaderClass(params).stream().map(x -> MapUtils.getLong(x, "classId"))
          .collect(Collectors.toSet());
        //是任课老师的班级
        Map<Long, Set<Long>> courseId2TeachClassIds = commonClassService.getTeacherClassCourse(params).stream()
          .collect(groupingBy(x -> MapUtils.getLong(x, "courseId"), mapping(x -> MapUtils.getLong(x, "classId"), toSet())));

        MongoCollection<Document> classStatCol = mongo.getCollection("examClassStat");
        MongoCollection<Document> classCourseStatCol = mongo.getCollection("examClassCourseStat");
        for (Map<String, Object> e : examList) {
            Long examId = MapUtils.getLong(e, "examId");
            params.put("examId", examId);
            long statId = examId2StatId.get(examId);
            //老师是考试创建人或者是单独可见账号，则可以看到所有的班级的数据，否则只能看到自己的执教班级的数据
            boolean showAllClass = examService.isExamCreator(params) ||
              examExtraVisibleAccountService.isExamExtraVisibleUser(examId, MapUtils.getLong(params, "userId"));
            List<String> fields = Arrays.asList("examId", "classId", "className", "courseId", "courseName", "totalStudent", "averageScore",
              "fullMark", "passRate");
            if (!showAllClass) {
                Map<String, Object> displayConfig = displayExamService.getDisplayConfigNew(params);
                if (MapUtils.isNotEmpty(displayConfig)) {
                    Map<String, Object> teacherDisplayExam = MapUtil.getListMap(displayConfig, "displayExam").stream()
                      .filter(d -> ObjectUtil.isValueEquals(d.get("userType"), DictUtil.getDictValue("userType", "teacher"))).findFirst()
                      .orElseGet(Collections::emptyMap);
                    if (ObjectUtil.isValueEquals(0, teacherDisplayExam.get("examDisplay")) &&
                      ObjectUtil.isValueEquals(0, teacherDisplayExam.get("examDisplayOnlyTeachingClass"))) {
                        fields.remove("averageScore");
                        fields.remove("passRate");
                    }
                }
            }
            List<Document> total = Collections.emptyList();
            List<Document> course = new ArrayList<>();
            Bson baseQuery = and(eq("examId", examId), eq("statId", statId));
            //班级主任
            if (CollectionUtils.isNotEmpty(headerClassIds)) {
                Bson classTotalQuery = showAllClass ? baseQuery : and(baseQuery, in("classId", headerClassIds));
                total = classStatCol.find(classTotalQuery)
                  .projection(fields(include(fields), excludeId()))
                  .sort(ascending("classSort", "classId"))
                  .into(new ArrayList<>());
            }

            //任课老师的班级
            if (MapUtils.isNotEmpty(courseId2TeachClassIds)) {
                Set<Long> examStatCourseIdSet = new HashSet<>(examStatReportService.getExamStatCourseIds(examId, statId));
                courseId2TeachClassIds.forEach((courseId, courseClassIds) -> {
                    if (examStatCourseIdSet.contains(courseId)) {
                        Bson classCourseQuery = and(baseQuery, eq("courseId", courseId));
                        if (!showAllClass) {
                            classCourseQuery = and(classCourseQuery, in("classId", courseClassIds));
                        }
                        course.addAll(classCourseStatCol.find(classCourseQuery)
                          .projection(fields(include(fields), excludeId()))
                          .sort(ascending("classSort", "classId"))
                          .into(new ArrayList<>()));
                    }
                });
            }
            e.put("total", total);
            e.put("course", course);
        }
        return examList;
    }

}
