package com.dongni.exam.plan.service;

import com.alibaba.excel.EasyExcel;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.manager.base.ExamClassStatManager;
import com.dongni.analysis.manager.base.ExamStudentStatManager;
import com.dongni.analysis.manager.common.ExamConfigManager;
import com.dongni.analysis.view.monitor.service.ExamStatService;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.clazz.bean.ClassInfoDTO;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.student.bean.StudentUpdateInfoDTO;
import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.basedata.export.student.service.ExportExamStudentService;
import com.dongni.basedata.export.tag.service.CommonTagService;
import com.dongni.basedata.school.course.bean.CourseSelectionGroupNameDTO;
import com.dongni.basedata.school.course.service.CourseSelectionGroupService;
import com.dongni.basedata.school.escalation.bean.common.Course;
import com.dongni.basedata.school.escalation.bean.common.Excel;
import com.dongni.basedata.school.escalation.service.impl.IBaseDataManageService;
import com.dongni.basedata.school.export.SimpleDoubleHeaderExcelReport;
import com.dongni.basedata.system.account.service.impl.UserMembershipService;
import com.dongni.common.report.excel.ExcelReport;
import com.dongni.common.report.excel.ExcelVersionEnum;
import com.dongni.common.report.excel.ExportExcelTemplate;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.common.report.excel.simple.SimpleExcelReport;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.PageUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.common.utils.Pinyin4jUtil;
import com.dongni.common.utils.basedata.UserInfoUtil;
import com.dongni.common.utils.spring.SpringProfilesActiveUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.exception.DongniException;
import com.dongni.commons.exception.ErrorCode;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.constant.CommonConstant;
import com.dongni.exam.common.mark.constant.RequestUtil;
import com.dongni.exam.common.mark.serivice.basedata.IStudentService;
import com.dongni.exam.common.mark.serivice.exam.IExamPaperClientService;
import com.dongni.exam.common.mark.serivice.exam.IExamService;
import com.dongni.exam.common.mark.serivice.exam.IStudyGuideHWService;
import com.dongni.exam.common.mark.serivice.item.IExamItemService;
import com.dongni.exam.common.mark.serivice.mark.IPaperReadTaskService;
import com.dongni.exam.common.mark.vo.DelItemRangeVO;
import com.dongni.exam.common.mark.vo.ExamClassVO;
import com.dongni.exam.common.mark.vo.ExamPaperInfoVO;
import com.dongni.exam.common.mark.vo.ExamSchoolVO;
import com.dongni.exam.common.mark.vo.ExamVO;
import com.dongni.exam.common.mark.vo.InsertExamClsVO;
import com.dongni.exam.common.mark.vo.RequestVO;
import com.dongni.exam.dispiay.service.DisplayExamService;
import com.dongni.exam.enumeration.ExamTypeEnum;
import com.dongni.exam.health.check.ExamPaperStatus;
import com.dongni.exam.mark.manager.ICardReportItemManager;
import com.dongni.exam.mark.util.ExamCheckUtil;
import com.dongni.exam.newcard.parse.enumeration.ExamResultStatusEnum;
import com.dongni.exam.plan.bean.ExamStudentInfoDTO;
import com.dongni.exam.plan.bean.StudentExamNumUpdateDTO;
import com.dongni.exam.plan.bean.bo.ClassStudentBO;
import com.dongni.exam.plan.bean.bo.ExamResultBO;
import com.dongni.exam.plan.bean.bo.ExamStuAddBO;
import com.dongni.exam.plan.bean.bo.ExamStudentBO;
import com.dongni.exam.plan.bean.bo.ExamStudentTagBO;
import com.dongni.exam.plan.bean.bo.ExamTagBO;
import com.dongni.exam.plan.bean.bo.StudentTagBo;
import com.dongni.exam.plan.bean.dto.ExamPaperDTO;
import com.dongni.exam.plan.bean.dto.ExamStuPaperDTO;
import com.dongni.exam.plan.bean.dto.ExamStuPapersDTO;
import com.dongni.exam.plan.bean.dto.PaperIdStudentIdDTO;
import com.dongni.exam.plan.bean.dto.StuUpdateInfoDTO;
import com.dongni.exam.plan.bean.entity.ExamStudentExt;
import com.dongni.exam.plan.bean.excel.CheckExcelResponse;
import com.dongni.exam.plan.bean.excel.ErrCell;
import com.dongni.exam.plan.bean.excel.NotFondStudent;
import com.dongni.exam.plan.bean.excel.RepeatCell;
import com.dongni.exam.plan.bean.excel.SimilarStudent;
import com.dongni.exam.plan.bean.excel.listener.StudentAddListener;
import com.dongni.exam.plan.bean.excel.sheet.StudentImportSheet;
import com.dongni.exam.plan.bean.request.ExamStuDeleteReq;
import com.dongni.exam.plan.bean.request.ExamStuImportReq;
import com.dongni.exam.plan.bean.request.ExamStuInfoQueryReq;
import com.dongni.exam.plan.bean.request.ExamStuUpdateReq;
import com.dongni.exam.plan.bean.request.StuExamNumUpdateExcelReq;
import com.dongni.exam.plan.bean.response.StuUpdateInfoPreviewRes;
import com.dongni.exam.plan.enums.ExamStuUpdateFieldsEnum;
import com.dongni.exam.plan.manager.IExamStudentExtManager;
import com.dongni.exam.plan.manager.INewPlanExamManager;
import com.dongni.exam.selection.bean.bo.StudentRemoveResult;
import com.dongni.exam.selection.bean.requestParam.RemoveStudentParam;
import com.dongni.exam.selection.service.SelectionCommonService;
import com.dongni.exam.wrong.serevice.WrongExamService;
import com.dongni.newmark.bean.entity.Exam;
import com.dongni.newmark.bean.entity.ExamClass;
import com.dongni.newmark.bean.entity.ExamClassPaper;
import com.dongni.newmark.bean.entity.ExamResult;
import com.dongni.newmark.bean.entity.ExamSchool;
import com.dongni.newmark.bean.entity.ExamSchoolPaper;
import com.dongni.newmark.bean.entity.ExamStudent;
import com.dongni.newmark.bean.entity.ExamStudentTag;
import com.dongni.newmark.manager.IExamClassManager;
import com.dongni.newmark.manager.IExamClassPaperManager;
import com.dongni.newmark.manager.IExamManager;
import com.dongni.newmark.manager.IExamResultManager;
import com.dongni.newmark.manager.IExamSchoolManager;
import com.dongni.newmark.manager.IExamSchoolPaperManager;
import com.dongni.newmark.manager.IExamStudentManager;
import com.dongni.newmark.manager.IExamStudentTagManager;
import com.dongni.newmark.manager.IExamTagManager;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.wrong.book.bean.dto.DelWrongBookStudentDTO;
import com.dongni.tiku.wrong.book.service.WrongBookStudentService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.or;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

/**
 * Created by scott
 * time: 10:32 2018/11/14
 * description:考试学生
 */
@Service
public class ExamStudentService {

    private final static Logger log = LoggerFactory.getLogger(ExamStudentService.class);

    @Autowired
    ExamRepository commonRepository;
    @Autowired
    private CommonStudentService commonStudentService;
    @Autowired
    private CommonTagService commonTagService;
    @Autowired
    private ExportExamStudentService exportExamStudentService;
    @Autowired
    private WrongExamService wrongExamService;
    @Autowired
    private ExamStudentTagService examStudentTagService;
    @Autowired
    private ExamCourseService examCourseService;
    @Autowired
    private ExamTraceService examTraceService;

    @Autowired
    private ICardReportItemManager cardReportItemManager;

    @Autowired
    private IExamItemService examItemService;
    @Autowired
    private ExamService examService;
    @Autowired
    private ExamUnionNewService examUnionNewService;
    @Autowired
    private ExamConfigService examConfigService;
    @Autowired
    private SelectionCommonService selectionCommonService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private IPaperReadTaskService paperReadTaskService;
    @Autowired
    private WrongBookStudentService wrongBookStudentService;
    @Autowired
    private IExamStudentExtManager examStudentExtManager;
    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;
    @Autowired
    private IStudyGuideHWService studyGuideHWService;
    @Autowired
    private IExamManager examManager;
    @Autowired
    private ExamSchoolPaperService examSchoolPaperService;
    @Autowired
    private ExamSchoolService examSchoolService;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private UserMembershipService userMembershipService;
    @Autowired
    private ExamStatService examStatViewService;
    @Autowired
    private ExamStudentStatManager examStudentStatManager;
    @Autowired
    private ExamConfigManager examConfigManager;
    @Autowired
    private DisplayExamService displayExamService;
    @Autowired
    private ExamClassStatManager examClassStatManager;
    @Autowired
    private IExamService newExamService;
    @Autowired
    private CourseSelectionGroupService courseSelectionGroupService;
    @Autowired
    private IBaseDataManageService baseDataManageService;
    @Autowired
    private IExamPaperClientService examPaperClientService;
    @Autowired
    private IExamClassPaperManager examClassPaperManager;
    @Autowired
    private IExamSchoolPaperManager examSchoolPaperManager;
    @Autowired
    private INewPlanExamManager newPlanExamManager;
    @Autowired
    private IExamStudentManager examStudentManager;
    @Autowired
    private IExamResultManager examResultManager;
    @Autowired
    private IExamClassManager examClassManager;
    @Autowired
    private IExamStudentTagManager examStudentTagManager;
    @Autowired
    private ExamStudentService $this;
    @Autowired
    protected IStudentService studentService;
    @Autowired
    private IExamSchoolManager examSchoolManager;
    @Autowired
    private BaseDataRepository baseDataRepository;
    @Autowired
    private IExamTagManager examTagManager;



    //考生管理-更新学生考号excel的表头：学校、学生姓名、考号、班级、学号
    //表头各个列的下标
    private static final int SCH_NAME_IDX = 0;
    private static final int STU_NAME_IDX = 1;
    private static final int STU_EXAM_NUM_IDX = 2;
    private static final int CLASS_NAME_IDX = 3;
    private static final int STU_NUM_IDX = 4;
    private static final String STU_NOT_EXIST_MSG = "考生在诊断中不存在，可在考生管理中将该考生手动添加到诊断中或在更新考号的表格中去除该考生";
    private static final String STU_SIMILAR_NONE_BY_STU_NUM = "填写的班级或姓名信息与根据学校和学号在考生管理中匹配到的考生的信息不一致，请确认考生信息";
    private static final String STU_SIMILAR_MULTI_BY_STU_NUM = "根据学校和学号匹配到了多个考生，请确认考生信息";
    private static final String STU_SIMILAR_NONE_BY_CLASS_NAME = "填写的班级或姓名信息与在考生管理中匹配到的考生的信息不一致，请确认考生信息";
    private static final String STU_SIMILAR_MULTI_BY_CLASS_NAME ="根据学校、班级和姓名匹配到了多个考生，请确认考生信息";
    private static final String STU_SIMILAR_MULTI_BY_STU_NAME ="根据学校和姓名匹配到了多个考生，请确认考生信息";
    /**
     * 批量操作的批次默认大小
     */
    private static final int RESULT_DEFAULT_BATCH_SIZE = 10000;

    /**
     * 获取考试学生
     *
     * @param params userId examId
     */
    public Map<String, Object> getExamStudent(Map<String, Object> params) {
        Verify.of(params).isValidId("classId")
                .verify();
        Map<String, Object> rs = new HashMap<>();
        //通过classIds或examId确定所有学生A,通过classId找到所有学生，取交集
        List<Map<String, Object>> student1;
        if (ObjectUtil.isValidId(params.get("examId"))) {
            student1 = commonRepository.selectList("ExamStudentMapper.getExamStudent", params);
        } else {
            List<Map<String, Object>> students = commonStudentService.getClassStudent(params);
            if (CollectionUtils.isEmpty(students)) {
                rs.put("totalCount", 0);
                rs.put("student", new ArrayList<>());
                return rs;
            }
            student1 = students;
        }

        if (CollectionUtils.isEmpty(student1)) {
            rs.put("totalCount", 0);
            rs.put("student", new ArrayList<>());
            return rs;
        }

        params.put("classIds", params.get("classId"));
        List<Map<String, Object>> student2 = commonStudentService.getClassStudent(params);
        Map<Long, Map<String, Object>> stuMap = student2.stream().collect(toMap(s -> Long.valueOf(s.get("studentId").toString()), s -> s));

        List<Map<String, Object>> list = new ArrayList<>();
        for (Map<String, Object> map : student1) {
            if (stuMap.keySet().contains(Long.valueOf(map.get("studentId").toString()))) {
                list.add(map);
            }
        }

        Integer count = list.size();
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            Integer pageSize = Integer.valueOf(params.get("pageSize").toString());
            Integer fromIndex = Integer.valueOf(params.get("currentIndex").toString());
            int toIndex = Math.min(count, fromIndex + pageSize);
            list = list.subList(fromIndex, toIndex);
            //考试学生还要加标签
            if (ObjectUtil.isValidId(params.get("examId")) && CollectionUtils.isNotEmpty(list)) {
                StringBuilder sb = new StringBuilder();
                list.forEach(l -> sb.append(l.get("studentId")).append(","));
//                Response rp = commonStudentService.getStudentTag(sb.toString());
//                List<Map<String, Object>> stuTag = (List) rp.getData();
                params.put("studentIds", sb.toString());
                List<Map<String, Object>> stuTag = commonTagService.getStudentTag(params);
                Map<Long, Map<String, Object>> stu = stuTag.stream().collect(toMap(s -> Long.valueOf(s.get("studentId").toString()), s -> s));
                list.forEach(l -> {
                    Map<String, Object> map = stu.get(Long.valueOf(l.get("studentId").toString()));
                    if (map != null) {
                        l.putAll(map);
                    }
                });
            }
        }
        //分页
        rs.put("totalCount", count);
        rs.put("student", list);
        return rs;
    }

    /**
     * 获取不在此考试中的学生
     *
     * @param params examId schoolIds
     * @return 学生
     */
    public Map<String, Object> getAvailableSchoolStudent(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").verify();
        if (ObjectUtil.isBlank(params.get("paperIds"))) {
            //没有选择试卷时，就相当于选择了所有的试卷
            //联考只能操作已经走完了发布流程的试卷
            List<Map<String, Object>> examPapers = commonRepository.selectList("ExamUnionNewMapper.getStatPaper",
              MapUtils.getLong(params, "examId"));
            params.put("paperIds", examPapers.stream().map(x -> x.get("paperId").toString()).collect(Collectors.joining(",")));
        }
        params.put("fromTool", true);
        return getAvailableStudent(params);
    }

    public Map<String, Object> getSingleAvailableSchoolStudent(Map<String, Object> params) {
        Map<String, Object> availableSchoolStudent = getAvailableSchoolStudent(params);
        // 已存在考生
        Set<Long> existStudentIds = new HashSet<>(examStudentManager.findStuIdsByExamId(MapUtil.getLong(params, "examId")));
        List<Map<String, Object>> students = (List<Map<String, Object>>) availableSchoolStudent.get("student");
        if (CollectionUtils.isEmpty(students)) {
            return availableSchoolStudent;
        }
        List<Map<String, Object>> studentList = students.stream()
                .filter(s -> !existStudentIds.contains(MapUtil.getLong(s, "studentId")))
                .collect(toList());
        Map<String, Object> rs = new HashMap<>();
        rs.put("totalCount", studentList.size());
        rs.put("student", studentList);
        return rs;
    }

    /**
     * 获取考试中的学生是否已产生明细数据
     *
     * @param params examId studentId [paperId]
     * @return 学生
     */
    public Long getExamItemCount(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").isValidId("studentId").verify();
        return commonRepository.selectOne("ExamStudentMapper.getExamItemCount", params);
    }

    /**
     * 删除考试中的学生
     *
     * @param params examId studentId [paperId]
     * 传了paperId表示在单个试卷下删除学生，否则表示直接在考试中删除这个学生
     *
     * -不能删的条件
     * 1.按试题 有paperId就只看paperId对应的试卷，否则看所有的试卷
     * -试卷有正在进行中的上传任务(t_exam_uploader有type为1但状态不是7的数据 + type为11但状态不是2或7的数据），还没有处理完成
     * -非缺考且试卷正在阅卷中(t_exam_paper的状态是13、14、15)
     * -学生正在做重阅（t_exam_item_reread中状态为未阅），不允许删除
     * -学生正在进行异常卷处理（t_recognition_student对应的t_recognition的状态为进行中），不允许删除
     * -异常卷申报中有这个学生的数据（t_card_report_item中有数据），不允许删除
     * 2.按班级
     * -班级有正在进行中的上传任务(t_exam_uploader有type为2但状态不是7的数据 + type为12但状态不是2或7的数据），还没有处理完成
     * -非缺考且班级正在阅卷中(t_class_paper的状态是15)
     * -学生正在做重阅（t_exam_item_reread中状态为未阅），不允许删除
     * -学生正在进行异常卷处理（t_recognition_student对应的t_recognition的状态为进行中），不允许删除
     * -异常卷申报中有这个学生的数据（t_card_report_item中有数据），不允许删除
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void deleteExamStudent(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").isNotBlank("studentId").verify();

        String studentId = MapUtil.getString(params, "studentId");

        List<Long> studentIds = Arrays.stream(studentId.split(",")).map(Long::valueOf).collect(toList());

        if (CollectionUtils.isEmpty(studentIds)) {
            return;
        }
        params.remove("studentId");
        params.put("studentIds", studentIds);


        Long paperId;
        if (!ObjectUtil.isValidId(params.get("paperId"))) {
            paperId = null;
            params.remove("paperId");
        } else {
            paperId = MapUtil.getLong(params, "paperId");
        }

        for (Long stuId : studentIds) {
            Set<Long> paperIds = newPlanExamManager.getStuRelatedPaperIds(MapUtil.getLong(params, "examId"), stuId);
            if (!paperIds.isEmpty() && (paperId==null|| paperIds.contains(paperId))){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "无法删除已关联答卷的考生，请先删除考生答卷");
            }
        }
        long examId = MapUtil.getLong(params, "examId");
        boolean deleteByImport = MapUtil.getBoolean(params, "deleteByImport");

        // 非导入时调用，限制不能全部删除
        if (!deleteByImport){
            boolean allDeleted = examPaperClientService.listPapers(examId)
                    .stream()
                    .filter(x -> paperId == null || x.getPaperId() == paperId)
                    .anyMatch(x -> examResultManager.countNotInStu(examId, x.getPaperId(), studentIds) == 0);
            if (allDeleted) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "至少需保留一位考生，无法全部删除");
            }
        }

        List<Map<String, Object>> studentResult = commonRepository.selectList("ExamResultMapper.getExamStudentResultById", params);
        if (CollectionUtils.isEmpty(studentResult)) {
            if (!ObjectUtil.isValidId(params.get("paperId"))) {
                //学生的所有课程都删完了，那把学生也删了
                commonRepository.delete("ExamStudentMapper.deleteExamStu", params);
                // 删除学生的考试标签
                examStudentTagService.deleteStudentExamTagByStudentIds(params);
            }
            return;
        }

        Map<String, Object> examDetail = examService.getExamDetail(params);
        if (DictUtil.isEquals(MapUtils.getInteger(examDetail, "correctMode"), "correctMode", "readByQuestion")) {
            //按试题
            List<Map<String, Object>> uploadingPaper = commonRepository.selectList("ExamStudentMapper.getUploadingPaperByStudent", params);
            if(CollectionUtils.isNotEmpty(uploadingPaper)){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "删除失败，" + uploadingPaper.stream().map(x ->
                  MapUtils.getString(x, "courseName")).collect(Collectors.joining("、")) + "存在进行中的上传任务");
            }

            //非缺考的试卷
            Set<Long> attendPaperIds = studentResult.stream()
              .filter(x -> DictUtil.isEquals(MapUtils.getInteger(x, "resultStatus"), "resultStatus", "attend"))
                    .map(x -> MapUtils.getLong(x, "paperId")).collect(toSet());
            if(CollectionUtils.isNotEmpty(attendPaperIds)){
                params.put("paperIds", attendPaperIds);
                List<Map<String, Object>> attendPapers = commonRepository.selectList("ExamUnionNewMapper.getExamPaper", params);
                List<Map<String, Object>> readingPapers = attendPapers.stream().filter(
                  x -> DictUtil.isEquals(MapUtils.getInteger(x, "examPaperStatus"), "examPaperStatus", "trialReadPaper",
                    "trialReadComplete", "readPaper")).collect(toList());
                //按试题的考试，试评阅卷中、试评完成、正评阅卷中都认为是在阅卷中
                if (CollectionUtils.isNotEmpty(readingPapers)){
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "删除失败，" + readingPapers.stream().map(x ->
                      MapUtils.getString(x, "courseName")).collect(Collectors.joining("、")) + "还未完成阅卷");
                }
            }
        } else {
            //按班级
            List<Long> examUploaderIds = commonRepository.selectList("ExamStudentMapper.getClassUploadingTaskByStudent", params);
            if(CollectionUtils.isNotEmpty(examUploaderIds)){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "删除失败，学生所在班级存在进行中的上传任务");
            }

            if (DictUtil.isEquals(MapUtils.getInteger(studentResult.get(0), "resultStatus"), "resultStatus", "attend")) {
                List<Map<String, Object>> examClasses = commonRepository.selectList("ExamStudentMapper.getExamClassByExamIdAndStudentId", params);
                if (examClasses.stream().anyMatch(examClass ->
                        DictUtil.isEquals(
                                MapUtils.getInteger(examClass, "examClassPaperStatus"),
                                "examPaperStatus",
                                "readPaper"
                        ))) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "删除失败，学生所在班级还未完成阅卷");
                }
            }
        }

        //正在做重新批阅不能删除 t_exam_item_reread
        Map<String, Object> unfinishedItem = commonRepository.selectOne("ExamMarkReadRepeatMapper.getUnfinishedRereadItemByStudent", params);
        if (MapUtils.isNotEmpty(unfinishedItem)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "删除失败，学生正在重新批阅中");
        }

        //正在异常卷处理中不能删除 t_recognition_student
        Map<String, Object> recognition = commonRepository.selectOne("RecognitionCardMapper.getUnfinishedRecognitionByStudent", params);
        if (MapUtils.isNotEmpty(recognition)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "删除失败，学生正在进行异常卷处理");
        }

//        //在异常卷申报中不能删除 t_card_report_item
//        long examId = MapUtil.getLong(params, "examId");
//        long studentId = MapUtil.getLong(params, "studentId");
//        ListItemVO listItemVO = new ListItemVO();
//        listItemVO.setExamId(examId);
//        if (paperId != null) {
//            listItemVO.setPaperId(paperId);
//        }
//        listItemVO.setStudentIds(Collections.singletonList(studentId));
//        listItemVO.setGetAllStatusItem();
//        List<ItemDetailVO> items = examItemService.getItems(listItemVO);
//        List<CardReportItem> cardReportItems = null;
//        if (!items.isEmpty()) {
//            List<Long> itemIds = items.stream().map(ItemDetailVO::getExamItemId).collect(toList());
//            cardReportItems = cardReportItemManager.findByItemIds(itemIds);
//        }
//        if (CollectionUtils.isNotEmpty(cardReportItems)) {
//            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "删除失败，学生的异常卷申报还未处理");
//        }

        //删除阅卷Task和Record数据
        DelItemRangeVO delItemRangeVO = new DelItemRangeVO();
        delItemRangeVO.setExamId(examId);
        delItemRangeVO.setStudentIds(studentIds);
        delItemRangeVO.setPaperIds(paperId != null ? Collections.singletonList(paperId) :
          examPaperService.getExamPaper(examDetail).stream().map(x -> MapUtils.getLong(x, "paperId")).collect(toList()));
        paperReadTaskService.delTaskAndRecordByRange(delItemRangeVO);

        commonRepository.delete("ExamStudentMapper.deleteExamResult",params);
        commonRepository.delete("ExamStudentMapper.deleteExamItem",params);

        // 如果该学生全部科目均被删除，则从考试中删除学生
        List<Map<String, Object>> studentCount = commonRepository.selectList("ExamStudentMapper.getStudentResult", params);
        Map<Long, Integer> stu2Count = CollectionUtils.isEmpty(studentCount) ? Collections.emptyMap() : studentCount.stream()
                .collect(toMap(x -> MapUtils.getLong(x, "studentId"), x -> MapUtils.getInteger(x, "count")));
        Set<Long> delStudentIds = studentIds.stream().filter(x -> stu2Count.getOrDefault(x, 0) == 0).collect(toSet());
        if (CollectionUtils.isNotEmpty(delStudentIds)) {
            Map<String, Object> delParams = MapUtil.copy(params, "examId", "paperId");
            delParams.put("studentIds", delStudentIds);
            commonRepository.delete("ExamStudentMapper.deleteExamStu", delParams);
            // 删除学生的考试标签
            examStudentTagService.deleteStudentExamTagByStudentIds(delParams);
        }
        for (Long id : studentIds) {
            // 刷新选科组合异常处理的缓存
            Map<String, Object> flushParams = MapUtil.copy(params, "examId");
            flushParams.put("studentId", id);
            selectionCommonService.rebuildCacheByStudentId(flushParams);

            // 删除个册对应的学生, 捕获异常，不影响主流程。个册未删除成功对之后个册生成无影响，生成会再次校验，这里调用只是为了前端显示对齐
            try {
                List<Long> paperIdList = studentResult.stream()
                        .map(x -> MapUtils.getLong(x, "paperId")).collect(toList());
                DelWrongBookStudentDTO delWrongBookStudentDTO = new DelWrongBookStudentDTO();
                delWrongBookStudentDTO.setExamId(examId);
                delWrongBookStudentDTO.setStudentId(id);
                delWrongBookStudentDTO.setPaperIdList(paperId != null? Lists.newArrayList(paperId): paperIdList);
                wrongBookStudentService.syncExamDeleteStudentForWrongBookStudent(delWrongBookStudentDTO);
            } catch (Exception e) {
                log.info("个册删除学生失败：考试:{},学生：{}", examId, studentId);
            }
        }
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void deleteBatchExamStudent(ExamStuDeleteReq request) {

        String studentId = request.getStudentId();
        if (studentId != null) {
            Map<String, Object> params = MapUtil.of(
                    "examId", request.getExamId(),
                    "paperId", request.getPaperId(),
                    "studentId", studentId
            );
            deleteExamStudent(params);
            return;
        }

        ExamStuInfoQueryReq queryReq = new ExamStuInfoQueryReq();
        queryReq.setExamId(request.getExamId());
        queryReq.setSchoolId(request.getSchoolId());
        queryReq.setExamTagId(request.getExamTagId());
        List<ExamStudentBO> examStudents = getExamStudentByExam(queryReq);
        if (CollectionUtils.isEmpty(examStudents)) {
            return;
        }
        Set<Long> studentIds = examStudents.stream().map(ExamStudentBO::getStudentId).collect(toSet());
        Map<String, Object> params = MapUtil.of(
                "examId", request.getExamId(),
                "paperId", request.getPaperId(),
                "studentId", StringUtils.join(studentIds, CommonConstant.COMMA)
        );
        deleteExamStudent(params);
    }

    /**
     * 新增学生成绩
     *
     * @param params examId examStudent examCourse
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void addExamResult(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").isNotEmptyCollections("examStudent").verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        Long examId = MapUtils.getLong(params, "examId");
        Exam exam = examManager.findById(examId);
        List<Map<String, Object>> examStudent = MapUtil.getListMap(params, "examStudent");

        if (exam.getExamType() == ExamTypeEnum.STUDY_GUIDE_HOMEWORK.getCode()) {
            InsertExamClsVO insertExamClsVO = new InsertExamClsVO();
            insertExamClsVO.setExamId(examId);
            insertExamClsVO.setUserId(MapUtils.getLong(params, "userId"));
            insertExamClsVO.setUserName(MapUtils.getString(params, "userName"));
            List<Long> studentIds = examStudent.stream().map(x -> MapUtils.getLong(x, "studentId")).collect(toList());
            insertExamClsVO.setStudentIds(studentIds);
            insertExamClsVO.setWay(1);
            studyGuideHWService.insertClsAndExecuteAfterUpload(insertExamClsVO);
            return;
        }

        List<Map<String, Object>> examPaper = MapUtil.getListMap(params, "examPaper");
        List<Map<String,Object>> stuClass = commonStudentService.getStudentClassInfo(params);
        Map<Long, List<Map<String, Object>>> studentIdMap = stuClass.stream().collect(groupingBy(s -> MapUtils.getLong(s, "studentId")));
        List<Map<String,Object>> result = new ArrayList<>();
        //ter中的resultStatus默认设置成0
        Integer resultStatus = DictUtil.getDictValue("resultStatus", "attend");
        //t_exam_class中所有的班级
        Set<Long> examClassIds = new HashSet<>(commonRepository.selectList("ExamStudentMapper.getExamClassId", params));
        //需要新增的t_exam_class_paper
        Set<Map<String, Object>> examClassPaper = new HashSet<>();
        Set<Map<String, Object>> examSchoolPaper = new HashSet<>();
        int readComplete = DictUtil.getDictValue("examPaperStatus", "readComplete");
        int answerCardUpload = DictUtil.getDictValue("examPaperStatus", "answerCardUpload");
        for(Map<String,Object> p : examPaper){
            Long paperId = MapUtils.getLong(p, "paperId");
            params.put("paperId", paperId);
            //t_exam_class_paper中教学班的数量
            int count = commonRepository.selectOne("ExamClassPaperMapper.getExamPaperTeachClassCountByPaperId", params);
            int examPaperStatus = MapUtils.getInteger(Optional.ofNullable(examPaperService.getExamPaperByExamIdAndPaperId(params))
                .orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "试卷在诊断中不存在！paperId:" + paperId)),
              "examPaperStatus");
            Map<Long, Integer> schoolId2PaperStatus = examSchoolPaperService.getByExamIdAndPaperIdAndSchoolIds(examId, paperId,
                examStudent.stream().map(x -> MapUtils.getLong(x, "schoolId")).distinct().collect(Collectors.toList())).stream()
              .collect(Collectors.toMap(x -> MapUtils.getLong(x, "schoolId"), x -> MapUtils.getInteger(x, "examSchoolPaperStatus")));
            for(Map<String,Object> s : examStudent){
                //查找学生所在所有班级，如学生A在教学A，教学B，行政A班，
                // 判断哪个班级已经存在examClassPaper中的(一定只有一个班级，一个学生不能同时在多个班考同一课程)，则用此班级id保存
                List<Map<String, Object>> list = Optional.ofNullable(studentIdMap.get(MapUtils.getLong(s, "studentId")))
                  .orElseThrow(() -> new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学生" + s.get("studentName") + "在基础数据中未找到所属班级"));
                p.put("studentClass",list);
                p.put("examId", examId);
                //这里只会查出一个班级，否则是错误的
                List<Map<String,Object>> studentExamClassPaper = commonRepository.selectList("ExamStudentMapper.getOneExamClassPaper", p);
                boolean addClassPaper = false;
                if (CollectionUtils.isEmpty(studentExamClassPaper) && count == 0) {
                    // 可能会出现学生选科信息选错需要添加学生选科但班级又不参考这个课程的情况，此时如果试卷下所有的班级都是行政班的话，
                    // 那么支持直接根据t_exam_class来查询学生，不再根据t_exam_class_paper
                    studentExamClassPaper = list.stream().filter(x -> examClassIds.contains(MapUtils.getLong(x, "classId"))).collect(
                      Collectors.toList());
                    addClassPaper = true;
                }
                if(CollectionUtils.isEmpty(studentExamClassPaper)){
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                      "学生" + s.get("studentName") + "所在班级" + studentExamClassPaper.stream().map(x -> x.get("className").toString())
                        .collect(Collectors.joining("、")) + "没有参与考试的" + p.get("courseName") + "课程！");
                }
                if(studentExamClassPaper.size()>1){
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                      "学生" + s.get("studentName") + "所在班级" + studentExamClassPaper.stream().map(x -> x.get("className").toString())
                        .collect(Collectors.joining("、")) + "都参与了考试的" + p.get("courseName") + "课程，无法确定学生班级！");
                }
                Map<String,Object> rs = new HashMap<>(s);
                Map<String, Object> stuResult = studentExamClassPaper.get(0);
                MapUtil.copy(p, stuResult, "courseId", "courseName", "paperId");
                rs.putAll(stuResult);
                rs.put("resultStatus", resultStatus);
                result.add(rs);
                if(addClassPaper){
                    //需要添加的课程参考班级
                    Map<String,Object> classPaper = new HashMap<>(stuResult);
                    Long schoolId = MapUtils.getLong(classPaper, "schoolId");
                    if (schoolId2PaperStatus.containsKey(schoolId)) {
                        classPaper.put("examClassPaperStatus", schoolId2PaperStatus.get(schoolId));
                    } else {
                        int examSchoolPaperStatus = examPaperStatus == readComplete ? readComplete : answerCardUpload;
                        classPaper.put("examClassPaperStatus", examSchoolPaperStatus);
                        Map<String, Object> schoolPaper = MapUtil.copy(classPaper, "schoolId", "paperId");
                        schoolPaper.put("examSchoolPaperStatus", examSchoolPaperStatus);
                        examSchoolPaper.add(schoolPaper);
                        schoolId2PaperStatus.put(schoolId, examSchoolPaperStatus);
                    }
                    examClassPaper.add(classPaper);
                }
            }
            params.remove("paperId");
        }
        checkCourseDuplicateStuResult(examId, result);
        //填充学生在t_exam_student中的班级信息、学生的选科、考号信息
        Map<Long, Map<String, Object>> stuId2StuInfo = commonStudentService.getStuInfoByStuIds(
            examStudent.stream().map(x -> MapUtils.getLong(x, "studentId")).collect(toList())).stream()
          .collect(toMap(x -> MapUtils.getLong(x, "studentId"), x -> x));
        Map<Long, List<Map<String, Object>>> stuId2ResultList = result.stream().collect(groupingBy(x -> MapUtils.getLong(x, "studentId")));
        examStudent.forEach(student -> {
            Long studentId = MapUtils.getLong(student, "studentId");
            List<Map<String, Object>> studentClass = studentIdMap.get(studentId).stream()
              .filter(x -> examClassIds.contains(MapUtils.getLong(x, "classId"))).collect(toList());
            if (studentClass.size() != 1) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学生" + student.get("studentName") + "班级信息异常，请联系管理员处理！");
            }
            MapUtil.copy(studentClass.get(0), student, "classId", "className");
            Map<String, Object> stuInfo = Optional.ofNullable(stuId2StuInfo.get(studentId)).orElseThrow(
              () -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "学生在基础数据中不存在或已被删除，请联系管理员处理！studentId：" + studentId));
            student.putAll(stuInfo);
            String studentExamNum = MapUtils.getString(stuInfo, "studentExamNum");
            stuId2ResultList.getOrDefault(studentId, Collections.emptyList()).forEach(r -> r.put("studentExamNum", studentExamNum));
        });
        insertStudentInfoToExam(params, examId, examStudent, result, true);
        if (CollectionUtils.isNotEmpty(examClassPaper)) {
            params.put("examClassPaper", examClassPaper);
            commonRepository.insert("ExamClassPaperMapper.insertExamClassPaperForAddResult", params);
            if (CollectionUtils.isNotEmpty(examSchoolPaper)) {
                params.put("examSchoolPaper", examSchoolPaper);
                commonRepository.insert("ExamSchoolPaperMapper.insertExamSchoolPaperForAddResult", params);
            }
            //重置报告配置
            examConfigService.resetExamConfigInfo(params);
        }
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void addOne(ExamStuAddBO examStuAddBO) {
        List<ExamStuAddBO> examStuAddBOS = Collections.singletonList(examStuAddBO);
        addStudent(examStuAddBO.getExamId(), examStuAddBOS);
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void updateOne(ExamStuAddBO examStuAddBO) {
        long examId = examStuAddBO.getExamId();
        long studentId = examStuAddBO.getStudentId();
        List<ExamStudent> examStudents = examStudentManager.findByExamIdAndStus(examId, Collections.singletonList(studentId));
        if (examStudents.size() != 1) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未找到参考学生【" + examStuAddBO.getStudentName());
        }

        List<ExamClassVO> examClassVOS = newExamService.listExamClasses(examId);
        Map<Long, ExamClassVO> examClassIds = examClassVOS.stream().collect(toMap(ExamClassVO::getClassId, Function.identity()));
        long classId = examStuAddBO.getClassId();
        if (!examClassIds.containsKey(classId)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "班级【" + examStuAddBO.getClassName() + "】未参加考试");
        }

        List<ExamPaperInfoVO> examPaperInfoVOS = examPaperClientService.listPapers(examId);
        Map<Long, ExamPaperInfoVO> paperId2Info = examPaperInfoVOS.stream().collect(toMap(ExamPaperInfoVO::getPaperId, Function.identity()));

        // 参考学校
        List<ExamSchoolPaper> examSchoolPapers = examSchoolPaperManager.findByExam(examId);
        Map<Long, Map<Long,ExamSchoolPaper>> schId2Info = examSchoolPapers.stream()
                .collect(groupingBy(ExamSchoolPaper::getSchoolId, toMap(ExamSchoolPaper::getPaperId, Function.identity())));

        ExamStudent examStudent = examStudents.get(0);
        String className = examStuAddBO.getClassName();
        long schoolId = examStuAddBO.getSchoolId();

        //校验考号在t_exam_student中是否已经存在
        String studentExamNum = examStuAddBO.getStudentExamNum();
        if (StringUtils.isBlank(studentExamNum)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "学生【" + examStuAddBO.getStudentName() + "】的考号不能为空");
        }
        if (examStudentManager.countSameExamNumStudents(examId, schoolId, studentId, studentExamNum) != 0) {
            //考号在t_exam_student中已被其他学生使用
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "考号在诊断中已被使用，请先核查数据或联系管理员处理");
        } else if (examResultManager.countSameExamNumStudents(examId, schoolId, studentId, studentExamNum) != 0) {
            //校验考号在t_exam_student中是否已经存在
            //理论上学生在t_exam_student中的考号和在t_exam_result中的考号是一样的，但可能会有数据出问题的情况，导致两边不完全一致
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "考号在诊断的部分课程中已被使用，请先核查数据或联系管理员处理");
        }

        examStudent.setStudentExamNum(studentExamNum);
        examStudent.setCourseSelectionGroupId(examStuAddBO.getCourseSelectionGroupId());
        examStudent.setForeignCourseId(examStuAddBO.getForeignCourseId());
        // 当前学生在t_exam_student中的考号已经是正确的了，那就把t_exam_result和t_answer_card中不一致的数据改一下
        // 更新t_exam_result、t_answer_card中的学生考号
        StudentExamNumUpdateDTO studentExamNumUpdateDTO = new StudentExamNumUpdateDTO()
                .setExamId(examId)
                .setStudentId(studentId)
                .setStudentExamNum(studentExamNum);
        UserInfoUtil.copyModifyUserInfo(studentExamNumUpdateDTO, DongniUserInfoContext.get());
        commonRepository.update("ExamStudentMapper.updateResStuExamNumByExamIdAndStuId", studentExamNumUpdateDTO);
        commonRepository.update("NewAnswerCardMapper.updateAnswerCardStuExamNumByExamIdAndStuId", studentExamNumUpdateDTO);

        //更新学生标签
        Map<Long, String> examTagId2TagName = Optional.ofNullable(examStuAddBO.getStudentTags())
                .orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(StudentTagBo::getExamTagId, StudentTagBo::getTagName, (k1, k2) -> k1));
        if (!examTagId2TagName.isEmpty()) {

            // 考试标签
            List<ExamTagBO> examTagBOS = examTagManager.findByExamAndSchool(examId, schoolId);
            Map<Long, String> schoolTag2Name = examTagBOS.stream().collect(
                    Collectors.toMap(ExamTagBO::getExamTagId, ExamTagBO::getTagName)
            );
            boolean tagErr = examTagId2TagName.entrySet().stream().anyMatch(entry ->
                    !schoolTag2Name.containsKey(entry.getKey()) ||
                    !StringUtils.equals(entry.getValue(), schoolTag2Name.get(entry.getKey()))
            );
            if (tagErr) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "标签数据有误");
            }

            Date now = new Date();
            RequestVO user = RequestUtil.getUser();
            List<ExamStudentTag> examStudentTags = examTagId2TagName.keySet().stream().map(examTagId -> new ExamStudentTag()
                    .setExamId(examId)
                    .setExamTagId(examTagId)
                    .setStudentId(studentId)
                    .setCreatorId(user.getUserId())
                    .setCreatorName(user.getUserName())
                    .setCreateDateTime(now)
                    .setModifierId(user.getUserId())
                    .setModifierName(user.getUserName())
                    .setModifyDateTime(now)).collect(toList());
            examStudentTagManager.batchInsert(examStudentTags);
        }
        examStudentTagManager.deleteTagNotInTagIds(examId, studentId, examTagId2TagName.keySet());

        // 更新班级
        Long oldClassId = examStudent.getClassId();
        if (oldClassId != classId) {
            examStudent.setClassId(classId);
            examStudent.setClassName(className);
            updateStuClass(examStuAddBO, oldClassId);
        }

        examStudentManager.batchInsertOrUpdate(Collections.singletonList(examStudent));

        // 参考科目
        List<ExamPaperDTO> examPaper = examStuAddBO.getExamPaper();
        Map<Long, List<ExamPaperDTO>> courseId2Papers = examPaper.stream().collect(groupingBy(ExamPaperDTO::getCourseId));

        courseId2Papers.forEach((courseId, papers) -> {
            if (CollectionUtils.isNotEmpty(papers) &&  papers.size() > 1) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "相同科目，无法参考多个答题卡");
            }
            Long paperId = papers.get(0).getPaperId();
            if (!paperId2Info.containsKey(paperId)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未找到参考试卷【" + paperId + "】");
            }
        });

        List<ExamStuPapersDTO> allCoursePapers = getStudentRelatedAnswerCard(examId, studentId);

        Set<Long> delResultPapers = new HashSet<>();
        Set<Long> delClassPapers = new HashSet<>();
        List<ExamResult> examResultList = new ArrayList<>();
        List<ExamClassPaper> newClassPapers = new ArrayList<>();
        List<ExamSchoolPaper> newSchoolPapers = new ArrayList<>();
        for (ExamStuPapersDTO examStuPapersDTO : allCoursePapers) {
            long courseId = examStuPapersDTO.getCourseId();
            ExamPaperDTO newPaper =  courseId2Papers.getOrDefault(courseId,Collections.emptyList())
                    .stream().findFirst().orElse(null);
            ExamStuPaperDTO oldPaper = examStuPapersDTO.getPapers()
                    .stream().filter(ExamStuPaperDTO::isSelected).findFirst().orElse(null);
            // 当前科目没有参考
            if (newPaper == null) {
                // 当前科目之前参考
                if (oldPaper != null) {
                    if (oldPaper.isAssociated()) {
                        throw new CommonException(ResponseStatusEnum.DATA_ERROR, "答卷[" + oldPaper.getPaperName() + "]已关联，无法取消参考");
                    }
                    delResultPapers.add(oldPaper.getPaperId());
                }
            }
            // 当前科目之前未参考
            else if (oldPaper == null) {
                Set<Long> oldClassPaperIds = examClassPaperManager.findByExamAndCourseAndClsId(examId, courseId, classId)
                        .stream().map(ExamClassPaper::getPaperId).collect(toSet());
                // 相同班级新增的试卷与旧的试卷不一致
                long paperId = newPaper.getPaperId();
                if (CollectionUtils.isNotEmpty(oldClassPaperIds) && !oldClassPaperIds.contains(paperId)) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "相同科目，无法参考多个答题卡");
                }
                if (CollectionUtils.isEmpty(oldClassPaperIds)){
                    ExamClassVO examClassVO = examClassIds.get(classId);
                    Map<Long, ExamSchoolPaper> paperId2SchoolPaper = schId2Info.getOrDefault(examClassVO.getSchoolId(), Collections.emptyMap());
                    initClassPaperAndSchoolPaper(examId, paperId, examClassVO,
                            paperId2SchoolPaper.get(paperId), paperId2Info.get(paperId), newSchoolPapers, newClassPapers);
                }
                ExamResult examResult = createExamResult(examStuAddBO, courseId, paperId);
                examResultList.add(examResult);
            }
            // 更换了试卷
            else if (oldPaper.getPaperId() != newPaper.getPaperId()){
                List<ExamResult> examResults = examResultManager.findByExamAndPaperAndClass(examId, oldPaper.getPaperId(), classId)
                        .stream().filter(examResult -> studentId != examResult.getStudentId()).collect(toList());
                // 旧的试卷仍在被其他学生使用
                if (CollectionUtils.isNotEmpty(examResults)) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "相同科目下一个班级只能参考一个答题卡");
                }
                // 被更换的试卷已关联
                if (oldPaper.isAssociated()) {
                    throw new CommonException(ResponseStatusEnum.DATA_ERROR, "答卷[" + oldPaper.getPaperName() + "]已关联，无法更换参考");
                }
                long paperId = newPaper.getPaperId();
                ExamResult examResult = createExamResult(examStuAddBO, courseId, paperId);
                examResultList.add(examResult);

                ExamClassVO examClassVO = examClassIds.get(classId);
                Map<Long, ExamSchoolPaper> paperId2SchoolPaper = schId2Info.getOrDefault(examClassVO.getSchoolId(), Collections.emptyMap());
                initClassPaperAndSchoolPaper(examId, paperId, examClassVO,
                        paperId2SchoolPaper.get(paperId), paperId2Info.get(paperId), newSchoolPapers, newClassPapers);

                delResultPapers.add(oldPaper.getPaperId());
                delClassPapers.add(oldPaper.getPaperId());
            }
        }
        examResultManager.deleteByStuAndPapers(examId, studentId, delResultPapers);
        examResultManager.batchInsert(examResultList);
        examClassPaperManager.deleteByExamAndClassAndPapers(examId, classId, delClassPapers);
        examClassPaperManager.batchInsert(newClassPapers);
        // 学生全部科目删除，则删除学生和标签
        List<ExamResult> examResults = examResultManager.findByExamAndStudent(examId, studentId);
        if (CollectionUtils.isEmpty(examResults)){
            examStudentManager.deleteByExamAndStudents(examId, Collections.singletonList(studentId));
            examStudentTagManager.deleteByExamAndStudents(examId, Collections.singletonList(studentId));
        }

        if (CollectionUtils.isNotEmpty(newClassPapers)) {
            //重置报告配置
            RequestVO user = RequestUtil.getUser();
            Map<String, Object> params = MapUtil.of(
                    "examId", examId,
                    "userId", user.getUserId(),
                    "userName", user.getUserName()
            );
            examConfigService.resetExamConfigInfo(params);
        }
    }

    private static ExamResult createExamResult(ExamStuAddBO examStuAddBO, long courseId, long paperId) {
        RequestVO user = RequestUtil.getUser();
        Date now = new Date();
        ExamResult examResult = new ExamResult();
        examResult.setExamId(examStuAddBO.getExamId());
        examResult.setCourseId(courseId);
        examResult.setPaperId(paperId);
        examResult.setSchoolId(examStuAddBO.getSchoolId());
        examResult.setClassId(examStuAddBO.getClassId());
        examResult.setClassName(examStuAddBO.getClassName());
        examResult.setStudentId(examStuAddBO.getStudentId());
        examResult.setStudentNum(examStuAddBO.getStudentNum());
        examResult.setStudentName(examStuAddBO.getStudentName());
        examResult.setStudentExamNum(examStuAddBO.getStudentExamNum());
        examResult.setStudentNamePinyin(Pinyin4jUtil.chinese2Pinyin(examStuAddBO.getStudentName()));
        examResult.setResultStatus(ExamResultStatusEnum.ABSENT.getCode());
        examResult.setCreatorId(user.getUserId());
        examResult.setCreatorName(user.getUserName());
        examResult.setCreateDateTime(now);
        examResult.setModifierId(user.getUserId());
        examResult.setModifierName(user.getUserName());
        examResult.setModifyDateTime(now);
        return examResult;
    }

    private void updateStuClass(ExamStuAddBO examStuAddBO, Long oldClassId) {
        long examId = examStuAddBO.getExamId();
        long schoolId = examStuAddBO.getSchoolId();
        // 保存初始的学生班级信息
        RequestVO user = RequestUtil.getUser();
        Map<String, Object> params = MapUtil.of(
                "examId", examId,
                "schoolId", schoolId,
                "userId", user.getUserId(),
                "userName", user.getUserName()
        );
        ((ExamStudentService) AopContext.currentProxy()).insertExamStudentRestore(params);

        //更新前的班级ids
        List<ExamClass> examClasses = examClassManager.findByExamIdAndSchool(examId, schoolId);
        List<Long> oldClassIds = examClasses.stream().map(ExamClass::getClassId).collect(toList());

        boolean classChanged = false;

        // 之前没有该班级
        if (!oldClassIds.contains(examStuAddBO.getClassId())) {
            classChanged = true;
            ExamClass examClass = createExamClass(examStuAddBO);
            examClassManager.batchInsert(Collections.singletonList(examClass));
        }

        List<ExamStudent> examStudentList = examStudentManager.findByExamAndClass(examId, oldClassId)
                .stream().filter(es -> es.getStudentId() != examStuAddBO.getStudentId()).collect(toList());
        // 更换之后班级没有其他学生，删除该班级
        if (CollectionUtils.isEmpty(examStudentList)) {
            classChanged = true;
            examClassManager.deleteByExamAndClass(examId, oldClassId);
        }

        //考试班级发生了变动时，需要更新报告的配置
        if (classChanged) {
            Map<String, Object> params1 = MapUtil.of(
                    "examId", examId,
                    "userId", user.getUserId(),
                    "userName", user.getUserName()
            );
            examConfigService.resetExamConfigInfo(params1);
        }
    }

    private ExamClass createExamClass(ExamStuAddBO examStuAddBO) {
        Date now = new Date();
        RequestVO user = RequestUtil.getUser();
        List<ExamPaperInfoVO> examPaperInfoVOS = examPaperClientService.listPapers(examStuAddBO.getExamId());
        ExamClass examClass = new ExamClass();
        examClass.setExamId(examStuAddBO.getExamId());
        examClass.setSchoolId(examStuAddBO.getSchoolId());
        examClass.setGradeId(examStuAddBO.getGradeId());
        examClass.setClassId(examStuAddBO.getClassId());
        examClass.setClassName(examStuAddBO.getClassName());
        examClass.setClassStatus(examPaperInfoVOS.get(0).getExamPaperStatus());
        examClass.setArtsScience(examPaperInfoVOS.get(0).getArtsScience());
        examClass.setClassType(1);
        examClass.setCreatorId(user.getUserId());
        examClass.setCreatorName(user.getUserName());
        examClass.setCreateDateTime(now);
        examClass.setModifierId(user.getUserId());
        examClass.setModifierName(user.getUserName());
        examClass.setModifyDateTime(now);
        return examClass;
    }

    public String exportAddStudentTemplate(ExamStuImportReq request) {
        long paperId = request.getPaperId();
        if (paperId != 0){
            return new ExportExcelTemplate("exam-student/singleAddStudentTemplate.xlsx", new HashMap<>())
                    .exportToFileStorage("单个答题卡批量导入考生", null);
        }
        return new ExportExcelTemplate("exam-student/batchAddStudentTemplate.xlsx", new HashMap<>())
                .exportToFileStorage("批量导入考生", null);
    }

    @Transactional(ExamRepository.TRANSACTION)
    public List<String> batchImportExamStudent(ExamStuImportReq request) {

        if (ObjectUtil.isBlank(request.getFilePath())) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请上传文件!");
        }
        long examId = request.getExamId();
        long paperId = request.getPaperId();

        ExamVO examDetail = newExamService.getExamDetail(examId);
        if (examDetail == null) {
            throw new DongniException(ErrorCode.USER_EXCEPTION, "未找到对应考试");
        }

        List<String> validators = new ArrayList<>();

        FileStorageTemplate.get(request.getFilePath(), file -> {
            List<StudentImportSheet> res = new ArrayList<>();
            StudentAddListener readListener =
                    new StudentAddListener(res, validators, ExamCheckUtil.isUnion(examDetail.getExamType()), paperId != 0);
            EasyExcel.read(file, StudentImportSheet.class, readListener)
                    .sheet()
                    .headRowNumber(2)
                    .doRead();
            if (!validators.isEmpty()) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, String.join("；", validators));
            }
            if (res.isEmpty()) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "文件内容为空！");
            }
            boolean fullCoverage = request.getAddStudentType() == 2;
            $this.checkAndImportExamStudent(examId, paperId, fullCoverage, validators, res);
        });
        if (!validators.isEmpty()) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, String.join("；", validators));
        }
        return validators;
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void checkAndImportExamStudent(long examId, long paperId, boolean fullCoverage,
                                           List<String> validators, List<StudentImportSheet> res) {
        ExamVO examDetail = newExamService.getExamDetail(examId);
        boolean isUnion = ExamCheckUtil.isUnion(examDetail.getExamType());
        // 考试班级
        List<ExamClass> examClasses = examClassManager.findByExamId(examId);
        List<Long> examClassIds = examClasses.stream().map(ExamClass::getClassId).collect(toList());
        // 学生基础数据
        List<ClassStudentBO> classStudents = getClassStudents(examClassIds);
        if (CollectionUtils.isEmpty(classStudents)) {
            validators.add("未找到学生信息");
            return;
        }
        Map<Long, Map<String, List<ClassStudentBO>>> schId2StuName2Students =
                classStudents.stream().collect(groupingBy(ClassStudentBO::getSchoolId, groupingBy(ClassStudentBO::getStudentName)));

        // 参考学校
        List<ExamSchool> examSchools = examSchoolManager.findByExamId(examId);
        if (CollectionUtils.isEmpty(examSchools)) {
            validators.add("未找到参考学校信息");
            return;
        }
        Map<String, ExamSchool> schoolName2SchoolInfo = examSchools.stream().collect(Collectors.toMap(ExamSchool::getSchoolName, Function.identity()));

        // 全量覆盖的时候删除已存在的学生
        if (fullCoverage){
            for (ExamSchool examSchool : examSchools) {
                ExamStuInfoQueryReq queryReq = new ExamStuInfoQueryReq();
                queryReq.setExamId(examId);
                queryReq.setSchoolId(examSchool.getSchoolId());
                List<ExamStudentBO> examStudents = getExamStudentByExam(queryReq);
                if (CollectionUtils.isEmpty(examStudents)) {
                    continue;
                }
                Set<Long> studentIds = examStudents.stream().map(ExamStudentBO::getStudentId).collect(toSet());
                Map<String, Object> params = MapUtil.of(
                        "examId", examId,
                        "paperId", paperId,
                        "studentId", StringUtils.join(studentIds, CommonConstant.COMMA),
                        "deleteByImport", true
                );
                $this.deleteExamStudent(params);
            }
        }

        // 考试课程
        List<ExamPaperInfoVO> examPaperInfoVOS = examPaperClientService.listPapers(examId);
        Map<String, List<ExamPaperInfoVO>> courseName2PaperInfo = examPaperInfoVOS.stream()
                .collect(Collectors.groupingBy(ExamPaperInfoVO::getCourseName));

        Map<Long, Boolean> paperId2IsStudentExist = new HashMap<>();

        // 已存在考生
        Set<Long> existStudentIds = new HashSet<>(examStudentManager.findStuIdsByExamId(examId));
        // 待导入考生
        Map<Long,List<Integer>> importStudentIds2RowNums = new HashMap<>();

        Map<Long, Set<String>> schoolId2StudentExamNums = new HashMap<>();

        List<ExamStuAddBO> examStuAddBOS = new ArrayList<>();
        for (StudentImportSheet studentInfo : res) {
            if (isUnion && !schoolName2SchoolInfo.containsKey(studentInfo.getSchoolName())) {
                validators.add("第" + studentInfo.getRowNumber() + "行，学校不在本次参考学校中");
                continue;
            }
            ExamSchool examSchool = isUnion ? schoolName2SchoolInfo.get(studentInfo.getSchoolName()) : examSchools.get(0);
            Long schoolId = examSchool.getSchoolId();
            Map<String, List<ClassStudentBO>> stuName2Students = schId2StuName2Students.getOrDefault(schoolId, Collections.emptyMap());
            List<ClassStudentBO> schSameNameStudents = stuName2Students.getOrDefault(studentInfo.getStudentName(), Collections.emptyList());
            if (CollectionUtils.isEmpty(schSameNameStudents)) {
                validators.add("第" + studentInfo.getRowNumber() + "行，未在学校【" + examSchool.getSchoolName() + "】的参考班级中找到名为【" + studentInfo.getStudentName() + "】的学生");
                continue;
            }
            ClassStudentBO classStudentBO;
            // 学校有同名学生
            if (schSameNameStudents.size() > 1) {
                // 先按班级查找
                // 没有填写班级
                if (StringUtils.isBlank(studentInfo.getClassName())) {
                    validators.add("第" + studentInfo.getRowNumber() + "行，学校【" + examSchool.getSchoolName() +
                            "】内找到同名学生【" + studentInfo.getStudentName() + "】，请填写班级名称");
                    continue;
                }
                List<ClassStudentBO> clsSameNameStudents = schSameNameStudents.stream()
                        .filter(sameNameStudent -> sameNameStudent.getClassName().equals(studentInfo.getClassName()))
                        .collect(toList());
                if (CollectionUtils.isEmpty(clsSameNameStudents)) {
                    validators.add("第" + studentInfo.getRowNumber() + "行，【" + studentInfo.getStudentName() +
                            "】在参考班级中不存在，请检查考生信息");
                    continue;
                }
                // 班级有同名学生
                else if (clsSameNameStudents.size() > 1) {
                    // 再按学号查找
                    // 没有填写学号
                    if (StringUtils.isBlank(studentInfo.getStudentNum())) {
                        validators.add("第" + studentInfo.getRowNumber() + "行，学校【" + examSchool.getSchoolName() + "】，班级【" +
                                studentInfo.getClassName() + "】内找到同名学生【" + studentInfo.getStudentName() + "】，请填写学号");
                        continue;
                    }
                    classStudentBO = clsSameNameStudents.stream()
                            .filter(s -> s.getStudentNum().equals(studentInfo.getStudentNum())).findFirst().orElse(null);
                    if (classStudentBO == null) {
                        validators.add("第" + studentInfo.getRowNumber() + "行，学号和姓名匹配失败");
                        continue;
                    }
                } else {
                    // 班级没有同名学生
                    classStudentBO = clsSameNameStudents.get(0);
                }
            }
            // 没有同名学生
            else {
                classStudentBO = schSameNameStudents.get(0);
                if (!StringUtils.isBlank(studentInfo.getClassName()) &&
                        !StringUtils.equals(studentInfo.getClassName(), classStudentBO.getClassName())) {
                    validators.add("第" + studentInfo.getRowNumber() + "行，【" + studentInfo.getStudentName() +
                            "】在参考班级中不存在，请检查考生信息");
                    continue;
                }
                if (!StringUtils.isBlank(studentInfo.getStudentNum()) &&
                        !StringUtils.equals(studentInfo.getStudentNum(), classStudentBO.getStudentNum())) {
                    validators.add("第" + studentInfo.getRowNumber() + "行，学号和姓名匹配失败");
                    continue;
                }
            }
            long studentId = classStudentBO.getStudentId();
            // 重复学生
            importStudentIds2RowNums.computeIfAbsent(studentId, k -> new ArrayList<>()).add(studentInfo.getRowNumber());
            // 考生已存在
            if (existStudentIds.contains(studentId)) {
                validators.add("第" + studentInfo.getRowNumber() + "行，学生【" + studentInfo.getStudentName() + "】已存在，请勿重复添加");
                continue;
            }
            // 单校考号不能重复
            Set<String> schoolStudentExamNums = schoolId2StudentExamNums.getOrDefault(schoolId, new HashSet<>());
            if (schoolStudentExamNums.contains(studentInfo.getStudentExamNum()) ||
                    examStudentManager.countSameExamNumStudents(examId, schoolId, studentId, studentInfo.getStudentExamNum()) > 0) {
                validators.add("第" + studentInfo.getRowNumber() + "行，【" + studentInfo.getStudentExamNum() +
                        "】考号在参考人数中重复，请检查并修改");
                continue;
            }
            schoolStudentExamNums.add(studentInfo.getStudentExamNum());
            schoolId2StudentExamNums.put(schoolId, schoolStudentExamNums);

            // 选择单个试卷下载的模板不带课程列，不校验可能存在的课程列
            if (paperId != 0) {
                List<ExamPaperInfoVO> examPaperInfos = examPaperInfoVOS.stream().filter(e -> e.getPaperId() == paperId).collect(toList());
                if (CollectionUtils.isEmpty(examPaperInfos)) {
                    validators.add("未找到参考试卷" + paperId);
                    continue;
                }
                List<ExamPaperDTO> examPapers = examPaperInfos.stream().map(paperInfo -> {
                    ExamPaperDTO e = new ExamPaperDTO();
                    e.setPaperId(paperInfo.getPaperId());
                    e.setCourseId(paperInfo.getCourseId());
                    e.setCourseName(paperInfo.getCourseName());
                    return e;
                }).collect(toList());
                ExamStuAddBO examStuAddBO = getExamStuAddBO(examId, studentInfo, classStudentBO, examSchool, examPapers);
                examStuAddBOS.add(examStuAddBO);
                paperId2IsStudentExist.putIfAbsent(paperId, true);
                continue;
            }

            String courses = studentInfo.getCourses();
            Set<String> courseNames = courses == null ?
                    courseName2PaperInfo.keySet() : Arrays.stream(courses.split("，")).collect(toSet());
            boolean courseError = false;
            List<ExamPaperDTO> examPapers = new ArrayList<>();
            for (String courseName : courseNames) {
                if (!courseName2PaperInfo.containsKey(courseName)) {
                    validators.add("未找到参考课程" + courseName);
                    courseError = true;
                    continue;
                }
                List<ExamPaperInfoVO> coursePaper = courseName2PaperInfo.get(courseName);
                if (coursePaper.size() > 1) {
                    validators.add(courseName + "课程存在多张答题卡，请按照答题卡分别导入考生");
                    courseError = true;
                    continue;
                }
                ExamPaperDTO e = new ExamPaperDTO();
                e.setPaperId(coursePaper.get(0).getPaperId());
                e.setCourseId(coursePaper.get(0).getCourseId());
                e.setCourseName(courseName);
                examPapers.add(e);
                paperId2IsStudentExist.putIfAbsent(coursePaper.get(0).getPaperId(), true);
            }
            if (courseError) {
                continue;
            }

            ExamStuAddBO examStuAddBO = getExamStuAddBO(examId, studentInfo, classStudentBO, examSchool, examPapers);
            examStuAddBOS.add(examStuAddBO);
        }

        importStudentIds2RowNums.values().stream()
                // 筛选重复行
                .filter(rowNums -> rowNums.size() > 1)
                .map(rowNums -> StringUtils.join(rowNums, "、"))
                .sorted(Comparator.comparing(s -> Integer.parseInt(s.split("、")[0])))
                .forEach(rowNums -> validators.add("第" + rowNums + "行，考生重复"));

        if (!validators.isEmpty()) {
            return;
        }

        if (fullCoverage) {
            List<String> noStudentPapers = examPaperInfoVOS.stream()
                    .filter(e -> paperId == 0 || e.getPaperId() == paperId)
                    .filter(e -> !paperId2IsStudentExist.getOrDefault(e.getPaperId(), false))
                    .map(ExamPaperInfoVO::getPaperName)
                    .collect(toList());
            if (!noStudentPapers.isEmpty()) {
                validators.add("试卷【"+StringUtils.join(noStudentPapers, "、")+"】，至少需有一位考生参与考试");
                return;
            }
        }

        fillStudentInfo(examId, examStuAddBOS);
        $this.addStudent(examId, examStuAddBOS);
    }

    private static ExamStuAddBO getExamStuAddBO(long examId, StudentImportSheet studentInfo, ClassStudentBO classStudentBO,
                                                ExamSchool examSchool, List<ExamPaperDTO> examPapers) {
        return new ExamStuAddBO()
                .setExamId(examId)
                .setStudentId(classStudentBO.getStudentId())
                .setStudentName(classStudentBO.getStudentName())
                .setStudentNum(classStudentBO.getStudentNum())
                .setStudentExamNum(studentInfo.getStudentExamNum())
                .setClassId(classStudentBO.getClassId())
                .setClassName(classStudentBO.getClassName())
                .setSchoolId(examSchool.getSchoolId())
                .setSchoolName(examSchool.getSchoolName())
                .setGradeId(examSchool.getGradeId())
                .setArtsScience(classStudentBO.getArtsScience())
                .setExamPaper(examPapers);
    }

    private List<ClassStudentBO> getClassStudents(List<Long> examClassIds) {
        if (examClassIds.isEmpty()) {
            return Collections.emptyList();
        }

        return baseDataRepository.selectList("ClassStudentMapper.getClassStudentInfoByIds", examClassIds);
    }


    private void fillStudentInfo(long examId, List<ExamStuAddBO> examStuAddBOS) {
        List<Long> studentIds = examStuAddBOS.stream().map(ExamStuAddBO::getStudentId).collect(toList());
        List<StudentUpdateInfoDTO> studentInfos = commonStudentService.getStudentUpdateInfoByIds(studentIds);

        // 考试标签
        List<ExamTagBO> examTagBOS = examTagManager.findByExam(examId);
        Map<Long, Map<String, Long>> schoolId2TagName2ExamTagId = examTagBOS.stream()
                .collect(groupingBy(ExamTagBO::getSchoolId,
                        toMap(ExamTagBO::getTagName, ExamTagBO::getExamTagId, (k1, k2) -> k1)));

        Map<Long, StudentUpdateInfoDTO> stuId2Info = studentInfos.stream()
                .collect(toMap(StudentUpdateInfoDTO::getStudentId, Function.identity(), (k1, k2) -> k1));

        for (ExamStuAddBO examStuAddBO : examStuAddBOS) {
            StudentUpdateInfoDTO studentInfo = stuId2Info.get(examStuAddBO.getStudentId());
            if (studentInfo == null) {
                continue;
            }
            if (studentInfo.getForeignCourseId() != null) {
                examStuAddBO.setForeignCourseId(studentInfo.getForeignCourseId());
            }
            if (studentInfo.getCourseSelectionGroupId() != null) {
                examStuAddBO.setCourseSelectionGroupId(studentInfo.getCourseSelectionGroupId());
            }
            if (studentInfo.getTagId() != null) {
                Set<String> tagNames = Arrays.stream(studentInfo.getTagName().split(CommonConstant.COMMA))
                        .collect(Collectors.toSet());
                Map<String, Long> tagName2ExamTagId = schoolId2TagName2ExamTagId.getOrDefault(studentInfo.getSchoolId(), Collections.emptyMap());
                List<StudentTagBo> studentTagBos = tagNames.stream()
                        .filter(tagName2ExamTagId::containsKey)
                        .map(tagName -> new StudentTagBo().setExamTagId(tagName2ExamTagId.get(tagName)).setTagName(tagName))
                        .collect(toList());
                examStuAddBO.setStudentTags(studentTagBos);
            }
        }
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void addStudent(long examId, List<ExamStuAddBO> examStuAddBOS) {

        // 校验t_exam_class
        List<ExamClassVO> examClassVOS = newExamService.listExamClasses(examId);
        Map<Long, ExamClassVO> examClassIds = examClassVOS.stream().collect(toMap(ExamClassVO::getClassId, Function.identity()));

        // 考试标签
        List<ExamTagBO> examTagBOS = examTagManager.findByExam(examId);
        Map<Long, Map<String, Long>> schoolId2TagName2ExamTagId = examTagBOS.stream()
                .collect(groupingBy(ExamTagBO::getSchoolId,
                        toMap(ExamTagBO::getTagName, ExamTagBO::getExamTagId, (k1, k2) -> k1)));

        // 参考学校
        List<ExamSchool> examSchools = examSchoolManager.findByExamId(examId);
        Map<Long, ExamSchool> schoolId2ExamSchool = examSchools.stream().collect(toMap(ExamSchool::getSchoolId, i -> i));

        // 不同课程下每个班级参考的试卷
        Map<Long, Map<Long, Set<Long>>> courseId2ClassId2PaperIds = new HashMap<>();

        // 已存在考生
        Set<Long> existStudentIds = new HashSet<>(examStudentManager.findStuIdsByExamId(examId));

        Set<Long> addPaperIds = new HashSet<>();
        for (ExamStuAddBO examStuAddBO : examStuAddBOS) {
            long classId = examStuAddBO.getClassId();
            if (!examClassIds.containsKey(classId)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "班级【" + examStuAddBO.getClassName() + "】未参加考试");
            }
            List<ExamPaperDTO> examPaper = examStuAddBO.getExamPaper();
            if (CollectionUtils.isEmpty(examPaper)){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学生【" + examStuAddBO.getStudentName() + "】的参考试卷不能为空");
            }
            for (ExamPaperDTO examPaperDTO : examPaper) {
                long courseId = examPaperDTO.getCourseId();
                long paperId = examPaperDTO.getPaperId();
                courseId2ClassId2PaperIds
                        .computeIfAbsent(courseId, k -> new HashMap<>())
                        .computeIfAbsent(classId, k -> new HashSet<>())
                        .add(paperId)
                ;
                addPaperIds.add(paperId);
            }

            long schoolId = examStuAddBO.getSchoolId();
            if (!schoolId2ExamSchool.containsKey(schoolId)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学校【" + examStuAddBO.getSchoolName() + "】未参加考试");
            }
            List<StudentTagBo> studentTags = examStuAddBO.getStudentTags();
            Map<String, Long> tagName2ExamTagId = schoolId2TagName2ExamTagId.getOrDefault(schoolId, Collections.emptyMap());
            for (StudentTagBo studentTag : studentTags) {
                if (!tagName2ExamTagId.containsKey(studentTag.getTagName())) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学校【" + examStuAddBO.getSchoolName() + "】的标签【" + studentTag.getTagName() + "】不存在");
                }
                if (!Objects.equals(tagName2ExamTagId.get(studentTag.getTagName()), studentTag.getExamTagId())) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学校【" + examStuAddBO.getSchoolName() + "】的标签【" + studentTag.getTagName() + "】ID错误");
                }
            }
            String studentExamNum = examStuAddBO.getStudentExamNum();
            if (StringUtils.isBlank(studentExamNum)) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "学生【" + examStuAddBO.getStudentName() + "】的考号不能为空");
            }

            if (existStudentIds.contains(examStuAddBO.getStudentId())) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "学生【" + examStuAddBO.getStudentName() + "】已存在");
            }

            int sameExamNumStudents = examStudentManager.countSameExamNumStudents(examId, schoolId, examStuAddBO.getStudentId(), studentExamNum);
            if (sameExamNumStudents > 0) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "学校【" + examStuAddBO.getSchoolName() + "】已存在考号为【" + studentExamNum + "】的学生");
            }
        }

        // 校验t_exam_paper
        List<ExamPaperInfoVO> examPaperInfoVOS = examPaperClientService.listPapers(examId);
        Map<Long, ExamPaperInfoVO> paperId2Info = examPaperInfoVOS.stream().collect(toMap(ExamPaperInfoVO::getPaperId, Function.identity()));
        for (Long addPaperId : addPaperIds) {
            if (!paperId2Info.containsKey(addPaperId)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未找到参考试卷【" + addPaperId + "】");
            }
        }

        // 学校参考试卷
        List<ExamSchoolPaper> examSchoolPapers = examSchoolPaperManager.findByExam(examId);
        Map<Long, Map<Long, ExamSchoolPaper>> schId2Info = examSchoolPapers.stream()
                .collect(groupingBy(ExamSchoolPaper::getSchoolId, toMap(ExamSchoolPaper::getPaperId, Function.identity())));

        // 需要新增的t_exam_class_paper
        List<ExamClassPaper> newClassPapers = new ArrayList<>();
        List<ExamSchoolPaper> newSchoolPapers = new ArrayList<>();
        // 校验t_exam_class_paper,需要同时校验已有的和新增的
        courseId2ClassId2PaperIds.forEach((courseId, classId2PaperIds) -> {
            classId2PaperIds.forEach((classId, classPaperIds) -> {
                // 已有的t_exam_class_paper
                Set<Long> oldClassPaperIds = examClassPaperManager.findByExamAndCourseAndClsId(examId, courseId, classId)
                        .stream().map(ExamClassPaper::getPaperId).collect(toSet());
                // 新增的是多个试卷或者与旧的试卷不一致
                if (classPaperIds.size() > 1 || (CollectionUtils.isNotEmpty(oldClassPaperIds) && !oldClassPaperIds.containsAll(classPaperIds))) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "相同科目下一个班级只能参考一个答题卡");
                }
                // 需要新增t_exam_class_paper t_exam_school_paper
                if (CollectionUtils.isEmpty(oldClassPaperIds)) {
                    Long paperId = classPaperIds.stream().findFirst().get();
                    ExamClassVO examClassVO = examClassIds.get(classId);
                    Map<Long, ExamSchoolPaper> paperId2SchoolPaper = schId2Info.getOrDefault(examClassVO.getSchoolId(), Collections.emptyMap());
                    initClassPaperAndSchoolPaper(examId, paperId, examClassVO,
                            paperId2SchoolPaper.get(paperId), paperId2Info.get(paperId), newSchoolPapers, newClassPapers);
                }
            });
        });

        initExamStudentAndResult(examId, examStuAddBOS);

        // 添加标签
        addExamStudentTag(examStuAddBOS);

        if (CollectionUtils.isNotEmpty(newSchoolPapers)) {
            examSchoolPaperManager.batchInsert(newSchoolPapers);
        }
        if (CollectionUtils.isNotEmpty(newClassPapers)) {
            examClassPaperManager.batchInsert(newClassPapers);
            //重置报告配置
            RequestVO user = RequestUtil.getUser();
            Map<String, Object> params = MapUtil.of(
                    "examId", examId,
                    "userId", user.getUserId(),
                    "userName", user.getUserName()
            );
            examConfigService.resetExamConfigInfo(params);
        }
    }

    private void addExamStudentTag(List<ExamStuAddBO> examStuAddBOS) {
        RequestVO user = RequestUtil.getUser();
        Date now = new Date();
        List<ExamStudentTag> examStudentTags = new ArrayList<>();
        for (ExamStuAddBO examStuAddBO : examStuAddBOS) {
            List<StudentTagBo> studentTags = examStuAddBO.getStudentTags();
            for (StudentTagBo studentTag : studentTags) {
                ExamStudentTag examStudentTag = new ExamStudentTag()
                        .setExamTagId(studentTag.getExamTagId())
                        .setStudentId(examStuAddBO.getStudentId())
                        .setExamId(examStuAddBO.getExamId())
                        .setCreatorId(user.getUserId())
                        .setCreatorName(user.getUserName())
                        .setCreateDateTime(now)
                        .setModifierId(user.getUserId())
                        .setModifierName(user.getUserName())
                        .setModifyDateTime(now);
                examStudentTags.add(examStudentTag);
            }
        }

        if (CollectionUtils.isEmpty(examStudentTags)){
            return;
        }
        examStudentTagManager.batchInsert(examStudentTags);
    }

    /**
    * 初始化t_exam_student和t_exam_result
    */
    private void initExamStudentAndResult(long examId, List<ExamStuAddBO> examStuAddBOS) {

        Date now = new Date();
        RequestVO user = RequestUtil.getUser();
        Map<String, Object> params = MapUtil.of(
                "examId", examId,
                "userId", user.getUserId(),
                "userName", user.getUserName(),
                "currentTime", now
        );

        List<Map<String, Object>> examStudent = new ArrayList<>();
        List<Map<String,Object>> examResult = new ArrayList<>();

        for (ExamStuAddBO examStuAddBO : examStuAddBOS) {
            Map<String, Object> tes = new HashMap<>();
            tes.put("examId", examId);
            tes.put("schoolId", examStuAddBO.getSchoolId());
            tes.put("classId", examStuAddBO.getClassId());
            tes.put("className", examStuAddBO.getClassName());
            tes.put("studentId", examStuAddBO.getStudentId());
            tes.put("studentName", examStuAddBO.getStudentName());
            tes.put("studentNum", examStuAddBO.getStudentNum());
            tes.put("studentExamNum", examStuAddBO.getStudentExamNum());
            tes.put("courseSelectionGroupId", examStuAddBO.getCourseSelectionGroupId());
            tes.put("foreignCourseId", examStuAddBO.getForeignCourseId());
            tes.put("artsScience", examStuAddBO.getArtsScience());
            tes.put("userId", user.getUserId());
            tes.put("userName", user.getUserName());
            tes.put("currentTime", now);
            examStudent.add(tes);

            List<ExamPaperDTO> examPaper = examStuAddBO.getExamPaper();
            for (ExamPaperDTO examPaperDTO : examPaper) {
                Map<String, Object> ter = new HashMap<>();
                ter.put("examId", examId);
                ter.put("courseId", examPaperDTO.getCourseId());
                ter.put("paperId", examPaperDTO.getPaperId());
                ter.put("schoolId", examStuAddBO.getSchoolId());
                ter.put("classId", examStuAddBO.getClassId());
                ter.put("className", examStuAddBO.getClassName());
                ter.put("studentId", examStuAddBO.getStudentId());
                ter.put("studentNum", examStuAddBO.getStudentNum());
                ter.put("studentName", examStuAddBO.getStudentName());
                ter.put("studentExamNum", examStuAddBO.getStudentExamNum());
                ter.put("resultStatus", DictUtil.getDictValue("resultStatus", "attend"));
                ter.put("userId", user.getUserId());
                ter.put("userName", user.getUserName());
                ter.put("currentTime", now);
                examResult.add(ter);
            }
        }

        insertStudentInfoToExam(params, examId, examStudent, examResult, true);
    }

    /**
     * 初始化t_exam_class_paper,t_exam_school_paper
     */
    private static void initClassPaperAndSchoolPaper(long examId, Long paperId, ExamClassVO examClassVO,
                                                     ExamSchoolPaper examSchoolPaper, ExamPaperInfoVO examPaperInfoVO,
                                                     List<ExamSchoolPaper> newSchoolPapers, List<ExamClassPaper> newClassPapers) {
        RequestVO user = RequestUtil.getUser();
        Date now = new Date();
        ExamClassPaper classPaper = new ExamClassPaper();

        classPaper.setExamId(examId);
        classPaper.setPaperId(paperId);
        long schoolId = examClassVO.getSchoolId();
        classPaper.setSchoolId(schoolId);
        classPaper.setGradeId(examClassVO.getGradeId());
        classPaper.setClassId(examClassVO.getClassId());
        classPaper.setClassName(examClassVO.getClassName());
        classPaper.setClassType(examClassVO.getClassType());
        classPaper.setArtsScience(examClassVO.getArtsScience());
        // 需要添加参考学校
        if (examSchoolPaper == null) {
            ExamSchoolPaper schoolPaper = new ExamSchoolPaper();
            schoolPaper.setExamId(examId);
            schoolPaper.setSchoolId(schoolId);
            schoolPaper.setPaperId(paperId);
            int examSchoolPaperStatus = examPaperInfoVO.getExamPaperStatus() == ExamPaperStatus.Finished.getStatus() ?
                    ExamPaperStatus.Finished.getStatus() : ExamPaperStatus.StructureSplit.getStatus();
            schoolPaper.setExamSchoolPaperStatus(examSchoolPaperStatus);
            schoolPaper.setCreatorId(user.getUserId());
            schoolPaper.setCreatorName(user.getUserName());
            schoolPaper.setCreateDateTime(now);
            schoolPaper.setModifierId(user.getUserId());
            schoolPaper.setModifierName(user.getUserName());
            schoolPaper.setModifyDateTime(now);
            newSchoolPapers.add(schoolPaper);

            classPaper.setExamClassPaperStatus(examSchoolPaperStatus);
        } else {
            classPaper.setExamClassPaperStatus(examSchoolPaper.getExamSchoolPaperStatus());
        }
        classPaper.setCreatorId(user.getUserId());
        classPaper.setCreatorName(user.getUserName());
        classPaper.setCreateDateTime(now);
        classPaper.setModifierId(user.getUserId());
        classPaper.setModifierName(user.getUserName());
        classPaper.setModifyDateTime(now);
        newClassPapers.add(classPaper);
    }


    /**
     * 校验在添加result后是否存在有学生在单个课程中考多张试卷的情况
     * @param examId 考试id
     * @param stuResult 要添加的result
     */
    private void checkCourseDuplicateStuResult(Long examId, List<Map<String, Object>> stuResult) {
        if (stuResult.isEmpty()) {
            return;
        }
        Map<Long, List<Map<String, Object>>> courseId2StuResultList = stuResult.stream()
          .collect(groupingBy(x -> MapUtils.getLong(x, "courseId")));
        Map<Long, String> paperId2PaperName = examPaperService.getExamPaper(examId).stream()
          .collect(toMap(x -> MapUtils.getLong(x, "paperId"), x -> MapUtils.getString(x, "paperName")));
        List<Map<String, Object>> existsStuResult = commonRepository.selectList("ExamResultMapper.getExistsStuResultByCourseAndStuList",
          MapUtil.of("examId", examId, "courseIds", courseId2StuResultList.keySet(), "studentIds",
            stuResult.stream().map(x -> MapUtils.getLong(x, "studentId")).collect(Collectors.toSet())));
        Map<Long, List<Map<String, Object>>> courseId2ExistsStuResultList = existsStuResult.stream()
          .collect(groupingBy(x -> MapUtils.getLong(x, "courseId")));
        courseId2StuResultList.forEach((courseId, stuResultList) -> {
            List<Map<String, Object>> courseExistsStuResult = courseId2ExistsStuResultList.get(courseId);
            if (CollectionUtils.isEmpty(courseExistsStuResult)) {
                return;
            }
            Map<Long, List<Long>> stuId2ExistsPaperIds = courseExistsStuResult.stream()
              .collect(groupingBy(x -> MapUtils.getLong(x, "studentId"), mapping(x -> MapUtils.getLong(x, "paperId"), toList())));
            stuResultList.forEach(result -> {
                List<Long> existsPaperIds = stuId2ExistsPaperIds.get(MapUtils.getLong(result, "studentId"));
                if (CollectionUtils.isNotEmpty(existsPaperIds)) {
                    existsPaperIds.add(MapUtils.getLong(result, "paperId"));
                    List<String> paperNames = existsPaperIds.stream().map(x -> Optional.ofNullable(paperId2PaperName.get(x)).orElseThrow(
                      () -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,
                        "试卷在诊断中不存在，请刷新页面后再试！examId：" + examId + "，paperId：" + x))).collect(toList());
                    throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                      result.get("studentName") + "同时参考了" + result.get("courseName") + "课程下的" + StringUtils.join(paperNames, "、") +
                        "，学生在单个课程中只能参考一张试卷！");
                }
            });
        });
    }

    /**
     * 获取不在此考试中的学生
     * 考试工具箱：examId paperIds（一或多个） fromTool:true
     * 异常处理：examId classIds（一或多个） paperIds（一个）
     * 作业工具箱：examId classIds（一个）
     * @param params examId classId
     * @return 学生
     */
    public Map<String, Object> getAvailableStudent(Map<String, Object> params) {
        Verify.of(params).isValidId("examId").verify();
        List<Map<String, Object>> classStudentIds = new ArrayList<>();
        if (ObjectUtil.isNotBlank(params.get("classIds"))) {
            params.put("classIds", Arrays.stream(params.get("classIds").toString().split(",")).collect(toList()));
        }
        List<Long> rangeClassIds = null;
        if(ObjectUtil.isNotBlank(params.get("paperIds"))){
            //查找examClass中的班级id 学生所在的班级的范围
            rangeClassIds = commonRepository.selectList("ExamStudentMapper.getExamClassId", params);
            if (rangeClassIds.isEmpty()) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "诊断班级数据为空，请联系管理员处理");
            }
            Boolean fromTool = MapUtils.getBoolean(params, "fromTool", false);
            //存在选多个paperId的情况,当选择多个paperId时,只要有一科未关联t_exam_result,就需要返回
            for (String paperId : params.get("paperIds").toString().split(",")) {
                Map<String, Object> paperParam = MapUtil.of("search", params.get("search"));
                params.put("paperId", paperId);
                List<Long> paperStudentIds = commonRepository.selectList("ExamStudentMapper.getStudentIdWithPaper", params);
                if (CollectionUtils.isNotEmpty(paperStudentIds)) {
                    paperParam.put("studentIds", paperStudentIds);
                }
                // 可能会出现学生选科信息选错需要添加学生选科但班级又不参考这个课程的情况，此时如果试卷下所有的班级都是行政班的话，
                // 那么支持直接根据t_exam_class来查询学生，不再根据t_exam_class_paper
                int count = commonRepository.selectOne("ExamClassPaperMapper.getExamPaperTeachClassCountByPaperId", params);
                if (fromTool && Objects.equals(count, 0)) {
                    paperParam.put("classIds", rangeClassIds);
                }else {
                    //从考试工具箱-考生管理-添加学生处调接口时，不会传classIds
                    paperParam.put("classIds", fromTool ? commonRepository.selectList("ExamClassPaperMapper.getExamClassPaperByPaperId", params)
                      : params.get("classIds"));
                }
                classStudentIds.add(paperParam);
            }
        }else {
            List<Long> stuIds = commonRepository.selectList("ExamStudentMapper.getStudentId", params);
            if(CollectionUtils.isNotEmpty(stuIds)){
                params.put("studentIds", stuIds);
            }
            classStudentIds.add(MapUtil.copy(params, "classIds", "studentIds", "search"));
        }
        return exportExamStudentService.getAvailableStudent(params, classStudentIds, rangeClassIds);
    }

    /**
     * 查询学生考试列表
     */
    public Map<String, Object> getStudentExam(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .verify();

        Map<String, Object> resultMap = new HashMap<>();
        boolean isHide = hideExamList(params);
        if (isHide) {
            return MapUtil.of("totalCount", 0, "exam", Collections.emptyList());
        }

        // 非vip只查询90天内的考试 membershipStatus==1是vip
//        Map<String, Object> studentMembership = userMembershipService.getStudentMembership(params);
//        boolean vip = studentMembership != null && ObjectUtil.isValueEquals(studentMembership.get("membershipStatus"), 1);
//        if (!vip) {
//            LocalDate startDate = LocalDate.now().minusDays(90);
//            params.put("startDate", startDate);
//        }

        params.put("examType", Arrays.asList(2, 3, 4, 5, 7, 9, 10));
        params.put("readComplete", DictUtil.getDictValue("examPaperStatus","readComplete"));
        params.put("classStatus", DictUtil.getDictValue("examPaperStatus","readComplete"));

        List<Map<String, Object>> examList;
        List<Map<String, Object>> examByClassList;
        //判断查询全部还是单科
        if (ObjectUtil.isBlank(params.get("courseId"))) {
            examList = commonRepository.selectList("ExamResultMapper.getExamStudent", params);
            examByClassList = commonRepository.selectList("ExamResultMapper.getExamStudentByClass", params);
            //按班级阅卷过滤非自动公布的考试
            List<Map<String, Object>> examByClassAutoPublish = getPublishExam(params, examByClassList);
            examList.addAll(examByClassAutoPublish);
        } else {
            Long courseId = Long.valueOf(params.get("courseId").toString());
            examList = commonRepository.selectList("ExamResultMapper.getStudentExam", params);
            examByClassList = commonRepository.selectList("ExamResultMapper.getStudentExamByClass", params);
            //按班级阅卷过滤非自动公布的考试
            List<Map<String, Object>> examByClassAutoPublish = getPublishExam(params, examByClassList);
            examList.addAll(examByClassAutoPublish);
            List<Map<String, Object>> resultList = new ArrayList<>();
            wrongExamService.dealCourse(examList, resultList, courseId);
            examList = resultList;
        }

        resultMap.put("totalCount", examList.size());
        if (examList.isEmpty()) {
            resultMap.put("exam", Collections.emptyList());
            return resultMap;
        }

        List<Map<String, Object>> exam = examList.stream()
                .sorted(Comparator.comparing(m -> ((Date) ((Map) m).get("startDate")).getTime()).reversed())
                .skip((int) params.get("currentIndex")).limit((int) params.get("pageSize"))
                .collect(Collectors.toList());

        List<Long> examIds = exam.stream().map(i -> MapUtil.getLongNullable(i, "examId"))
                .collect(toList());

        // 查询已公布报告学生的得分
        Map<Long, List<Document>> examStudentStatMap = Collections.emptyMap();
        List<Map<String, Object>> publishStats = examStatViewService.getPublishSchoolExamStat(examIds, MapUtil.getLong(params, "schoolId"));
        List<Bson> examIdAndStatIdList = publishStats.stream().map(i ->
                and(eq("statId", MapUtil.getInt(i, "statId")),
                        eq("examId", MapUtil.getLongNullable(i, "examId"))))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(examIdAndStatIdList)) {
            Bson query = and(eq("studentId", MapUtil.getLong(params, "studentId")), or(examIdAndStatIdList));
            examStudentStatMap = examStudentStatManager.getList(query,
                        new String[]{"examId", "statId", "classId", "studentId", "resultStatus", "totalScore", "fullMark",
                                "originalScore", "classRanking"},
                        new String[]{"_id"})
                .stream().collect(groupingBy(i -> MapUtil.getLong(i, "examId")));
        }

        // 是否开启新高考赋分
        Map<Long, Map<Integer, Document>> examId2StatId2ScoreChange = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(examIdAndStatIdList)) {
            examId2StatId2ScoreChange = examConfigManager
                .getList(or(examIdAndStatIdList), new String[]{"scoreChange", "examId", "statId"})
                    .stream().collect(groupingBy(i -> MapUtil.getLong(i, "examId"),
                            toMap(i -> MapUtil.getInt(i, "statId"), Function.identity(), (o1, o2) -> o1)));
        }

        // 将报告数据塞入考试里面
        Map<String, Object> displayParams = MapUtil.copy(params, "schoolId");
        for (Map<String, Object> examItem : exam) {
            long examId = MapUtil.getLong(examItem, "examId");
            displayParams.put("examId", examId);
            Map<String, Object> studentDisplayConfig = displayExamService.getStudentDisplayConfig(displayParams);
            // 个人得分可见性
            boolean displayScoreAndRanking = Optional.ofNullable(studentDisplayConfig)
                    .<Map<String, Object>>map(i -> MapUtil.getCast(i, "scoreStatus"))
                    .map(i -> MapUtil.getInt(i, "scoreAndRanking", 0))
                    .map(i -> i == 1)
                    .orElse(false);
            // 班级排名可见性
            Boolean displayClassRanking = Optional.ofNullable(studentDisplayConfig)
                    .map(i -> MapUtil.getInt(i, "classRankingStatus", 0))
                    .map(i -> i == 1)
                    .orElse(false);

            // 已公布的第一个报告
            Document minExamStudentStat = examStudentStatMap.getOrDefault(examId, Collections.emptyList())
                    .stream()
                    .min(Comparator.comparingInt(i -> MapUtil.getInt(i, "statId")))
                    .orElse(null);

            if (minExamStudentStat != null) {
                Document stat = new Document("examId", examId).append("studentId", MapUtil.getLong(params, "studentId"));
                stat.put("statId", minExamStudentStat.get("statId"));
                stat.put("resultStatus", minExamStudentStat.get("resultStatus"));

                if (displayScoreAndRanking) {
                    stat.put("fullMark", minExamStudentStat.get("fullMark"));
                    stat.put("originalScore", minExamStudentStat.get("originalScore"));
                    stat.put("scoreChange", minExamStudentStat.get("scoreChange"));
                    stat.put("totalScore", minExamStudentStat.get("totalScore"));
                    Boolean scoreChange = Optional.of(examId2StatId2ScoreChange.get(examId))
                        .map(i -> i.get(MapUtil.getInt(minExamStudentStat, "statId")))
                        .map(i -> MapUtil.<Document>getCast(i, "scoreChange"))
                        .map(i -> MapUtil.getBoolean(i, "enable"))
                        .orElse(false);
                    stat.put("scoreChange", scoreChange);
                }
                if (displayClassRanking) {
                    Bson query = and(
                            eq("examId", examId),
                            eq("statId", minExamStudentStat.get("statId")),
                            eq("classId", minExamStudentStat.get("classId"))
                    );
                    Document firstClassStat = examClassStatManager.getFirst(query, new String[]{"participationNumber"});
                    Integer participationNumber = Optional.of(firstClassStat)
                            .map(i -> i.getInteger("participationNumber"))
                            .orElse(null);
                    if (participationNumber != null) {
                        stat.put("classRankNumber", participationNumber);
                        stat.put("classRanking", minExamStudentStat.get("classRanking"));
                    }
                }
                examItem.put("stat", stat);
            } else {
                examItem.put("stat", null);
            }
        }
        
        // 处理填充字典值
        for (Map<String, Object> examItem : exam) {
            Object examTypeObj = MapUtil.getCast(examItem, "examType");
            Integer examType = MapUtil.getIntNullable(examTypeObj);
            String examTypeName;
            if (examType == null) {
                examTypeName = "缺少值(" + examTypeObj + ")";
            } else {
                try {
                    examTypeName = DictUtil.getDictLabel("examType", examType);
                } catch (CommonException e) {
                    if (e.getMessage().contains("字典值不存在")) {
                        examTypeName = "不存在(" + examType +")";
                    } else {
                        throw e;
                    }
                }
            }
            examItem.put("examTypeName", examTypeName);
        }
        
        resultMap.put("exam", exam);
        return resultMap;
    }

    public List<Map<String, Object>> getPublishExam(Map<String, Object> params, List<Map<String, Object>> examByClassList) {
        List<Map<String,Object>> examByClassAutoPublish = examByClassList.stream()
                .filter(s->s.get("autoPublish").toString().equals("1")).collect(Collectors.toList());
        List<Map<String,Object>> examByClassNotAutoPublish = examByClassList.stream()
                .filter(s->s.get("autoPublish").toString().equals("0")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(examByClassNotAutoPublish)){
            List<String> examIds = examByClassNotAutoPublish.stream()
                    .map(s->s.get("examId").toString()).collect(Collectors.toList());
            params.put("examIds",examIds);
            List<Map<String,Object>> publishExam = commonRepository.selectList("ExamResultMapper.getPublishExamIn",params);
            Set<String> publishExamIdSet = publishExam.stream().collect(groupingBy(s->s.get("examId").toString())).keySet();
            List<Map<String,Object>> temp = examByClassNotAutoPublish.stream()
                    .filter(s->publishExamIdSet.contains(s.get("examId").toString())).collect(Collectors.toList());
            examByClassAutoPublish.addAll(temp);
        }
        return examByClassAutoPublish;
    }

    /**
     * 查询学生考试最近考试
     */
    public Map<String, Object> getStudentExamLatest(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .verify();

        params.put("currentIndex", 0);
        params.put("pageSize", 3);
        return getStudentExam(params);
    }

    /**
     * 查询学生考试列表
     */
    public Map<String, Object> getStudentExamInfo(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("examId")
                .verify();
        return commonRepository.selectOne("ExamResultMapper.getExamStudentInfo", params);
    }

    /**
     * 查询学生考试列表
     */
    public List<Map<String, Object>> getStudentExamInfoListForDeclaration(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNotEmptyCollections("studentIdList")
                .verify();
        return commonRepository.selectList("ExamResultMapper.getExamStudentInfoList", params);
    }

    /**
     * 查询学生近十场考试
     */
    public List<Map<String, Object>> getStudentExamLimit(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();
        params.put("limitCount", 10);

        return commonRepository.selectList("ExamResultMapper.getStudentExamInfo", params);
    }
    /**
     * 查询学生所有考试
     */
    public List<Map<String, Object>> getAllStudentExamAsc(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();
        return commonRepository.selectList("ExamResultMapper.getAllStudentExamAsc", params);
    }

    /**
     * 查询学生所有考试 包含未公布的
     */
    public List<Map<String, Object>> getAllStudentExamAscForWrongBook(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .verify();
        return commonRepository.selectList("ExamResultMapper.getAllStudentExamAscForWrongBook", params);
    }

    /**
     * 查询学生近十场考试
     */
    public Map<String, Object> getExamStartDate(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .verify();
        return commonRepository.selectOne("ExamResultMapper.getExamStartDate", params);
    }

    /**
     * 修改考试所在班级信息
     * @param params
     * userId       用户Id
     * userName     用户名
     * examId       考试Id
     * studentId    要修改的学生Id
     * classId      要更换到的班级Id
     * className    要更换到的班级名称
     * paperId      试卷Id
     * courseId     课程Id
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateExamStudent(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("paperId")
                .isValidId("studentId")
                .isValidId("classId")
                .isNotBlank("className")
                .isNotBlank("updateType")
                .verify();

        // 保存初始的学生班级信息
        ((ExamStudentService) AopContext.currentProxy()).insertExamStudentRestore(params);

        params.putAll(commonRepository.selectOne("ExamClassPaperMapper.getExamPaperInfo",params));
        String updateType = params.get("updateType").toString();
        //更新前的班级ids
        List<Long> oldClassIds;
        //更新后的班级ids
        List<Long> newClassIds;

        // 替换总分
        if (updateType.equals("total")) {
            oldClassIds = commonRepository.selectList("ExamClassMapper.getExamClassIdList", params);
            // t_exam_student
            commonRepository.update("ExamStudentMapper.updateExamStudentInfo", params);
            // t_exam_class
            commonRepository.insert("ExamClassMapper.insertExamClassByExam", params);

            // 删除被剔除的班级
            List<Long> classIdSet = commonRepository.selectList("ExamStudentMapper.getExamClassInfo", params);
            params.put("classIdSet", classIdSet);
            if (CollectionUtils.isNotEmpty(classIdSet)) {
                commonRepository.delete("ExamClassMapper.deleteExamClassByClassId", params);
            }
            newClassIds = commonRepository.selectList("ExamClassMapper.getExamClassIdList", params);
        // 替换单科
        }else if (updateType.equals("course")){
            oldClassIds = commonRepository.selectList("ExamClassPaperMapper.getExamClassPaperByPaperId", params);
            // t_exam_item
            commonRepository.update("ExamStudentMapper.updateExamStudentItem",params);
            // t_exam_result
            commonRepository.update("ExamResultMapper.updateExamStudentResult",params);
            // t_exam_class_paper
            commonRepository.insert("ExamClassPaperMapper.insertExamClassPaper",params);

            // 删除被剔除的班级
            List<Long> classIdSet = commonRepository.selectList("ExamResultMapper.getExamStudentResultClass", params);
            params.put("classIdSet", classIdSet);
            if (CollectionUtils.isNotEmpty(classIdSet)) {
                commonRepository.delete("ExamClassPaperMapper.deleteExamClassPaperByClassId", params);
            }
            newClassIds = commonRepository.selectList("ExamClassPaperMapper.getExamClassPaperByPaperId", params);
        } else {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "不支持的更换班级方式");
        }

        Set<Long> oldClassIdSet = new HashSet<>(oldClassIds);
        Set<Long> newClassIdSet = new HashSet<>(newClassIds);
        if (!oldClassIdSet.containsAll(newClassIdSet) || !newClassIdSet.containsAll(oldClassIdSet)) {
            //考试班级或者考试试卷班级发生了变动时，需要更新报告的配置
            examConfigService.resetExamConfigInfo(params);
        }
    }


    /**
    * @Description: 根据最新的基础学生数据更新学生考试所在的班级
    * @Param:  examId paperIds schoolId gradeId
    */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateExamStudentClass(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .isValidId("gradeId")
                .verify();

        //在进行一键更新操作前，判断是否有课程的参考班级包含了教学班，只要t_exam_class_paper中有班级是教学班，就不可以进行一键更新班级的操作，否则更新后数据就会出问题
        params.put("teachingClassType", DictUtil.getDictValue("classType", "teaching"));
        Map<String, Object> teachingClass = commonRepository.selectOne("ExamClassPaperMapper.getExamClassPaperTeachingClass", params);
        if (MapUtils.isNotEmpty(teachingClass)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
              "存在教学班，不支持本功能！如需调整班级，可在调整基础数据后，报告管理-新增报告完成班级数据更新：建议关闭【原始报告】");
        }
        //如果是按班级，且考试还没阅卷结束，也不允许操作
        Map<String, Object> examDetail = examService.getExamDetail(params);
        if (DictUtil.isEquals(MapUtils.getInteger(examDetail, "correctMode"), "correctMode", "readByClass") &&
          DictUtil.isEquals(MapUtils.getInteger(examDetail, "examStatus"), "examStatus", "executing")) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "按班级阅卷的诊断请在诊断结束后再使用本功能");
        }

        List<Map<String, Object>> examSchoolPaper = examSchoolPaperService.getExamSchoolPaperByExamIdAndSchoolId(params);
        Map<Long, Integer> paperId2Status = examSchoolPaper.stream()
          .collect(toMap(x -> MapUtils.getLong(x, "paperId"), x -> MapUtils.getInteger(x, "examSchoolPaperStatus")));
        List<Long> paperIdList = new ArrayList<>(paperId2Status.keySet());
        Map<String, Object> examSchool = examSchoolService.getExamSchoolBySchoolId(params);
        Integer examSchoolStatus = MapUtils.getInteger(examSchool, "examSchoolStatus");

        // 获取考试学生列表-总分
        params.put("paperId", 0);
        List<Map<String,Object>> studentClassInfo = MapUtil.getListMap(examStudentTagService.getStudent(params), "student");
        // 获取考试学生列表-单科
        paperIdList.forEach(paperId->{
            params.put("paperId",paperId);
            List<Map<String,Object>> studentClassInfoByCourse = MapUtil.getListMap(examStudentTagService.getStudent(params), "student");
            studentClassInfo.addAll(studentClassInfoByCourse);
        });
        params.remove("paperId");
        if (CollectionUtils.isEmpty(studentClassInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "没有找到考试学生");
        }

        // 获取基础信息最新的学生班级信息
        List<Map<String,Object>> studentClassInfoList = commonStudentService.getStudentClassInfoList(params);
        Map<Long, Map<String, Object>> stuId2StuClassInfo = studentClassInfoList.stream()
          .collect(Collectors.toMap(a -> MapUtils.getLong(a, "studentId"), a -> a, (v1, v2) -> v1));

        // 判断哪些学生需要更新
        List<Map<String, Object>> studentClassList = new ArrayList<>();
        studentClassInfo.stream().collect(Collectors.groupingBy(x -> MapUtils.getLong(x, "studentId")))
          .forEach((stuId, stuList) -> {
              Map<String, Object> stuClassInfo = stuId2StuClassInfo.get(stuId);
              if (MapUtils.isNotEmpty(stuClassInfo)) {
                  Long classId = MapUtils.getLong(stuClassInfo, "classId");
                  if (stuList.stream().anyMatch(s -> !ObjectUtil.isValueEquals(classId, s.get("classId")))) {
                      studentClassList.add(stuClassInfo);
                  }
              }
          });

        if (CollectionUtils.isEmpty(studentClassList)) {
            return;
        }

        // 保存初始的学生班级信息
        ((ExamStudentService) AopContext.currentProxy()).insertExamStudentRestore(params);

        List<Map<String, Object>> classInfoList = new ArrayList<>();
        List<Map<String, Object>> classPaperInfoList = new ArrayList<>();
        studentClassList.stream().collect(groupingBy(a -> a.get("classId")))
          .forEach((classId,studentInfo)->{
            Map<String, Object> classInfoMap = MapUtil.copy(studentInfo.get(0), "classId", "className");
            classInfoMap.put("classStatus", examSchoolStatus);
            classInfoList.add(classInfoMap);

            paperIdList.forEach(paperId->{
                Map<String, Object> paperClassInfo = new HashMap<>(classInfoMap);
                paperClassInfo.put("paperId", paperId);
                paperClassInfo.put("examClassPaperStatus", paperId2Status.get(paperId));
                classPaperInfoList.add(paperClassInfo);
            });

            params.putAll(classInfoMap);
            params.put("studentIdList", studentInfo.stream().map(a -> a.get("studentId")).collect(Collectors.toList()));
            params.put("paperIdList", paperIdList);

            // 总分-t_exam_student
            commonRepository.update("ExamStudentMapper.updateExamStudentInfoBatch", params);
            // 单科-t_exam_item
            commonRepository.update("ExamStudentMapper.updateExamStudentItemBatch",params);
            // 单科-t_exam_result
            commonRepository.update("ExamResultMapper.updateExamStudentResultBatch",params);
        });


        // 总分-t_exam_class 插入新增的班级
        Set<Long> oldClassIdSet = new HashSet<>(commonRepository.selectList("ExamClassMapper.getExamClassIdList", params));
        List<Map<String, Object>> classInfo = classInfoList.stream()
                .filter(a -> !oldClassIdSet.contains(Long.parseLong(a.get("classId").toString()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(classInfo)) {
            fillClassArtsScience(classInfo);
            params.put("classInfo", classInfo);
            commonRepository.insert("ExamClassMapper.insertExamClassByExamBatch", params);
        }

        // 总分-删除被剔除的班级
        List<Long> classIds = commonRepository.selectList("ExamStudentMapper.getExamClassInfo", params);
        if (CollectionUtils.isNotEmpty(classIds)) {
            params.put("classIdSet", classIds);
            commonRepository.delete("ExamClassMapper.deleteExamClassByClassId", params);
        }

        // 单科-t_exam_class_paper
        if (CollectionUtils.isNotEmpty(classPaperInfoList)) {
            fillClassArtsScience(classPaperInfoList);
            params.put("classPaperInfo", classPaperInfoList);
            commonRepository.insert("ExamClassPaperMapper.insertExamClassPaperBatch",params);
        }

        // 删除没有result的classPaper
        commonRepository.delete("ExamClassPaperMapper.deleteExamClassPaperByStuResult", params);

        //重置原始报告配置
        examConfigService.resetExamConfigInfo(params);
    }


    /**
    * @Description: 保存初始的学生班级信息
    */
    @Transactional(ExamRepository.TRANSACTION)
    public void insertExamStudentRestore(Map<String, Object> params) {
        Long examStudentId = commonRepository.selectOne("ExamStudentMapper.getOneExamSchStuRestoreId", params);
        if (examStudentId == null) {
            // 获取初始考试学生班级信息
            List<Map<String,Object>> getExamStudent = commonRepository.selectList("ExamStudentMapper.getOriginExamStudent", params);
            List<Map<String,Object>> getExamResult = commonRepository.selectList("ExamStudentMapper.getOriginExamResult", params);

            params.put("examStudentList", getExamStudent);
            commonRepository.insert("ExamStudentMapper.insertExamStudentRestore", params);
            params.put("examResultList", getExamResult);
            commonRepository.insert("ExamStudentMapper.insertExamResultRestore", params);
        }
    }


    /**
    * @Description: 重置考试学生班级
    * @Param:
    */
    @Transactional(ExamRepository.TRANSACTION)
    public void resetExamStudentClass(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .isValidId("gradeId")
                .verify();

        // 获取初始的学生班级信息
        List<Map<String,Object>> examStudentRestore = commonRepository.selectList("ExamStudentMapper.getExamStudentRestore", params);
        List<Map<String,Object>> examResultRestore = commonRepository.selectList("ExamStudentMapper.getExamResultRestore", params);
        if (CollectionUtils.isEmpty(examStudentRestore)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "暂无备份数据");
        }
        //t_exam_student有备份数据，但t_exam_result没有备份数据，那应该是出问题了
        if (CollectionUtils.isEmpty(examResultRestore)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "备份数据异常，请联系管理员处理");
        }

        updateExamSchoolStudentClassInfo(params, examStudentRestore, examResultRestore);
    }

    public String getClassByExcel(Map<String, Object> params) {
        List<Map<String,Object>> list = (List<Map<String, Object>>) examStudentTagService.getStudent(params).get("student");
        list.forEach(a->{
            a.put("courseName", params.get("courseName"));
            a.put("paperName", params.get("paperName"));
        });

        // 文件生成
        SimpleExcelHeader headers = new SimpleExcelHeader(
                Arrays.asList("schoolName", "studentName", "studentNum", "courseName", "paperName", "className", "newClassName"),
                Arrays.asList("学校", "姓名", "学号", "课程", "试卷名称", "原班级名称", "新班级名称")
        );
        ExcelReport report = new SimpleExcelReport(list, headers);

       return report.exportToFileStorage("修改考试学生班级模板");
    }

    @Transactional(ExamRepository.TRANSACTION)
    public Map<String,Object> updateClassByExcel(Map<String,Object> params, List<Map<String, String>> body) {

        // 获取考试的试卷信息
        List<Map<String,Object>> examPaperInfo = commonRepository.selectList("ExamPaperMapper.getExamPaper", params);
        Map<String, Long> paperName2PaperId = examPaperInfo.stream()
          .collect(Collectors.toMap(a -> MapUtil.getString(a, "paperName"), a -> MapUtils.getLong(a, "paperId"), (v1, v2) -> v1));

        // 获取考试学生信息
        List<Map<String,Object>> examStudentInfo = commonRepository.selectList("ExamStudentMapper.getExamStudentInfo", params);
        Map<String, Long> stuNum2StuId = examStudentInfo.stream()
          .collect(toMap(a -> MapUtils.getString(a, "studentNum"), a -> MapUtils.getLong(a, "studentId")));

        // 获取学校班级信息
        List<Map<String,Object>> studentClassInfoList = commonStudentService.getStudentClassInfoList(params);
        Map<String, Long> className2ClassId = studentClassInfoList.stream()
          .collect(Collectors.toMap(a -> MapUtils.getString(a, "className"), a -> MapUtils.getLong(a, "classId"), (v1, v2) -> v1));

        List<String> validator = new ArrayList<>();
        Map<String, Object> rs = new HashMap<>();
        List<Map<String, Object>> stuTotalClass = new ArrayList<>();
        List<Map<String, Object>> stuPaperClass = new ArrayList<>();
        body.forEach(a->{
            Map<String, Object> paramsTemp = new HashMap<>(params);
            String rowNum = a.get("rowNum");

            if ("总分".equals(a.get("paperName"))) {
                stuTotalClass.add(paramsTemp);
            } else {
                Long paperId = paperName2PaperId.get(a.get("paperName"));
                if (paperId == null) {
                    validator.add("第" + rowNum + "行，试卷名称错误");
                } else {
                    paramsTemp.put("paperId", paperId);
                    stuPaperClass.add(paramsTemp);
                }
            }

            Long newClassId = className2ClassId.get(a.get("newClassName"));
            if (newClassId == null) {
                validator.add("第" + rowNum + "行，班级不存在");
            } else {
                paramsTemp.put("classId", newClassId);
                paramsTemp.put("className", a.get("newClassName"));
            }

            Long studentId = stuNum2StuId.get(a.get("studentNum"));
            if (studentId == null) {
                validator.add("第" + rowNum + "行，学生不存在");
            } else {
                paramsTemp.put("studentId", studentId);
            }

            if (CollectionUtils.isNotEmpty(validator)) {
                rs.put("validator", validator);
            }

            paramsTemp.putAll(a);
        });

        // 校验不通过导入失败
        if (CollectionUtils.isNotEmpty(validator)) {
            rs.put("validator", validator);
            return rs;
        }

        updateExamSchoolStudentClassInfo(params, stuTotalClass, stuPaperClass);

        return rs;
    }


    /**
     * 查询考试的科目信息
     */
    public List<Map<String, Object>> getExamCourse(Map<String, Object> params) {
        return commonRepository.selectList("ExamResultMapper.getExamCourse", params);
    }

    /*
    *
    * 获取缺考学生信息
    */
    public List<Map<String,Object>> getAbsentStudent(Map<String,Object> params){
        return commonRepository.selectList("ExamResultMapper.getAbsentStudent",params);
    }

    /**
     * 更新学生在考试中总分、各课程的班级信息
     * @param params examId、schoolId、gradeId、用户信息等
     * @param newStuTotalClass 学生的新的班级-总分
     * @param newStuPaperClass 学生的新的班级-各课程试卷
     */
    private void updateExamSchoolStudentClassInfo(Map<String, Object> params, List<Map<String, Object>> newStuTotalClass,
      List<Map<String, Object>> newStuPaperClass) {

        //将学校试卷的状态作为班级试卷的状态来填充
        List<Map<String, Object>> examSchoolPaper = examSchoolPaperService.getExamSchoolPaperByExamIdAndSchoolId(params);
        Map<Long, Integer> paperId2Status = examSchoolPaper.stream()
          .collect(toMap(x -> MapUtils.getLong(x, "paperId"), x -> MapUtils.getInteger(x, "examSchoolPaperStatus")));

        // 获取考试学生列表-总分
        params.put("paperId", 0);
        List<Map<String,Object>> studentClassInfo = MapUtil.getListMap(examStudentTagService.getStudent(params), "student");
        // 获取考试学生列表-单科
        List<Map<String, Object>> studentCourseClassInfo = new ArrayList<>();
        paperId2Status.keySet().forEach(paperId->{
            params.put("paperId",paperId);
            List<Map<String,Object>> studentClassInfoByCourse = MapUtil.getListMap(examStudentTagService.getStudent(params), "student");
            studentCourseClassInfo.addAll(studentClassInfoByCourse);
        });
        params.remove("paperId");
        if (CollectionUtils.isEmpty(studentClassInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "没有找到考试学生");
        }

        Map<Long, Long> stuId2CurrClassId = studentClassInfo.stream()
          .collect(toMap(x -> MapUtils.getLong(x, "studentId"), x -> MapUtils.getLong(x, "classId")));
        Map<Long, Map<Long, Long>> stuId2PaperId2CurrClassId = studentCourseClassInfo.stream().collect(
          groupingBy(x -> MapUtils.getLong(x, "studentId"),
            toMap(x -> MapUtils.getLong(x, "paperId"), x -> MapUtils.getLong(x, "classId"), (v1, v2) -> v1)));

        //将学校的状态作为班级的状态来填充
        Map<String, Object> examSchool = examSchoolService.getExamSchoolBySchoolId(params);
        Integer examSchoolStatus = MapUtils.getInteger(examSchool, "examSchoolStatus");

        // 判断哪些学生需要更新-总分
        List<Map<String, Object>> studentClassList = newStuTotalClass.stream().filter(x -> {
            Long stuId = MapUtils.getLong(x, "studentId");
            Long currClassId = stuId2CurrClassId.get(stuId);
            return currClassId != null && !ObjectUtil.isValueEquals(currClassId, x.get("classId"));
        }).collect(toList());

        // 判断哪些学生需要更新-单科
        List<Map<String, Object>> studentCourseClassList = newStuPaperClass.stream().filter(x -> {
            Long stuId = MapUtils.getLong(x, "studentId");
            if (stuId2PaperId2CurrClassId.containsKey(stuId)) {
                Long paperId = MapUtils.getLong(x, "paperId");
                Long currClassId = stuId2PaperId2CurrClassId.get(stuId).get(paperId);
                return currClassId != null && !ObjectUtil.isValueEquals(currClassId, x.get("classId"));
            }
            return false;
        }).collect(toList());

        if (CollectionUtils.isEmpty(studentClassList) && CollectionUtils.isEmpty(studentCourseClassList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "没有可更新的学生");
        }

        // 保存初始的学生班级信息
        ((ExamStudentService) AopContext.currentProxy()).insertExamStudentRestore(params);

        // 更新总分
        Map<Object, List<Map<String, Object>>> studentClassMap = studentClassList.stream().collect(groupingBy(a -> a.get("classId")));
        List<Map<String, Object>> classInfoList = new ArrayList<>();
        studentClassMap.forEach((classId,studentInfo)->{
            Map<String, Object> classInfoMap = MapUtil.copy(studentInfo.get(0), "classId", "className");
            classInfoMap.put("classStatus", examSchoolStatus);
            classInfoList.add(classInfoMap);

            params.putAll(classInfoMap);
            params.put("studentIdList", studentInfo.stream().map(a -> a.get("studentId")).collect(Collectors.toList()));

            // 总分-t_exam_student
            commonRepository.update("ExamStudentMapper.updateExamStudentInfoBatch", params);
        });

        // 总分-t_exam_class 插入新增的班级
        Set<Long> oldClassIdSet = new HashSet<>(commonRepository.selectList("ExamClassMapper.getExamClassIdList", params));
        List<Map<String, Object>> classInfo = classInfoList.stream().filter(a -> !oldClassIdSet.contains(MapUtils.getLong(a, "classId")))
          .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(classInfo)) {
            fillClassArtsScience(classInfo);
            params.put("classInfo", classInfo);
            commonRepository.insert("ExamClassMapper.insertExamClassByExamBatch", params);
        }

        // 总分-删除被剔除的班级
        Set<Long> classIdSet = new HashSet<>(commonRepository.selectList("ExamStudentMapper.getExamClassInfo", params));
        if (CollectionUtils.isNotEmpty(classIdSet)) {
            params.put("classIdSet", classIdSet);
            commonRepository.delete("ExamClassMapper.deleteExamClassByClassId", params);
        }

        // 更新单科
        List<Map<String, Object>> classPaperInfoList = new ArrayList<>();
        studentCourseClassList.stream().collect(Collectors.groupingBy(c -> MapUtils.getLong(c, "classId")))
          .forEach((classId, paperStuList) -> {
              Map<String, Object> classInfoMap = MapUtil.copy(paperStuList.get(0), "classId", "className");
              paperStuList.stream().collect(Collectors.groupingBy(p -> MapUtils.getLong(p, "paperId")))
                .forEach((paperId, stuList) -> {
                    Map<String, Object> paperClassInfo = new HashMap<>(classInfoMap);
                    paperClassInfo.put("paperId", paperId);
                    paperClassInfo.put("examClassPaperStatus", paperId2Status.get(paperId));
                    classPaperInfoList.add(paperClassInfo);

                    params.putAll(classInfoMap);
                    params.put("studentIdList", stuList.stream().map(s -> MapUtils.getLong(s, "studentId")).collect(Collectors.toList()));
                    params.put("paperIdList", Collections.singletonList(paperId));

                    // 单科-t_exam_item
                    commonRepository.update("ExamStudentMapper.updateExamStudentItemBatch", params);
                    // 单科-t_exam_result
                    commonRepository.update("ExamResultMapper.updateExamStudentResultBatch", params);
                });
          });

        // 单科-t_exam_class_paper
        if (CollectionUtils.isNotEmpty(classPaperInfoList)) {
            fillClassArtsScience(classPaperInfoList);
            params.put("classPaperInfo", classPaperInfoList);
            commonRepository.insert("ExamClassPaperMapper.insertExamClassPaperBatch",params);
        }

        // 删除没有result的t_exam_class_paper
        commonRepository.delete("ExamClassPaperMapper.deleteExamClassPaperByStuResult", params);

        //重置原始报告配置
        examConfigService.resetExamConfigInfo(params);
    }

    /**
     * 给班级数据填充文理属性（文理属性从基础数据中获取）
     * @param data 需要填充文理属性的班级数据列表
     */
    private void fillClassArtsScience(List<Map<String, Object>> data){
        List<ClassInfoDTO> classInfos = commonClassService.getClassInfoByIds(
          data.stream().map(x -> MapUtils.getLong(x, "classIds")).distinct().collect(toList()));
        Map<Long, Integer> classId2ArtsScience = classInfos.stream().collect(toMap(ClassInfoDTO::getClassId, ClassInfoDTO::getArtsScience));
        data.forEach(x -> x.put("artsScience", classId2ArtsScience.getOrDefault(MapUtils.getLong(x, "classId"), 0)));
    }

    /**
     * 获取学生参与的区域考试
     * @param params studentId examType
     * @return 区域考试
     */
    public List<Long> getStudentAreaExam(Map<String,Object> params){
        return commonRepository.selectList("ExamResultMapper.getStudentAreaExam",params);
    }

    /**
     * 查询学生考试列表，有筛选项，PC专用
     */
    public Map<String,Object> getStudentExamList(Map<String,Object> params) {
        return getStudentExam(params);
    }

    /**
     * 是否已经公布考试的报告
     * @param params examId statId schoolId correctMode
     * @return true 已经公布， false 暂未公布
     */
    public boolean isExamStatPublished(Map<String,Object> params){

        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .isValidId("schoolId")
                .isValidId("classId")
                .isNumeric("correctMode")
                .verify();

        //线下作业单独判断 线下作业只有作业是已公布状态才可以看
        if (examService.checkExamTypeOffline(params)) {
            return DictUtil.isEquals(MapUtils.getInteger(examService.getExamDetail(params), "examStatus"), "examStatus", "published");
        }

        int correctMode = Integer.parseInt(params.get("correctMode").toString());
        int readByClass = DictUtil.getDictValue("correctMode","readByClass");
        int readComplete = DictUtil.getDictValue("examPaperStatus","readComplete");
        params.put("readComplete", readComplete);
        params.put("classStatus", readComplete);

        if(correctMode == readByClass){
            Map<String,Object> rs = commonRepository.selectOne("ExamResultMapper.getExamStatPublishByClass",params);
            if(MapUtils.isEmpty(rs)){
                return false;
            }
            if(Integer.parseInt(rs.get("classStatus").toString()) == readComplete){
                return true;
            }else if(Integer.parseInt(rs.get("homeworkType").toString()) == DictUtil.getDictValue("homeworkType","auto")){
                return true;
            }
            return false;
        }else {
            Map<String,Object> rs = commonRepository.selectOne("ExamResultMapper.getExamStatPublishByQuestion",params);
            if(rs == null || MapUtils.isEmpty(rs)){
                return false;
            }
            return Integer.parseInt(rs.get("deleted").toString()) == 0
                    && Integer.parseInt(rs.get("statStatus").toString()) == 1
                    && Integer.parseInt(rs.get("isDisplay").toString()) == 1;
        }
    }

    /**
     * 批量插入t_exam_student examStudentList中每个item包含t_exam_student所有字段
     *
     * TODO t_exam_result_restore 不知道有何作用
     *
     * @param params examStudentList
     */
    public void insertExamStudentForScoreImport(Map<String, Object> params) {

        Verify.of(params)
                .isNotEmptyCollections("examStudentList")
                .verify();

        commonRepository.batchInsert("ExamStudentMapper.insertExamStudentForScoreImport",
                MapUtil.of("list", params.get("examStudentList")));
    }

    /**
     * 搜索学生
     *   前端要做下拉选择
     *   由于数据可能会过大，多以，studentName studentNum 必须提供一个，都不提供返回空数组
     * @param params examId schoolId (studentName studentNum 二选一 模糊搜索)
     * @return studentId studentName studentNum classId className
     */
    public List<Map<String, Object>> getStudentListForDeclare(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .verify();
        String studentName = Optional.ofNullable(MapUtil.getStringNullable(params, "studentName"))
                .map(item -> ObjectUtil.isBlank(item) ? null : item.trim())
                .orElse(null);
        String studentNum = Optional.ofNullable(MapUtil.getStringNullable(params, "studentNum"))
                .map(item -> ObjectUtil.isBlank(item) ? null : item.trim())
                .orElse(null);
        if (studentName == null && studentNum == null) {
            return new ArrayList<>();
        }

        if (studentName == null) {
            params.remove("studentName");
        } else {
            params.put("studentName", studentName);
        }

        if (studentNum == null) {
            params.remove("studentNum");
        } else {
            params.put("studentNum", studentNum);
        }

        return commonRepository.selectList("ExamStudentMapper.getStudentListForDeclare", params);
    }

    /**
     * 缺考申报审核通过后，对申报的学生科目进行更新状态为缺考
     *     因为正常的学生也可以设为缺考
     * @param params absentDeclarationId
     * @return 更新的数量
     */
    public int updateStudentAbsentForDeclare(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("absentDeclarationId")
                .verify();
        int resultStatusAttend = DictUtil.getDictValue("resultStatus", "attend");
        int resultStatusAbsent = DictUtil.getDictValue("resultStatus", "absent");
        params.put("resultStatusAttend", resultStatusAttend);
        params.put("resultStatusAbsent", resultStatusAbsent);
        ParamsUtil.setCurrentTimeIfAbsent(params);
        return commonRepository.update("ExamStudentMapper.updateStudentAbsentForDeclare", params);
    }

    /**
     * 获取考试-学校维度的学生
     * @param params examId schoolId
     * @return
     */
    public List<Map<String, Object>> getStudentListForWrongBook(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("examId")
                .isNotBlank("schoolId")
                .isNotEmptyCollections("paperIdList")
                .verify();

        return commonRepository.selectList("ExamStudentMapper.getStudentListForWrongBook", params);
    }

    /**
     * 学生数量
     * @param params examId schoolId paperIdList [search]
     * @return
     */
    public Integer countStudentImportListForWrongBook(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("examId")
                .isNotBlank("schoolId")
                .isNotEmptyCollections("paperIdList")
                .verify();

        return commonRepository.selectOne("ExamStudentMapper.countStudentImportListForWrongBook", params);
    }

    /**
     * 获取考试-学校维度的学生
     * @param params examId schoolId paperIdList [search]
     * @return
     */
    public List<Map<String, Object>> getStudentImportListForWrongBook(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("examId")
                .isNotBlank("schoolId")
                .isNotEmptyCollections("paperIdList")
                .verify();

        return commonRepository.selectList("ExamStudentMapper.getStudentImportListForWrongBook", params);
    }

    /**
     * 获取考试的studentId列表
     * @param params examId [schoolId]
     */
    public List<Long> getExamStudentIdList(Map<String, Object> params) {
        return commonRepository.selectList("ExamStudentMapper.getExamStudentIdList", params);
    }

    /**
     * 获取周期本学生导入->添加学生的数量
     * @param params examPaperParamsList [{examId, paperId, schoolId}]
     * @return
     */
    public Integer countStudentImportListForCycleWrongBook(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("examPaperParamsList")
                .verify();

        return commonRepository.selectOne("ExamStudentMapper.countStudentImportListForCycleWrongBook", params);
    }

    /**
     * 获取周期本学生导入->添加学生的学生列表
     * @param params examPaperParamsList [{examId, paperId, schoolId}]
     * @return
     */
    public List<Map<String, Object>> getStudentImportListForCycleWrongBook(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("examPaperParamsList")
                .verify();

        return commonRepository.selectList("ExamStudentMapper.getStudentImportListForCycleWrongBook", params);
    }

    /**
     * 获取学生考试信息
     * @param params [examId, paperId, schoolId, studentId]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentAddListForCycleWrongBook(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("examStudentParamsList")
                .verify();
        return commonRepository.selectList("ExamStudentMapper.getWrongBookStudentAddListForCycleWrongBook", params);
    }

    /**
     * 同步考生信息
     * 通过基础数据同步考生信息时同步除考号外的其他信息（学号、姓名、标签、选科信息）
     * @param params examId、schoolId(可选参数)
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateExamAllStudentInfo(Map<String, Object> params){
        Verify.of(params)
          .isValidId("examId")
          .verify();

        if (!ObjectUtil.isValidId(params.get("schoolId"))) {
            params.remove("schoolId");
        }
        List<ExamStudentBO> examStudents = commonRepository.selectList("ExamStudentMapper.getExamStudentByExam", params);
        List<StudentUpdateInfoDTO> students =
          commonStudentService.getStudentUpdateInfoByIds(examStudents.stream().map(ExamStudentBO::getStudentId).collect(toList()));
        if (students.isEmpty()) {
            return;
        }

        Set<Long> studentIds = students.stream().map(StudentUpdateInfoDTO::getStudentId).collect(Collectors.toSet());
        Map<Long, ExamStudentBO> stuId2ExamStudent = examStudents.stream().filter(x -> studentIds.contains(x.getStudentId()))
          .collect(toMap(ExamStudentBO::getStudentId, x -> x));
        List<ExamResultBO> examResults = commonRepository.selectList("ExamStudentMapper.getExamResultByExam", params);
        Map<Long, List<ExamResultBO>> stuId2ExamResults = examResults.stream().filter(x -> studentIds.contains(x.getStudentId()))
          .collect(groupingBy(ExamResultBO::getStudentId));
        params.put("currentTime", DateUtil.getCurrentDateTime());
        Long examId = MapUtils.getLong(params, "examId");

        String fieldsToUpdateStr = MapUtil.getStringNullable(params, "fieldsToUpdate");
        Set<String> fieldsToUpdate = Optional.ofNullable(fieldsToUpdateStr)
                .map(s -> Arrays.stream(s.split(CommonConstant.COMMA)))
                .orElseGet(() -> ExamStuUpdateFieldsEnum.getSupportedFields().stream())
                .collect(Collectors.toSet());

        List<ExamTagBO> examTagS = new ArrayList<>();
        Set<Long> existsTagIds = new HashSet<>();
        List<ExamStudentTagBO> examStudentTags = new ArrayList<>();
        for (StudentUpdateInfoDTO s : students) {
            long studentId = s.getStudentId();
            if (ObjectUtil.isNotBlank(s.getTagId())) {
                List<Long> tagIds = StringUtil.strToList(s.getTagId(), ",", Long.class);
                List<String> tagNames = StringUtil.strToList(s.getTagName(), ",", String.class);
                long schoolId = s.getSchoolId();
                for (int i = 0; i < tagIds.size(); i++) {
                    Long tagId = tagIds.get(i);
                    ExamStudentTagBO examStudentTag = new ExamStudentTagBO()
                      .setExamId(examId)
                      .setTagId(tagId)
                      .setStudentId(studentId);
                    if (!existsTagIds.contains(tagId)) {
                        ExamTagBO examTag = new ExamTagBO()
                          .setTagId(tagId)
                          .setTagName(tagNames.get(i))
                          .setExamId(examId)
                          .setSchoolId(schoolId);
                        examTagS.add(examTag);
                        existsTagIds.add(tagId);
                    }
                    examStudentTags.add(examStudentTag);
                }
            }
            String studentName = s.getStudentName();
            String studentNum = s.getStudentNum();
            stuId2ExamStudent.get(studentId)
                    .setStudentName(studentName)
                    .setStudentNum(studentNum)
                    .setCourseSelectionGroupId(s.getCourseSelectionGroupId())
                    .setForeignCourseId(s.getForeignCourseId());
            stuId2ExamResults.getOrDefault(studentId, Collections.emptyList())
                    .forEach(x -> x.setStudentName(studentName)
                            .setStudentNamePinyin(Pinyin4jUtil.chinese2Pinyin(studentName))
                            .setStudentNum(studentNum));
        }

        if (ExamStuUpdateFieldsEnum.withTag(fieldsToUpdate)) {
            updateTag(params, examTagS, examStudentTags);
        }

        if (ExamStuUpdateFieldsEnum.withStudentInfo(fieldsToUpdate)) {
            updateStudentBasicInfo(params, fieldsToUpdate, stuId2ExamStudent, stuId2ExamResults);
        }
    }

    private void updateStudentBasicInfo(Map<String, Object> params, Set<String> fieldsToUpdate,
                                        Map<Long, ExamStudentBO> stuId2ExamStudent,
                                        Map<Long, List<ExamResultBO>> stuId2ExamResults) {
        boolean withName = ExamStuUpdateFieldsEnum.withName(fieldsToUpdate);
        params.put("withName", withName);
        boolean withStudentNum = ExamStuUpdateFieldsEnum.withStudentNum(fieldsToUpdate);
        params.put("withStudentNum", withStudentNum);
        params.put("withForeignLanguage", ExamStuUpdateFieldsEnum.withForeignLanguage(fieldsToUpdate));
        params.put("withElectiveSubjects", ExamStuUpdateFieldsEnum.withElectiveSubjects(fieldsToUpdate));

        // 更新学号、姓名、选科信息、外语类型
        BatchDataUtil.execute(
                new ArrayList<>(stuId2ExamStudent.values()),
                x -> {
                    params.put("examStudent", x);
                    commonRepository.insert("ExamStudentMapper.updateExamStudentInfoByInsert", params);
                }
        );
        params.remove("examStudent");

        // 更新学号、姓名+拼音
        if ((withStudentNum || withName) && !stuId2ExamResults.isEmpty()) {
            BatchDataUtil.execute(
                    stuId2ExamResults.values().stream().flatMap(Collection::stream).collect(toList()),
                    x -> {
                        params.put("examResult", x);
                        commonRepository.insert("ExamStudentMapper.updateExamStudentResultInfoByInsert", params);
                    }
            );
            params.remove("examResult");
        }

        // 更新学号
        if (withStudentNum) {
            commonRepository.update("NewAnswerCardMapper.updateExamAnswerCardStuNum", params);
        }
    }

    private void updateTag(Map<String, Object> params, List<ExamTagBO> examTagS, List<ExamStudentTagBO> examStudentTags) {
        //更新标签
        //标签：来源是基础数据的标签，删除这些标签以及标签对应的学生
        cleanExistingTags(params);
        if (examTagS.isEmpty()) {
           return;
        }
        long examId = MapUtil.getLong(params, "examId");
        Long schoolId = MapUtil.getLongNullable(params, "schoolId");
        List<ExamTagBO> examTagBOS = examTagManager.findByExam(examId)
                .stream().filter(x -> schoolId == null || x.getSchoolId() == schoolId).collect(toList());
        Map<Long, Map<String, Long>> schoolId2TagName2ExamTagId = examTagBOS.stream()
                .collect(groupingBy(ExamTagBO::getSchoolId, toMap(ExamTagBO::getTagName, ExamTagBO::getExamTagId, (x, y) -> x)));

        Map<Long, Long> tagId2ExamTagId = new HashMap<>();
        // 筛选出新增的标签，如果考试已存在和基础数据同名的标签，则不保存
        examTagS = examTagS.stream()
                .filter(x -> {
                    Map<String, Long> tagName2ExamTagId = schoolId2TagName2ExamTagId.getOrDefault(x.getSchoolId(), Collections.emptyMap());
                    if (tagName2ExamTagId.containsKey(x.getTagName())) {
                        tagId2ExamTagId.putIfAbsent(x.getTagId(), tagName2ExamTagId.get(x.getTagName()));
                        return false;
                    }
                    return true;
                })
                .collect(toList());
        params.put("examTagS", examTagS);
        commonRepository.insert("ExamTagMapper.batchInsertExamTag", params);

        examTagS = commonRepository.selectList("ExamTagMapper.getExamTagByTagIds", params);
        tagId2ExamTagId.putAll(examTagS.stream().collect(toMap(ExamTagBO::getTagId, ExamTagBO::getExamTagId)));
        examStudentTags.forEach(x -> x.setExamTagId(tagId2ExamTagId.get(x.getTagId())));
        params.put("examStudentTags", examStudentTags);
        commonRepository.insert("ExamStudentTagMapper.insertExamStudentTag", params);
        //t_exam_tag现有的保存路径：
        //1.正常发布考试：从基础数据中根据学生获取对应的标签保存，此时tagId不为空
        //2.成绩导入
        //3.借读生申报：在base_data-t_tag增量添加数据，在exam-t_exam_tag、t_exam_student_tag中添加数据，此时tagId不为空
        //4.工具箱/诊断管理-考生管理-添加标签
    }

    private void cleanExistingTags(Map<String, Object> params) {
        commonRepository.delete("ExamStudentTagMapper.deleteExamStudentTagByExamTag", params);
        commonRepository.delete("ExamStudentTagMapper.deleteExamTag", params);
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void updateExamStudentInfo(ExamStuUpdateReq request) {
        Set<String> fieldsToUpdate = request.getUpdateFields();
        // 验证请求的字段是否都被支持
        Set<String> supportedFields = ExamStuUpdateFieldsEnum.getSupportedFields();
        for (String field : fieldsToUpdate) {
            if (!supportedFields.contains(field)) {
                throw new IllegalArgumentException("不支持的更新字段: " + field);
            }
        }
        long examId = request.getExamId();
        long schoolId = request.getSchoolId();
        List<ExamSchoolVO> examSchoolVOS = newExamService.listExamSchools(examId).stream()
                .filter(e -> schoolId == 0 || schoolId == e.getSchoolId()).collect(toList());
        RequestVO user = RequestUtil.getUser();
        // 更新班级
        if (ExamStuUpdateFieldsEnum.withClass(fieldsToUpdate)) {
            for (ExamSchoolVO examSchoolVO : examSchoolVOS) {
                long schoolId1 = examSchoolVO.getSchoolId();
                long gradeId = examSchoolVO.getGradeId();
                Map<String, Object> params = MapUtil.of(
                        "examId", examId,
                        "schoolId", schoolId1,
                        "gradeId", gradeId,
                        "userId", user.getUserId(),
                        "userName", user.getUserName()
                );
                this.updateExamStudentClass(params);
            }
        }
        Map<String, Object> params = MapUtil.of(
                "examId", examId,
                "fieldsToUpdate", String.join(CommonConstant.COMMA, fieldsToUpdate),
                "userId", user.getUserId(),
                "userName", user.getUserName()
        );
        if (schoolId != 0) {
            params.put("schoolId", schoolId);
        }
        this.updateExamAllStudentInfo(params);
    }

    public StuUpdateInfoPreviewRes updateExamStudentInfoPreview(ExamStuInfoQueryReq request) {
        Set<String> updateFields = request.getUpdateFields() == null ?
                Collections.emptySet() : Arrays.stream(request.getUpdateFields().split(CommonConstant.COMMA)).collect(toSet());
        if (CollectionUtils.isEmpty(updateFields)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "请先选择需要同步的内容！");
        }
        List<ExamStudentBO> examStudents = getExamStudentByExam(request);
        if (examStudents.isEmpty()) {
            return new StuUpdateInfoPreviewRes();
        }
        List<Long> studentIds = new ArrayList<>();
        Set<Long> courseSelectionGroupIds = new HashSet<>();
        Set<Long> foreignCourseIds = new HashSet<>();
        for (ExamStudentBO examStudent : examStudents) {
            studentIds.add(examStudent.getStudentId());
            if (examStudent.getCourseSelectionGroupId() != null) {
                courseSelectionGroupIds.add(examStudent.getCourseSelectionGroupId());
            }
            if (examStudent.getForeignCourseId() != null) {
                foreignCourseIds.add(examStudent.getForeignCourseId());
            }
        }
        List<StudentUpdateInfoDTO> studentInfos = commonStudentService.getStudentUpdateInfoByIds(studentIds);
        for (StudentUpdateInfoDTO studentInfo : studentInfos) {
            if (studentInfo.getCourseSelectionGroupId() != null) {
                courseSelectionGroupIds.add(studentInfo.getCourseSelectionGroupId());
            }
            if (studentInfo.getForeignCourseId() != null) {
                foreignCourseIds.add(studentInfo.getForeignCourseId());
            }
        }
        // 获取学生信息
        Map<Long, StudentUpdateInfoDTO> studentId2Info = studentInfos.stream()
                .collect(toMap(StudentUpdateInfoDTO::getStudentId, Function.identity(), (v1, v2) -> v1));
        // 获取班级信息
        Map<Long, ClassStudentBO> student2ClassInfo = getStudent2ClassInfo(request.getExamId());
        // 外语类型
        Map<Long, String> fcId2Name = baseDataManageService.getCourseByIDs(new ArrayList<>(foreignCourseIds))
                .stream().collect(Collectors.toMap(Course::getCourseId, Course::getCourseName));
        // 选考科目信息
        Map<Long, String> csgId2Name = courseSelectionGroupService.getCourseSelectionGroupNameByIds(
                new ArrayList<>(courseSelectionGroupIds)).stream().collect(Collectors.toMap(
                CourseSelectionGroupNameDTO::getCourseSelectionGroupId,
                CourseSelectionGroupNameDTO::getCourseSelectionGroupName
        ));

        StuUpdateInfoPreviewRes stuUpdateInfoPreviewRes = new StuUpdateInfoPreviewRes();
        List<StuUpdateInfoDTO> studentChangeInfos = new ArrayList<>();

        for (ExamStudentBO examStudent : examStudents) {
            StuUpdateInfoDTO studentInfoDTO = new StuUpdateInfoDTO();
            long studentId = examStudent.getStudentId();
            studentInfoDTO.setStudentId(studentId);
            studentInfoDTO.setStudentName(examStudent.getStudentName());
            studentInfoDTO.setClassId(examStudent.getClassId());
            studentInfoDTO.setClassName(examStudent.getClassName());
            studentInfoDTO.setStudentExamNum(examStudent.getStudentExamNum());
            List<String> changeInfos = getChangeInfos(
                    examStudent, studentId2Info.get(studentId), student2ClassInfo.get(studentId), fcId2Name, csgId2Name, updateFields
            );
            if (request.isShowChanged() && Collections.singletonList("暂无变更").equals(changeInfos)) {
                continue;
            }
            studentInfoDTO.setChangeInfos(changeInfos);
            studentChangeInfos.add(studentInfoDTO);
        }
        int pageNum = request.getPageNum() <= 0 ? 1 : request.getPageNum();
        int pageSize = request.getPageSize() <= 0 ? 10 : request.getPageSize();
        stuUpdateInfoPreviewRes.setStudentInfos(PageUtil.getPageList(studentChangeInfos, pageSize, (pageNum - 1) * pageSize));
        stuUpdateInfoPreviewRes.setTotalCount(studentChangeInfos.size());
        return stuUpdateInfoPreviewRes;
    }

    private List<String> getChangeInfos(ExamStudentBO examStudent, StudentUpdateInfoDTO studentInfo, ClassStudentBO studentClassInfo,
                                        Map<Long, String> fcId2Name, Map<Long, String> csgId2Name, Set<String> updateFields) {
        if (studentInfo == null || studentClassInfo == null) {
            return Collections.singletonList("已删除");
        }
        List<String> changeInfos = new ArrayList<>();
        if (updateFields.contains(ExamStuUpdateFieldsEnum.NAME.getFieldName()) &&
                !StringUtils.equals(examStudent.getStudentName(), studentInfo.getStudentName())) {
            changeInfos.add("考生姓名：”" + examStudent.getStudentName() + "“变更为”" + studentInfo.getStudentName() + "“");
        }
        if (updateFields.contains(ExamStuUpdateFieldsEnum.CLASS.getFieldName()) &&
                !StringUtils.equals(examStudent.getClassName(), studentClassInfo.getClassName())) {
            changeInfos.add("班级：”" + examStudent.getClassName() + "“变更为”" + studentClassInfo.getClassName() + "“");
        }
        if (updateFields.contains(ExamStuUpdateFieldsEnum.STUDENT_NUM.getFieldName()) &&
                !StringUtils.equals(examStudent.getStudentNum(), studentInfo.getStudentNum())) {
            changeInfos.add("学号：”" + examStudent.getStudentNum() + "“变更为”" + studentInfo.getStudentNum() + "“");
        }
        if (updateFields.contains(ExamStuUpdateFieldsEnum.ELECTIVE_SUBJECTS.getFieldName()) &&
                !ObjectUtils.equals(examStudent.getCourseSelectionGroupId(), studentInfo.getCourseSelectionGroupId())) {
            changeInfos.add("选考科目：”" + csgId2Name.getOrDefault(examStudent.getCourseSelectionGroupId(), "无") +
                    "“变更为”" + csgId2Name.getOrDefault(studentInfo.getCourseSelectionGroupId(), "无") + "“");
        }
        if (updateFields.contains(ExamStuUpdateFieldsEnum.FOREIGN_LANGUAGE.getFieldName()) &&
                !ObjectUtils.equals(examStudent.getForeignCourseId(), studentInfo.getForeignCourseId())) {
            changeInfos.add("外语课程：”" + fcId2Name.getOrDefault(examStudent.getForeignCourseId(), "无") +
                    "“变更为”" + fcId2Name.getOrDefault(studentInfo.getForeignCourseId(), "无") + "“");
        }
        if (updateFields.contains(ExamStuUpdateFieldsEnum.TAG.getFieldName())) {
            Set<String> baseTagNames = getBaseTagNames(studentInfo);
            Set<String> baseTagNamesInExam = getBaseTagNamesInExam(examStudent, baseTagNames);
            if (!baseTagNamesInExam.equals(baseTagNames)) {
                changeInfos.add("标签：”" + String.join("，", baseTagNamesInExam) + "“变更为”" + String.join("，", baseTagNames) + "“");
            }
        }
        return changeInfos.isEmpty() ? Collections.singletonList("暂无变更") : changeInfos;
    }

    private static Set<String> getBaseTagNames(StudentUpdateInfoDTO studentInfo) {
        if (studentInfo.getTagName() == null) {
            return Collections.singleton("无");
        }
        return new HashSet<>(Arrays.asList(studentInfo.getTagName().split(CommonConstant.COMMA)));
    }

    private Set<String> getBaseTagNamesInExam(ExamStudentBO examStudent, Set<String> baseTagNames) {
        if (examStudent.getTagName() == null) {
            return Collections.singleton("无");
        }
        Set<String> examTagNames = new HashSet<>();
        List<String> tagIds = Arrays.asList(examStudent.getTagId().split(CommonConstant.COMMA));
        List<String> tagNames = Arrays.asList(examStudent.getTagName().split(CommonConstant.COMMA));
        for (int i = 0; i < tagIds.size(); i++) {
            String tagId = tagIds.get(i);
            // 只比较从基础数据同步（tagId!=null）的标签
            if (StringUtils.equals(tagId, "null")) {
                // 如果考试标签和基础数据同名，同步的时候不会再把基础数据的同步过来，这时候把考试标签也算作从基础数据同步的
                if (baseTagNames.contains(tagNames.get(i))){
                    examTagNames.add(tagNames.get(i));
                }
                continue;
            }
            examTagNames.add(tagNames.get(i));
        }
        return examTagNames.isEmpty() ? Collections.singleton("无") : examTagNames;
    }

    private Map<Long, ClassStudentBO> getStudent2ClassInfo(long examId) {
        List<ExamSchoolVO> examSchoolVOS = newExamService.listExamSchools(examId);
        Map<Long, ClassStudentBO> student2ClassInfo = new HashMap<>();
        for (ExamSchoolVO examSchoolVO : examSchoolVOS) {
            Map<String, Object> params = MapUtil.of("schoolId", examSchoolVO.getSchoolId(),
                    "gradeId", examSchoolVO.getGradeId());
            List<Map<String, Object>> studentClassInfoList = commonStudentService.getStudentClassInfoList(params);
            for (Map<String, Object> studentClassInfo : studentClassInfoList) {
                ClassStudentBO classStudentBO = new ClassStudentBO();
                classStudentBO.setStudentId(MapUtil.getLong(studentClassInfo, "studentId"));
                classStudentBO.setStudentName(MapUtil.getString(studentClassInfo, "studentName"));
                classStudentBO.setStudentNum(MapUtil.getString(studentClassInfo, "studentNum"));
                classStudentBO.setClassId(MapUtil.getLong(studentClassInfo, "classId"));
                classStudentBO.setClassName(MapUtil.getString(studentClassInfo, "className"));
                student2ClassInfo.put(classStudentBO.getStudentId(), classStudentBO);
            }
        }
        return student2ClassInfo;
    }

    public List<ExamStudentBO> getExamStudentByExam(ExamStuInfoQueryReq request) {
        Map<String, Object> params = MapUtil.of("examId", request.getExamId());
        if (request.getSchoolId() != 0) {
            params.put("schoolId", request.getSchoolId());
        }
        if (request.getClassId() != 0) {
            params.put("classId", request.getClassId());
        }
        if (request.getExamTagId() != null && !request.getExamTagId().isEmpty()) {
            Set<Long> examTagIdList = Arrays.stream(request.getExamTagId().split(","))
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .map(MapUtil::getLong)
                    .collect(Collectors.toSet());
            params.put("examTagIdList", examTagIdList);
        }
        if (request.getSearch() != null && !request.getSearch().isEmpty()) {
            params.put("search", request.getSearch());
        }
        boolean useMultiSchoolScanType = examService.useMultiSchoolScanType(params);
        params.put("useMultiSchoolScanType", useMultiSchoolScanType);
        List<Map<String, Object>> examStudents = commonRepository.selectList("ExamStudentTagMapper.getStudent", params);
        return examStudents.stream().map(x -> new ExamStudentBO()
                .setSchoolId(MapUtil.getLong(x, "schoolId"))
                .setClassId(MapUtil.getLong(x, "classId"))
                .setClassName(MapUtil.getString(x, "className"))
                .setStudentId(MapUtil.getLong(x, "studentId"))
                .setStudentName(MapUtil.getString(x, "studentName"))
                .setStudentNum(MapUtil.getString(x, "studentNum"))
                .setStudentExamNum(MapUtil.getStringNullable(x, "studentExamNum"))
                .setCourseSelectionGroupId(MapUtil.getLongNullable(x, "courseSelectionGroupId"))
                .setForeignCourseId(MapUtil.getLongNullable(x, "foreignCourseId"))
                .setExamTagId(MapUtil.getStringNullable(x, "examTagId"))
                .setTagId(MapUtil.getStringNullable(x, "tagId"))
                .setTagName(MapUtil.getStringNullable(x, "tagName"))
        ).collect(toList());
    }
    /**
     * 更新学生考号时，下载模板（这个模板只能用来复制数据到在线excel，无法用来导入）
     * @return 模板的url
     */
    public String getStudentExamNumUpdateTemplate() {
        //表头：学校、考生姓名、考号、班级、学号
        SimpleExcelHeader headers = new SimpleExcelHeader(
          Arrays.asList("schoolName", "studentName", "studentExamNum", "className", "studentNum"),
          Arrays.asList("学校", "考生姓名（必填）", "考号（必填）", "班级", "学号"));
        SimpleDoubleHeaderExcelReport report = new SimpleDoubleHeaderExcelReport(Collections.emptyList(), headers, "填写说明：\n" +
          "（1）请填写本次考试参考考生信息: 更新时考生姓名、考号为必填项；一行仅支持录入一个考生信息；\n" +
          "（2）若学校内考生姓名重复时请填写班级名称，班级名称需与考试中的班级名称一致；若班级内考生姓名重复，请填写学号区分；\n" +
          "（3）若填写的姓名和班级不一致，无法更新考生信息，更新时优先匹配学号、姓名+班级、姓名；\n" +
          "（4）考号：请录入本次参考学生的考号，数字位数限制为4-32位之间，校内考号不允许重复；", 4, null, ExcelVersionEnum.V_2007);

        return report.exportToFileStorage("更新参考考号模板");
    }

    /**
     * 修正参考考号  暂时用不到，代码先留着
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void importStudentExamNumUpdateTemplate(Map<String, Object> params, MultipartFile importFil){

        //先校验数据


        //更新考试数据
        List<StudentExamNumUpdateDTO> students = new ArrayList<>();

        // 需要更细的数据
        //更新t_exam_item的student_id改成正确的（这里会改成正确的stuId的负数，防止出现唯一约束冲突）
        commonRepository.batchUpdate("ExamStudentMapper.updateItemStuIdByStuExamNum", students);
        //將t_exam_item的student_id再改回正数
        commonRepository.update("ExamStudentMapper.updateItemStuIdForNegative", params);
        //更新answer_card的student_id
        commonRepository.batchUpdate("ExamStudentMapper.updateAnswerCardStuInfoByStuExamNum", students);
        //更新t_exam_result的student_exam_num
        //现在学生各科的考号都一样，但考虑到以后可能会有学生各科考号不一样的情况，就将考号存在t_exam_result中而不是t_exam_student中了）
        commonRepository.update("ExamStudentMapper.updateResultStuExamNumById", students);
    }

    /**
     * 更新参考考号
     */
    @Transactional(ExamRepository.TRANSACTION)
    public CheckExcelResponse importStudentExamNumUpdateTemplate(StuExamNumUpdateExcelReq req) {
        List<Excel> excelDataList = req.getExcelData();
        Long examId = req.getExamId();
        if (!ObjectUtil.isValidId(examId) || CollectionUtils.isEmpty(excelDataList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        if (!ObjectUtil.isValidId(req.getSchoolId())) {
            req.setSchoolId(null);
        }
        boolean isSchoolExam = !examService.checkExamTypeUnion(MapUtil.of("examId", examId));
        String cellSchoolName = null;
        if (isSchoolExam) {
            List<Map<String, Object>> examSchoolList = examSchoolService.getExamSchool(MapUtil.of("examId", examId));
            if (examSchoolList.size() != 1) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "诊断学校数据异常，请联系管理员处理！");
            }
            cellSchoolName = MapUtils.getString(examSchoolList.get(0), "schoolName");
        }
        //返回数据
        CheckExcelResponse studentExcelResponse = new CheckExcelResponse();
        //异常数据列表
        List<ErrCell> errCells = new ArrayList<>();
        studentExcelResponse.setErrCells(errCells);
        //异常数据中的重复数据
        Map<Integer, Map<String, List<RepeatCell>>> repeatInfo = new HashMap<>();
        studentExcelResponse.setRepeatInfo(repeatInfo);
        //相似考生列表
        List<SimilarStudent> similarStuList = new ArrayList<>();
        studentExcelResponse.setSimilarStudentList(similarStuList);
        //不存在考生列表
        List<NotFondStudent> notFoundStuList = new ArrayList<>();
        studentExcelResponse.setNotFoundStudentList(notFoundStuList);

        //存放通过校验的学生信息列表
        List<ExamStudentInfoDTO> passedStudentList = new ArrayList<>();

        //从t_exam_student中获取学生数据
        List<ExamStudentInfoDTO> students = commonRepository.selectList("ExamStudentMapper.getExamStuInfoForUpdateStuExamNum", req);
        //学校id - 学生num - 学生信息
        Map<Long, Map<String, List<ExamStudentInfoDTO>>> stuSch2StuNum2Stu = new HashMap<>();
        //学校name - 学校id
        Map<String, Long> schName2SchId = new HashMap<>();
        //学校id - 学生name - 学生列表
        Map<Long, Map<String, List<ExamStudentInfoDTO>>> stuSch2StuName2StuList = new HashMap<>();
        students.stream().collect(Collectors.groupingBy(ExamStudentInfoDTO::getSchoolId)).forEach((schoolId, schStuList) -> {
            String schoolName = schStuList.get(0).getSchoolName();
            Map<String, List<ExamStudentInfoDTO>> stuNum2Stu = new HashMap<>();
            Map<String, List<ExamStudentInfoDTO>> stuName2StuList = new HashMap<>();
            for (ExamStudentInfoDTO stu : schStuList) {
                //学号 - 学生信息
                stuNum2Stu.computeIfAbsent(stu.getStudentNum(), k -> new ArrayList<>()).add(stu);
                //姓名 - 学生列表
                stuName2StuList.computeIfAbsent(stu.getStudentName(), k -> new ArrayList<>()).add(stu);
            }
            schName2SchId.put(schoolName, schoolId);
            stuSch2StuNum2Stu.put(schoolId, stuNum2Stu);
            stuSch2StuName2StuList.put(schoolId, stuName2StuList);
        });

        //前端传参
        Map<Integer, ExamStudentInfoDTO> rowExcelDataMap = new HashMap<>();
        //需要忽略的行号
        Set<Integer> ignoreRowSet = new HashSet<>();
        for (Excel excel : excelDataList) {
            ExamStudentInfoDTO examStudentInfoDTO = rowExcelDataMap.computeIfAbsent(excel.getR(), k -> {
                ExamStudentInfoDTO student = new ExamStudentInfoDTO();
                student.setRow(k);
                return student;
            });
            setStudent(excel, examStudentInfoDTO, ignoreRowSet);
        }

        //schoolId -> 考号 -> 第一次出现时在的行数，用来判断考号在excel中是否有重复
        Map<Long, Map<String, Integer>> schoolId2StuExamNum2Row = new HashMap<>();
        //schoolId -> 考号 -> 在execl中的行号List
        Map<Long, Map<String, List<Integer>>> schoolId2StuExamNum2RowList = new HashMap<>();

        //schoolId -> 学号 -> 第一次出现时在的行数，用来判断学号在excel中的单个学校中是否有重复
        Map<Long, Map<String, Integer>> schoolId2StuNum2Row = new HashMap<>();
        //schoolId -> 学号 -> 在execl中的行号List
        Map<Long, Map<String, List<Integer>>> schoolId2StuNum2RowList = new HashMap<>();

        //已经确定了行号的studentId -> 行号
        Map<Long, Integer> stuId2Row = new HashMap<>();
        //studentId -> 在execl中的行号List
        Map<Long, List<Integer>> stuId2RowList = new HashMap<>();

        boolean havingErrCell = false;
        //一行一行遍历
        for (Entry<Integer, ExamStudentInfoDTO> entry : rowExcelDataMap.entrySet()) {
            int row = entry.getKey();
            if (ignoreRowSet.contains(row)) {
                //需要忽略的行
                continue;
            }
            ExamStudentInfoDTO student = entry.getValue();
            List<ErrCell> rowErrCells = new ArrayList<>();

            String schName = isSchoolExam ? cellSchoolName : student.getSchoolName();
            String stuName = student.getStudentName();
            String stuExamNum = student.getStudentExamNum();
            String className = student.getClassName();
            String stuNum = student.getStudentNum();
            Long schoolId = null;

            //学校列不能为空
            if (StringUtils.isBlank(schName)) {
                rowErrCells.add(builderCellErr(row, SCH_NAME_IDX, "异常数据，学校列未填写"));
            } else if (!schName2SchId.containsKey(schName)) {
                rowErrCells.add(builderCellErr(row, SCH_NAME_IDX, "异常数据，填写的学校不在诊断范围中"));
            } else {
                schoolId = schName2SchId.get(schName);
            }

            //学生姓名列不能为空
            if (StringUtils.isBlank(stuName)) {
                rowErrCells.add(builderCellErr(row, STU_NAME_IDX, "异常数据，考生姓名列未填写"));
            }

            //考号列不能为空
            if (StringUtils.isBlank(stuExamNum)) {
                rowErrCells.add(builderCellErr(row, STU_EXAM_NUM_IDX, "异常数据，考号列未填写"));
            } else if (!stuExamNum.matches("\\d{4,32}")) {
                rowErrCells.add(builderCellErr(row, STU_EXAM_NUM_IDX, "异常数据，考号格式不正确"));
            } else if (schoolId != null) {
                //考号格式没问题，再校验考号在excel中的单个学校中是否重复了
                Map<String, Integer> schoolStuExamNum2Row = schoolId2StuExamNum2Row.computeIfAbsent(schoolId, k -> new HashMap<>());
                if (schoolStuExamNum2Row.containsKey(stuExamNum)) {
                    schoolId2StuExamNum2RowList.computeIfAbsent(schoolId, k -> new HashMap<>())
                      .computeIfAbsent(stuNum, k -> new ArrayList<>())
                      .add(row);
                    havingErrCell = true;
                } else {
                    schoolStuExamNum2Row.put(stuExamNum, row);
                    schoolId2StuExamNum2Row.put(schoolId, schoolStuExamNum2Row);
                }
            }

            //学号列可以为空，但不为空时，在excel的单个学校内不能重复
            if (StringUtils.isNotBlank(stuNum) && schoolId != null) {
                Map<String, Integer> schoolStuNum2Row = schoolId2StuNum2Row.computeIfAbsent(schoolId, k -> new HashMap<>());
                if (schoolStuNum2Row.containsKey(stuNum)) {
                    schoolId2StuNum2RowList.computeIfAbsent(schoolId, k -> new HashMap<>())
                        .computeIfAbsent(stuNum, k -> new ArrayList<>())
                        .add(row);
                    havingErrCell = true;
                } else {
                    schoolStuNum2Row.put(stuNum, row);
                    schoolId2StuNum2Row.put(schoolId, schoolStuNum2Row);
                }
            }

            if (!rowErrCells.isEmpty()) {
                errCells.addAll(rowErrCells);
                havingErrCell = true;
                continue;
            }

            //存在异常的数据时，不再进行后续的相似考生、不存在考生的校验，因为就算校验了也展示不了
            if (havingErrCell) {
                continue;
            }

            //是否有异常数据的校验完成，进行后续的相似考生、不存在考生的校验
            List<ExamStudentInfoDTO> studentInfo;
            boolean matchByStuNum = true;
            if (StringUtils.isNotBlank(stuNum)) {
                //填写了学号时，根据学校+学号确定学生
                studentInfo = stuSch2StuNum2Stu.getOrDefault(schoolId, Collections.emptyMap()).get(stuNum);
            } else {
                //没填写学号时，根据学校+班级+姓名确定学生
                studentInfo = stuSch2StuName2StuList.getOrDefault(schoolId, Collections.emptyMap()).get(stuName);
                matchByStuNum = false;
            }
            if (CollectionUtils.isEmpty(studentInfo)) {
                //考生不存在
                notFoundStuList.add(new NotFondStudent().createByCopy(student, row, STU_NOT_EXIST_MSG));
                continue;
            }

            ExamStudentInfoDTO passedStudent;
            if (studentInfo.size() == 1) {
                //匹配到了一个考生
                ExamStudentInfoDTO examStudentInfo = studentInfo.get(0);
                if (matchByStuNum) {
                    if ((StringUtils.isNotBlank(className) && !ObjectUtil.isValueEquals(examStudentInfo.getClassName(), className)) ||
                        !ObjectUtil.isValueEquals(examStudentInfo.getStudentName(), stuName)) {
                        //填写的班级或姓名信息与考生在诊断管理处的不一致
                        similarStuList.add(new SimilarStudent().createByCopy(student, studentInfo, row, STU_SIMILAR_NONE_BY_STU_NUM));
                        continue;
                    }
                } else {
                    if (StringUtils.isNotBlank(className) && !ObjectUtil.isValueEquals(examStudentInfo.getClassName(), className)) {
                        //填写的班级信息与考生在诊断管理处的不一致
                        similarStuList.add(new SimilarStudent().createByCopy(student, studentInfo, row, STU_SIMILAR_NONE_BY_CLASS_NAME));
                        continue;
                    }
                }
                passedStudent = examStudentInfo;
            } else {
                //匹配到了多个考生
                if (matchByStuNum) {
                    //根据学校+学号匹配到了多个考生，说明考试中的学号数据有问题，需要去检查一下
                    similarStuList.add(new SimilarStudent().createByCopy(student, studentInfo, row, STU_SIMILAR_MULTI_BY_STU_NUM));
                    continue;
                } else if (StringUtils.isNotBlank(className)) {
                    List<ExamStudentInfoDTO> filterStuList = studentInfo.stream()
                      .filter(x -> ObjectUtil.isValueEquals(x.getClassName(), className)).collect(toList());
                    if (filterStuList.isEmpty()) {
                        similarStuList.add(new SimilarStudent().createByCopy(student, studentInfo, row, STU_SIMILAR_NONE_BY_CLASS_NAME));
                        continue;
                    } else if (filterStuList.size() > 1) {
                        similarStuList.add(new SimilarStudent().createByCopy(student, filterStuList, row, STU_SIMILAR_MULTI_BY_CLASS_NAME));
                        continue;
                    }
                    passedStudent = filterStuList.get(0);
                } else {
                    //根据学校+姓名匹配到了多个考生
                    similarStuList.add(new SimilarStudent().createByCopy(student, studentInfo, row, STU_SIMILAR_MULTI_BY_STU_NAME));
                    continue;
                }
            }

            Long studentId = passedStudent.getStudentId();
            if (stuId2Row.containsKey(studentId)) {
                //学生填写重复了
                stuId2RowList.computeIfAbsent(studentId, k -> new ArrayList<>()).add(row);
                havingErrCell = true;
                continue;
            }

            //暂时校验通过
            stuId2Row.put(studentId, row);
            passedStudent.setStudentExamNum(stuExamNum);
            passedStudentList.add(passedStudent);
        }

        //在学校内考号重复的
        if (!schoolId2StuExamNum2RowList.isEmpty()) {
            Map<String, List<RepeatCell>> repeatStuExamNum = new HashMap<>();
            schoolId2StuExamNum2RowList.forEach((schoolId, stuExamNum2RowList) -> {
                Map<String, Integer> schoolStuExamNum2Row = schoolId2StuExamNum2Row.get(schoolId);
                stuExamNum2RowList.forEach((stuExamNum, rowList) -> {
                    //考号在学校中第一次出现时所在的行
                    rowList.add(0, schoolStuExamNum2Row.get(stuExamNum));
                    List<RepeatCell> repeatCells = new ArrayList<>(rowList.size());
                    rowList.forEach(row -> {
                        errCells.add(builderCellErr(row, STU_EXAM_NUM_IDX, "异常数据，填写的考号在excel中重复"));
                        repeatCells.add(new RepeatCell(row, schoolId.toString()));
                    });
                    repeatStuExamNum.computeIfAbsent(stuExamNum, k -> new ArrayList<>()).addAll(repeatCells);
                });
                repeatInfo.put(STU_EXAM_NUM_IDX, repeatStuExamNum);
            });
        }

        //在学校内学号重复的
        if (!schoolId2StuNum2RowList.isEmpty()) {
            Map<String, List<RepeatCell>> repeatStuNum = new HashMap<>();
            schoolId2StuNum2RowList.forEach((schoolId, stuNum2RowList) -> {
                Map<String, Integer> schoolStuNum2Row = schoolId2StuNum2Row.get(schoolId);
                stuNum2RowList.forEach((stuNum, rowList) -> {
                    //学号在学校中第一次出现时所在的行
                    rowList.add(0, schoolStuNum2Row.get(stuNum));
                    List<RepeatCell> repeatCells = new ArrayList<>(rowList.size());
                    rowList.forEach(row -> {
                        errCells.add(builderCellErr(row, STU_NUM_IDX, "学号列填写重复"));
                        repeatCells.add(new RepeatCell(row, schoolId.toString()));
                    });
                    repeatStuNum.computeIfAbsent(stuNum, k -> new ArrayList<>()).addAll(repeatCells);
                });
                repeatInfo.put(STU_NUM_IDX, repeatStuNum);
            });
        }

        //学生重复
        if (!stuId2RowList.isEmpty()) {
            Map<String, List<RepeatCell>> repeatStuName = new HashMap<>();
            stuId2RowList.forEach((stuId, rowList) -> {
                //学生第一次出现时所在的行
                rowList.add(0, stuId2Row.get(stuId));
                List<RepeatCell> repeatCells = new ArrayList<>(rowList.size());
                rowList.forEach(row -> {
                    errCells.add(builderCellErr(row, STU_NAME_IDX, "异常数据，考生在表格中填写重复"));
                    repeatCells.add(new RepeatCell(row));
                });
                repeatStuName.put(stuId.toString(), repeatCells);
            });
            repeatInfo.put(STU_NAME_IDX, repeatStuName);
        }

        //计算、填充有数据重复的行的数量
        studentExcelResponse.setRepeatCount(repeatInfo.entrySet()
          .stream()
          .flatMap(entry -> entry.getValue().entrySet().stream())
          .flatMap(innerEntry -> innerEntry.getValue().stream())
          .map(RepeatCell::getR)
          .collect(Collectors.toSet())
          .size());

        //有异常数据或相似考生时，不让更新（不存在的考生会校验，但不阻塞更新操作。只有不存在考生这一种问题时，仍然让更新）
        if (!errCells.isEmpty() || !similarStuList.isEmpty()) {
            return studentExcelResponse;
        }

        if (!passedStudentList.isEmpty()) {
            //已经校验通过的学生，判断考号是否和没有更新的学生的考号存在重复的
            List<StudentExamNumUpdateDTO> stuExamNums = commonRepository.selectList("ExamStudentMapper.getStudentExamNumUpdateInfoByExamId",
                examId);
            Map<Long, String> stuId2NewStuExamNum = passedStudentList.stream()
                .collect(toMap(ExamStudentInfoDTO::getStudentId, ExamStudentInfoDTO::getStudentExamNum));
            Map<String, List<RepeatCell>> repeatStuExamNum = new HashMap<>();
            stuExamNums.stream().filter(x -> {
                    Long studentId = x.getStudentId();
                    String newStuExamNum = stuId2NewStuExamNum.get(studentId);
                    if (newStuExamNum != null) {
                        x.setStudentExamNum(newStuExamNum);
                    }
                  //考号为空的数据认为是异常数据（比如没有处理的旧考试的数据），不去管
                  return ObjectUtil.isNotBlank(x.getStudentExamNum());
              }).collect(Collectors.groupingBy(StudentExamNumUpdateDTO::getSchoolId, groupingBy(StudentExamNumUpdateDTO::getStudentExamNum)))
              .forEach((schoolId, stuExamNum2StuList) ->
                stuExamNum2StuList.forEach((stuExamNum, stuList) -> {
                    if (stuList.size() > 1) {
                        //只给在在线excel中的学生添加错误信息
                        List<StudentExamNumUpdateDTO> updateStuList = stuList.stream()
                          .filter(s -> stuId2NewStuExamNum.containsKey(s.getStudentId())).collect(toList());
                        if (!updateStuList.isEmpty()) {
                            errCells.addAll(updateStuList.stream()
                              .map(stu -> builderCellErr(stuId2Row.get(stu.getStudentId()), STU_EXAM_NUM_IDX, "异常数据，填写的考号在诊断中已存在"))
                              .collect(toList()));
                            repeatStuExamNum.put(stuExamNum,
                              updateStuList.stream().map(c -> new RepeatCell(stuId2Row.get(c.getStudentId()), c.getSchoolId().toString()))
                                .collect(toList()));
                        }
                    }
                }));

            if (!repeatStuExamNum.isEmpty()) {
                repeatInfo.put(STU_EXAM_NUM_IDX, repeatStuExamNum);
                studentExcelResponse.setRepeatCount(repeatStuExamNum.entrySet()
                    .stream()
                    .flatMap(entry -> entry.getValue().stream())
                    .map(RepeatCell::getR)
                    .collect(Collectors.toSet())
                    .size());
                return studentExcelResponse;
            }

            // 校验通过了并且用户没有选择确认更新的话，则调识别的接口看有没有已经关联过学生的课程，有的话给提示
            if (!ObjectUtil.isValueEquals(req.getConfirmUpdate(), true)) {
                List<ExamPaperDTO> recognizedCourseInfos = newPlanExamUploaderService.getRecognizedCourseInfos(examId);
                if (!recognizedCourseInfos.isEmpty()) {
                    studentExcelResponse.setRecognizedCourseNameList(
                      recognizedCourseInfos.stream().sorted(Comparator.comparing(ExamPaperDTO::getCourseId))
                        .map(ExamPaperDTO::getCourseName).distinct().collect(toList()));
                    return studentExcelResponse;
                }
            }

            //更新t_exam_student、t_exam_result、t_answer_card中的学生考号
            Map<String, Object> updateParams = MapUtil.of("examId", examId, "userId", req.getUserId(), "userName", req.getUserName());
            Long schoolId = req.getSchoolId();
            if (ObjectUtil.isValidId(schoolId)) {
                updateParams.put("schoolId", schoolId);
            }
            List<Map<String, Object>> examPaperList = examPaperService.getExamPaper(updateParams);
            updateParams.put("paperIds", examPaperList.stream().map(x -> MapUtils.getLong(x, "paperId")).collect(toList()));
            BatchDataUtil.execute(passedStudentList, (x) -> {
                updateParams.put("examStudent", x);
                commonRepository.insert("ExamStudentMapper.updateStuExamNumByInsert", updateParams);
                updateParams.put("studentIds", x.stream().map(ExamStudentInfoDTO::getStudentId).collect(toList()));
                commonRepository.update("ExamStudentMapper.updateResStuExamNum", updateParams);
                commonRepository.update("NewAnswerCardMapper.updateExamAnswerCardStuExamNum", updateParams);
            });
        }

        studentExcelResponse.setNormal(passedStudentList.size());
        return studentExcelResponse;
    }

    private void setStudent(Excel excel, ExamStudentInfoDTO examStudentInfoDTO, Set<Integer> ignoreRowSet) {
        Integer c = excel.getC();
        if (c == 0) {
            //学校
            examStudentInfoDTO.setSchoolName(excel.getV());
        } else if (c == 1) {
            //学生姓名
            examStudentInfoDTO.setStudentName(excel.getV());
        } else if (c == 2) {
            //考号
            examStudentInfoDTO.setStudentExamNum(excel.getV());
        } else if (c == 3) {
            //班级
            examStudentInfoDTO.setClassName(excel.getV());
        } else if (c == 4) {
            //学号
            examStudentInfoDTO.setStudentNum(excel.getV());
        } else if (c == 5 && "none".equals(excel.getV())) {
            ignoreRowSet.add(excel.getR());
        }
    }

    private ErrCell builderCellErr(Integer r, Integer c, String msg) {
        ErrCell errCell = new ErrCell();
        errCell.setR(r);
        errCell.setC(c);
        errCell.setMsg(msg);
        return errCell;
    }

    /**
     * 导出在考试中不存在的考生的列表
     */
    public String exportNotFoundStudentList(Map<String, Object> params) {

        List<Map<String, Object>> list = MapUtil.getListMap(params, "notFoundStudentList");
        if (CollectionUtils.isEmpty(list)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未找到可导出的考生信息");
        }
        SimpleExcelHeader headers = new SimpleExcelHeader(
          Arrays.asList("index", "schoolName", "studentName", "studentExamNum", "className", "studentNum", "row", "msg"),
          Arrays.asList("序号", "学校", "学生姓名", "考号", "班级", "学号", "原行号", "异常原因"));

        // 文件生成
        return new SimpleExcelReport(list, headers, ExcelVersionEnum.V_2007).exportToFileStorage("异常名单");
    }

    /**
     * 向考试中保存t_exam_student、t_exam_result, t_exam_student_ext的数据
     * @param params examId、userId、userName
     * @param examId 考试id
     * @param examStudentList 需要保存的t_exam_student数据
     * @param examResultList 需要保存的t_exam_result数据
     * @param needReplaceStuExamNum 是否需要根据考生在考试中已有的考号来替换掉需要保存的数据中的考生考号
     */
    public void insertStudentInfoToExam(Map<String, Object> params, long examId, List<Map<String, Object>> examStudentList,
      List<Map<String, Object>> examResultList, boolean needReplaceStuExamNum) {
        if (needReplaceStuExamNum) {
            replaceStuExamNumFromExam(examId, examStudentList, examResultList);
        }

        insertExamStudent(params, examStudentList);
        insertExamResult(params, examResultList);
    }

    /**
     * 向考试中保存t_exam_student、t_exam_student_ext的数据
     * @param params examId、userId、userName
     * @param examStudent 需要保存的t_exam_student数据
     */
    private void insertExamStudent(Map<String, Object> params, List<Map<String, Object>> examStudent) {
        if (examStudent.isEmpty()) {
            return;
        }
        BatchDataUtil.execute(examStudent, (x) -> {
            params.put("examStudent", x);
            commonRepository.insert("ExamUnionNewMapper.insertExamStudent", params);
        }, RESULT_DEFAULT_BATCH_SIZE);
        long examId = MapUtil.getLong(params, "examId");

        // 保存 t_exam_student_ext数据，主要是考场信息和座位号
        Date now = new Date();
        List<ExamStudentExt> examStudentExtList = examStudent.stream()
                .filter(x -> x.get("examRoomNo") != null || x.get("seatNumber") != null).map(x -> {
            ExamStudentExt examStudentExt = new ExamStudentExt();
            examStudentExt.setExamId(examId);
            examStudentExt.setStudentId(MapUtil.getLong(x, "studentId"));
            Object examRoomNo = x.get("examRoomNo");
            if (examRoomNo != null) {
                examStudentExt.setExamRoomNo(examRoomNo.toString());
            }
            Object seatNumber = x.get("seatNumber");
            if (seatNumber != null) {
                examStudentExt.setSeatNumber(seatNumber.toString());
            }
            examStudentExt.setCreateDateTime(now);
            examStudentExt.setModifyDateTime(now);
            examStudentExt.setModifierId(MapUtil.getLong(params, "userId"));
            examStudentExt.setModifierName(MapUtil.getString(params, "userName"));
            return examStudentExt;
        }).collect(toList());
        examStudentExtManager.batchInsert(examStudentExtList);
        params.remove("examStudent");
    }

    /**
     * 向考试中保存t_exam_result的数据
     * @param params examId、userId、userName
     * @param examResult 需要保存的t_exam_result数据
     */
    private void insertExamResult(Map<String, Object> params, List<Map<String, Object>> examResult) {
        if (examResult.isEmpty()) {
            return;
        }
        int attend = DictUtil.getDictValue("resultStatus", "attend");
        //添加学校等地方在向t_exam_result中保存数据时，不会传resultStatus参数，这些不传这个参数的地方就需要填充默认值
        examResult.forEach(x -> x.putIfAbsent("resultStatus", attend));
        Pinyin4jUtil.chinese2Pinyin(examResult, "studentName", "studentNamePinyin");
        BatchDataUtil.execute(examResult, (x) -> {
            params.put("examResult", x);
            commonRepository.insert("ExamUnionNewMapper.insertExamResult", params);
        }, RESULT_DEFAULT_BATCH_SIZE);
        params.remove("examResult");
    }

    /**
     * 根据examId从考试的t_exam_student中获取考生的考号，以此替换掉需要保存的数据中的考生考号（即保证一个学生在考试的tes、ter中的考号唯一）
     */
    private void replaceStuExamNumFromExam(Long examId, List<Map<String, Object>> examStudentList, List<Map<String, Object>> examResultList) {
        if(examStudentList.isEmpty() && examResultList.isEmpty()){
            return;
        }

        Map<Long, String> stuId2StuExamNum = getExamStuExamNum(examId).
          stream().collect(toMap(StudentExamNumUpdateDTO::getStudentId, StudentExamNumUpdateDTO::getStudentExamNum));
        replaceStuExamNumFromExam(stuId2StuExamNum, examStudentList);
        replaceStuExamNumFromExam(stuId2StuExamNum, examResultList);
    }

    /**
     * 根据stuId-stuExamNum替换list中的考生考号
     * @param stuId2StuExamNum stuId-stuExamNum的map
     * @param list 需要替换考生考号的数据
     */
    private void replaceStuExamNumFromExam(Map<Long, String> stuId2StuExamNum, List<Map<String, Object>> list){
        if (list.isEmpty()) {
            return;
        }

        //替换考生的考号，如果学生的数据在t_exam_student tes中已经存在，则优先取tes中的考号（需要保证学生在考试中的考号唯一）
        list.forEach(x -> {
            String existStuExamNum = stuId2StuExamNum.get(MapUtils.getLong(x, "studentId"));
            if (ObjectUtil.isNotBlank(existStuExamNum)) {
                x.put("studentExamNum", existStuExamNum);
            }
        });
    }

    /**
     * 检查考试中是否存在考生考号重复的情况
     * @param examId 考试id
     * @return true：考试中存在考生考号重复的情况
     *         false：考试中不存在考生考号重复的情况
     */
    public boolean checkStudentExamNumDuplicate(Long examId){
        return commonRepository.selectOne("ExamStudentMapper.checkStudentExamNumDuplicate", examId) != null;
    }

    /**
     * 导出在考试中考号重复的考生名单
     * @param examId 考试id
     */
    public String exportStudentExamNumDuplicateExcel(Long examId) {
        List<Map<String, Object>> studentList = commonRepository.selectList("ExamStudentMapper.getStudentExamNumDuplicateList", examId);
        if (studentList.isEmpty()) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "未找到考号重复的考生");
        }

        SimpleExcelHeader headers = new SimpleExcelHeader(
          Arrays.asList("index", "schoolName", "studentName", "studentExamNum", "className", "studentNum"),
          Arrays.asList("序号", "学校", "考生姓名", "考生考号", "班级", "学号"));

        return new SimpleExcelReport(studentList, headers, ExcelVersionEnum.V_2007).exportToFileStorage("考号重复考生名单");
    }


    /**
     * 获取学生关联的答题卡
     */
    public List<ExamStuPapersDTO> getStudentRelatedAnswerCard(long examId, long studentId) {
        List<ExamPaperInfoVO> examPaperInfoVOS = examPaperClientService.listPapers(examId);

        List<ExamResult> examResults = examResultManager.findByExamAndStudent(examId, studentId);
        if (examResults.isEmpty()) {
            return Collections.emptyList();
        }

        // 学生参考试卷
        Set<Long> studentPaperIds = examResults.stream().map(ExamResult::getPaperId).collect(toSet());

        // 学生已关联答题卡的试卷
        Set<Long> paperIds = newPlanExamManager.getStuRelatedPaperIds(examId, studentId);

        Map<Long, List<ExamPaperInfoVO>> courseId2Papers = examPaperInfoVOS.stream()
                .collect(groupingBy(ExamPaperInfoVO::getCourseId));

        return courseId2Papers.entrySet().stream().map(x -> {
            ExamStuPapersDTO examStuCardDTO = new ExamStuPapersDTO();
            examStuCardDTO.setCourseId(x.getKey());
            List<ExamStuPaperDTO> papers = new ArrayList<>();
            examStuCardDTO.setPapers(papers);
            for (ExamPaperInfoVO examPaperInfoVO : x.getValue()) {
                ExamStuPaperDTO examStuPaperDTO = new ExamStuPaperDTO();
                long paperId = examPaperInfoVO.getPaperId();
                examStuPaperDTO.setPaperId(paperId);
                examStuPaperDTO.setPaperName(examPaperInfoVO.getPaperName());
                examStuPaperDTO.setSelected(studentPaperIds.contains(paperId));
                examStuPaperDTO.setAssociated(paperIds.contains(paperId));
                papers.add(examStuPaperDTO);
            }
            return examStuCardDTO;
        }).collect(toList());
    }

    /**
     * 根据examId从t_exam_result获取所有的paperId与其对应的studentId
     * @param examId 考试id
     */
    public List<PaperIdStudentIdDTO> getExamPaperStudent(long examId){
        return commonRepository.selectList("ExamStudentMapper.getExamPaperStudent", examId);
    }

    /**
     * 根据examId从t_exam_student中获取所有的考生与其对应的考号
     * @param examId 考试id
     */
    public List<StudentExamNumUpdateDTO> getExamStuExamNum(long examId) {
        return commonRepository.selectList("ExamStudentMapper.getStudentExamNumUpdateInfoByExamId", examId);
    }

    /**
     * 考生管理-编辑 更新单个学生的考号、选考科目、标签
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateSingleStudentInfo(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isValidId("studentId")
          .isNotBlank("studentExamNum")
          .verify();

        Long schoolId = Optional.ofNullable(getExamStudentSchoolIdByExamIdAndStudentId(params)).orElseThrow(
          () -> new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,
            "未查询到考生信息！examId：" + params.get("examId") + "，studentId：" + params.get("studentId")));
        params.put("schoolId", schoolId);
        //校验考号在t_exam_student中是否已经存在
        if (commonRepository.selectOne("ExamStudentMapper.getStuIdByExamIdAndStuExamNum", params) != null) {
            //考号在t_exam_student中已被其他学生使用
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "考号在诊断中已被使用，请先核查数据或联系管理员处理");
        } else if (commonRepository.selectOne("ExamStudentMapper.getResStuIdByExamIdAndStuExamNum", params) != null) {
            //校验考号在t_exam_student中是否已经存在
            //理论上学生在t_exam_student中的考号和在t_exam_result中的考号是一样的，但可能会有数据出问题的情况，导致两边不完全一致
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "考号在诊断的部分课程中已被使用，请先核查数据或联系管理员处理");
        }
        //当前学生在t_exam_student中的考号已经是正确的了，那就把t_exam_result和t_answer_card中不一致的数据改一下
        //更新t_exam_result、t_answer_card中的学生考号
        StudentExamNumUpdateDTO studentExamNumUpdateDTO = new StudentExamNumUpdateDTO();
        studentExamNumUpdateDTO.setExamId(MapUtils.getLong(params, "examId"))
            .setStudentId(MapUtils.getLong(params, "studentId"))
              .setStudentExamNum(MapUtils.getString(params, "studentExamNum"));
        UserInfoUtil.copyModifyUserInfo(studentExamNumUpdateDTO, DongniUserInfoContext.get());
        commonRepository.update("ExamStudentMapper.updateStuExamNumByExamIdAndStuId", studentExamNumUpdateDTO);
        commonRepository.update("ExamStudentMapper.updateResStuExamNumByExamIdAndStuId", studentExamNumUpdateDTO);
        commonRepository.update("NewAnswerCardMapper.updateAnswerCardStuExamNumByExamIdAndStuId", studentExamNumUpdateDTO);

        //更新选考科目
        if (!ObjectUtil.isValidId(params.get("paperId"))) {
            //在切换到课程的时候，不会展示选科科目，也无法修改选考科目
            if (!ObjectUtil.isValidId(params.get("courseSelectionGroupId"))) {
                params.remove("courseSelectionGroupId");
            }
            commonRepository.update("ExamStudentMapper.updateExamStuCourseSelectionById", params);
        }

        //更新学生标签
        List<Long> examTagIds = MapUtil.getListLong(params, "examTagIds");
        params.remove("examTagIds");
        if (CollectionUtils.isNotEmpty(examTagIds)) {
            params.put("examTagIds", examTagIds);
            commonRepository.insert("ExamStudentMapper.insertExamStudentTagByIds", params);
        }
        commonRepository.delete("ExamStudentMapper.deleteExamStudentTagByIds", params);


    }

    /**
     * 根据examId和studentId获取学生所在的学校id
     * @param params examId、studentId
     * @return schoolId
     */
    public Long getExamStudentSchoolIdByExamIdAndStudentId(Map<String, Object> params){
        return commonRepository.selectOne("ExamStudentMapper.getExamStudentSchoolIdByExamIdAndStudentId", params);
    }

    /**
     * 判断是否隐藏考试列表
     *
     * @param params
     * @return true 隐藏  false 不隐藏
     */
    private boolean hideExamList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isNotBlank("userType")
                .verify();

        // 特殊需求: 江苏省锡山高级中学schoolId=988 + 家长角色 查询返回空
        String property = SpringProfilesActiveUtil.getSpringProfilesActive();
        if (DictUtil.isEquals(MapUtils.getInteger(params, "userType"), "userType", "parent")
                && SpringProfilesActiveUtil.isProductAliyun(property)) {
            Long schoolId = commonStudentService.getStudentSchoolId(MapUtil.getLong(params, "studentId"));
            if (schoolId == null) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学生信息不存在,请联系管理员!");
            }
            return schoolId == 988L;
        }
        return false;
    }

    public List<Long> getStudentIdByLast(long examId, long lastStudentId, long limit) {
        Map<String, Object> tmp = MapUtil.of("examId", examId, "lastStudentId", lastStudentId, "limit", limit);
        return commonRepository.selectList("ExamStudentMapper.getStudentIdByLast", tmp);
    }

    /**
     * 删除学生科目信息 -- 选科组合异常处理智能核查专用
     * 副作用: 入参RemoveStudentParam.studentIds列表值会被剔除
     */
    public List<StudentRemoveResult> deleteStudentCourse4SelectionCheck(RemoveStudentParam removeStudentParam) {
        long examId = removeStudentParam.getExamId();
        long paperId = removeStudentParam.getPaperId();

        List<StudentRemoveResult> studentRemoveResultList = new ArrayList<>();

        // -----------------------------------------试卷已经被删除了，剔除并置为删除成功------------------------------------------------
        List<Map<String, Object>> studentResult = commonRepository.selectList("ExamStudentMapper.getStudentResult4Remove", removeStudentParam);
        Set<Long> existsStudentIds = studentResult.stream().map(i -> MapUtil.getLong(i, "studentId"))
                .collect(Collectors.toSet());
        Set<Long> attendStudentIds = studentResult.stream()
                            .filter(i -> DictUtil.isEquals(MapUtil.getInt(i, "resultStatus"), "resultStatus", "attend"))
                            .map(i -> MapUtil.getLong(i, "studentId"))
                            .collect(Collectors.toSet());

        List<Long> paramStudentIds = removeStudentParam.getStudentIds();
        Iterator<Long> iterator = paramStudentIds.iterator();
        while (iterator.hasNext()) {
            Long studentId = iterator.next();
            if (!existsStudentIds.contains(studentId)) {
                StudentRemoveResult studentRemoveResult = StudentRemoveResult.removeSuccess(studentId);
                studentRemoveResultList.add(studentRemoveResult);
                iterator.remove();
            }
        }

        // -----------------------------------------校验学生状态---------------------------------------------------
        Map<String, Object> examDetail = examService.getExamDetail(MapUtil.of("examId", examId));
        if (DictUtil.isEquals(MapUtils.getInteger(examDetail, "correctMode"), "correctMode", "readByQuestion")) {
            // 还有上传任务的学生 - 剔除并置为删除失败
            if (CollectionUtils.isNotEmpty(paramStudentIds)) {
                List<Map<String, Object>> uploadingStudent = commonRepository.selectList(
                        "ExamStudentMapper.getUploadingPaperByStudent4Remove", removeStudentParam);
                Set<Long> uploadingStudentIds = uploadingStudent.stream().map(i -> MapUtil.getLong(i, "studentId"))
                        .collect(Collectors.toSet());
                removeIfExists(paramStudentIds, uploadingStudentIds, studentRemoveResultList, "存在进行中的上传任务");
            }
            // 试卷还在阅卷中 - 非缺考的学生剔除并置为删除失败
            if (CollectionUtils.isNotEmpty(paramStudentIds)) {
                Map<String, Object> paramMap = MapUtil.of("examId", examId, "paperId", paperId);
                Map<String, Object> examPaper = examPaperService.getExamPaperInfoByExamIdAndPaperId(paramMap);
                if (DictUtil.isEquals(MapUtil.getInt(examPaper, "examPaperStatus"), "examPaperStatus",
                        "trialReadPaper", "trialReadComplete", "readPaper")) {
                    removeIfExists(paramStudentIds, attendStudentIds, studentRemoveResultList, "试卷还在完成阅卷");
                }
            }
        } else {
            // 班级还有上传任务 - 剔除并置为删除失败
            if (CollectionUtils.isNotEmpty(paramStudentIds)) {
                List<Map<String, Object>> uploadingStudent = commonRepository.selectList(
                        "ExamStudentMapper.getClassUploadingTaskByStudent4Remove", removeStudentParam);
                Set<Long> uploadingStudentIds = uploadingStudent.stream().map(i -> MapUtil.getLong(i, "studentId"))
                        .collect(Collectors.toSet());
                removeIfExists(paramStudentIds, uploadingStudentIds, studentRemoveResultList, "所在班级存在进行中的上传任务");
            }
            // 班级阅卷未完成 - 非缺考的学生剔除并置为删除失败
            if (CollectionUtils.isNotEmpty(paramStudentIds)) {
                List<Map<String, Object>> inReadPaperStudent = commonRepository.selectList(
                        "ExamStudentMapper.getExamClassByExamIdAndStudentId4Remove", removeStudentParam);
                Set<Long> inReadPaperStudentIds = inReadPaperStudent.stream().map(i -> MapUtil.getLong(i, "studentId"))
                        .collect(Collectors.toSet());
                Collection<Long> intersection = CollectionUtils.intersection(inReadPaperStudentIds, attendStudentIds);
                removeIfExists(paramStudentIds, new HashSet<>(intersection), studentRemoveResultList, "学生所在的班级未完成阅卷");
            }
        }

        // 正在重新批阅的学生 - 剔除并置为删除失败
        if (CollectionUtils.isNotEmpty(paramStudentIds)) {
            List<Long> reReadStudentIds = commonRepository.selectList(
                    "ExamMarkReadRepeatMapper.getUnfinishedRereadItemByStudent4Remove", removeStudentParam);
            removeIfExists(paramStudentIds, new HashSet<>(reReadStudentIds), studentRemoveResultList, "正在重新批阅中");
        }
        // 正在异常卷处理的学生 - 剔除并置为删除失败
        if (CollectionUtils.isNotEmpty(paramStudentIds)) {
            List<Long> unfinishedRecognitionStudentIds = commonRepository.selectList(
                    "RecognitionCardMapper.getUnfinishedRecognitionByStudent4Remove", removeStudentParam);
            removeIfExists(paramStudentIds, new HashSet<>(unfinishedRecognitionStudentIds), studentRemoveResultList, "正在进行异常卷处理");
        }

        // -----------------------------------------删除阶段---------------------------------------------------
        if (CollectionUtils.isNotEmpty(paramStudentIds)) {
            DelItemRangeVO delItemRangeVO = new DelItemRangeVO();
            delItemRangeVO.setExamId(examId);
            delItemRangeVO.setPaperIds(Collections.singletonList(paperId));
            delItemRangeVO.setStudentIds(paramStudentIds);
            paperReadTaskService.delTaskAndRecordByRange(delItemRangeVO);

            commonRepository.delete("ExamStudentMapper.deleteExamResult4Remove", removeStudentParam);
            commonRepository.delete("ExamStudentMapper.deleteExamItem4Remove", removeStudentParam);

            // 如果学生全部科目并删除了，直接删除学生
            List<Long> hasResultStudentIds = commonRepository.selectList(
                    "ExamStudentMapper.getStudentAllResult4Remove", removeStudentParam);
            Set<Long> hasResultStudentIdSet = new HashSet<>(hasResultStudentIds);
            List<Long> deleteStudentIds = paramStudentIds.stream().filter(i -> !hasResultStudentIdSet.contains(i))
                    .collect(toList());
            if (CollectionUtils.isNotEmpty(deleteStudentIds)) {
                Map<String, Object> tmp = MapUtil.of("examId", examId, "studentIds", deleteStudentIds);
                commonRepository.delete("ExamStudentMapper.deleteExamStudentByIds", tmp);
                commonRepository.delete("ExamStudentTagMapper.deleteStudentExamTagByStudentIds", tmp);
            }

            // 全部置为删除成功
            for (Long paramStudentId : paramStudentIds) {
                StudentRemoveResult studentRemoveResult = StudentRemoveResult.removeSuccess(paramStudentId);
                studentRemoveResultList.add(studentRemoveResult);
            }
        }

        return studentRemoveResultList;
    }

    /**
     * 如果paramStudentIds的学生存在errStudentIds中，需要剔除，且设置为失败
     */
    private void removeIfExists(List<Long> paramStudentIds,
                                                     Set<Long> errStudentIds,
                                                     List<StudentRemoveResult> result,
                                                     String errMsg) {
        Iterator<Long> iterator = paramStudentIds.iterator();
        while (iterator.hasNext()) {
            Long studentId = iterator.next();
            if (errStudentIds.contains(studentId)) {
                StudentRemoveResult studentRemoveResult = StudentRemoveResult.removeFail(studentId, errMsg);
                result.add(studentRemoveResult);
                iterator.remove();
            }
        }
    }
}
