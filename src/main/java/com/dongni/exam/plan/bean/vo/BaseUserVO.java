package com.dongni.exam.plan.bean.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
public class BaseUserVO {
    private long userId;
    private String userName;
    private int pageSize;
    private int pageNo;
    private int currentIndex;
    private int userType;

    public void handlePageInfo(int totalCount) {
        if (totalCount == 0) {
            currentIndex = 0;
            pageNo = 1;
        } else {
            int totalPage = (int) Math.ceil(totalCount * 1.0d / pageSize);
            if (pageNo > totalPage) {
                pageNo = totalPage;
                currentIndex = (pageNo - 1) * pageSize;
            }
        }
    }
}
