package com.dongni.exam.plan.bean;

public class ExamUploaderClassVO {
    private long classId;

    private long examUploaderId;

    private int uploadType;

    private int uploadStatus;

    public long getClassId() {
        return classId;
    }

    public void setClassId(long classId) {
        this.classId = classId;
    }

    public long getExamUploaderId() {
        return examUploaderId;
    }

    public void setExamUploaderId(long examUploaderId) {
        this.examUploaderId = examUploaderId;
    }

    public int getUploadType() {
        return uploadType;
    }

    public void setUploadType(int uploadType) {
        this.uploadType = uploadType;
    }

    public int getUploadStatus() {
        return uploadStatus;
    }

    public void setUploadStatus(int uploadStatus) {
        this.uploadStatus = uploadStatus;
    }

    public boolean isExamUploaderProcessing() {
        return this.getUploadStatus() < 7;
    }

    public boolean isExamUploaderCompleted() {
        return getUploadStatus() == 7;
    }

}
