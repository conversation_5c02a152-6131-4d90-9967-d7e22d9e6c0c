package com.dongni.exam.plan.controller;

import com.dongni.analysis.view.monitor.bean.SchExamListParam;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.plan.service.ExamListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: hzw
 * @date: 2025/6/3
 * @description:
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/list")
public class ExamListController extends BaseController {

	@Autowired
	private ExamListService examListService;

	/**
	 * 获取区域下的考试列表
	 *    秦国强 鼓楼区
	 * @param regionId 区域id
	 * @return 考试列表（包括学校信息）
	 */

	@GetMapping("/by/area")
	@DongniNotRequireLogin
	public Response getExamListByAreaId(Long regionId) {
		return new Response(examListService.getExamListByAreaId(regionId));
	}

	/**
	 * 根据学校、起止时间获取学校的考试列表
	 * @return 考试列表
	 */
	@PostMapping("/by/school")
	@DongniNotRequireLogin
	public Response getExamListBySchoolAndTime() {
		SchExamListParam param = JSONUtil.parse(JSONUtil.toJson(getParameterMap()), SchExamListParam.class);
		return new Response(examListService.getExamListBySchoolAndTime(param));
	}

	/**
	 * 根据学校、报告刷新时间获取学校的考试列表
	 * @return 考试列表
	 */
	@PostMapping("/by/school/stat")
	@DongniNotRequireLogin
	public Response getExamListBySchoolAndStatTime() {
		SchExamListParam param = JSONUtil.parse(JSONUtil.toJson(getParameterMap()), SchExamListParam.class);
		return new Response(examListService.getExamListBySchoolAndStatTime(param));
	}

}
