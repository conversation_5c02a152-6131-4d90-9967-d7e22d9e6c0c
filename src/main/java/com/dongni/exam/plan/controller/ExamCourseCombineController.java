package com.dongni.exam.plan.controller;

import com.dongni.analysis.stat.service.ExamStatQueueService;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.plan.service.ExamCourseCombineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by l<PERSON><PERSON><PERSON>
 * time: 10:32 2019/08/20
 * description:组合排名
 */
@RestController
@RequestMapping("/exam/plan")
public class ExamCourseCombineController extends BaseController {

    @Autowired
    private ExamCourseCombineService examCourseCombineService;
    @Autowired
    private ExamStatQueueService examStatQueueService;

    /**
     * 获取组合排名列表
     */
    @GetMapping("/course/combine")
    public Response getCourseCombine() {
        return new Response(examCourseCombineService.getCourseCombine(getParameterMap()));
    }

    /**
     * 非GET/POST治理 考试
     * 删除组合排名
     */
    @DeleteMapping("/course/combine")
    @Deprecated
    public Response deleteCourseCombine() {
        examCourseCombineService.deleteCourseCombine(getParameterMap());
        return new Response();
    }

    /**
     * 删除组合排名 post
     */
    @PostMapping("/course/combine/delete")
    public Response deleteCourseCombine2() {
        examCourseCombineService.deleteCourseCombine(getParameterMap());
        return new Response();
    }

    /**
     * 添加组合排名
     */
    @PostMapping("/course/combine")
    public Response addCourseCombine() {
        examCourseCombineService.addCourseCombine(getParameterMap());
        return new Response();
    }

    /**
     * 判断是否已选课
     */
    @GetMapping("/course/courseRemark")
    public Response isCourseRemark(){
        return new Response(examCourseCombineService.getCourseRemark(getParameterMap()));
    }

    /**
     * 获取考试所属省份
     */
    @GetMapping("/province")
    public Response getProvince(){
        return new Response(examCourseCombineService.getProvince(getParameterMap()));
    }

    /**
     * 刷新组合排名
     */
    @GetMapping("/course/combine/refresh")
    public Response refreshCourseCombine() {
        //添加组合后刷新统计
        examStatQueueService.computeCourseCombine(getParameterMap());
        return new Response();
    }

}
