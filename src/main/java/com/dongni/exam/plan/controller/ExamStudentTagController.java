package com.dongni.exam.plan.controller;


import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.plan.service.ExamStudentTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by <PERSON>wei<PERSON> on 2017/11/1.
 *
 *  学生考试标签管理控制器
 */
@RestController
@RequestMapping(value = "/exam/student/tag")
public class ExamStudentTagController extends BaseController {

    @Autowired
    private ExamStudentTagService examStudentTagService;

    /**
     * @Description: 获取联考学校
     * @Param: examId
     * @return:
     */
    @GetMapping("/school")
    public Response getExamSchool(Map<String,Object> params) {
        return new Response(examStudentTagService.getExamSchool(params));
    }

    /**
     * 获取考试学生列表
     * params examBaseId schoolId
     * @return 学生列表
     */
    @GetMapping("")
    public Response getStudent(){
        return new Response(examStudentTagService.getStudent(getParameterMap()));
    }

    /**
     * 导出参考学生excel
     */
    @GetMapping("/export")
    public Response exportStudentList(){
        return new Response(examStudentTagService.exportStudentList(getParameterMap()));
    }

    /**
     * 获取学生关联的标签
     * params examStudentId
     * @return 标签列表
     */
    @GetMapping("/detail")
    public Response getStudentExamTag(){
        return new Response(examStudentTagService.getStudentExamTag(getParameterMap()));
    }

    /**
     * 给学生关联标签
     * @param params examStudentId tagId
     * @return 学生总数
     */
    @PostMapping
    public Response insertStudentExamTag(@RequestBody Map<String,Object> params){
        return new Response(examStudentTagService.insertStudentExamTag(params));
    }

    /**
     * 删除学生标签的关联
     * @param params examStudentId tagId
     */
    @PostMapping("/delete")
    public Response deleteStudentExamTag(@RequestBody Map<String,Object> params) {
        examStudentTagService.deleteStudentExamTag(params);
        return new Response();
    }

}
