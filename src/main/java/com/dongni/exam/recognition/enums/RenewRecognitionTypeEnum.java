package com.dongni.exam.recognition.enums;

public enum RenewRecognitionTypeEnum {
    QUESTION_RENEW(1, "试题重新切图"),
    OBJECTIVE_RENEW(2, "客观题重新识别"),
    SPLIT_RENEW(3, "拆分试题"),
    CHOOSE_RENEW(4, "选做题重新识别"),
    LOCATION_RENEW(5, "定位点重新识别"),
    MERGE_RENEW(7, "合并试题"),
    ADD_RENEW(8, "新增试题"),
    STU_NO_RENEW(9, "学号异常识别");

    private int type;
    private String code;

    RenewRecognitionTypeEnum(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
