package com.dongni.exam.recognition.enums;

public enum StuExamNumRecogTypeEnum {
    BY_BAR(1, "byBar"),
    BY_QR(2, "byQR"),
    BY_FILLING(3, "byFilling"),
    BY_HANDWRITING(4, "byHandwriting"),
    BY_NAME_OCR(5, "byNameOCR");

    private int typeCode;
    private String typeName;

    StuExamNumRecogTypeEnum(int typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public int getTypeCode() {
        return typeCode;
    }

    public String getTypeName(){
        return typeName;
    }
}
