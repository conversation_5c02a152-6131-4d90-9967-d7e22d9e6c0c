package com.dongni.exam.recognition.enums;

public enum RecognitionStatusEnum {
    DELETED(-1, "删除"),
    PROGRESSING(0, "识别中"),
    COMPLETED(1, "识别完成");
    private int status;
    private String desc;

    RecognitionStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
