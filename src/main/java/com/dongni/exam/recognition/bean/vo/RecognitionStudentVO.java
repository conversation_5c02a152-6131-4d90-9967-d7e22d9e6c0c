package com.dongni.exam.recognition.bean.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/6
 */
@Data
public class RecognitionStudentVO {
    private long recognitionId;
    private long examId;
    private long paperId;
    private long studentId;
    private String studentName;
    private String studentExamNum;
    private long recognitionCardId;

    private String barNum;
    private String fillingNum;
    private String handNum;
    private String handName;
    private String qrNum;
    private int numStatus;
    private long relativeStudentId;
    private String relativeStudentName;
    private String relativeStudentExamNum;
    private int relativeStatus;  // 是否关联. 1: 已关联. 0:未关联.
    private int repeatNum;

    private long classId;
    private String className;
}
