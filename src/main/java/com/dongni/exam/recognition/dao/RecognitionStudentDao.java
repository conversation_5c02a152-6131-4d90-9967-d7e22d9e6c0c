package com.dongni.exam.recognition.dao;

import com.dongni.exam.plan.bean.bo.ExamResultBO;
import com.dongni.exam.recognition.bean.vo.RecognitionStuVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStudentVO;
import com.dongni.exam.recognition.bean.vo.RecognitionTemplateStuExpVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RecognitionStudentDao {
    List<RecognitionStudentVO> getRecognitionStudents(RecognitionStuVO recognitionStuVO);

    int getRecognitionStudentCount(RecognitionStuVO recognitionStuVO);

    List<RecognitionStudentVO> getRecognitionStudentCards(long recognitionId, long studentId);

    void updateRelativeStudentId(long recognitionId, List<Long> recognitionCardIds, long toStudentId);

    List<Long> getRepeatStudentIds(long recognitionId);

    void updateRepeatNum(long recognitionId, long relativeStudentId, int repeatNum);

    int getRepeatNum(long recognitionId, long relativeStudentId);

    void insertRecognitionCardExtList(@Param("cards") List<RecognitionStudentVO> cards);

    void renewRelativeStudentId(long recognitionId, long recognitionTemplateId);

    List<ExamResultBO> getExamResultList(long recognitionId);

    List<RecognitionStudentVO> getRecognitionStudentCardList(RecognitionStuVO recognitionStuVO);

    List<Long> getModifyStudentIds(long recognitionId);

    int getNotMatchCount(long recognitionId);

    List<Long> getCardStudentIds(long recognitionId);

    List<Long> getRelativeStudentIds(@Param("recognitionId") long recognitionId, @Param("relativeStudentIds") List<Long> relativeStudentIds);

    List<Long> getRelativeStudentIdsByNormalStudentIds(@Param("recognitionId")long recognitionId, @Param("studentIds") List<Long> studentIds);

    List<RecognitionTemplateStuExpVO> getRecognitionTemplateStuExp(@Param("recognitionId") long recognitionId);
}
