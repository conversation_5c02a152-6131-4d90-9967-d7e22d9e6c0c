package com.dongni.exam.recognition.dao;

import com.dongni.exam.recognition.bean.dto.RecognitionTemplateDTO;
import com.dongni.exam.recognition.bean.vo.RecognitionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RecognitionDao {
    int getPaperStructureModifyingCount(@Param("examId") long examId, @Param("paperId") long paperId);

    void updateFailure2Normal(long recognitionId);

    List<Map<String, Object>> getRecognitionStudents(@Param("recognitionId") Long recognitionId);

	List<RecognitionTemplateDTO> getRecognitionTemplates(@Param("recognitionTemplateIds") Set<Long> recognitionTemplateIds);

    RecognitionVO getRecognition(long recognitionId);

    void updateRecognitionStatus(long recognitionId, int status);

    int getOtherProgressingRecognitionCount(@Param("examId") long examId, @Param("paperId") long paperId, @Param("recognitionId") long recognitionId);

    List<Integer> getRecognitionQns(@Param("recognitionId") Long recognitionId);
}
