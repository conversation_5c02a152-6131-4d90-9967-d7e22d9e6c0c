package com.dongni.exam.recognition.service;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.vo.ExamPaperInfoVO;
import com.dongni.exam.mark.bean.dto.ExamPaperQnScoreDTO;
import com.dongni.exam.mark.service.PaperObjItemScoreService;
import com.dongni.exam.recognition.bean.RecogQnsAndStusBO;
import com.dongni.exam.recognition.bean.RecognitionItemBO;
import com.dongni.exam.recognition.bean.dto.RecognitionQuestionDTO;
import com.dongni.exam.recognition.bean.dto.RecognitionTemplateDTO;
import com.dongni.exam.recognition.dao.RecognitionDao;
import com.dongni.exam.recognition.dao.RecognitionItemDao;
import com.dongni.exam.recognition.dao.RecognitionQuestionDao;
import com.dongni.exam.recognition.enums.RecognitionTypeEnum;
import com.dongni.exam.recognition.manager.IRecognitionCardManager;
import com.dongni.exam.recognition.manager.IRecognitionManager;
import com.dongni.tiku.common.enumeration.PaperAnswerStatus;
import com.dongni.tiku.manager.impl.PaperManager;
import com.google.common.collect.Sets;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static java.util.stream.Collectors.*;

@Service
public class NewRecognitionService {

    @Resource
    private RecognitionDao recognitionDao;

    @Resource
    private RecognitionItemDao recognitionItemDao;

    @Autowired
    private IRecognitionManager recognitionManager;

    @Autowired
    private IRecognitionCardManager recognitionCardManager;

    public void isPaperStructureModifying(long examId, long paperId) {
        int count = recognitionDao.getPaperStructureModifyingCount(examId, paperId);
        if (count > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "管理员正在修改阅卷试题，请稍后扫描或联系管理员及时处理");
        }
    }

    @Autowired
    private PaperManager paperManager;

    @Autowired
    private PaperObjItemScoreService paperObjQusScoreService;

    /**
     * 完成扫描的时候，修复客观题分数错误.
     *
     * @param recognitionId
     */
    public void updateRecognitionObjectiveItems(long recognitionId) {
        List<RecognitionItemBO> unfinishedObjectiveItems = recognitionItemDao.getUnfinishedObjectiveItems(recognitionId);
        if (CollectionUtils.isEmpty(unfinishedObjectiveItems)) {
            return;
        }
        long paperId = unfinishedObjectiveItems.get(0).getPaperId();
        Document paper = paperManager.getPaper(paperId);
        if (!ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS.getStatus())) {
            return;
        }
        ExamPaperQnScoreDTO examPaperQnScoreDTO = new ExamPaperQnScoreDTO();
        examPaperQnScoreDTO.setExamId(unfinishedObjectiveItems.get(0).getExamId());
        examPaperQnScoreDTO.setPaperId(paperId);
        examPaperQnScoreDTO.setQnScores(new ArrayList<>());
        Map<Integer, List<RecognitionItemBO>> qn2ListMap = unfinishedObjectiveItems.stream().collect(groupingBy(RecognitionItemBO::getQuestionNumber));

        for (Map.Entry<Integer, List<RecognitionItemBO>> entry : qn2ListMap.entrySet()) {
            ExamPaperQnScoreDTO.QnScore qnScore = new ExamPaperQnScoreDTO.QnScore();
            qnScore.setQuestionNumber(entry.getKey());
            List<ExamPaperQnScoreDTO.QnScore.RecognitionValueScore> values = entry.getValue().stream().map(it -> {
                ExamPaperQnScoreDTO.QnScore.RecognitionValueScore recognitionValueScore = new ExamPaperQnScoreDTO.QnScore.RecognitionValueScore();
                recognitionValueScore.setRecognitionValue(it.getRecognitionValue());
                return recognitionValueScore;
            }).collect(toList());

            qnScore.setRecognitionValueScores(values);
            examPaperQnScoreDTO.getQnScores().add(qnScore);
        }

        examPaperQnScoreDTO = paperObjQusScoreService.getExamPaperQnScore(examPaperQnScoreDTO);
        List<RecognitionItemBO> itemBOS = recognitionItemDao.getZeroObjectiveItems(recognitionId);
        Map<Integer, List<RecognitionItemBO>> qn2ItemMap = itemBOS.stream().collect(groupingBy(RecognitionItemBO::getQuestionNumber));
        for (Map.Entry<Integer, List<RecognitionItemBO>> entry : qn2ItemMap.entrySet()) {
            ExamPaperQnScoreDTO.QnScore qnScore = examPaperQnScoreDTO.getQnScores().stream().filter(it -> it.getQuestionNumber().equals(entry.getKey())).findFirst().orElse(null);
            if (qnScore == null) {
                continue;
            }
            List<ExamPaperQnScoreDTO.QnScore.RecognitionValueScore> scores = qnScore.getRecognitionValueScores();
            Map<String, List<RecognitionItemBO>> rv2ItemsMap = entry.getValue().stream().collect(groupingBy(RecognitionItemBO::getRecognitionValue));
            for (Map.Entry<String, List<RecognitionItemBO>> rv : rv2ItemsMap.entrySet()) {
                String recv = rv.getKey();
                ExamPaperQnScoreDTO.QnScore.RecognitionValueScore score = scores.stream().filter(it -> it.getRecognitionValue().equals(recv)).findFirst().orElse(null);
                if (score == null) {
                    continue;
                }
                rv.getValue().forEach(it -> {
                    it.setFinallyScore(score.getScore());
                    it.setReadStatus(1);
                });
            }
        }
        recognitionItemDao.updateRecognitionItems(itemBOS);
    }

    public List<Map<String, Object>> getRecognitionStudents(Long recognitionId) {
        return recognitionDao.getRecognitionStudents(recognitionId);
    }

    @Resource
    private RecognitionQuestionDao recognitionQuestionDao;

    public List<Integer> getSubjectiveQNS(long recognitionId) {
        return getQNList(recognitionId).stream()
                .filter(it -> it.getReadType() == 2)
                .map(it -> it.getQuestionNumber()).collect(toList());
    }

    /**
     * 设计到选做题，需要特别处理下
     *
     * @param recognitionId
     * @return
     */
    private List<RecognitionQuestionDTO> getQNList(long recognitionId) {
        List<RecognitionQuestionDTO> questionDTOS = recognitionQuestionDao.getRQNList(recognitionId);
        long studentId = recognitionItemDao.getRecognitionItem(recognitionId);
        List<RecognitionItemBO> itemBOS = recognitionItemDao.getStudentRecognitionItem(recognitionId, studentId);
        Map<Integer, RecognitionItemBO> qnInfo = itemBOS.stream().collect(toMap(l -> l.getQuestionNumber(), l -> l));
        questionDTOS.forEach(x -> {
            if (qnInfo.containsKey(x.getQuestionNumber())) {
                x.setReadType(qnInfo.get(x.getQuestionNumber()).getReadType());
            } else {
                x.setReadType(2);
            }
        });

        return questionDTOS;
    }

    public List<Integer> getObjectiveQNS(long recognitionId) {
        return getQNList(recognitionId).stream()
                .filter(it -> it.getReadType() == 1)
                .map(it -> it.getQuestionNumber()).collect(toList());
    }

    public List<RecognitionTemplateDTO> getRecognitionTemplates(Set<Long> recognitionTemplateIds) {
        return recognitionDao.getRecognitionTemplates(recognitionTemplateIds);
    }


    /**
     * 阅卷那边需要的异常卷处理的qns, stuIds.
     * @param recognitionId
     * @return
     */
    public RecogQnsAndStusBO getRecognitionQnsAndStus(long recognitionId, int recognitionType) {
        List<Integer> qns = recognitionManager.getRecognitionQns(recognitionId, recognitionType);
        // 试题重新切图 选做题重新识别 定位点重新识别，可以选择特定的学生
        Set<Integer> recognitionTypes = Sets.newHashSet(
                RecognitionTypeEnum.SUBJECTIVE.getType(),
                RecognitionTypeEnum.OPTIONAL.getType(),
                RecognitionTypeEnum.LOCATION.getType()
        );
        List<Long> studentIds = recognitionTypes.contains(recognitionType) ?
                recognitionCardManager.getRecogStudentIds(recognitionId) : Collections.emptyList();
        RecogQnsAndStusBO recogQnsAndStusBO = new RecogQnsAndStusBO();
        recogQnsAndStusBO.setQns(qns);
        recogQnsAndStusBO.setStudentIds(studentIds);
        recogQnsAndStusBO.setRecognitionId(recognitionId);
        return recogQnsAndStusBO;
    }
}
