package com.dongni.exam.recognition.service;

import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.basedata.export.user.service.CommonUserService;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.ResourceConfig;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.enums.UnitTypeEnum;
import com.dongni.exam.common.mark.serivice.exam.IExamPaperClientService;
import com.dongni.exam.common.mark.serivice.mark.ICardItemClientService;
import com.dongni.exam.common.mark.serivice.mark.IPaperReadService;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.*;
import com.dongni.exam.health.check.bean.bo.ExamItemQuestionNumberBO;
import com.dongni.exam.maintain.service.ExamCompleteAgainService;
import com.dongni.exam.mark.ai.service.IExamItemCutting;
import com.dongni.exam.mark.bean.entity.PaperRead;
import com.dongni.exam.mark.constant.CommonConstant;
import com.dongni.exam.mark.manager.ICardReportItemManager;
import com.dongni.exam.mark.manager.IPaperReadManager;
import com.dongni.exam.mark.service.ExamMarkCompleteService;
import com.dongni.exam.mark.service.IAbnormityItemService;
import com.dongni.exam.mark.service.IRepeatMarkService;
import com.dongni.exam.mark.util.ExamCheckUtil;
import com.dongni.exam.newcard.bean.RecognitionTaskBean;
import com.dongni.exam.newcard.enums.TaskTypeEnum;
import com.dongni.exam.newcard.service.IRecognitionTaskService;
import com.dongni.exam.plan.service.ExamPaperService;
import com.dongni.exam.recognition.dao.RecognitionItemDao;
import com.dongni.exam.recognition.helper.HandleReadHelper;
import com.dongni.exam.recognize.enumeration.ItemCategoryEnum;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.manager.impl.RecognitionQuestionMappingManager;
import com.dongni.tiku.own.service.OwnPaperService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.eq;
import static java.util.stream.Collectors.*;

/**
 * @description: 异常答题卡识别
 * @author: Jianfeng
 * @create: 2019-07-15 19:13
 **/
@Service
public class RecognitionManualService {

    private final static Logger log = LoggerFactory.getLogger(RecognitionManualService.class);

    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private CommonUserService commonUserService;

    @Autowired
    private IPaperReadService paperReadService;

    @Autowired
    private IExamPaperClientService examPaperClientService;

    @Autowired
    private HandleReadHelper handleReadHelper;

    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private CardReportService cardReportService;
    @Autowired
    private ExamCompleteAgainService examCompleteAgainService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private IPaperReadManager paperReadManager;
    @Autowired
    private ExamConfigService examConfigService;

    @Autowired
    private ICardItemClientService cardItemClientService;


    @Autowired
    private IRepeatMarkService repeatMarkService;

    @Autowired
    private ICardReportItemManager cardReportItemManager;

    @Autowired
    private IAbnormityItemService abnormityItemService;

    @Autowired
    private RecognitionQuestionMappingManager recognitionQuestionMappingManager;

    @Autowired
    private IQsClientService qsClientService;

    @Autowired
    private ExamMarkCompleteService examMarkCompleteService;

    @Autowired
    private IExamItemCutting examItemCutting;


    /**
     * 考试试卷状态-阅卷安排
     */
    private static final int READ_ARRANGE = 10;
    /**
     * 考试试卷状态-阅卷中
     */
    private static final int READ_PAPER = 15;
    /**
     *考试试卷状态-阅卷完成
     */
    private static final int READ_COMPLETE = 20;

    @Autowired
    private RecognitionExpStuNoService recognitionExpStuNoService;

    /**
     * 开始识别
     *
     * @param params recognitionTemplateId
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void startRecognition(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .verify();

        int count = examRepository.selectOne("RecognitionTaskMapper.getRecognitionTemplateCount", params);
        if(count > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "异常卷识别进行中，请稍后！");
        }
        // 删除小任务.
        examRepository.delete("RecognitionTaskMapper.deleteRecognitionTemplate", params);
        Integer pageNumber = examRepository.selectOne("RecognitionCardMapper.getRecognitionCardPageNumber", params);
        params.put("templateNumber", pageNumber);
        // 更新答题卡模版重新识别状态
        examRepository.update("RecognitionCardMapper.updateRecognitionTemplateStatusRecognitionIng",params);

        // 查询识别模版记录
        Map<String, Object> recognitionTemplate = examRepository.selectOne("RecognitionCardMapper.getRecognitionTemplate", params);

        long recognitionTemplateId = MapUtil.getLong(recognitionTemplate, "recognitionTemplateId");
        long recognitionId = MapUtil.getLong(recognitionTemplate, "recognitionId");
        params.put("recognitionId", recognitionId);
        examRepository.delete("RecognitionCardMapper.deleteRecognitionItemByTemplate", params);
        // 获取redis队列
        String mapName = ResourceConfig.getString("answerCardRenewMap") + params.get("recognitionTemplateId");
        String queueName = ResourceConfig.getString("answerCardRenewRecognitionQueue");

        // 组装redis参数
        Map<String, String> redisParams = new HashMap<>();
        redisParams.put("userId", params.get("userId").toString());
        redisParams.put("userName", params.get("userName").toString());

        if(params.get("templateNumber")!=null) {
            redisParams.put("templateNumber", params.get("templateNumber").toString());
        }else{
            log.error("com.dongni.exam.recognition.service.RecognitionManualService.startRecognition 95行 未传页码 templateNumber");
        }
        if(params.containsKey("objectItemRecoType")){
            redisParams.put("objectItemRecoType", params.get("objectItemRecoType").toString());
        }
        recognitionTemplate.forEach((key, val) -> {
            if (val != null) {
                redisParams.put(key, val.toString());
            }
        });

        recognitionExpStuNoService.initRecognitionExtStudentInfo(recognitionId, recognitionTemplateId);
        insertTask2RecognitionTask(params, redisParams);
    }

    @Autowired
    private IRecognitionTaskService recognitionTaskService;
    private void insertTask2RecognitionTask(Map<String, Object> params, Map<String, String> redisParams) {
        int studentCount = examRepository.selectOne("RecognitionCardMapper.getStudentCount", params);
        int pieceCount = 50;
        int pieces = (int)Math.ceil(studentCount * 1.0 / pieceCount);
        RecognitionTaskBean bean = new RecognitionTaskBean();
        bean.setRecognitionTemplateId(MapUtil.getLong(params, "recognitionTemplateId"));
        bean.setTaskStatus(1);
        bean.setCreateDateTime(DateUtil.getCurrentDateTime());
        bean.setTaskType(TaskTypeEnum.NORMAL_OR_RECOGNITION_CARD_TASK.getCode());
        int templateNumber =  Integer.valueOf(redisParams.get("templateNumber"));
        for(int i = 0; i < pieces; i++) {
            String taskId = MapUtil.getString(params, "recognitionTemplateId") + ":" + String.valueOf(pieceCount) + ":" + (i+ 1);
            redisParams.put("taskId", taskId);
            redisParams.put("cardCount", String.valueOf(pieceCount * templateNumber));
            bean.setTaskId(taskId);
            bean.setRedisInfo(JSONUtil.toJson(redisParams));
            recognitionTaskService.insertRecognitionTask(bean);
        }
    }
    //todo 可能存在长事务

    @Resource
    private RecognitionItemDao recognitionItemDao;

    // 检查学生都是cards都是ok的
    private void checkStudentIsOk(Map<String, Object> params) {
        int count = examRepository.selectOne("RecognitionCardMapper.getFailedStudents", params);
        if(count > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "存在切割失败的同学，请联系系统管理员");
        }

        // 增加检测识别完成的同学要与t_recognition_student一致
        long cardStudentCount =  examRepository.selectOne("RecognitionCardMapper.getStudentByAnswerCardCount", params);
        long recognitionStudentCount = examRepository.selectOne("RecognitionCardMapper.getRecognitionStudentCount", params);
        if(cardStudentCount != recognitionStudentCount) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "存在识别完成的学生,没有进行解答题拆分！");
        }

        long recognitionId = MapUtil.getLong(params, "recognitionId");
        List<QuestionStructureVO> qns = getRecognitionQuestions(recognitionId);
        List<Integer> needQns = qns.stream().filter(it -> !UnitTypeEnum.isOptionalQn(it.getUnitType())).map(QuestionStructureVO::getQuestionNumber).collect(toList());
        if(needQns.size() > 0) {
            List<ExamItemQuestionNumberBO> questionNumberBOS = recognitionItemDao.getQuestionNumbers(recognitionId, needQns);
            if(questionNumberBOS.size() < needQns.size()) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "题块缺失，请联系管理员！");
            }
            count = (int) questionNumberBOS.stream().filter(item -> item.getItemCount() < cardStudentCount).count();
            if(count > 0) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "题块个别学生明细缺失，请联系管理员！");
            }
        }
        // 检测每个学生的明细是否存在
        List<Integer> values = examRepository.selectList("RecognitionCardMapper.getCardStudentCount", params);
        int size = CollectionUtils.isEmpty(values) || values.size() < 2 ? 0 : values.size();
        if(size != 2) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "存在某些同学切割明细丢失，请联系系统管理员");
        } else if(size == 2) {
            int v0 = values.get(0);
            int v1 = values.get(1);

            if(v0 != v1) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "存在某些同学切割明细丢失，请联系系统管理员");
            }
        }

    }

    public List<QuestionStructureVO> getRecognitionQuestions(long recognitionId) {
        List<QuestionStructureVO>  qns = new ArrayList<>();
        Document recognitionQuestionMappingDoc = recognitionQuestionMappingManager.getFirst(eq("recognitionId", recognitionId));
        if (recognitionQuestionMappingDoc != null && recognitionQuestionMappingDoc.get("questionList") != null) {
            List<Document> list = recognitionQuestionMappingDoc.get("questionList", List.class);
            qns = list.stream().map(item -> {
                QuestionStructureVO vo = new QuestionStructureVO();
                vo.setQuestionNumber(item.get("questionNumber", Integer.class));
                vo.setUnitType(item.get("unitType", Integer.class));
                vo.setQuestionType(item.get("questionType", Integer.class));
                vo.setReadType(item.get("readType", Integer.class));
                return vo;
            }).collect(toList());
        }
        return qns;
    }

    /**
    * @Description: 试题拆分/合并识别全部处理完成
    * @Param: examId paperId recognitionId
    */
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName = "EXAM", name = "questionSplitRecognitionComplete", argValueKeys = {"[0].paperId"}, expireTime = -1)
    public void questionSplitRecognitionComplete(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionId")
                .verify();

        long examId = MapUtil.getLong(params, "examId");
        long paperId = MapUtil.getLong(params, "paperId");
        long recognitionId = MapUtil.getLong(params, "recognitionId");

//        ExamPaperInfoVO examPaperInfo = examPaperClientService.getExamPaperInfo(examId, paperId);
//        if (ExamCheckUtil.finishMark(examPaperInfo.getExamPaperStatus())) {
//            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已阅卷完成，不支持");
//        }

        Map<String, Object> recognitionInfo = examRepository.selectOne("RecognitionCardMapper.getRecognition", params);
        int status = MapUtil.getInt(recognitionInfo, "recognitionStatus", 0);
        if (status == 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已经完成，请勿重复操作！");
        }
        checkStudentIsOk(params);
        // 更新答题卡识别状态为已完成
        examRepository.update("RecognitionCardMapper.updateRecognitionStatusComplete", params);

        // 查询需要处理的学校
        List<Long> schoolIds = examRepository.selectList("RecognitionCardMapper.getExamSchool", params);
        params.put("schoolId", StringUtils.join(schoolIds, ","));

        //获取试题映射关系
        Document recognitionQuestionMappingDoc = recognitionQuestionMappingManager.getFirst(eq("recognitionId", recognitionId));

        Map<Integer, List<Integer>> newQn2OldQn = new HashMap<>();
        Map<Integer, QuestionStructureVO> newQn2QsMap = new HashMap<>();
        List<Integer> newQuestionNumbers = new ArrayList<>();
        List<Integer> oldQuestionNumbers = new ArrayList<>();
        if(recognitionQuestionMappingDoc !=null && recognitionQuestionMappingDoc.get("questionList")!=null){
            List<Document> questionList = recognitionQuestionMappingDoc.get("questionList",List.class);
            if(CollectionUtils.isNotEmpty(questionList)) {
                for (Document question : questionList) {
                    Integer newQn = question.get("questionNumber", Integer.class);
                    String structureNumber = question.get("structureNumber", String.class);
                    BigDecimal scoreValue = new BigDecimal(question.get("scoreValue").toString());
                    QuestionStructureVO vo = new QuestionStructureVO();
                    vo.setStructureNumber(structureNumber);
                    vo.setQuestionNumber(newQn);
                    vo.setScoreValue(scoreValue);
                    newQn2QsMap.put(newQn, vo);
                    List<Integer> oldQns = new ArrayList<>();
                    if(question.get("list")!=null){
                        List<Document> list = (List<Document>) question.get("list");
                        for(Document doc: list){
                            Integer oldQn = doc.get("questionNumber", Integer.class);
                            oldQns.add(oldQn);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(oldQns)){
                        newQn2OldQn.put(newQn, oldQns);
                        oldQuestionNumbers.addAll(oldQns);
                        newQuestionNumbers.add(newQn);
                    }
                }
            }
        }
        params.put("newQuestionNumbers", newQuestionNumbers);
        params.put("oldQuestionNumbers", oldQuestionNumbers);
        params.put("newQn2OldQn", newQn2OldQn);
        params.put("newQn2QsMap", newQn2QsMap);
        // 设置题号
//        setQuestionNumber(params);

        // 获取学生试卷阅卷完成状态
        Map paperStatus = examPaperStatus(params);

        // 更新paper和answerCardTemplate
        ownPaperService.replacePaperAndTemplate(params);

        Map<String, Object> examPaper = examPaperService.getExamPaperInfoByExamIdAndPaperId(params);
        params.put("examPaperId", examPaper.get("examPaperId"));
//        List<Integer> oldQuestionNumbers = ((List<String>) params.get("oldQuestionNumbers")).stream().map(Integer::parseInt).collect(Collectors.toList());

        // 处理阅卷任务
        // 删除异常卷申报记录
        cardItemClientService.deleteByQns(examId, paperId, oldQuestionNumbers);

        List<PaperRead> oldPaperRead = paperReadManager.findByExamIdAndPaperId(examId, paperId);

        String blkIds = oldPaperRead.stream().map(x -> String.valueOf(x.getReadBlockId())).distinct().collect(joining(CommonConstant.COMMA));
        handleReadHelper.executePrAndTaskAndRecord(blkIds, params, paperStatus, examId, paperId, oldPaperRead);

        //重置原始报告配置
        examConfigService.resetExamConfigInfo(params);
    }



    /**
     * @Description: 试题拆分/合并识别全部处理完成
     * @Param: examId paperId recognitionId
     */
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName = "EXAM", name = "questionAddRecognitionComplete", argValueKeys = {"[0].paperId"}, expireTime = -1)
    public void questionAddRecognitionComplete(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionId")
                .verify();

        long examId = MapUtil.getLong(params, "examId");
        long paperId = MapUtil.getLong(params, "paperId");
        long recognitionId = MapUtil.getLong(params, "recognitionId");

//        ExamPaperInfoVO examPaperInfo = examPaperClientService.getExamPaperInfo(examId, paperId);
//        if (ExamCheckUtil.finishMark(examPaperInfo.getExamPaperStatus())) {
//            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已阅卷完成，不支持");
//        }

        Map<String, Object> recognitionInfo = examRepository.selectOne("RecognitionCardMapper.getRecognition", params);
        int status = MapUtil.getInt(recognitionInfo, "recognitionStatus", 0);
        if (status == 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已经完成，请勿重复操作！");
        }
        checkStudentIsOk(params);


        List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(paperId);

        // 更新答题卡识别状态为已完成
        examRepository.update("RecognitionCardMapper.updateRecognitionStatusComplete", params);

        // 查询需要处理的学校
        List<Long> schoolIds = examRepository.selectList("RecognitionCardMapper.getExamSchool", params);
        params.put("schoolId", StringUtils.join(schoolIds, ","));

        //获取试题映射关系
        Document recognitionQuestionMappingDoc = recognitionQuestionMappingManager.getFirst(eq("recognitionId", recognitionId));

        List<Integer> addQns = new ArrayList<>();
        List<Integer> oldQns = new ArrayList<>();
        List<QuestionStructureVO> addQsList = new ArrayList<>();
        BigDecimal addScoreValue = new BigDecimal(0.0d);
        if(recognitionQuestionMappingDoc !=null && recognitionQuestionMappingDoc.get("questionList")!=null){
            List<Document> questionList = recognitionQuestionMappingDoc.get("questionList",List.class);
            if(CollectionUtils.isNotEmpty(questionList)) {
                for (Document question : questionList) {
                    Integer newQn = Integer.valueOf(question.get("questionNumber").toString());
                    String structureNumber = question.get("structureNumber").toString();
                    QuestionStructureVO newQs = new QuestionStructureVO();
                    newQs.setQuestionNumber(newQn);
                    newQs.setStructureNumber(structureNumber);
                    newQs.setReadType(Integer.valueOf(question.get("readType").toString()));
                    newQs.setCourseId(Long.valueOf(question.get("courseId").toString()));
                    newQs.setCourseName(question.get("courseName").toString());
                    newQs.setQuestionType(Integer.valueOf(question.get("questionType").toString()));
                    newQs.setQuestionTypeName(question.get("questionTypeName").toString());
                    newQs.setScoreValue(new BigDecimal(question.get("scoreValue").toString()));
                    newQs.setUnitType(Integer.valueOf(question.get("unitType").toString()));
                    if(question.get("optionalGroup")!=null && StringUtils.isNotEmpty(question.get("optionalGroup").toString())){
                        newQs.setOptionalGroup(Integer.valueOf(question.get("optionalGroup").toString()));
                    }
                    if(question.get("optionalCount")!=null && StringUtils.isNotEmpty(question.get("optionalCount").toString())){
                        newQs.setOptionalCount(Integer.valueOf(question.get("optionalCount").toString()));
                    }
//                    addScoreValue = addScoreValue.add(newQs.getScoreValue());
                    addQns.add(newQn);
                    addQsList.add(newQs);
                }
            }
        }
        params.put("addQns", addQns);
        params.put("addQsList", addQsList);
        params.put("newQuestionNumbers", addQns);
        params.put("oldQuestionNumbers", oldQns);
        // 设置题号
//        setQuestionNumber(params);

        // 获取学生试卷阅卷完成状态
        Map paperStatus = examPaperStatus(params);

        // 更新paper和answerCardTemplate
        ownPaperService.replacePaperAndTemplate(params);

        Map<String, Object> examPaper = examPaperService.getExamPaperInfoByExamIdAndPaperId(params);
        params.put("examPaperId", examPaper.get("examPaperId"));
        FullMarkVO fullMark = qsClientService.getFullMark(paperId);
        String currentDateTime = DateUtil.getCurrentDateTime(); // 更新时间
        Map<String, Object> user = commonUserService.getUserById(params);
        params.put("userName", user.get("userName").toString());
        params.put("paperFullMark", fullMark.getFullMark());
        // 1. 更新t_exam_paper分值
        examRepository.update("ExamCombineActionMapper.updateExamPaperFullMark",
                MapUtil.of(
                        "userId", params.get("userId"),
                        "userName", params.get("userName"),
                        "currentTime", currentDateTime,
                        "examId", params.get("examId"),
                        "paperId", params.get("paperId"),
                        "fullMark", params.get("paperFullMark")
                ));

        List<QuestionStructureVO> subjectQsList = questionStructureVOS.stream().filter(qs -> qs.getReadType() == 2).collect(toList());
        boolean hasSubjectItem = true;
        if(CollectionUtils.isEmpty(subjectQsList)){
            hasSubjectItem = false;
        }

        if(!hasSubjectItem) {
            Map<String, Object> examInfo = examRepository.selectOne("ExamMapper.getExamInfo", params);
            boolean isUnion = ObjectUtil.isValueEquals(examInfo.get("examType"), DictUtil.getDictValue("examType", "union"))
                    || ObjectUtil.isValueEquals(examInfo.get("examType"), DictUtil.getDictValue("examType", "area"));
            boolean isUseTemplate = false;
            if (examInfo.get("entryType") != null) {
                int examType = Integer.valueOf(examInfo.get("examType").toString());
                if (examType == 2) {
                    isUseTemplate = true;
                }
            }
            if (isUseTemplate) {
                examPaperClientService.updateTplEpWhileMark(examId, paperId);
            } else if (isUnion) {
                examPaperClientService.updateUnionExamArrange(examId, paperId);
            }
        }
        // 处理阅卷任务
        List<PaperRead> oldPaperRead = paperReadManager.findByExamIdAndPaperId(examId, paperId);

        String blkIds = oldPaperRead.stream().map(x -> String.valueOf(x.getReadBlockId())).distinct().collect(joining(CommonConstant.COMMA));
        handleReadHelper.executePrAndTaskAndRecord(blkIds, params, paperStatus, examId, paperId, oldPaperRead);

        // 答题卡添加临时试题-给委托关联试题使用
        ownPaperService.insertTmpSubjectQuestion(paperId);

        //重置原始报告配置
        examConfigService.resetExamConfigInfo(params);
    }


    /**
     * 设置题号
     *
     * @param params
     */
    private void setQuestionNumber(Map<String, Object> params) {
        // 获取拆分后的试题题号
        List<String> newQuestionNumbers = examRepository.selectList("RecognitionCardMapper.getQuestionInfoBySplit", params);

        // 获取拆分的试题来源题号
        List<String> oldQuestionNumbers = new ArrayList<>();
        Map<String, String> questionNumberMap = new HashMap<>();

        Map<String, Object> recognitionPaper = ownPaperService.getRecognitionPaper(params);
        PaperUtil.forEachQuestion(recognitionPaper, item -> {
            boolean isTrue = false;
            List<Map<String, Object>> structures = (List<Map<String, Object>>) item.get("structures");
            for (Map<String, Object> structure : structures) {
                if (newQuestionNumbers.contains(structure.get("questionNumber").toString())) {
                    isTrue = true;
                    questionNumberMap.put(structure.get("questionNumber").toString(), item.get("questionNumber").toString());
                }
            }

            if (isTrue) {
                oldQuestionNumbers.add(item.get("questionNumber").toString());
            }
        });

        params.put("newQuestionNumbers", newQuestionNumbers);
        params.put("oldQuestionNumbers", oldQuestionNumbers);
        params.put("questionNumberMap", questionNumberMap);
    }

    /**
     * 检查题目分值是否OK
     * @param params
     */
    private void checkQuestionNumberScore(Map<String, Object> params) {
        List<Map<String, Object>> recognitionScoreList = examRepository.selectList("RecognitionCardMapper.getRecognitionQuestionNumberScore", params);
        List<Map<String, Object>> scoreList = examRepository.selectList("RecognitionCardMapper.getQuestionNumberScore", params);
        boolean someScoreDiff = recognitionScoreList.stream().anyMatch(item -> {
            long questionNumber = MapUtil.getLong(item, "questionNumber");
            double scoreValue = MapUtil.getDouble(item, "scoreValue");
            return scoreList.stream().anyMatch(it -> {
                long qn = MapUtil.getLong(it, "questionNumber");
                double score = MapUtil.getDouble(it, "scoreValue");
                if(questionNumber == qn) {
                    return scoreValue != score;
                } else {
                    return false;
                }
            });
        });

        if(someScoreDiff) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "异常卷里的分值与试卷的分值不同，请重新识别异常卷！");
        }
    }


    @Autowired
    private NewRecognitionService newRecognitionService;
    /**
     * @Description: 客观题识别全部处理完成
     * @Param: examId paperId recognitionId
     */
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName = "EXAM", name = "objectiveQuestionRecognitionComplete", argValueKeys = {"[0].recognitionId"}, expireTime = -1)
    public void objectiveQuestionRecognitionComplete(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionId")
                .verify();

        long recognitionId = MapUtil.getLong(params, "recognitionId");
        newRecognitionService.updateRecognitionObjectiveItems(recognitionId);
        checkQuestionNumberScore(params);
        // 更新答题卡识别状态为已完成
        examRepository.update("RecognitionCardMapper.updateRecognitionStatusComplete",params);

        List<Map<String, Object>> studentIdList = newRecognitionService.getRecognitionStudents(recognitionId);
        List<Integer> QNList = newRecognitionService.getObjectiveQNS(recognitionId);
        params.put("QNList", QNList);
        BatchDataUtil.execute(studentIdList, x -> {
            params.put("studentIdList", x);
            examRepository.insert("RecognitionCardMapper.insertExamItem", params);
        });
        params.put("studentIdList", studentIdList);
        // 插入item

        Long paperId = MapUtil.getLong(params, "paperId");
        List<QuestionStructureVO> questionStructureVOS = qsClientService.listQuestionStructure(paperId);
        List<Map> examPaperInfos = examRepository.selectList("RecognitionCardMapper.getRecognitionExamPaperInfo", params);
        params.putAll(examPaperInfos.get(0));
        List<Integer> qns = questionStructureVOS.stream().filter(vo -> vo.getReadType()==1).map(vo -> vo.getQuestionNumber()).collect(toList());
        params.put("questionNumbers", qns);
        List<Long> studentIdsWithNull =  examRepository.selectList("RecognitionCardMapper.getRecognitionStudentsWithNull", params);
        List<Integer> singles = questionStructureVOS.stream().filter(vo -> vo.getReadType()==1 && vo.getUnitType() == 1).map(vo -> vo.getQuestionNumber()).collect(toList());
        params.put("questionNumbers",  singles);
        List<Long> studentIdsWithMulti = new ArrayList<>();
        if(!singles.isEmpty()) {
            studentIdsWithMulti =  examRepository.selectList("RecognitionCardMapper.getRecognitionStudentsWithMulti", params);
        }

        params.put("studentIdListWithNull", studentIdsWithNull);
        params.put("studentIdListWithMulti", studentIdsWithMulti);
        if(CollectionUtils.isNotEmpty(studentIdsWithNull)) {
            examRepository.update("RecognitionCardMapper.updateAnswerCardErrCodeWithNull", params);
        }
        examRepository.update("RecognitionCardMapper.updateAnswerCardErrCodeWithNoNull", params);
        if(CollectionUtils.isNotEmpty(studentIdsWithMulti)) {
            examRepository.update("RecognitionCardMapper.updateAnswerCardErrCodeWithMulti", params);
        }
        examRepository.update("RecognitionCardMapper.updateAnswerCardErrCodeWithNoMulti", params);

        // 执行统计
        examCompleteAgainService.invokeExamCompleteAgain(params);

//        // 获取学生试卷阅卷完成状态
//        Map paperStatus = examPaperStatus(params);
//
//        // 处理阅卷未开始的学生
//        if (paperStatus.get(READ_ARRANGE) != null) {
//            params.put("studentIdList", paperStatus.get(READ_ARRANGE));
//            examRepository.insert("RecognitionCardMapper.insertExamItem", params);
//        }
//
//        // 处理阅卷结束的学生
//        if (paperStatus.get(READ_COMPLETE) != null) {
//            params.put("studentIdList", paperStatus.get(READ_COMPLETE));
//            examRepository.insert("RecognitionCardMapper.insertExamItem", params);
//
//            // 判断整个考试是否阅卷完成
//            Map paperInfo = examRepository.selectOne("AnswerCardMapper.getExamPaperComplete", params);
//            Boolean isReadComplete = ObjectUtil.isValueEquals(READ_COMPLETE, paperInfo.get("examPaperStatus"));
//
//            // 执行阅卷完成的所有统计项
//            if (isReadComplete) {
//                examStatQueueService.computeAll(params);
//                return;
//            }
//
//            // 按班级阅卷单班级完成
//            if ((Boolean) paperStatus.get("readByClass")) {
//                examStatQueueService.computeReadClass(params);
//            // 按试题阅卷单科目完成
//            } else {
//                examStatQueueService.computeReadPaper(params);
//            }
//        }
    }

    private void checkCardStatus(Map<String, Object> params) {
        int count = examRepository.selectOne("RecognitionCardMapper.getFailedStudents", params);
        if(count > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "存在切割失败的同学，请联系系统管理员");
        }

//        if (examMarkCompleteService.completeExam(params)) {
//            throw new CommonException(ResponseStatusEnum.ANSWER_CARD_ERROR, "该考试科目阅卷已结束，无法处理完成，请删除该异常卷处理任务");
//        }
    }
    /**
     * @Description: 主观题识别全部处理完成
     * @Param: examId paperId recognitionId
     */
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName = "EXAM", name = "subjectiveQuestionRecognitionComplete", argValueKeys = {"[0].recognitionId"}, expireTime = -1)
    public void subjectiveQuestionRecognitionComplete(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionId")
                .verify();

        long examId = MapUtil.getLong(params, "examId");
        long paperId = MapUtil.getLong(params, "paperId");
        ExamPaperInfoVO examPaperInfo = examPaperClientService.getExamPaperInfo(examId, paperId);
        if (ExamCheckUtil.finishMark(examPaperInfo.getExamPaperStatus())) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已阅卷完成，不支持");
        }

        Map<String, Object> examPaper = examPaperService.getExamPaperInfoByExamIdAndPaperId(params);
        params.put("examPaperId", examPaper.get("examPaperId"));

        checkCardStatus(params);
        // 更新答题卡识别状态为已完成
        examRepository.update("RecognitionCardMapper.updateRecognitionStatusComplete", params);

        // 获取学生试卷阅卷完成状态
        examPaperStatus(params);

        putBlkIds(params);

        // 获取试题
        List<Integer> questionNumbers = examRepository.selectList("RecognitionCardMapper.getRecognitionQuestion", params);
        params.put("QNList", questionNumbers);
        handleReadHelper.handleRead(params);


        examItemCutting.deleteExamItemCuttingByQNs(examId, paperId, questionNumbers);

        // 删除异常申报记录
        long recognitionId = MapUtil.getLong(params, "recognitionId");
        abnormityItemService.delStuAbItem(questionNumbers, recognitionId);

        // 校验并删除待办
        cardReportService.checkAndDeleteCardReportTodoTask(params);
    }

    /**
     * 获取异常处理的题块id
     */
    private void putBlkIds(Map<String, Object> params) {
        List<Integer> qns = examRepository.selectList("RecognitionCardMapper.getRecognitionQuestion", params);
        long examId = Long.parseLong(params.get("examId").toString());
        long paperId = Long.parseLong(params.get("paperId").toString());
        List<PaperReadVO> paperReadVOS = paperReadService.listByExamIdAndPaperId(examId, paperId);
        // 增加默认值，防止报错
        if (paperReadVOS.isEmpty()) {
            params.put("readBlockIds", "0");
            return;
        }
        String blkIds = paperReadVOS.stream().filter(x-> qns.contains(x.getQuestionNumber()))
                .map(x -> String.valueOf(x.getReadBlockId())).distinct().collect(joining(","));
        params.put("readBlockIds", blkIds);
    }


    @Autowired
    private RecognitionTemplateService recognitionTemplateService;

    @Autowired
    private RecognitionCardService recognitionCardService;

    @Autowired
    private RecognitionErrorCodeService recognitionErrorCodeService;
    /**
     * @Description:  定位区识别全部处理完成
     * @Param: examId paperId recognitionId
     */
    @Transactional(ExamRepository.TRANSACTION)
    @DistributeLock(moduleName = "EXAM", name = "featureRedrawRecognitionComplete", argValueKeys = {"[0].recognitionId"}, expireTime = -1)
    public void featureRedrawRecognitionComplete(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionId")
                .isNotBlank("itemCategory")
                .verify();

        long recognitionId = MapUtil.getLong(params, "recognitionId");
        long examId = MapUtil.getLong(params, "examId");
        long paperId = MapUtil.getLong(params, "paperId");
        ExamPaperInfoVO examPaperInfo = examPaperClientService.getExamPaperInfo(examId, paperId);

        String readRecordsFlag = "";
        if (params.get("readRecordsOperate") != null) {
            readRecordsFlag = params.get("readRecordsOperate").toString();
        }
        if (ExamCheckUtil.finishMark(examPaperInfo.getExamPaperStatus()) && "remove".equalsIgnoreCase(readRecordsFlag)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已阅卷完成，不支持");
        }

        Map<String, Object> examPaper = examPaperService.getExamPaperInfoByExamIdAndPaperId(params);
        params.put("examPaperId", examPaper.get("examPaperId"));

        checkCardStatus(params);
        //itemCategory: 1-仅客观题；2-仅主观题；3-客观题+主观题
        List<Integer> categories = (List<Integer>) params.get("itemCategory");
        if(CollectionUtils.isEmpty(categories)){
            return;
        }

        newRecognitionService.updateRecognitionObjectiveItems(MapUtil.getLong(params, "recognitionId"));
        // 更新答题卡识别状态为已完成
        examRepository.update("RecognitionCardMapper.updateRecognitionStatusComplete",params);
        boolean isOverNormalTemplate = isAllQuestionDraw(params);
        int failedCount = examRepository.selectOne("RecognitionCardMapper.getFailedCount", params);

        if(failedCount > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "还存在识别失败的学生，请继续处理！");
        }

        if(isOverNormalTemplate) {
            // 存下模板
            recognitionTemplateService.savePERecognitionTemplate2ManualTemplate(params);
            // 更新answer_card表里的correct_param, 清空correct_path, correct_status.
            recognitionCardService.updateAnswerCardTemplateCodeAndCorrectParam(params);
        }

        params.remove("examPaperId");
        if(ItemCategoryEnum.hasObjectiveItems(categories)){
            // 插入客观题item
            List<Map<String, Object>> studentIdList = newRecognitionService.getRecognitionStudents(recognitionId);
            Map<String, Object> newParams = new HashMap<>();
            newParams.putAll(params);
            newParams.put("QNList", newRecognitionService.getObjectiveQNS(recognitionId));
            BatchDataUtil.execute(studentIdList, x -> {
                newParams.put("studentIdList", x);
                examRepository.insert("RecognitionCardMapper.insertObjectExamItem", newParams);
            });
            // 执行统计
            examCompleteAgainService.invokeExamCompleteAgain(params);
        }

        if(ItemCategoryEnum.hasSubjectiveItems(categories)){
            // 获取学生试卷阅卷完成状态
            Map paperStatus = examPaperStatus(params);
            List<Map<String, Object>> studentIdList = MapUtil.getListMap(params, "studentIdList");
            // 处理阅卷未开始的学生
            if (paperStatus.get(READ_ARRANGE) != null) {
                List<Map<String, Object>> stuList = (List<Map<String, Object>>)paperStatus.get(READ_ARRANGE);
                BatchDataUtil.execute(stuList, x -> {
                    params.put("studentIdList", x);
                    examRepository.insert("RecognitionCardMapper.insertExamItem", params);
                });
                // 重写studentIdList.
                params.put("studentIdList", studentIdList);
            }

            putBlkIds(params);

            params.put("QNList", newRecognitionService.getSubjectiveQNS(recognitionId));
            params.put(CommonConstant.NO_INTELLI_RGT, true);
            // 处理阅卷中的学生
            handleReadHelper.handleRead(params);

            // 删除异常申报记录
            List<Integer> questionNumbers = examRepository.selectList("RecognitionCardMapper.getRecognitionQuestion", params);
            examItemCutting.deleteExamItemCuttingByQNs(examId, paperId, questionNumbers);
            abnormityItemService.delStuAbItem(questionNumbers, recognitionId);

            // 校验并删除待办
            cardReportService.checkAndDeleteCardReportTodoTask(params);
        }
    }


    /**
     * @Description: 学生试卷阅卷情况
     * @Param: examId paperId recognitionId
     */
    private Map examPaperStatus(Map<String, Object> params) {

        // 判断阅卷类型
        Map examBaseInfo = examRepository.selectOne("ExamMapper.getExamBaseInfo", params);
        Integer dictValue = DictUtil.getDictValue("correctMode", "readByClass");
        Boolean readByClass = ObjectUtil.isValueEquals(dictValue, examBaseInfo.get("correctMode"));

        Map studentPaper = new HashMap();
        studentPaper.put("readByClass", readByClass);

        // 获取异常识别选择的学生信息
        List<Map<String, Object>> studentInfoList = examRepository.selectList("RecognitionCardMapper.getRecognitionCard", params);
        if (CollectionUtils.isEmpty(studentInfoList)) {
            return studentPaper;
        }
        params.put("studentIdList", studentInfoList);
        Map<Object, List<Map<String, Object>>> studentInfoMapByClassId = studentInfoList.stream().collect(Collectors.groupingBy(a -> a.get("classId")));
        Map<Object, List<Map<String, Object>>> studentInfoMapBySchoolId = studentInfoList.stream().collect(Collectors.groupingBy(a -> a.get("schoolId")));

        // 按班级
        if (readByClass) {
            // 获取班级阅卷情况
            params.put("classIdList", studentInfoMapByClassId.keySet());
            List<Map<String,Object>> classPaperInfoList = examRepository.selectList("AnswerCardMapper.getExamClassPaperComplete", params);
            Map<Object, List<Map<String, Object>>> examClassPaperStatus = classPaperInfoList.stream().collect(Collectors.groupingBy(a -> a.get("examClassPaperStatus")));

            // 得到学生试卷的阅卷情况
            examClassPaperStatus.forEach((k,v)->{
                List list = new ArrayList();
                v.forEach(a-> list.addAll(studentInfoMapByClassId.get(a.get("classId"))));
                studentPaper.put(k, list);
            });

        // 按试题
        } else {
            // 获取考试类型
            params.put("examType", examBaseInfo.get("examType"));
            Integer union = DictUtil.getDictValue("examType", "union");
            Boolean isUnion = ObjectUtil.isValueEquals(union, examBaseInfo.get("examType"));

            //联考
            if (isUnion) {
                // 获取试题阅卷情况
                params.put("schoolIdList", studentInfoMapBySchoolId.keySet());
                List<Map<String, Object>> paperInfoList = examRepository.selectList("AnswerCardMapper.getSchoolExamPaperComplete", params);
                Map<Object, List<Map<String, Object>>> examPaperStatus = paperInfoList.stream().collect(Collectors.groupingBy(a -> a.get("examPaperStatus")));

                // 得到学生试卷的阅卷情况
                examPaperStatus.forEach((k, v) -> {
                    List list = new ArrayList();
                    v.forEach(a -> list.addAll(studentInfoMapBySchoolId.get(a.get("schoolId"))));
                    studentPaper.put(k, list);
                });

            // 普通考试
            } else {
                // 获取试题阅卷情况
                Map paperInfo = examRepository.selectOne("AnswerCardMapper.getExamPaperComplete", params);

                // 得到学生试卷的阅卷情况
                studentPaper.put(paperInfo.get("examPaperStatus"), studentInfoList);
            }
        }

        return studentPaper;
    }


    /**
     * 判断是不是所有题都进行了切割.
     * @param params
     * @return
     */
    private boolean isAllQuestionDraw(Map<String, Object> params) {
        List<Integer> recognitionQuestions = examRepository.selectList("RecognitionCardMapper.getRecognitionQuestion", params);
        List<Integer> questions = examRepository.selectList("RecognitionCardMapper.getRecognitionPaperQuestion", params);
        if(questions.size() == recognitionQuestions.size()){
            return recognitionQuestions.stream().allMatch(questionNumber -> questions.contains(questionNumber));
        }
        return false;
    }
}