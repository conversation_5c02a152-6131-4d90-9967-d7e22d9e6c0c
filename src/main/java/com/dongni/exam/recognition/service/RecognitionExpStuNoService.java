package com.dongni.exam.recognition.service;

import com.alibaba.excel.util.StringUtils;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.vo.ExamPaperInfoVO;
import com.dongni.exam.maintain.service.ExamCompleteAgainService;
import com.dongni.exam.newcard.bean.ExamResultStudentVO;
import com.dongni.exam.newcard.manager.IAnswerCardManager;
import com.dongni.exam.newcard.manager.ICardExamItemManager;
import com.dongni.exam.plan.bean.bo.ExamResultBO;
import com.dongni.exam.plan.bean.vo.ResultListVO;
import com.dongni.exam.plan.manager.INewExamResultManager;
import com.dongni.exam.plan.manager.INewExamUploaderManager;
import com.dongni.exam.plan.service.NewExamResultService;
import com.dongni.exam.plan.service.NewPlanExamUploaderService;
import com.dongni.exam.recognition.bean.vo.*;
import com.dongni.exam.recognition.enums.RecognitionStatusEnum;
import com.dongni.exam.recognition.manager.IRecognitionManager;
import com.dongni.exam.recognition.manager.IRecognitionStudentManager;
import com.dongni.newmark.manager.ExamPaperManager;
import com.dongni.tiku.common.util.MapUtil;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/12
 */
@Service
public class RecognitionExpStuNoService{

    @Autowired
    private IRecognitionStudentManager recognitionStudentManager;

    @Autowired
    private INewExamUploaderManager newExamUploaderManager;

    @Autowired
    private IRecognitionManager recognitionManager;

    @Autowired
    private ExamPaperManager examPaperManager;

    @Autowired
    private ICardExamItemManager cardExamItemManager;

    @Autowired
    private IAnswerCardManager answerCardManager;

    public static final String DEFAULT_STU_NO = "0";

    @Autowired
    private ExamCompleteAgainService examCompleteAgainService;

    @Autowired
    private INewExamResultManager newExamResultManager;

    @Autowired
    private NewExamResultService newExamResultService;

    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;

    public List<RecognitionTemplateStuExpVO> getRecognitionTemplateStuExp(long recognitionId) {
        List<RecognitionTemplateStuExpVO> recognitionTemplateStuExpVOS = recognitionStudentManager.getRecognitionTemplateStuExp(recognitionId);
        return recognitionTemplateStuExpVOS;
    }

    public ResultListVO<RecognitionStudentVO> getExpStuNos(RecognitionStuVO recognitionStuVO) {
        ResultListVO<RecognitionStudentVO> resultListVO = new ResultListVO<>();
        int pageSize = recognitionStuVO.getPageSize();
        List<RecognitionStudentVO> recognitionStudentVOS = recognitionStudentManager.getRecognitionStudents(recognitionStuVO);
        int totalCount = pageSize == 0 ? recognitionStudentVOS.size() : recognitionStudentManager.getRecognitionStudentCount(recognitionStuVO);
        resultListVO.setList(recognitionStudentVOS);
        List<Long> relativeStudentIds = recognitionStudentVOS.stream()
                .filter(x -> x.getRelativeStudentId() != 0)
                .map(x -> x.getRelativeStudentId())
                .collect(toSet())
                .stream().collect(toList());

        if (!CollectionUtils.isEmpty(relativeStudentIds)) {
            RecognitionVO recognitionVO = recognitionManager.getRecognition(recognitionStuVO.getRecognitionId());
            List<ExamResultStudentVO> examResultList = newExamResultManager.getExamResultStudentsByExamIdAndPaperIdAndStuIds(recognitionVO.getExamId(), recognitionVO.getPaperId(), relativeStudentIds);
            Map<Long, ExamResultStudentVO> examResultInfo = examResultList.stream().collect(toMap(x -> x.getStudentId(), x -> x));
            recognitionStudentVOS.forEach(x -> {
                long relativeStudentId = x.getRelativeStudentId();
                if (examResultInfo.containsKey(relativeStudentId)) {
                    x.setRelativeStudentExamNum(examResultInfo.get(relativeStudentId).getStudentExamNum());
                    x.setRelativeStudentName(examResultInfo.get(relativeStudentId).getStudentName());
                }
            });
        }

        resultListVO.setTotalCount(totalCount);
        return resultListVO;
    }

    /**
     * @param renewRelativeVO
     * 关联另个学生.
     */
    @DistributeLock(moduleName="RENEW_RECOGNITION", name = "relativeStudent", argValueKeys = {"[0].recognitionId"}, expireTime = 30)
    @Transactional(ExamRepository.TRANSACTION)
    public void handleRelativeStudent(RenewRelativeVO renewRelativeVO) {
        long recognitionId = renewRelativeVO.getRecognitionId();
        long studentId = renewRelativeVO.getStudentId();
        List<RecognitionStudentVO> recognitionCards = recognitionStudentManager.getRecognitionStudentCards(recognitionId, studentId);
        if (CollectionUtils.isEmpty(recognitionCards)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "找不到答题卡信息");
        }

        long toStudentId = renewRelativeVO.getToStudentId();
        List<Long> recognitionCardIds = recognitionCards.stream().map(RecognitionStudentVO::getRecognitionCardId).collect(toList());
        recognitionStudentManager.updateRelativeStudentId(recognitionId, recognitionCardIds, toStudentId);
        int repeatNum = recognitionStudentManager.getRepeatNum(recognitionId, toStudentId);
        recognitionStudentManager.updateRepeatNum(recognitionId, toStudentId, repeatNum);
        // 判断relativeStudentId是否有变化.
        long oldRelativeStudentId = recognitionCards.get(0).getRelativeStudentId();
        if (oldRelativeStudentId != 0) {
            repeatNum = recognitionStudentManager.getRepeatNum(recognitionId, oldRelativeStudentId);
            if (repeatNum > 0) {
                recognitionStudentManager.updateRepeatNum(recognitionId, oldRelativeStudentId, repeatNum);
            }
        }

    }


    // 阅卷完成才可以处理完成， 要检查关联答题卡的唯一性
    @DistributeLock(moduleName="RENEW_RECOGNITION", name = "completeRelativeStudent", argValueKeys = {"[0].recognitionId"}, expireTime = 600)
    @Transactional(ExamRepository.TRANSACTION)
    public void handleCompleteRelativeStudent(Map<String, Object> params) {
        // 没有进行中的扫描任务
        Verify.of(params)
                .isValidId("recognitionId")
                .verify();

        long recognitionId = MapUtil.getLong(params, "recognitionId");
        RecognitionVO recognitionVO = recognitionManager.getRecognition(recognitionId);
        if (recognitionVO.getRecognitionStatus() == RecognitionStatusEnum.COMPLETED.getStatus()) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已经完成,请勿重复完成");
        }

        int other = recognitionManager.getOtherProgressingRecognitionCount(recognitionVO.getExamId(), recognitionVO.getPaperId(), recognitionId);
        if (other > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "还有未完成异常处理任务");
        }

        // 检查与正向答题卡的关联答题卡的唯一性
        int notMatchCount = recognitionStudentManager.getNotMatchCount(recognitionId);
        if (notMatchCount > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "异常任务里的学生答题卡与正向学生答题卡关联关系不一致！");
        }

        int count = newExamUploaderManager.getProgressingExamUploaderCount(recognitionVO.getExamId(), recognitionVO.getPaperId());
        if (count > 0) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "还有未完成扫描任务");
        }

        ExamPaperInfoVO examPaperInfoVO = examPaperManager.findByExamIdAndPaperId(recognitionVO.getExamId(), recognitionVO.getPaperId());
        if (examPaperInfoVO == null) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "找不到阅卷信息");
        }
        int examPaperStatus = examPaperInfoVO.getExamPaperStatus();
        if (examPaperStatus < 20) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "还没完成阅卷");
        }

        // 检查答题卡的唯一性
        List<Long> repeatStudentIds = recognitionStudentManager.getRepeatStudentIds(recognitionId);
        if (!CollectionUtils.isEmpty(repeatStudentIds)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "有重复答题卡，请检查");
        }

        // 验证重新关联是否一致.
        Map<String, List<Long>> outInfo = checkRelativeStudentIdIsUnique(recognitionId);

        List<Long> modifyStudentIds = recognitionStudentManager.getModifyStudentIds(recognitionId);
        if (CollectionUtils.isEmpty(modifyStudentIds)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "考号与学生关联一致，没必要完成");
        }

        List<Long> qns = newPlanExamUploaderService.getQuestionNumbers(recognitionVO.getPaperId());
        // 将exam_item  student_id  的关联关系， 移动到exam_item_student_id
        cardExamItemManager.updateExamItemStudentIdToNegative(recognitionId, modifyStudentIds,qns);
        // 将studentId改成正常及classId

        List<Long> relativeStudentIds = recognitionStudentManager.getRelativeStudentIdsByNormalStudentIds(recognitionId, modifyStudentIds);
        cardExamItemManager.updateExamItemStudentIdToPositive(recognitionId, relativeStudentIds, qns);
        //将答题卡改成正常。
        answerCardManager.updateStudentIdByRecognitionId(recognitionId, modifyStudentIds);

        List<Long> outRelativeStuIds = outInfo.get("outRelativeStuIds");
        List<Long> outNormalStuIds = outInfo.get("outNormalStuIds");
        // 将正向答题卡关联 （A, B） => (C, B)集合
        // outRelativeStuIds (C), outNormalStuIds (A)
        if (!CollectionUtils.isEmpty(outRelativeStuIds)) {
            long examId = recognitionVO.getExamId();
            long paperId = recognitionVO.getPaperId();
            newExamResultService.updateUploadedStatusExamIdAndPaperIdAndSchoolAndStudents(examId, paperId, 1,null, outRelativeStuIds);
            // 防止缺考状态没被重置.
            newExamResultManager.updateExamResultStatus(examId, paperId, 0, outRelativeStuIds);
        }

        if (!CollectionUtils.isEmpty(outNormalStuIds)) {
            newExamResultService.updateUploadedStatusExamIdAndPaperIdAndSchoolAndStudents(recognitionVO.getExamId(), recognitionVO.getPaperId(), 0,null, outNormalStuIds);
        }

        recognitionManager.updateRecognitionStatus(recognitionId, RecognitionStatusEnum.COMPLETED.getStatus());
        // 刷新报告.
        params.put("examPaperId", Collections.singletonList(examPaperInfoVO.getExamPaperId()));
        examCompleteAgainService.invokeExamCompleteAgain(params);
    }

    private Map<String, List<Long>> checkRelativeStudentIdIsUnique(long recognitionId) {
        List<Long> normalStudentIds = recognitionStudentManager.getCardStudentIds(recognitionId);
        List<Long> relativeStudentIds = recognitionStudentManager.getRelativeStudentIds(recognitionId, null);

        if (normalStudentIds.size() != relativeStudentIds.size()) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "重新关联与正向关联学生份数不一致");
        }

        Map<Long, List<Long>> normalStudentIdInfo = normalStudentIds.stream().collect(groupingBy(l -> l));
        Map<Long, List<Long>> relativeStudentIdInfo = relativeStudentIds.stream().collect(groupingBy(l -> l));
        // 异常卷学生集合不在正向识别的池子的学生的集合，需要判断该学生是否在正向识别的池子里
        List<Long> outRelativeStuIds = new ArrayList<>();
        for(Long relativeStudentId : relativeStudentIds) {
            if (!normalStudentIdInfo.containsKey(relativeStudentId)) {
                outRelativeStuIds.add(relativeStudentId);
            }
        }

        List<Long> outNormalStuIds = new ArrayList<>();
        for(Long studentId : normalStudentIds) {
            if (!relativeStudentIdInfo.containsKey(studentId)) {
                outNormalStuIds.add(studentId);
            }
        }


        // 如果正向答题卡存在的话，那么就是有问题的
        if (!CollectionUtils.isEmpty(outRelativeStuIds)) {
            RecognitionVO recognitionVO = recognitionManager.getRecognition(recognitionId);
            List<Long> studentIds = answerCardManager.getAnswerCardStudentIds(recognitionVO.getExamId(), recognitionVO.getPaperId(), outRelativeStuIds);
            if (!CollectionUtils.isEmpty(studentIds)) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "异常卷关联的答题卡与正常答题卡关联冲突，请联系系统管理员！");
            }
        }

        Map<String, List<Long>> outInfo = MapUtil.of("outRelativeStuIds", outRelativeStuIds, "outNormalStuIds", outNormalStuIds);
        return outInfo;
    }

    public ResultListVO<RecognitionStudentVO> getCompleteStudentList(RecognitionStuVO recognitionStuVO) {
        ResultListVO<RecognitionStudentVO> resultListVO = new ResultListVO<>();
        recognitionStuVO.setRecognitionTemplateId(0);
        recognitionStuVO.setNumStatus(0);
        RecognitionVO recognitionVO = recognitionManager.getRecognition(recognitionStuVO.getRecognitionId());
        int totalCount = newExamResultManager.getExamResultCount(recognitionStuVO);
        List<RecognitionStudentVO> studentVOList = newExamResultManager.getExamResultList(recognitionStuVO);
        List<Long> studentIds = studentVOList.stream().map(RecognitionStudentVO::getStudentId).collect(toSet()).stream().collect(toList());
        List<Long> relativeStudentIds = recognitionStudentManager.getRelativeStudentIds(recognitionStuVO.getRecognitionId(), studentIds);
        studentVOList.forEach(studentVO -> {
            studentVO.setRelativeStatus(relativeStudentIds.contains(studentVO.getStudentId()) ? 1 : 0);
        });
        resultListVO.setList(studentVOList);
        resultListVO.setTotalCount(totalCount);
        return resultListVO;
    }

    @DistributeLock(moduleName="RENEW_RECOGNITION", name = "relativeStudent", argValueKeys = {"[0].recognitionId"}, expireTime = 600)
    @Transactional(ExamRepository.TRANSACTION)
    public void batchRelativeStudent(RecognitionStuVO recognitionStuVO) {
        // 找到对应的学生
        List<ExamResultBO> examResultBOList = recognitionStudentManager.getExamResultList(recognitionStuVO.getRecognitionId());
        recognitionStuVO.setJoinExt(1);
        List<RecognitionStudentVO> recognitionStudentVOS = recognitionStudentManager.getRecognitionStudentCardList(recognitionStuVO);
        Map<String, List<ExamResultBO>> studentExamNumInfo = examResultBOList.stream().collect(groupingBy(ExamResultBO::getStudentExamNum));
        Map<String, List<ExamResultBO>> nameInfo = examResultBOList.stream().collect(groupingBy(ExamResultBO::getStudentName));

        for (RecognitionStudentVO recognitionStudentVO : recognitionStudentVOS) {
            int numStatus = recognitionStudentVO.getNumStatus();
            if (numStatus == 1) {
                continue;
            }

            for (int value : recognitionStuVO.getSortNums()) {
                if (numStatus == 1) {
                    break;
                }
                List<ExamResultBO> students = getExamResultBO(recognitionStudentVO, value, studentExamNumInfo, nameInfo);
                int size = CollectionUtils.isEmpty(students) ? 0 : students.size();
                if (size == 0) {
                    continue;
                }
                if (size == 1) {
                    numStatus = 1;
                    recognitionStudentVO.setRelativeStudentId(students.get(0).getStudentId());
                    recognitionStudentVO.setNumStatus(numStatus);
                } else {
                    ExamResultBO resultBO = students.stream().filter(x -> x.getStudentId() == recognitionStudentVO.getStudentId()).findFirst().orElse(null);
                    if (resultBO != null) {
                        numStatus = 1;
                        recognitionStudentVO.setRelativeStudentId(resultBO.getStudentId());
                        recognitionStudentVO.setNumStatus(numStatus);
                    }
                }
            }

            if (numStatus != 1) {
                recognitionStudentVO.setRelativeStudentId(0);
                recognitionStudentVO.setNumStatus(-1);
            }
        }

        Map<Long, List<RecognitionStudentVO>> studentIdInfo = recognitionStudentVOS.stream().filter(x -> x.getNumStatus() == 1).collect(groupingBy(RecognitionStudentVO::getRelativeStudentId));
        for(RecognitionStudentVO recognitionStudentVO : recognitionStudentVOS) {
            if (recognitionStudentVO.getNumStatus() == 1) {
                List<RecognitionStudentVO> relativeCardList = studentIdInfo.get(recognitionStudentVO.getRelativeStudentId());
                int studentCount = relativeCardList.stream().map(RecognitionStudentVO::getStudentId).collect(toSet()).size();
                recognitionStudentVO.setRepeatNum(studentCount);
                if (studentCount > 1) {
                    recognitionStudentVO.setNumStatus(-1);
                }
            }
        }

        recognitionStudentManager.insertRecognitionCardExtList(recognitionStudentVOS);
    }

    @Transactional(ExamRepository.TRANSACTION)
    public void initRecognitionExtStudentInfo(long recognitionId, long recognitionTemplateId) {
        // 重新设置关联关系，绘制了模板重新识别.
        if (recognitionTemplateId != 0) {
            recognitionStudentManager.renewRelativeStudentId(recognitionId, recognitionTemplateId);
            return;
        }

        // 新建的时候，插入数据
        RecognitionStuVO recognitionStuVO = new RecognitionStuVO();
        recognitionStuVO.setRecognitionId(recognitionId);
        recognitionStuVO.setJoinExt(0);
        List<RecognitionStudentVO> recognitionStudentVOS = recognitionStudentManager.getRecognitionStudentCardList(recognitionStuVO);
        BatchDataUtil.execute(recognitionStudentVOS, cards -> {
            cards.forEach(card -> {
                card.setRepeatNum(1);
                card.setNumStatus(1);
                card.setRelativeStudentId(card.getStudentId());
                card.setBarNum(DEFAULT_STU_NO);
                card.setFillingNum(DEFAULT_STU_NO);
                card.setHandNum(DEFAULT_STU_NO);
                card.setQrNum(DEFAULT_STU_NO);
                card.setHandName(StringUtils.EMPTY);
            });
            recognitionStudentManager.insertRecognitionCardExtList(cards);
        });
    }

    private List<ExamResultBO> getExamResultBO(RecognitionStudentVO recognitionStudentVO, int value, Map<String, List<ExamResultBO>> studentExamNumInfo, Map<String, List<ExamResultBO>> nameInfo) {
        String studentExamNum = null;
        String studentName = null;
        switch (value) {
            case 1:
                studentExamNum = recognitionStudentVO.getBarNum();
                break;
            case 3:
                studentExamNum = recognitionStudentVO.getFillingNum();
                break;
            case 4:
                studentExamNum = recognitionStudentVO.getHandNum();
                break;
            case 5:
                studentName = recognitionStudentVO.getHandName();
                break;
            default:
                studentExamNum = recognitionStudentVO.getQrNum();
        }

        if (StringUtil.isNotBlank(studentExamNum)) {
            return studentExamNumInfo.get(studentExamNum);
        }
        if (StringUtil.isNotBlank(studentName)) {
            return nameInfo.get(studentName);
        }
        return null;
    }
}
