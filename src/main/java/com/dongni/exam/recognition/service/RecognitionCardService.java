package com.dongni.exam.recognition.service;

import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.common.mongo.Order;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.lock.DistributeLockHelper;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.service.AnswerCardManualService;
import com.dongni.exam.card.service.AnswerCardService;
import com.dongni.exam.common.mark.enums.UnitTypeEnum;
import com.dongni.exam.common.mark.serivice.mark.IPaperReadService;
import com.dongni.exam.common.mark.serivice.mark.IQnMappingClientService;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.PaperReadVO;
import com.dongni.exam.common.mark.vo.QnMappingVO;
import com.dongni.exam.common.mark.vo.QuestionStructureVO;
import com.dongni.exam.newcard.parse.enumeration.RecognitionTypeEnum;
import com.dongni.exam.newcard.parse.utils.AnswerCardRecognitionUtil;
import com.dongni.exam.newcard.parse.utils.AnswerCardUtils;
import com.dongni.exam.plan.service.NewPlanExamUploaderService;
import com.dongni.exam.recognition.bean.ExamQuestionVO;
import com.dongni.exam.recognition.bean.dto.RecognitionQuestionDTO;
import com.dongni.exam.recognition.bean.vo.RecognitionTemplateStuExpVO;
import com.dongni.exam.recognition.dao.RecognitionCardDao;
import com.dongni.exam.recognition.dao.RecognitionItemDao;
import com.dongni.exam.recognition.dao.RecognitionQuestionDao;
import com.dongni.exam.recognition.enums.RenewRecognitionTypeEnum;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.AnswerCardTemplateManager;
import com.dongni.tiku.manager.impl.AnswerCardTemplateManualManager;
import com.dongni.tiku.manager.impl.RecognitionQuestionMappingManager;
import com.dongni.tiku.own.service.AnswerCardTemplateService;
import com.dongni.tiku.own.service.OwnAnswerCardService;
import com.dongni.tiku.own.service.OwnPaperService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.*;
import static java.util.stream.Collectors.*;

/**
 * @description: 异常答题卡处理
 * @author: Jianfeng
 * @create: 2019-07-12 14:59
 **/
@Service
public class RecognitionCardService {

    private final static Logger LOGGER = LoggerFactory.getLogger(RecognitionCardService.class);

    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private OwnAnswerCardService ownAnswerCardService;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private CommonStudentService commonStudentService;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private RecognitionSplitService recognitionSplitService;
    @Autowired
    private AnswerCardTemplateService answerCardTemplateService;
    @Autowired
    private AnswerCardService answerCardService;
    @Autowired
    private TikuMongodb tikuMongodb;
    @Autowired
    private IPaperReadService paperReadService;
    @Autowired
    private IQsClientService iQsClientService;

    @Autowired
    private IQnMappingClientService qnMappingClientService;

    @Autowired
    private RecognitionQuestionMappingManager recognitionQuestionMappingManager;

    @Autowired
    private AnswerCardTemplateManager answerCardTemplateManager;

    @Autowired
    private AnswerCardTemplateManualManager answerCardTemplateManualManager;

    /**
     * 获取异常卷识别信息
     * @param params recognitionId
     * @return recognitionId, examId, paperId, recognitionType, recognitionStatus
     */
    public Map<String, Object> getRecognition(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionId")
                .verify();
        Map<String, Object> recognition = examRepository.selectOne("RecognitionCardMapper.getRecognition", params);
        if (MapUtils.isEmpty(recognition)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "异常处理不存在");
        }
        return recognition;
    }

    private boolean isBlock(int originQn, List<PaperReadVO> paperReadVOS) {
        Optional<PaperReadVO> first = paperReadVOS.stream().filter(x -> x.getQuestionNumber() == originQn).findFirst();
        if(!first.isPresent()){
            return false;
        }
        long readBlockId =  first.get().getReadBlockId();
        long count = paperReadVOS.stream().filter(x -> x.getReadBlockId() == readBlockId).count();
        return count > 1;
    }

    /**
     * @Description: 判断试卷是否能进行解答题拆分
     * @Param:  paperId
     */
    public void newRecognitionCheck(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotBlank("recognitionType")
                .verify();
        Integer recognitionType = Integer.valueOf(params.get("recognitionType").toString());
        Long examId = Long.valueOf(params.get("examId").toString());
        Long paperId = Long.valueOf(params.get("paperId").toString());
        // 获取试卷被用以几场考试
        long count = examRepository.selectOne("RecognitionCardMapper.getPaperExamCount", params);
        if (count > 1) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷被多场考试使用，无法拆分");
        }
        newPlanExamUploaderService.checkExamUploaderProgressing(examId, paperId, recognitionType);

        if(recognitionType==3 || recognitionType==7 || recognitionType==8) {
            List<Map> templateInfoList = examRepository.selectList("ExamUploaderTemplateMapper.getExamTemplateInfo", params);
            if (CollectionUtils.isNotEmpty(templateInfoList)) {
                for (Map<String, Object> templateInfo : templateInfoList) {
                    int templateType = Integer.valueOf(templateInfo.get("templateType").toString());
                    if (templateType == 1) {
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "该场考试包含手阅模板，不支持试题拆分/合并/新增！");
                    }
                }
            }
            List<Document> templateList = ownAnswerCardService.getManualAnswerCardTemplateByExamIdAndPaperId(params);
            for (Document template : templateList) {
                List<Document> list = template.get("answerCardTemplate", Document.class).get("template", List.class);
                for (Document doc : list) {
                    List<Document> cardScoreList = doc.get("cardScore", List.class);
                    if (CollectionUtils.isNotEmpty(cardScoreList)) {
                        throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "该场考试包含手阅模板，不支持试题拆分/合并/新增！");
                    }
                }
            }
        }
    }
    /**
     * @Description: 新增答题卡重新识别记录（试题结构解耦新版）
     */
    @Transactional(ExamRepository.TRANSACTION)
    public Map newInsertRecognition(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionType")
                .verify();

        long examId = Long.valueOf(params.get("examId").toString());
        long paperId = Long.valueOf(params.get("paperId").toString());


        // 检验试卷解答题是否可以被拆分
        // 获取试卷被用以几场考试
        long count = examRepository.selectOne("RecognitionCardMapper.getPaperExamCount", params);
        if (count > 1) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷被多场考试使用，无法进行拆分/合并/新增");
        }

        //recognitionType: 3-试题拆分，7-试题合并，8-试题新增
        if(params.get("questionList")!=null){
            List<Map> questionMappings = (List<Map>) params.get("questionList");
            List<QuestionStructureVO> questionStructureVOS = iQsClientService.listQuestionStructure(paperId);
            Map<Integer, QuestionStructureVO> questionStructureVOMap = questionStructureVOS.stream().collect(toMap(v -> v.getQuestionNumber(), v -> v));
            for( Map questionMap:questionMappings ) {
                if(questionMap!=null && questionMap.get("list")!=null){
                    List<Map> originQuestionList =  (List<Map>) questionMap.get("list");
                    BigDecimal scoreValue = new BigDecimal(0.0d);
                    boolean firstFlag = true;
                    for( Map originQuestionMap:originQuestionList ) {
                        Integer oriQuestionNum = Integer.valueOf(originQuestionMap.get("questionNumber").toString());
                        List<PaperReadVO> paperReadVOS = paperReadService.listByExamIdAndPaperId(examId, paperId);
                        boolean isBlock = isBlock(oriQuestionNum, paperReadVOS);
                        if (isBlock) {
                            throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                                    "被拆分/合并的题目（第【" + questionMap.get("structureNumber") + "】题）被设置为组合阅卷，不允许拆分，请先解除组合阅卷");
                        }
                        if(questionStructureVOMap.get(oriQuestionNum)!=null){
                            QuestionStructureVO oldQsVO = questionStructureVOMap.get(oriQuestionNum);
                            scoreValue = scoreValue.add(oldQsVO.getScoreValue());
                            if(firstFlag){
                                questionMap.put("questionIndex",oldQsVO.getQuestionIndex());
                                questionMap.put("questionType",oldQsVO.getQuestionType());
                                questionMap.put("questionTypeName",oldQsVO.getQuestionTypeName());
                                questionMap.put("courseId", oldQsVO.getCourseId());
                                questionMap.put("paperId",oldQsVO.getPaperId());
                                questionMap.put("courseName",oldQsVO.getCourseName());
                                questionMap.put("halfRight",oldQsVO.getHalfRight());
                                questionMap.put("readType",oldQsVO.getReadType());
                                questionMap.put("unitType",oldQsVO.getUnitType());
                                firstFlag = false;
                            }
                        }
                    }
                    if(!questionMap.containsKey("scoreValue")){
                        questionMap.put("scoreValue", scoreValue);
                    }
                }
            }
        }

        List<Map<String, Object>> recognitionList = examRepository.selectList("RecognitionCardMapper.getUnFinishRecognition", params);
        List<Map<String, Object>> subjRenewRecogitionList = recognitionList.stream().filter(r -> AnswerCardRecognitionUtil.isSubjectRenewRecognition(r)).collect(toList());
        // 如果有解答题拆分存在的话，或者新异常卷处理任务是解答题拆分的话，那么就不允许新任务进来：
        int recognitionType = MapUtils.getInteger(params, "recognitionType");
        newPlanExamUploaderService.checkExamUploaderProgressing(examId, paperId, recognitionType);
        if (subjRenewRecogitionList.size() > 0 && (recognitionType == 3 || recognitionType == 7 || recognitionType == 8)) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前有主观题异常处理任务正在进行中，请先处理完成异常处理任务（检查试题设置和工具箱-异常卷处理任务）");
        }

        boolean isHavingStructureModifyTask = recognitionList.stream().filter(item ->
                 AnswerCardRecognitionUtil.isSubjectSplitMergeAddRecognition(item) ).count() > 0;
        if(isHavingStructureModifyTask && recognitionType!=2) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前异常卷有试题拆分/合并/新增，请先处理完成（检查试题设置）");
        }
        for(Map<String, Object> recognition: recognitionList) {
            if(AnswerCardRecognitionUtil.isSameSubjTypeRenewRecognitionExist(recognition,recognitionType)){
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前有相同类型的异常处理任务正在进行中，请先处理完成该异常处理任务");
            }
        }
        List<Long> classIdList = null;
        examRepository.insert("RecognitionCardMapper.insertRecognition", params);
        examRepository.insert("RecognitionCardMapper.insertRecognitionQuestion", params);
        // 如果是解答题拆分/合并/新增需要初始化所有学生
        if (
                ObjectUtil.isValueEquals(params.get("recognitionType"), 3)
                || ObjectUtil.isValueEquals(params.get("recognitionType"), 7)
                || ObjectUtil.isValueEquals(params.get("recognitionType"), 8)
        ) {

            classIdList = examRepository.selectList("RecognitionCardMapper.getClassByAnswerCard", params);
            params.put("classIdList", classIdList);
            classIdList = updateAnswerCardClassIds(params, classIdList);
            List studentInfoList = (List) getAnswerCardAllStudentList(params).get("studentInfoList");
            if(CollectionUtils.isEmpty(studentInfoList)){
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前可以处理的学生答卷数量为0，请扫描完成后再处理");
            }
//            params.put("studentList", studentInfoList);
        }

        if(classIdList ==null) {
            examRepository.insert("RecognitionCardMapper.insertRecognitionStudent", params);
        }else{
            List<List<Long>> lists = AnswerCardUtils.splitList(classIdList, 100);
            Map<String, Object> map =new HashMap<>(params);
            for(List<Long> classIds : lists) {
                map.put("classIdList", classIds);
                examRepository.insert("RecognitionCardMapper.insertRecognitionStudentByClassIds", map);
            }
        }
        examRepository.insert("RecognitionCardMapper.insertRecognitionTemplate", params);

        if(CollectionUtils.isNotEmpty(classIdList)){
            params.remove("studentList");
        }
        //保存questionNumber对应关系
        recognitionSplitService.insertMongoDataByRecognitionId(params, "recognitionQuestionMapping", params );
        List<Map<String, Object>> recognitionTemplateList = examRepository.selectList("RecognitionCardMapper.getRecognitionTemplateId", params);
        final List finalClassIds = classIdList;
        recognitionTemplateList.forEach(a->{
            params.putAll(a);
            if(finalClassIds == null) {
                examRepository.insert("RecognitionCardMapper.insertRecognitionCard", params);
            }else{
                List<List<Long>> lists = AnswerCardUtils.splitList(finalClassIds, 100);
                Map<String, Object> map =new HashMap<>(params);
                for(List<Long> classIds : lists) {
                    map.put("classIdList", classIds);
                    examRepository.insert("RecognitionCardMapper.insertRecognitionCard", map);
                }
            }
            // 解答题拆分保存mongo，已废弃
            if (!ObjectUtil.isBlank(params.get("recognitionPaper"))) {
                recognitionSplitService.answerQuestionSplit(params);
            }
        });



        return MapUtil.of("recognitionId", params.get("recognitionId"));
    }

    public List<Long> updateAnswerCardClassIds(Map<String, Object> params, List<Long> classIdList) {
        Integer classIdChangedStuCount = examRepository.selectOne("RecognitionCardMapper.getClassIdChangedStuCount", params);
        if(classIdChangedStuCount!=null && classIdChangedStuCount >0){
            classIdList =  examRepository.selectList("RecognitionCardMapper.getRecognitionNewClassIds", params);;
            examRepository.update("RecognitionCardMapper.updateAnswerCardClassIds" , params);
            params.put("classIdList", classIdList);
            params.put("classIds", StringUtils.join(classIdList, ","));
        }
        return classIdList;
    }

    /**
     * @Description: 新增答题卡重新识别记录
     */
    @Transactional(ExamRepository.TRANSACTION)
    @Deprecated
    public Map insertRecognition(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionType")
                .verify();

        long examId = Long.valueOf(params.get("examId").toString());
        long paperId = Long.valueOf(params.get("paperId").toString());

        if (params.get("studentList") != null && params.get("classIds") != null) {
            //base by students
            params.remove("classIds");
        }

        List<Long> classIdList;
        if (!ObjectUtil.isBlank(params.get("classIds"))) {
            classIdList = Arrays.stream(params.get("classIds").toString().split(",")).map(Long::parseLong).collect(Collectors.toList());
        } else {
            classIdList = null;
        }

        if (CollectionUtils.isNotEmpty(classIdList)) {
            params.put("classIdList", classIdList);
        }

        if (params.get("originQuestion") != null) {
            List<Map> originQuestions = (List<Map>) params.get("originQuestion");
            for (Map originQuestionMap : originQuestions) {
                if (originQuestionMap != null && originQuestionMap.get("questionNumber") != null) {
                    Integer oriQuestionNum = Integer.valueOf(originQuestionMap.get("questionNumber").toString());
                    List<PaperReadVO> paperReadVOS = paperReadService.listByExamIdAndPaperId(examId, paperId);
                    boolean isBlock = isBlock(oriQuestionNum, paperReadVOS);
                    if (isBlock) {
                        throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                                "被拆分的题目（第【" + originQuestionMap.get("structureNumber") + "】题）被设置为组合阅卷，不允许拆分，请先解除组合阅卷");
                    }
                }
            }
        }

        List<Map<String, Object>> recognitionList = examRepository.selectList("RecognitionCardMapper.getUnFinishRecognition", params);
        List<Map<String, Object>> subjRenewRecogitionList = recognitionList.stream().filter(r -> AnswerCardRecognitionUtil.isSubjectRenewRecognition(r)).collect(toList());

        // 如果有解答题拆分存在的话，或者新异常卷处理任务是解答题拆分的话，那么就不允许新任务进来：
        int recognitionType = MapUtils.getInteger(params, "recognitionType");
        newPlanExamUploaderService.checkExamUploaderProgressing(examId, paperId, recognitionType);
        if (subjRenewRecogitionList.size() > 0 &&
                (recognitionType == RenewRecognitionTypeEnum.SPLIT_RENEW.getType()
                || recognitionType == RenewRecognitionTypeEnum.MERGE_RENEW.getType()
                || recognitionType == RenewRecognitionTypeEnum.ADD_RENEW.getType())) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前有主观题异常处理任务正在进行中，请先处理完成异常处理任务（检查试题设置和工具箱-异常卷处理任务）");
        }

        boolean isHavingStructureModifyTask = recognitionList.stream().filter(item ->
                (MapUtils.getInteger(item, "recognitionType") ==  RenewRecognitionTypeEnum.SPLIT_RENEW.getType()
                        || MapUtils.getInteger(item, "recognitionType") == RenewRecognitionTypeEnum.MERGE_RENEW.getType()
                        || MapUtils.getInteger(item, "recognitionType") == RenewRecognitionTypeEnum.ADD_RENEW.getType())).count() > 0;
        if (isHavingStructureModifyTask && recognitionType != 2) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前异常卷有试题拆分/合并/新增，请先处理完成（检查试题设置）");
        }

        for (Map<String, Object> recognition : recognitionList) {
            if (AnswerCardRecognitionUtil.isSameSubjTypeRenewRecognitionExist(recognition, recognitionType)) {
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前有相同类型的异常处理任务正在进行中，请先处理完成该异常处理任务");
            }
        }
        examRepository.insert("RecognitionCardMapper.insertRecognition", params);
        if (recognitionType != RenewRecognitionTypeEnum.STU_NO_RENEW.getType()) {
            examRepository.insert("RecognitionCardMapper.insertRecognitionQuestion", params);
        }
        // 如果是解答题拆分/合并/新增需要初始化所有学生
        if (ObjectUtil.isValueEquals(params.get("recognitionType"), RenewRecognitionTypeEnum.SPLIT_RENEW.getType())
                || ObjectUtil.isValueEquals(params.get("recognitionType"), RenewRecognitionTypeEnum.MERGE_RENEW.getType())
                || ObjectUtil.isValueEquals(params.get("recognitionType"), RenewRecognitionTypeEnum.ADD_RENEW.getType())
        ) {
            params.put("studentList", getAnswerCardAllStudentList(params).get("studentInfoList"));
        }
        if (CollectionUtils.isEmpty(classIdList)) {
            examRepository.insert("RecognitionCardMapper.insertRecognitionStudent", params);
        } else {
            classIdList = updateAnswerCardClassIds(params, classIdList);
            examRepository.insert("RecognitionCardMapper.insertRecognitionStudentByClassIds", params);
        }
        examRepository.insert("RecognitionCardMapper.insertRecognitionTemplate", params);

        if (CollectionUtils.isNotEmpty(classIdList)) {
            params.remove("studentList");
        }
        List<Map<String, Object>> recognitionTemplateList = examRepository.selectList("RecognitionCardMapper.getRecognitionTemplateId", params);
        recognitionTemplateList.forEach(a -> {
            params.putAll(a);
            examRepository.insert("RecognitionCardMapper.insertRecognitionCard", params);

            // 解答题拆分保存mongo
            if (!ObjectUtil.isBlank(params.get("recognitionPaper"))) {
                recognitionSplitService.answerQuestionSplit(params);
            }
        });

        long recognitionId = MapUtils.getLong(params, "recognitionId");
        recognitionExpStuNoService.initRecognitionExtStudentInfo(recognitionId, 0);
        return MapUtil.of("recognitionId", recognitionId);
    }

    /**
     * @Description: 新增答题卡重新识别记录
     */
    @Autowired
    private RecognitionErrorCodeService recognitionErrorCodeService;

    @Autowired
    private AnswerCardManualService answerCardManualService;

    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;

    @Autowired
    private RecognitionExpStuNoService recognitionExpStuNoService;

    @Transactional(ExamRepository.TRANSACTION)
    public Map insertManualRecognition(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionType")
                .verify();


        if(params.get("studentList")!=null && params.get("classIds")!=null){
            //base by students
            params.remove("classIds");
        }

        List<Long> classIdList;
        if (!ObjectUtil.isBlank(params.get("classIds"))) {
            classIdList = Arrays.stream(params.get("classIds").toString().split(",")).map(Long::parseLong).collect(Collectors.toList());
        }else{
            classIdList = null;
        }

        if(CollectionUtils.isNotEmpty(classIdList)){
            params.put("classIdList", classIdList);
        }

        List<Map<String, Object>> recognitionList = examRepository.selectList("RecognitionCardMapper.getUnFinishRecognition", params);
        List<Map<String, Object>> subjRenewRecogitionList = recognitionList.stream().filter(r -> AnswerCardRecognitionUtil.isSubjectRenewRecognition(r)).collect(toList());

        // 如果有解答题拆分存在的话，或者新异常卷处理任务是解答题拆分的话，那么就不允许新任务进来：
        int recognitionType = MapUtils.getInteger(params, "recognitionType");
        long examId = MapUtil.getLong(params, "examId");
        long paperId = MapUtil.getLong(params, "paperId");
        newPlanExamUploaderService.checkExamUploaderProgressing(examId, paperId, recognitionType);

        if (subjRenewRecogitionList.size() > 0 && (recognitionType == 3 || recognitionType == 7 || recognitionType == 8)) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前有主观题异常处理任务正在进行中，请先处理完成异常处理任务（检查试题设置和工具箱-异常卷处理任务）");
        }

        boolean isHavingStructureModifyTask = recognitionList.stream().filter(item ->
                (MapUtils.getInteger(item, "recognitionType")==3 || MapUtils.getInteger(item, "recognitionType")== 7
                        || MapUtils.getInteger(item, "recognitionType")== 8) ).count() > 0;
        if(isHavingStructureModifyTask && recognitionType!=2) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前异常卷有试题拆分/合并/新增，请先处理完成（检查试题设置）");
        }

        for(Map<String, Object> recognition: recognitionList) {
            if(AnswerCardRecognitionUtil.isSameSubjTypeRenewRecognitionExist(recognition,recognitionType)){
                throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "当前有相同类型的异常处理任务正在进行中，请先处理完成该异常处理任务");
            }
        }
        examRepository.insert("RecognitionCardMapper.insertRecognition", params);
        examRepository.insert("RecognitionCardMapper.insertRecognitionQuestion", params);
        // 如果是解答题拆分/合并/新增需要初始化所有学生
        if (
                ObjectUtil.isValueEquals(params.get("recognitionType"), 3)
                        || ObjectUtil.isValueEquals(params.get("recognitionType"), 7)
                        || ObjectUtil.isValueEquals(params.get("recognitionType"), 8)
        ) {
            params.put("studentList", getAnswerCardAllStudentList(params).get("studentInfoList"));
        }
        if(CollectionUtils.isEmpty(classIdList)) {
            examRepository.insert("RecognitionCardMapper.insertRecognitionStudent", params);
        }else{
            classIdList = updateAnswerCardClassIds(params, classIdList);
            examRepository.insert("RecognitionCardMapper.insertRecognitionStudentByClassIds", params);
            if(!params.containsKey("studentList")) {
                params.put("studentList", getAnswerCardAllStudentList(params).get("studentInfoList"));
            }
        }

        List<Map<String, Object>> templateNumbers = new ArrayList<>();
        // 非定位点的话，
        if(!ObjectUtil.isValueEquals(params.get("recognitionType"), 5)) {
            examRepository.insert("RecognitionCardMapper.insertManualRecognitionTemplate", params);
        } else {
            if(params.containsKey("studentList")) {
                List<Map<String, Object>> studentList = MapUtil.getListMap(params, "studentList");
                if(CollectionUtils.isNotEmpty(studentList)) {
                    BatchDataUtil.execute(studentList, stuList -> {
                        params.put("studentList", stuList);
                        List<Map<String, Object>> numbers = examRepository.selectList("RecognitionCardMapper.getExamPaperTemplateNumber", params);
                        for(Map<String, Object> it : numbers) {
                            Map<String, Object> t = templateNumbers.stream().filter(x -> MapUtil.getInt(x, "templateNumber") == MapUtil.getInt(it, "templateNumber")).findFirst().orElse(null);
                            if(t == null) {
                                templateNumbers.add(it);
                            }
                        }
                    });
                    params.put("studentList", studentList);
                }
            }
            params.put("templateNumbers", templateNumbers);
            templateNumbers.forEach(item -> item.put("templateCode", answerCardManualService.getTemplateCode()));
            examRepository.insert("RecognitionCardMapper.insertManualRecognitionTemplatePointException", params);
            recognitionErrorCodeService.saveErrorCode(params);
        }

        if(CollectionUtils.isNotEmpty(classIdList)){
            params.remove("studentList");
        }
        List<Map<String, Object>> recognitionTemplateList = examRepository.selectList("RecognitionCardMapper.getRecognitionTemplateId", params);
        if( ObjectUtil.isValueEquals(params.get("recognitionType"), 5)) {
           if(recognitionTemplateList.size() > 1) {
               List<Map<String, Object>> templateExamUploaderList = examRepository.selectList("RecognitionCardMapper.getExamPaperTemplateNumberExamUploader", params);
               Map<Integer, List<Map<String, Object>>> templateNumberExamUploaderListMap = templateExamUploaderList.stream().collect(groupingBy(item -> MapUtil.getInt(item, "templateNumber")));
               final List<Map<String, Object>> numbers = templateNumbers;
               recognitionTemplateList.forEach(a -> {
                   params.putAll(a);
                   Map<String, Object> templateNumberInfo = numbers.stream().filter(item -> MapUtil.getLong(item, "templateCode") == MapUtil.getLong(a, "templateCode")).findFirst().orElse(null);
                   if(templateNumberInfo != null) {
                       int templateNumber = MapUtil.getInt(templateNumberInfo, "templateNumber", 0);
                       if(templateNumberExamUploaderListMap.containsKey(templateNumber)) {
                           List<Long> examUploaderIds =  templateNumberExamUploaderListMap.get(templateNumber)
                                   .stream().map(item -> MapUtil.getLong(item, "examUploaderId")).collect(toList());
                           params.put("examUploaderIds",examUploaderIds);
                           examRepository.insert("RecognitionCardMapper.insertPointExceptionRecognitionCard", params);
                       }
                   }
               });
           } else {
               recognitionTemplateList.forEach(a -> {
                   params.putAll(a);
                   examRepository.insert("RecognitionCardMapper.insertPointExceptionRecognitionCard", params);
               });
           }
        } else {
            recognitionTemplateList.forEach(a->{
                    params.putAll(a);
                    examRepository.insert("RecognitionCardMapper.insertRecognitionCard", params);
            });
        }

        // 解答题拆分保存mongo
        if (!ObjectUtil.isBlank(params.get("recognitionPaper"))) {
            recognitionSplitService.answerQuestionSplit(params);
        }

        return MapUtil.of("recognitionId", params.get("recognitionId"));
    }


    @Resource
    private RecognitionCardDao recognitionCardDao;

    @Resource
    private RecognitionItemDao recognitionItemDao;

    /**
     * 删除异常卷重新识别
     *
     * @param params
     */
    public void deleteRecognition(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionId")
                .verify();

        Map<String, Object> recognition = getRecognition(params);
        if (ObjectUtil.isValueEquals(recognition.get("recognitionStatus"), DictUtil.getDictValue("recognitionStatus", "finish"))) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "已完成，无法删除");
        }

        long recognitionId = MapUtils.getLong(params, "recognitionId");
        // 删除模版数据
        answerCardTemplateService.deleteRecognitionTemplateAndManual(params);

        // 删除mysql数据
        examRepository.delete("RecognitionCardMapper.deleteRecognition", params);
        examRepository.delete("RecognitionCardMapper.deleteRecognitionCard", params);

        List<Long> studentIds = recognitionCardDao.getRecognitionStudents(recognitionId);
        if (CollectionUtils.isNotEmpty(studentIds)) {
            BatchDataUtil.execute(studentIds, x -> recognitionItemDao.deleteRecognitionItemByIds(recognitionId, x));
        }
        examRepository.delete("RecognitionCardMapper.deleteRecognitionQuestion", params);
        examRepository.delete("RecognitionCardMapper.deleteRecognitionStudent", params);
        examRepository.delete("RecognitionCardMapper.deleteRecognitionTemplate", params);
    }

    /**
     * 删除某个examId+paperId下所有的异常卷重新识别
     *
     *
     */
    public void deleteRecognitions(List<Long> recognitionIdList, Long examId, Long paperId) {

        for(Long recognitionId : recognitionIdList) {
            // 删除模版数据
            Map<String, Object> params1 = new HashMap<>();
            params1.put("recognitionId",recognitionId);
            answerCardTemplateService.deleteRecognitionTemplateAndManual(params1);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("recognitionIdList" , recognitionIdList);
        params.put("examId" , examId);
        params.put("paperId" , paperId);

        if(CollectionUtils.isNotEmpty(recognitionIdList)) {
            // 删除mysql数据
            examRepository.delete("RecognitionCardMapper.deleteRecognitions", params);
            examRepository.delete("RecognitionCardMapper.deleteRecognitionCards", params);
            examRepository.delete("RecognitionCardMapper.deleteRecognitionItems", params);
            examRepository.delete("RecognitionCardMapper.deleteRecognitionQuestions", params);
            examRepository.delete("RecognitionCardMapper.deleteRecognitionStudents", params);
            examRepository.delete("RecognitionCardMapper.deleteRecognitionTemplates", params);
        }
    }
    /**
    * @Description: 获取异常卷处理列表
    * @Param: examId paperId
    */
    public Map getRecognitionList(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();
        if(params.get("readType")!=null){
            int readType = Integer.parseInt(params.get("readType").toString());
            List<Integer> recognitionTypes = new ArrayList<>();
            if(readType == 1){
                recognitionTypes.add(2);
            }else if(readType == 2){
                recognitionTypes.add(1);
                recognitionTypes.add(3);
            }
            if(CollectionUtils.isNotEmpty(recognitionTypes)){
                params.put("recognitionTypes",recognitionTypes);
            }
        }
        if(params.get("recognitionTypes")!=null){
            String recognitionTypesStr = params.get("recognitionTypes").toString();
            String[] recognitionTypeArr = recognitionTypesStr.split(",");
            List<Integer> recognitionTypes = new ArrayList<>();
            for(String recognitionType: recognitionTypeArr){
                recognitionTypes.add(Integer.parseInt(recognitionType));
            }
            if(CollectionUtils.isNotEmpty(recognitionTypes)){
                params.put("recognitionTypes",recognitionTypes);
            }
        }
        Long totalCount = examRepository.selectOne("RecognitionCardMapper.getRecognitionListCount", params);
        if (totalCount == null || totalCount == 0) {
            return MapUtil.of("totalCount", totalCount ,"recognitionList" ,Collections.emptyList());
        }

        List<Map<String,Object>> recognitionList = examRepository.selectList("RecognitionCardMapper.getRecognitionList", params);

        return MapUtil.of("totalCount", totalCount, "recognitionList", recognitionList);
    }

    /**
    * @Description: 根据主客观提获取答题卡题目
    * @Param: paperId readType(1客观题 2主观题) [unitType] [recognitionType]
    */
    public List getAnswerCardQuestionList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
//                .isNotBlank("readType")
                .verify();

        // 获取试卷详情
//        Map<String, Object> paperDetail = ownPaperService.getPaperDetailNotKnowledge(params);
//        List<Map<String, Object>> questions = PaperUtil.getQuestions(paperDetail);
//        Map<String, Map<String, Object>> questionMap = questions.stream().collect(toMap(item -> item.get("_id").toString(), item -> item));
        Long paperId = Long.parseLong(params.get("paperId").toString());
        // 获取答题卡结构
        List<QuestionStructureVO> questionStructureVOS = iQsClientService.listQuestionStructure(paperId);
        List<Map<String, Object>> answerCardStructure = ownAnswerCardService.getAnswerCardStructure(params);
        Map<Integer,Integer> qn2OptionsCountMap = answerCardStructure
                .stream()
                .filter(item -> ObjectUtil.isValueEquals(item.get("readType"), 1))
                .collect(Collectors.toMap(item-> Integer.valueOf(item.get("questionNumber").toString()) ,
                        item->  Integer.valueOf(item.get("optionsCount").toString()) ));

        List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(paperId);
        Map<Integer, List<QnMappingVO>> markQn2PaperQn = qnMappingList.stream().collect(groupingBy(QnMappingVO::getMarkQn));

        Bson query = eq("paperId", paperId);
        Map<String, Object> answerCardTemplate = null;
        Map<String, Object> answerCardTemplateManual = answerCardTemplateManualManager.getFirst(query,  Order.Field.asc("createDateTime"));
        if (MapUtils.isEmpty(answerCardTemplateManual)) {
             answerCardTemplate = answerCardTemplateManager.getFirst(query);
        }else{
            answerCardTemplate = MapUtil.getCast(answerCardTemplateManual, "answerCardTemplate");
        }

        // list of raw score qn
        List<Integer> rawScoreContentQns  = new ArrayList<>();
        //raw score qn -> raw score content
        Map<Integer, ExamQuestionVO> rawScoreContentMap = new HashMap<>();
        List<ExamQuestionVO> examQuestionList = new ArrayList<>();
        for(QuestionStructureVO vo: questionStructureVOS){
            int paperQuestionNumber;
            if(markQn2PaperQn.get(vo.getQuestionNumber()) !=null){
                paperQuestionNumber = markQn2PaperQn.get(vo.getQuestionNumber()).get(0).getPaperQn();
            }else{
                paperQuestionNumber = vo.getQuestionNumber();
            }
            Integer optionsCount = qn2OptionsCountMap.get(paperQuestionNumber);
            if(optionsCount!=null){
                vo.setOptionsCount(optionsCount);
            }
            ExamQuestionVO examQuestionVO = new ExamQuestionVO(vo);
            if(vo.getUnitType() == UnitTypeEnum.WriteQN.getUnitType()){
                rawScoreContentQns.add(vo.getQuestionNumber());
                rawScoreContentMap.put(vo.getQuestionNumber(), examQuestionVO);
            }
            examQuestionList.add(examQuestionVO);
        }
        if(CollectionUtils.isNotEmpty(rawScoreContentQns)){
            Map<Integer,List<String>> rawScoreQn2SourceIds = new HashMap<>();
            Map<String, Map> contentId2ContentMap = new HashMap<>();
            if(answerCardTemplate!=null) {
                List<Map<String, Object>> templateList = MapUtil.getCast(answerCardTemplate, "template");
                if (CollectionUtils.isNotEmpty(templateList)) {
                    for (Map<String, Object> template : templateList) {
                        List<Map<String, Object>> cardContents = MapUtil.getCast(template, "cardContent");
                        if (CollectionUtils.isNotEmpty(cardContents)) {
                            for (Map<String, Object> cardContent : cardContents) {
                                int recognitionType = MapUtil.getCast(cardContent, "recognitionType");
                                int qn = MapUtil.getCast(cardContent, "questionNumber");
                                String contentId = MapUtil.getCast(cardContent, "id");
                                if (!RecognitionTypeEnum.isObjectiveItem(recognitionType)
                                        && rawScoreContentQns.contains(qn) ) {
                                    //卷面分
                                    String sourceContentId = MapUtil.getCast(cardContent, "sourceContentId");
                                    if (StringUtils.isNotBlank(sourceContentId)) {
                                        List<String> rawScoreSourceIds = rawScoreQn2SourceIds.get(qn);
                                        if (rawScoreSourceIds == null) {
                                            rawScoreSourceIds = new ArrayList<>();
                                            rawScoreQn2SourceIds.put(qn, rawScoreSourceIds);
                                        }
                                        rawScoreSourceIds.add(sourceContentId);
                                    }
                                }
                                contentId2ContentMap.put(contentId, cardContent);
                            }
                        }
                    }
                }
            }
            Set<Map.Entry<Integer, List<String>>> entries = rawScoreQn2SourceIds.entrySet();
            if(entries.size() > 0 ) {
                for (Map.Entry<Integer, List<String>> entry: entries) {
                    Set<Integer> rawScoreSourceQns = new HashSet<>();
                    Integer rawScoreQn = entry.getKey();
                    List<String> sourceContentIds = entry.getValue();
                    for( String sourceContentId : sourceContentIds) {
                        Map<String, Object> content = contentId2ContentMap.get(sourceContentId);
                        if (content != null) {
                            int qn = MapUtil.getCast(content, "questionNumber");
                            rawScoreSourceQns.add(qn);
                            if(content.get("questionNumbers") != null){
                                rawScoreSourceQns.addAll(MapUtil.getCast(content, "questionNumbers"));
                            }
                        }
                    }
                    ExamQuestionVO examQuestionVO = rawScoreContentMap.get(rawScoreQn);
                    examQuestionVO.setSourceQns(new ArrayList<>(rawScoreSourceQns));
                }

            }
        }


        // 根据主客观提获取答题卡题目
//        int splitRecognitionType = DictUtil.getDictValue("recognitionType", "split");
        List<QuestionStructureVO> answerCardQuestionList = null;
        if(params.get("readType")==null || StringUtils.isBlank(params.get("readType").toString())){
            answerCardQuestionList = examQuestionList.stream()
                    .filter(a -> {
//                        if (ObjectUtil.isValueEquals(params.get("recognitionType"), splitRecognitionType)) {
//                            // 解答题拆分，需要过滤掉选做题，只保留解答题
//                            if (a.get("isOptional") != null && Boolean.valueOf(a.get("isOptional").toString())) {
//                                return false;
//                            }
//                        }
                        if (ObjectUtil.isBlank(params.get("unitType"))) {
                            return true;
                        } else {
                            return ObjectUtil.isValueEquals(a.getUnitType(), params.get("unitType"));
                        }
                    })
                    .collect(Collectors.toList());
        }else {
            answerCardQuestionList = examQuestionList.stream()
                    .filter(a -> ObjectUtil.isValueEquals(a.getReadType(), params.get("readType")))
                    .filter(a -> {
//                        if (ObjectUtil.isValueEquals(params.get("recognitionType"), splitRecognitionType)) {
//                            // 解答题拆分，需要过滤掉选做题，只保留解答题
//                            if (a.get("isOptional") != null && Boolean.valueOf(a.get("isOptional").toString())) {
//                                return false;
//                            }
//                        }
                        if (ObjectUtil.isBlank(params.get("unitType"))) {
                            return true;
                        } else {
                            return ObjectUtil.isValueEquals(a.getUnitType(), params.get("unitType"));
                        }
                    })
                    .collect(Collectors.toList());
        }

//        if (CollectionUtils.isEmpty(answerCardQuestionList)) {
//            int readType = MapUtil.getInt(params, "readType", -1);
//            String message = "没有可拆的解答题 ，或解答题已拆过， 请到答题卡预览查看试卷的题型结构";
//            if(readType == 1) {
//                message = "没有客观题";
//            }
//            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, message);
//        }

//        // 添加小问数量
//        for (QuestionStructureVO item : answerCardQuestionList) {
//            if (!ObjectUtil.isBlank(item.getQuestionIndex())) {
//                Map<String, Object> question = questionMap.get(item.getQuestionIndex());
//                if (question.get("questions") != null) {
//                    item.put("problemCount", ((List)question.get("questions")).size());
//                }
//            }
//        }
        answerCardQuestionList.sort(Comparator.comparing(qs -> qs.getQuestionNumber()));
        return answerCardQuestionList;
    }

    /**
     * @Description: 根据学校筛选有答题卡的班级
     * @Param: examId paperId schoolIds
     */
    public List getAnswerCardClassList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotBlank("schoolIds")
                .verify();

        List schoolIdList = Arrays.asList(params.get("schoolIds").toString().split(","));
        params.put("schoolIdList", schoolIdList);

        List<Long> classIdList = examRepository.selectList("RecognitionCardMapper.getClassByAnswerCard", params);

        if (CollectionUtils.isEmpty(classIdList)) {
            return Collections.EMPTY_LIST;
        }
        params.put("classIds", classIdList);

        List<Map<String, Object>> examClassIds = examRepository.selectList("ExamClassMapper.getExamSchoolClassInfos", params);
        StringBuilder sb = new StringBuilder();
        classIdList.forEach(a->sb.append(a.toString()).append(","));
        params.put("classIds", sb.toString());

        List<Map<String, Object>> baseDataClassIds = commonClassService.getClassList(params);
        // 合并两个列表并去重（根据classId）
        List<Map<String, Object>> mergedClassIds = Stream.concat(
                examClassIds.stream(),
                baseDataClassIds.stream()
        )
                .collect(Collectors.toMap(
                        map -> map.get("classId").toString(), // 以classId作为唯一标识
                        map -> map,
                        (existing, replacement) -> existing   // 遇到重复时保留原有元素
                ))
                .values().stream().collect(Collectors.toList());
        return mergedClassIds;
    }

    /**
    * @Description: 根据班级筛选有答题卡的学生
    * @Param: examId paperId classIds
    */
    public Map getAnswerCardStudentList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNotBlank("classIds")
                .verify();

        List classIdList = Arrays.asList(params.get("classIds").toString().split(","));
        params.put("classIdList", classIdList);

        Long totalCount = examRepository.selectOne("RecognitionCardMapper.getStudentByAnswerCardCount", params);
        if (totalCount == null || totalCount == 0) {
            return MapUtil.of("totalCount", totalCount, "studentInfoList", Collections.emptyList());
        }

        // 查询出studentId
        List studentIdList = examRepository.selectList("RecognitionCardMapper.getStudentByAnswerCard", params);

        // 根据studentId获取学生信息
        params.put("studentIdList", studentIdList);
        List studentInfoList = commonStudentService.getStudentInfoList(params);

        return MapUtil.of("totalCount", totalCount, "studentInfoList", studentInfoList);
    }

    /**
     * @Description: 查询全部有答题卡的学生
     * @Param: examId paperId classIds
     */
    public Map getAnswerCardAllStudentList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();

        if (!ObjectUtil.isBlank(params.get("classIds"))) {
            List classIdList = Arrays.asList(params.get("classIds").toString().split(","));
            params.put("classIdList", classIdList);
        }

        // 查询出studentId
        List studentIdList = examRepository.selectList("RecognitionCardMapper.getStudentByAnswerCard", params);

        // 根据studentId获取学生信息
        params.put("studentIdList", studentIdList);
        List studentInfoList = new ArrayList();
        if(CollectionUtils.isNotEmpty(studentIdList)) {
            studentInfoList = examRepository.selectList("ExamMapper.getStudentSimpleInfoList", params);
        }
        return MapUtil.of("studentInfoList", studentInfoList);
    }

    /**
     * @Description: 查询全部有答题卡的学生
     * @Param: examId paperId classIds
     */
    public Map getCompletedAnswerCardAllStudentList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();

        if (!ObjectUtil.isBlank(params.get("classIds"))) {
            List classIdList = Arrays.asList(params.get("classIds").toString().split(","));
            params.put("classIdList", classIdList);
        }

        // 查询出studentId
        List studentIdList = examRepository.selectList("RecognitionCardMapper.getStudentByCompletedAnswerCard", params);

        // 根据studentId获取学生信息
        params.put("studentIdList", studentIdList);
        if(CollectionUtils.isEmpty(studentIdList)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"暂无学生信息");
        }
        List studentInfoList = commonStudentService.getStudentSimpleInfoList(params);

        return MapUtil.of("studentInfoList", studentInfoList);
    }
    /**
     * @Description: 查询全部做第三方答题卡识别的学生
     * @Param: examId paperId classIds
     */
    public Map getManualAnswerCardAllStudentList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();

        if (!ObjectUtil.isBlank(params.get("classIds"))) {
            List classIdList = Arrays.asList(params.get("classIds").toString().split(","));
            params.put("classIdList", classIdList);
        }

        // 查询出studentId
        List studentIdList = examRepository.selectList("RecognitionCardMapper.getStudentByManualAnswerCard", params);

        // 根据studentId获取学生信息
        params.put("studentIdList", studentIdList);
        List studentInfoList = commonStudentService.getStudentSimpleInfoList(params);

        return MapUtil.of("studentInfoList", studentInfoList);
    }


    @Resource
    private RecognitionQuestionDao recognitionQuestionDao;

    /**
    * @Description: 获取异常答题卡识别列表
    * @Param: recognitionId
    */
    @Transactional(ExamRepository.TRANSACTION)
    public Map getRecognitionCardList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionId")
                .verify();

        Long totalCount = examRepository.selectOne("RecognitionCardMapper.getRecognitionCardListCount", params);
        if (totalCount == null || totalCount == 0) {
            return MapUtil.of("totalCount", totalCount ,"recognitionCardList", Collections.emptyList());
        }

        // 重新试卷详情
        Map<String, Object> recognition = getRecognition(params);
        Long recognitionId = Long.parseLong(params.get("recognitionId").toString());
        Long paperId = Long.parseLong(params.get("paperId").toString());
//        Document recognitionPaper = tikuMongodb.getMongoDatabase().getCollection("recognitionPaper")
//                .find(eq("recognitionId", recognitionId)).first();
//        List<Map<String, Object>> answerCardStructure;
//        if (recognitionPaper != null) {
//            answerCardStructure =  PaperUtil.getPaperStructure(recognitionPaper);
//        } else {
//            answerCardStructure = ownAnswerCardService.getAnswerCardStructure(recognition);
//        }

        //获取试题映射关系
        Document recognitionQuestionMappingDoc = recognitionQuestionMappingManager.getFirst(eq("recognitionId", recognitionId));
        Map<Integer, Document> questionNumberMap = null;
        if(recognitionQuestionMappingDoc!=null) {
            List<Document> newQuestionList = recognitionQuestionMappingDoc.get("questionList", List.class);
            questionNumberMap = newQuestionList.stream()
                    .collect(toMap(item -> Integer.valueOf(item.get("questionNumber").toString()), item -> item));
        }else{
            List<QuestionStructureVO> questionStructureVOS = iQsClientService.listQuestionStructure(paperId);
            List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(paperId);
            Map<Integer, List<QnMappingVO>> markQn2PaperQn = qnMappingList.stream().collect(groupingBy(QnMappingVO::getMarkQn));

            List<Map<String, Object>> structureNumberList = ownPaperService.getPaperStructureById(paperId);
            Map<Integer,Integer> paperQn2OptionsCountMap = structureNumberList
                    .stream()
                    .filter(item -> ObjectUtil.isValueEquals(item.get("readType"), 1))
                    .collect(Collectors.toMap(item-> Integer.valueOf(item.get("questionNumber").toString()) ,
                            item->  Integer.valueOf(item.get("optionsCount").toString()) ));

            questionNumberMap = new HashMap<>();
            for(QuestionStructureVO vo: questionStructureVOS){
                Document questionDoc = new Document();
                questionDoc.put("readType", vo.getReadType());
                questionDoc.put("courseId", vo.getCourseId());
                questionDoc.put("courseName", vo.getCourseName());
                questionDoc.put("questionType", vo.getQuestionType());
                questionDoc.put("questionTypeName", vo.getQuestionTypeName());
                questionDoc.put("questionNumber", vo.getQuestionNumber());
                questionDoc.put("structureNumber", vo.getStructureNumber());
                questionDoc.put("scoreValue", vo.getScoreValue());
                questionDoc.put("unitType", vo.getUnitType());
                questionDoc.put("optionalGroup", vo.getOptionalGroup());
                questionDoc.put("optionalCount", vo.getOptionalCount());
                List<QnMappingVO> paperQnVOs = markQn2PaperQn.get(vo.getQuestionNumber());
                if(paperQnVOs!=null && paperQnVOs.size() > 0) {
                    int paperQn = paperQnVOs.get(0).getPaperQn();
                    Integer optionsCount = paperQn2OptionsCountMap.get(paperQn);
                    if (optionsCount == null) {
                        optionsCount = vo.getOptionsCount();
                    }
                    questionDoc.put("optionsCount", optionsCount);
                }
                questionNumberMap.put(vo.getQuestionNumber(), questionDoc);
            }
        }
        // 设置试题信息
        List<Map<String,Object>> recognitionCardList = examRepository.selectList("RecognitionCardMapper.getRecognitionCardList", params);
        // 追加试题信息；
        List<RecognitionQuestionDTO> qnDTOList = recognitionQuestionDao.getRQNList(recognitionId);
        recognitionCardList.forEach(x -> x.put("questionList", qnDTOList.stream().map(it -> {
            Map<String, Object> item = new HashMap<>();
            item.put("questionNumber", it.getQuestionNumber());
            item.put("structureNumber", it.getStructureNumber());
            return item;
        }).collect(toList())));

        if (!CollectionUtils.isEmpty(recognitionCardList) ) {

            for (Map<String, Object> item : recognitionCardList) {
                List<Map<String, Object>> questionList = (List<Map<String, Object>>) item.get("questionList");
                for (Map<String, Object> questionItem : questionList) {
                    if( questionNumberMap!=null) {
                        Document question = questionNumberMap.get(Integer.valueOf(questionItem.get("questionNumber").toString()));
                        if (question != null) {
                            question.remove("list");
                            questionItem.putAll(question);
                        }
                    }
                }
            }

            List<Map<String, Object>> recognitionTemplateList = examRepository.selectList("RecognitionTaskMapper.getRecognitionTemplateInfo", params);
            List<Map<String, Object>> taskInfoList = examRepository.selectList("RecognitionTaskMapper.getRecognitionTemplates", params);

            recognitionCardList.forEach(item -> {
                Long recognitionTemplateId = MapUtil.getLong(item, "recognitionTemplateId");
                // completed percent
                List<Map<String, Object>> results = recognitionTemplateList.stream()
                        .filter(tempInfo -> {
                            Long _id_ = MapUtil.getLong(tempInfo, "recognitionTemplateId");
                            return _id_.equals(recognitionTemplateId);
                        }).collect(toList());
                double percent = 0.0d;
                if (CollectionUtils.isNotEmpty(results)) {
                    int _tc_ = MapUtil.getInt(results.get(0), "totalCount", 1);
                    if (CollectionUtils.isNotEmpty(results)) {
                        percent += MapUtil.getInt(results.get(0), "completedCount") * 100.0 / _tc_;
                    }
                    for (Map<String, Object> taskInfo : taskInfoList) {
                        String taskId = MapUtil.getString(taskInfo, "taskId");
                        int taskCount = Integer.valueOf(taskId.split(":")[1]);
                        int cardCount = MapUtil.getInt(taskInfo, "cardCount");
                        int studentCount = MapUtil.getInt(taskInfo, "studentCount");
                        int templateNumber = cardCount / studentCount;
                        int recogzedCount = MapUtil.getInt(taskInfo, "recogzedCount");
                        percent += recogzedCount * 100.0 / taskCount / templateNumber / _tc_;
                    }
                }
                int recognitionTemplateStatus = MapUtil.getInt(item, "recognitionTemplateStatus", 1);
                if(recognitionTemplateStatus >=3 )
                    percent = 100;
                percent =  Math.round(percent);
                if(percent > 100) {
                    percent = 80;
                }
                // 由于并发导致更新状态异常，先做个紧急修复. @TODO
                updateRecognitionTemplateId(recognitionTemplateId, percent, recognitionTemplateStatus);
                item.put("p", percent);
                item.put("percent", percent + "%");
            });
        }

        recognition.putAll(MapUtil.copy(params, "userId", "userName"));
//        addPERecognitionCardFailTemplate(recognition, recognitionCardList);
        addRecognitionTemplateExamUploaderId(recognition, recognitionCardList);
        List<RecognitionTemplateStuExpVO> expVOS = recognitionExpStuNoService.getRecognitionTemplateStuExp(recognitionId);
        if (!CollectionUtils.isEmpty(expVOS)) {
            Map<Long, RecognitionTemplateStuExpVO> expVOMap = expVOS.stream().collect(Collectors.toMap(RecognitionTemplateStuExpVO::getRecognitionTemplateId, x -> x));
            recognitionCardList.forEach(item -> {
                RecognitionTemplateStuExpVO expVO = expVOMap.get(MapUtil.getLong(item, "recognitionTemplateId"));
                if (expVO != null) {
                    item.put("expStuExamNumCount", expVO.getExpStuExamNumCount());
                }
            });
        }
        return MapUtil.of("totalCount", totalCount, "recognitionCardList", recognitionCardList);
    }


    /**
     * @param recognitionTemplateId
     * @param percent
     * @param recognitionTemplateStatus
     */
    private void updateRecognitionTemplateId(long recognitionTemplateId, double percent, int recognitionTemplateStatus) {
        if (recognitionTemplateStatus > 2 || percent <= 95.0d)
            return;
        Map<String, Object> params = new HashMap<>();
        params.put("recognitionTemplateId", recognitionTemplateId);
        Map<String, Object> countInfo = examRepository.selectOne("RecognitionTaskMapper.getRecognitionTemplateCountInfo", params);
        int failCount = MapUtil.getInt(countInfo, "failCount", 0);
        int processCount = MapUtil.getInt(countInfo, "processCount", 0);
        if(processCount == 0) {
            params.put("recognitionTemplateStatus", failCount > 0 ? 3 : 4);
            examRepository.update("RecognitionCardMapper.updateRecognitionTemplateStatus", params);
        }
    }

    /**
    * @Description: 获取预览解答题列表
    * @Param: recognitionTemplateId examId paperId questionNumber
    */
    public Map getPreviewSubjectiveQuestionList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("questionNumber")
                .verify();

        Long totalCount = examRepository.selectOne("RecognitionCardMapper.getPreviewSubjectiveQuestionListCount", params);
        if (totalCount == null || totalCount == 0) {
            return MapUtil.of("totalCount", totalCount ,"recognitionCardList" , Collections.emptyList());
        }

        List previewSubjectiveQuestionList = examRepository.selectList("RecognitionCardMapper.getPreviewSubjectiveQuestionList", params);

        return MapUtil.of("totalCount", totalCount, "recognitionCardList", previewSubjectiveQuestionList);
    }

    /**
     * @Description: 获取预览选择题列表(按学生)
     * @Param:  recognitionTemplateId
     */
    public Map getPreviewObjectiveQuestionListByStudent(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .verify();

        Long totalCount = examRepository.selectOne("RecognitionCardMapper.getPreviewObjectiveQuestionListByStudentCount", params);
        if (totalCount == null || totalCount == 0) {
            return MapUtil.of("totalCount", totalCount , "recognitionCardList" ,Collections.emptyList());
        }

        List previewObjectiveQuestionList = examRepository.selectList("RecognitionCardMapper.getPreviewObjectiveQuestionListByStudent", params);

        return MapUtil.of("totalCount", totalCount, "recognitionCardList", previewObjectiveQuestionList);
    }

    /**
     * @Description: 获取预览选择题列表(按试题)
     * @Param:  recognitionTemplateId
     */
    public Map getPreviewObjectiveQuestionListByQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .verify();

        Long totalCount = examRepository.selectOne("RecognitionCardMapper.getPreviewObjectiveQuestionListByQuestionCount", params);
        if (totalCount == null || totalCount == 0) {
            return MapUtil.of("totalCount", totalCount , "recognitionCardList", Collections.emptyList());
        }

        List previewObjectiveQuestionList = examRepository.selectList("RecognitionCardMapper.getPreviewObjectiveQuestionListByQuestion", params);

        return MapUtil.of("totalCount", totalCount, "recognitionCardList", previewObjectiveQuestionList);
    }

    /**
     * 获取识别的答题卡
     *
     * @param params recognitionTemplateId
     * @return
     */
    public List<Map<String, Object>> getRecognitionCardByTemplate(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .verify();

        Integer pageNumber = examRepository.selectOne("RecognitionCardMapper.getRecognitionCardPageNumber", params);
        params.put("pageNumber", pageNumber);
        return examRepository.selectList("RecognitionCardMapper.getRecognitionCardByTemplate", params);
    }

    /**
     *  获取答题卡（非第三方）
     *
     * @param params recognitionTemplateId
     * @return [{}]  item为一个学生的一份卷子答题卡信息
     *     recognitionCardId, answerCardId, pageNumber, studentId, correctStatus
     *     filePathList   每一页的答题卡url
     */
    public List<Map<String, Object>> getRecognitionTemplateCard(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .verify();

        Integer pageNumber = examRepository.selectOne("RecognitionCardMapper.getRecognitionCardPageNumber", params);
        params.put("pageSize", pageNumber * 10);
        List<Map<String, Object>> cardList = examRepository.selectList("RecognitionCardMapper.getRecognitionTemplateCard", params);
        // 校正图片 item.filePath会被设置为新的url，也有可能没变
        cardList.stream().forEach(item -> answerCardService.convertTifToPng(params, item));

        Map<Long, List<Map<String, Object>>> cardGroup = cardList.stream()
                .collect(groupingBy(item -> Long.valueOf(item.get("studentId").toString())));
        List<Map<String, Object>> result = new ArrayList<>();
        cardGroup.forEach((studentId, items) -> {
            Map<String, Object> map = new HashMap<>(items.get(0));
            List<String> filePathList = items.stream()
                    .sorted(Comparator.comparing(item -> Integer.valueOf(item.get("pageNumber").toString())))
                    .map(item -> item.get("filePath").toString())
                    .collect(Collectors.toList());
            map.put("filePathList", filePathList);
            map.remove("filePath");
            map.remove("correctParam");
            result.add(map);
        });

        return result;
    }

    /**
     *  获取答题卡（非第三方）
     *
     * @param params
     * @return [{}]  item为一个学生的一份卷子答题卡信息
     *     recognitionCardId, answerCardId, pageNumber, studentId, correctStatus
     *     filePathList   每一页的答题卡url
     */
    public List<Map<String, Object>> getNewRecognitionTemplateCard(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .verify();

        Integer pageNumber = examRepository.selectOne("RecognitionCardMapper.getRecognitionCardPageNumber", params);
        params.put("pageSize", pageNumber * 10);
        List<Map<String, Object>> cardList = examRepository.selectList("RecognitionCardMapper.getOriginRecognitionTemplateCard", params);
        // 不校正图片
//        cardList.stream().forEach(item -> answerCardService.convertTifToPng(params, item));

        Map<Long, List<Map<String, Object>>> cardGroup = cardList.stream()
                .collect(groupingBy(item -> Long.valueOf(item.get("studentId").toString())));
        List<Map<String, Object>> result = new ArrayList<>();
        cardGroup.forEach((studentId, items) -> {
            Map<String, Object> map = new HashMap<>(items.get(0));
            List<Map<String, Object>> newItems = new ArrayList<>();
            for(Map<String, Object> item: items){
                Map<String, Object> newItem = new HashMap<>();
                newItem.put("filePath",item.get("filePath"));
                newItem.put("pageNumber",item.get("pageNumber"));
                newItem.put("answerCardId",item.get("answerCardId"));
                newItem.put("answerCardCode",item.get("answerCardCode"));
                newItem.put("answerCardPair",item.get("answerCardPair"));
                newItems.add(newItem);
            }
            List<Map<String, Object>> filePathList = newItems.stream()
                    .sorted(Comparator.comparing(item -> Integer.valueOf(item.get("pageNumber").toString())))
                    .collect(Collectors.toList());
            map.put("filePathList", filePathList);
            map.remove("filePath");
            map.remove("correctParam");
            result.add(map);
        });

        return result;
    }


    /**
    * @Description: 是否显示异常卷处理按钮
    * @Param:  examId paperId
    */
    public Map<String, Object> abnormalPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();

        List<Map<String, Object>> abnormalPaperList = examRepository.selectList("RecognitionCardMapper.abnormalPaperByTemplateCode", params);

        Map result = new HashMap();
        if (abnormalPaperList == null || abnormalPaperList.size() == 0) {
            result.put("abnormalPaper", 0);
            result.put("featureAbnormal", 0);
        } else {
            int totalCnt = 0, thirdRecoCnt = 0;
            for(Map record:abnormalPaperList){
                long templateCode = Long.valueOf(record.get("templateCode").toString());
                int cnt = Integer.valueOf(record.get("cnt").toString());
                if(templateCode != 0L){
                    thirdRecoCnt += cnt;
                }
                totalCnt += cnt;
            }
            if(totalCnt> 0 ){
                result.put("abnormalPaper", 1);
            }else {
                result.put("abnormalPaper", 0);
            }
            if(thirdRecoCnt > 0){
                result.put("featureAbnormal", 1);
            }else{
                result.put("featureAbnormal", 0);
            }
        }
        return result;
    }

    /**
     * 更新异常卷明细的识别信息及分数
     * @param params recognitionItemId recognitionId examId finallyScore [recognitionValue]
     */
    public void updateItemRecognitionValueAndScore(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionItemId")
                .isValidId("recognitionId")
                .isValidId("examId")
                .isNumeric("finallyScore")
                .verify();

        // 数据校验
        Map<String,Object> item = examRepository.selectOne("RecognitionCardMapper.getRecognitionItem",params);
        if(MapUtils.isEmpty(item)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"没有找到明细");
        }

        // 业务校验
        BigDecimal newFinallyScore = new BigDecimal(params.get("finallyScore").toString());
        BigDecimal scoreValue = new BigDecimal(item.get("scoreValue").toString());
        if(newFinallyScore.compareTo(scoreValue) > 0){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,"得分超过了试题的分值");
        }
        BigDecimal oldFinallyScore = new BigDecimal(item.get("finallyScore").toString());

        if(ObjectUtil.isValueEquals(2, item.get("readType"))){
            // 主观题
            if(oldFinallyScore.compareTo(newFinallyScore) == 0){
                return;
            }
            // 去掉识别结果
            params.remove("recognitionValue");
        }else{
            // 客观题
            if(ObjectUtil.isValueEquals(item.get("recognitionValue"), params.get("recognitionValue"))
                && oldFinallyScore.compareTo(newFinallyScore) == 0){
                return;
            }
            params.putIfAbsent("recognitionValue", "");
        }

        // 更新item
        ParamsUtil.setCurrentTimeIfAbsent(params);
        examRepository.update("RecognitionCardMapper.updateRecognitionItem",params);
    }


    public void addRecognitionTemplateExamUploaderId(Map<String, Object> params, List<Map<String, Object>> cardList) {
        if(!isPointExceptionRecognition(params))
            return;
        List<Map<String, Object>> templateExamUploaderList = examRepository.selectList("RecognitionCardMapper.getExamPaperTemplateNumberExamUploader", params);
        Map<Integer, List<Map<String, Object>>> templateNumberExamUploaderListMap = templateExamUploaderList.stream().collect(groupingBy(item -> MapUtil.getInt(item, "templateNumber")));
        cardList.forEach(item -> {
            int templateNumber = examRepository.selectOne("RecognitionCardMapper.getRecognitionTemplateNumber", item);
            if(templateNumberExamUploaderListMap.containsKey(templateNumber)) {
                item.put("examUploaderId", MapUtil.getLong(templateNumberExamUploaderListMap.get(templateNumber).get(0), "examUploaderId"));
            }
        });
    }

    public boolean isPointExceptionRecognition(Map<String,Object> params) {
        int recognitionType = MapUtil.getInt(params, "recognitionType", 0);
        return recognitionType == 5;
    }

    // 点定位异常处理的时候，回改templateCode, correct_param.
    // @TODO
    public void updateAnswerCardTemplateCodeAndCorrectParam(Map<String, Object> params) {
        List<Map<String,Object>> templateList = MapUtil.getListMap(params, "templateList");
        if(CollectionUtils.isEmpty(templateList))
            return;
        for(Map<String, Object> templateInfo : templateList) {
            examRepository.update("RecognitionCardMapper.syncRecognitionCard2AnswerCardTemplateAndCorrectParam", templateInfo);
        }
    }

    @Autowired
    private DistributeLockHelper distributeLockHelper;
    @Transactional(ExamRepository.TRANSACTION)
    public Map<String, Object> addNewRecognitionTemplate(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionTemplateId")
                .isValidId("recognitionId")
                .verify();

        long recognitionId = MapUtil.getLong(params, "recognitionId");
        return distributeLockHelper.tryExecute("LOCK:addNewRecognitionTemplate:" + recognitionId, 90, TimeUnit.SECONDS,  () -> {
            int count = examRepository.selectOne("RecognitionTaskMapper.getOneRecognitionTemplateInfo", params);
            if(count > 0) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "识别还没有完成，请等待识别完成！");
            }

            count = examRepository.selectOne("RecognitionCardMapper.getErrorCardCount", params);
            if(count < 1) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "没找到识别失败的学生");
            }

            params.put("recognitionTemplateStatus", 4);
            examRepository.update("RecognitionCardMapper.updateRecognitionTemplateStatus", params);

            Map<String, Object> recognition = getRecognition(params);
            recognition.putAll(params);
            List<Map<String, Object>> templateNumberExamUploaderList = examRepository.selectList("RecognitionCardMapper.getErrorExamPaperTemplateNumberExamUploader", recognition);
            templateNumberExamUploaderList.forEach(item -> item.put("templateCode", answerCardManualService.getTemplateCode()));
            recognition.put("templateNumbers", templateNumberExamUploaderList);
            // 新增模板来处理识别失败的答题卡.
            Map<String, Object> recognitionTemplate =  MapUtil.copy(recognition, "modifierId", "modifierName", "paperId");
            for(Map<String, Object> template : templateNumberExamUploaderList) {
                // 将失败的答题卡设置新的模板任务来处理
                template.putAll(recognition);
                List<Long> studentIds = examRepository.selectList("RecognitionCardMapper.getTemplateNumberStudentIds", template);
                if(CollectionUtils.isEmpty(studentIds)) {
                    continue;
                }
                template.put("studentIds", studentIds);
                examRepository.insert("RecognitionCardMapper.insertManualRecognitionTemplatePointException", template);
                examRepository.update("RecognitionCardMapper.updateErrorCardToNewTemplate", template);
                int studentCount = examRepository.selectOne("RecognitionCardMapper.getRecognitionTemplateStudentCount", template);
                recognitionTemplate.put("percent", "0%");
                recognitionTemplate.put("recognitionTemplateStatus", 1);
                recognitionTemplate.put("studentCount", studentCount);
                recognitionTemplate.put("recognitionTemplateId", template.get("recognitionTemplateId"));
                recognitionTemplate.put("questionList", examRepository.selectList("RecognitionCardMapper.getRecognitionQuestionList", recognition));
                recognitionTemplate.put("examUploaderId", MapUtil.getLong(template, "examUploaderId"));
            }
            return recognitionTemplate;
        });

    }


    /**
     * 根据paperId、examId查看是否有改变试卷结构的识别任务在进行中
     *
     * @param paperId 试卷id
     * @param examId 考试id
     */
    public boolean hasUnfinishedRecognitionTasks(long paperId, long examId) {
        Map<String, Object> params = MapUtil.of("paperId", paperId, "examId", examId);
        List<Map<String, Object>> recognitionList = examRepository.selectList("RecognitionCardMapper.getUnFinishRecognition", params);

        return recognitionList.stream()
                .anyMatch(item -> (MapUtils.getInteger(item, "recognitionType") == 3
                        || MapUtils.getInteger(item, "recognitionType") == 7
                        || MapUtils.getInteger(item, "recognitionType") == 8));
    }
}

