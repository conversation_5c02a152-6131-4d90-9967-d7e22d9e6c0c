package com.dongni.exam.recognition.manager.impl;

import com.dongni.exam.recognition.bean.vo.RecognitionVO;
import com.dongni.exam.recognition.dao.RecognitionDao;
import com.dongni.exam.recognition.enums.RecognitionTypeEnum;
import com.dongni.exam.recognition.manager.IRecognitionManager;
import com.dongni.tiku.manager.impl.RecognitionQuestionMappingManager;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.mongodb.client.model.Filters.eq;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Component
public class RecognitionManagerImpl implements IRecognitionManager {

    @Autowired
    private RecognitionQuestionMappingManager recognitionQuestionMappingManager;

    @Resource
    private RecognitionDao recognitionDao;

    @Override
    public List<Integer> getRecognitionQns(Long recognitionId, int recognitionType) {
        if (RecognitionTypeEnum.SPLIT.getType() != recognitionType && RecognitionTypeEnum.MERGE.getType() != recognitionType) {
            return recognitionDao.getRecognitionQns(recognitionId);
        }
        //获取试题映射关系
        Document recognitionQuestionMappingDoc = recognitionQuestionMappingManager.getFirst(eq("recognitionId", recognitionId));
        Set<Integer> oldQuestionNumbers = new HashSet<>();
        if(recognitionQuestionMappingDoc !=null && recognitionQuestionMappingDoc.get("questionList")!=null){
            List<Document> questionList = recognitionQuestionMappingDoc.get("questionList",List.class);
            if(CollectionUtils.isNotEmpty(questionList)) {
                for (Document question : questionList) {
                    List<Integer> oldQns = new ArrayList<>();
                    if(question.get("list")!=null){
                        List<Document> list = (List<Document>) question.get("list");
                        for(Document doc: list){
                            Integer oldQn = doc.get("questionNumber", Integer.class);
                            oldQns.add(oldQn);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(oldQns)){
                        oldQuestionNumbers.addAll(oldQns);
                    }
                }
            }
        }
        return new ArrayList<>(oldQuestionNumbers);
    }

    @Override
    public RecognitionVO getRecognition(long recognitionId) {
        return recognitionDao.getRecognition(recognitionId);
    }

    @Override
    public void updateRecognitionStatus(long recognitionId, int status) {
        recognitionDao.updateRecognitionStatus(recognitionId, status);
    }

    @Override
    public int getOtherProgressingRecognitionCount(long examId, long paperId, long recognitionId) {
        return recognitionDao.getOtherProgressingRecognitionCount(examId, paperId, recognitionId);
    }
}
