package com.dongni.exam.recognition.manager;

import com.dongni.exam.plan.bean.bo.ExamResultBO;
import com.dongni.exam.recognition.bean.vo.RecognitionStuVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStudentVO;
import com.dongni.exam.recognition.bean.vo.RecognitionTemplateStuExpVO;

import java.util.List;

public interface IRecognitionStudentManager {
    List<RecognitionStudentVO> getRecognitionStudents(RecognitionStuVO recognitionStuVO);

    int getRecognitionStudentCount(RecognitionStuVO recognitionStuVO);

    List<RecognitionStudentVO> getRecognitionStudentCards(long recognitionId, long studentId);

    void updateRelativeStudentId(long recognitionId, List<Long> recognitionCardIds, long toStudentId);

    List<Long> getRepeatStudentIds(long recognitionId);

    void updateRepeatNum(long recognitionId, long toStudentId, int repeatNum);

    int getRepeatNum(long recognitionId, long toStudentId);

    void insertRecognitionCardExtList(List<RecognitionStudentVO> cards);

    void renewRelativeStudentId(long recognitionId, long recognitionTemplateId);

    List<ExamResultBO> getExamResultList(long recognitionId);

    List<RecognitionStudentVO> getRecognitionStudentCardList(RecognitionStuVO recognitionStuVO);

    List<Long> getModifyStudentIds(long recognitionId);

    int getNotMatchCount(long recognitionId);
    
    List<Long> getCardStudentIds(long recognitionId);

    List<Long> getRelativeStudentIds(long recognitionId, List<Long> relativeStudentIds);

    List<Long> getRelativeStudentIdsByNormalStudentIds(long recognitionId, List<Long> studentIds);

    List<RecognitionTemplateStuExpVO> getRecognitionTemplateStuExp(long recognitionId);
}
