package com.dongni.exam.recognition.manager.impl;

import com.dongni.exam.plan.bean.bo.ExamResultBO;
import com.dongni.exam.recognition.bean.vo.RecognitionStuVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStudentVO;
import com.dongni.exam.recognition.bean.vo.RecognitionTemplateStuExpVO;
import com.dongni.exam.recognition.dao.RecognitionStudentDao;
import com.dongni.exam.recognition.manager.IRecognitionStudentManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Component
public class RecognitionStudentManagerImpl implements IRecognitionStudentManager {

    @Resource
    private RecognitionStudentDao recognitionStudentDao;
    @Override
    public List<RecognitionStudentVO> getRecognitionStudents(RecognitionStuVO recognitionStuVO) {
        return recognitionStudentDao.getRecognitionStudents(recognitionStuVO);
    }

    @Override
    public int getRecognitionStudentCount(RecognitionStuVO recognitionStuVO) {
        return recognitionStudentDao.getRecognitionStudentCount(recognitionStuVO);
    }

    @Override
    public List<RecognitionStudentVO> getRecognitionStudentCards(long recognitionId, long studentId) {
        return recognitionStudentDao.getRecognitionStudentCards(recognitionId, studentId);
    }

    @Override
    public void updateRelativeStudentId(long recognitionId, List<Long> recognitionCardIds, long toStudentId) {
        recognitionStudentDao.updateRelativeStudentId(recognitionId, recognitionCardIds, toStudentId);
    }

    @Override
    public List<Long> getRepeatStudentIds(long recognitionId) {
        return recognitionStudentDao.getRepeatStudentIds(recognitionId);
    }

    @Override
    public void updateRepeatNum(long recognitionId, long relativeStudentId, int repeatNum) {
        recognitionStudentDao.updateRepeatNum(recognitionId, relativeStudentId, repeatNum);
    }

    @Override
    public int getRepeatNum(long recognitionId, long relativeStudentId) {
        return recognitionStudentDao.getRepeatNum(recognitionId, relativeStudentId);
    }

    @Override
    public void insertRecognitionCardExtList(List<RecognitionStudentVO> cards) {
        recognitionStudentDao.insertRecognitionCardExtList(cards);
    }

    @Override
    public void renewRelativeStudentId(long recognitionId, long recognitionTemplateId) {
        recognitionStudentDao.renewRelativeStudentId(recognitionId, recognitionTemplateId);
    }

    @Override
    public List<ExamResultBO> getExamResultList(long recognitionId) {
        return recognitionStudentDao.getExamResultList(recognitionId);
    }

    @Override
    public List<RecognitionStudentVO> getRecognitionStudentCardList(RecognitionStuVO recognitionStuVO) {
        return recognitionStudentDao.getRecognitionStudentCardList(recognitionStuVO);
    }

    @Override
    public List<Long> getModifyStudentIds(long recognitionId) {
        return recognitionStudentDao.getModifyStudentIds(recognitionId);
    }

    @Override
    public int getNotMatchCount(long recognitionId) {
        return recognitionStudentDao.getNotMatchCount(recognitionId);
    }

    @Override
    public List<Long> getCardStudentIds(long recognitionId) {
        return recognitionStudentDao.getCardStudentIds(recognitionId);
    }

    @Override
    public List<Long> getRelativeStudentIds(long recognitionId, List<Long> relativeStudentIds) {
        return recognitionStudentDao.getRelativeStudentIds(recognitionId, relativeStudentIds);
    }

    @Override
    public List<Long> getRelativeStudentIdsByNormalStudentIds(long recognitionId, List<Long> studentIds) {
        return recognitionStudentDao.getRelativeStudentIdsByNormalStudentIds(recognitionId, studentIds);
    }

    @Override
    public List<RecognitionTemplateStuExpVO> getRecognitionTemplateStuExp(long recognitionId) {
        return recognitionStudentDao.getRecognitionTemplateStuExp(recognitionId);
    }

}
