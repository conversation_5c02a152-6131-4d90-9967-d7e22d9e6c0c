package com.dongni.exam.recognition.manager;

import com.dongni.exam.recognition.bean.vo.RecognitionVO;
import java.util.List;

public interface IRecognitionManager {

    RecognitionVO getRecognition(long recognitionId);

    void updateRecognitionStatus(long recognitionId, int status);

    int getOtherProgressingRecognitionCount(long examId, long paperId, long recognitionId);

    List<Integer> getRecognitionQns(Long recognitionId, int recognitionType);
}
