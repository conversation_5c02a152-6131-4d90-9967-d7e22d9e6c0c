package com.dongni.exam.recognition.controller;

import com.dongni.common.async.AsyncInfo;
import com.dongni.common.async.MyAsyncExecutor;
import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.plan.bean.vo.ResultListVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStuVO;
import com.dongni.exam.recognition.bean.vo.RecognitionStudentVO;
import com.dongni.exam.recognition.bean.vo.RenewRelativeVO;
import com.dongni.exam.recognition.service.RecognitionCardService;
import com.dongni.exam.recognition.service.RecognitionExpStuNoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * @description: 异常答题卡处理
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2019-07-12 15:00
 **/
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/answerCard/recognition")
public class RecognitionCardController extends BaseController {
    @Autowired
    private RecognitionCardService recognitionCardService;

    @Autowired
    private RecognitionExpStuNoService recognitionExpStuNoService;

    @Autowired
    private MyAsyncExecutor myAsyncExecutor;

    /**
     * @Description: 新增答题卡重新识别记录
     */
    @PostMapping("")
    @Deprecated
    public Response initRecognition() {
        Map<String, Object> paraMap = getParameterMap();
        paraMap.put("templateStatus", 1);
        return new Response(recognitionCardService.insertRecognition(paraMap));
    }

    /**
     * @Description: 新增答题卡重新识别记录
     */
    @PostMapping("/new")
    public Response newInitRecognition() {
        Map<String, Object> paraMap = getParameterMap();
        paraMap.put("templateStatus", 1);
        return new Response(recognitionCardService.newInsertRecognition(paraMap));
    }



    @GetMapping("/new/check")
    public Response newInitRecognitionCheck() {
        Map<String, Object> paraMap = getParameterMap();
        paraMap.put("templateStatus", 1);
        recognitionCardService.newRecognitionCheck(paraMap);
        return new Response();
    }


    /**
     * @Description: 新增答题卡覆盖识别记录
     */
    @PostMapping("/replace")
    public Response initReplaceRecognition() {
        Map<String, Object> paraMap = getParameterMap();
        paraMap.put("templateStatus", 5);
        return new Response(recognitionCardService.insertRecognition(paraMap));
    }


    /**
     * @Description: 新增第三方答题卡重新识别记录
     */
    @PostMapping("/manual")
    public Response insertManualRecognition() {
        return new Response(recognitionCardService.insertManualRecognition(getParameterMap()));
    }


    /**
     * 定位点异常识别，失败的时候，新增
     */
    @PostMapping("/addNewRecognitionTemplate")
    public Response addNewRecognitionTemplate(){
        return new Response(recognitionCardService.addNewRecognitionTemplate(getParameterMap()));
    }



    /**
     * 非GET/POST治理 考试
     * 删除异常卷重新识别
     *
     * @return
     */
    @Deprecated
    @DeleteMapping
    public Response deleteRecognition() {
        recognitionCardService.deleteRecognition(getParameterMap());
        return new Response();
    }

    /**
     * 删除异常卷重新识别
     *
     * @return
     */
    @PostMapping("/delete")
    public Response deleteRecognition2() {
        recognitionCardService.deleteRecognition(getParameterMap());
        return new Response();
    }

    /**
     * @Description: 获取异常卷处理列表
     * @Param: examId paperId
     */
    @GetMapping("")
    public Response getRecognitionList() {
        return new Response(recognitionCardService.getRecognitionList(getParameterMap()));
    }

    /**
     * @Description: 根据主客观提获取答题卡题目
     * @Param: paperId readType(1客观题 2主观题) [unitType]
     */
    @GetMapping("/question")
    public Response getAnswerCardQuestionList() {
        return new Response(recognitionCardService.getAnswerCardQuestionList(getParameterMap()));
    }

    /**
     * @Description: 根据学校筛选有答题卡的班级
     * @Param: examId paperId schoolIds
     */
    @GetMapping("/class")
    public Response getAnswerCardClassList() {
        return new Response(recognitionCardService.getAnswerCardClassList(getParameterMap()));
    }

    /**
     * @Description: 根据班级筛选有答题卡的学生
     * @Param: examId paperId classIds
     */
    @GetMapping("/student")
    public Response getAnswerCardStudentList() {
        return new Response(recognitionCardService.getAnswerCardStudentList(getParameterMap()));
    }

    /**
     * @Description: 根据班级筛选有答题卡的学生
     * @Param: examId paperId classIds
     */
    @GetMapping("/simple/student")
    public Response getAnswerCardAllStudentList() {
        return new Response(recognitionCardService.getCompletedAnswerCardAllStudentList(getParameterMap()));
    }

    /**
     * @Description: 根据班级筛选有答题卡的学生
     * @Param: examId paperId classIds
     */
    @PostMapping("/simple/student")
    public Response getAnswerCardAllStudentListPost(Map<String, Object> params) {
        return new Response(recognitionCardService.getCompletedAnswerCardAllStudentList(params));
    }
    /**
     * @Description: 获取异常答题卡识别列表
     * @Param: recognitionId
     */
    @GetMapping("/abnormal")
    public Response getRecognitionCardList() {
        return new Response(recognitionCardService.getRecognitionCardList(getParameterMap()));
    }

    /**
     * @Description: 获取预览解答题列表
     * @Param: recognitionId examId paperId questionNumber
     */
    @GetMapping("/preview/subjective/question")
    public Response getPreviewSubjectiveQuestionList() {
        return new Response(recognitionCardService.getPreviewSubjectiveQuestionList(getParameterMap()));
    }

    /**
     * @Description: 获取预览选择题列表(按学生)
     * @Param:  recognitionTemplateId
     */
    @GetMapping("/preview/objective/question/student")
    public Response getPreviewObjectiveQuestionList() {
        return new Response(recognitionCardService.getPreviewObjectiveQuestionListByStudent(getParameterMap()));
    }

    /**
     * @Description: 获取预览选择题列表(按班级)
     * @Param:  recognitionTemplateId
     */
    @GetMapping("/preview/objective/question")
    public Response getPreviewObjectiveQuestionLisByQuestion() {
        return new Response(recognitionCardService.getPreviewObjectiveQuestionListByQuestion(getParameterMap()));
    }

    /**
     * 获取答题卡
     *
     * @return
     */
    @GetMapping("/card")
    public Response getRecognitionCard() {
        return new Response(recognitionCardService.getRecognitionCardByTemplate(getParameterMap()));
    }

    /**
     * 获取答题卡（非第三方）
     *
     * @return
     */
    @GetMapping("/template/card")
    public Response getRecognitionTemplateCard() {
        return new Response(recognitionCardService.getRecognitionTemplateCard(getParameterMap()));
    }


    /**
     * 获取答题卡（非第三方），返回数据中，每张答题卡会带上anwsercardcode和pair
     *
     * @return
     */
    @GetMapping("/template/card/new")
    public Response getNewRecognitionTemplateCard() {
        return new Response(recognitionCardService.getNewRecognitionTemplateCard(getParameterMap()));
    }

    /**
     * @Description: 是否显示异常卷处理按钮
     * @Param:  examId paperId
     */
    @GetMapping("/abnormal/paper")
    public Response abnormalPaper() {
        return new Response(recognitionCardService.abnormalPaper(getParameterMap()));
    }
    
    /**
     * 更新异常卷明细的识别信息及分数
     * @param params recognitionItemId recognitionId examId finallyScore [recognitionValue]
     */
    @PostMapping("/item/score")
    public Response updateItemRecognitionValueAndScore(Map<String, Object> params) {
        recognitionCardService.updateItemRecognitionValueAndScore(params);
        return new Response();
    }

    @GetMapping("/stunos")
    public Response getExpStuNOList() {
        RecognitionStuVO recognitionStuVO = JSONUtil.parse(JSONUtil.toJson(getParameterMap()), RecognitionStuVO.class);
        ResultListVO<RecognitionStudentVO> resultListVO = recognitionExpStuNoService.getExpStuNos(recognitionStuVO);
        return new Response(resultListVO);
    }

    @GetMapping("/complete/student")
    public Response getCompleteStudentList() {
        RecognitionStuVO recognitionStuVO = JSONUtil.parse(JSONUtil.toJson(getParameterMap()), RecognitionStuVO.class);
        ResultListVO<RecognitionStudentVO> resultListVO = recognitionExpStuNoService.getCompleteStudentList(recognitionStuVO);
        return new Response(resultListVO);
    }

    @PostMapping("/relative")
    public Response handleRelativeStudent() {
        RenewRelativeVO renewRelativeVO = JSONUtil.parse(JSONUtil.toJson(getParameterMap()), RenewRelativeVO.class);
        recognitionExpStuNoService.handleRelativeStudent(renewRelativeVO);
        return new Response();
    }

    @PostMapping("/batch/relative/stunos")
    public Response handleBatchRelativeStudent() {
        RecognitionStuVO recognitionStuVO = JSONUtil.parse(JSONUtil.toJson(getParameterMap()), RecognitionStuVO.class);
        recognitionExpStuNoService.batchRelativeStudent(recognitionStuVO);
        return new Response();
    }

    @PostMapping("/relative/stunos/complete/async")
    public Response handleCompleteRelativeStudent() {
        Map<String, Object> params = getParameterMap();
        AsyncInfo asyncInfo = myAsyncExecutor.execute("考号识别全部处理完成", params,
                () -> recognitionExpStuNoService.handleCompleteRelativeStudent(params));
        return new Response(asyncInfo);
    }
}

