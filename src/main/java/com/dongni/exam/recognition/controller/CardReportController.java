package com.dongni.exam.recognition.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.recognition.service.CardReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 异常卷上报
 *
 * <AUTHOR>
 * @date 2019/09/23 14:25
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/card/report")
public class CardReportController extends BaseController {

    @Autowired
    private CardReportService cardReportService;


    /**
     * 查询异常卷申报明细列表
     *
     * @return
     */
    @GetMapping("/item/list")
    public Response getCardReportItemList() {
        return new Response(cardReportService.getCardReportItemList(getParameterMap()));
    }

    /**
     * 获取异常卷申报明细数量
     *
     * @return
     */
    @GetMapping("/item/count")
    public Response getCardReportItemCount() {
        return new Response(cardReportService.getCardReportItemCount(getParameterMap()));
    }

    /**
     * 查询异常卷申报明细详情
     *
     * @return
     */
    @GetMapping("/item/detail")
    public Response getCardReportItemDetail() {
        return new Response(cardReportService.getCardReportItemDetail(getParameterMap()));
    }

    /**
     * 非GET/POST治理 考试
     * 替换图片
     *
     * @return
     */
    @Deprecated
    @PutMapping("/item")
    public Response updateCardReportItem() {
        cardReportService.updateCardReportItem(getParameterMap());
        return new Response();
    }

    /**
     * 替换图片
     *
     * @return
     */
    @PostMapping("/item/replace")
    public Response updateCardReportItem1() {
        cardReportService.updateCardReportItem(getParameterMap());
        return new Response();
    }

    /**
     * 替换答题卡
     *
     * @return
     */
    @PostMapping("/answerCard")
    public Response updateStudentAnswerCard() {
        cardReportService.updateStudentAnswerCard(getParameterMap());
        return new Response();
    }

    /**
     * 替换图片处理完成
     *
     * @return
     */
    @PostMapping("/complete")
    public Response completeReplace() {
        cardReportService.completeReplace(getParameterMap());
        return new Response();
    }

    /**
     * 切割预览（学生列表）
     *
     * @return
     */
    @GetMapping("/item/preview/student")
    public Response getItemPreviewByStudent() {
        return new Response(cardReportService.getItemPreviewByStudent(getParameterMap()));
    }

    /**
     * 切割预览（试题列表）
     *
     * @return
     */
    @GetMapping("/item/preview/question")
    public Response getItemPreviewByQuestion() {
        return new Response(cardReportService.getItemPreviewByQuestion(getParameterMap()));
    }

    /**
     * 切割预览（学生详情）
     *
     * @return
     */
    @GetMapping("/item/preview/student/detail")
    public Response getItemPreviewDetailByStudent() {
        return new Response(cardReportService.getItemPreviewDetailByStudent(getParameterMap()));
    }

    /**
     * 切割预览（学生详情）
     *
     * @return
     */
    @GetMapping("/item/preview/question/detail")
    public Response getItemPreviewDetailByQuestion() {
        return new Response(cardReportService.getItemPreviewDetailByQuestion(getParameterMap()));
    }

}
