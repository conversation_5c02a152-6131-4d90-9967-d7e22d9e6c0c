package com.dongni.exam.monitor.school.service;

import com.dongni.analysis.view.monitor.school.service.MonitorSchoolUserGroupService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Create by sapluk <br/>
 * time 18:00 2019/04/16 <br/>
 * description: <br/>
 *  t_monitor_school
 */
@Service
public class MonitorSchoolService {

    @Autowired
    private MonitorSchoolUserGroupService monitorSchoolUserTypeService;

    @Autowired
    private ExamRepository examRepository;

    /**
     * 获取监控主信息
     *   不对外提供接口，如果获取不到，会插入一条记录
     * @param params
     *   - schoolId
     *   - gradeId
     *   - gradeType
     *   - userType    -> userGroup
     *   - courseId      0为总分 + 正常课程id,  备课组长不能传0
     *   - [artsScience]   高一及以下允许不传
     * @return 监控主信息
     */
    public Map<String, Object> getMonitorSchool(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isValidId("gradeId")
                .isNumeric("gradeType")
                .isNumeric("courseId")
                .isNumeric("statType")
                .verify();
        Long courseId = Long.parseLong(params.get("courseId").toString());
        Integer userGroup = monitorSchoolUserTypeService.getAndSetMonitorUserGroup(params);

        // 校领导课程id锁定为 0
        if (DictUtil.isEquals(userGroup, "monitorSchoolUserGroup", "principal")) {
            params.put("courseId", -1);
        }

        // 备课组长没有总分
        if (DictUtil.isEquals(userGroup, "monitorSchoolUserGroup", "prepareLeader")
                && ObjectUtil.isValueEquals(params.get("courseId"), 0)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "备课组长必须提供合法的courseId");
        }

        Map<String, Object> monitorSchoolInfo = examRepository.selectOne("MonitorSchoolMapper.getMonitorSchoolInfo", params);

        // 查不到数据，直接插入一条新的记录
        if (MapUtils.isEmpty(monitorSchoolInfo)) {
            params.put("currentTime", DateUtil.getCurrentDateTime());
            examRepository.insert("MonitorSchoolMapper.insertMonitorSchool", params);
            if (params.get("monitorSchoolId") == null) {
                monitorSchoolInfo = examRepository.selectOne("MonitorSchoolMapper.getMonitorSchoolInfo", params);
            } else {
                monitorSchoolInfo = MapUtil.copy(params,
                        "monitorSchoolId", "schoolId", "gradeId", "userGroup", "courseId", "artsScience"
                );
            }
        }
        params.put("courseId", courseId);
        return monitorSchoolInfo;
    }
}
