package com.dongni.analysis.security;

import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.commons.annotation.UserSession;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.dispiay.util.DisplayExamUtil;
import com.dongni.exam.plan.service.ExamStudentService;
import com.dongni.tiku.common.util.MapUtil;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: hzw
 * @date: 2024/11/25
 * @description: 校级角色查看考试时对studentId参数的权限校验
 */
@Component
public class ExamStudentIdScope extends ExamBaseDataScope {

	/**
	 * 存放用户可见的studentIds的key
	 */
	private static final String USER_STUDENT_IDS_KEY = "userExamStudentIds";
	@Autowired
	private CommonStudentService commonStudentService;
	@Autowired
	private ExamStudentService examStudentService;

	@Override
	public boolean validate(Map<String, Object> params, UserSession userSession) {

		//不是在可见性中的这几个校级角色，不需要做校验
		if (!DisplayExamUtil.isExamConfigSchoolUserType(userSession.getUserType())) {
			return true;
		}

		String studentIdStr = MapUtil.getStringNullable(params, "studentId");
		String examId = MapUtil.getStringNullable(params, "examId");
		if (!ObjectUtil.isValidId(studentIdStr) || !ObjectUtil.isValidId(examId)) {
			return false;
		}
		String studentIdKey = getKey(examId, studentIdStr);
		if (userSession.isInList(USER_STUDENT_IDS_KEY, studentIdKey)) {
			return true;
		}

		long studentId = Long.parseLong(studentIdStr);
		Long studentSchoolId = commonStudentService.getStudentSchoolId(studentId);
		if (studentSchoolId == null) {
			//基础数据中找不到这个学生的学校，可能是这个学生在基础数据中已经被删除了，再在考试中查询下试试
			studentSchoolId = examStudentService.getExamStudentSchoolIdByExamIdAndStudentId(params);
		}
		if (ObjectUtil.isBlank(studentSchoolId)) {
			return false;
		}
		Set<Long> userVisibleSchoolIds = getUserVisibleSchoolIdsByExamInfo(params);
		if (userVisibleSchoolIds.contains(studentSchoolId)) {
			userSession.addToList(USER_STUDENT_IDS_KEY, Collections.singleton(studentIdKey), EXPIRE_TIME);
			return true;
		}
		return false;
	}
}
