package com.dongni.analysis.report.custom.controller;

import com.dongni.analysis.report.custom.service.CustomExcelExamStatInfoService;
import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 获取报告中的学校班级学生
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2019-06-17 20:36
 **/
@RestController
@RequestMapping("/analysis/excel/custom/exam/stat")
public class CustomExcelExamStatInfoController extends BaseController {
    @Autowired
    private CustomExcelExamStatInfoService customExcelExamStatInfoService;

    @PostMapping("/area")
    public Response getExamStatArea() {
        return new Response(customExcelExamStatInfoService.getExamStatArea(getParameterMap()));
    }

    @PostMapping("/school")
    public Response getExamStatSchool() {
        return new Response(customExcelExamStatInfoService.getExamStatSchool(getParameterMap()));
    }

    @PostMapping("/class")
    public Response getExamStatClass() {
        return new Response(customExcelExamStatInfoService.getExamStatClass(getParameterMap()));
    }

    @PostMapping("/student")
    public Response getExamStatStudent() {
        return new Response(customExcelExamStatInfoService.getExamStatStudent(getParameterMap()));
    }

}

