package com.dongni.analysis.report.tutor.export;

import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.bson.Document;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dongni.common.report.excel.ExcelUtil.addMergedRegion;
import static com.dongni.common.report.excel.ExcelUtil.createCell;


/**
 * Created by jayfree on 2019/05/07.
 * <p>
 * 导师制报表
 */
public class ExamTutorTrackStatExcelReport extends ExportExcel {

    private List<Document> data;
    private List<String> field;

    public ExamTutorTrackStatExcelReport(String sheetName, List<String> field, List<Document> data) {
        super(sheetName);
        this.field = field;
        this.data = data;

    }

    /**
     * 生成表头
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        CellStyle style = ExcelStyle.getHeaderStyle(sheet.getWorkbook());

        String[] strs = {"平均分数", "平均名次", "排名", "排名升降"};
        // 第一行
        Row row1 = ExcelUtil.createRow(sheet,currentRow);

        //第二行
        Row row2 = ExcelUtil.createRow(sheet,currentRow+1);

        createCell(row1,currentCol,"导师",style);
        addMergedRegion(sheet,currentRow,currentRow+1,currentCol,currentCol);
        currentCol++;
        for (String s : field) {
            createCell(row1,currentCol,s,style);
            addMergedRegion(sheet,currentRow,currentRow,currentCol,currentCol+strs.length-1);
            for (String str : strs) {
                createCell(row2,currentCol++,str,style);
            }
        }
        return currentRow + 2;
    }

    /**
     * 生成表体
     */
    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        if(data == null || data.isEmpty()){ return currentRow;}
        String[] strs = {"averageScore", "tutorAverageRanking", "ranking", "offset"};
        CellStyle style = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        for (Document doc : data) {
            Row row = ExcelUtil.createRow(sheet,currentRow);
            createCell(row,currentCol++,doc.get("teacherName"),style);
            if (doc.get("exam") != null){
                List<Map<String, Object>> examList =  (List)doc.get("exam");
                examList = examList.stream()
                        .sorted(Comparator.comparing(m -> Long.valueOf(m.get("examId").toString()))).collect(Collectors.toList());
                for (Map<String, Object> exam : examList) {
                    for (String str : strs) {
                        createCell(row,currentCol++,ExcelFormatUtil.forMatScore(exam.get(str)),style);
                    }
                }

            }
            currentRow++;
            currentCol = 0;
        }
        return currentRow;
    }


}
