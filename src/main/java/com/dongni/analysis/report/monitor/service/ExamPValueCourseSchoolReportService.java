package com.dongni.analysis.report.monitor.service;

import com.dongni.analysis.area.view.exam.service.ExamAreaCoursePValueStatService;
import com.dongni.analysis.report.monitor.export.AreaPValueCourseExcelReport;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

/**
 * Created by Admin
 * time:2019/3/7 11:34
 * description:联考、区域考学校（课程）P值
 **/
@Service
public class ExamPValueCourseSchoolReportService {

    @Autowired
    private ExamAreaCoursePValueStatService examAreaCoursePValueStatService;
    @Autowired
    private ExamLineStatReportService examLineStatReportService;

    public String getExamPValueCourseSchoolReport(Map<String, Object> parameterMap) {
        Map<String,Object> map = examAreaCoursePValueStatService.getExamAreaCoursePValue(parameterMap);
        List<Document> documents = (List<Document>) map.get("list");
        examLineStatReportService.dealAreaName(documents);
        documents.forEach(s->{
            if (ObjectUtil.isBlank(s.get("schoolId"))){
                s.remove("PValueRanking");
            }
        });
        String[] headers = {"实参人数","P值","排名","均分","优秀率","良好率","及格率"};
        String[] fields = {"participationNumber", "PValue", "PValueRanking", "averageScore", "excellentRate", "goodRate", "passRate"};
        ExportExcel exportExcel = new AreaPValueCourseExcelReport("区域学校单科P值统计",documents,headers,fields,false);
        return exportExcel.exportToFileStorage();
    }

    public String getExamPValueCourseClassReport(Map<String,Object> parameterMap) {
        Map<String,Object> map = examAreaCoursePValueStatService.getExamAreaClassCoursePValue(parameterMap);
        List<Document> documents = (List<Document>) map.get("list");
        examLineStatReportService.dealAreaName(documents);
        documents.forEach(s->{
            if (ObjectUtil.isBlank(s.get("schoolId"))){
                s.remove("PValueRanking");
            }
        });
        String[] headers = {"实参人数","P值","排名","均分","优秀率","良好率","及格率"};
        String[] fields = {"participationNumber", "PValue", "PValueRanking", "averageScore", "excellentRate", "goodRate", "passRate"};
        ExportExcel exportExcel = new AreaPValueCourseExcelReport("区域班级单科P值统计",documents,headers,fields,true);
        return exportExcel.exportToFileStorage();
    }
}
