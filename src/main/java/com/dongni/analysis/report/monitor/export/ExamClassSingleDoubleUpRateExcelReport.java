package com.dongni.analysis.report.monitor.export;

import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.bson.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dongni.common.report.excel.ExcelUtil.createCell;

/**
 * Created by scott
 * time: 11:50 2018/3/14
 * description:联考单双上线率
 */
public class ExamClassSingleDoubleUpRateExcelReport extends ExportExcel {

    /**
     * 表体填充数据
     */
    private List<Document> data;
    private String line;
    private Boolean totalScoreVisible;
    private Map<String,Integer> courseColMap = new HashMap<>();

    public ExamClassSingleDoubleUpRateExcelReport(String sheetName, List<Document> data, String line, Boolean totalScoreVisible) {
        super(sheetName);
        this.data = data;
        this.line = line;
        this.totalScoreVisible = totalScoreVisible;
    }

    /**
     * 生成表头
     *
     * @param sheet      sheet
     * @param currentRow 当前行
     * @return 下一行
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        if(null == data || 0 == data.size()) {
            return currentRow;
        }

        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        Row row1 = sheet.createRow(currentRow);
        Row row2 = sheet.createRow(currentRow+1);
        List<String> headers = new ArrayList<>();
        List<String> scores = new ArrayList<>();

        ExcelUtil.createCell(row1, currentCol, "学校", headerStyle);
        ExcelUtil.addMergedRegion(sheet, currentRow, currentRow + 1, currentCol, currentCol);
        currentCol++;

        ExcelUtil.createCell(row1, currentCol, "班级", headerStyle);
        ExcelUtil.addMergedRegion(sheet, currentRow, currentRow + 1, currentCol, currentCol);
        currentCol++;

        ExcelUtil.createCell(row1, currentCol, "实参人数", headerStyle);
        ExcelUtil.addMergedRegion(sheet, currentRow, currentRow + 1, currentCol, currentCol);

        if (totalScoreVisible) {
            currentCol++;
            String score= (String) data.get(0).get("line"+line,Map.class).getOrDefault("score","");
            if (ObjectUtil.isBlank(score)){
                ExcelUtil.createCell(row1, currentCol, "总分", headerStyle);
            }else {
                ExcelUtil.createCell(row1, currentCol, "总分" + "(" + ExcelFormatUtil.forMatScore(score) + "分)", headerStyle);
            }
            ExcelUtil.createCell(row2, currentCol, "上线人数", headerStyle);
        }

        List<Document> course = data.get(0).get("course", List.class);
        for (Document doc : course) {
            headers.add(doc.get("courseName").toString());
            Document lineDoc = (Document) doc.get("line" + line);
            scores.add(ExcelFormatUtil.forMatScore(lineDoc.get("score")).toString());
        }

        for (int i = 0; i < headers.size(); i++) {
            courseColMap.put(headers.get(i),currentCol+1);
            ExcelUtil.createCell(row1, currentCol+1, headers.get(i) + "("+ scores.get(i) +"分)", headerStyle);
            ExcelUtil.addMergedRegion(sheet, currentRow, currentRow, currentCol+1, currentCol+4);

            ExcelUtil.createCell(row2, currentCol+1, "单上线人数", headerStyle);
            ExcelUtil.createCell(row2, currentCol+2, "双上线人数", headerStyle);
            ExcelUtil.createCell(row2, currentCol+3, "命中率", headerStyle);
            ExcelUtil.createCell(row2, currentCol+4, "贡献率", headerStyle);

            currentCol+=4;
        }
        return currentRow+2;
    }

    /**
     * 生成表体
     *
     * @param sheet      sheet
     * @param currentRow 当前行
     * @return 下一行
     */
    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        if(null == data || 0 == data.size()) {
            return currentRow;
        }
        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle rateStyle = ExcelStyle.getRateStyle(sheet.getWorkbook());
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(currentRow);
            Document document = data.get(i);
            ExcelUtil.createCell(row, currentCol++, document.getOrDefault("schoolName","跨校诊断总体"), bodyStyle);
            ExcelUtil.createCell(row, currentCol++, document.getOrDefault("className",""), bodyStyle);
            ExcelUtil.createCell(row, currentCol++, document.getOrDefault("participationNumber",""), bodyStyle);
            if (totalScoreVisible) {
                Document lineDoc = (Document) document.get("line" + line);
                if (lineDoc!=null){
                    ExcelUtil.createCell(row, currentCol++, lineDoc.getOrDefault("totalUp",0), bodyStyle);
                }else {
                    ExcelUtil.createCell(row, currentCol++,"", bodyStyle);
                }
            }
            List<Document> o = document.get("course",List.class);
            for(Document courseDoc : o){
                Document lineX = (Document)courseDoc.get("line" + line);
                String courseName =courseDoc.getString("courseName");
                Integer courseCol = courseColMap.get(courseName);
                if (lineX!=null && courseCol != null){
                    createCell(row,courseCol,lineX.get("singleUp"),bodyStyle);
                    createCell(row,courseCol+1,lineX.get("doubleUp"),bodyStyle);
                    createCell(row,courseCol+2,ExcelFormatUtil.forMatRate(lineX.get("hitRate")),rateStyle);
                    createCell(row,courseCol+3,ExcelFormatUtil.forMatRate(lineX.get("contributionRate")),rateStyle);
                }else {
                    createCell(row,currentCol++,"",bodyStyle);
                    createCell(row,currentCol++,"",bodyStyle);
                    createCell(row,currentCol++,"",bodyStyle);
                    createCell(row,currentCol++,"",bodyStyle);
                }
            }
            ++currentRow;
            currentCol = 0;
        }
        return ++currentRow;
    }

}
