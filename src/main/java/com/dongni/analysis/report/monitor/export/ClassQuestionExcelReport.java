package com.dongni.analysis.report.monitor.export;

import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by scott
 * time: 11:50 2018/6/5
 * description:试题班级/学校得分率
 */
public class ClassQuestionExcelReport extends ExportExcel {

    /**
     * 表体填充数据
     */
    private Map<String, String> fields;
    private List<Map<String, Object>> item;
    private Set<String> ops;
    private String className;

    public ClassQuestionExcelReport(String sheetName, Map<String, String> fields,
                                    Set<String> ops, List<Map<String, Object>> item, String className) {
        super(sheetName);
        this.fields = fields;
        this.ops = ops;
        this.item = item;
        this.className = className;
    }
    
    /**
     * 生成表头
     *
     * @param sheet      sheet
     * @param currentRow 当前行
     * @return 下一行
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());

        String title = (ObjectUtil.isBlank(className)?"年级":className)+"试题分析" + "（注:此表与选项维度的区别在于多选题按照学生实际作答统计，主观题按照得分区间统计）";
        RichTextString richTextString = ExcelStyle.getRichTextString(sheet.getWorkbook(), title, "（注");
        Row titleRow = sheet.createRow(0);
        Cell cell = titleRow.createCell(0);
        cell.setCellStyle(headerStyle);
        cell.setCellValue(richTextString);

        ExcelUtil.addMergedRegion(sheet, 0, 0, 0, fields.size() + 3- 1);

        Row row = ExcelUtil.createRow(sheet, 1);
        Row row2 = ExcelUtil.createRow(sheet, 2);
        Set<String> keys = fields.keySet();
        int index = 0;
        for (String key : keys) {
            ExcelUtil.createCell(row, index, fields.get(key), headerStyle);
            ExcelUtil.addMergedRegion(sheet, 1, 2, index, index);
            index++;
        }
        // 作答情况
        ExcelUtil.createCell(row, index, "作答情况", headerStyle);
        ExcelUtil.createCell(row2, index, "人数", headerStyle);
        ExcelUtil.createCell(row2, index + 1, "占比", headerStyle);
        ExcelUtil.addMergedRegion(sheet, 1, 1, index, index + 1);
        index += 2;
        // 学生名单
        ExcelUtil.createCell(row, index, "学生名单", headerStyle);
        ExcelUtil.addMergedRegion(sheet, 1, 2, index, index);

        // 设置学生名单列宽度 66个字符
        sheet.setColumnWidth(index, 66 * 256);

        return 3;
    }


    /**
     * 生成表体
     *
     * @param sheet      sheet
     * @param currentRow 当前行
     * @return 下一行
     */
    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle rateStyle = ExcelStyle.getRateStyle(sheet.getWorkbook());
        Row row;
        int index = 3;
        for (int i = 0; i < item.size(); i++, index++) {
            row = ExcelUtil.createRow(sheet, index);
            int offset = 0;
            Map<String, Object> score = item.get(i);
            for (String key : fields.keySet()) {
                if (key.endsWith("Rate")){
                    if ("answer".equals(key) && score.get(key) == null) {
                        ExcelUtil.createCell(row, offset++, "--", bodyStyle);
                    } else {
                        ExcelUtil.createCell(row, offset++, ExcelFormatUtil.forMatRate(score.get(key)), rateStyle);
                    }
                }else {
                    if ("answer".equals(key) && score.get(key) == null) {
                        ExcelUtil.createCell(row, offset++, "--", bodyStyle);
                    } else {
                        ExcelUtil.createCell(row, offset++, ExcelFormatUtil.format(key,score.get(key)), bodyStyle);
                    }
                }
            }

            int firstRowIndex = index;
            if (ObjectUtil.isValueEquals("1", score.get("readType"))) {
                List<Map<String, Object>> optionSectionList = MapUtil.getListMap(score, "optionSection");
                for (int j = 0; j < optionSectionList.size(); j++) {
                    Map<String, Object> map = optionSectionList.get(j);
                    ExcelUtil.createCell(row, offset, map.get("section") + ":" + map.get("total"), bodyStyle);
                    ExcelUtil.createCell(row, offset + 1, ExcelFormatUtil.forMatRate(map.get("rate")), rateStyle);
                    ExcelUtil.createCell(row, offset + 2, StringUtils.join((List<String>)map.get("names"), "、"), bodyStyle);
                    if (j != optionSectionList.size() - 1) {
                        row = ExcelUtil.createRow(sheet, ++index);
                    }
                }
                if (optionSectionList.size() > 1) {
                    int z = 0;
                    while (z < fields.size()) {
                        ExcelUtil.addMergedRegion(sheet, firstRowIndex, firstRowIndex + optionSectionList.size() - 1, z, z);
                        z++;
                    }
                }
            } else {
                List<Map<String, Object>> scoreSectionList = MapUtil.getListMap(score, "scoreSection");
                for (int j = 0; j < scoreSectionList.size(); j++) {
                    Map<String, Object> map = scoreSectionList.get(j);

                    String value;
                    if (map.get("close") == null) {
                        value = "满分" + ":" + map.get("total");
                    } else {
                        value = "(" + formatDouble(map.get("open")) + ", " + formatDouble(map.get("close")) + "]" + ":" + map.get("total");
                    }

                    ExcelUtil.createCell(row, offset, value, bodyStyle);
                    ExcelUtil.createCell(row, offset + 1, ExcelFormatUtil.forMatRate(map.get("rate")), rateStyle);
                    ExcelUtil.createCell(row, offset + 2, StringUtils.join((List<String>)map.get("names"), "、"), bodyStyle);
                    if (j != scoreSectionList.size() - 1) {
                        row = ExcelUtil.createRow(sheet, ++index);
                    }
                }
                if (scoreSectionList.size() > 1) {
                    int z = 0;
                    while (z < fields.size()) {
                        ExcelUtil.addMergedRegion(sheet, firstRowIndex, firstRowIndex + scoreSectionList.size() - 1, z, z);
                        z++;
                    }
                }
            }
        }
        return currentRow;
    }

    private String formatDouble(Object value) {
        if (value == null) {
            return "null";
        }
        DecimalFormat df = new DecimalFormat("0");
        df.setRoundingMode(RoundingMode.FLOOR);
        return df.format(value);
    }
}
