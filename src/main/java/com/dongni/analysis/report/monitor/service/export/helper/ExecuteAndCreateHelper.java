package com.dongni.analysis.report.monitor.service.export.helper;

import com.dongni.analysis.report.monitor.service.export.data.IExportMethod;
import com.dongni.analysis.report.monitor.service.export.enums.DataType;
import com.dongni.analysis.report.monitor.service.export.excel.base.BaseExportExcel;
import com.dongni.analysis.report.monitor.service.export.excel.base.ContainerExportExcel;
import com.dongni.analysis.report.monitor.service.export.log.ErrorLogHandler;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.spring.SpringContextUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.lang.Nullable;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/7/27 下午 03:19
 * @Version 1.0.0
 */
public class ExecuteAndCreateHelper {

    private static final ErrorLogHandler LOG_HANDLER;

    static {
        LOG_HANDLER = SpringContextUtil.getBean("errorLogHandler");
    }

    /**
     * 基础的执行方法
     * 1. 反射执行方法获取数据
     * 2. 将数据设置到对应的excel对象上
     * 3. 返回对应的excel对象
     *
     * @param method      待执行的方法
     * @param params      参数
     * @param exportExcel excel对象
     * @return ExportExcel对象
     */
    private static BaseExportExcel baseExecute(Method method, Map<String, Object> params,
                                               @Nullable BaseExportExcel exportExcel) {
        try {
            DataType dataType = MapUtil.getCast(params, "dataType");
            IExportMethod exportMethodBean = MappingHelper.getExportMethod(dataType);
            if (exportMethodBean == null) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, dataType.name() + "不存在对应的IExportMethod对象!");
            }

            // 获取数据
            Map<String, Object> data = (Map<String, Object>) ReflectionUtils.invokeMethod(method, exportMethodBean, params);
            if (data != null) {
                // 放入dataType
                data.put("dataType", dataType);
                // 新需求来了，再塞个examType进去
                data.put("examType", params.get("examType"));
                // 再放一个,表示单校报表导出的
                data.put("isSingleReport", params.get("isSingleReport"));
                // 可见性
                data.put("displayMap", params.get("displayMap"));
                // 考试学段的
                data.put("stage", params.get("stage"));
            }
            if (exportExcel != null) {
                exportExcel.setData(data);
            }
        } catch (CommonException e) {
            // 数据不存在的异常返回null对象，不去生成excel
            if (e.getResponseStatusEnum().equals(ResponseStatusEnum.DATA_NOT_EXISTS)) {
                return null;
            } else {
                throw e;
            }
        }
        return exportExcel;
    }

    /**
     * 执行产生多个sheet，打包成一个ExportExcel对象返回
     *
     * @param methodAndExcel method 方法
     *                       exportExcel UnionBaseExportExcel对象
     * @param params         参数
     * @param sheetName      容器excel的文件名
     */
    private static ExportExcel containerExecute(List<Map<String, Object>> methodAndExcel, Map<String, Object> params, String sheetName) {
        // 设置containerExcel的SXSSF属性,一个sheet不启用,则没法启用
        AtomicBoolean allSxssf = new AtomicBoolean(true);

        ContainerExportExcel excelContainer = ContainerExportExcel.getExcelContainer(sheetName);
        methodAndExcel.forEach(item -> {
            Method method = (Method) item.get("method");
            BaseExportExcel exportExcel = (BaseExportExcel) item.get("exportExcel");
            ExportExcel excel = baseExecute(method, params, exportExcel);
            if (excel != null) {
                excelContainer.addSheet(excel);
                if (!excel.getEnableSXSSF()) {
                    allSxssf.set(false);
                }
            }
        });

        excelContainer.setEnableSXSSF(allSxssf.get());
        if (excelContainer.isBankSheet()) {
            return null;
        } else {
            return excelContainer;
        }
    }

    /**
     * 将exportExcel对象生成到指定路径
     *
     * @param exportExcel ExportExcel对象
     * @param path        指定路径
     */
    private static void createExcel(@Nullable ExportExcel exportExcel, String path) {
        // 容器excel可以返回空对象
        if (exportExcel != null) {
            exportExcel.exportToLocalPath(path);
        }
    }

    /**
     * 将exportExcel对象生成到指定路径
     *
     * @param exportExcel ExportExcel对象
     * @param path        指定路径
     * @param excelName   生成的文件名字
     */
    private static void createExcel(@Nullable ExportExcel exportExcel, String path, String excelName) {
        // 容器excel可以返回空对象
        if (exportExcel != null) {
            exportExcel.exportToLocalPath(excelName, path);
        }
    }

    /**
     * 获取数据并生成excel表格
     *
     * @param method      获取数据的方法
     * @param params      参数
     * @param exportExcel excel对象
     * @param rootPath    生成excel的路径
     */
    public static void baseExecuteAndCreateExcel(Method method,
                                                 Map<String, Object> params,
                                                 @Nullable BaseExportExcel exportExcel,
                                                 String rootPath) {
        LOG_HANDLER.around(() -> {
            BaseExportExcel baseExportExcel = baseExecute(method, params, exportExcel);
            createExcel(baseExportExcel, rootPath);
        }, params);
    }

    /**
     * 获取数据并生成excel表格--可定义生成的excel文件名
     * sheetName由exportExcel对象决定，但excel的文件名由fileNamePrefix决定
     *
     * @param method      获取数据的方法
     * @param params      参数
     * @param exportExcel excel对象
     * @param rootPath    生成excel的路径
     * @param fileName    生成的excel文件名
     */
    public static void baseExecuteAndCreateExcel(Method method,
                                                 Map<String, Object> params,
                                                 @Nullable BaseExportExcel exportExcel,
                                                 String rootPath,
                                                 String fileName) {
        LOG_HANDLER.around(() -> {
            BaseExportExcel baseExportExcel = baseExecute(method, params, exportExcel);
            createExcel(baseExportExcel, rootPath, fileName);
        }, params);
    }

    /**
     * 获取数据并生成excel表格
     *
     * @param methodAndExcel 获取数据的方法和对应的excel对象
     * @param params         参数
     * @param sheetName      生成的最外层excel文件名
     * @param rootPath       生成excel的路径
     */
    public static void containerExecuteAndCreateExcel(List<Map<String, Object>> methodAndExcel,
                                                      Map<String, Object> params,
                                                      String sheetName,
                                                      String rootPath) {
        LOG_HANDLER.around(() -> {
            ExportExcel exportExcel = containerExecute(methodAndExcel, params, sheetName);
            createExcel(exportExcel, rootPath);
        }, params);
    }

    /**
     * 获取数据并生成excel表格-每个获取数据的方法参数是不一样的，参数放在methodAndExcel每一项里面
     * 目前是一个科目多试卷，每张试卷是一个sheet时使用
     *
     * @param methodAndExcel 获取数据的方法和对应的excel对象、参数
     * @param sheetName      sheetName
     * @param rootPath       生成的excel路径
     */
    public static void containerExecuteAndCreateExcel(List<Map<String, Object>> methodAndExcel,
                                                      String sheetName,
                                                      String rootPath) {
        Map<String, Object> firstParams = null;
        // 设置containerExcel的SXSSF属性,一个sheet不启用,则没法启用
        AtomicBoolean allSxssf = new AtomicBoolean(true);

        ContainerExportExcel excelContainer = ContainerExportExcel.getExcelContainer(sheetName);
        for (Map<String, Object> item : methodAndExcel) {
            Method method = (Method) item.get("method");
            BaseExportExcel exportExcel = (BaseExportExcel) item.get("exportExcel");
            Map<String, Object> params = MapUtil.getCast(item, "params");
            // 生成excel时的参数设置为第一个params，但是paperId去掉，因为没法确定是那张试卷在渲染时出错的
            if (firstParams == null) {
                firstParams = new HashMap<>(params);
                firstParams.remove("paperId");
            }

            ExportExcel excel = LOG_HANDLER.around(() -> baseExecute(method, params, exportExcel), params);
            if (excel != null) {
                excelContainer.addSheet(excel);
                if (!excel.getEnableSXSSF()) {
                    allSxssf.set(false);
                }
            }
        }

        excelContainer.setEnableSXSSF(allSxssf.get());
        if (!excelContainer.isBankSheet()) {
            LOG_HANDLER.around(() -> createExcel(excelContainer, rootPath), firstParams);
        }
    }

    public static void containerExecuteAndCreateExcelWithBaseExportExcel(List<BaseExportExcel> exportExcelList,
                                                                         String sheetName,
                                                                         String rootPath,
                                                                         Map<String, Object> logParams) {
        AtomicBoolean allSxssf = new AtomicBoolean(true);

        ContainerExportExcel excelContainer = ContainerExportExcel.getExcelContainer(sheetName);
        for (BaseExportExcel exportExcel : exportExcelList) {
            if (exportExcel != null) {
                excelContainer.addSheet(exportExcel);
                if (!exportExcel.getEnableSXSSF()) {
                    allSxssf.set(false);
                }
            }
        }

        excelContainer.setEnableSXSSF(allSxssf.get());
        if (!excelContainer.isBankSheet()) {
            LOG_HANDLER.around(() -> createExcel(excelContainer, rootPath), logParams);
        }
    }
}
