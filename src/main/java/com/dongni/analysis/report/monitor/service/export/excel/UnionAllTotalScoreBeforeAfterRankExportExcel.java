package com.dongni.analysis.report.monitor.service.export.excel;

import com.dongni.analysis.report.monitor.service.export.excel.base.BaseExportExcel;
import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.bson.Document;

import java.util.LinkedHashMap;
import java.util.List;

import static com.dongni.common.report.excel.ExcelUtil.*;

/**
 * 联考整体-总分-前后N名分布
 *
 * <AUTHOR>
 * @Date 2023/7/26 下午 04:11
 * @Version 1.0.0
 */
public class UnionAllTotalScoreBeforeAfterRankExportExcel extends BaseExportExcel {

    /**
     * courseId -> data (总分courseId=0L)
     */
    private LinkedHashMap<Long, List<Document>> courseId2DataList;

    /**
     * 按排名和按比例相隔2列
     */
    private static final int COL_INTERVAL = 2;

    /**
     * 总分和单科也相隔2行
     */
    private static final int ROW_INTERVAL = 2;

    /**
     * 按比例的个数
     */
    private int proportionNum;

    /**
     * 按排名的个数
     */
    private int rankNum;

    public UnionAllTotalScoreBeforeAfterRankExportExcel(String sheetName) {
        super(sheetName);
    }

    /**
     * 不使用本方法,报表复杂,不适用header+body这种形式
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        processData();
        return 0;
    }

    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());

        // 循环遍历每一个单科
        for (List<Document> data : courseId2DataList.values()) {
            proportionNum = 0;
            rankNum = 0;

            Document firstData = data.get(0);
            String courseName = MapUtil.getString(firstData, "courseName", "总分");

            currentRow = genSingleCourseHeader(sheet, currentRow, currentCol, data, courseName, headerStyle);
            currentRow = genSingleCourseBody(sheet, currentRow, currentCol, data, bodyStyle) + ROW_INTERVAL;
        }

        return currentRow;
    }

    /**
     * 渲染单个课程的的头部
     *
     * @param sheet       sheet对象
     * @param currentRow  当前行
     * @param currentCol  当前列
     * @param data        单科的数据
     * @param courseName  课程名称
     * @param headerStyle 头部样式
     * @return 下一个可渲染的行序号
     */
    private int genSingleCourseHeader(Sheet sheet, int currentRow, int currentCol, List<Document> data, String courseName,
                                      CellStyle headerStyle) {
        Row row1 = sheet.createRow(currentRow);
        Row row2 = sheet.createRow(currentRow + 1);

        // ==================================按比例的表头===================================
        Document firstData = data.get(0);
        List<Document> rankList = MapUtil.getCast(firstData, "proportionBeforeRank");
        if (CollectionUtils.isEmpty(rankList)) {
            createCell(row1, currentCol, "全部缺考,暂无统计数据!", headerStyle);
            return currentRow;
        }

        int bakCol = currentCol;
        createCell(row1, currentCol, courseName + "-按比例", headerStyle);
        createCell(row2, currentCol++, "学校", headerStyle);
        // 按比例前
        for (Document str : rankList) {
            createCell(row2, currentCol++, "前" + str.getInteger("ranking") + "%", headerStyle);
            proportionNum++;
        }
        // 按比例后
        rankList = MapUtil.getCast(firstData, "proportionAfterRank");
        for (Document str : rankList) {
            createCell(row2, currentCol++, "后" + str.getInteger("ranking") + "%", headerStyle);
            proportionNum++;
        }
        addMergedRegion(sheet, currentRow, currentRow, bakCol, currentCol - 1);

        // ==================================按排名的表头===================================
        currentCol += COL_INTERVAL;
        bakCol = currentCol;
        createCell(row1, currentCol, courseName + "-按排名", headerStyle);
        createCell(row2, currentCol++, "学校", headerStyle);
        // 按排名前
        rankList = MapUtil.getCast(firstData, "beforeUnionRank");
        for (Document str : rankList) {
            createCell(row2, currentCol++, "前" + str.getInteger("ranking") + "名", headerStyle);
            rankNum++;
        }
        // 按排名后
        rankList = MapUtil.getCast(firstData, "afterUnionRank");
        for (Document str : rankList) {
            createCell(row2, currentCol++, "后" + str.getInteger("ranking") + "名", headerStyle);
            rankNum++;
        }
        addMergedRegion(sheet, currentRow, currentRow, bakCol, currentCol - 1);

        return currentRow + 2;
    }

    /**
     * 渲染单个课程的的body
     *
     * @param sheet      sheet对象
     * @param currentRow 当前行
     * @param currentCol 当前列
     * @param data       单科数据
     * @param bodyStyle  body样式
     * @return 下一个可渲染的行序号
     */
    private int genSingleCourseBody(Sheet sheet, int currentRow, int currentCol, List<Document> data, CellStyle bodyStyle) {
        int bakRow = currentRow;
        // 渲染最低分行
        Document firstData = data.get(0);
        Row row = createRow(sheet, currentRow++);
        createCell(row, currentCol++, "最低分", bodyStyle);
        List<Document> proportionBeforeRank = MapUtil.getCast(firstData, "proportionBeforeRank");
        // 全部缺考没有数据
        if (CollectionUtils.isNotEmpty(proportionBeforeRank)) {
            for (Document item : proportionBeforeRank) {
                createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("minScore")), bodyStyle);
            }
            List<Document> proportionAfterRank = MapUtil.getCast(firstData, "proportionAfterRank");
            for (Document item : proportionAfterRank) {
                createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("minScore")), bodyStyle);
            }
        } else {
            for (int i = 0; i < proportionNum; i++) {
                createCell(row, currentCol++, "-", bodyStyle);
            }
        }
        // 渲染学校数据-第一条数是全体的
        for (Document schoolData : data) {
            currentCol = 0;
            row = createRow(sheet, currentRow++);

            createCell(row, currentCol++, MapUtil.getString(schoolData, "schoolName", "总体"), bodyStyle);
            proportionBeforeRank = MapUtil.getCast(schoolData, "proportionBeforeRank");
            // 全部缺考没有数据
            if (CollectionUtils.isNotEmpty(proportionBeforeRank)) {
                for (Document item : proportionBeforeRank) {
                    createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("total")), bodyStyle);
                }
                List<Document> proportionAfterRank = MapUtil.getCast(schoolData, "proportionAfterRank");
                for (Document item : proportionAfterRank) {
                    createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("total")), bodyStyle);
                }
            } else {
                for (int i = 0; i < proportionNum; i++) {
                    createCell(row, currentCol++, "-", bodyStyle);
                }
            }
        }
        // ========================== ↑ 按比例 =============================== 按排名 ↓ ====================================
        currentCol += COL_INTERVAL;
        int bakCol = currentCol;
        row = sheet.getRow(bakRow++);
        createCell(row, currentCol++, "最低分", bodyStyle);
        List<Document> beforeUnionRank = MapUtil.getCast(firstData, "beforeUnionRank");
        // 全部缺考没有数据
        if (CollectionUtils.isNotEmpty(beforeUnionRank)) {
            for (Document item : beforeUnionRank) {
                createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("minScore")), bodyStyle);
            }
            List<Document> afterUnionRank = MapUtil.getCast(firstData, "afterUnionRank");
            for (Document item : afterUnionRank) {
                createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("minScore")), bodyStyle);
            }
        } else {
            for (int i = 0; i < rankNum; i++) {
                createCell(row, currentCol++, "-", bodyStyle);
            }
        }
        // 渲染学校数据-第一条数是全体的
        for (Document schoolData : data) {
            currentCol = bakCol;
            row = sheet.getRow(bakRow++);

            createCell(row, currentCol++, MapUtil.getString(schoolData, "schoolName", "总体"), bodyStyle);
            beforeUnionRank = MapUtil.getCast(schoolData, "beforeUnionRank");
            // 全部缺考没有数据
            if (CollectionUtils.isNotEmpty(beforeUnionRank)) {
                for (Document item : beforeUnionRank) {
                    createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("total")), bodyStyle);
                }
                List<Document> afterUnionRank = MapUtil.getCast(schoolData, "afterUnionRank");
                for (Document item : afterUnionRank) {
                    createCell(row, currentCol++, ExcelFormatUtil.forMatScore(item.get("total")), bodyStyle);
                }
            } else {
                for (int i = 0; i < rankNum; i++) {
                    createCell(row, currentCol++, "-", bodyStyle);
                }
            }
        }

        return bakRow;
    }

    /**
     * 初始化需要使用的数据
     */
    public void processData() {
        this.courseId2DataList = MapUtil.getCast(super.data, "data");
        this.proportionNum = 0;
        this.rankNum = 0;
    }
}
