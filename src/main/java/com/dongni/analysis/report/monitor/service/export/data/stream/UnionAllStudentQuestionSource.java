package com.dongni.analysis.report.monitor.service.export.data.stream;

import com.dongni.analysis.common.service.TranscriptPopulateService;
import com.dongni.analysis.config.bean.ScoreChanges;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.manager.base.ExamStudentPaperStatManager;
import com.dongni.analysis.report.monitor.service.export.pojo.CommonRequest;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.common.mongo.Order;
import com.dongni.exam.common.mark.serivice.mark.IPaperReadService;
import com.dongni.exam.common.mark.vo.PointDetailsVO;
import com.dongni.exam.common.mark.vo.PointVO;
import com.dongni.tiku.common.util.MapUtil;
import com.mongodb.Block;
import lombok.Getter;
import org.bson.Document;
import org.bson.conversions.Bson;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.*;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/5/12 周一 下午 05:26
 * @Version 1.0.0
 */
public class UnionAllStudentQuestionSource {
    private final ExamStudentPaperStatManager examStudentPaperStatManager;
    private final TranscriptPopulateService transcriptPopulateService;
    private final IPaperReadService paperReadService;
    private final ExamConfigService examConfigService;
    private final CommonCourseService commonCourseService;

    private final long examId;
    private final long statId;
    private final long paperId;
    private final long courseId;
    private Set<Long> courseIds; // courseId+courseId的子课程
    @Getter
    private final String courseName;

    private int curPage = 1;
    private int maxPage;

    // 每次查询N个学生
    private static final int LIMIT = 1000;

    private final String[] include = {"classId", "className", "studentId", "courseName", "totalScore", "correctRate",
                "studentName", "studentNum", "studentExamNum", "questions", "resultStatus", "schoolName", "originalScore",
                "scoreChangeLevelName"};
    private final Order order = Order.Field.desc("totalScore");

    public UnionAllStudentQuestionSource(ExamStudentPaperStatManager examStudentPaperStatManager,
                                         TranscriptPopulateService transcriptPopulateService,
                                         IPaperReadService paperReadService,
                                         ExamConfigService examConfigService,
                                         CommonCourseService commonCourseService,
                                         CommonRequest commonRequest) {
        this.examStudentPaperStatManager = examStudentPaperStatManager;
        this.transcriptPopulateService = transcriptPopulateService;
        this.paperReadService = paperReadService;
        this.examConfigService = examConfigService;
        this.commonCourseService = commonCourseService;
        this.examId = commonRequest.getExamId();
        this.statId = commonRequest.getStatId();
        this.paperId = commonRequest.getPaperId();
        this.courseName = commonRequest.getCourseName();
        this.courseId = commonRequest.getCourseId();

        courseIds = new HashSet<>(commonCourseService.getCourseIdsByCourseId(courseId));

        setMaxPage();
    }

    // 获取表头数据
    public HeaderInfo getQuestionHeader() {
        ScoreChanges scoreChanges = getScoreChanges();
        Map<String, Integer> qn2Index = new LinkedHashMap<>();
        List<String> result = new ArrayList<>();
        result.add("序号");
        result.add("学校");
        result.add("学号");
        result.add("考号");
        result.add("座位号");
        result.add("学籍号");
        result.add("班级");
        result.add("姓名");
        if (scoreChanges.levelScore(courseName)) {
            result.add("原始分");
            result.add("赋分");
            if (scoreChanges.levelName(courseName)) {
                result.add("赋分等级");
            }
        } else {
            result.add("总分");
        }
        int initIndex = result.size();

        List<PointDetailsVO> pointDetailsVOS = paperReadService.listPointDetails(examId, paperId);
        pointDetailsVOS = pointDetailsVOS.stream()
                .filter(i -> courseIds.contains(i.getCourseId()))
                .collect(Collectors.toList());
        for (PointDetailsVO pointDetailsVO : pointDetailsVOS) {
            String qn = String.valueOf(pointDetailsVO.getQuestionNumber());
            result.add(pointDetailsVO.getStructureNumber());
            qn2Index.put(qn, initIndex++);

            List<PointVO> points = pointDetailsVO.getPoint();
            for (PointVO point : points) {
                int index = point.getIndex();
                String structureNumber = point.getStructureNumber();
                result.add(structureNumber);
                qn2Index.put(qn + "-" + index, initIndex++);
            }
        }

        return new HeaderInfo(result, qn2Index);
    }

    // 分批获取学生小题分
    public List<Document> getStudentQuestions() {
        List<Bson> query = new ArrayList<>();
        query.add(eq("examId", examId));
        query.add(eq("statId", statId));
        query.add(eq("paperId", paperId));
        query.add(eq("courseId", courseId));

        List<Document> result = new ArrayList<>(LIMIT);
        if (curPage <= maxPage) {
            int skip = (curPage - 1) * LIMIT;
            examStudentPaperStatManager.getFindIterable(and(query), include, new String[]{"_id"}, order, skip, LIMIT)
                    .forEach((Block<Document>) result::add);
            curPage++;
        }

        // 填充座位号、学籍号
        transcriptPopulateService.populateStudentSeatNumberAndStudentNo(result);
        return result;
    }

    public ScoreChanges getScoreChanges() {
        Map<String, Object> params = MapUtil.of("examId", examId, "statId", statId);
        return examConfigService.getScoreChangeObj(params);
    }

    private void setMaxPage() {
        List<Bson> query = new ArrayList<>();
        query.add(eq("examId", examId));
        query.add(eq("statId", statId));
        query.add(eq("paperId", paperId));

        long count = examStudentPaperStatManager.count(and(query));
        maxPage = (int) (count + LIMIT - 1) / LIMIT;
    }

    @Getter
    public static class HeaderInfo {
        private final List<String> headers;

        private final Map<String, Integer> qn2Index;

        public HeaderInfo(List<String> headers, Map<String, Integer> qn2Index) {
            this.headers = headers;
            this.qn2Index = qn2Index;
        }
    }
}
