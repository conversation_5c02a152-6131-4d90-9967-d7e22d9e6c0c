package com.dongni.analysis.report.monitor.service.export.excel;

import com.dongni.analysis.report.monitor.service.export.enums.DataType;
import com.dongni.analysis.report.monitor.service.export.excel.base.BaseExportExcel;
import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.bson.Document;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dongni.common.report.excel.ExcelUtil.*;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * 题型均分
 * 使用：联考整体-单科
 *
 * <AUTHOR>
 * @Date 2022/12/29 下午 03:16
 * @Version 1.0.0
 */
public class SubjectTypeAvgExportExcel extends BaseExportExcel {
    private Map<Long, Document> allData;

    private Map<Long, List<Document>> itemData;

    private Map<String, Document> questionTypeName2AllDataItem;

    private DataType dataType;

    // 每张试卷需要清空一次
    private final Map<String, Integer> questionTypeName2Index = new HashMap<>();

    public SubjectTypeAvgExportExcel(String sheetName) {
        super(sheetName);
    }

    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        processData();

        return currentRow;
    }

    private int header(Sheet sheet, int currentRow, int currentCol, String paperName) {
        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        Row row0 = createRow(sheet, currentRow++);
        Row row1 = createRow(sheet, currentRow);
        Row row2 = createRow(sheet, currentRow + 1);

        createCell(row0, currentCol, "试卷:" + paperName, headerStyle);
        if (dataType.isSchool) {
            createCell(row1, currentCol++, "班级", headerStyle);
        } else {
            createCell(row1, currentCol++, "学校", headerStyle);
        }
        addMergedRegion(sheet, currentRow, currentRow + 1, currentCol - 1, currentCol - 1);

        // 清空列位置记录
        questionTypeName2Index.clear();
        for (Map.Entry<String, Document> entry : questionTypeName2AllDataItem.entrySet()) {
            Document value = entry.getValue();
            String questionTypeName = value.get("questionTypeName", String.class);
            questionTypeName2Index.put(questionTypeName, currentCol);
            createCell(row1, currentCol, questionTypeName, headerStyle);
            createCell(row2, currentCol++, "均分", headerStyle);
            createCell(row2, currentCol++, "得分率", headerStyle);
            createCell(row2, currentCol++, "得分率差", headerStyle);
            addMergedRegion(sheet, currentRow, currentRow, currentCol - 3, currentCol - 1);
        }
        questionTypeName2Index.put("总分", currentCol);
        createCell(row1, currentCol, "总分", headerStyle);
        createCell(row2, currentCol++, "均分", headerStyle);
        createCell(row2, currentCol++, "得分率", headerStyle);
        createCell(row2, currentCol++, "得分率差", headerStyle);
        addMergedRegion(sheet, currentRow, currentRow, currentCol - 3, currentCol - 1);

        return currentRow + 2;
    }

    private int body(Sheet sheet, int currentRow, int currentCol, Document allData, List<Document> itemData) {
        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle rateStyle = ExcelStyle.getRateStyle(sheet.getWorkbook());

        itemData.add(0, allData);

        int num = 0;
        for (Document data : itemData) {
            Row row = createRow(sheet, currentRow++);
            if (dataType.isSchool) {
                createCell(row, currentCol++, data.get("className"), bodyStyle);
            } else {
                createCell(row, currentCol++, data.get("schoolName"), bodyStyle);
            }
            List<Document> dataQuestionType = MapUtil.getCast(data, "questionType");
            // 缺考时为空
            if (CollectionUtils.isEmpty(dataQuestionType)) {
                // +1是因为总分
                for (int i = 0; i < questionTypeName2AllDataItem.size() + 1; i++) {
                    createCell(row, currentCol++, "缺", bodyStyle);
                    createCell(row, currentCol++, "缺", bodyStyle);
                    createCell(row, currentCol++, "缺", bodyStyle);
                }
            } else {
                dataQuestionType.sort(comparing(i -> MapUtil.getLong(i, "questionType")));

                for (Document type : dataQuestionType) {
                    String qtn = MapUtil.getString(type, "questionTypeName");
                    Document schoolQuestionType = questionTypeName2AllDataItem.get(qtn);
                    // 上级数据还没有统计这道题，那这道题就别显示了（阅卷中会出现这种情况）
                    if (schoolQuestionType == null) {
                        continue;
                    }
                    currentCol = questionTypeName2Index.get(qtn);
                    createCell(row, currentCol++, ExcelFormatUtil.forMatScore(type.get("averageScore")), bodyStyle);
                    createCell(row, currentCol++, ExcelFormatUtil.forMatRate(type.get("scoreRate")), rateStyle);
                    if (num != 0) {
                        BigDecimal subtract = new BigDecimal(type.get("scoreRate").toString())
                                .subtract(new BigDecimal(schoolQuestionType.get("scoreRate").toString()));
                        createCell(row, currentCol, ExcelFormatUtil.forMatRate(subtract), rateStyle);
                    } else {
                        createCell(row, currentCol, "-", bodyStyle);
                    }
                }

                // 总分
                String totalAverageScore = data.get("averageScore", String.class);
                String totalScoreRate = data.get("averageRate", String.class);
                currentCol = questionTypeName2Index.get("总分");
                createCell(row, currentCol++, ExcelFormatUtil.forMatScore(totalAverageScore), bodyStyle);
                createCell(row, currentCol++, ExcelFormatUtil.forMatRate(totalScoreRate), rateStyle);
                if (num != 0) {
                    BigDecimal subtract = new BigDecimal(data.get("averageRate").toString())
                            .subtract(new BigDecimal(allData.get("averageRate").toString()));
                    createCell(row, currentCol, ExcelFormatUtil.forMatRate(subtract), rateStyle);
                } else {
                    createCell(row, currentCol, "-", bodyStyle);
                }
            }
            num++;
            currentCol = 0;
        }
        return currentRow;
    }

    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        for (Map.Entry<Long, Document> item : this.allData.entrySet()) {
            Document value = item.getValue();
            List<Document> list = this.itemData.get(item.getKey());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            // 题型映射
            List<Document> schoolQuestionType = MapUtil.getCast(value, "questionType");
            this.questionTypeName2AllDataItem = schoolQuestionType.stream().collect(Collectors.toMap(
                    i -> MapUtil.getString(i, "questionTypeName"),
                    i -> i
            ));

            currentRow = header(sheet, currentRow, currentCol, value.getString("paperName"));
            currentRow = body(sheet, currentRow, 0, value, list);

            // 下一张试卷间隔3行
            currentRow += 3;
        }
        return currentRow;
    }

    private void processData() {
        List<Document> tmpAllData = MapUtil.getCast(super.data, "allData");
        List<Document> tmpItemDat = MapUtil.getCast(super.data, "itemData");

        this.allData = tmpAllData.stream().collect(toMap(i -> MapUtil.getLong(i, "paperId"), i -> i));
        this.itemData = tmpItemDat.stream().collect(groupingBy(i -> MapUtil.getLong(i, "paperId")));

        this.dataType = MapUtil.getCast(super.data, "dataType");

    }
}
