package com.dongni.analysis.report.monitor.service.export.entry;

import cn.hutool.core.io.FileUtil;
import com.dongni.analysis.manager.base.ExamStudentStatManager;
import com.dongni.analysis.report.monitor.service.export.enums.DownloadType;
import com.dongni.analysis.report.monitor.service.export.helper.DisplayHelper;
import com.dongni.analysis.report.monitor.service.export.pojo.ReportItem;
import com.dongni.analysis.view.report.service.ExamStatReportService;
import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import com.dongni.common.report.util.IllegalCharacterUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.export.CommonExamService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.dongni.analysis.report.monitor.service.export.helper.ExportHelper.compressDirectory;
import static com.dongni.analysis.report.monitor.service.export.helper.ExportHelper.exportZip;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/3/24 周一 上午 10:50
 * @Version 1.0.0
 */
@Service
public abstract class AbstractExcelReportEntry implements InitializingBean {
    @Autowired
    private ExamService examService;

    @Autowired
    private CourseServiceImpl courseService;

    @Autowired
    private ExamStatReportService examStatReportService;

    @Autowired
    private CommonExamService commonExamService;

    @Autowired
    private ExamStudentStatManager examStudentStatManager;

    /**
     * 新的入口嘞，现在允许勾选多个报告一起下载🤦🏻
     *
     * @return 整个压缩包的下载地址
     */
    public String multiStatEntry(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNotBlank("zipFileName")
                .isNotEmptyCollections("statIds")
                .verify();

        // 校级角色过滤下载项
        DisplayHelper.filterDownloadItem(params);

        // 考试名称
        Map<String, Object> exam = examService.getSimpleExamInfo(params);
        if (MapUtils.isEmpty(exam)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试不存在或已被删除");
        }
        String examName = MapUtil.getString(exam, "examName");

        // 课程名称
        AtomicReference<String> courseName = new AtomicReference<>(null);
        if (ObjectUtil.isValidId(params.get("courseId"))) {
            Map<String, Object> courseDetail = courseService.getCourseDetail(params);
            if (MapUtils.isEmpty(courseDetail)) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试课程已被删除");
            }
            courseName.set(MapUtil.getString(courseDetail, "courseName"));
        }

        // 下载
        ReportItem reportItem = getReportItem(params);
        List<Long> statIds = MapUtil.getListLong(params, "statIds");
        return FileStorageTemplate.put(fileStoragePut -> {
            String rootPath = fileStoragePut.getRootPath();
            // 每份报告依次下载
            for (Long statId : statIds) {
                Map<String, Object> tmpParams = new HashMap<>(params);
                tmpParams.put("statId", statId);
                Map<String, Object> stat = examStatReportService.getExamStatDetail(tmpParams);
                if (MapUtils.isEmpty(stat)) {
                    throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "报告不存在或已被删除");
                }
                String statName = MapUtil.getString(stat, "statName");

//                // 判断报告是否有学生 - 应对用户将报告全部学生都剔除的情况
//                Bson query = and(eq("examId", MapUtil.getLong(params, "examId")),
//                        eq("statId", statId));
//                Document first = examStudentStatManager.getFirst(query, new String[]{"_id"});
//                if (MapUtils.isEmpty(first)) {
//                    throw new CommonException(ResponseStatusEnum.DATA_ERROR,
//                            "报告：" + statName + "没有学生数据，请确认选考设置是否正确");
//                }

                // 设置当前报告的文件路径
                String directoryName = getDirectoryName(courseName.get(), examName, statName);

                entry(tmpParams, reportItem, rootPath + File.separator + directoryName);
            }

            // 空的删除掉
            Arrays.stream(FileUtil.ls(rootPath))
                    .filter(File::isDirectory)
                    .filter(FileUtil::isEmpty)
                    .forEach(FileUtil::del);

            List<File> statDir = Arrays.stream(FileUtil.ls(rootPath))
                    .filter(File::isDirectory)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(statDir)) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "没有文件生成");
            }

            // 压缩目录层次
            for (File file : statDir) {
                compressDirectory(file);
            }

            // 决定zip包路径和名称
            File zipRootDir = fileStoragePut.getRootDir();
            String zipFileName = MapUtil.getString(params, "zipFileName");
            if (CollectionUtils.size(statDir) == 1) {
                zipRootDir = statDir.get(0);
            }

            // 1.单个报告+全部科目  {报告名称}-{考试名称}.zip
            // 2.单个报告+单个科目  {科目名称}-{报告名称}-{考试名称}.zip
            // ============================================================================
            // 如果文件夹只有一个(有些报告不包含勾选的科目或学校),文件夹晋升为zip包
            // 3.多个报告+全部科目  {考试名称}.zip  {报告名称}-{考试名称}  ==>
            // 4.多个报告+单科科目  {科目名称}-{报告名称}.zip  {科目名称}-{报告名称}-{考试名称}
            exportZip(zipRootDir, zipFileName, fileStoragePut);
        });
    }

    public abstract ReportItem getReportItem(Map<String, Object> params);

    public abstract DownloadType getDownloadType();

    public abstract void entry(Map<String, Object> params, ReportItem reportItem, String directory);

    @Override
    public void afterPropertiesSet() throws Exception {
        EntryRegistry.registerEntry(getDownloadType(), this);
    }

    private String getDirectoryName(String courseName, String examName, String statName) {
        StringBuilder directoryName = new StringBuilder();
        if (courseName != null) {
            directoryName.append(courseName).append("-");
        }
        directoryName.append(statName).append("-");
        directoryName.append(examName);

        return IllegalCharacterUtil.normalizeFileName(directoryName.toString());
    }

    /**
     * 如果params里面classId为null，也就是没有传班级的情况需要对班级数据进行过滤
     * classList的数据过滤掉不是老师执教的班级
     *
     * @param params 基础参数
     * @param courseList 课程列表
     * @param classList 班级列表
     */
    protected void filterClass(Map<String, Object> params, List<Map<String, Object>> courseList, List<Map<String, Object>> classList) {
        if (!DictUtil.isEquals(MapUtil.getInt(params, "userType"), "userType", "teacher")) {
            return;
        }

        // 当前老师可见的课程
        List<Long> courseIds = courseList.stream().map(i -> MapUtil.getLong(i, "courseId"))
                .collect(Collectors.toList());
        courseIds.add(0L);

        // 根据这些课程得出考试中老师可见的班级（包含班主任的班级）
        Map<String, Object> copy = MapUtil.copy(params, "examId", "teacherId");
        copy.put("courseId", courseIds);
        Set<Long> classIds = new HashSet<>(commonExamService.getExamTeachingClassByCourse(copy));

        // 不在可见班级的去除掉
        classList.removeIf(i -> !classIds.contains(MapUtil.getLong(i, "classId")));
    }
}
