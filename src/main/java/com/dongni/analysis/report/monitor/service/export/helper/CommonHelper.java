package com.dongni.analysis.report.monitor.service.export.helper;

import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/3/20 周四 下午 01:54
 * @Version 1.0.0
 */
@Service
public class CommonHelper {
    @Autowired
    private CourseServiceImpl courseService;

    public void dealExamCourse(List<Map<String, Object>> es) {
        for (Map<String, Object> member : es) {
            String courseNames = member.get("courseName").toString();
            if (member.get("memberStr").toString().contains(",")) {
                List<String> names = new ArrayList<>();
                for (String id : member.get("memberStr").toString().split(",")) {
                    Map<String, Object> p = new HashMap<>();
                    p.put("courseId", id);
                    names.add(courseService.getCourseDetail(p).get("courseName").toString());
                }
                courseNames = StringUtils.join(names, ",");
            }
            member.put("courseNames", courseNames);
        }
    }
}
