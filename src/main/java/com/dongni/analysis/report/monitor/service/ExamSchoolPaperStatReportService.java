package com.dongni.analysis.report.monitor.service;

import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.report.monitor.export.ExamSchoolPaperStatExcelReport;
import com.dongni.analysis.report.monitor.export.ExamSchoolQuestionStat4ObjvExcelReport;
import com.dongni.analysis.report.monitor.export.ExamSchoolQuestionStatBidirectionalExcelReport;
import com.dongni.analysis.report.monitor.export.ExamSchoolQuestionStatCorrectExcelReport;
import com.dongni.analysis.report.monitor.export.ExamSchoolQuestionStatExcelReport;
import com.dongni.analysis.view.monitor.service.ExamSchoolPaperStatService;
import com.dongni.analysis.view.monitor.service.ExamSchoolQuestionStatService;
import com.dongni.common.report.excel.ExportExcel;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by zu<PERSON><PERSON> on 2019/1/21.
 *
 * 试卷分析 学校维度
 *  - 全科总览-命题质量
 *  - 双向细目表
 *  - 小题分析
 *  - 客观题选项分析
 */
@Service
public class ExamSchoolPaperStatReportService {

    @Autowired
    private ExamSchoolPaperStatService paperStatService;

    @Autowired
    private ExamSchoolQuestionStatService schoolQuestionStatService;

    @Autowired
    private ExamConfigService examConfigService;

    /**
     * 全科总览-命题质量
     * @return
     */
    public String getQualityDistribution(Map<String,Object> params) {
        List data = paperStatService.getScoreRate(params);
        ExportExcel report = new ExamSchoolPaperStatExcelReport("全科总览-命题质量",(List<Map<String,Object>>) data);
        return report.exportToFileStorage();
    }

    /**
     * 双向细目表
     * @param params params
     * @return
     */
    public String getPaperBidirectionalSheet(Map<String,Object> params){
        List<Map<String, Object>> data = schoolQuestionStatService.getScoreRate(params, true);
        Map<String, Object> data0 = data.get(0);
        String courseName = data0.get("courseName").toString();
        String paperName = data0.get("paperName").toString();

        // 需要隐藏的指标
        Set<String> hiddenIndex = new HashSet<>(examConfigService.getHiddenIndex(params));

        if (!hiddenIndex.contains("knowledge")) {
            for (Map<String, Object> dataMap : data) {
                // 知识点
                List<String> knowledgeNameList = new ArrayList<>();
                List<Map<String, Object>> knowledgeList = (List<Map<String, Object>>) dataMap.get("knowledge");
                if (knowledgeList != null) {
                    knowledgeList.forEach(o -> {
                        knowledgeNameList.add(o.get("knowledgeName").toString());
                    });
                }
                dataMap.put("knowledge", StringUtils.join(knowledgeNameList, "、"));
            }
        }

        ExportExcel report = new ExamSchoolQuestionStatBidirectionalExcelReport(courseName + paperName + "双向细目表", data,
          examConfigService.showCorrectRate(params), hiddenIndex);
        return report.exportToFileStorage();
    }

    /**
     * 小题分析
     * @param params params
     * @return
     */
    public String getQuestionDistribution(Map<String,Object> params){
        List<Map<String, Object>> data = schoolQuestionStatService.getScoreRate(params, true);
        Map<String, Object> data0 = data.get(0);
        String courseName = data0.get("courseName").toString();
        String paperName = data0.get("paperName").toString();

        // 需要隐藏的指标
        Set<String> hiddenIndex = new HashSet<>(examConfigService.getHiddenIndex(params));

        ExportExcel report = !examConfigService.showCorrectRate(params)
          ? new ExamSchoolQuestionStatExcelReport(courseName + paperName + "小题分析", data, hiddenIndex)
          : new ExamSchoolQuestionStatCorrectExcelReport(courseName + paperName + "小题分析", data, hiddenIndex);
        return report.exportToFileStorage();
    }

    /**
     *客观题选项分布
     * @param params
     * @return
     */
    public String getOptionRateDistribution(Map<String,Object> params) {
        List data = schoolQuestionStatService.getObjectiveOptions(params);
        ExportExcel report = new ExamSchoolQuestionStat4ObjvExcelReport("客观题选项排名",(List<Map<String,Object>>) data);
        return report.exportToFileStorage();
    }


}
