package com.dongni.analysis.report.monitor.export;

import com.dongni.analysis.config.bean.ScoreChanges;
import com.dongni.analysis.report.monitor.bean.StudentReportDisplayParams;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.dongni.common.report.excel.ExcelFormatUtil.forMatScore;
import static com.dongni.common.report.excel.ExcelUtil.createCell;
import static java.util.stream.Collectors.toMap;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/4/19.
 *
 * 学生题型得分报表
 */
public class StudentPaperSubjectTypeExcelReport extends ExportExcel {

    protected List<Map<String,Object>> data;

    protected LinkedHashMap<String,Long> questionType;

    protected String courseName;

    private final ScoreChanges scoreChanges;

    private final StudentReportDisplayParams studentReportDisplayParams;

    public StudentPaperSubjectTypeExcelReport(String sheetName,
                                              List<Map<String,Object>> data,
                                              ScoreChanges scoreChanges,
                                              StudentReportDisplayParams studentReportDisplayParams) {
        super(sheetName);

        this.data = data;
        this.scoreChanges = scoreChanges;
        this.studentReportDisplayParams = studentReportDisplayParams;
        questionType = new LinkedHashMap<>();
        if(data != null && !data.isEmpty()){
            courseName = (String) data.get(0).get("courseName");
            for (Map<String,Object> d : data){
                if("1".equals(d.get("resultStatus").toString())) continue;
                List<Map<String,Object>> st = (List) d.get("questionType");
                for (Map<String,Object> s : st){
                    questionType.put(s.get("questionTypeName").toString(),Long.valueOf(s.get("questionType").toString()));
                }
                break;
            }
        }
    }

    /**
     * 生成表头
     *
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        CellStyle style = ExcelStyle.getHeaderStyle(sheet.getWorkbook());

        // 第一行标题
        Row row1 = ExcelUtil.createRow(sheet,currentRow);
        createCell(row1,currentCol++,"姓名",style);
        createCell(row1,currentCol++,"班级",style);
        createCell(row1,currentCol++,"学号",style);
        createCell(row1,currentCol++,"考号",style);
        createCell(row1,currentCol++,"座位号",style);
        createCell(row1,currentCol++,"学籍号",style);
        if (scoreChanges.levelScore(courseName)) {
            if (studentReportDisplayParams.isDisplayStudentScore()) {
                createCell(row1,currentCol++,"原始分",style);
                createCell(row1,currentCol++,"赋分",style);
            }
            if (scoreChanges.levelName(courseName)) {
                createCell(row1,currentCol++,"赋分等级",style);
            }
        } else {
            if (studentReportDisplayParams.isDisplayStudentScore()) {
                createCell(row1,currentCol++,"总分",style);
            }
        }
        for (String questionTypeName : questionType.keySet()){
            createCell(row1,currentCol++,questionTypeName,style);
        }
        return currentRow+1;
    }

    /**
     * 生成表体
     *
     */
    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        if(data == null || data.isEmpty()) return currentRow;

        CellStyle style = ExcelStyle.getBodyStyle(sheet.getWorkbook());

        for (Map<String,Object> cs : data){
            int col = currentCol;
            Row row = ExcelUtil.createRow(sheet,currentRow);

            // 学生
            createCell(row,col++,cs.get("studentName"),style);
            createCell(row,col++,cs.get("className"),style);
            createCell(row,col++,cs.get("studentNum"),style);
            createCell(row,col++,cs.get("studentExamNum"),style);
            createCell(row,col++,cs.get("seatNumber"),style);
            createCell(row,col++,cs.get("studentNo"),style);

            int resultStatus = Integer.valueOf(cs.get("resultStatus").toString());
            if(resultStatus == 1){
                if (scoreChanges.levelScore(courseName)) {
                    if (studentReportDisplayParams.isDisplayStudentScore()) {
                        createCell(row,col++,"缺",style);
                        createCell(row,col++,"缺",style);
                    }
                    if (scoreChanges.levelName(courseName)) {
                        createCell(row,col++,"缺",style);
                    }
                } else {
                    if (studentReportDisplayParams.isDisplayStudentScore()) {
                        createCell(row,col++,"缺",style);
                    }
                }

                for (String questionTypeName : questionType.keySet()){
                    createCell(row,col++,"缺",style);
                }
            }
            else {
                if (scoreChanges.levelScore(courseName)) {
                    if (studentReportDisplayParams.isDisplayStudentScore()) {
                        createCell(row,col++,forMatScore(cs.get("originalScore")),style);
                        createCell(row,col++,forMatScore(cs.get("totalScore")),style);
                    }
                    if (scoreChanges.levelName(courseName)) {
                        createCell(row,col++,cs.get("scoreChangeLevelName"),style);
                    }
                } else {
                    if (studentReportDisplayParams.isDisplayStudentScore()) {
                        createCell(row,col++,forMatScore(cs.get("totalScore")),style);
                    }
                }

                // 题型
                List<Map<String,Object>> st = (List) cs.get("questionType");
                Map<String,Map<String,Object>> stg = st.stream().collect(toMap(s->s.get("questionTypeName").toString(), a->a));
                for (String questionTypeName : questionType.keySet()){
                    if(stg.containsKey(questionTypeName)){
                        createCell(row,col++,forMatScore(stg.get(questionTypeName).get("finallyScore")),style);
                    }else {
                        createCell(row,col++,"",style);
                    }
                }
            }
            currentRow++;
        }

        return currentRow;
    }
}
