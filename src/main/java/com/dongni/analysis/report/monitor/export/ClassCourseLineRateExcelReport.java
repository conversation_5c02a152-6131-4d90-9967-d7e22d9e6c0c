package com.dongni.analysis.report.monitor.export;

import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExportExcel;
import java.util.Collections;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.List;
import java.util.Map;

import static com.dongni.common.report.excel.ExcelUtil.*;


/**
 * Created by Heweipo on 2018/3/7.
 */
public class ClassCourseLineRateExcelReport extends ExportExcel {

    private Map<String,Object> data;

    private Map<String,Object> totalScore;

    private List<Map<String,Object>> classScore;

    private List<String> fields;

    public ClassCourseLineRateExcelReport(String sheetName, Map<String,Object> data){
        super(sheetName);
        this.data = data;

        totalScore = (Map) data.get("school");
        classScore = (List) data.get("class");
        fields = (List<String>) data.get("line");
    }

    /**
     * 生成表头
     *
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        if(null == data || 0 == data.size()) {
            return currentRow;
        }

        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle rateStyle = ExcelStyle.getRateStyle(sheet.getWorkbook());

        Row row1 = createRow(sheet,currentRow);
        Row row2 = createRow(sheet,currentRow+1);
        Row row3 = createRow(sheet,currentRow+2);

        // 第一列
        createCell(row1,currentCol,"班级",headerStyle);
        addMergedRegion(sheet,currentRow,currentRow+1,currentCol,currentCol);

        if (totalScore != null) {
            createCell(row3,currentCol,"全校",bodyStyle);
        }

        // 第二列
        createCell(row1,currentCol+1,"诊断人数",headerStyle);
        addMergedRegion(sheet,currentRow,currentRow,currentCol+1,currentCol+2);

        createCell(row2,currentCol+1,"应参",headerStyle);
        createCell(row2,currentCol+2,"实参",headerStyle);

        if (totalScore != null) {
            createCell(row3,currentCol+1,totalScore.get("totalStudent"),bodyStyle);
            createCell(row3,currentCol+2,totalScore.get("participationNumber"),bodyStyle);
        }

        // 第三列
        currentCol += 3;
        for (String field : fields){
            Map<String,Object> r;
            if(totalScore == null || !totalScore.containsKey(field)) {
                r = (Map) classScore.get(0).getOrDefault(field, Collections.emptyMap());
            } else {
                r = (Map) totalScore.get(field);
            }

            createCell(row1,currentCol,field.replace("line", "")+"线("+ ExcelFormatUtil.forMatScore(r.get("score"))+"分)",headerStyle);
            addMergedRegion(sheet,currentRow,currentRow,currentCol,currentCol+3);

            createCell(row2,currentCol,"人数",headerStyle);
            createCell(row2,currentCol+1,"上线率",headerStyle);
            createCell(row2,currentCol+2,"上临界",headerStyle);
            createCell(row2,currentCol+3,"下临界",headerStyle);

            createCell(row3,currentCol,r.get("totalUp"),bodyStyle);
            createCell(row3,currentCol+1,ExcelFormatUtil.forMatRate(r.get("upRate")),rateStyle);
            createCell(row3,currentCol+2,r.get("criticalUp"),bodyStyle);
            createCell(row3,currentCol+3,r.get("criticalDown"),bodyStyle);

            currentCol+=4;
        }

        // 第四列
        createCell(row1,currentCol,"最高分",headerStyle);
        addMergedRegion(sheet,currentRow,currentRow+1,currentCol,currentCol);
        if (totalScore != null) {
            createCell(row3,currentCol,ExcelFormatUtil.forMatScore(totalScore.get("highestScore")),bodyStyle);
        }

        // 第五列
        currentCol+=1;
        createCell(row1,currentCol,"班主任",headerStyle);
        addMergedRegion(sheet,currentRow,currentRow+1,currentCol,currentCol);
        if (totalScore != null) {
            createCell(row3,currentCol,"",bodyStyle);
        }

        return totalScore == null ? currentRow + 2 : currentRow + 3;
    }

    /**
     * 生成表体
     *
     */
    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        if(null == data || 0 == data.size()) {
            return currentRow;
        }

        CellStyle style = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle rateStyle = ExcelStyle.getRateStyle(sheet.getWorkbook());

        for (Map<String,Object> m : classScore){
            int col = currentCol;
            Row row = createRow(sheet,currentRow++);
            createCell(row,col++,m.get("className"),style);
            createCell(row,col++,m.get("totalStudent"),style);
            createCell(row,col++,m.get("participationNumber"),style);

            for (String field : fields){
                if(!m.containsKey(field)){
                    createCell(row,col++,"-",style);
                    createCell(row,col++,"-",style);
                    createCell(row,col++,"-",style);
                    createCell(row,col++,"-",style);
                }else {
                    Map<String,Object> r = (Map) m.get(field);
                    createCell(row,col++,r.get("totalUp"),style);
                    createCell(row,col++,ExcelFormatUtil.forMatRate(r.get("upRate")),rateStyle);
                    createCell(row,col++,r.get("criticalUp"),style);
                    createCell(row,col++,r.get("criticalDown"),style);
                }
            }
            Integer resultStatus = Integer.valueOf(m.get("resultStatus").toString());
            if (resultStatus.equals(1)){
                createCell(row,col++,"缺",style);
            }else {
                createCell(row,col++,ExcelFormatUtil.forMatScore(m.get("highestScore")),style);
            }
            createCell(row,col++,m.get("headerName"),style);
        }

        return currentRow;
    }

}
