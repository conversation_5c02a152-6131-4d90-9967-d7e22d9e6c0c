package com.dongni.analysis.report.monitor.export;

import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.common.utils.DictUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.*;

import static com.dongni.common.report.excel.ExcelUtil.*;
import static java.util.stream.Collectors.toMap;

/**
 * Created by zuGer on 2019/1/19.
 * <p>
 * 校级档次分布导出-课程维度
 */
public class SchoolCourseConsistStatExcelReport extends ExportExcel {

    private List<Map<String, Object>> data;

    private List<Map<String, Object>> consistHeaders;

    private Map<String, Object> consistHeadersTotal;

    // 每一个档次需要渲染的字段
    private Map<String, String> preConsistHeaders = new LinkedHashMap<>();

    public SchoolCourseConsistStatExcelReport(String sheetName, List<Map<String, Object>> data, List<Map<String, Object>> totalMapDocuments) {
        super(sheetName);
        this.data = data;

        Map<String, Object> consistMap = data.get(0);
        consistHeaders = (List<Map<String, Object>>) consistMap.get("consist");

        consistHeadersTotal = totalMapDocuments.get(0);

        preConsistHeaders.put("人数", "number");
        preConsistHeaders.put("比例", "rate");
        preConsistHeaders.put("累计人数", "accumulativeNum");
        preConsistHeaders.put("累计比例", "accumulativeRate");
    }

    /**
     * 生成表头
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {

        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());

        Row row1 = createRow(sheet, currentRow);
        Row row2 = createRow(sheet, currentRow + 1);

        //
        createCell(row1, currentCol, "总分", headerStyle);
        addMergedRegion(sheet, currentRow, currentRow + 1, currentCol, currentCol);
        createCell(row1, currentCol + 1, "实参人数", headerStyle);
        addMergedRegion(sheet, currentRow, currentRow + 1, currentCol + 1, currentCol + 1);
        createCell(row1, currentCol + 2, "平均分", headerStyle);
        addMergedRegion(sheet, currentRow, currentRow + 1, currentCol + 2, currentCol + 2);
        createCell(row1, currentCol + 3, "最高分", headerStyle);
        addMergedRegion(sheet, currentRow, currentRow + 1, currentCol + 3, currentCol + 3);
        createCell(row1, currentCol + 4, "最低分", headerStyle);
        addMergedRegion(sheet, currentRow, currentRow + 1, currentCol + 4, currentCol + 4);

        currentCol += 5;

        // 档次
        for (Map<String, Object> couseItemMap : consistHeaders) {

            String lineName = couseItemMap.get("name").toString();
            createCell(row1, currentCol, lineName, headerStyle);
            addMergedRegion(sheet, currentRow, currentRow, currentCol, currentCol + preConsistHeaders.size() - 1);

            for (String preConsistHeader : preConsistHeaders.keySet()) {
                createCell(row2, currentCol++, preConsistHeader, headerStyle);
            }
        }
        return currentRow + 2;
    }

    /**
     * 生成表体
     */
    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        CellStyle style = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle rateStyle = ExcelStyle.getRateStyle(sheet.getWorkbook());

        Row row = createRow(sheet, currentRow++);

        //  联考总体
        Map<String, Object> totalRow = consistHeadersTotal;
        createCell(row, currentCol, "跨校诊断总体", style);
        createCell(row, currentCol + 1, totalRow.get("participationNumber"), style);
        createCell(row, currentCol + 2, ExcelFormatUtil.forMatScore(totalRow.get("averageScore")), style);
        createCell(row, currentCol + 3, ExcelFormatUtil.forMatScore(totalRow.get("highestScore")), style);
        createCell(row, currentCol + 4, ExcelFormatUtil.forMatScore(totalRow.get("lowestScore")), style);
        int col = currentCol + 5;

        List<Map<String, Object>> documents = (List<Map<String, Object>>) totalRow.get("consist");
        // 渲染的字段
        Collection<String> values = preConsistHeaders.values();

        Map<String, Map<String, Object>> consistMap = consistHeaders.stream().collect(toMap(s -> s.get("key").toString(), a -> a));
        for (Map<String, Object> mapItem : documents) {
            if (consistMap.containsKey(mapItem.get("key"))) {
                createCell(row, col++, mapItem.get("number"), style);
                createCell(row, col++, ExcelFormatUtil.forMatRate(mapItem.get("rate")), rateStyle);
                createCell(row, col++, mapItem.get("accumulativeNum"), style);
                createCell(row, col++, ExcelFormatUtil.forMatRate(mapItem.get("accumulativeRate")), rateStyle);
            } else {
                for (String _ : values) {
                    createCell(row, col++, "", style);
                }
            }
        }

        // 参考学校
        for (Map<String, Object> map : data) {
            row = createRow(sheet, currentRow++);
            boolean absent = DictUtil.isEquals(MapUtil.getInt(map, "resultStatus"), "resultStatus", "absent");

            createCell(row, currentCol, map.get("schoolName"), style);
            if (absent) {
                createCell(row, currentCol + 1, "缺", style);
                createCell(row, currentCol + 2, "缺", style);
                createCell(row, currentCol + 3, "缺", style);
                createCell(row, currentCol + 4, "缺", style);
            } else {
                createCell(row, currentCol + 1, map.get("participationNumber"), style);
                createCell(row, currentCol + 2, ExcelFormatUtil.forMatScore(map.get("averageScore")), style);
                createCell(row, currentCol + 3, ExcelFormatUtil.forMatScore(map.get("highestScore")), style);
                createCell(row, currentCol + 4, ExcelFormatUtil.forMatScore(map.get("lowestScore")), style);
            }
            col = currentCol + 5;

            documents = (List<Map<String, Object>>) map.get("consist");
            if (absent) {
                int loopCount = values.size() * consistMap.size();
                for (int i = 0; i < loopCount; i++) {
                    createCell(row, col++, "缺", style);
                }
            } else {
                for (Map<String, Object> mapItem : documents) {
                    if (consistMap.containsKey(mapItem.get("key"))) {
                        createCell(row, col++, mapItem.get("number"), style);
                        createCell(row, col++, ExcelFormatUtil.forMatRate(mapItem.get("rate")), rateStyle);
                        createCell(row, col++, mapItem.get("accumulativeNum"), style);
                        createCell(row, col++, ExcelFormatUtil.forMatRate(mapItem.get("accumulativeRate")), rateStyle);
                    } else {
                        for (String _ : values) {
                            createCell(row, col++, "", style);
                        }
                    }
                }
            }
        }
        return currentRow;
    }
}
