package com.dongni.analysis.report.monitor.export;

import com.dongni.common.report.excel.ExcelFormatUtil;
import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.bson.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dongni.common.report.excel.ExcelUtil.createCell;

/**
 * Created by zuGer
 * time: 11:50 2019/1/30
 * description:区域上线率
 */
public class ExamAreaCourseUpLineExcelReport extends ExportExcel {

    /**
     * 表体填充数据
     */
    private List<Document> data;
    private String line;
    private Document config;
    private Map<String,Integer> courseCol = new HashMap<>();


    public ExamAreaCourseUpLineExcelReport(String sheetName,
                                           List<Document> data,
                                           String line,Document config) {
        super(sheetName);
        this.data = data;
        this.line = line;
        this.config = config;
    }

    /**
     * 生成表头
     *
     * @param sheet      sheet
     * @param currentRow 当前行
     * @return 下一行
     */
    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        CellStyle headerStyle = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        Row row1 = sheet.createRow(currentRow);
        Row row2 = sheet.createRow(currentRow+1);
        List<String> headers = new ArrayList<>();
        List<String> scores = new ArrayList<>();

        ExcelUtil.createCell(row1, currentCol, "上级对象", headerStyle);
        ExcelUtil.addMergedRegion(sheet, currentRow, currentRow + 1, currentCol, currentCol);

        ExcelUtil.createCell(row1, currentCol+1, "当前对象", headerStyle);
        ExcelUtil.addMergedRegion(sheet, currentRow, currentRow + 1, currentCol+1, currentCol+1);

        ExcelUtil.createCell(row1, currentCol+2, "类别", headerStyle);
        ExcelUtil.addMergedRegion(sheet, currentRow, currentRow + 1, currentCol+2, currentCol+2);

        ExcelUtil.createCell(row1, currentCol+3, "实参人数", headerStyle);
        ExcelUtil.addMergedRegion(sheet, currentRow, currentRow + 1, currentCol+3, currentCol+3);

        Document lineConfig = config.get("line"+line,Document.class);
        String score= (String) lineConfig.getOrDefault("score","");
        if (ObjectUtil.isBlank(score)){
            ExcelUtil.createCell(row1, currentCol+4, "总分", headerStyle);
        }else {
            ExcelUtil.createCell(row1, currentCol + 4,
                    "总分" + "(" + ExcelFormatUtil.forMatScore(score) + "分)", headerStyle);
        }
        ExcelUtil.createCell(row2, currentCol+4, "上线人数", headerStyle);

        List<Document> course = lineConfig.get("course", List.class);
        for (Document doc : course) {
            headers.add(doc.get("courseName").toString());
            scores.add(ExcelFormatUtil.forMatScore(doc.get("score")).toString());
        }

        currentCol+=5;
        for (int i = 0; i < headers.size(); i++) {
            ExcelUtil.createCell(row1, currentCol, headers.get(i) + "("+ scores.get(i) +"分)", headerStyle);
            ExcelUtil.addMergedRegion(sheet, currentRow, currentRow, currentCol, currentCol+3);
            courseCol.put(headers.get(i),currentCol);
            ExcelUtil.createCell(row2, currentCol, "单上线人数", headerStyle);
            ExcelUtil.createCell(row2, currentCol+1, "双上线人数", headerStyle);
            ExcelUtil.createCell(row2, currentCol+2, "命中率", headerStyle);
            ExcelUtil.createCell(row2, currentCol+3, "贡献率", headerStyle);

            currentCol+=4;
        }
        return currentRow+2;
    }


    /**
     * 生成表体
     *
     * @param sheet      sheet
     * @param currentRow 当前行
     * @return 下一行
     */
    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {
        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());
        CellStyle rateStyle = ExcelStyle.getRateStyle(sheet.getWorkbook());
        String[] fields = {"parentName", "currentName", "schoolPropertyName", "participationNumber","line"+line, "course"};
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(currentRow);
            Document document = data.get(i);
            for (String field : fields) {
                if(field.contains("course")) {
                    List<Document> o = document.get("course",List.class);
                    for(Document doc : o){
                        Document lineX = (Document)doc.getOrDefault("line" + line,new Document());
                        Integer col = courseCol.get(doc.getString("courseName"));
                        if (col!=null){
                            createCell(row,col,ExcelFormatUtil.format4Absence(lineX.getOrDefault("singleUp",0)),bodyStyle);
                            createCell(row,col+1,ExcelFormatUtil.format4Absence(lineX.getOrDefault("doubleUp",0)),bodyStyle);
                            createCell(row,col+2,ExcelFormatUtil.forMatRate(lineX.getOrDefault("hitRate",0)),rateStyle);
                            createCell(row,col+3,ExcelFormatUtil.forMatRate(lineX.getOrDefault("contributionRate",0)),rateStyle);
                        }
                    }
                }else if (field.contains("line"+line)){
                    Document lineDoc = (Document) document.get(field);
                    if (lineDoc!=null){
                        ExcelUtil.createCell(row, currentCol++, lineDoc.getOrDefault("totalUp",""), bodyStyle);
                    }else {
                        ExcelUtil.createCell(row, currentCol++,"", bodyStyle);
                    }
                } else{
                    Object o = document.get(field);
                    String schoolPropertyName = "";
                    if (ObjectUtil.isValueEquals("schoolPropertyName", field)) {
                        if (ObjectUtil.isNotBlank(document.get("schoolProperty"))) {
                            schoolPropertyName = ObjectUtil.isValueEquals(document.get("schoolProperty"),
                                    DictUtil.getDictValue("schoolProperty", "public")) ? DictUtil.getDictLabel("schoolProperty", "public")
                                    : DictUtil.getDictLabel("schoolProperty", "private");
                            o = schoolPropertyName;
                        }
                    }
                    ExcelUtil.createCell(row, currentCol++, o, bodyStyle);
                }
            }
            ++currentRow;
            currentCol = 0;
        }
        return ++currentRow;
    }

}
