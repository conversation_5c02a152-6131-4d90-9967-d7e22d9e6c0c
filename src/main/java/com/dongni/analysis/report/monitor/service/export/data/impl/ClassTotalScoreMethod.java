package com.dongni.analysis.report.monitor.service.export.data.impl;

import com.dongni.analysis.report.monitor.service.export.data.AbstractExportMethod;
import com.dongni.analysis.report.monitor.service.export.enums.DataType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 班级总分
 *
 * <AUTHOR>
 * @Date 2023/7/26 上午 10:54
 * @Version 1.0.0
 */
@Service
public class ClassTotalScoreMethod extends AbstractExportMethod {
    @Autowired
    private MixedMethod mixedMethod;

    public ClassTotalScoreMethod() {
        super.supportDataType = DataType.CLASS_TOTAL_SCORE;
    }

    @Override
    public Map<String, Object> getStudentResult(Map<String, Object> params) {
        return mixedMethod.getSchoolTotalScoreStudentResult(params);
    }

    @Override
    public Map<String, Object> getDistribution(Map<String, Object> params) {
        return mixedMethod.getClassTotalScoreDistribution(params);
    }

    @Override
    public Map<String, Object> getScoreSection(Map<String, Object> params) {
        return mixedMethod.getClassTotalScoreScoreSection(params);
    }

    @Override
    public Map<String, Object> getBeforeAfterRank(Map<String, Object> params) {
        return mixedMethod.getClassTotalScoreBeforeAfterRank(params);
    }

    @Override
    public Map<String, Object> getUpLine(Map<String, Object> params) {
        return mixedMethod.getClassTotalScoreUpLine(params);
    }

    @Override
    public Map<String, Object> getScoreRate(Map<String, Object> params) {
        return mixedMethod.getClassTotalScoreScoreRate(params);
    }

    @Override
    public Map<String, Object> getHighestScore(Map<String, Object> params) {
        return mixedMethod.getClassTotalScoreHighestScore(params);
    }

    @Override
    public Map<String, Object> getWeakStudent(Map<String, Object> params) {
        return mixedMethod.getSchoolTotalScoreWeakStudent(params);
    }

    @Override
    public Map<String, Object> getCriticalStudent(Map<String, Object> params) {
        return mixedMethod.getSchoolTotalScoreCriticalStudent(params);
    }
}
