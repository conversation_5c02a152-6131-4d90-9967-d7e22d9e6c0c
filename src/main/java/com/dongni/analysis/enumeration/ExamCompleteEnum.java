package com.dongni.analysis.enumeration;

/**
* <AUTHOR>  统计服务考试完成统计类枚举
* @Date 20:02 20/12/2018
**/
public enum ExamCompleteEnum {

    EXAM_COMPLETE_SERVICE("ExamCompleteService"),

    COMPUTE_All("computeAll"),
    COMPUTE_CLASS("computeClass"),
    COMPUTE_ALL_CLASS("computeAllClass");

    private String name;

    ExamCompleteEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

}
