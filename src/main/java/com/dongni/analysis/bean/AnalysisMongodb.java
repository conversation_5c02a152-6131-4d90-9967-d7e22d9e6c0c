package com.dongni.analysis.bean;

import com.dongni.common.mongo.PoolSettingMongoClientManager;
import com.dongni.common.threadpool.MyAsyncConfigurer;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * Create by sapluk <br/>
 * time 21:41 2018/12/05 <br/>
 * description: <br/>
 */
@Component
@ConfigurationProperties("spring.data.mongodb." + AnalysisConfig.APP)
public class AnalysisMongodb extends PoolSettingMongoClientManager {

    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;

    /**
     * 考试学校试题统计
     */
    public static final String COLLECTION_EXAM_SCHOOL_QUESTION_STAT = "examSchoolQuestionStat";

    /**
     * 考试班级课程统计
     */
    public static final String COLLECTION_EXAM_CLASS_COURSE_STAT = "examClassCourseStat";

    /**
     * 考试试题统计
     */
    public static final String COLLECTION_EXAM_QUESTION_STAT = "examQuestionStat";

    /**
     * 考试区域试题统计
     */
    public static final String COLLECTION_EXAM_AREA_QUESTION_STAT = "examAreaQuestionStat";

    /**
     * 考试学校课程统计
     */
    public static final String COLLECTION_EXAM_SCHOOL_COURSE_STAT = "examSchoolCourseStat";

    /**
     * 考试学生统计
     */
    public static final String COLLECTION_EXAM_STUDENT_STAT = "examStudentStat";

    /**
     * 考试学生课程统计
     */
    public static final String COLLECTION_EXAM_STUDENT_COURSE_STAT = "examStudentCourseStat";

    /**
     * 考试统计
     */
    public static final String COLLECTION_EXAM_STAT = "examStat";

    /**
     * 考试学校统计
     */
    public static final String COLLECTION_EXAM_SCHOOL_STAT = "examSchoolStat";

    /**
     * 考试课程统计
     */
    public static final String COLLECTION_EXAM_COURSE_STAT = "examCourseStat";

    /**
     * 考试上线统计
     */
    public static final String COLLECTION_EXAM_LINE_STAT = "examLineStat";

    /**
     * 考试课程上线统计
     */
    public static final String COLLECTION_EXAM_COURSE_LINE_STAT = "examCourseLineStat";

    /**
     * 考试学校上线统计
     */
    public static final String COLLECTION_EXAM_SCHOOL_LINE_STAT = "examSchoolLineStat";

    /**
     * 考试学校课程上线统计
     */
    public static final String COLLECTION_EXAM_SCHOOL_COURSE_LINE_STAT = "examSchoolCourseLineStat";

    /**
     * 考试档次统计
     */
    public static final String COLLECTION_EXAM_CONSIST_STAT = "examConsistStat";

    /**
     * 考试课程档次统计
     */
    public static final String COLLECTION_EXAM_COURSE_CONSIST_STAT = "examCourseConsistStat";

    /**
     * 考试学校档次统计
     */
    public static final String COLLECTION_EXAM_SCHOOL_CONSIST_STAT = "examSchoolConsistStat";

    /**
     * 考试学校课程档次统计
     */
    public static final String COLLECTION_EXAM_SCHOOL_COURSE_CONSIST_STAT = "examSchoolCourseConsistStat";

    /**
     * 考试班级试卷统计
     */
    public static final String COLLECTION_EXAM_CLASS_PAPER_STAT = "examClassPaperStat";

    /**
     * 考试学生试卷统计
     */
    public static final String COLLECTION_EXAM_STUDENT_PAPER_STAT = "examStudentPaperStat";

    /**
     * 考试班级试题统计
     */
    public static final String COLLECTION_EXAM_CLASS_QUESTION_STAT = "examClassQuestionStat";

    /**
     * 考试学生组合课程统计
     */
    public static final String COLLECTION_EXAM_STUDENT_COURSE_COMBINE_STAT = "examStudentCourseCombineStat";

    /**
     * 考试学生小题明细统计
     */
    public static final String COLLECTION_EXAM_ITEM_STAT = "examItemStat";

    /**
     * 区别报告用户指标可见性
     */
    public static final String COLLECTION_AREA_REPORT_USER_DISPLAY_INDEX = "areaReportUserDisplayIndex";

    /**
     * 考试配置
     */
    public static final String COLLECTION_EXAM_CONFIG = "examConfig";

    /**
     * 考试班级统计
     */
    public static final String COLLECTION_EXAM_CLASS_STAT = "examClassStat";

    public static final String COLLECTION_EXAM_AREA_PAPER_STAT = "examAreaPaperStat";

    /**
     * 统计报告的学生
     */
    public static final String COLLECTION_EXAM_STAT_STUDENT = "examStatStudent";

    @PostConstruct
    private void initIndex() {

        //examStudentPaperStat索引
        dropIndex(COLLECTION_EXAM_STUDENT_PAPER_STAT, "examId_-1_statId_1");

        //examStudentCourseStat索引
        dropIndex(COLLECTION_EXAM_STUDENT_COURSE_STAT, "examId_-1_statId_1");

        //examItemStat索引
        dropIndex(COLLECTION_EXAM_ITEM_STAT, "examId_1_statId_1");

        //大表的操作使用异步执行
        Objects.requireNonNull(myAsyncConfigurer.getAsyncExecutor()).execute(() -> {

            //examStudentStat索引
            initIndex(COLLECTION_EXAM_STUDENT_STAT, "examId_1_statId_1_schoolId_1_examRanking_1",
              new Document("examId", 1).append("statId", 1).append("schoolId", 1).append("examRanking", 1));

            //examStudentPaperStat索引
//            initIndex(COLLECTION_EXAM_STUDENT_PAPER_STAT, "examId_1_statId_1_paperId_1",
//              new Document("examId", 1).append("statId", 1).append("paperId", 1));
            dropIndex(COLLECTION_EXAM_STUDENT_PAPER_STAT, "examId_1_paperId_1_statId_1");
            dropIndex(COLLECTION_EXAM_STUDENT_PAPER_STAT, "examId_1.0_paperId_1.0_statId_1.0");
            initIndex(COLLECTION_EXAM_STUDENT_PAPER_STAT, "examId_1_statId_1_paperId_1_courseId_1_totalScore_-1",
                    new Document("examId", 1).append("statId", 1).append("paperId", 1).append("courseId", 1).append("totalScore", -1));

            //examStudentCourseCombineStat索引
            initIndex(COLLECTION_EXAM_STUDENT_COURSE_COMBINE_STAT, "examId_1_statId_1_courseId_1_studentId_1",
              new Document("examId", 1).append("statId", 1).append("courseId", 1).append("studentId", 1));
            dropIndex(COLLECTION_EXAM_STUDENT_COURSE_COMBINE_STAT, "examId_1_courseId_1_statId_1_studentId_1");
            dropIndex(COLLECTION_EXAM_STUDENT_COURSE_COMBINE_STAT, "examId_1_course_1_statId_1_student_1");
            dropIndex(COLLECTION_EXAM_STUDENT_COURSE_COMBINE_STAT, "examId_-1_statId_1");

            //examStudentCourseStat索引
            initIndex(COLLECTION_EXAM_STUDENT_COURSE_STAT, "examId_1_statId_1_courseId_1_studentId_1",
              new Document("examId", 1).append("statId", 1).append("courseId", 1).append("studentId", 1));
            dropIndex(COLLECTION_EXAM_STUDENT_COURSE_STAT, "examId_1_courseId_1_statId_1_studentId_1");
            dropIndex(COLLECTION_EXAM_STUDENT_COURSE_STAT, "examId_1_course_1_statId_1_student_1");
            initIndex(COLLECTION_EXAM_STUDENT_COURSE_STAT, "examId_1_statId_1_paperId_1",
              new Document("examId", 1).append("statId", 1).append("paperId", 1));
            dropIndex(COLLECTION_EXAM_STUDENT_COURSE_STAT, "examId_1_paperId_1_statId_1");
            dropIndex(COLLECTION_EXAM_STUDENT_COURSE_STAT, "examId_1.0_paperId_1.0_statId_1.0");

            //examItemStat索引
            initIndex(COLLECTION_EXAM_ITEM_STAT, "examId_1_statId_1_schoolId_1",
              new Document("examId", 1).append("statId", 1).append("schoolId", 1));

            //examClassQuestionStat索引
            initIndex(COLLECTION_EXAM_CLASS_QUESTION_STAT, "examId_1_statId_1_courseId_1_paperId_1_classId_1",
              new Document("examId", 1).append("statId", 1).append("courseId", 1).append("paperId", 1).append("classId", 1));
            initIndex(COLLECTION_EXAM_CLASS_QUESTION_STAT, "examId_1_statId_1_paperId_1_classId_1",
              new Document("examId", 1).append("statId", 1).append("paperId", 1).append("classId", 1));
            initIndex(COLLECTION_EXAM_CLASS_QUESTION_STAT, "examId_1_statId_1_schoolId_1_courseId_1_paperId_1_classId_1",
              new Document("examId", 1).append("statId", 1).append("schoolId", 1).append("courseId", 1).append("paperId", 1)
                .append("classId", 1));
            dropIndex(COLLECTION_EXAM_CLASS_QUESTION_STAT, "examId_-1_statId_1");
            dropIndex(COLLECTION_EXAM_CLASS_QUESTION_STAT, "examSchoolClassPaperStat");

            //examSchoolQuestionStat索引
            initIndex(COLLECTION_EXAM_SCHOOL_QUESTION_STAT, "examId_1_statId_1_courseId_1_paperId_1",
              new Document("examId", 1).append("statId", 1).append("courseId", 1).append("paperId", 1));
            initIndex(COLLECTION_EXAM_SCHOOL_QUESTION_STAT, "examId_1_statId_1_schoolId_1_courseId_1_paperId_1",
              new Document("examId", 1).append("statId", 1).append("schoolId", 1).append("courseId", 1).append("paperId", 1));
            dropIndex(COLLECTION_EXAM_SCHOOL_QUESTION_STAT, "examId_1_schoolId_1_paperId_1_statId_1");

            //examClassCourseStat索引
            initIndex(COLLECTION_EXAM_CLASS_COURSE_STAT, "examId_1_statId_1_schoolId_1",
              new Document("examId", 1).append("statId", 1).append("schoolId", 1));
            dropIndex(COLLECTION_EXAM_CLASS_COURSE_STAT, "examId_-1_statId_1");

            //examClassPaperStat索引
            initIndex(COLLECTION_EXAM_CLASS_PAPER_STAT, "examId_1_statId_1_paperId_1_courseId_1",
              new Document("examId", 1).append("statId", 1).append("paperId", 1).append("courseId", 1));
            initIndex(COLLECTION_EXAM_CLASS_PAPER_STAT, "examId_1_statId_1_schoolId_1_paperId_1_courseId_1",
              new Document("examId", 1).append("statId", 1).append("schoolId", 1).append("paperId", 1).append("courseId", 1));
            dropIndex(COLLECTION_EXAM_CLASS_PAPER_STAT, "examId_-1_statId_1");
        });

        // areaReportUserDisplayIndex索引
        initIndex(COLLECTION_AREA_REPORT_USER_DISPLAY_INDEX, "examId_1_userId_1_statId_1",
          new Document("examId", 1).append("userId", 1).append("statId", 1));
        initIndex(COLLECTION_EXAM_AREA_PAPER_STAT, "examId_1_statId_1",  new Document("examId", 1).append("statId", 1));
        initIndex("paperReadLLMPrompt","paperReadId_1",new Document("paperReadId", 1));
    }
}
