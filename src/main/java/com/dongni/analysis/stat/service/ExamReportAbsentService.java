package com.dongni.analysis.stat.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.common.report.excel.ExcelReport;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.common.report.excel.simple.SimpleExcelReport;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.plan.service.ExamStudentService;
import com.mongodb.client.MongoCollection;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;

/**
 * <AUTHOR>
 * 2019-05-27
 * 获取考试缺考学生信息
 */
@Service
public class ExamReportAbsentService {

    private MongoCollection<Document> examStatReport;

    @Autowired
    private ExamStudentService examStudentService;

    @Autowired
    public ExamReportAbsentService(AnalysisMongodb mongodb) {
        examStatReport = mongodb.getMongoDatabase().getCollection("examStatReport");
    }


    public Map<String,Object> getAbsentStudent(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("examId")
                .isNotBlank("statId")
                .isNotBlank("courseId")
                .isNotBlank("schoolId")
                .verify();
        List<Map<String,Object>> resultList = new ArrayList<>();
        Map<String,Object> resultMap = new HashMap<>();
        //转换courseId为list
        String[] courseIdsStr = params.get("courseId").toString().split(",");
        List<Long> courseIds = dealIds(courseIdsStr);
        //转换schoolIds
        String[] schoolIdsStr = params.get("schoolId").toString().split(",");
        List<Long> schoolIds = dealIds(schoolIdsStr);
        //转换classIds
        Document report = examStatReport.find(
                and(
                eq("examId", Long.valueOf(params.get("examId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString()))
                )).first();
        List<Map<String,Object>> examSchool = (List)report.get("examSchool");
        List<Long> classIds = new ArrayList<>();
        for (Map<String,Object> map:examSchool) {
            List<Map<String,Object>> list = (List)map.get("examClass");
            for (Map<String,Object> map1:list){
                classIds.add(Long.valueOf(map1.get("classId").toString()));
            }
        }
        params.put("classIds",classIds);
        //构造查询条件
        params.put("courseIds",courseIds);
        params.put("schoolIds",schoolIds);
        List<Map<String,Object>> student = examStudentService.getAbsentStudent(params);
        if (CollectionUtils.isEmpty(student)){
            resultMap.put("totalCount",0);
            resultMap.put("list",resultList);
        }
        Map<Long,List<Map<String,Object>>> studentCourseMap = student
                .stream().collect(Collectors.groupingBy(s->Long.valueOf(s.get("studentId").toString())));
        for (Map.Entry<Long,List<Map<String,Object>>> entry:studentCourseMap.entrySet()){
            Map<String,Object> d = entry.getValue().get(0);
            StringBuilder sb = new StringBuilder();
            for (Map<String,Object> document:entry.getValue()){
                sb.append(document.get("courseName")+",");
            }
            sb.deleteCharAt(sb.lastIndexOf(","));
            d.put("absentCourseName",sb.toString());
            resultList.add(d);
        }
        Long currentIndex = Long.valueOf(params.get("currentIndex").toString());
        Long pageSize = Long.valueOf(params.get("pageSize").toString());
        resultMap.put("totalCount",resultList.size());
        resultMap.put("list",resultList.stream().skip(currentIndex).limit(pageSize).collect(Collectors.toList()));
        return resultMap;
    }

    private List<Long> dealIds(String[] idsStr) {
        List<Long> ids = new ArrayList<>();
        for (String s:idsStr){
            ids.add(Long.valueOf(s));
        }
        return ids;
    }

    public String getAbsentStudentExcel(Map<String,Object> params) {
        params.put("currentIndex",0);
        params.put("pageSize",Long.MAX_VALUE);
        Map<String,Object> map = getAbsentStudent(params);
        List<Map<String,Object>> student = (List<Map<String,Object>>) map.get("list");

        SimpleExcelHeader headers = new SimpleExcelHeader(
                Arrays.asList("schoolName","studentName","studentNum","className","absentCourseName"),
                Arrays.asList("学校","姓名","学号","班级","缺考课程"));
        ExcelReport report = new SimpleExcelReport(student, headers);
        return report.exportToFileStorage("缺考学生名单");
    }
}
