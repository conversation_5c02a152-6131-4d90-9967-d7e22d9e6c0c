package com.dongni.analysis.stat.controller;

import com.dongni.analysis.stat.service.ExamReportStatService;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 2018-11-09
 * 获取统计报告基础信息
 */
@RestController
@RequestMapping("/analysis/export/exam/class")
public class ExamReportStatController extends BaseController {


    @Autowired
    private ExamReportStatService examReportStatService;

    /**
     * 获取联考班级信息
     * <p>
     * params examId schoolId  statId
     */
    @GetMapping("/stat")
    public Response getClassStat() {
        return new Response(examReportStatService.getUnionClassStat(getParameterMap()));
    }

    /**
     * 获取联考课程-班级信息
     * params examId statId courseId schoolId
     */
    @GetMapping("/course/stat")
    public Response getUnionClassCourseStat() {
        return new Response(examReportStatService.getUnionClassCourseStat(getParameterMap()));
    }


}
