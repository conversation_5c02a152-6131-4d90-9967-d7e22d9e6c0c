package com.dongni.analysis.area.view.exam.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.manager.area.ExamAreaQuestionStatManager;
import com.dongni.analysis.manager.exam.ExamQuestionStatManager;
import com.dongni.analysis.view.monitor.utils.PaperAnalysisHiddenIndexUtil;
import com.dongni.analysis.view.monitor.utils.QuestionDifficultyConvert;
import com.dongni.analysis.manager.AnalysisQuery;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.NumberFormatUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.plan.service.ExamAreaService;
import com.dongni.tiku.common.util.MapUtil;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Sorts;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.dongni.common.mongo.Order.Field.asc;
import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.*;
import static java.util.stream.Collectors.groupingBy;

/**
 * Created by scott
 * time: 9:58 2018/6/11
 * description:试卷分析 就是examPaper这层级数据
 */
@Service
public class ExamAreaQuestionStatService {
    /**
     * 获取Mongodb数据库对象
     */
    private MongoDatabase mongo;

    private ExamAreaService examAreaService;

    @Autowired
    private ExamAreaQuestionStatManager examAreaQuestionStatManager;

    @Autowired
    private ExamQuestionStatManager examQuestionStatManager;

    @Autowired
    private ExamConfigService examConfigService;

    @Autowired
    public ExamAreaQuestionStatService(AnalysisMongodb analysisMongodb,ExamAreaService examAreaService) {
        this.mongo = analysisMongodb.getMongoDatabase();
        this.examAreaService=examAreaService;
    }

    /**
     * 获取小题
     * @param params examId paperId
     * @return 小题
     */
    public List<Map<String, Object>> getQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("courseId")
                .verify();
        // 查询参数
        List<Bson> queryParams = new ArrayList<>();
        queryParams.add(eq("examId",Long.valueOf(params.get("examId").toString())));
        queryParams.add(eq("paperId",Long.valueOf(params.get("paperId").toString())));
        queryParams.add(eq("courseId",Long.valueOf(params.get("courseId").toString())));
        queryParams.add(eq("statId", ObjectUtil.isValidId(params.get("statId"))?Long.valueOf(params.get("statId").toString()):0L));

        // 查询字段
        String[] queryFields = new String[]{
                "examName",
                "courseName",
                "paperName",
                "lowestScore","highestScore",            // 最低分，最高分
                "questionNumber", "structureNumber",     // 题目编号 显示题目
                "questionTypeName",                      // 题型名称  选择题/填空题....
                "difficultyCoefficient",                 // 难度系数
                "discrimination",                        // 区分度
                "participationNumber",                   // 参考人数
                "scoreValue",                            // 分值
                "averageScore",                          // 平均分
                "fullScoreRate",                         // 满分率
                "zeroScoreRate",                         // 零分率
                "readType",                              // 阅卷类型 0：不需要批阅，1系统批阅(客观题)，2老师批阅
                "answer",                                // 正确答案
                "optionSection",                         // 客观题的选项结果占比
//                "subjectiveSection",                   // 主观题的得分结果占比
                "knowledge",                             // 知识点信息
                "mark"
        };

        List<Map<String, Object>> scoreRateList = mongo.getCollection("examQuestionStat")
                .find(and(queryParams))
                .projection(fields(include(queryFields), excludeId()))
                .sort(Sorts.ascending("questionNumber"))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(scoreRateList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "区域该试卷还未统计");
        }
        // 需要隐藏的指标
        Set<String> hiddenIndex = new HashSet<>(examConfigService.getHiddenIndex(params));

        // 处理困难等级及小数点
        scoreRateList.forEach(o -> {
            // 处理难度等级   简单 中等 困难
            Double difficultyCoefficient = Double.valueOf(o.get("difficultyCoefficient").toString());
            o.put("difficultyCoefficient", NumberFormatUtil.formatByScale(difficultyCoefficient, 2));
            o.put("discrimination", NumberFormatUtil.formatByScale(o.get("discrimination"), 2));
            // 处理预测难度
            QuestionDifficultyConvert.predictionDifficultyConvert(MapUtil.getCast(o, "predictionDifficulty"), false);
            // 隐藏指标
            PaperAnalysisHiddenIndexUtil.hiddenQuestionAnalysis(o, hiddenIndex);
        });
        return scoreRateList;
    }

    /**
     * 获取客观题
     * @param params examId paperId
     * @return 客观题
     */
    public List<Document> getObjectiveQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .isValidId("paperId")
                .isValidId("courseId")
                .verify();

        // 查询条件
        Bson and = and(eq("examId", Long.parseLong(params.get("examId").toString())),
                eq("statId", Long.parseLong(params.get("statId").toString())),
                eq("readType", 1),
                eq("courseId", Long.parseLong(params.get("courseId").toString())),
                eq("paperId", Long.parseLong(params.get("paperId").toString())));

        // 查询字段
        String[] queryFields = new String[]{
                "courseId",
                "courseName",
                "paperId",
                "answer",
                "readType",
                "structureNumber",
                "averageScore",
                "discrimination",
                "difficultyCoefficient",
                "standardDeviation",
                "questionNumber",
                "participationNumber",
                "knowledge",
                "optionSection",
                "questionTypeName",
                "scoreValue"
        };
        // 数据查询
        return mongo.getCollection("examQuestionStat")
                .find(and)
                .projection(fields(include(queryFields), excludeId()))
                .sort(Sorts.ascending("questionNumber"))
                .into(new ArrayList<>());
    }



    /**
     * 获取区域课程
     *
     * @param params examId statId courseId areaCode  parentAreaCode
     * @return 区域课程
     */
    public Map<String, Object> getExamAreaQuestion(Map<String, Object> params) {
        //参数校验
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .isValidId("paperId")
                .isValidId("courseId")
                .isNotBlank("areaCode")
                .isNotBlank("parentAreaCode")
                .verify();

        //先查出能看的区域及学校areaCode
        List<Map<String, Object>> examArea = examAreaService.getExamArea(params);
        Map<String, Object> rs = new HashMap<>();
        int totalCount = examArea.size();
        rs.put("totalCount", totalCount);
        if (totalCount == 0) {
            rs.put("list", new ArrayList<>());
            return rs;
        }
        Integer pageSize = (Integer) params.get("pageSize");
        Integer currentIndex = (Integer) params.get("currentIndex");
        int start = currentIndex*pageSize;
        int end = (currentIndex+1)*pageSize;

        List<Map<String, Object>> list = examArea.subList(start, (end>totalCount)?totalCount:end);

        Set<Long> areaId = new HashSet<>();
        List<Long> schoolId = new ArrayList<>();
        for(Map<String,Object> map : list){
            if(map.containsKey("schoolId")){
                schoolId.add(Long.valueOf(map.get("schoolId").toString()));
            }else {
                areaId.add(Long.valueOf(map.get("areaId").toString()));
            }
        }

        Bson and = and(eq("examId", Long.valueOf(params.get("examId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())),
                eq("courseId", Long.valueOf(params.get("courseId").toString())),
                eq("paperId", Long.valueOf(params.get("paperId").toString())));


        Map<Long, List<Document>> areaQuestionMap = new HashMap<>();
        Map<Long, List<Document>> schoolQuestionMap = new HashMap<>();
        //区域数据
        if(CollectionUtils.isNotEmpty(areaId)){
            Bson areaAnd = and(and, in("areaId", areaId));
            List<Document> areaQuestion = mongo.getCollection("examAreaQuestionStat")
                    .find(areaAnd)
                    .projection(fields(include(
                            "courseId", "courseName", "structureNumber", "averageScore",
                            "paperId", "questionNumber", "participationNumber", "areaId", "areaCode", "areaName","point"
                            ),
                            excludeId())).into(new ArrayList<>());
            areaQuestionMap = areaQuestion.stream().collect(groupingBy(a -> a.getLong("areaId")));

        }
        //学校数据
        if(CollectionUtils.isNotEmpty(schoolId)){
            Bson schoolAnd = and(and, in("schoolId", schoolId));
            List<Document> schoolQuestion = mongo.getCollection("examSchoolQuestionStat")
                    .find(schoolAnd)
                    .projection(fields(include(
                            "schoolId","schoolName","courseId", "courseName", "structureNumber", "averageScore",
                            "paperId", "questionNumber", "participationNumber", "areaId", "areaCode", "areaName","point"
                            ),
                            excludeId())).into(new ArrayList<>());
            schoolQuestionMap = schoolQuestion.stream().collect(groupingBy(a -> a.getLong("schoolId")));
        }

        List<Map<String,Object>> remove = new ArrayList<>();
        for(Map<String,Object> map : list){
            if(map.containsKey("schoolId")){
                List<Document> schoolList = schoolQuestionMap.get(Long.valueOf(map.get("schoolId").toString()));
                if(CollectionUtils.isNotEmpty(schoolList)){
                    schoolList.sort(Comparator.comparing(s->Long.valueOf(s.get("questionNumber").toString())));
                    map.put("question",schoolList);
                    map.put("areaId",schoolList.get(0).get("areaId"));
                    map.put("areaName",schoolList.get(0).get("areaName"));
                    map.put("participationNumber",schoolList.get(0).get("participationNumber"));
                }else {//报告中可能移除的此学校再统计
                    remove.add(map);
                }
            }else {
                List<Document> areaList = areaQuestionMap.get(Long.valueOf(map.get("areaId").toString()));
                if(CollectionUtils.isNotEmpty(areaList)){
                    areaList.sort(Comparator.comparing(s->Long.valueOf(s.get("questionNumber").toString())));
                    map.put("question",areaList);
                    map.put("areaId",areaList.get(0).get("areaId"));
                    map.put("areaName",areaList.get(0).get("areaName"));
                    map.put("participationNumber",areaList.get(0).get("participationNumber"));
                }
            }
        }

        list.removeAll(remove);
        rs.put("list",list);
        return rs;

    }

    /**
     * 报告 试卷分析
     * 获取试题得分率
     * @param params
     *  examId   考试Id
     *  areaId 区域Id
     *  paperId  试卷Id
     * @return 试题得分率
     */
    public List<Map<String, Object>> getExamAreaScoreRate(Map<String,Object> params, boolean hidden){
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .verify();
        // 查询参数
        AnalysisQuery query = AnalysisQuery.of(params)
                .appendBaseQuery()
                .appendPaperId()
                .appendLong("courseId", true);

        // 查询字段
        String[] queryFields = new String[]{
                "examName",
                "courseName",
                "paperName",
                "lowestScore","highestScore",            // 最低分，最高分
                "questionNumber", "structureNumber",     // 题目编号 显示题目
                "questionTypeName",                      // 题型名称  选择题/填空题....
                "difficultyCoefficient",                 // 难度系数
                "discrimination",                        // 区分度
                "participationNumber",                   // 参考人数
                "scoreValue",                            // 分值
                "averageScore",                          // 平均分
                "fullScoreRate",                         // 满分率
                "zeroScoreRate",                         // 零分率
                "readType",                              // 阅卷类型 0：不需要批阅，1系统批阅(客观题)，2老师批阅
                "answer",                                // 正确答案
                "optionSection.total",                   // 客观题的选项结果占比
                "optionSection.rate",                    // 客观题的选项结果占比
                "optionSection.section",                 // 客观题的选项结果占比
                "knowledge",                             // 知识点信息
                "mark",                                // 核心素养
                "point"                                  // 批分点
        };

        List<Document> scoreRateList = examQuestionStatManager.getList(
                query, queryFields, AnalysisQuery.id(), asc("questionNumber")
        );

        if (CollectionUtils.isEmpty(scoreRateList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "该试卷还没统计");
        }
        // 需要隐藏的指标
        Set<String> hiddenIndex = new HashSet<>(examConfigService.getHiddenIndex(params));

        // 处理困难等级及小数点
        scoreRateList.forEach(o -> {
            QuestionDifficultyConvert.convert(o);
            // 预测难度转换
            QuestionDifficultyConvert.predictionDifficultyConvert(MapUtil.getCast(o, "mark"), true);
            if (hidden) {
                PaperAnalysisHiddenIndexUtil.hiddenTableOfSpecification(o, hiddenIndex);
                PaperAnalysisHiddenIndexUtil.hiddenQuestionAnalysis(o, hiddenIndex);
            }

            if (o.containsKey("point")) {
                List<Document> pointList = MapUtil.getCast(o, "point");
                pointList.forEach(p -> {
                    QuestionDifficultyConvert.convert(p);
                    if (hidden) {
                        PaperAnalysisHiddenIndexUtil.hiddenTableOfSpecification(p, hiddenIndex);
                        PaperAnalysisHiddenIndexUtil.hiddenQuestionAnalysis(p, hiddenIndex);
                    }
                });
            }
        });
        // 太多地方调用了，还是转成原本的map返回
        return scoreRateList.stream().map(HashMap::new).collect(Collectors.toList());
    }

    /**
     * 报告 试卷分析
     * 获取试题得分率
     * @param params
     *  examId   考试Id
     *  areaId 区域Id
     *  paperId  试卷Id
     * @return 试题得分率
     */
    public List<Map<String, Object>> getRealExamAreaScoreRate(Map<String,Object> params){
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("courseId")
                .verify();
        // 查询参数
        AnalysisQuery query = AnalysisQuery.of(params)
                .appendBaseQuery()
                .appendPaperId()
                .appendCourseId()
                .appendAreaId();

        // 查询字段
        String[] queryFields = new String[]{
                "examName",
                "courseName",
                "paperName",
                "lowestScore","highestScore",            // 最低分，最高分
                "questionNumber", "structureNumber",     // 题目编号 显示题目
                "questionTypeName",                      // 题型名称  选择题/填空题....
                "difficultyCoefficient",                 // 难度系数
                "discrimination",                        // 区分度
                "participationNumber",                   // 参考人数
                "scoreValue",                            // 分值
                "averageScore",                          // 平均分
                "fullScoreRate",                         // 满分率
                "zeroScoreRate",                         // 零分率
                "readType",                              // 阅卷类型 0：不需要批阅，1系统批阅(客观题)，2老师批阅
                "answer",                                // 正确答案
                "optionSection",                         // 客观题的选项结果占比
                "knowledge",                             // 知识点信息
                "mark",                                   // 核心素养
                "point"                                  // 批分点
        };
        List<Document> scoreRateList = examAreaQuestionStatManager.getList(query, queryFields, AnalysisQuery.id(), asc("questionNumber"));
        if (CollectionUtils.isEmpty(scoreRateList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "区域该试卷还未统计");
        }

// 处理困难等级及小数点
        scoreRateList.forEach(o -> {
            QuestionDifficultyConvert.convert(o);
            if (o.containsKey("point")) {
                List<Map<String, Object>> pointList = MapUtil.getCast(o, "point");
                pointList.forEach(QuestionDifficultyConvert::convert);
            }
        });
        // 太多地方调用了，还是转成原本的map返回
        return scoreRateList.stream().map(HashMap::new).collect(Collectors.toList());
    }

    /**
     * 试卷分析 客观题选项分布
     * 获取客观题选项分布
     *
     * @param params -
     *  examId   考试Id
     *  paperId  试卷Id
     * @return 客观题选项分布
     */
    public List<Map<String, Object>> getObjectiveOptions(Map<String, Object> params) {
        List<Map<String, Object>> data = getExamAreaScoreRate(params, false);
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> d : data) {
            if (d.get("readType").toString().equals("1")) {
                d.put("difficultyCoefficient", NumberFormatUtil.formatByScale(d.get("difficultyCoefficient"), 2));
                d.put("discrimination", NumberFormatUtil.formatByScale(d.get("discrimination"), 2));

                // 知识点
                List<String> knowledgeNameList = new ArrayList<>();
                List<Map<String, Object>> knowledgeList = (List<Map<String, Object>>) d.get("knowledge");
                if (knowledgeList != null) {
                    knowledgeList.forEach(o -> {
                        knowledgeNameList.add(o.get("knowledgeName").toString());
                    });
                }
                d.put("knowledge", StringUtils.join(knowledgeNameList, "、"));

                Map<String, Object> optionRate = new LinkedHashMap<>();
                // 选项解析
                if (d.get("optionSection") != null) {
                    List<Map<String, Object>> ls = (List<Map<String, Object>>) d.get("optionSection");
                    Comparator<Map<String, Object>> rateComparator = Comparator.comparing(o -> Double.valueOf(o.get("rate").toString()));
                    ls.sort(rateComparator.reversed());
                    double topThreeRate = 0.0;
                    for (int i = 0, iLen = Math.min(3, ls.size()); i < iLen; i++) {
                        topThreeRate += Double.parseDouble(ls.get(i).get("rate").toString());
                        String option = ls.get(i).get("section") +
                                "(" +
                                NumberFormatUtil.forMatRate(ls.get(i).get("rate")) +
                                ")";
                        optionRate.put("option" + i, option);
                    }
                    double optionOther = 1-topThreeRate;
                    optionRate.put("optionOther", optionOther == 0 ? "" : NumberFormatUtil.forMatRate(optionOther));
                    d.put("optionRate", optionRate);
                }
                result.add(d);
            }
        }
        return result;
    }

    /**
     * 试卷分析 客观题选项分布
     * 获取客观题选项分布
     *
     * @param params -
     *  examId   考试Id
     *  paperId  试卷Id
     * @return 客观题选项分布
     */
    public List<Map<String, Object>> getRealObjectiveOptions(Map<String, Object> params) {
        List<Map<String, Object>> data = getRealExamAreaScoreRate(params);
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> d : data) {
            if (d.get("readType").toString().equals("1")) {
                d.put("difficultyCoefficient", NumberFormatUtil.formatByScale(d.get("difficultyCoefficient"), 2));
                d.put("discrimination", NumberFormatUtil.formatByScale(d.get("discrimination"), 2));

                // 知识点
                List<String> knowledgeNameList = new ArrayList<>();
                List<Map<String, Object>> knowledgeList = (List<Map<String, Object>>) d.get("knowledge");
                if (knowledgeList != null) {
                    knowledgeList.forEach(o -> {
                        knowledgeNameList.add(o.get("knowledgeName").toString());
                    });
                }
                d.put("knowledge", StringUtils.join(knowledgeNameList, "、"));

                Map<String, Object> optionRate = new LinkedHashMap<>();
                // 选项解析
                if (d.get("optionSection") != null) {
                    List<Map<String, Object>> ls = (List<Map<String, Object>>) d.get("optionSection");
                    Comparator<Map<String, Object>> rateComparator = Comparator.comparing(o -> Double.valueOf(o.get("rate").toString()));
                    ls.sort(rateComparator.reversed());
                    double topThreeRate = 0.0;
                    for (int i = 0, iLen = Math.min(3, ls.size()); i < iLen; i++) {
                        topThreeRate += Double.parseDouble(ls.get(i).get("rate").toString());
                        String option = ls.get(i).get("section") +
                                "(" +
                                NumberFormatUtil.forMatRate(ls.get(i).get("rate")) +
                                ")";
                        optionRate.put("option" + i, option);
                    }
                    double optionOther = 1-topThreeRate;
                    optionRate.put("optionOther", optionOther == 0 ? "" : NumberFormatUtil.forMatRate(optionOther));
                    d.put("optionRate", optionRate);
                }
                result.add(d);
            }
        }
        return result;
    }
}
