package com.dongni.analysis.area.view.exam.controller;

import com.dongni.analysis.area.view.exam.service.ExamAreaStatService;
import com.dongni.analysis.area.view.exam.uril.ExamAreaDataPageUtil;
import com.dongni.analysis.view.monitor.service.ExamStatService;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Created by scott
 * time: 9:58 2018/6/11
 * description:区域
 */
@RestController
@RequestMapping("/analysis/area/view/exam")
public class ExamAreaStatController extends BaseController {

    @Autowired
    private ExamAreaStatService examAreaStatService;
    @Autowired
    private ExamStatService examStatService;

    /**
     * 获取区域课程信息
     * params examId
     * @return 区域课程
     */
    @GetMapping
    public Response getExamArea(){
        Map<String,Object> params = getParameterMap();
        Map<String, Object> map=examAreaStatService.getExamArea(params);
        List<Document> scoreSectionList=(List<Document>)map.get("list");
        examStatService.dealScoreSection(scoreSectionList);
        return new Response(ExamAreaDataPageUtil.getDataAfterPage(params, map));
    }

    /**
     * 获取区域课程信息
     * params examId
     * @return 区域课程
     */
    @GetMapping("/before")
    public Response getExamAreaBefore(){
        return new Response(examAreaStatService.getExamAreaBefore(getParameterMap()));
    }

    /**
     * 获取区域课程信息
     * params examId
     * @return 区域课程
     */
    @GetMapping("/class/before")
    public Response getClassExamAreaBefore(){
        return new Response(examAreaStatService.getClassExamAreaBefore(getParameterMap()));
    }

    /**
     * 获取区域课程信息 学校角色查看区域报告
     * params examId
     * @return 区域课程
     */
    @GetMapping("/class/before/list")
    public Response getClassExamAreaBeforeList(){
        return new Response(examAreaStatService.getClassExamAreaBeforeList(getParameterMap()));
    }

    /**
     * 获取区域考试信息
     * params areaCode
     * @return 区域考试
     */
    @GetMapping("/list")
    public Response getAreaExamInfo(){
        return new Response(examAreaStatService.getAreaExamInfo(getParameterMap()));
    }

    /**
     * 获取最新指标（单个）
     *
     * @return
     */
    @GetMapping("/latest/field/one")
    public Response getExamAreaLatestFieldOne() {
        return new Response(examAreaStatService.getExamAreaLatestFieldOne(getParameterMap()));
    }

    /**
     * 获取最新指标（多个跟踪）
     *
     * @return
     */
    @GetMapping("/latest/field")
    public Response getExamAreaLatestField() {
        return new Response(examAreaStatService.getExamAreaLatestField(getParameterMap()));
    }

    /**
     * 获取最新N场考试
     *
     * @return
     */
    @GetMapping("/new")
    public Response getExamList() {
        return new Response(examAreaStatService.getExamList(getParameterMap()));
    }

    /**
     * 获取考试的所有报告
     *
     * @return
     */
    @GetMapping("/stat")
    public Response getExamStat() {
        return new Response(examAreaStatService.getExamStat(getParameterMap()));
    }

    /**
     * 获取学校均分排名
     *
     * @return
     */
    @GetMapping("/rank")
    public Response getExamSchoolRank() {
        return new Response(examAreaStatService.getExamSchoolRank(getParameterMap()));
    }


    /**
     * 获取加入对比的最新两场考试均分
     *
     * @return 加入对比的最新两场考试均分
     */
    @GetMapping("/latest/averageScore")
    public Response getExamAverageScore() {
        return new Response(examAreaStatService.getExamAverageScore(getParameterMap()));
    }

    /**
     * 获取加入对比的最新两场考试均分
     *
     * @return 加入对比的最新两场考试均分
     */
    @GetMapping("/latest/passRate")
    public Response getExamPassRate() {
        return new Response(examAreaStatService.getExamPassRate(getParameterMap()));
    }

    /**
     * 获取加入对比的最新两场考试上线情况
     *
     * @return 加入对比的最新两场考试上线情况
     */
    @GetMapping("/latest/pvalue")
    public Response getExamPValue() {
        return new Response(examAreaStatService.getExamPValue(getParameterMap()));
    }


    /**
     * 获取加入对比的最新两场考试上线情况
     *
     * @return 加入对比的最新两场考试上线情况
     */
    @GetMapping("/latest/pvalue/total")
    public Response getExamTotalPValue() {
        return new Response(examAreaStatService.getExamTotalPValue(getParameterMap()));
    }

    /**
     * 获取加入对比的最新两场考试上线情况
     *
     * @return 加入对比的最新两场考试上线情况
     */
    @GetMapping("/latest/rate/total")
    public Response getExamTotalRate() {
        return new Response(examAreaStatService.getExamTotalRate(getParameterMap()));
    }



    /**
     * 获取区域前N名分布
     *
     */
    @GetMapping("/beforeRank")
    public Response getExamBeforeRanking() {
        return new Response(examAreaStatService.getExamBeforeRanking(getParameterMap()));
    }


    /**
     * 获取加入对比的最新两场考试上线情况
     *
     * @return 加入对比的最新两场考试上线情况
     */
    @GetMapping("/latest/upRate")
    public Response getExamUpRate() {
        return new Response(examAreaStatService.getExamUpRate(getParameterMap()));
    }


    /**
     * 获取加入对比的最新两场考试上线情况
     *
     * @return 加入对比的最新两场考试上线情况
     */
    @GetMapping("/latest/upRate/total")
    public Response getExamTotalUpRate() {
        return new Response(examAreaStatService.getExamTotalUpRate(getParameterMap()));
    }

    /**
     * 获取加入对比的最新两场考试优秀率情况
     *
     * @return 加入对比的最新两场考试优秀率情况
     */
    @GetMapping("/latest/excellentRate")
    public Response getExamExcellentRate() {
        return new Response(examAreaStatService.getExamExcellentRate(getParameterMap()));
    }

    /**
     * 获取加入对比的最新两场考试优秀率情况
     *
     * @return 加入对比的最新两场考试优秀率情况
     */
    @GetMapping("/latest/excellentRate/total")
    public Response getExamTotalExcellentRate() {
        return new Response(examAreaStatService.getExamTotalExcellentRate(getParameterMap()));
    }



}
