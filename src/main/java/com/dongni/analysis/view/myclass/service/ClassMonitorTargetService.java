package com.dongni.analysis.view.myclass.service;

import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.tiku.own.service.AreaPaperService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by scott
 * time: 16:58 2019/1/19
 * description:质量监测
 */
@Service
public class ClassMonitorTargetService {

    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private AreaPaperService areaPaperService;

    /**
     * 目标值
     *
     * @param params classMonitorId
     */
    public void insertClassMonitorTarget(Map<String, Object> params) {
        Verify.of(params).isValidId("classMonitorId").isNumeric("value").isNumeric("type").verify();
        params.put("currentTime", DateUtil.getCurrentDateTime());
        examRepository.insert("ClassMonitorTargetMapper.insertClassMonitorTarget", params);
    }

    /**
     * 获取目标值
     *
     * @param params classMonitorId
     * @return 目标值
     */
    public List<Map<String, Object>> getClassMonitorTarget(Map<String, Object> params) {
        Verify.of(params).isValidId("classMonitorId").verify();
        return examRepository.selectList("ClassMonitorTargetMapper.getClassMonitorTarget", params);
    }

    /**
     * 获取目标值
     *
     * @param params classMonitorId
     */
    public void initClassMonitorTarget(Map<String, Object> params) {
        Verify.of(params).isValidId("classMonitorId").verify();
        Integer[] values = {90, 60, 50};
        for (int i = 0; i < 3; i++) {
            params.put("type", i + 1);
            params.put("value", values[i]);
            insertClassMonitorTarget(params);
            params.remove("classMonitorTargetId");
        }
    }
    /**
     * 初始化中高考试卷
     *TODO [完成]多学段-
     * @param params classMonitorId
     */
    public void initClassMonitorPaper(Map<String, Object> params) {
        Verify.of(params).isValidId("classMonitorId").isValidId("areaId").isNumeric("stage").verify();
        Map<String,Object> paperParams = new HashMap<>(params);
        if(ObjectUtil.isValueEquals(params.get("stage"),3)){
            paperParams.put("paperType",301);
        }else {
            paperParams.put("paperType",201);
        }
        paperParams.put("pageSize",Integer.MAX_VALUE);
        paperParams.put("pageNo",1);
        paperParams.put("currentIndex",0);
        Map<String, Object> areaPaper = areaPaperService.getAreaPaper(paperParams);
        List<Map<String,Object>> list = (List<Map<String, Object>>) areaPaper.get("list");
        if(CollectionUtils.isNotEmpty(list)){
            list.sort(Comparator.comparing(l->Long.valueOf(((Map<String,Object>)l).getOrDefault("year","0").toString())).reversed());
            if(list.size()>5){
                list = list.subList(0,5);
            }
            for (Map<String,Object> p : list) {
                p.putAll(params);
                examRepository.insert("ClassMonitorPaperMapper.insertClassMonitorPaper",p);
            }
        }
    }


}
