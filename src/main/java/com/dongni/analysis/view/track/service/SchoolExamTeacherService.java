package com.dongni.analysis.view.track.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.view.util.SearchUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * Created by scott
 * time: 16:58 2019/1/19
 * description:领导端 教师评价
 */
@Service
public class SchoolExamTeacherService {

    private MongoDatabase mongo;

    @Autowired
    public SchoolExamTeacherService(AnalysisMongodb analysisMongodb) {
        this.mongo = analysisMongodb.getMongoDatabase();
    }

	@Autowired
	private ExamRepository examRepository;


    /**
     * 获取对比
     *
     * @param params gradeType gradeYear areaId courseId artsScience
     * @return 对比
     */
    public Map<String, Object> getExamTeacher(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("sortKey")
                .isNotBlank("sortType")
                .isValidId("schoolId")
                .isNumeric("courseId")
                .isNotBlank("indexCode")
                .isNotBlank("examId")
                .isNotBlank("statId")
                .verify();

        Map<String, Object> rs = new HashMap<>();
        Long courseId = Long.valueOf(params.get("courseId").toString());
        String indexCode = params.get("indexCode").toString();

        String[] examIds = params.get("examId").toString().split(",");
        String[] statIds = params.get("statId").toString().split(",");

        long examId = Long.valueOf(examIds[0]);
        long statId = Long.valueOf(statIds[0]);
        long schoolId = Long.valueOf(params.get("schoolId").toString());
        long compareExamId = 0;
        long compareStatId = 1;
        if (examIds.length > 1) {
            compareExamId = Long.valueOf(examIds[1]);
            compareStatId = Long.valueOf(statIds[1]);
        }

        Boolean isCourse = !ObjectUtil.isValueEquals(0,courseId);
        String collection = "examTeacher"+(isCourse?"Course":"")+"Stat";

        //averageScore,averageScoreChange, averageScoreRanking, averageScoreDifference
        String sortKey = params.get("sortKey").toString();

        //排名key  例如 均分averageScore 则均分排名 averageScoreRanking
        String rank = "Ranking";

        List<Bson> query = new ArrayList<>();
        Bson baseBson = and(eq("examId", examId), eq("statId", statId),
                eq("schoolId", schoolId));
        if(isCourse){
            baseBson = and(baseBson,eq("courseId", courseId));
        }
        query.add(baseBson);
        //模糊查询
        SearchUtil.mongoSearch(params, query, isCourse?"teacherName":"headerName");

        //查询总数
        MongoCollection<Document> stat = mongo.getCollection(collection);
        long totalCount = stat.countDocuments(and(query));
        rs.put("total", totalCount);
        if (totalCount == 0) {
            rs.put("list", Collections.emptyList());
            return rs;
        }
        boolean isAsc = !ObjectUtil.isValueEquals(params.get("sortType"), "desc");

        String[] fields = new String[]{"examId","teacherId", "teacherName","headerId", "headerName", "courseId", "courseName",
                "schoolName", "resultStatus", "classInfo","classId","className", indexCode, indexCode + rank};
        //获取老师合班
        List<Document> ls = stat.find(and(query))
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());
        //获取年级数据
        Document examSchoolCourseStat = mongo.getCollection("examSchool"+(isCourse?"Course":"")+"Stat")
                .find(baseBson).projection(fields(include(indexCode), excludeId()))
                .first();
        String schoolQueryKeyStr = examSchoolCourseStat.get(indexCode).toString();
        BigDecimal schoolQueryKey = new BigDecimal(schoolQueryKeyStr);

        Set<Long> classIdSet = new HashSet<>();
        for (Document l : ls) {
            if(!isCourse){
                List<Map<String,Object>> c = new ArrayList<>();
                Map<String,Object> map = new HashMap<>();
                map.put("resultStatus",l.get("resultStatus"));
                map.put("classId", l.getLong("classId"));
                map.put("className", l.get("className"));
                map.put("averageScore", l.get("averageScore"));
                map.put("averageRate", l.get("averageRate"));
                map.put("ranking", l.get("ranking"));
                c.add(map);
                l.put("classInfo",c);
            }
            Object classInfo = l.get("classInfo");
            if (classInfo != null && classInfo instanceof List) {
                for (Map<String, Object> c : ((List<Map<String, Object>>) classInfo)) {
                    //过滤缺考
                    if (c.get("resultStatus") != null && 0 == Long.valueOf(c.get("resultStatus").toString())) {
                        classIdSet.add(Long.valueOf(c.get("classId").toString()));
                    }
                }
            }
        }
        //获取合班下各班级数据
        List<Long> classIds = new ArrayList<>(classIdSet);
        List<Bson> queryClass = new ArrayList<>();
        queryClass.add(eq("examId", examId));
        queryClass.add(eq("statId", statId));
        if(isCourse){
            queryClass.add(eq("courseId", courseId));
        }
        queryClass.add(in("classId", classIds));

        List<Document> examClassCourseStat = mongo.getCollection("examClass"+(isCourse?"Course":"")+"Stat")
                .find(and(queryClass)).projection(fields(include("examId","examName", "classId","className", indexCode, indexCode + rank), excludeId()))
                .into(new ArrayList<>());


	    String countRanKing = indexCode.concat(rank);
	    //*判断当前考试类型
	    Map<String, Object> examDetail = examRepository.selectOne("ExamUnionAsyncMapper.getExamDetail", MapUtil.of("examId", examId));
	    Set<Integer> examSets = DictUtil.getDictValues("examType", "asyncUnion", "union", "area");
	    if (examSets.contains(MapUtils.getInteger(examDetail, "examType"))) {
		    recountRanking(ls, countRanKing);
		    recountRanking(examClassCourseStat, countRanKing);
	    }

	    //*判断比较的考试类型
	    boolean isJoinExam = false;
	    if (compareExamId != 0) {
		    Map<String, Object> examComDetail = examRepository.selectOne("ExamUnionAsyncMapper.getExamDetail", MapUtil.of("examId", compareExamId));
		    Integer examType = MapUtils.getInteger(examComDetail, "examType");
			isJoinExam = examSets.contains(examType);
	    }

        //合班下具体班级对比考试数据获取
        if (compareExamId != 0) {
            Bson and = and(eq("examId", compareExamId), eq("statId", compareStatId));
            if(isCourse){
                and=and(and,eq("courseId", courseId));
            }
            Bson queryCompareClass = and(in("classId", classIds), eq("resultStatus", 0));
            //班级数据
            List<Document> compareExamClassCourseStat = mongo.getCollection("examClass"+(isCourse?"Course":"")+"Stat")
                    .find(and(and, queryCompareClass)).projection(fields(include("examId", "classId", indexCode + rank), excludeId()))
                    .into(new ArrayList<>());
            Map<Long, Document> compareClassIdMap = compareExamClassCourseStat.stream()
                    .collect(toMap(item -> Long.valueOf(item.get("classId").toString()), item -> item));


	        if (isJoinExam) {
		        //*比较其中的班级，平均分重新排名
		        recountRanking(compareExamClassCourseStat, countRanKing);
	        }

	        for (Document classCourse : examClassCourseStat) {
                Long classId = classCourse.getLong("classId");
                if (compareClassIdMap.containsKey(classId)) {
                    //班级在两场考试都没缺考 则计算升降
                    Integer h1 = Integer.valueOf(compareClassIdMap.get(classId).getOrDefault(indexCode + rank, "0").toString());
                    Integer c1 = Integer.valueOf(classCourse.getOrDefault(indexCode + rank, "0").toString());
                    if (h1 == 0 || c1 == 0) {
                        continue;
                    } else {
                        classCourse.put(indexCode + "Change", h1 - c1);
                    }
                }
            }
        }

        //补充各个班级年级对比均分差
        for (Document classCourse : examClassCourseStat) {
            BigDecimal value = new BigDecimal(classCourse.get(indexCode).toString());
            classCourse.put("compareWithSchool", (value.subtract(schoolQueryKey).compareTo(new BigDecimal(0E-8)))==0?"0":value.subtract(schoolQueryKey).toString());
        }

        Map<Long, Document> classIdMap = examClassCourseStat.stream()
                .collect(toMap(item -> Long.valueOf(item.get("classId").toString()), item -> item));
        for (Document l : ls) {
            Object classInfo;
            classInfo = l.get("classInfo");
            if (classInfo != null && classInfo instanceof List) {
                for (Map<String, Object> c : ((List<Map<String, Object>>) classInfo)) {
                    if (c.get("resultStatus") != null && 0 == Long.valueOf(c.get("resultStatus").toString())) {
                        c.putAll(classIdMap.get(Long.valueOf(c.get("classId").toString())));
                    }
                }
            }
        }

        //对比考试
        if (compareExamId != 0) {
            String groupByKey = isCourse?"teacherId":"headerId";
            List<String> compareIds = ls.stream().map(m -> m.get(groupByKey).toString()).collect(Collectors.toList());
            List<Bson> compareQuery = new ArrayList<>();
            compareQuery.add(eq("examId", compareExamId));
            if(isCourse){
                compareQuery.add(eq("courseId", courseId));
            }
            compareQuery.add(eq("statId", compareStatId));
            compareQuery.add(in(groupByKey, compareIds));

            String[] compareFields = new String[]{indexCode, indexCode + rank, groupByKey,"classId","examId"};

            ArrayList<Document> compareExam = mongo.getCollection(collection).find(and(compareQuery))
                    .projection(fields(include(compareFields), excludeId())).into(new ArrayList<>());

            /*联考重新计算排名*/
	        if (isJoinExam) {
		        recountRanking(compareExam, countRanKing);
	        }

            if (CollectionUtils.isNotEmpty(compareExam)) {
                Map<String, List<Document>> cm = compareExam.stream().collect(groupingBy(item -> item.get(groupByKey).toString()));
                for (Map<String, Object> s : ls) {
                    if (cm.containsKey(s.get(groupByKey).toString()) && cm.get(s.get(groupByKey).toString()) != null) {
                        List<Document> documents = cm.get(s.get(groupByKey).toString());
                        Document cc = new Document();
                        if(s.containsKey("classId")){
                            for(Document doc : documents){
                                if(ObjectUtil.isValueEquals(doc.get("classId"),s.get("classId"))){
                                    cc=doc;
                                }
                            }
                        }else {
                            cc=documents.get(0);
                        }
                        Integer h1 = Integer.valueOf(cc.getOrDefault(indexCode + rank, "0").toString());
                        Integer c1 = Integer.valueOf(s.getOrDefault(indexCode + rank, "0").toString());
                        if (h1 == 0 || c1 == 0) {
                            continue;
                        } else {
                            s.put(indexCode + "Change", h1 - c1);
                        }
                    }
                }
            }
        }
        for (Document s : ls) {
            //补充合班年级对比均分差
            if(ObjectUtil.isValueEquals(s.get("resultStatus"),1))continue;
            BigDecimal value = new BigDecimal(s.get(indexCode).toString());

            s.put("compareWithSchool", (value.subtract(schoolQueryKey).compareTo(new BigDecimal(0E-8)))==0?"0":value.subtract(schoolQueryKey).toString());
        }

        rs.put("total", ls.size());
        //排序  转成BigDecimal排序
        Stream<Document> sorted;
        if (isAsc) {
            sorted = ls.stream().sorted(Comparator
                    .comparing(o -> new BigDecimal(o.getOrDefault(sortKey,"0").toString())));
        } else {
            sorted = ls.stream().sorted(Comparator
                    .comparing(o -> new BigDecimal(((Map) o).getOrDefault(sortKey,"0").toString())).reversed());
        }

        if(!ObjectUtil.isBlank(params.get("pageSize"))){
            ls = sorted.skip((int) params.get("currentIndex")).limit((int) params.get("pageSize"))
                    .collect(Collectors.toList());
        }else {
            ls = sorted.collect(Collectors.toList());
        }

        rs.put("list", ls);
        return rs;
    }


	/**
	 * 重新计算某个排名属性
	 *
	 * @param ls 文档
	 * @param countRanKing 要计算的属性
	 */
	private void recountRanking(List<Document> ls, String countRanKing) {
		ls.sort(Comparator.comparingInt(x -> MapUtils.getInteger(x, countRanKing, 0)));
		//*重新排序
		int i = 1;
		for (Document item : ls) {
			if (Objects.nonNull(MapUtils.getInteger(item, countRanKing))) {
				item.put(countRanKing, i++);
			}
		}
	}
}
