package com.dongni.analysis.view.track.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.view.myclass.service.ClassStatCompareService;
import com.dongni.analysis.view.pvalue.util.ScoreRateUtils;
import com.dongni.analysis.view.util.ComputeRankUtil;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.export.CommonExamService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.tiku.common.util.MapUtil;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Sorts;
import java.math.BigDecimal;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.*;
import static java.util.stream.Collectors.*;


/**
 * Created by scott
 * time: 16:58 2019/1/19
 * description:领导端 考试对比
 */
@Service
public class SchoolCompareExamService {

    @Autowired
    private ExamRepository examRepository;
    @Autowired
    private CommonExamService commonExamService;
    @Autowired
    private SchoolFinalExamService schoolFinalExamService;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private SchoolCompareGroupService schoolCompareGroupService;
    @Autowired
    private ClassStatCompareService classStatCompareService;
    @Autowired
    private ExamService examService;
    @Autowired
    private ExamConfigService examConfigService;

    /**
     * 获取Mongodb 数据库对象
     */
    private MongoDatabase mongo;

    @Autowired
    public SchoolCompareExamService(AnalysisMongodb analysisMongodb) {
        this.mongo = analysisMongodb.getMongoDatabase();
    }


    /**
     * 获取加入的考试
     *
     * @param params schoolCompareId
     */
    public List<Map<String, Object>> getSchoolCompareExam(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolCompareId").verify();

        List<Map<String, Object>> exam = examRepository.selectList("SchoolCompareExamMapper.getSchoolCompareExam", params);
        List<Map<String, Object>> rs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(exam)) {

            // 过滤不合法的考试
            List<Long> examIds = exam.stream().map(e->Long.valueOf(e.get("examId").toString())).collect(toList());
            Map<String,Object> p = new HashMap<>(params);
            p.put("examIds",examIds);
            p.put("schoolId",exam.get(0).get("schoolId"));
            Set<Long> invalidExamIds = classStatCompareService.getInvalidExamId(p);
            if(CollectionUtils.isNotEmpty(invalidExamIds)){
                exam.removeIf(e->invalidExamIds.contains(Long.valueOf(e.get("examId").toString())));
                Map<String,Object> q = new HashMap<>(params);
                q.put("examIds",invalidExamIds);
                examRepository.delete("SchoolCompareExamMapper.deleteSchoolCompareExamByExamId",q);
            }
            if(CollectionUtils.isEmpty(exam)){
                return rs;
            }

            // 过滤不合法的报告
            Set<Long> invalidExamStat = new HashSet<>();
            for (Map<String,Object> e : exam){
                if(!classStatCompareService.isValidExamStat(e)){
                    invalidExamStat.add(Long.valueOf(e.get("examId").toString()));
                }
            }
            if(CollectionUtils.isNotEmpty(invalidExamStat)){
                exam.removeIf(e->invalidExamStat.contains(Long.valueOf(e.get("examId").toString())));
                Map<String,Object> q = new HashMap<>(params);
                q.put("examIds",invalidExamStat);
                examRepository.delete("SchoolCompareExamMapper.deleteSchoolCompareExamByExamId",q);
            }
            if(CollectionUtils.isEmpty(exam)){
                return rs;
            }

            params.put("exam", exam);
            List<Map<String, Object>> baseExam = commonExamService.getExam(params);
            Map<Long, Map<String, Object>> examMap = baseExam.stream().collect(toMap(b -> Long.valueOf(b.get("examId").toString()), b -> b));
            exam.forEach(e -> {
                Map<String, Object> map = examMap.get(Long.valueOf(e.get("examId").toString()));
                if (MapUtils.isNotEmpty(map)) {
                    e.putAll(map);
                    rs.add(e);
                }
            });
        }
        rs.sort(Comparator.comparing(m -> DateUtil.getDateLongValue(((Map) m).get("startDate").toString())).reversed());

        return rs;
    }

    /**
     * 保存加入的考试
     *
     * @param params examId
     */
    @Transactional(transactionManager = ExamRepository.TRANSACTION, rollbackFor = Exception.class)
    public void insertSchoolCompare(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolCompareId")
                .isNotEmptyCollections("exam")
                .verify();
        List<Map<String, Object>> exam = MapUtil.getCast(params, "exam");
        long distinctExamCount = exam.stream().map(i -> MapUtil.getLong(i, "examId")).distinct().count();
        if (exam.size() != distinctExamCount) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "每场考试只能选择一个报告!");
        }

        params.put("currentTime", DateUtil.getCurrentDateTime());
        //先删除考试
        examRepository.delete("SchoolCompareExamMapper.deleteSchoolCompareExam", params);
        if (CollectionUtils.isEmpty(exam)) return;
        examRepository.insert("SchoolCompareExamMapper.insertSchoolCompareExam", params);
    }

    /**
     * 通过课程获取加入的考试
     *
     * @param params schoolId courseId
     * @return 考试
     */
    public List<Map<String, Object>> getSchoolCompareExamByCourse(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolId").isNumeric("courseId")
                .isNumeric("artsScience").verify();
        return examRepository.selectList("SchoolCompareExamMapper.getSchoolCompareExamByCourse", params);
    }

    /**
     * 通过考试对比id获取加入的考试
     *
     * @param params schoolId
     * @return 考试
     */
    public List<Map<String, Object>> getSchoolCompareExamBySchoolCompareId(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolId").isValidId("schoolCompareId").verify();
        return examRepository.selectList("SchoolCompareExamMapper.getSchoolCompareExamBySchoolCompareId", params);
    }

    /**
     * 通过考试对比课程
     *
     * @param params schoolCompareId
     * @return 考试课程
     */
    public List<Map<String, Object>> getSchoolCompareExamCourse(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolCompareId").verify();
        List<Long> courseId = examRepository.selectList("SchoolCompareExamMapper.getSchoolCompareExamCourseId", params);
        //综合课程返回子课程
        return commonCourseService.getSubCourse(courseId);
    }

    /**
     * 获取考试报告
     *
     * @param params schoolId
     * @return 考试报告
     */
    public Map<String, Object> getSchoolCompareExamStat(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolId").isNumeric("gradeId").verify();
        //先获取区域下所有考试id
        Map<String, Object> schoolExam = commonExamService.getGradeExam(params);
        if (ObjectUtil.isValueEquals(0, schoolExam.get("totalCount"))) {
            return schoolExam;
        }
        List<Map<String, Object>> examList = (List<Map<String, Object>>) schoolExam.get("exam");

        // 某些考试不允许被添加为对比诊断-2025/01/14 孙岩
        Set<String> schoolCompareHideExamId = JedisTemplate
                .execute(jedis -> jedis.smembers(JedisUtil.getKey("schoolCompareHideExam")));
        if (CollectionUtils.isNotEmpty(schoolCompareHideExamId)) {
            Iterator<Map<String, Object>> iterator = examList.iterator();
            while (iterator.hasNext()) {
                Map<String, Object> next = iterator.next();
                String examId = MapUtil.getString(next, "examId");
                if (schoolCompareHideExamId.contains(examId)) {
                    iterator.remove();
                }
            }
        }

        params.put("exam", examList);
        List<Map<String, Object>> examStat = examRepository.selectList("SchoolCompareExamMapper.getExamStat", params);
        // 再取到所有报告
        Map<Long, List<Map<String, Object>>> examStatMap = examStat.stream().collect(groupingBy(e -> Long.valueOf(e.get("examId").toString())));
        for (Map<String, Object> exam : examList) {
            exam.put("examStat", examStatMap.get(Long.valueOf(exam.get("examId").toString())));
        }
        schoolExam.put("exam", examList);
        return schoolExam;
    }

    /**
     * 一分四率  同级数据
     * 选中学校节点时  parentSchoolId 传递学校的 schoolId
     *
     * @param params examIds parentSchoolId
     * @return 一分四率
     */
    public List<Document> getSchoolParentScoreRate(Map<String, Object> params) {
        Verify.of(params).isNumeric("courseId").isNumeric("level").isNotBlank("examIds").isNotBlank("statIds").isNotBlank("parentSchoolId").verify();

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));

        List<Document> rs = new ArrayList<>();
        String[] examIds = params.get("examIds").toString().split(",");
        String[] statIds = params.get("statIds").toString().split(",");
        Map<String, Object> examParams = new HashMap<>(params);
        //同级
        boolean schoolLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        Map<String, Object> map = fixParentParams(schoolLevel, isCourse, examIds, statIds, params);

        List<Document> examSchoolStat = mongo.getCollection(map.get("stat").toString()).find(or((List<Bson>) map.get("query")))
                .projection(fields(include("examId", "statId", "schoolId", "schoolName", "examName", "schoolId", "schoolCode", "schoolName", "averageScore", "averageRate", "excellentRate", "goodRate", "passRate", "lowScoreRate"), excludeId()))
                .sort(Sorts.ascending("schoolId"))
                .into(new ArrayList<>());
        String key = schoolLevel ? "school" : "school";
        Map<Long, List<Document>> schoolMap = examSchoolStat.stream().collect(groupingBy(e -> e.getLong(key + "Id")));
        addResult(key, schoolMap, rs, false);

        //上级
        if (schoolLevel) {
            List<Bson> or = fixChildrenParams(examIds, statIds, isCourse, params);
            List<Document> parent = mongo.getCollection(isCourse ? "examSchoolCourseStat" : "examSchoolStat").find(or(or))
                    .projection(fields(include("examId", "statId", "examName", "schoolId", "schoolCode", "schoolName", "averageScore", "averageRate", "excellentRate", "goodRate", "passRate", "lowScoreRate"), excludeId()))
                    .into(new ArrayList<>());
            Map<Long, List<Document>> schoolMap1 = parent.stream().collect(groupingBy(e -> e.getLong("schoolId")));
            addResult("school", schoolMap1, rs, true);
        }

        addExamInfo(examParams, rs);
        return rs;
    }

    /**
     * 单位排行榜 一分四率  同级层对比
     * 选中学校节点时  parentSchoolId 传递学校的 schoolId
     *
     * @param params examIds parentSchoolId
     * @return 一分四率
     */
    public List<Map<String, Object>> getSchoolParentScoreRateRanking(Map<String, Object> params) {
        Verify.of(params).isNumeric("courseId").isNumeric("level")
                .isNotBlank("examIds")
                .isNotBlank("statIds")
                .isNotBlank("sortKey")
                .isNotBlank("sortType")
                .isNotBlank("parentSchoolId").verify();
        // sortType: ranking 排行榜, progress 进步榜
        String sortKey = params.get("sortKey").toString();
        String sortType = params.get("sortType").toString();
        String progress = "progress";
        String ranking = "ranking";
        long parentSchoolId = Long.valueOf(params.get("parentSchoolId").toString());

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));
        Long examId = examSorts(params);
        Long compareExamId = null;
        if (ObjectUtil.isValidId(params.get("compareExamId"))) {
            compareExamId = Long.valueOf(params.get("compareExamId").toString());
        }
        String[] examIds = new String[]{examId.toString()};
        if (compareExamId != null) {
            examIds = new String[]{examId.toString(), compareExamId.toString()};
        }
        String[] statIds = params.get("statIds").toString().split(",");
        //同级
        boolean schoolLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        String key = schoolLevel ? "schoolId" : "schoolId";
        Map<String, Object> map = fixParentParams(schoolLevel, isCourse, examIds, statIds, params);

        List<String> fields = Arrays.asList("averageScoreRanking", "excellentRateRanking", "goodRateRanking",
                "passRateRanking", "lowScoreRateRanking");

        List<Document> examSchoolStat = mongo.getCollection(map.get("stat").toString())
                .find(or((List<Bson>) map.get("query")))
                .projection(fields(include(
                        "examId", "statId", "parentSchoolId",
                        "schoolId", "schoolName", "examName",
                        "schoolId", "schoolCode", "schoolName",
                        "averageScore", "averageRate", "excellentRate",
                        "goodRate", "passRate", "lowScoreRate", "averageScoreRanking",
                        "averageRateRanking", "excellentRateRanking", "goodRateRanking",
                        "passRateRanking", "lowScoreRateRanking"), excludeId()))
                .sort(Sorts.ascending("schoolId"))
                .into(new ArrayList<>());
        // Map<Long, List<Document>> schoolMap = examSchoolStat.stream().collect(groupingBy(e -> e.getLong(key + "Id")));
        if (CollectionUtils.isEmpty(examSchoolStat)) {
            return Collections.emptyList();
        }
        Document doc = new Document();
        if (!schoolLevel) {
            String schoolStr = parentSchoolId == 1 ? "parentSchoolId" : "schoolId";
            doc = examSchoolStat.stream().filter(m -> (m.getLong(schoolStr).longValue() == parentSchoolId
                    && m.getLong("examId").longValue() == examId))
                    .findAny().orElse(null);
            examSchoolStat = examSchoolStat.stream().filter(m -> m.getLong("schoolId") != parentSchoolId)
                    .collect(toList());
            if (CollectionUtils.isEmpty(examSchoolStat)) {
                List<Map<String, Object>> rs = new ArrayList();
                rs.add(doc);
                return rs;
            }
        }
        Map<Long, List<Map<String, Object>>> examIdMapping = examSchoolStat.stream()
                .collect(groupingBy(m -> Long.valueOf(m.get("examId").toString())));

        List<Map<String, Object>> rs = examIdMapping.get(examId);
        if (CollectionUtils.isEmpty(rs)) {
            rs = new ArrayList<>();
        }
        // addResult(key, schoolMap, rs, false);
        if (compareExamId != null && examIdMapping.containsKey(compareExamId) && examIdMapping.containsKey(examId)) {
            Map<String, Map<String, Object>> compareExam = examIdMapping.get(compareExamId).stream().collect(toMap(c -> c.get(key).toString(), c -> c));
            //进退计算
            ComputeRankUtil.computeRankChange(examIdMapping.get(examId), compareExam, fields, key);
            if (sortType.equals(progress)) {
                rs = rs.stream().sorted(
                        Comparator.comparing(d -> Integer.valueOf(((Map) d).getOrDefault(sortKey + "Change", Integer.MIN_VALUE).toString()))
                                .reversed()
                ).collect(toList());
            }
        }
        //排序
        if (sortType.equals(ranking)) {
            rs = rs.stream().sorted(Comparator.comparing(d -> d.getOrDefault(sortKey, Integer.MAX_VALUE).toString())).collect(Collectors.toList());
        }
        //上级
        if (schoolLevel) {
            List<Bson> or = fixChildrenParams(examIds, statIds, isCourse, params);
            List<Document> parent = mongo.getCollection(isCourse ? "examSchoolCourseStat" : "examSchoolStat").find(or(or))
                    .projection(fields(include(
                            "examId", "statId",
                            "examName", "schoolId", "schoolCode",
                            "schoolName", "averageScore", "averageRate",
                            "excellentRate", "goodRate", "passRate",
                            "lowScoreRate", "averageRateRanking", "averageScoreRanking",
                            "excellentRateRanking", "goodRateRanking",
                            "passRateRanking", "lowScoreRateRanking"), excludeId()))
                    .into(new ArrayList<>());

            for (Document document : parent) {
                if (document.getLong("examId").equals(examId)) {
                    rs.add(0, document);
                }
            }
        } else {
            if (MapUtils.isNotEmpty(doc)) {
                rs.add(0, doc);
            }
        }

        return rs;
    }

    /**
     * 单位排行榜 一分四率  下级对比
     *
     * @return 一分四率
     */
    public List<Map<String, Object>> getSchoolChildrenScoreRateRanking(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("level")
                .isNumeric("courseId")
                .isNotBlank("examIds")
                .isNotBlank("statIds")
                .isNotBlank("sortKey")
                .isNotBlank("sortType")
                .isNotBlank("schoolId").verify();

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));
        // sortType: ranking 排行榜, progress 进步榜
        String sortKey = params.get("sortKey").toString();
        String sortType = params.get("sortType").toString();
        String progress = "progress";
        String ranking = "ranking";
        Long examId = examSorts(params);
        Long compareExamId = null;
        if (ObjectUtil.isValidId(params.get("compareExamId"))) {
            compareExamId = Long.valueOf(params.get("compareExamId").toString());
        }
        String[] examIds = new String[]{examId.toString()};
        if (compareExamId != null) {
            examIds = new String[]{examId.toString(), compareExamId.toString()};
        }
        String[] statIds = params.get("statIds").toString().split(",");
        //同级
        boolean classLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        boolean schoolLevel = ObjectUtil.isValueEquals("2", params.get("level"));
        String key = classLevel ? "classId" : (schoolLevel ? "schoolId" : "schoolId");
        //同级 只有一条
        Map<String, Object> map = fixNextParentParams(classLevel, isCourse, examIds, statIds, params);
        List<Map<String, Object>> examSchoolStat = mongo.getCollection(map.get("stat").toString()).find(or((List<Bson>) map.get("query")))
                .projection(fields(include("examId", "statId", "examName", "schoolId", "schoolCode", "schoolName",
                        "classId", "className", "schoolId", "schoolName",
                        "averageScore", "averageRate", "excellentRate",
                        "goodRate", "passRate",
                        "lowScoreRate", "averageRateRanking", "averageScoreRanking",
                        "excellentRateRanking", "goodRateRanking",
                        "passRateRanking", "lowScoreRateRanking"), excludeId()))
                .into(new ArrayList<>());
        if (CollectionUtils.isEmpty(examSchoolStat)) {
            return Collections.emptyList();
        }
        //下级
        Map<String, Object> m = fixNextChildrenParams(classLevel, schoolLevel, examIds, statIds, isCourse, params);
        List<Document> children = mongo.getCollection(m.get("stat").toString()).find(or((List<Bson>) m.get("query")))
                .projection(fields(include("examId", "statId", "examName", "schoolId", "schoolCode", "schoolName",
                        "classId", "className", "schoolId", "schoolName",
                        "averageScore", "averageRate", "excellentRate",
                        "goodRate", "passRate",
                        "lowScoreRate", "averageScoreRanking",
                        "excellentRateRanking", "goodRateRanking",
                        "passRateRanking", "lowScoreRateRanking"),
                        excludeId()))
                .into(new ArrayList<>());
        if (CollectionUtils.isEmpty(children)) {
            return examSchoolStat;
        }

        Map<Long, List<Map<String, Object>>> examIdMapping = children.stream().collect(groupingBy(o -> Long.valueOf(o.get("examId").toString())));
        List<Map<String, Object>> rs = examIdMapping.get(examId);
        if (CollectionUtils.isEmpty(rs)) {
            rs = new ArrayList<>();
        }
        List<String> fields = Arrays.asList("averageScoreRanking", "excellentRateRanking", "goodRateRanking",
                "passRateRanking", "lowScoreRateRanking");
        if (compareExamId != null && examIdMapping.containsKey(compareExamId) && examIdMapping.containsKey(examId)) {
            Map<String, Map<String, Object>> compareExam = examIdMapping.get(compareExamId).stream().collect(toMap(c -> c.get(key).toString(), c -> c));
            //进退计算
            ComputeRankUtil.computeRankChange(examIdMapping.get(examId), compareExam, fields, key);
            //排序
            if (sortType.equals(progress)) {
                rs = rs.stream().sorted(
                        Comparator.comparing(d -> Integer.valueOf(((Map) d).getOrDefault(sortKey + "Change", Integer.MIN_VALUE).toString()))
                                .reversed()
                ).collect(toList());

            }
        }
        if (sortType.equals(ranking)) {
            rs = rs.stream().sorted(Comparator.comparing(d -> Integer.valueOf(d.getOrDefault(sortKey, Integer.MAX_VALUE).toString()))).collect(Collectors.toList());
        }
        for (Map<String, Object> document : examSchoolStat) {
            if (Long.valueOf(document.get("examId").toString()).equals(examId)) {
                rs.add(0, document);
            }
        }
        return rs;
    }

    /**
     * 一分四率 下级数据
     *
     * @param params examIds schoolCode
     * @return 一分四率
     */
    public List<Document> getSchoolChildrenScoreRate(Map<String, Object> params) {
        Verify.of(params).isNumeric("level").isNumeric("courseId").isNotBlank("examIds").isNotBlank("statIds").isNotBlank("schoolId").verify();

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));

        boolean classLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        boolean schoolLevel = ObjectUtil.isValueEquals("2", params.get("level"));
        String[] examIds = params.get("examIds").toString().split(",");
        String[] statIds = params.get("statIds").toString().split(",");
        Map<String, Object> examParams = new HashMap<>(params);
        //同级 只有一条
        Map<String, Object> map = fixNextParentParams(classLevel, isCourse, examIds, statIds, params);
        List<Document> examSchoolStat = mongo.getCollection(map.get("stat").toString()).find(or((List<Bson>) map.get("query")))
                .projection(fields(include("examId", "statId", "examName", "schoolId", "schoolCode", "schoolName",
                        "classId", "className", "schoolId", "schoolName",
                        "averageScore", "averageRate", "excellentRate", "goodRate", "passRate", "lowScoreRate"), excludeId()))
                .into(new ArrayList<>());

        String key = classLevel ? "school" : "school";
        Map<Long, List<Document>> schoolMap = examSchoolStat.stream().collect(groupingBy(e -> e.getLong(key + "Id")));
        List<Document> rs = new ArrayList<>();
        addResult(key, schoolMap, rs, false);

        //下级
        Map<String, Object> m = fixNextChildrenParams(classLevel, schoolLevel, examIds, statIds, isCourse, params);
        List<Document> children = mongo.getCollection(m.get("stat").toString()).find(or((List<Bson>) m.get("query")))
                .projection(fields(include("examId", "statId", "examName", "schoolId", "schoolCode", "schoolName",
                        "classId", "className", "schoolId", "schoolName",
                        "averageScore", "averageRate", "excellentRate", "goodRate", "passRate", "lowScoreRate"),
                        excludeId()))
                .into(new ArrayList<>());

        String finalKey = m.get("key").toString();
        Map<Long, List<Document>> childrenMap = children.stream().collect(groupingBy(e -> e.getLong(finalKey + "Id")));
        addResult(finalKey, childrenMap, rs, false);
        addExamInfo(examParams, rs);
        return rs;
    }

    /**
     * @return java.lang.Long
     * @Description 考试排序, 获取最新的两考考试
     * @Param [params]
     **/
    private Long examSorts(Map<String, Object> params) {

        List<Long> examIds = StringUtil.strToList(params.get("examIds").toString(), ",", Long.class);
        Map<Long, Long> examStatMap = new HashMap<>();
        if (!ObjectUtil.isBlank(params.get("statIds"))) {
            List<Long> statIds = StringUtil.strToList(params.get("statIds").toString(), ",", Long.class);
            if (examIds.size() != statIds.size()) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "报告参数异常");
            }
            for (int i = 0; i < examIds.size(); i++) {
                examStatMap.put(examIds.get(i), statIds.get(i));
            }

        }
        if (examIds.size() == 1) {
            return examIds.get(0);
        }
        ArrayList<Document> examList = mongo.getCollection("examStat")
                .find(and(in("examId", examIds), eq("statId", 0L)))
                .projection(fields(include("examId", "startDate"))).into(new ArrayList<>());
        List<Document> newExamList = examList.stream()
                .sorted(Comparator.comparing(m -> ((Date) ((Map) m).get("startDate")).getTime()).reversed()).collect(Collectors.toList());
        if (MapUtils.isNotEmpty(examStatMap)) {
            StringBuilder sb = new StringBuilder();
            for (Document doc : newExamList) {
                long examId = doc.getLong("examId");
                sb.append(examStatMap.get(examId) + ",");
            }
            params.put("statIds", sb.substring(0, sb.length() - 1));
        }
        params.put("compareExamId", newExamList.get(1).getLong("examId"));
        return newExamList.get(0).getLong("examId");
    }

    /**
     * 获取尖子生 同级与上级
     *
     * @param params examIds schoolCode
     * @return 尖子生
     */
    public List<Document> getParentTop(Map<String, Object> params) {
        Verify.of(params).isNumeric("level").isNumeric("type").isNumeric("value")
                .isNumeric("courseId").isNotBlank("examIds").isNotBlank("statIds")
                .isNotBlank("parentSchoolId").isNotBlank("parentSchoolCode").verify();

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));
        String[] examIds = params.get("examIds").toString().split(",");
        String[] statIds = params.get("statIds").toString().split(",");
        List<Document> rs = new ArrayList<>();
        Map<String, Object> examParams = new HashMap<>(params);

        //同级
        boolean schoolLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        Map<String, Object> map = fixParentParams(schoolLevel, isCourse, examIds, statIds, params);
        List<Document> examSchoolStat = mongo.getCollection(map.get("stat").toString()).find(or((List<Bson>) map.get("query")))
                .projection(fields(include("examId", "statId", "examName", "schoolId", "schoolName", "schoolId", "schoolCode", "schoolName", "participationNumber"), excludeId()))
                .into(new ArrayList<>());

        //获取考试的参数人数 根节点的参考人数
        Document document = getExamStudentAndRanking(isCourse, examIds, statIds, params);
        List<Document> examStudentStat = (List<Document>) document.get("student");
        Map<Long, Map<Long, Map<String, Object>>> examRanking = (Map<Long, Map<Long, Map<String, Object>>>) document.get("examRanking");

        Map<Object, List<Document>> schoolMap = examSchoolStat.stream().collect(groupingBy(e -> e.get(schoolLevel ? "schoolId" : "schoolCode")));
        addTopResult(schoolMap, examStudentStat, examRanking, rs, schoolLevel);

        //上级数据  (学校的上级是区域，区域的在上面查询已经包含)
        if (schoolLevel) {
            List<Bson> or = fixChildrenParams(examIds, statIds, isCourse, params);
            List<Document> parent = mongo.getCollection(isCourse ? "examSchoolCourseStat" : "examSchoolStat").find(or(or))
                    .projection(fields(include("examId", "statId", "examName", "schoolId", "schoolCode", "schoolName", "participationNumber"), excludeId()))
                    .into(new ArrayList<>());
            Map<String, List<Document>> schoolMap1 = parent.stream().collect(groupingBy(e -> e.getString("schoolCode")));
            for (String k : schoolMap1.keySet()) {
                List<Document> exam = schoolMap1.get(k);
                //计算每场考试的占比
                List<Document> student = examStudentStat.stream().filter(s -> s.getString("schoolCode").startsWith(k.toString())).collect(toList());
                Map<Long, Map<Long, List<Document>>> studentMap = student.stream().collect(groupingBy(s -> s.getLong("examId"), groupingBy(s -> s.getLong("statId"))));
                computeTopRate(exam, studentMap, examRanking);
                Document doc = new Document();
                doc.put("exam", exam);
                doc.put("schoolId", exam.get(0).get("schoolId"));
                doc.put("schoolName", exam.get(0).get("schoolName"));
                rs.add(0, doc);
            }
        }

        addExamInfo(examParams, rs);
        return rs;
    }

    /**
     * 获取尖子生 当前与下级
     *
     * @param params examIds schoolCode
     * @return 尖子生
     */
    public List<Document> getChildrenTop(Map<String, Object> params) {
        Verify.of(params).isNumeric("level").isNumeric("type").isNumeric("value")
                .isNumeric("courseId").isNotBlank("examIds").isNotBlank("statIds")
                .isValidId("schoolId").isNotBlank("parentSchoolCode").verify();

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));

        String[] examIds = params.get("examIds").toString().split(",");
        String[] statIds = params.get("statIds").toString().split(",");
        List<Document> rs = new ArrayList<>();
        Map<String, Object> examParams = new HashMap<>(params);

        List<String> fields = Arrays.asList("examId", "statId", "examName", "classId", "className", "schoolId", "schoolName", "schoolId", "schoolCode", "schoolName", "participationNumber");

        //当前级
        boolean schoolLevel = ObjectUtil.isValueEquals("2", params.get("level"));
        boolean classLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        Map<String, Object> map = fixNextParentParams(classLevel, isCourse, examIds, statIds, params);
        List<Document> examSchoolStat = mongo.getCollection(map.get("stat").toString()).find(or((List<Bson>) map.get("query")))
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());

        //获取考试的参数人数 根节点的参考人数
        Document document = getExamStudentAndRanking(isCourse, examIds, statIds, params);
        List<Document> examStudentStat = (List<Document>) document.get("student");
        Map<Long, Map<Long, Map<String, Object>>> examRanking = (Map<Long, Map<Long, Map<String, Object>>>) document.get("examRanking");

        Map<Object, List<Document>> schoolMap = examSchoolStat.stream().collect(groupingBy(e -> e.get(classLevel ? "schoolId" : "schoolCode")));
        addTopResult(schoolMap, examStudentStat, examRanking, rs, classLevel);

        //下级数据  (学校的下级是班级)
        Map<String, Object> m = fixNextChildrenParams(classLevel, schoolLevel, examIds, statIds, isCourse, params);
        List<Document> children = mongo.getCollection(m.get("stat").toString()).find(or((List<Bson>) m.get("query")))
                .projection(fields(include(fields),
                        excludeId()))
                .into(new ArrayList<>());

        String finalKey = m.get("key").toString();
        if (ObjectUtil.isValueEquals("school", finalKey)) {
            finalKey = finalKey + "Code";
        } else {
            finalKey = finalKey + "Id";
        }
        String finalKey1 = finalKey;
        Map<Object, List<Document>> childrenMap = children.stream().collect(groupingBy(e -> e.get(finalKey1)));
        for (Object k : childrenMap.keySet()) {
            List<Document> exam = childrenMap.get(k);
            //计算每场考试的占比
            List<Document> student;
            if (!schoolLevel && !classLevel) {
                student = examStudentStat.stream().filter(s -> s.getString("schoolCode").startsWith(k.toString())).collect(toList());
            } else {
                student = examStudentStat.stream().filter(s -> ObjectUtil.isValueEquals(s.get(finalKey1), k.toString())).collect(toList());
            }
            Map<Long, Map<Long, List<Document>>> studentMap = student.stream().collect(groupingBy(s -> s.getLong("examId"), groupingBy(s -> s.getLong("statId"))));
            computeTopRate(exam, studentMap, examRanking);
            Document doc = new Document();
            doc.put("exam", exam);
            String childrenKey = m.get("key").toString();
            doc.put(childrenKey + "Id", exam.get(0).get(childrenKey + "Id"));
            doc.put(childrenKey + "Name", exam.get(0).get(childrenKey + "Name"));
            rs.add(doc);
        }
        addExamInfo(examParams, rs);
        return rs;
    }


    private Document getExamStudentAndRanking(boolean isCourse, String[] examIds, String[] statIds, Map<String, Object> params) {
        List<Bson> studentOr = new ArrayList<>();
        List<Map<String, Object>> examRoot =null;// commonExamService.getSchoolExamRoot(params);
        Map<Long, Map<String, Object>> examMap = examRoot.stream().collect(toMap(e -> Long.valueOf(e.get("examId").toString()), e -> e));
        List<Bson> query = new ArrayList<>();
        for (int i = 0; i < examIds.length; i++) {
            Long examId = Long.valueOf(examIds[i]);
            Long statId = Long.valueOf(statIds[i]);
            Map<String, Object> e = examMap.get(examId);
            Bson and = and(eq("schoolId", Long.valueOf(e.get("schoolId").toString())),
                    eq("examId", examId),
                    eq("statId", statId));
            if (isCourse) {
                and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
            }
            query.add(and);
        }
        List<Document> participationStat = mongo.getCollection(isCourse ? "examSchoolCourseStat" : "examSchoolStat").find(or(query))
                .projection(fields(include("examId", "statId", "courseId", "schoolId", "schoolCode", "schoolName", "participationNumber"), excludeId()))
                .into(new ArrayList<>());

        //1按名次/2按百分比
        Integer ranking = 0;
        Map<Long, Map<Long, Map<String, Object>>> examRanking = new HashMap<>();
        boolean isRanking = ObjectUtil.isValueEquals(1, params.get("type"));
        for (Document d : participationStat) {
            Integer participationNumber = d.getInteger("participationNumber");
            if (isRanking) {
                ranking = Integer.valueOf(params.get("value").toString());
            } else {
                ranking = (int) (participationNumber * Double.valueOf(params.get("value").toString()) * 0.01);
            }
            Map<Long, Map<String, Object>> statMap = new HashMap<>();
            Map<String, Object> r = new HashMap<>();
            r.put("ranking", ranking);
            r.put("participationNumber", participationNumber);
            statMap.put(d.getLong("statId"), r);
            examRanking.put(d.getLong("examId"), statMap);
        }

        //查询学生条件
        Map<String, Object> rankingMap;
        for (int i = 0; i < examIds.length; i++) {
            Long examId = Long.valueOf(examIds[i]);
            Long statId = Long.valueOf(statIds[i]);
            if (!isRanking) {
                Map<Long, Map<String, Object>> examRankingMap = examRanking.get(examId);
                if (MapUtils.isEmpty(examRankingMap)) continue;
                rankingMap = examRankingMap.get(statId);
                ranking = Integer.valueOf(rankingMap.get("ranking").toString());
            }
            Bson studentAnd = and(eq("examId", examId),
                    eq("statId", statId),
                    lte("ranking", ranking),
                    regex("schoolCode", "^" + params.get("parentSchoolCode").toString()));
            if (isCourse) {
                studentAnd = and(studentAnd, eq("courseId", Long.valueOf(params.get("courseId").toString())));
            }
            studentOr.add(studentAnd);
        }

        //取在此排名前的所有学生  按区域id分组 计算出每个区域的数据
        List<Document> into = mongo.getCollection(isCourse ? "examStudentCourseStat" : "examStudentStat").find(or(studentOr))
                .projection(fields(include("examId", "statId", "examName", "classId", "className", "schoolId", "schoolName", "schoolId", "schoolCode", "schoolName", "studentId", "studentName"), excludeId()))
                .into(new ArrayList<>());

        Document rs = new Document();
        rs.put("student", into);
        rs.put("examRanking", examRanking);
        return rs;
    }


    /**
     * 上线 同级与上级
     *
     * @param params parentSchoolId
     * @return 结果
     */
    public List<Document> getSchoolParentLine(Map<String, Object> params) {
        Verify.of(params).isNumeric("courseId").isNumeric("level").isNotBlank("line").isNotBlank("examIds").isNotBlank("statIds").isNotBlank("parentSchoolId").verify();

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));

        String[] examIds = params.get("examIds").toString().split(",");
        String[] statIds = params.get("statIds").toString().split(",");
        Map<String, Object> examParams = new HashMap<>(params);

        List<Document> rs = new ArrayList<>();
        String line = "line" + params.get("line");
        List<String> fields = Arrays.asList("examId", "statId", "schoolId", "schoolName", "examName", "schoolId", "schoolCode", "schoolName", line);

        //同级
        boolean schoolLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        Map<String, Object> map = fixParentParams(schoolLevel, isCourse, examIds, statIds, params);

        List<Document> examSchoolStat = mongo.getCollection(map.get("stat").toString()).find(or((List<Bson>) map.get("query")))
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());
        String key = schoolLevel ? "school" : "school";
        Map<Long, List<Document>> schoolMap = examSchoolStat.stream().collect(groupingBy(e -> e.getLong(key + "Id")));
        addResult(key, schoolMap, rs, false);

        //上级
        if (schoolLevel) {
            List<Bson> or = fixChildrenParams(examIds, statIds, isCourse, params);
            List<Document> parent = mongo.getCollection(isCourse ? "examSchoolCourseLineStat" : "examSchoolLineStat").find(or(or))
                    .projection(fields(include(fields), excludeId()))
                    .into(new ArrayList<>());
            Map<Long, List<Document>> schoolMap1 = parent.stream().collect(groupingBy(e -> e.getLong("schoolId")));
            addResult("schoolId", schoolMap1, rs, true);
        }
        addExamInfo(examParams, rs);
        return rs;
    }

    /**
     * 上线 当前与下级
     *
     * @param params parentSchoolId
     * @return 结果
     */
    public List<Document> getSchoolChildrenLine(Map<String, Object> params) {
        Verify.of(params).isNumeric("level").isNotBlank("line").isNumeric("courseId").isNotBlank("examIds").isNotBlank("statIds").isNotBlank("schoolId").verify();

        boolean isCourse = !ObjectUtil.isValueEquals(0, params.get("courseId"));
        String line = "line" + params.get("line");
        List<String> fields = Arrays.asList("examId", "statId", "schoolId", "schoolName", "classId", "className", "examName", "schoolId", "schoolCode", "schoolName", line);

        boolean classLevel = ObjectUtil.isValueEquals("3", params.get("level"));
        boolean schoolLevel = ObjectUtil.isValueEquals("2", params.get("level"));
        String[] examIds = params.get("examIds").toString().split(",");
        String[] statIds = params.get("statIds").toString().split(",");
        Map<String, Object> examParams = new HashMap<>(params);

        //同级 只有一条
        Map<String, Object> map = fixNextParentParams(classLevel, isCourse, examIds, statIds, params);
        List<Document> examSchoolStat = mongo.getCollection(map.get("stat").toString()).find(or((List<Bson>) map.get("query")))
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());

        String key = classLevel ? "school" : "school";
        Map<Long, List<Document>> schoolMap = examSchoolStat.stream().collect(groupingBy(e -> e.getLong(key + "Id")));
        List<Document> rs = new ArrayList<>();
        addResult(key, schoolMap, rs, true);

        //下级
        //参数
        Map<String, Object> m = fixNextChildrenParams(classLevel, schoolLevel, examIds, statIds, isCourse, params);
        List<Document> children = mongo.getCollection(m.get("stat").toString()).find(or((List<Bson>) m.get("query")))
                .projection(fields(include(fields),
                        excludeId()))
                .into(new ArrayList<>());

        String finalKey = m.get("key").toString();
        Map<Long, List<Document>> childrenMap = children.stream().collect(groupingBy(e -> e.getLong(finalKey + "Id")));
        addResult(finalKey, childrenMap, rs, false);
        addExamInfo(examParams, rs);
        return rs;
    }

    private Map<String, Object> fixParentParams(boolean schoolLevel, boolean isCourse, String[] examIds, String[] statIds, Map<String, Object> params) {
        List<Bson> query = new ArrayList<>();
        boolean isLine = false;
        if (params.containsKey("line")) {
            isLine = true;
        }
        String stat;
        for (int i = 0; i < examIds.length; i++) {
            Bson and = and(eq("examId", Long.valueOf(examIds[i])),
                    eq("statId", Long.valueOf(statIds[i])),
                    or(eq("schoolId", Long.valueOf(params.get("parentSchoolId").toString())),
                            eq("parentSchoolId", Long.valueOf(params.get("parentSchoolId").toString()))));
            if (isCourse) {
                and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
            }
            query.add(and);
        }

        if (!schoolLevel) {
            stat = isCourse ? "examSchoolCourse" + (isLine ? "Line" : "") + "Stat" : "examSchool" + (isLine ? "Line" : "") + "Stat";
        } else {
            stat = isCourse ? "examSchoolCourse" + (isLine ? "Line" : "") + "Stat" : "examSchool" + (isLine ? "Line" : "") + "Stat";
        }
        Map<String, Object> rs = new HashMap<>();
        rs.put("query", query);
        rs.put("stat", stat);
        return rs;

    }

    private List<Bson> fixChildrenParams(String[] examIds, String[] statIds, boolean isCourse, Map<String, Object> params) {
        List<Bson> or = new ArrayList<>();
        for (int i = 0; i < examIds.length; i++) {
            Bson and = and(eq("examId", Long.valueOf(examIds[i])),
                    eq("statId", Long.valueOf(statIds[i])),
                    eq("schoolId", Long.valueOf(params.get("parentSchoolId").toString())));
            if (isCourse) {
                and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
            }
            or.add(and);
        }
        return or;
    }

    private Map<String, Object> fixNextParentParams(boolean classLevel, boolean isCourse, String[] examIds, String[] statIds, Map<String, Object> params) {
        List<Bson> or = new ArrayList<>();
        for (int i = 0; i < examIds.length; i++) {
            Bson and = and(eq("examId", Long.valueOf(examIds[i])),
                    eq("statId", Long.valueOf(statIds[i])),
                    eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
            if (isCourse) {
                and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
            }
            if (classLevel) {
                and = and(and, eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
            }
            or.add(and);
        }

        String line = "";
        if (!ObjectUtil.isBlank(params.get("line"))) {
            line = "Line";
        }
        String stat;
        if (!classLevel) {
            stat = isCourse ? "examSchoolCourse" + line + "Stat" : "examSchool" + line + "Stat";
        } else {
            stat = isCourse ? "examSchoolCourse" + line + "Stat" : "examSchool" + line + "Stat";
        }
        Map<String, Object> rs = new HashMap<>();
        rs.put("stat", stat);
        rs.put("query", or);
        return rs;
    }

    private Map<String, Object> fixNextChildrenParams(boolean classLevel, boolean schoolLevel, String[] examIds, String[] statIds, boolean isCourse, Map<String, Object> params) {
        List<Bson> or = new ArrayList<>();
        String stat;
        String key;
        String line = "";
        if (!ObjectUtil.isBlank(params.get("line"))) {
            line = "Line";
        }
        if (classLevel) {//班级
            for (int i = 0; i < examIds.length; i++) {
                Bson and = and(eq("examId", Long.valueOf(examIds[i])),
                        eq("statId", Long.valueOf(statIds[i])),
                        eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
                if (isCourse) {
                    and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
                }
                or.add(and);
            }
            stat = isCourse ? "examClassCourse" + line + "Stat" : "examClass" + line + "Stat";
            key = "class";
        } else if (schoolLevel) {//学校
            for (int i = 0; i < examIds.length; i++) {
                Bson and = and(eq("examId", Long.valueOf(examIds[i])),
                        eq("statId", Long.valueOf(statIds[i])),
                        eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
                if (isCourse) {
                    and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
                }
                or.add(and);
            }
            stat = isCourse ? "examSchoolCourse" + line + "Stat" : "examSchool" + line + "Stat";
            key = "school";
        } else {//区域
            for (int i = 0; i < examIds.length; i++) {
                Bson and = and(eq("examId", Long.valueOf(examIds[i])),
                        eq("statId", Long.valueOf(statIds[i])),
                        eq("parentSchoolId", Long.valueOf(params.get("schoolId").toString())));
                if (isCourse) {
                    and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
                }
                or.add(and);
            }
            stat = isCourse ? "examSchoolCourse" + line + "Stat" : "examSchool" + line + "Stat";
            key = "school";
        }
        Map<String, Object> rs = new HashMap<>();
        rs.put("stat", stat);
        rs.put("query", or);
        rs.put("key", key);
        return rs;
    }


    private void addResult(String key, Map<Long, List<Document>> schoolMap, List<Document> rs, boolean zeroIndex) {
        Set<Long> sortSet = new TreeSet<>(Comparator.naturalOrder());
        sortSet.addAll(schoolMap.keySet());
        for (Long schoolId : sortSet) {
            List<Document> exam = schoolMap.get(schoolId);
            Document doc = new Document();
            doc.put(key + "Id", schoolId);
            doc.put("schoolCode", exam.get(0).get("schoolCode"));
            doc.put("schoolName", exam.get(0).get("schoolName"));
            doc.put(key + "Name", exam.get(0).get(key + "Name"));
            doc.put("exam", exam);
            if (zeroIndex) {
                rs.add(0, doc);
            } else {
                rs.add(doc);
            }
        }
    }


    private void computeTopRate(List<Document> exam, Map<Long, Map<Long, List<Document>>> studentMap, Map<Long, Map<Long, Map<String, Object>>> rankingMap) {
        for (Map<String, Object> e : exam) {
            Long examId = Long.valueOf(e.get("examId").toString());
            Long statId = Long.valueOf(e.get("statId").toString());
            Map<Long, List<Document>> examStat = studentMap.get(examId);
            List<Document> document = new ArrayList<>();
            if (!MapUtils.isEmpty(examStat)) {
                document = examStat.get(statId);
            }
            //
            Map<Long, Map<String, Object>> statMap = rankingMap.get(examId);
            Map<String, Object> map = statMap.get(statId);
            Integer participationNumber = Integer.valueOf(map.get("participationNumber").toString());
            int size = document.size();
            e.put("topCount", size);
            e.put("topRate", ScoreRateUtils.rate.divide(size, participationNumber));
        }
    }

    private void addTopResult(Map<Object, List<Document>> schoolMap, List<Document> examStudentStat, Map<Long, Map<Long, Map<String, Object>>> examRanking, List<Document> rs, boolean schoolLevel) {
        String key = schoolLevel ? "school" : "school";
        for (Object k : schoolMap.keySet()) {
            List<Document> exam = schoolMap.get(k);
            //计算每场考试的占比
            List<Document> student;
            if (!schoolLevel) {
                student = examStudentStat.stream().filter(s -> s.getString("schoolCode").startsWith(k.toString())).collect(toList());
            } else {
                student = examStudentStat.stream().filter(s -> ObjectUtil.isValueEquals(s.get("schoolId"), k.toString())).collect(toList());
            }
            Map<Long, Map<Long, List<Document>>> studentMap = student.stream().collect(groupingBy(s -> s.getLong("examId"), groupingBy(s -> s.getLong("statId"))));
            computeTopRate(exam, studentMap, examRanking);
            Document doc = new Document();
            doc.put("exam", exam);
            doc.put(key + "Id", exam.get(0).get(key + "Id"));
            doc.put(key + "Name", exam.get(0).get(key + "Name"));
            rs.add(doc);
        }
    }

    /**
     * 获取分数
     *
     * @param parameterMap examIds statIds
     * @return 分数线
     */
    public Set<String> getSchoolLine(Map<String, Object> parameterMap) {
        Verify.of(parameterMap).isNotBlank("examIds").isNotBlank("statIds").verify();
        String[] examIds = parameterMap.get("examIds").toString().split(",");
        String[] statIds = parameterMap.get("statIds").toString().split(",");
        List<Bson> or = new ArrayList<>();
        for (int i = 0; i < examIds.length; i++) {
            Bson and = and(eq("examId", Long.valueOf(examIds[i])),
                    eq("statId", Long.valueOf(statIds[i])));
            or.add(and);
        }
        List<Document> into = mongo.getCollection("examConfig").find(or(or)).projection(include("overLine")).into(new ArrayList<>());
        Set<String> rs = new HashSet<>();
        for (Document config : into) {
            Document overLine = config.get("overLine", Document.class);
            Boolean enable = overLine.get("enable", Boolean.class);
            if (enable) {
                List<Document> list = overLine.get("line", List.class);
                for (Document line : list) {
                    rs.add(line.get("line").toString());
                }
            }
        }
        return rs;
    }

    /**
     * 增加考试信息
     *
     * @param params examIds
     * @param rs     结果
     */
    private void addExamInfo(Map<String, Object> params, List<Document> rs) {
        List<Map<String, Object>> exams = commonExamService.getExams(params);
        Map<Long, Map<String, Object>> examMap = exams.stream().collect(toMap(e -> Long.valueOf(e.get("examId").toString()), e -> e));
        for (Document doc : rs) {
            List<Document> exam = doc.get("exam", List.class);
            exam.forEach(e -> e.putAll(examMap.get(e.getLong("examId"))));
            exam.sort(Comparator.comparing(e -> DateUtil.getDateLongValue((e.get("startDate").toString()))));
        }
    }

    /**
     * 获取学校数据
     *
     * @param params schoolCompareId courseId indexCode
     * @return 学校数据
     */
    public List<Map<String, Object>> getSchoolCompareExamRate(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolCompareId").isNumeric("courseId").isNotBlank("indexCode").isNotBlank("format").isNumeric("offset").verify();
        List<Map<String, Object>> schoolCompareExam = getSchoolCompareExam(params);
        if(ObjectUtil.isValidId(params.get("schoolCompareGroupId"))){
            List<Long> classIds = schoolCompareGroupService.getSchoolGroupClassBySchoolGroupId(params);
            params.put("classId",classIds);
        }
        return getExamStat(schoolCompareExam, params);
    }

    /**
     * 计算升降 前面的是最新的考试，后一场-前一场
     *
     * @param exam ranking
     */
    public static void computeOffset(List<Document> exam,Map<String,Object> rs, String field, String format, String offset ) {
        for (int i = 0; i < exam.size(); i++) {
            Document first = exam.get(i);
            Object offsetValue = null;
            if (i + 1 < exam.size()) {
                Document second = exam.get(i + 1);
                if (DictUtil.isEquals(MapUtils.getInteger(first, "resultStatus"), "resultStatus", "attend") &&
                  DictUtil.isEquals(MapUtils.getInteger(second, "resultStatus"), "resultStatus", "attend")) {
                    Object fistField = first.get(field);
                    Object secondField = second.get(field);
                    if(fistField != null && secondField != null){
                        if (format.equals("ranking") || format.equals("count")) {
                            offsetValue = Integer.parseInt(secondField.toString()) - Integer.parseInt(fistField.toString());
                        } else {
                            offsetValue = ScoreRateUtils.score.subtract(secondField, fistField);
                        }
                    }
                }
            }

            //offsetValue为0表示数字越大越好(应该吧
            if(offsetValue!=null && ObjectUtil.isValueEquals(0,offset)){
                BigDecimal bd = new BigDecimal(String.valueOf(offsetValue));
                if (bd.compareTo(BigDecimal.ZERO) > 0) {
                    offsetValue = bd.negate();
                } else {
                    offsetValue = bd.abs();
                }
            }

            first.put("offset", offsetValue);
            rs.put(first.get("examId")+field,first.get(field));
            rs.put(first.get("examId")+"offset",offsetValue);
        }
    }

    /**
     * 封装考试
     *
     * @param result      结果
     * @param compareStat 加入对比的考试
     */
    public static List<Document> wrapStat(List<Document> result, List<Map<String, Object>> compareStat) {
        Map<Long, Map<Long, Document>> collect = result.stream().collect(
                groupingBy(r -> Long.valueOf(r.get("examId").toString()),
                        toMap(r -> Long.valueOf(r.get("statId").toString()), r -> r)));
        List<Document> rs = new ArrayList<>();
        for (Map<String, Object> stat : compareStat) {
            Long statId = Long.valueOf(stat.get("statId").toString());
            Long examId = Long.valueOf(stat.get("examId").toString());
            Map<Long, Document> map = collect.get(examId);
            Document doc = new Document(stat);
            if (MapUtils.isEmpty(map)) {
                doc.put("resultStatus", DictUtil.getDictValue("resultStatus", "absent"));
                rs.add(doc);
            } else {
                Document document = map.get(statId);
                if (MapUtils.isEmpty(document)) {
                    doc.put("resultStatus", DictUtil.getDictValue("resultStatus", "absent"));
                    rs.add(doc);
                } else {
                    document.putAll(stat);
                    rs.add(document);
                }
            }
        }
        return rs;
    }


    private void computeBeforeAfter(boolean isCourse, String field, List<Document> stat, List<Bson> studentOr) {
        List<Document> student = mongo.getCollection(isCourse ? "examStudentCourseStat" : "examStudentStat").find(or(studentOr)).projection(fields(
                include("examId", "statId", "schoolId", "classId", "studentId", "examRanking"),
                excludeId())).into(new ArrayList<>());
        Map<Long, Map<Long, Map<Long, List<Document>>>> studentMap = student.stream().collect(groupingBy(s -> Long.valueOf(s.get("examId").toString()),
                groupingBy(s -> Long.valueOf(s.get("statId").toString()),
                        groupingBy(s -> Long.valueOf(s.get("classId").toString())))));
        Map<Long, Map<Long, Map<Long, List<Document>>>> schoolStudentMap = student.stream().collect(groupingBy(s -> Long.valueOf(s.get("examId").toString()),
                groupingBy(s -> Long.valueOf(s.get("statId").toString()),
                        groupingBy(s -> Long.valueOf(s.get("schoolId").toString())))));
        for (Document doc : stat) {
            Long examId = Long.valueOf(doc.get("examId").toString());
            Long statId = Long.valueOf(doc.get("statId").toString());
            Long classId = Long.valueOf(doc.get("classId").toString());
            Long schoolId = Long.valueOf(doc.getOrDefault("schoolId",0).toString());
            List<Document> list;
            if (MapUtils.isEmpty(studentMap)) {
                list = new ArrayList<>();
            } else {
                if(classId==0){
                    list = schoolStudentMap.getOrDefault(examId, new HashMap<>()).getOrDefault(statId, new HashMap<>()).get(schoolId);
                }else {
                    list = studentMap.getOrDefault(examId, new HashMap<>()).getOrDefault(statId, new HashMap<>()).get(classId);
                }
                if (CollectionUtils.isEmpty(list)) {
                    list = new ArrayList<>();
                }
            }
            doc.put(field, list.size());
        }
    }

    private void computeLineRate(String field, List<Document> stat) {
        for (Document doc : stat) {
            if (!ObjectUtil.isValueEquals(doc.get("resultStatus"), DictUtil.getDictValue("resultStatus", "attend"))) {
                continue;
            }
            String[] strings = field.split("\\.");
            Document lineX = doc.get(strings[0], Document.class);
            if (MapUtils.isEmpty(lineX)) {
                continue;
            }
            Object rate = lineX.get(strings[1]);
            doc.put(field, rate);
        }
    }


    /**
     * 获取与入口考试对比
     *
     * @param params schoolCompareId
     * @return 考试对比
     */
    public List<Map<String, Object>> getSchoolCompareExamFinal(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolCompareId").isNumeric("courseId")
                .isNotBlank("indexCode").isNotBlank("format").isNumeric("offset")
                .isNumeric("artsScience")
                .isValidId("gradeId")
                .verify();

        List<Map<String, Object>> schoolCompareExam = getSchoolCompareExam(params);
        if(ObjectUtil.isValidId(params.get("schoolCompareGroupId"))){
            List<Long> classId = schoolCompareGroupService.getSchoolGroupClassBySchoolGroupId(params);
            params.put("classId",classId);
        }
        //取最新一场与入口考试
        params.put("finalType", 1);
        List<Map<String, Object>> schoolFinalExam = schoolFinalExamService.getSchoolFinalExam(params);
        List<Map<String,Object>> rs = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(schoolCompareExam)){
            rs.add(schoolCompareExam.get(0));
        }
        if(CollectionUtils.isNotEmpty(schoolFinalExam)){
            rs.addAll(schoolFinalExam);
        }
        if(CollectionUtils.isNotEmpty(rs)){
            return getExamStat(rs, params);
        }
        return rs;
    }

    /**
     * 获取历次考试对比
     *
     * @param params schoolId  gradeYear indexCode courseId format
     * @return 考试对比
     */
    public List<Document> getSchoolCompareExamPrevious(Map<String, Object> params) {
        Verify.of(params).isNumeric("courseId")
                .isNotBlank("indexCode").isNotBlank("format")
                .isValidId("gradeId").isNumeric("gradeYear")
                .verify();

        List<Map<String, Object>> list = examRepository.selectList("SchoolFinalExamMapper.getAllSchoolFinalExam", params);
        Map<Integer, Map<Integer, Map<String, Object>>> finalExam = list.stream().collect(
                groupingBy(l -> Integer.valueOf(l.get("gradeYear").toString()),
                        toMap(l -> Integer.valueOf(l.get("finalType").toString()), l -> l)));

        if(ObjectUtil.isValidId(params.get("schoolCompareGroupId"))){
            List<Long> classId = schoolCompareGroupService.getSchoolGroupClassBySchoolGroupId(params);
            params.put("classId",classId);
        }

        //考试报告数据
        List<Document> classStat = getClassStat(list, params);
        //按学校 按学年（补全） 按出/入口考试
        Map<Long, List<Document>> classMap = classStat.stream().collect(groupingBy(s -> Long.valueOf(s.get("classId").toString())));
        List<Document> rs = new ArrayList<>();
        for (Long classId : classMap.keySet()) {
            Document doc = new Document();
            //所有考试
            List<Document> classList = classMap.get(classId);
            Document schoolDoc = classList.get(0);
            doc.put("classId", schoolDoc.get("classId"));
            doc.put("className", schoolDoc.get("className"));
            doc.put("teacherName", ObjectUtil.isValueEquals(params.get("courseId"),0)?schoolDoc.get("headerName"):schoolDoc.get("teacherName"));
            Map<Long, Map<Long, Document>> schoolStatMap = classList.stream().collect(
                    groupingBy(s -> Long.valueOf(s.get("examId").toString()),
                            toMap(s -> Long.valueOf(s.get("statId").toString()), s -> s)));
            List<Document> gradeYearList = new ArrayList<>();
            List<Integer> integers = new ArrayList<>(finalExam.keySet());
            integers.sort(Comparator.comparing(i->i));
            for (int i = integers.size()-1;i>=0;i--) {
                Map<Integer, Map<String, Object>> finalExamMap = finalExam.get(integers.get(i));
                Document gradeYearMap = new Document();
                for (Integer finalType : finalExamMap.keySet()) {
                    Map<String, Object> exam = finalExamMap.get(finalType);
                    Long examId = Long.valueOf(exam.get("examId").toString());
                    Long statId = Long.valueOf(exam.get("statId").toString());
                    Map<Long, Document> schoolExamMap = schoolStatMap.get(examId);
                    if (MapUtils.isNotEmpty(schoolExamMap)) {
                        Document document = schoolExamMap.get(statId);
                        if (MapUtils.isEmpty(document)) {
                            document = new Document();
                            document.put("resultStatus", DictUtil.getDictValue("resultStatus", "absent"));
                        }
                        if (finalType == 1) {
                            gradeYearMap.put("participationNumber", document.get("participationNumber"));
                            gradeYearMap.put("inExam", document);
                        } else {
                            gradeYearMap.put("outExam", document);
                        }
                    } else {
                        Document document = new Document();
                        document.put("resultStatus", DictUtil.getDictValue("resultStatus", "absent"));
                        if (finalType == 1) {
                            gradeYearMap.put("participationNumber", document.get("participationNumber"));
                            gradeYearMap.put("inExam", document);
                        } else {
                            gradeYearMap.put("outExam", document);
                        }
                    }
                }
                if(gradeYearMap.get("inExam")==null){
                    Document document = new Document();
                    document.put("resultStatus", DictUtil.getDictValue("resultStatus", "absent"));
                    gradeYearMap.put("inExam", document);
                }
                if(gradeYearMap.get("outExam")==null){
                    Document document = new Document();
                    document.put("resultStatus", DictUtil.getDictValue("resultStatus", "absent"));
                    gradeYearMap.put("outExam", document);
                }
                gradeYearMap.put("gradeYear",integers.get(i)+"届");
                gradeYearList.add(gradeYearMap);
            }
            doc.put("exam", gradeYearList);
            rs.add(doc);
        }
        rs.sort(Comparator.comparing(r->Long.valueOf(r.get("classId").toString())));
        return rs;
    }

    /**
     * 获取报告数据
     *
     * @param examList 考试
     * @param params   indexCode format courseId
     * @return 报告数据
     */
    public List<Map<String, Object>> getExamStat(List<Map<String, Object>> examList, Map<String, Object> params) {
        String format = params.get("format").toString();
        String field = params.get("indexCode").toString();
        String offset = params.get("offset").toString();
        List<Document> list = getClassStat(examList, params);
        parseData(list, format, field);
        Map<String, List<Document>> classMap = list.stream().collect(groupingBy(l -> l.get("classId").toString()));
        List<Map<String, Object>> rs = new ArrayList<>();
        //班主任/任课老师信息
        Map<String, List<Map<String, Object>>> classTeacherMap = null;
        if(field.contains("line") || field.contains("PValue")){
            params.put("examList",examList);
            if(CollectionUtils.isNotEmpty(examList)){
                List<Map<String,Object>> examTeacher = examRepository.selectList("SchoolCompareExamMapper.getExamTeacher", params);
                classTeacherMap = examTeacher.stream().collect(groupingBy(e -> e.get("classId").toString()));
            }
        }
        for (String classId : classMap.keySet()) {
            List<Document> classList = classMap.get(classId);
            Map<String, Object> map = new HashMap<>();
            map.put("className", classList.get(0).get("className"));
            map.put("teacherName", ObjectUtil.isValueEquals(params.get("courseId"),0)?classList.get(0).get("headerName"):classList.get(0).get("teacherName"));
            classList = wrapStat(classList, examList);
            computeOffset(classList,map, field, format,offset);
            map.put("classId", classId);
            if(classTeacherMap!=null){
                if(classTeacherMap.get(classId)!=null){
                    Set<String> names = new HashSet<>();
                    for(Map<String,Object> m : classTeacherMap.get(classId)){
                        names.add(m.get("teacherName").toString());
                    }
                    map.put("teacherName",StringUtils.join(names,"、"));
                }
            }
            map.put("exam", classList);
            rs.add(map);
        }
        //过滤年级与排序字段为空的数据，再排序
        if(!ObjectUtil.isBlank(params.get("sortKey")) && !ObjectUtil.isBlank(params.get("sort"))){
            String sortKey = params.get("sortKey").toString();
            String sort = params.get("sort").toString();
            List<Map<String, Object>> schoolData = rs.stream().filter(r -> ObjectUtil.isValueEquals(0, r.get("classId"))).collect(toList());
            List<Map<String, Object>> blankData = rs.stream().filter(r -> ObjectUtil.isBlank(r.get(sortKey))).collect(toList());
            rs.removeAll(schoolData);
            rs.removeAll(blankData);
            if(sort.equals("asc")){
                rs.sort(Comparator.comparing(r->Double.valueOf(r.get(sortKey).toString())));
            }else {
                rs.sort(Comparator.comparing(r->Double.valueOf(((Map<String,Object>)r).get(sortKey).toString())).reversed());
            }
            //将年级与字段为空的数据加上
            rs.addAll(0,schoolData);
            rs.addAll(blankData);
        }else {
            rs.sort(Comparator.comparing(r->Long.valueOf(r.get("classId").toString())));
        }
        return rs;
    }


    /**
     * 获取报告
     *
     * @param examList 考试
     * @param params   courseId indexCode
     * @return 报告
     */
    public List<Document> getClassStat(List<Map<String, Object>> examList, Map<String, Object> params) {
        if (CollectionUtils.isEmpty(examList)) return new ArrayList<>();
        boolean isCourse = !ObjectUtil.isValueEquals("0", params.get("courseId"));
        String field = params.get("indexCode").toString();

        List<Bson> orList = new ArrayList<>();
        List<Bson> studentOr = new ArrayList<>();
        List<Bson> schoolOr = new ArrayList<>();
        for (Map<String, Object> exam : examList) {
            Bson and = and(eq("examId", Long.valueOf(exam.get("examId").toString())),
                    eq("statId", Long.valueOf(exam.get("statId").toString())));
            if (isCourse) {
                and = and(and, eq("courseId", Long.valueOf(params.get("courseId").toString())));
            }
            if(!ObjectUtil.isBlank(params.get("schoolId"))){
                and = and(and,eq("schoolId",Long.valueOf(params.get("schoolId").toString())));
            }
            Bson schoolAnd = and;
            if (ObjectUtil.isNotEmptyCollections(params.get("classId"))) {
                and = and(and, in("classId", (List)params.get("classId")));
            }

            if(!ObjectUtil.isBlank(params.get("search"))){
                and = and(and,regex("className",params.get("search").toString()));
            }

            orList.add(and);
            schoolOr.add(schoolAnd);
            Bson studentAnd = and;

            //如果是后50名，先查询每场考试最后一名
            if (field.contains("after")) {
                Integer ranking;
                Integer num = Integer.valueOf(field.replace("after", ""));
                List<Document> student = mongo.getCollection(isCourse ? "examStudentCourseStat" : "examStudentStat")
                        .find(and(and, eq("resultStatus", DictUtil.getDictValue("resultStatus", "attend"))))
                        .projection(fields(include("examId", "statId", "classId", "studentId", "ranking"), excludeId())).into(new ArrayList<>());
                //此场考试的最后排名
                Integer lastRanking = 0;
                if (CollectionUtils.isNotEmpty(student)) {
                    Document document = student.stream().max(Comparator.comparing(s -> Integer.valueOf(s.get("ranking").toString()))).get();
                    lastRanking = Integer.valueOf(document.get("ranking").toString());
                }
                if (lastRanking - num >= 0) {
                    ranking = lastRanking - num;
                } else {
                    ranking = 0;
                }
                studentAnd = and(studentAnd, gt("ranking", ranking));
            } else if (field.contains("before")) {
                Integer num = Integer.valueOf(field.replace("before", ""));
                studentAnd = and(studentAnd, lte("ranking", num));
            }
            studentOr.add(studentAnd);
        }

        List<Map<String, Object>> examsBaseInfo = examService.getExamsBaseInfo(MapUtil.of("examIdList",
          examList.stream().map(x -> MapUtils.getLong(x, "examId")).collect(toList())));
        //联考和区域考计算方式不同
        List<Integer> examTypes = new ArrayList<>();
        examTypes.add(DictUtil.getDictValue("examType", "union"));
        examTypes.add(DictUtil.getDictValue("examType", "area"));
        List<Long> examIds = examsBaseInfo.stream().filter(x -> !examTypes.contains(MapUtils.getInteger(x, "examType")))
          .map(x -> MapUtils.getLong(x,"examId")).collect(Collectors.toList());

        String collection;
        String schoolCollection;
        if (isCourse) {
            if (field.contains("line")) {
                collection = "examClassCourseLineStat";
                schoolCollection = "examSchoolCourseLineStat";
            } else if (field.contains("PValue")) {
                collection = "examClassCoursePValueStat";
                schoolCollection = "examSchoolCoursePValueStat";
            } else {
                collection = "examClassCourseStat";
                schoolCollection = "examSchoolCourseStat";
            }
        } else {
            if (field.contains("line")) {
                collection = "examClassLineStat";
                schoolCollection = "examSchoolLineStat";
            } else if (field.contains("PValue")) {
                collection = "examClassPValueStat";
                schoolCollection = "examSchoolPValueStat";
            } else {
                collection = "examClassStat";
                schoolCollection = "examSchoolStat";
            }
        }

        boolean flag = false;
        if(field.equals("PValueRanking")||field.equals("averageScoreRanking")
                ||field.equals("averageSubtraction")
                ||field.equals("averageSubtractionRate")){
            flag=true;
        }

        List<Document> list = mongo.getCollection(collection).find(or(orList))
                .projection(fields(include("examId", "statId", "resultStatus","participationNumber",
                        "classId", "className","teacherId", "teacherName", "headerId", "headerName", "examId", "statId", field), excludeId()))
                .into(new ArrayList<>());
        List<Document> schoolList = mongo.getCollection(schoolCollection).find(or(schoolOr))
                .projection(fields(include("examId", "statId", "resultStatus","participationNumber",
                        "schoolId", "schoolName", "gradeId", "examId", "statId", flag?"a":field), excludeId()))
                .into(new ArrayList<>());
        schoolList.forEach(s->{
            s.put("classId",0);
            s.put("className","年级");
            if(field.equals("TScore")){
                s.put("TScore", examConfigService.getTScoreAverageScore(MapUtils.getLong(s,"examId"), MapUtils.getLong(s,"statId"),
                  examIds.contains(MapUtils.getLong(s,"examId")) ? MapUtils.getLong(s,"gradeId") : 0, isCourse));

            }
        });
        list.addAll(0,schoolList);

        //如果是前x名，后x名，这里要从学生表中查找数据临时计算
        if (field.contains("after") || field.contains("before")) {
            computeBeforeAfter(isCourse, field, list, studentOr);
        }

        //如果是分数线 要将数据取出计算排名
        if (field.contains("line")) {
            computeLineRate(field, list);
        }

        return list;
    }

    /**
     * 对数据中分值字段进行四舍五入保留俩位小数
     *
     * @param data 统计数据
     * @param format 前端参数 score、count、rate、ranking 只对score、rate进行处理
     * @param indexCode 哪个指标
     */
    private void parseData(List<Document> data, String format, String indexCode) {
        if ("score".equals(format)) {
            for (Document item : data) {
                innerParseData(item, indexCode, 2);
            }
        } else if ("rate".equals(format)) {
            for (Document item : data) {
                innerParseData(item, indexCode, 4);
            }
        }
    }

    /**
     * 将item中field字段的值用decimalFormat格式化
     *
     * @param item 数据
     * @param field 字段
     * @param scale 保留的小数位数
     */
    private void innerParseData(Document item, String field, int scale) {
        if (MapUtils.isEmpty(item)) {
            return;
        }
        Object o = item.get(field);
        if (o == null) {
            return;
        }

        BigDecimal val = new BigDecimal(o.toString());
        val = val.setScale(scale, RoundingMode.HALF_UP);
        item.put(field, val.toString());
    }
}
