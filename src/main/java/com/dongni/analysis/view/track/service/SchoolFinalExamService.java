package com.dongni.analysis.view.track.service;

import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Created by scott
 * time: 16:58 2019/1/19
 * description:领导端 出/入口考试
 */
@Service
public class SchoolFinalExamService {

    @Autowired
    private ExamRepository examRepository;

    /**
     * 获取领导端出/入口考试
     *  params userId schoolId artsScience gradeYear
     *
     * @return 结果
     */
    public List<Map<String, Object>> getSchoolFinalExam(Map<String, Object> params) {
        Verify.of(params).isNumeric("gradeId").isNumeric("artsScience").verify();
        return examRepository.selectList("SchoolFinalExamMapper.getSchoolFinalExam", params);
    }

    /**
     * 获取领导端出/入口考试
     *  params schoolId artsScience gradeYear examId statId finalType
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void insertSchoolFinalExam(Map<String, Object> params) {
        Verify.of(params).isValidId("schoolId").isValidId("gradeId")
                .isNumeric("gradeYear").isNumeric("gradeType").isNumeric("artsScience")
                .isValidId("examId").isNumeric("statId").isNumeric("finalType")
                .verify();

        Integer finalType = Integer.valueOf(params.get("finalType").toString());
        if(finalType==1){
            params.put("finalType",2);
        }else {
            params.put("finalType",1);
        }
        List<Map<String, Object>> schoolFinalExam = getSchoolFinalExam(params);
        if(CollectionUtils.isNotEmpty(schoolFinalExam)){
            examRepository.delete("SchoolFinalExamMapper.deleteSchoolFinalExam", schoolFinalExam.get(0));
        }
        params.put("finalType", finalType);
        params.put("currentTime", DateUtil.getCurrentDateTime());
        examRepository.insert("SchoolFinalExamMapper.insertSchoolFinalExam", params);
    }


}
