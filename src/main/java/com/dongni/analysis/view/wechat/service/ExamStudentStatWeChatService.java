package com.dongni.analysis.view.wechat.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.config.bean.ScoreChanges;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.view.monitor.service.ExamItemStatService;
import com.dongni.analysis.view.monitor.service.ExamStatService;
import com.dongni.analysis.view.monitor.service.ExamStudentCourseStatService;
import com.dongni.analysis.view.monitor.service.ExamStudentPaperStatService;
import com.dongni.analysis.view.monitor.utils.ExamTranscriptUtil;
import com.dongni.analysis.view.pvalue.util.ScoreRateUtils;
import com.dongni.analysis.view.util.SearchUtil;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.system.account.service.impl.UserMembershipService;
import com.dongni.basedata.task.service.TaskService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.exception.DongniException;
import com.dongni.commons.exception.ErrorCode;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.dispiay.service.DisplayExamService;
import com.dongni.exam.dispiay.util.DisplayExamUtil;
import com.dongni.exam.export.CommonExamService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.exam.plan.service.ExamStudentService;
import com.dongni.exam.pvalue.service.ReportService;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Lists;
import com.mongodb.BasicDBObject;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Sorts;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dongni.common.utils.DataTypeConverter.toDoubleOrThrow;
import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.*;
import static java.util.stream.Collectors.*;

/**
 * Created by Jayfree
 * time: 15:50 2019/1/17
 * description:微信端学生数据相关
 */
@Service
public class ExamStudentStatWeChatService {

    private final static Logger log = LoggerFactory.getLogger(ExamStudentStatWeChatService.class);

    /**
     * 获取Mongodb数据库对象
     */
    private MongoDatabase mongo;
    @Autowired
    @Qualifier("examStatViewService")
    private ExamStatService examStatService;
    @Autowired
    private ExamStatClassWeChatService examStatClassWeChatService;
    @Autowired
    private ExamStudentService examStudentService;
    @Autowired
    private ReportService reportService;
    @Autowired
    private DisplayExamService displayExamService;
    @Autowired
    private CommonExamService commonExamService;
    @Autowired
    private ExamStudentPaperStatService examStudentPaperStatService;
    @Autowired
    private ExamStudentCourseStatService examStudentCourseStatService;
    @Autowired
    private ExamConfigService examConfigService;
    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private ExamService examService;
    @Autowired
    private UserMembershipService userMembershipService;
    @Autowired
    private BaseDataRepository baseDataRepository;
    @Autowired
    private ExamItemStatService examItemStatService;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private TaskService taskService;

    @Autowired
    public ExamStudentStatWeChatService(AnalysisMongodb mongoClientManager) {
        this.mongo = mongoClientManager.getMongoDatabase();
    }


    /**
     * 学生成绩单
     *
     * @param params examId   classId schoolId
     * @return 学生成绩单
     */
    public Map<String, Object> getResult(Map<String, Object> params) {
        //参数校验
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .isValidId("schoolId")
                .verify();
        Map<String, Object> rs = new HashMap<>();
        Long examId = Long.parseLong(params.get("examId").toString());
        Long statId = Long.parseLong(params.get("statId").toString());
        Long schoolId = Long.parseLong(params.get("schoolId").toString());

        //查询条件
        BasicDBObject basicDBObject = new BasicDBObject();
        basicDBObject.append("examId", examId);
        basicDBObject.append("statId", statId);
        basicDBObject.append("schoolId", schoolId);
        if (ObjectUtil.isNotBlank(params.get("classId"))) {
            Long classId = Long.parseLong(params.get("classId").toString());
            basicDBObject.append("classId", classId);
        }

        SearchUtil.mongoSearch(params, basicDBObject, "studentNum", "studentName");

        //查询总数
        MongoCollection<Document> stat = mongo.getCollection("examStudentStat");
        long totalCount = stat.countDocuments(basicDBObject);
        rs.put("totalCount", totalCount);
        if (totalCount == 0) {
            return rs;
        }

        FindIterable<Document> ls = stat.find(basicDBObject)
                .projection(fields(include("studentId", "studentName", "studentNum",
                        "totalScore", "originalScore", "classRanking", "examRanking", "ranking", "resultStatus"), excludeId()));

        ExamTranscriptUtil.sortHomework(ls, params, examConfigService.showCorrectRate(params));
        ExamTranscriptUtil.page(ls, params);

        List<Map<String, Object>> ss = ls.into(new ArrayList<>());
        List<Document> documents = displayExamService.filterResultListMap(ss, params);
        rs.put("rankingList", documents);

        // 单个班级从examClassStat或examClassCourseStat
        // 全部班级从examSchoolStat或examSchoolCourseStat
        boolean isClass = ObjectUtil.isNotBlank(params.get("classId"));
        boolean isCourse = ObjectUtil.isNotBlank(params.get("courseId"));
        if (isClass){
            rs.put("maxMinScore", examStatClassWeChatService.getExamInfo(params));
        }else {
            List<Bson> query = new ArrayList<>();
            query.add(eq("examId", examId));
            query.add(eq("statId", statId));
            query.add(eq("schoolId", schoolId));

            String collection = "examSchoolStat";

            if (isCourse){
                query.add(eq("courseId", Long.valueOf(params.get("courseId").toString())));
                collection = "examSchoolCourseStat";
            }
            Document doc = mongo.getCollection(collection).find(and(query))
                    .projection(fields(include("highestScore", "lowestScore", "averageScore"), excludeId()))
                    .first();
            rs.put("maxMinScore", doc);
        }
        return rs;
    }


    public Map<String, Object> getCourseResult(Map<String, Object> params) {
        //参数校验
        Verify.of(params)
                .isValidId("examId")
                .isValidId("courseId")
                .isNumeric("statId")
                .verify();


        Map<String, Object> rs = new HashMap<>();
        Long examId = Long.parseLong(params.get("examId").toString());
        Long statId = Long.parseLong(params.get("statId").toString());
        Long courseId = Long.parseLong(params.get("courseId").toString());

        //查询条件
        BasicDBObject basicDBObject = new BasicDBObject();
        basicDBObject.append("examId", examId);
        basicDBObject.append("statId", statId);
        basicDBObject.append("courseId", courseId);

        SearchUtil.mongoSearch(params, basicDBObject, "studentNum", "studentName");

        // 查询的班级，为空表示全部班级
        if (ObjectUtil.isValidId(params.get("classId"))) {
            basicDBObject.append("classId", Long.parseLong(params.get("classId").toString()));
        }
        if (ObjectUtil.isValidId(params.get("schoolId"))) {
            basicDBObject.append("schoolId", Long.parseLong(params.get("schoolId").toString()));
        }

        //查询总数
        MongoCollection<Document> stat = mongo.getCollection("examStudentCourseStat");
        long totalCount = stat.countDocuments(basicDBObject);
        rs.put("totalCount", totalCount);
        if (totalCount == 0) {
            return rs;
        }

        List<String> include = Stream.of("studentId", "studentName", "studentNum", "totalScore", "originalScore", "classRanking",
                        "examRanking", "ranking", "resultStatus", "correctRate", "correctRateRanking", "correctRateClassRanking")
                .collect(toList());
        ScoreChanges scoreChangeObj = examConfigService.getScoreChangeObj(params);
        if (scoreChangeObj.isShowLevelName()) {
            include.add("scoreChangeLevelName");
        }
        FindIterable<Document> ls = stat.find(basicDBObject).projection(fields(include(include), excludeId()));
        ExamTranscriptUtil.sortHomework(ls, params, examConfigService.showCorrectRate(params));
        ExamTranscriptUtil.page(ls, params);

        List<Map<String, Object>> ss = ls.into(new ArrayList<>());
        List<Document> documents = displayExamService.filterResultListMap(ss, params);
        rs.put("rankingList", documents);
        rs.put("maxMinScore", examStatClassWeChatService.getExamInfoMultiClass(params));

        return rs;
    }


    /**
     * 学生考试总成绩对比
     * <p>
     * params examId studentId
     *
     * @return 成绩信息
     */
    public Map<String, Object> getResultComparison(Map<String, Object> params) {

        //参数校验
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .isValidId("classId")
                .isValidId("studentId")
                .verify();
        //参数初始化
        Long examId = Long.parseLong(params.get("examId").toString());
        Long schoolId = Long.parseLong(params.get("schoolId").toString());
        Long classId = Long.parseLong(params.get("classId").toString());
        Long studentId = Long.parseLong(params.get("studentId").toString());
        Long statId = ObjectUtil.isValidId(params.get("statId")) ? Long.parseLong(params.get("statId").toString()) : 0L;
        List<Bson> queryStudent = new ArrayList<>();
        queryStudent.add(eq("examId", examId));
        queryStudent.add(eq("studentId", studentId));
        queryStudent.add(eq("statId", statId));

        Document rs = new Document();

        // todo  classRanking 班级排名 beatClassTotal 击败本班beatExamTotal 击败全年级

        Document studentInfo = mongo.getCollection("examStudentStat").find(and(queryStudent))
                .projection(fields(include("studentId", "studentName", "studentNum",
                        "totalScore", "fullMark", "examRanking", "classRanking", "resultStatus"), excludeId())).first();

        if (MapUtils.isNotEmpty(studentInfo)) {
            rs.putAll(studentInfo);
        }
        if (studentInfo.getInteger("resultStatus") == 0) {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("examId", examId);
            queryParams.put("schoolId", schoolId);
            queryParams.put("classId", classId);
            queryParams.put("examRanking", studentInfo.getInteger("examRanking"));
            queryParams.put("classRanking", studentInfo.getInteger("classRanking"));
            rs.putAll(beatExamCount(false, queryParams));
        }

        //获取班级最高分最低分
        List<Bson> queryClass = new ArrayList<>();
        queryClass.add(eq("examId", examId));
        queryClass.add(eq("classId", classId));
        queryClass.add(eq("statId", statId));
        Document classInfo = mongo.getCollection("examClassStat").find(and(queryClass))
                .projection(fields(include("lowestScore", "averageScore", "highestScore", "totalStudent"), excludeId())).first();

        if (MapUtils.isNotEmpty(classInfo)) {
            Document d = new Document();
            d.put("classLowestScore", classInfo.get("lowestScore"));
            d.put("classAverageScore", classInfo.get("averageScore"));
            d.put("classHighestScore", classInfo.get("highestScore"));
            d.put("classTotalStudent", classInfo.get("totalStudent"));
            rs.putAll(d);
        }

        //获取考试最高分最低分
        List<Bson> querySchool = new ArrayList<>();
        querySchool.add(eq("examId", examId));
        querySchool.add(eq("schoolId", schoolId));
        querySchool.add(eq("statId", statId));
        Document schoolInfo = mongo.getCollection("examSchoolStat").find(and(querySchool))
                .projection(fields(include("lowestScore", "totalStudent", "averageScore", "highestScore"), excludeId())).first();
        if (MapUtils.isNotEmpty(schoolInfo)) {
            Document d = new Document();
            d.put("examLowestScore", schoolInfo.get("lowestScore"));
            d.put("examTotalStudent", schoolInfo.get("totalStudent"));
            d.put("examAverageScore", schoolInfo.get("averageScore"));
            d.put("examHighestScore", schoolInfo.get("highestScore"));
            rs.putAll(d);
        }
        return rs;
    }

    /**
     * 学生考试课程绩对比
     * <p>
     * params examId studentId courseId
     *
     * @return 成绩信息
     */
    public List<Map<String, Object>> getResultCourseComparison(Map<String, Object> params) {

        //参数校验
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .isValidId("classId")
                .isValidId("studentId")
                .verify();

        //参数初始化
        Long examId = Long.parseLong(params.get("examId").toString());
        Long schoolId = Long.parseLong(params.get("schoolId").toString());
        Long classId = Long.parseLong(params.get("classId").toString());
        Long studentId = Long.parseLong(params.get("studentId").toString());
        Long statId = ObjectUtil.isValidId(params.get("statId")) ? Long.parseLong(params.get("statId").toString()) : 0L;

        List<Map<String, Object>> studentList = mongo.getCollection("examStudentCourseStat").find(and(eq("examId", examId)
                , eq("studentId", studentId), eq("statId", statId)))
                .projection(fields(include("studentId", "studentName", "studentNum", "courseId", "courseName", "fullMark", "scoreRate",
                        "totalScore", "examRanking", "classRanking", "resultStatus"), excludeId())).into(new ArrayList<>());

        List<Map<String, Object>> classList = mongo.getCollection("examClassCourseStat").find(and(eq("examId", examId)
                , eq("classId", classId), eq("statId", statId)))
                .projection(fields(include("lowestScore", "averageScore", "highestScore", "totalStudent", "scoreRate", "courseId"), excludeId())).into(new ArrayList<>());


        List<Map<String, Object>> schools = mongo.getCollection("examSchoolCourseStat").find(and(eq("examId", examId)
                , eq("schoolId", schoolId), eq("statId", statId)))
                .projection(fields(include("lowestScore", "totalStudent", "averageScore", "highestScore", "scoreRate", "courseId"), excludeId())).into(new ArrayList<>());

        if (CollectionUtils.isNotEmpty(schools)) {
            List<Map<String, Object>> schoolList = new ArrayList<>();
            for (Map<String, Object> school : schools) {
                Map<String, Object> m = new HashMap<>();
                m.put("examLowestScore", school.get("lowestScore"));
                m.put("examAverageScore", school.get("averageScore"));
                m.put("examHighestScore", school.get("highestScore"));
                m.put("examTotalStudent", school.get("totalStudent"));
                m.put("averageRate", school.get("scoreRate"));
                m.put("courseId", school.get("courseId"));
                schoolList.add(m);
            }

            Map<String, Map<String, Object>> cs = schoolList.stream().collect(toMap(m -> m.get("courseId").toString(), m -> m));
            //加入班级信息
            if (CollectionUtils.isNotEmpty(classList)) {
                Map<String, Map<String, Object>> cc = classList.stream().collect(toMap(m -> m.get("courseId").toString(), m -> m));
                for (String c : cc.keySet()) {
                    Map<String, Object> map = cs.get(c);
                    map.put("classLowestScore", cc.get(c).get("lowestScore"));
                    map.put("classAverageScore", cc.get(c).get("averageScore"));
                    map.put("classHighestScore", cc.get(c).get("highestScore"));
                    map.put("classTotalStudent", cc.get(c).get("totalStudent"));
                    map.put("classScoreRate", cc.get(c).get("scoreRate"));
                }
            }
            //加入学生
            if (CollectionUtils.isNotEmpty(studentList)) {
                Map<String, Map<String, Object>> studentMap = studentList.stream().collect(toMap(m -> m.get("courseId").toString(), m -> m));
                for (String courseId : studentMap.keySet()) {
                    cs.get(courseId).putAll(studentMap.get(courseId));

                    if (Integer.valueOf(studentMap.get(courseId).get("resultStatus").toString()) == 0) {
                        Map<String, Object> queryParams = new HashMap<>();
                        queryParams.put("examId", examId);
                        queryParams.put("schoolId", schoolId);
                        queryParams.put("classId", classId);
                        queryParams.put("examRanking", Integer.valueOf(studentMap.get(courseId).get("examRanking").toString()));
                        queryParams.put("classRanking", Integer.valueOf(studentMap.get(courseId).get("classRanking").toString()));
                        cs.get(courseId).putAll(beatExamCount(false, queryParams));
                    }
                    params.put("courseId", courseId);
                }
            }
            return schoolList;
        }

        return Collections.emptyList();
    }

    /**
     * 学生考试基本信息
     * <p>
     * params examId
     *
     * @return 考试基本信息
     */
    @Transactional(value = ExamRepository.TRANSACTION, rollbackFor = Exception.class)
    public Map<String, Object> getExamInfo(Map<String, Object> params) {
        //参数校验
        Verify.of(params)
                .isValidId("examId")
                .isValidId("studentId")
                .isNumeric("statId")
                .verify();
        Long examId = Long.valueOf(params.get("examId").toString());
        Long studentId = Long.valueOf(params.get("studentId").toString());
        //获取考试基本信息
        Map<String, Object> studentExamInfo = examStudentService.getStudentExamInfo(params);

        if (studentExamInfo == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试报告正在调整中，暂时撤回，本提示信息已失效，请稍后通过新提示消息查看。");
        } else if (DictUtil.isEquals(MapUtils.getInteger(params, "userType"), "userType", "student", "parent")) {
            Map<String,Object> p = new HashMap<>(params);
            p.put("schoolId",studentExamInfo.get("schoolId"));
            p.put("classId",studentExamInfo.get("classId"));
            p.put("correctMode",studentExamInfo.get("correctMode"));
            if(!examStudentService.isExamStatPublished(p)){
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试报告正在调整中，暂时撤回，本提示信息已失效，请稍后通过新提示消息查看。");
            }
        }

        int correctMode = Integer.parseInt(studentExamInfo.get("correctMode").toString());
        int readByClass = DictUtil.getDictValue("correctMode","readByClass");

        // 获取学生缺考信息
        String collection = "examStudentStat";
        if(correctMode == readByClass){
            collection = "examStudentCourseStat";
        }

        Document resultStatus = mongo.getCollection(collection)
                .find(and(eq("examId", examId), eq("studentId", studentId), eq("statId", 0L)))
                .projection(fields(include("resultStatus"), excludeId()))
                .first();

        if(resultStatus!=null){
            studentExamInfo.putAll(resultStatus);
        }

        return studentExamInfo;
    }

    /**
     * 学生的成绩与排名信息
     * @param params
     * @return
     */
    @Transactional(ExamRepository.TRANSACTION)
    public Map<String ,Object> getScoreRankInfo(Map<String, Object> params) {
        //参数校验
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .isValidId("examId")
                .isValidId("classId")
                .isNotBlank("statId")
                .verify();
        Map<String, Object> map = new HashMap<>();
        Long examId = Long.valueOf(params.get("examId").toString());
        Long studentId = Long.valueOf(params.get("studentId").toString());
        Long schoolId = Long.valueOf(params.get("schoolId").toString());
        Long statId = Long.valueOf(params.get("statId").toString());

        Map<String, Object> examInfo = commonExamService.getExamInfo(params);
        Map<String, Object> studentDisplayConfig = displayExamService.getStudentDisplayConfig(params);
        if (MapUtils.isEmpty(studentDisplayConfig)) {
            map.put("originalVolumeStatus",1);
            return map;
        }

        //年级配置
        boolean gradeRankingStatus = MapUtils.getInteger(studentDisplayConfig, "gradeRankingStatus", 0) == 1;
        //班级排名
        boolean classRankingStatus = MapUtils.getInteger(studentDisplayConfig, "classRankingStatus", 0) == 1;
        //班级均分
        boolean classAverageScoreStatus = MapUtils.getInteger(MapUtil.getMap(studentDisplayConfig, "classAverageScoreStatus"),
          "scoreAndRanking", 0).equals(1);
        //年级均分
        boolean schoolAverageScoreStatus = MapUtils.getInteger(MapUtil.getMap(studentDisplayConfig, "schoolAverageScoreStatus"),
          "scoreAndRanking", 0).equals(1);
        //个人得分
        boolean scoreStatus = MapUtils.getInteger(MapUtil.getMap(studentDisplayConfig, "scoreStatus"), "scoreAndRanking", 0).equals(1);
        map.put("originalVolumeStatus", studentDisplayConfig.get("originalVolumeStatus"));

        Document examConfig = mongo.getCollection("examConfig")
          .find(and(eq("examId",examId), eq("statId", 0)))
          .projection(fields(include("examId", "weixinDisplay"), excludeId()))
          .first();
        boolean rankingNeedVip = MapUtils.isNotEmpty(examConfig) &&
          examConfig.get("weixinDisplay", new Document()).getInteger("rankingNeedVip", 0) == 1;
        Boolean isVip = userMembershipService.getIsVip(params);
        // 联考/区域考排名字段
        String examTypeRank = DisplayExamUtil.getExamTypeRank(examInfo);
        boolean examRankingStatus = MapUtils.getInteger(studentDisplayConfig, examTypeRank, 0) == 1 && (!rankingNeedVip || isVip);

        String stuAreaCode = baseDataRepository.selectOne("StudentMapper.getStudentSchoolAreaCode", params);
        List<String> findQuery = findQuery(examRankingStatus, gradeRankingStatus, classRankingStatus, scoreStatus, examInfo, stuAreaCode);
        Bson query = and(and(eq("examId", examId), eq("schoolId", schoolId)), eq("studentId", studentId), eq("statId", statId));
        boolean queryByCourse = ObjectUtil.isValidId(params.get("courseId"));
        Bson courseQuery = queryByCourse ? and(query, eq("courseId", MapUtils.getLong(params, "courseId"))) : query;

        if (!gradeRankingStatus && !classRankingStatus && !classAverageScoreStatus && !schoolAverageScoreStatus && !scoreStatus) {
            //不显示分数与排名
            return map;
        } else {
            //总分
            //可能会出现学生的班级信息发生了变化，但报告并未刷新的情况，此时报告中的班级数据都是变化前的，查询时以报告中的为准
            findQuery.add("classId");
            Document examStudentStat = null;
            if (!queryByCourse) {
                examStudentStat = mongo.getCollection("examStudentStat")
                  .find(query)
                  .projection(fields(include(findQuery), excludeId()))
                  .first();
                if(MapUtils.isEmpty(examStudentStat)){
                    //看的是总分，但没有学生的总分统计数据，那就不展示总分这一行，并且仍然认为是在看单科
                    queryByCourse = true;
                }
            }
            //各科
            findQuery.add("courseId");
            findQuery.add("courseName");

            // 如果打开了单科年级成绩报告趋势
            Map<String, Object> weixinDisplay = displayExamService.getWeixinDisplay(params);
            Boolean gradeScoreReport = Optional.ofNullable(weixinDisplay)
                    .map(i -> MapUtil.<Document>getCast(weixinDisplay, "weixinDisplay"))
                    .map(i -> MapUtil.<Document>getCast(i, "weixinSingle"))
                    .map(i -> MapUtil.getInt(i, "gradeScoreReport", 0) == 1)
                    .orElse(false);
            if (gradeScoreReport) {
                findQuery.add("consistName");
            }
            List<Document> examStudentCourseStat = mongo.getCollection("examStudentCourseStat").find(courseQuery)
                    .projection(fields(include(
                            findQuery), excludeId()))
                    .into(new ArrayList<>());

            //人数统计
            Bson queryNumber = and(eq("examId", examId), eq("statId", statId));
            if (examRankingStatus) {
                //展示联考/区域考排名字段
                if (!queryByCourse) {
                    //总分
                    Document examStat = mongo.getCollection("examStat").find(queryNumber)
                            .projection(fields(include(
                                    "examId",
                                    "participationNumber"), excludeId()))
                            .first();
                    if(MapUtils.isNotEmpty(examStat)){
                        examStudentStat.put("examRankNumber", examStat.get("participationNumber"));
                    }
                }
                //各科
                List<Document> examCourseStat = mongo.getCollection("examCourseStat").find(queryNumber)
                        .projection(fields(include(
                                "examId",
                                "courseId",
                                "participationNumber"), excludeId()))
                        .into(new ArrayList<>());
                Map<Long, Document> courseId2Stat = examCourseStat.stream()
                  .collect(Collectors.toMap(e -> MapUtils.getLong(e, "courseId"), x -> x, (v1, v2) -> v1));
                examStudentCourseStat.forEach(x -> {
                    Long courseId =MapUtils.getLong(x, "courseId");
                    if(courseId2Stat.containsKey(courseId)){
                        x.put("examRankNumber", courseId2Stat.get(courseId).get("participationNumber"));
                    }
                });
            }

            if (gradeRankingStatus || schoolAverageScoreStatus) {
                //展示年级排名或年级均分
                queryNumber = and(queryNumber,eq("schoolId", schoolId));
                if (!queryByCourse) {
                    //总分
                    Document examSchoolStat = mongo.getCollection("examSchoolStat")
                      .find(queryNumber)
                      .projection(fields(include("schoolId", "participationNumber", "averageScore"), excludeId()))
                      .first();
                    if(MapUtils.isNotEmpty(examSchoolStat)){
                        if(gradeRankingStatus){
                            examStudentStat.put("schoolRankNumber", examSchoolStat.get("participationNumber"));
                        }
                        if(schoolAverageScoreStatus){
                            examStudentStat.put("schoolAverageScore", examSchoolStat.get("averageScore"));
                        }
                    }
                }

                //各科
                List<Document> examSchoolCourseStat = mongo.getCollection("examSchoolCourseStat")
                  .find(queryNumber)
                  .projection(fields(include("examId", "courseId", "participationNumber", "averageScore"), excludeId()))
                        .into(new ArrayList<>());
                Map<Long, Document> courseId2Stat = examSchoolCourseStat.stream()
                  .collect(Collectors.toMap(e -> MapUtils.getLong(e, "courseId"), x -> x, (v1, v2) -> v1));
                examStudentCourseStat.forEach(x -> {
                    Long courseId = MapUtils.getLong(x, "courseId");
                    if (courseId2Stat.containsKey(courseId)) {
                        Document stat = courseId2Stat.get(courseId);
                        if (gradeRankingStatus) {
                            x.put("schoolRankNumber", stat.get("participationNumber"));
                        }
                        if (schoolAverageScoreStatus) {
                            x.put("schoolAverageScore", stat.get("averageScore"));
                        }
                    }
                });
            }

            if (classRankingStatus || classAverageScoreStatus) {
                //展示班级排名或者班级均分
                //报告有数据时，始终以报告中的classId为准
                if (!queryByCourse) {
                    //总分
                    Document examClassStat = mongo.getCollection("examClassStat")
                      .find(and(queryNumber, eq("classId",
                        MapUtils.getLong(ObjectUtil.isValidId(examStudentStat.get("classId")) ? examStudentStat : params, "classId"))))
                      .projection(fields(include("classId", "participationNumber", "averageScore"), excludeId()))
                      .first();
                    if (MapUtils.isNotEmpty(examClassStat)) {
                        if (classRankingStatus) {
                            examStudentStat.put("classRankNumber", examClassStat.get("participationNumber"));
                        }
                        if (classAverageScoreStatus) {
                            examStudentStat.put("classAverageScore", examClassStat.get("averageScore"));
                        }
                    }
                }

                //各科
                Document examClassCourseStat;
                for (Document stat : examStudentCourseStat) {
                    examClassCourseStat = mongo.getCollection("examClassCourseStat")
                      .find(and(queryNumber, eq("classId", MapUtils.getLong(stat, "classId")),
                        eq("courseId", MapUtils.getLong(stat, "courseId"))))
                      .projection(fields(include("examId", "courseId", "participationNumber", "averageScore"), excludeId()))
                      .first();
                    if (MapUtils.isNotEmpty(examClassCourseStat)) {
                        if (classRankingStatus) {
                            stat.put("classRankNumber", examClassCourseStat.get("participationNumber"));
                        }
                        if (classAverageScoreStatus) {
                            stat.put("classAverageScore", examClassCourseStat.get("averageScore"));
                        }
                    }
                }
            }
            if (!queryByCourse) {
                examStudentCourseStat.add(0, examStudentStat);
            }
            map.put("scoreRanking", examStudentCourseStat);
            return map;
        }
    }

    private List<String> findQuery(boolean examRankingStatus, boolean gradeRankingStatus, boolean classRankingStatus, boolean scoreStatus,
      Map<String, Object> examInfo, String stuAreaCode) {
        List<String> rs = new ArrayList<>();
        if (examRankingStatus){
            rs.add("ranking");
            //是否添加地市或区县排名字段 现在暂时只有湖南省加这个字段
            if (stuAreaCode.length() != 0 && (Objects.equals(stuAreaCode.split(",")[0], "43"))) {
                //联考：cityRanking，地市排名  区域考：countyRanking，区县排名
                rs.addAll(DictUtil.isEquals(MapUtils.getInteger(examInfo, "examType"), "examType", "union", "asyncUnion") ?
                  Arrays.asList("cityRanking", "cityRankNumber") : Arrays.asList("countyRanking", "countyRankNumber"));
            }
        }
        if (gradeRankingStatus){
            rs.add("examRanking");
        }
        if (classRankingStatus){
            rs.add("classRanking");
        }
        if (scoreStatus){
            rs.add("totalScore");
        }
        rs.add("fullMark");
        return rs;
    }

    /**
     * 学生近十次考试分析
     * <p>
     * params examId
     *
     * @return 考试基本信息
     */
    public List<Document> getExamScoreRate(Map<String, Object> params) {

        //参数校验
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .verify();

        boolean isCourse = false;
        long courseId = -1;

        //获取近十场考试
        List<Map<String, Object>> studentExamList = examStudentService.getStudentExamLimit(params);

        if (CollectionUtils.isEmpty(studentExamList)) {
            return Collections.emptyList();
        }
        List<Long> examIds = studentExamList.stream().map(m -> Long.valueOf(m.get("examId").toString())).collect(toList());
        Map<Long, Map<String, Object>> examIdMap = studentExamList.stream().collect(toMap(m -> Long.valueOf(m.get("examId").toString()), m -> m));


        //参数初始化
        Long studentId = Long.parseLong(params.get("studentId").toString());
        Long schoolId = Long.parseLong(params.get("schoolId").toString());
        Long statId = ObjectUtil.isValidId(params.get("statId")) ? Long.parseLong(params.get("statId").toString()) : 0L;

        List<Bson> query = new ArrayList<>();
        query.add(in("examId", examIds));
        query.add(eq("studentId", studentId));
        query.add(eq("statId", statId));

        if (ObjectUtil.isValidId(params.get("courseId"))) {
            courseId = Long.valueOf(params.get("courseId").toString());
            query.add(eq("courseId", courseId));
            isCourse = true;
        }
        List<Document> studentList = mongo.getCollection(isCourse ? "examStudentCourseStat" : "examStudentStat").find(and(query))
                .projection(fields(include(
                        "studentId",
                        "studentName",
                        "studentNum",
                        "examId",
                        "examName",
                        "ranking",
                        "examRanking",
                        "scoreRate",
                        "fullMark",
                        "totalScore",
                        "fullMark",
                        "resultStatus"), excludeId()))
                .into(new ArrayList<>());
        if (CollectionUtils.isEmpty(studentList)) {
            return Collections.emptyList();
        }
        for (Document stringObjectMap : studentList) {
            stringObjectMap.put("studentScoreRate", stringObjectMap.get("scoreRate"));
            stringObjectMap.remove("scoreRate");
            stringObjectMap.putAll(examIdMap.get(stringObjectMap.getLong("examId")));
        }

        List<Bson> querySchool = new ArrayList<>();
        querySchool.add(eq("schoolId", schoolId));
        querySchool.add(in("examId", examIds));
        querySchool.add(eq("statId", statId));
        if (isCourse) {
            querySchool.add(eq("courseId", courseId));
        }
        List<Map<String, Object>> examList = mongo.getCollection(isCourse ? "examSchoolCourseStat" : "examSchoolStat").find(and(querySchool))
                .projection(fields(include("examId", "averageRate", "totalStudent", "createDateTime"), excludeId())).into(new ArrayList<>());

        Map<String, Map<String, Object>> examMap = examList.stream().collect(toMap(m -> m.get("examId").toString(), m -> m));

        for (Map<String, Object> sl : studentList) {
            String examId = sl.get("examId").toString();
            sl.put("gradeAverageRate", examMap.get(examId).get("averageRate"));
            sl.put("totalStudent", examMap.get(examId).get("totalStudent"));
            sl.put("createDateTime", examMap.get(examId).get("createDateTime"));
        }

        studentList.sort(Comparator.comparing(m -> (int) ((Date) m.get("startDate")).getTime()));
        return studentList;
    }

    /**
     * 学生近十次考试得分等级
     *
     * @return 学生近十次考试得分等级
     */
    public Map<String,Object> getExamScoreLevel(Map<String, Object> params) {

        //参数校验
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .isValidId("examId")
                .verify();

        boolean isCourse = ObjectUtil.isValidId(params.get("courseId"));
        Long examId = Long.valueOf(params.get("examId").toString());
        Long schoolId = Long.valueOf(params.get("schoolId").toString());
        int examIndex = 0;
        //获取当前考试往前推所有考试
        List<Map<String, Object>> studentExam = examStudentService.getAllStudentExamAsc(params);
        Map<String,Object> result=new HashMap<>();
        if (CollectionUtils.isEmpty(studentExam)) {
            result.put("list",Collections.emptyList());
            return result;
        }
        List<Map<String, Object>> studentExamList;
        for (int i = 0; i < studentExam.size(); i++) {
            Long studentExamId = Long.valueOf(studentExam.get(i).get("examId").toString());
            if (studentExamId.equals(examId)){
                examIndex=i;
            }
        }
        studentExamList=studentExam.stream().limit(examIndex+1).collect(toList());
        List<Long> examIds;
        Map<Long, Map<String, Object>> examIdMap;
        //判断是否是单科，如果不是就只取多科目考试
        if (isCourse){
            examIds = studentExamList.stream().map(m -> Long.valueOf(m.get("examId").toString())).collect(toList());
            examIdMap = studentExamList.stream().collect(toMap(m -> Long.valueOf(m.get("examId").toString()), m -> m));
        }else {
            examIds = new ArrayList<>();
            List<Long> queryExamIds = studentExamList.stream().map(m -> Long.valueOf(m.get("examId").toString())).collect(toList());
            Map<String,Object> query=new HashMap<>();
            query.put("examIds",queryExamIds);
            List<Map<String,Object>> examCourseList=examStudentService.getExamCourse(query);
            Map<Long, List<Map<String, Object>>> examList=examCourseList.stream().
                    collect(groupingBy(e->Long.valueOf(e.get("examId").toString())));
            for (Map.Entry<Long, List<Map<String, Object>>> entry:examList.entrySet()){
                if (entry.getValue().size()>1){
                    examIds.add(entry.getKey());
                }else{
                    Long courseId = Long.valueOf(entry.getValue().get(0).get("courseId").toString());
                    if (courseId.equals(41L) || courseId.equals(42L)){
                        examIds.add(entry.getKey());
                    }
                }
            }
            examIdMap = studentExamList.stream().filter(e->examIds.contains(Long.valueOf(e.get("examId").toString())))
                    .collect(toMap(m -> Long.valueOf(m.get("examId").toString()), m -> m));
        }
        //参数初始化
        Long studentId = Long.parseLong(params.get("studentId").toString());
        List<Map<String, Object>> publishSchoolExamStat = examStatService.getPublishSchoolExamStat(examIds, schoolId);
        if (CollectionUtils.isEmpty(publishSchoolExamStat)) {
            result.put("list", Collections.emptyList());
            return result;
        }
        Map<Long, List<Map<String, Object>>> examId2PublishStat = publishSchoolExamStat.stream()
                .collect(groupingBy(i -> MapUtil.getLong(i, "examId")));

        List<Bson> BaseQuery = new ArrayList<>();
        BaseQuery.add(eq("studentId", studentId));
        if (isCourse) {
            long courseId = Long.parseLong(params.get("courseId").toString());
            BaseQuery.add(eq("courseId", courseId));
        }
        List<Bson> examIdAndStatIdQuery = new ArrayList<>();

        for (Long queryExamId : examIds) {
            Document currentExamIdAndStatId = new Document();
            currentExamIdAndStatId.put("examId", queryExamId);

            // 当前前端查询的考试-使用参数携带的statId
            if (ObjectUtil.isValueEquals(params.get("examId"), queryExamId)) {
                Long statId = ObjectUtil.isValidId(params.get("statId")) ? Long.parseLong(params.get("statId").toString()) : 0L;
                currentExamIdAndStatId.put("statId", statId);
            }
            // 过往考试使用该考试第一个公布的报告
            else {
                List<Map<String, Object>> currentExamPublishStat = examId2PublishStat.get(queryExamId);
                if (CollectionUtils.isEmpty(currentExamPublishStat)) {
                    continue;
                }
                currentExamPublishStat.sort(Comparator.comparingInt(i -> MapUtil.getInt(i, "statSort")));
                currentExamIdAndStatId.put("statId", MapUtil.getLong(currentExamPublishStat.get(0), "statId"));
            }
            examIdAndStatIdQuery.add(currentExamIdAndStatId);
        }

        if (CollectionUtils.isEmpty(examIdAndStatIdQuery)) {
            result.put("list", Collections.emptyList());
            return result;
        }

        List<Document> studentList = mongo.getCollection(isCourse ? "examStudentCourseStat" : "examStudentStat")
                .find(and(and(BaseQuery), or(examIdAndStatIdQuery)))
                .projection(fields(include(
                        "studentId",
                        "studentName",
                        "studentNum",
                        "examId",
                        "statId",
                        "examName",
                        //"scoreLevel",采用配置中的规则 consistName，废弃原来的算法
                        "consistName",
                        "examRanking",
                        "classRanking",
                        "resultStatus"), excludeId()))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(studentList)) {
            result.put("list",Collections.emptyList());
            return result;
        }
        for (Document stringObjectMap : studentList) {
            stringObjectMap.putAll(examIdMap.get(stringObjectMap.getLong("examId")));
        }
        studentList.sort(Comparator.comparing(m -> ((Date) ((Map) m).get("startDate")).getTime()).reversed());
        //判断成绩等级浮动
        putStudentType(result, studentList);
        //判断排名浮动
        putStudentRankingType(result,studentList);
        result.put("list", studentList);
        return result;
    }

    private void putStudentRankingTypeClassRanking(Map<String,Object> result, List<Document> studentList) {
        if (studentList.size() == 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            Integer c1 = d1.getInteger("classRanking");
            Integer c2 = d2.getInteger("classRanking");
            if (ObjectUtil.isBlank(c1) || ObjectUtil.isBlank(c2)) {
                result.put("studentClassRankingType", 0);
            } else {
                dealRanking(result, c1, c2, "studentClassRankingType");
            }
        } else if (studentList.size() > 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            Document d3 = studentList.get(2);
            Integer c1 = d1.getInteger("classRanking");
            Integer c2 = d2.getInteger("classRanking");
            Integer c3 = d3.getInteger("classRanking");
            if (ObjectUtil.isBlank(c1) || ObjectUtil.isBlank(c2)) {
                result.put("studentClassRankingType", 0);
            } else if (ObjectUtil.isBlank(c3)) {
                dealRanking(result, c1, c2, "studentClassRankingType");
            } else {
                dealMoreRanking(result,c1,c2,c3,"studentClassRankingType");
            }
        } else if (studentList.size() == 1) {
//            result.put("studentExamRankingType", 0);
            result.put("studentClassRankingType", 0);
        }
    }

    private void putStudentRankingTypeRanking(Map<String,Object> result, List<Document> studentList) {
        if (studentList.size() == 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            Integer e1 = d1.getInteger("examRanking");
            Integer e2 = d2.getInteger("examRanking");
            if (ObjectUtil.isBlank(e1) || ObjectUtil.isBlank(e2)) {
                result.put("studentExamRankingType", 0);
            } else {
                dealRanking(result, e1, e2, "studentExamRankingType");
            }
        } else if (studentList.size() > 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            Document d3 = studentList.get(2);
            Integer e1 = d1.getInteger("examRanking");
            Integer e2 = d2.getInteger("examRanking");
            Integer e3 = d3.getInteger("examRanking");
            if (ObjectUtil.isBlank(e1) || ObjectUtil.isBlank(e2)) {
                result.put("studentExamRankingType", 0);
            } else if (ObjectUtil.isBlank(e3)) {
                dealRanking(result, e1, e2, "studentExamRankingType");
            } else {
                dealMoreRanking(result,e1,e2,e3,"studentExamRankingType");
            }
        } else if (studentList.size() == 1) {
            result.put("studentExamRankingType", 0);
//            result.put("studentClassRankingType", 0);
        }
    }


    private void putStudentRankingType(Map<String,Object> result, List<Document> studentList) {
        if (studentList.size() == 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            Integer c1 = d1.getInteger("classRanking");
            Integer c2 = d2.getInteger("classRanking");
            Integer e1 = d1.getInteger("examRanking");
            Integer e2 = d2.getInteger("examRanking");
            if (ObjectUtil.isBlank(c1) || ObjectUtil.isBlank(c2)) {
                result.put("studentClassRankingType", 0);
            } else {
                dealRanking(result, c1, c2, "studentClassRankingType");
            }
            if (ObjectUtil.isBlank(e1) || ObjectUtil.isBlank(e2)) {
                result.put("studentExamRankingType", 0);
            } else {
                dealRanking(result, e1, e2, "studentExamRankingType");
            }
        } else if (studentList.size() > 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            Document d3 = studentList.get(2);
            Integer c1 = d1.getInteger("classRanking");
            Integer c2 = d2.getInteger("classRanking");
            Integer c3 = d3.getInteger("classRanking");
            Integer e1 = d1.getInteger("examRanking");
            Integer e2 = d2.getInteger("examRanking");
            Integer e3 = d3.getInteger("examRanking");
            if (ObjectUtil.isBlank(c1) || ObjectUtil.isBlank(c2)) {
                result.put("studentClassRankingType", 0);
            } else if (ObjectUtil.isBlank(c3)) {
                dealRanking(result, c1, c2, "studentClassRankingType");
            } else {
                dealMoreRanking(result,c1,c2,c3,"studentClassRankingType");
            }
            if (ObjectUtil.isBlank(e1) || ObjectUtil.isBlank(e2)) {
                result.put("studentExamRankingType", 0);
            } else if (ObjectUtil.isBlank(e3)) {
                dealRanking(result, e1, e2, "studentExamRankingType");
            } else {
                dealMoreRanking(result,e1,e2,e3,"studentExamRankingType");
            }
        } else if (studentList.size() == 1) {
            result.put("studentExamRankingType", 0);
            result.put("studentClassRankingType", 0);
        }
    }

    private void dealMoreRanking(Map<String,Object> result, Integer i1, Integer i2, Integer i3, String key) {
        //连续上升
        if (i1 < i2 && i2 < i3) {
            result.put(key, 4);
        } else if (i1 > i2 && i2 > i3) {
            //连续下降
            result.put(key, 5);
        } else if (i1.equals(i2) && i2.equals(i3)) {
            //稳定
            result.put(key, 6);
        } else {
            //波动
            result.put(key, 7);
        }
    }

    private void dealRanking(Map<String, Object> result, Integer c1, Integer c2, String key) {
        if (c1 < c2) {
            //排名上升
            result.put(key, 1);
        } else if (c1 > c2) {
            //排名下降
            result.put(key, 2);
        } else {
            //排名不变
            result.put(key, 3);
        }
    }

    private void putStudentType(Map<String, Object> result, List<Document> studentList) {
        if (studentList.size() == 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            mapScoreLevel(d1);
            mapScoreLevel(d2);
            if (d1.getInteger("sort") > d2.getInteger("sort")) {
                result.put("studentType", 1);
            } else if (d1.getInteger("sort") < d2.getInteger("sort")) {
                result.put("studentType", 2);
            } else {
                result.put("studentType", 3);
            }
        } else if (studentList.size() > 2) {
            Document d1 = studentList.get(0);
            Document d2 = studentList.get(1);
            Document d3 = studentList.get(2);
            mapScoreLevel(d1);
            mapScoreLevel(d2);
            mapScoreLevel(d3);
            Integer i1 = d1.getInteger("sort");
            Integer i2 = d2.getInteger("sort");
            Integer i3 = d3.getInteger("sort");
            if (i1 > i2 && i2 > i3) {
                result.put("studentType", 4);
            } else if (i1 < i2 && i2 < i3) {
                result.put("studentType", 5);
            } else if (i1.equals(i2) && i2.equals(i3)) {
                result.put("studentType", 6);
            } else {
                result.put("studentType", 7);
            }
        } else if (studentList.size() == 1) {
            result.put("studentType", 0);
        }
    }

    private void mapScoreLevel(Document d) {
        String scoreLevel = d.getString("scoreLevel");
        if (ObjectUtil.isBlank(scoreLevel)) {
            d.put("sort", 0);
        } else {
            switch (scoreLevel) {
                case "A+":
                    d.put("sort", 9);
                    break;
                case "A":
                    d.put("sort", 8);
                    break;
                case "B+":
                    d.put("sort", 7);
                    break;
                case "B":
                    d.put("sort", 6);
                    break;
                case "C+":
                    d.put("sort", 5);
                    break;
                case "C":
                    d.put("sort", 4);
                    break;
                case "D+":
                    d.put("sort", 3);
                    break;
                case "D":
                    d.put("sort", 2);
                    break;
                case "E":
                    d.put("sort", 1);
                    break;
                default:
                    d.put("sort", 0);
                    break;
            }
        }
    }

    /**
     * 获取学生成绩击败率
     * <p>
     * examId studentID 必选
     *
     * @return 学生成绩击败率
     */
    public List<Map<String, Object>> getExamResultBeatRate(Map<String, Object> params) {
        //参数校验
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .verify();

        List<Long> examIdList = new ArrayList<>();
        //参数
        long studentId = Long.parseLong(params.get("studentId").toString());
        long schoolId = Long.parseLong(params.get("schoolId").toString());
        long statId = ObjectUtil.isValidId(params.get("statId")) ? Long.valueOf(params.get("statId").toString()) : 0L;
        List<Map<String, Object>> studentExamList = new ArrayList<>();
        //入参未传examId就是获取近十场的考试击败率
        if (ObjectUtil.isValidId(params.get("examId"))) {
            examIdList.add(Long.valueOf(params.get("examId").toString()));
            Map<String, Object> examInfo = examStudentService.getExamStartDate(MapUtil.of("examId", Long.valueOf(params.get("examId").toString())));
            studentExamList.add(examInfo);
        } else {
            //获取近十场考试
            studentExamList = examStudentService.getStudentExamLimit(params);
            examIdList.addAll(studentExamList.stream().map(m -> Long.valueOf(m.get("examId").toString())).collect(toList()));
        }

        List<Bson> querySchool = new ArrayList<>();
        querySchool.add(eq("schoolId", schoolId));
        querySchool.add(in("examId", examIdList));
        querySchool.add(eq("statId", statId));
        //获取考试信息
        List<Map<String, Object>> examList = mongo.getCollection("examSchoolStat").find(and(querySchool))
                .projection(fields(include("examId", "examName", "averageRate", "totalStudent", "createDateTime"), excludeId())).into(new ArrayList<>());

        List<Document> course;
        List<Document> total;

        //学校course数据
        MongoCollection<Document> examSchoolCourseStat = mongo.getCollection("examSchoolCourseStat");
        course = examSchoolCourseStat.find(and(in("examId", examIdList),
                eq("statId", statId),
                eq("schoolId", schoolId)))
                .projection(fields(include("examId", "courseId", "participationNumber"), excludeId()))
                .sort(Sorts.ascending("courseId"))
                .into(new ArrayList<>());

        //学校总成绩数据
        total = mongo.getCollection("examSchoolStat")
                .find(and(in("examId", examIdList),
                        eq("statId", statId),
                        eq("schoolId", schoolId)))
                .projection(fields(include("examId", "participationNumber"), excludeId())).into(new ArrayList<>());


        MongoCollection<Document> examStudentStat = mongo.getCollection("examStudentStat");
        //学生总成绩数据
        List<Document> currentStudent = examStudentStat
                .find(and(in("examId", examIdList),
                        eq("schoolId", schoolId),
                        eq("statId", statId),
                        eq("studentId", studentId)))
                .projection(fields(include("examId", "examRanking", "course", "resultStatus"), excludeId())).into(new ArrayList<>());

        if (CollectionUtils.isEmpty(currentStudent)) {
            return null;
        }

        //按考试分组统计
        Map<String, Document> totalMap = total.stream().collect(toMap(m -> m.get("examId").toString(), m -> m));
        Map<String, Document> currentStudentMap = currentStudent.stream().collect(toMap(m -> m.get("examId").toString(), m -> m));

        //后一名学生排名
        //  int ranking = currentStudent.getInteger("examRanking");
        List<Document> nextStudentList = new ArrayList<>();
        for (String examId : currentStudentMap.keySet()) {
            if (currentStudentMap.get(examId).getInteger("resultStatus") == 0) {
                Document nextStudent = examStudentStat.find(and(eq("examId", Long.valueOf(examId)),
                        eq("schoolId", schoolId),
                        eq("statId", statId),
                        gt("examRanking", currentStudentMap.get(examId).getInteger("examRanking"))))
                        .projection(fields(include("examId", "resultStatus", "examRanking"), excludeId()))
                        .sort(Sorts.ascending("examRanking"))
                        .first();
                if (MapUtils.isNotEmpty(nextStudent)) {
                    nextStudentList.add(nextStudent);
                }
            }
        }

        //总分击败率 = (参加人数+1-后一名学生排名)/(参加人数-1)
        String totalBeatRate = "0";
        Map<String, String> examTotalBeatRate = new HashMap<>();

        if (CollectionUtils.isNotEmpty(nextStudentList)) {
            Map<String, Document> nextStudentMap = nextStudentList.stream().collect(toMap(m -> m.get("examId").toString(), m -> m));
            for (String examId : nextStudentMap.keySet()) {

                //学生是否缺考
                if (currentStudentMap.get(examId).getInteger("resultStatus") == 1) {
                    examTotalBeatRate.put(examId, "0");
                    continue;
                }
                //计算击败率
                if (nextStudentMap.get(examId).getInteger("resultStatus") != 1) {
                    totalBeatRate = ScoreRateUtils.rate.divide(totalMap.get(examId).getInteger("participationNumber") + 1 - nextStudentMap.get(examId).getInteger("examRanking"),
                            totalMap.get(examId).getInteger("participationNumber") - 1);
                    examTotalBeatRate.put(examId, totalBeatRate);
                }
            }
        }

        //班级或年级课程
        Map<String, Map<Long, Document>> examCourseMap = course.stream().collect(groupingBy(m -> m.get("examId").toString(), toMap(m -> m.getLong("courseId"), m -> m)));

        //取学生课程排名后一名排名数据
        Map<String, List<Document>> rateMap = new HashMap<>();
        MongoCollection<Document> examStudentCourseStat = mongo.getCollection("examStudentCourseStat");

        for (String examId : currentStudentMap.keySet()) {
            List<Document> courseRate = new ArrayList<>();
            List studentCourse = currentStudentMap.get(examId).get("course", List.class);
            for (Object o : studentCourse) {
                Document doc = (Document) o;
                Document rs = new Document();
                rs.put("courseId", doc.get("courseId"));
                rs.put("courseName", doc.get("courseName"));
                //当前学生课程可能是缺考 击败率为 0
                if (doc.getInteger("resultStatus") == 1) {
                    rs.put("courseBeatRate", "0");
                } else {
                    Document data = examStudentCourseStat.find(and(in("examId", Long.valueOf(examId)),
                            eq("schoolId", schoolId),
                            eq("statId", statId),
                            eq("courseId", doc.getLong("courseId")),
                            gt("examRanking", doc.get("examRanking"))))
                            .projection(fields(include("examId", "examRanking", "courseId", "courseName", "resultStatus", "participationNumber"), excludeId()))
                            .sort(Sorts.ascending("examRanking"))
                            .first();
                    if (null != data && data.getInteger("resultStatus") != 1) {
                        String courseBeatRate = ScoreRateUtils.rate.divide(examCourseMap.get(examId).get(data.getLong("courseId")).getInteger("participationNumber") + 1 - data.getInteger("examRanking"),
                                examCourseMap.get(examId).get(data.getLong("courseId")).getInteger("participationNumber") - 1);
                        rs.put("courseBeatRate", courseBeatRate);
                    } else {//没有后一名学生或后一名学生是缺考的 则击败率为 0
                        rs.put("courseBeatRate", "0");
                    }
                }
                courseRate.add(rs);
                rateMap.put(examId, courseRate);
            }
        }

        Map<String, Map<String, Object>> examInfoMap = studentExamList.stream().collect(toMap(m -> m.get("examId").toString(), m -> m));

        for (Map<String, Object> exam : examList) {
            String examId = exam.get("examId").toString();
            if (examTotalBeatRate.containsKey(examId)) {
                exam.put("totalBeatRate", examTotalBeatRate.get(examId));
            }
            if (rateMap.containsKey(examId)) {
                exam.put("courseBeatRateList", rateMap.get(examId));
            }
            exam.putAll(examInfoMap.get(examId));
        }

        examList.sort(Comparator.comparing(m -> (int) ((Date) m.get("startDate")).getTime()));
        return examList;
    }


    /**
     * 获取试题得分失分分析（难度失分、题型失分、题目失分、题目得分）
     *
     * @param params examPlanSubjectId studentID 必选
     * @return 试题得分分析
     */
    @Transactional(ExamRepository.TRANSACTION)
    public Map<String, Object> getExamPaperQuestionAnalysis(Map<String, Object> params) {
        // 参数校验
        //参数校验
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("examId")
                .isValidId("courseId")
                .isValidId("schoolId")
                .isValidId("classId")
                .verify();
        //参数初始化
        //参数
        long examId = Long.parseLong(params.get("examId").toString());
        long schoolId = Long.parseLong(params.get("schoolId").toString());
        long courseId = Long.parseLong(params.get("courseId").toString());
        long studentId = Long.parseLong(params.get("studentId").toString());
        long statId = MapUtils.getLong(params, "statId", 0L);

        Map<String, Object> studentDisplayConfig = displayExamService.getStudentDisplayConfig(params);
        if (MapUtils.isEmpty(studentDisplayConfig)) {
            //可见性配置不存在，那就没得看了
            return Collections.emptyMap();
        }
        //班级均分
        boolean showClassAverageScore = MapUtils.getInteger(MapUtil.getMap(studentDisplayConfig, "classAverageScoreStatus"),
          "questionDetail", 1).equals(1);
        //年级均分
        boolean showSchoolAverageScore = MapUtils.getInteger(MapUtil.getMap(studentDisplayConfig, "schoolAverageScoreStatus"),
          "questionDetail", 1).equals(1);
        //个人得分
        boolean showStuScore = MapUtils.getInteger(MapUtil.getMap(studentDisplayConfig, "scoreStatus"), "questionDetail", 1).equals(1);
        //获取学生课程考试试卷
        Document paper = mongo.getCollection("examStudentPaperStat").find(and(eq("examId", examId),
            eq("statId", statId), eq("schoolId", schoolId), eq("courseId", courseId), eq("studentId", studentId)))
                .projection(fields(include("paperId"), excludeId())).first();

        if (MapUtils.isEmpty(paper)) {
            return null;
        }

        long paperId = paper.getLong("paperId");

        //获取学生作答item信息
        List<Document> studentItemList = mongo.getCollection("examItemStat").find(and(eq("examId", examId), eq("paperId", paperId),
                eq("studentId", studentId), eq("statId", statId)))
                .projection(fields(include("finallyScore",
                        "structureNumber",
                        "questionNumber",
                        "recognitionValue",
                        "paperId",
                        "readType",
                        "scoreValue",
                        "scoreRate",
                        "examItemId"
                        ), excludeId()))
                .sort(Sorts.ascending("questionNumber"))
                .into(new ArrayList<>());
        examItemStatService.fillExamItemSaveFileUrl(studentItemList);

        //获取试卷作答信息

        List<Document> paperItemList = mongo.getCollection("examSchoolQuestionStat").find(and(eq("examId", examId),
                eq("schoolId", schoolId), eq("paperId", paperId), eq("statId", statId)))
                .projection(fields(include("averageScore",
                        "questionId",
                        "questionType",
                        "questionTypeName",
                        "averageRate",
                        "questionNumber",
                        "participationNumber",
                        "lowestScore",
                        "highestScore",
                        "fullMark",
                        "isObjective"), excludeId()))
                .sort(Sorts.ascending("questionNumber"))
                .into(new ArrayList<>());

        //获取学生、班级和年级的题目类型及全卷得分率
        Document studentPaper = mongo.getCollection("examStudentPaperStat").find(and(
          eq("examId", examId),
          eq("statId", statId),
          eq("schoolId", schoolId),
          eq("paperId", paperId),
          eq("studentId", studentId)))
          .projection(fields(include("fullMark","totalScore","totalScoreRate", "questionType","classId")))
          .first();
        if (studentPaper == null) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "当前报告正在执行统计，请在报告统计完成后再查看数据");
        }
        //报告可能是按行政班,也可能是按教学班,这里从studentPaper中取classId会更准确
        Long classId = studentPaper.getLong("classId");
        List<Document> paperClassItemList = mongo.getCollection("examClassQuestionStat").find(and(eq("examId", examId),
                eq("schoolId", schoolId), eq("classId", classId), eq("paperId", paperId), eq("statId", statId)))
                .projection(fields(include(
                        "questionId",
                        "questionType",
                        "questionTypeName",
                        "averageScore",
                        "correctRate",
                        "questionNumber"), excludeId()))
                .sort(Sorts.ascending("questionNumber"))
                .into(new ArrayList<>());


        Document classPaper = Optional.ofNullable(mongo.getCollection("examClassPaperStat").find(and(
                eq("examId", examId),
                eq("statId", statId),
                eq("schoolId", schoolId),
                eq("classId", classId),
                eq("paperId", paperId)))
                .projection(fields(include("totalScoreRate", "questionType")))
                .first()).orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_ERROR, "当前报告正在执行统计，请在报告统计完成后再查看数据"));

        Document schoolPaper = mongo.getCollection("examSchoolPaperStat").find(and(
                eq("examId", examId),
                eq("statId", statId),
                eq("schoolId", schoolId),
                eq("paperId", paperId)))
                .projection(fields(include("totalScoreRate", "questionType")))
                .first();
        double totalScore = toDoubleOrThrow(studentPaper.get("totalScore"),
          () -> new DongniException(ErrorCode.USER_EXCEPTION, "未能获取到成绩,可能阅卷未完成或存在缺考")
        );
        Double fullMark = Double.valueOf(studentPaper.get("fullMark").toString());
        Double studentScoreRate = Double.valueOf(studentPaper.
                getOrDefault("totalScoreRate",totalScore/fullMark).toString());
        Double classScoreRate = Double.valueOf(classPaper.get("totalScoreRate").toString()) ;
        Double schoolScoreRate = 0d ;
        Map<String, Map<String, Object>> schoolQuestionType = new HashMap<>();
        if(schoolPaper!=null){
            schoolScoreRate = Double.valueOf(schoolPaper.get("totalScoreRate").toString()) ;
            schoolQuestionType = ((List<Map<String, Object>>) schoolPaper.get("questionType"))
                    .stream().collect(toMap(m -> m.get("questionTypeName").toString(), m -> m));

        }
        Map<String, Map<String, Object>> studentQuestionType = ((List<Map<String, Object>>) studentPaper.get("questionType"))
                .stream().collect(toMap(m -> m.get("questionTypeName").toString(), m -> m));
        Map<String, Map<String, Object>> classQuestionType = ((List<Map<String, Object>>) classPaper.get("questionType"))
                .stream().collect(toMap(m -> m.get("questionTypeName").toString(), m -> m));


        Map<Integer, Document> paperClassItemMap = paperClassItemList.stream().collect(toMap(m -> m.getInteger("questionNumber"), m -> m));

        Map<Integer, Document> qnMap = paperItemList.stream().collect(toMap(m -> m.getInteger("questionNumber"), m -> m));
        for (Document item : studentItemList) {
            Document qnInfo = new Document();
            if(MapUtils.isNotEmpty(qnMap)){
                qnInfo = qnMap.get(item.getInteger("questionNumber"))==null?new Document():qnMap.get(item.getInteger("questionNumber"));
            }
            Document paperClassItem = paperClassItemMap.get(item.getInteger("questionNumber"));
            if (MapUtils.isEmpty(paperClassItem) && ObjectUtil.isValidId(taskService.getExecutingTaskIdByExamIdAndStatId(examId, statId))) {
                //examClassQuestionStat中没有这道题的数据，且正好有正在执行中的统计任务，则认为应该是数据删除后还没重新保存好
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "当前报告正在执行统计，请在报告统计完成后再查看数据");
            }
            float finallyScore = Float.parseFloat(item.get("finallyScore").toString());
            float scoreValue = Float.parseFloat(item.get("scoreValue").toString());
            float averageScore = Float.parseFloat(qnInfo.getOrDefault("averageScore",0).toString());
            float classAverageScore = Float.parseFloat(paperClassItem.get("averageScore").toString());
            item.put("examAverageLoseScore", scoreValue - averageScore);
            item.put("loseScore", scoreValue - finallyScore);
            item.put("examClassAverageLoseScore", scoreValue - classAverageScore);
            item.putAll(MapUtils.isNotEmpty(qnInfo)?qnInfo:paperClassItem);
            if (showClassAverageScore) {
                //展示班级平均分
                item.put("classAverageScore", paperClassItem.get("averageScore"));
            }
            if (showSchoolAverageScore) {
                //展示年级平均分
                item.put("schoolAverageScore", qnInfo.getOrDefault("averageScore", 0));
            }
            item.put("classCorrectRate", paperClassItem.get("correctRate"));

            if (finallyScore == scoreValue) {
                item.put("isFullMark", 1);
            } else {
                item.put("isFullMark", 0);
            }

            if (finallyScore < averageScore) {
                item.put("isLower", 1);
            } else {
                item.put("isLower", 0);
            }
        }

        Map<String, Object> rs = new HashMap<>();
        rs.put("examStatus", "1");
        rs.put("scoreRate", studentScoreRate);
        rs.put("classScoreRate", classScoreRate);
        rs.put("schoolScoreRate", schoolScoreRate);

        if (CollectionUtils.isEmpty(studentItemList)) {
            return new HashMap<>();
        }

        rs.put("question", studentItemList);

        // 按照得分情况分组（满分、失分、低于年级平均分）
        Map<String, Integer> allScoreGroup = new HashMap<>();
        allScoreGroup.put("full", 0);
        allScoreGroup.put("lose", 0);
        allScoreGroup.put("lower", 0);
        rs.put("allScoreGroup", allScoreGroup);

        // 按照得分率情况分组（简答题、中等题、困难题）
        Map<String, Float> studentDiffGroup = new HashMap<>();
        studentDiffGroup.put("easy", 0f);
        studentDiffGroup.put("middle", 0f);
        studentDiffGroup.put("difficult", 0f);
        studentDiffGroup.put("easyFullMark", 0f);
        studentDiffGroup.put("middleFullMark", 0f);
        studentDiffGroup.put("difficultFullMark", 0f);

        Map<String, Float> classDiffGroup = new HashMap<>(studentDiffGroup);
        Map<String, Float> schoolDiffGroup = new HashMap<>(studentDiffGroup);

        Map<String, Map<String, Float>> diffGroup = new HashMap<>();
        diffGroup.put("student", studentDiffGroup);
        diffGroup.put("class", classDiffGroup);
        diffGroup.put("school", schoolDiffGroup);
        rs.put("diffGroup", diffGroup);

        // 按照题型分组情况分组（选择题、填空题、解答题......）
        Map<String, Object> questionTypeMap = new LinkedHashMap<>();
        rs.put("questionType", questionTypeMap.values());

        // 按照丢分情况分组（只要丢分就加入，丢分按照大小降序）
        List<Map<String, Object>> loseScoreGroup = new ArrayList<>();
        rs.put("loseScoreGroup", loseScoreGroup);

        float easy = 0.7f;
        float difficult = 0.4f;

        studentItemList.forEach(map -> {
            // 按照得分情况分组（满分、失分、低于年级平均分）
            if ("1".equals(map.get("isFullMark").toString())) {
                allScoreGroup.put("full", allScoreGroup.get("full") + 1);
            } else {
                allScoreGroup.put("lose", allScoreGroup.get("lose") + 1);
                if ("1".equals(map.get("isLower").toString())) {
                    allScoreGroup.put("lower", allScoreGroup.get("lower") + 1);
                }
            }

            // 按照得分率情况分组（1简答题、2中等题、3困难题）
            float averageRate = Float.parseFloat(map.getOrDefault("averageRate",0).toString());
            float loseScore = Float.parseFloat(map.get("loseScore").toString());
            float scoreValue = Float.parseFloat(map.get("scoreValue").toString());
            float examAverageLoseScore = Float.parseFloat(map.get("examAverageLoseScore").toString());
            float examClassAverageLoseScore = Float.parseFloat(map.get("examClassAverageLoseScore").toString());
            if (averageRate > easy) {
                studentDiffGroup.put("easy", studentDiffGroup.get("easy") + loseScore);
                studentDiffGroup.put("easyFullMark", studentDiffGroup.get("easyFullMark") + scoreValue);
                classDiffGroup.put("easy", classDiffGroup.get("easy") + examClassAverageLoseScore);
                classDiffGroup.put("easyFullMark", classDiffGroup.get("easyFullMark") + scoreValue);
                schoolDiffGroup.put("easy", schoolDiffGroup.get("easy") + examAverageLoseScore);
                schoolDiffGroup.put("easyFullMark", schoolDiffGroup.get("easyFullMark") + scoreValue);
                map.put("difficulty", "1");
            } else if (averageRate >= difficult) {
                studentDiffGroup.put("middle", studentDiffGroup.get("middle") + loseScore);
                studentDiffGroup.put("middleFullMark", studentDiffGroup.get("middleFullMark") + scoreValue);
                classDiffGroup.put("middle", classDiffGroup.get("middle") + examClassAverageLoseScore);
                classDiffGroup.put("middleFullMark", classDiffGroup.get("middleFullMark") + scoreValue);
                schoolDiffGroup.put("middle", schoolDiffGroup.get("middle") + examAverageLoseScore);
                schoolDiffGroup.put("middleFullMark", schoolDiffGroup.get("middleFullMark") + scoreValue);
                map.put("difficulty", "2");
            } else {
                studentDiffGroup.put("difficult", studentDiffGroup.get("difficult") + loseScore);
                studentDiffGroup.put("difficultFullMark", studentDiffGroup.get("difficultFullMark") + scoreValue);
                classDiffGroup.put("difficult", classDiffGroup.get("difficult") + examClassAverageLoseScore);
                classDiffGroup.put("difficultFullMark", classDiffGroup.get("difficultFullMark") + scoreValue);
                schoolDiffGroup.put("difficult", schoolDiffGroup.get("difficult") + examAverageLoseScore);
                schoolDiffGroup.put("difficultFullMark", schoolDiffGroup.get("difficultFullMark") + scoreValue);
                map.put("difficulty", "3");
            }

            // 按照题型分组情况分组（选择题、填空题、解答题......）
            String questionTypeName = map.get("questionTypeName").toString();
            Map<String, Object> r;
            if (questionTypeMap.containsKey(questionTypeName)) {
                r = (Map<String, Object>) questionTypeMap.get(questionTypeName);
                r.put("scoreValue", Float.parseFloat(r.get("scoreValue").toString()) + Float.parseFloat(map.get("scoreValue").toString()));
                r.put("loseScore", Float.parseFloat(r.get("loseScore").toString()) + Float.parseFloat(map.get("loseScore").toString()));
                r.put("examAverageLoseScore", Float.parseFloat(r.get("examAverageLoseScore").toString()) + Float.parseFloat(map.get("examAverageLoseScore").toString()));
            } else {
                r = new HashMap<>();
                r.put("questionType", map.get("questionType"));
                r.put("questionTypeName", questionTypeName);
                r.put("scoreValue", map.get("scoreValue"));
                r.put("loseScore", map.get("loseScore"));
                r.put("examAverageLoseScore", map.get("examAverageLoseScore"));
                questionTypeMap.put(questionTypeName, r);
            }

            // 按照丢分情况分组（只要丢分就加入，丢分按照大小降序）
            if ("0".equals(map.get("isFullMark").toString())) {
                loseScoreGroup.add(map);
            }

        });
        for (Map.Entry<String, Object> entry : questionTypeMap.entrySet()) {
            Map<String, Object> stMap = studentQuestionType.get(entry.getKey());
            Map<String, Object> cMap = classQuestionType.get(entry.getKey());
            Map<String, Object> scMap = schoolQuestionType.get(entry.getKey());
            Map<String, Object> map = (Map<String, Object>) entry.getValue();
            if (MapUtils.isNotEmpty(stMap)){
                map.put("scoreRate", stMap.get("scoreRate"));
            }
            if (MapUtils.isNotEmpty(cMap)){
                map.put("classScoreRate", cMap.get("scoreRate"));
            }
            if (MapUtils.isNotEmpty(scMap)){
                map.put("schoolScoreRate", scMap.get("scoreRate"));
            }
        }

        loseScoreGroup.sort((m1, m2) -> {
            float r = Float.parseFloat(m2.get("loseScore").toString()) - Float.parseFloat(m1.get("loseScore").toString());
            if (r > 0f) {
                return 1;
            }
            if (r < 0f) {
                return -1;
            }

            return 0;
        });

        if (!showStuScore) {
            //不展示个人得分时，需要把和个人得分相关的部分都去掉
            studentItemList.forEach(x -> {
                x.remove("finallyScore");
                x.remove("loseScore");
                x.remove("isLower");
            });
        }
        return rs;
    }

    /**
     * @return void
     * @Description 考试击败人数
     **/
    private Map<String, Object> beatExamCount(boolean isCourse, Map<String, Object> params) {

        String collection = isCourse ? "examStudentCourseStat" : "examStudentStat";
        Long examId = Long.valueOf(params.get("examId").toString());
        Long schoolId = Long.valueOf(params.get("schoolId").toString());
        Long classId = Long.valueOf(params.get("classId").toString());
        Integer classRanking = Integer.valueOf(params.get("classRanking").toString());
        Integer examRanking = Integer.valueOf(params.get("examRanking").toString());
        Long statId = 0L;

        Map<String, Object> rs = new HashMap<>();

        List<Bson> query = new ArrayList<>();
        query.add(eq("examId", examId));
        query.add(eq("schoolId", schoolId));
        query.add(eq("statId", statId));
        query.add(eq("resultStatus", 0));
        Bson b1 = gt("examRanking", examRanking);
        Bson b2 = gt("classRanking", classRanking);
        query.add(gt("examRanking", examRanking));
        if (isCourse) {
            Long courseId = Long.valueOf(params.get("courseId").toString());
            query.add(eq("courseId", courseId));
        }
        //获取学校击败人数
        long beatExamTotal = mongo.getCollection(collection).countDocuments(and(query));
        //获取班级击败人数
        query.remove(b1);
        query.add(b2);
        query.add(eq("classId", classId));
        long beatExamClassCount = mongo.getCollection(collection).countDocuments(and(query));

        rs.put("beatExamTotal", beatExamTotal);
        rs.put("beatExamClassCount", beatExamClassCount);
        return rs;
    }

    /**
     * @Description: 根据学生id获取已公布考试报告的数目
     * @Param: studentId examId schoolId
     * @return: 考试id和对应的报告数
     */
    public Map<String, Object> getAllExamStatResult(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("examId")
                .isValidId("schoolId")
                .verify();


        Map<String,Object> exam = reportService.getExam(params);
        if(MapUtils.isEmpty(exam)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"考试不存在或已被删除");
        }

        // 判断是否是作业，作业需要特殊处理
        if (examService.isHomework(exam)) {
            Map<String, Object> rs = new HashMap<>();
            rs.put("statId",0L);
            return rs;
        }

        //已排序好的考试报告
        List<Long> statIds = reportService.getExamStatId(params);

        // 判断是否按班级批改，按班级批改的规则比较特殊
        if (CollectionUtils.isEmpty(statIds)){
            if(DictUtil.isEquals(Integer.valueOf(exam.get("correctMode").toString()),"correctMode","readByClass")){
                // 自动公布 && 班级阅卷完成
                if(DictUtil.isEquals(Integer.valueOf(exam.get("autoPublish").toString()),"autoPublish","yes")){
                    Integer classStatus = reportService.getExamClassStatus(params);
                    if(classStatus != null && DictUtil.isEquals(classStatus,"examPaperStatus","readComplete")){
                        Map<String, Object> rs = new HashMap<>();
                        rs.put("statId",0L);
                        return rs;
                    }
                }

            }
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "该考试暂未开放考试报告");
        }

        Map<String, Object> rs = new HashMap<>();
        //从mongo中查找含有studentId的考试报告
        //查 examStudentCourseStat 是因为按班级阅卷完成 单个班级阅卷完成 examStudentStat 没数据
        List<Document> documentList = mongo.getCollection("examStudentCourseStat").find(
                and(
                        eq("studentId", Long.valueOf(params.get("studentId").toString())),
                        eq("examId", Long.valueOf(params.get("examId").toString())),
                        in("statId", statIds)
                ))
                .projection(fields(include("statId"), excludeId()))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(documentList)){
            throw  new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "该考试暂未开放相关考试报告");
        }
        Set<Long> statIdSet = documentList.stream().map(m -> m.getLong("statId")).collect(Collectors.toSet());
        //默认返回排序最靠前的一份考试报告
        for (Long statId : statIds) {
            if (statIdSet.contains(statId)){
                rs.put("statId", statId);
                break;
            }
        }
        return rs;
    }

    public Map<String,Object> getMaxMinScore(Map<String,Object> params) {
        Verify.of(params)
                .isNotBlank("examId")
                .isNotBlank("statId")
                .isNotBlank("schoolId")
                .verify();
        boolean isClass = false;
        boolean isCourse = false;
        if (!ObjectUtil.isBlank(params.get("classId"))){
            isClass = true;
        }
        if (!ObjectUtil.isBlank(params.get("courseId"))){
            isCourse = true;
        }
        if (isClass){
            return examStatClassWeChatService.getExamInfo(params);
        }else {
            Long examId = Long.parseLong(params.get("examId").toString());
            Long statId = Long.parseLong(params.get("statId").toString());

            List<Bson> query = new ArrayList<>();
            query.add(eq("examId", examId));
            query.add(eq("statId", statId));
            query.add(eq("schoolId", Long.valueOf(params.get("schoolId").toString())));

            String collection = "examSchoolStat";

            if (isCourse){
                query.add(eq("courseId", Long.valueOf(params.get("courseId").toString())));
                collection = "examSchoolCourseStat";
            }
            Document doc = mongo.getCollection(collection).find(and(query))
                    .projection(fields(include("highestScore", "lowestScore", "averageScore"), excludeId()))
                    .first();
            return doc;
        }
    }

    public Map<String,Object> getExamResult(Map<String,Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .isValidId("studentId")
                .verify();
        return mongo.getCollection("examStudentStat").find(and(
                eq("examId", Long.parseLong(params.get("examId").toString())),
                eq("statId", Long.parseLong(params.get("statId").toString())),
                eq("studentId", Long.parseLong(params.get("studentId").toString()))))
                .projection(fields(include("studentId", "examId", "statId","studentName","resultStatus","ranking","classRanking","examRanking", "course"), excludeId()))
                .first();
    }

    /**
     * 学生近十次考试得分等级 用于错题本
     * @param params
     * @return
     */
    public Map<String, Object> getWrongBookExamLevel(Map<String, Object> params) {
        //参数校验
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("schoolId")
                .isValidId("examId")
                .verify();

        boolean isCourse = false;
        if (ObjectUtil.isValidId(params.get("courseId"))) {
            isCourse = true;
        }
        Long examId = Long.valueOf(params.get("examId").toString());
        int examIndex = 0;
        //获取当前考试往前推所有考试
        List<Map<String, Object>> studentExam = examStudentService.getAllStudentExamAscForWrongBook(params);
        //过滤掉正在进行中的考试
        //studentExam = studentExam.stream().filter(t-> !DictUtil.isEquals((int)t.get("examStatus"),"examStatus","executing")).collect(toList());
        Map<String,Object> result=new HashMap<>();
        if (CollectionUtils.isEmpty(studentExam)) {
            result.put("list",Collections.emptyList());
            return result;
        }
        List<Map<String, Object>> studentExamList;
        for (int i = 0; i < studentExam.size(); i++) {
            Long studentExamId = Long.valueOf(studentExam.get(i).get("examId").toString());
            if (studentExamId.equals(examId)){
                examIndex=i;
            }
        }
        studentExamList=studentExam.stream().limit(examIndex+1).collect(toList());
        List<Long> examIds;
        Map<Long, Map<String, Object>> examIdMap;
        //判断是否是单科，如果不是就只取多科目考试
        if (isCourse){
            examIds = studentExamList.stream().map(m -> Long.valueOf(m.get("examId").toString())).collect(toList());
            examIdMap = studentExamList.stream().collect(toMap(m -> Long.valueOf(m.get("examId").toString()), m -> m));
        }else {
            examIds = new ArrayList<>();
            List<Long> queryExamIds = studentExamList.stream().map(m -> Long.valueOf(m.get("examId").toString())).collect(toList());
            Map<String,Object> query=new HashMap<>();
            query.put("examIds",queryExamIds);
            List<Map<String,Object>> examCourseList=examStudentService.getExamCourse(query);
            Map<Long, List<Map<String, Object>>> examList=examCourseList.stream().
                    collect(groupingBy(e->Long.valueOf(e.get("examId").toString())));
            for (Map.Entry<Long, List<Map<String, Object>>> entry:examList.entrySet()){
                if (entry.getValue().size()>1){
                    examIds.add(entry.getKey());
                }else{
                    Long courseId = Long.valueOf(entry.getValue().get(0).get("courseId").toString());
                    if (courseId.equals(41L) || courseId.equals(42L)){
                        examIds.add(entry.getKey());
                    }
                }
            }
            examIdMap = studentExamList.stream().filter(e->examIds.contains(Long.valueOf(e.get("examId").toString())))
                    .collect(toMap(m -> Long.valueOf(m.get("examId").toString()), m -> m));
        }
        //参数初始化
        Long studentId = Long.parseLong(params.get("studentId").toString());
        Long statId = ObjectUtil.isValidId(params.get("statId")) ? Long.parseLong(params.get("statId").toString()) : 0L;

        List<Bson> query = new ArrayList<>();
        query.add(in("examId", examIds));
        query.add(eq("studentId", studentId));
        query.add(eq("statId", statId));

        if (isCourse) {
            long courseId = Long.valueOf(params.get("courseId").toString());
            query.add(eq("courseId", courseId));
        }
        List<Document> studentList = mongo.getCollection(isCourse ? "examStudentCourseStat" : "examStudentStat").find(and(query))
                .projection(fields(include(
                        "studentId",
                        "studentName",
                        "studentNum",
                        "examId",
                        "classId",
                        "examName",
                        "totalScore",
                        "consistName",
                        "examRanking",
                        "classRanking",
                        "resultStatus"
                        ), excludeId()))
                .into(new ArrayList<>());
        if (CollectionUtils.isEmpty(studentList)) {
            result.put("list",Collections.emptyList());
            return result;
        }


        if (isCourse) {
            long schoolId = MapUtil.getLong(params, "schoolId");
            long classId = MapUtil.getLong(params, "classId");
            long courseId = MapUtil.getLong(params, "courseId");
            List<Bson> examClassAttendCountQueryList = new ArrayList<>();
            List<Bson> examSchoolAttendCountQueryList = new ArrayList<>();
            studentList.forEach(studentExamItem -> {
                Bson examClassAttendCountQuery = and(eq("examId", MapUtil.getLong(studentExamItem, "examId")), eq("statId", statId),
                        eq("classId", MapUtil.getLong(studentExamItem, "classId")), eq("courseId", courseId));
                examClassAttendCountQueryList.add(examClassAttendCountQuery);

                Bson examSchoolAttendCountQuery = and(eq("examId", MapUtil.getLong(studentExamItem, "examId")), eq("statId", statId),
                        eq("schoolId", schoolId), eq("courseId", courseId));
                examSchoolAttendCountQueryList.add(examSchoolAttendCountQuery);
            });

            // 获取班级参考人数
            List<Document> examClassCourseDocList = mongo.getCollection("examClassCourseStat").find(or(examClassAttendCountQueryList))
                    .projection(fields(include("classId", "className", "courseId", "courseName", "examId", "examName", "resultStatus", "totalStudent"), excludeId()))
                    .into(Lists.newArrayList());
            Map<Long, Integer> examClassCourseDocMap = examClassCourseDocList.stream()
                    .collect(toMap(item -> MapUtil.getLong(item, "examId"), item -> MapUtil.getInt(item, "totalStudent", 0)));
            // 获取年级参考人数
            List<Document> examSchoolCourseDocList = mongo.getCollection("examSchoolCourseStat").find(or(examSchoolAttendCountQueryList))
                    .projection(fields(include("classId", "className", "courseId", "courseName", "examId", "examName", "resultStatus", "totalStudent"), excludeId()))
                    .into(Lists.newArrayList());
            Map<Long, Integer> examSchoolCourseDocMap = examSchoolCourseDocList.stream()
                    .collect(toMap(item -> MapUtil.getLong(item, "examId"), item -> MapUtil.getInt(item, "totalStudent", 0)));

            for (Document examStudentDoc : studentList) {
                long examIdItem = MapUtil.getLong(examStudentDoc, "examId");
                Integer classTotalStudent = Optional.ofNullable(examClassCourseDocMap.get(examIdItem)).orElse(0);
                examStudentDoc.put("classParticipationNumber", classTotalStudent);

                Integer schoolTotalStudent = Optional.ofNullable(examSchoolCourseDocMap.get(examIdItem)).orElse(0);
                examStudentDoc.put("schoolParticipationNumber", schoolTotalStudent);
            }
        }
        for (Document stringObjectMap : studentList) {
            stringObjectMap.putAll(examIdMap.get(stringObjectMap.getLong("examId")));
        }
        studentList.sort(Comparator.comparing(m -> (Date) ((Map) m).get("startDate"))
          .thenComparing(m -> MapUtils.getLong((Map)m, "examId")).reversed());
        //只展示最近的十次
        //filterStudentRanking(studentList,MapUtils.getLong(params, "schoolId"));
        //判断成绩等级浮动
        //putStudentType(result, studentList);
        //判断排名浮动
        putStudentRankingType(result,studentList);
        //result.put("list", studentList);
        //result.put("studentScoreType", result.get("studentType"));
        //年级排名趋势
        List<Document> examRankingTrends = new ArrayList<>();
        //班级排名趋势
        List<Document> classRankingTrends = new ArrayList<>();
        //总分排名趋势
        List<Document> totalScoreTrends = new ArrayList<>();
        if(isCourse){
            //过滤掉正在进行的科目
            ArrayList<Long> examIdList = studentList.stream()
                    .map(doc -> doc.getLong("examId"))
                    .collect(Collectors.toCollection(ArrayList::new));
            Map <Object, Object> getExamCouresMap = new HashMap<>();
            getExamCouresMap.put("courseId",params.get("courseId"));
            getExamCouresMap.put("examIds",examIdList);
            List<String> courseId = new ArrayList<>();
            courseId.add((String) params.get("courseId"));
            //查找父课程
            List<Map<String, Object>> parentCourseIdMap = commonCourseService.getParentCourse(courseId);
            List<Object> parentCourseId = parentCourseIdMap.stream().map(t -> t.get("courseId")).collect(toList());
            List<Map> examCourseStatus = commonRepository.selectList("ExamCourseMapper.getExamCourseStatus", getExamCouresMap);
            Map<Object, Integer> examStatusMap = examCourseStatus.stream().collect(toMap(t -> t.get("examId"), t -> (Integer) t.get("examCourseStatus")));
            for (Document document : studentList) {
                //代表考试未找到此课程，查找考试是否包含父课程
                if(examStatusMap.get(document.get("examId")) == null){
                    Map <Object, Object> getExamCouresMoreMap = new HashMap<>();
                    getExamCouresMoreMap.put("examId",document.get("examId"));
                    getExamCouresMoreMap.put("courseIds",parentCourseId);
                    List<Map> examParentCourseStatus = commonRepository.selectList("ExamCourseMapper.getExamCourseStatusMore", getExamCouresMoreMap);
                    if(!examParentCourseStatus.isEmpty()){
                        //取其中一个综合课程
                        examStatusMap.put(document.get("examId"),(Integer) examParentCourseStatus.get(0).get("examCourseStatus"));
                    }
                }
            }
            studentList = studentList.stream().filter(t -> examStatusMap.get(t.get("examId")) !=null && (long) examStatusMap.get(t.get("examId")) == 20).collect(toList());
        }else {
            //过滤掉正在进行中的考试
            Map<Object, Object> studentExamTypeMap = studentExam.stream().collect(toMap(t -> t.get("examId"), t -> t.get("examStatus")));
            studentList = studentList.stream().filter(t-> !DictUtil.isEquals((int)studentExamTypeMap.get(t.get("examId")),"examStatus","executing")).collect(toList());
        }

        for (Map<String, Object> student : studentList) {
            Document document = new Document();
            if(ObjectUtil.isNotBlank(student.get("examType"))){
                document.put("examType", student.get("examType"));
            }
            if(ObjectUtil.isNotBlank(student.get("startDate"))){
                document.put("startDate", student.get("startDate"));
            }
            if(ObjectUtil.isNotBlank(student.get("examId"))){
                document.put("examId", student.get("examId"));
            }
            if(ObjectUtil.isNotBlank(student.get("examName"))){
                document.put("examName", student.get("examName"));
            }
            if(ObjectUtil.isNotBlank(student.get("classRanking"))){
                document.put("classRanking", student.get("classRanking"));
                document.put("classParticipationNumber", student.get("classParticipationNumber"));
            }else {
                continue;
            }
            classRankingTrends.add(document);
        }


        for (Map<String, Object> student : studentList) {
            Document document = new Document();
            if(ObjectUtil.isNotBlank(student.get("examType"))){
                document.put("examType",student.get("examType"));
            }
            if(ObjectUtil.isNotBlank(student.get("startDate"))){
                document.put("startDate",student.get("startDate"));
            }
            if(ObjectUtil.isNotBlank(student.get("examId"))){
                document.put("examId",student.get("examId"));
            }
            if(ObjectUtil.isNotBlank(student.get("examName"))){
                document.put("examName",student.get("examName"));
            }
            if(ObjectUtil.isNotBlank(student.get("examRanking"))){
                document.put("examRanking",student.get("examRanking"));
                document.put("schoolParticipationNumber", student.get("schoolParticipationNumber"));
            }else {
                continue;
            }
            examRankingTrends.add(document);
        }

        for (Map<String, Object> student : studentList) {
            Document document = new Document();
            if(ObjectUtil.isNotBlank(student.get("examType"))){
                document.put("examType",student.get("examType"));
            }
            if(ObjectUtil.isNotBlank(student.get("startDate"))){
                document.put("startDate",student.get("startDate"));
            }
            if(ObjectUtil.isNotBlank(student.get("examId"))){
                document.put("examId",student.get("examId"));
            }
            if(ObjectUtil.isNotBlank(student.get("examName"))){
                document.put("examName",student.get("examName"));
            }
            if(ObjectUtil.isNotBlank(student.get("totalScore"))){
                document.put("totalScore",student.get("totalScore"));
            }else {
                continue;
            }
            totalScoreTrends.add(document);
        }
        //判断排名浮动
        putStudentRankingTypeRanking(result,examRankingTrends);
        putStudentRankingTypeClassRanking(result,classRankingTrends);
        result.put("examRankingTrends", examRankingTrends.stream().limit(10).collect(Collectors.toList()));
        result.put("classRankingTrends", classRankingTrends.stream().limit(10).collect(Collectors.toList()));
        result.put("totalScoreTrends", totalScoreTrends.stream().limit(10).collect(Collectors.toList()));
        return result;
    }

    /**
     * 过滤学生的年级排名、班级排名
     */
    public void filterStudentRanking(List<Document> list, Long schoolId) {
        Map<Long, Document> studentRankingDisplay = displayExamService
          .getStudentRankingDisplay(list.stream().map(x -> x.getLong("examId")).collect(toList()), schoolId);
        if (MapUtils.isNotEmpty(studentRankingDisplay)) {
            for (Document doc : list) {
                Long examId = doc.getLong("examId");
                Document examDisplay = studentRankingDisplay.get(examId);
                if (MapUtils.isNotEmpty(examDisplay)) {
                    if (examDisplay.getInteger("classRankingStatus", 0) == 0) {
                        doc.remove("classRanking");
                    }
                    if (examDisplay.getInteger("gradeRankingStatus", 0) == 0) {
                        doc.remove("examRanking");
                    }
                    if (examDisplay.get("scoreStatus") instanceof Map) {
                        Map<String, Object> scoreStatus = MapUtil.getMap(examDisplay, "scoreStatus");
                        Map<String, Object> scoreStatusMap = Optional.of(scoreStatus).orElse(new HashMap<>());
                        Integer scoreAndRanking = MapUtil.getInt(scoreStatusMap, "scoreAndRanking", 0);
                        if (scoreAndRanking == 0) {
                            doc.remove("totalScore");
                        }
                    } else {
                        doc.remove("totalScore");
                    }
                }
            }
        }
    }

    /**
     * 获取考试报告中的学生成绩等级
     * @Param: examId statId studentId courseId
     * @return 成绩等级
     */
    public Map<String,Object> getExamScoreRating(Map<String,Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isNumeric("statId")
          .isValidId("studentId")
          .verify();
        Map<String,Object> rs = new HashMap<>();
        Document examConfig = mongo.getCollection("examConfig").find(and(
          eq("examId", MapUtils.getLong(params,"examId")),
          eq("statId", MapUtils.getLong(params,"statId"))))
          .projection(fields(include("examId", "statId", "scoreRating"), excludeId()))
          .first();
        if(MapUtils.isEmpty(examConfig)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "报告配置不存在");
        }
        if(examConfig.get("scoreRating") != null){
            Document scoreRating = (Document) examConfig.get("scoreRating");
            long courseId = ObjectUtil.isBlank(params.get("courseId")) ? 0 : MapUtils.getLong(params,"courseId");
            Document course = ((List<Document>) scoreRating.get("course")).stream()
              .filter(x->MapUtils.getLong(x,"courseId") == courseId).collect(toList()).get(0);
            List<Bson> query = new ArrayList<>();
            query.add(eq("examId", MapUtils.getLong(params,"examId")));
            query.add(eq("statId", MapUtils.getLong(params,"statId")));
            query.add(eq("studentId", MapUtils.getLong(params,"studentId")));
            String collection = "examStudentStat";
            if(courseId != 0){
                query.add(eq("courseId",courseId));
                collection = "examStudentCourseStat";
            }
            Document studentStat = mongo.getCollection(collection).find(and(query))
              .projection(fields(include("examId", "statId", "studentId", "resultStatus", "totalScore"), excludeId()))
              .first();
            if(MapUtils.isEmpty(studentStat)){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "报告生成中，请稍后再试");
            }
            //不缺考时才计算成绩等级，不过缺考的话应该是不会调这个接口的
            if(MapUtils.getInteger(studentStat,"resultStatus") == 0){
                Double totalScore = MapUtils.getDouble(studentStat, "totalScore");
                List<Document> levelList = (List<Document>) course.get("level");
                for (Document level : levelList) {
                    Object open = level.get("open");
                    Object close = level.get("close");
                    if (ObjectUtil.isBlank(open)) {
                        Double closeValue = Double.parseDouble(close.toString());
                        if (totalScore >= closeValue) {
                            rs.put("scoreRating", level.get("name").toString());
                            break;
                        }
                    } else {
                        Double openValue = Double.parseDouble(open.toString());
                        Double closeValue = Double.parseDouble(close.toString());
                        if (totalScore < openValue && totalScore >= closeValue) {
                            rs.put("scoreRating", level.get("name").toString());
                            break;
                        }
                    }
                }
            }
        }
        return rs;
    }

    /**
     * 获取小题分明细
     * 参数: examId courseId paperId schoolId
     *      [statId] 不传默认为0
     *      [classId] 不传则为全部班级
     *      [search] 查询studentNum和studentName
     *
     * @return 小题分明细列表
     */
    public Map<String, Object> getPaperQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("courseId")
                .isValidId("paperId")
                .isValidId("schoolId")
                .verify();

        Map<String, Object> rs = new HashMap<>();

        // mongo查询参数
        List<Bson> query = new ArrayList<>();
        query.add(eq("examId", Long.valueOf(params.get("examId").toString())));
        // statId参数
        long statId = 0L;
        if (ObjectUtil.isValidId(params.get("statId"))) {
            statId = Long.parseLong(params.get("statId").toString());
        }
        query.add(eq("statId", statId));
        query.add(eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        query.add(eq("paperId", Long.valueOf(params.get("paperId").toString())));
        Long courseId = Long.valueOf(params.get("courseId").toString());
        query.add(eq("courseId", courseId));
        // classId参数
        if (ObjectUtil.isValidId(params.get("classId"))) {
            query.add(eq("classId", Long.parseLong(params.get("classId").toString())));
        }
        // 模糊查询参数
        SearchUtil.mongoSearch(params, query, "studentName", "studentNum");

        // 查询总数
        MongoCollection<Document> stat = mongo.getCollection("examStudentPaperStat");
        long totalCount = stat.countDocuments(and(query));
        rs.put("totalCount", totalCount);
        if (totalCount == 0) {
            return rs;
        }

        List<String> include = Stream.of("classId", "className", "studentId", "courseName", "originalScore",
                        "totalScore", "studentName", "studentNum", "questions", "resultStatus", "schoolName")
                .collect(toList());
        ScoreChanges scoreChange = examConfigService.getScoreChangeObj(params);
        if (scoreChange.isShowLevelName()) {
            include.add("scoreChangeLevelName");
        }
        // 查询列表
        FindIterable<Document> ls = mongo.getCollection("examStudentPaperStat").find(and(query))
                .projection(fields(include(include), excludeId()));
        ExamTranscriptUtil.sortExam(ls, params);
        ExamTranscriptUtil.page(ls, params);
        List<Document> studentList = ls.into(new ArrayList<>());

        //获取配置 是否有剔除的班级或学生
        Document examConfig = examConfigService.getExamConfig(params);
        Document studentScope = examConfig.get("studentScope", Document.class);
        Object excludeTag = (studentScope == null) ? null : studentScope.get("excludeTag");
        Object excludeClass = (studentScope == null) ? null : studentScope.get("excludeClass");
        List<Long> excludeStudentIds = new ArrayList<>();
        List<Long> excludeClassIds = new ArrayList<>();

        if (excludeTag != null && ((List) excludeTag).size() > 0) {
            params.put("excludeTagIds", excludeTag);
            excludeStudentIds = commonRepository.selectList("CustomTagStatMapper.getExamStudentIdByExcludeTag", params);
        }
        if (excludeClass != null && ((List) excludeClass).size() > 0) {
            for (Object c : (List) excludeClass) {
                excludeClassIds.add(Long.valueOf(((Map<String, Object>) c).get("classId").toString()));
            }
        }

        Set<Long> excludeStudentIds2 = new HashSet<>(excludeStudentIds);
        Set<Long> excludeClassIds2 = new HashSet<>(excludeClassIds);

        List<Document> rr = new ArrayList<>();
        for (Document s : studentList) {
            Long studentId = Long.valueOf(s.get("studentId").toString());
            Long classId = Long.valueOf(s.get("classId").toString());
            if (!excludeStudentIds2.contains(studentId) && !excludeClassIds2.contains(classId)) {
                rr.add(s);
            }
        }

        studentList = examStudentPaperStatService.processStudentListDataConvert(rr);
        examStudentCourseStatService.getStudentSeatNumberByDocument(studentList);
        rs.put("student", studentList);

        return rs;
    }

    /**
     * 获取题型分明细
     * 参数: examId courseId paperId schoolId
     *      [statId] 不传默认为0
     *      [classId] 不传则为全部班级
     *      [search] 查询studentNum和studentName
     *
     * @return 题型分明细列表
     */
    public Map<String, Object> getPaperQuestionType(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("courseId")
                .isValidId("paperId")
                .isValidId("schoolId")
                .verify();

        Map<String, Object> rs = new HashMap<>();

        // mongo查询参数
        List<Bson> query = new ArrayList<>();
        query.add(eq("examId", Long.valueOf(params.get("examId").toString())));
        // statId参数
        long statId = 0L;
        if (ObjectUtil.isValidId(params.get("statId"))) {
            statId = Long.parseLong(params.get("statId").toString());
        }
        query.add(eq("statId", statId));
        query.add(eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        query.add(eq("paperId", Long.valueOf(params.get("paperId").toString())));
        Long courseId = Long.valueOf(params.get("courseId").toString());
        query.add(eq("courseId", courseId));
        // classId参数
        if (ObjectUtil.isValidId(params.get("classId"))) {
            query.add(eq("classId", Long.parseLong(params.get("classId").toString())));
        }
        // 模糊查询参数
        SearchUtil.mongoSearch(params, query, "studentName", "studentNum");

        // 查询总数
        MongoCollection<Document> stat = mongo.getCollection("examStudentPaperClassifyStat");
        long totalCount = stat.countDocuments(and(query));
        rs.put("totalCount", totalCount);

        // 查询列表
        FindIterable<Document> ls = mongo.getCollection("examStudentPaperClassifyStat").find(and(query))
                .projection(fields(include("schoolId", "schoolName", "classId", "className", "courseName",
                        "studentId", "studentName", "studentNum", "resultStatus", "totalScore", "questionType"), excludeId()));

        ScoreChanges scoreChanges = examConfigService.getScoreChangeObj(params);
        if (totalCount == 0) {
            // 从默认题型中查
            stat = mongo.getCollection("examStudentPaperStat");
            totalCount = stat.countDocuments(and(query));
            rs.put("totalCount", totalCount);

            List<String> include = Stream.of("schoolId", "schoolName", "classId", "className", "courseName",
                            "studentId", "studentName", "studentNum", "resultStatus", "totalScore", "questionType", "originalScore")
                    .collect(toList());
            if (scoreChanges.isShowLevelName()) {
                include.add("scoreChangeLevelName");
            }
            ls = mongo.getCollection("examStudentPaperStat").find(and(query))
                    .projection(fields(include(include), excludeId()));
            if (totalCount == 0) {
                return rs;
            }
        }

        ExamTranscriptUtil.sortExam(ls, params);
        ExamTranscriptUtil.page(ls, params);
        List<Document> ss = ls.into(new ArrayList<>());
        // 补充学生座位号
        examStudentCourseStatService.getStudentSeatNumberByDocument(ss);
        rs.put("student", ss);

        return rs;
    }

    /**
     * 获取线下作业报告中的学生等级分布
     * @Param: examId studentId
     * @return 成绩等级
     */
    public Map<String,Object> getCorrectRating(Map<String,Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isValidId("studentId")
          .verify();

        Map<String,Object> rs = new HashMap<>();
        if(examConfigService.showCorrectRate(params)){
            Document studentStat = mongo.getCollection("examStudentCourseStat")
              .find(and(eq("examId", MapUtils.getLong(params,"examId")), eq("statId", 0),
                eq("studentId", MapUtils.getLong(params, "studentId"))))
              .projection(fields(include("examId", "statId", "studentId", "resultStatus", "correctRate"), excludeId()))
              .first();
            if(MapUtils.getInteger(studentStat,"resultStatus") == 0){
                Double correctRate = MapUtils.getDouble(studentStat, "correctRate");
                String correctRating;
                if (correctRate < 0.6) {
                    correctRating = "低分";
                } else if (correctRate < 0.75) {
                    correctRating = "合格";
                } else if (correctRate < 0.85) {
                    correctRating = "中等";
                } else if (correctRate < 0.95) {
                    correctRating = "良好";
                } else if (correctRate <= 1) {
                    correctRating = "优秀";
                } else {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "正确率异常");
                }
                rs.put("correctRating", correctRating);
            }
        }
        return rs;
    }

    /**
     * 获取含有该学生的全部已公布报告
     * 参数：studentId examId schoolId
     *
     * @return statId statName
     */
    public List<Map<String, Object>> getAllSchoolExamStat(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studentId")
                .isValidId("examId")
                .isValidId("schoolId")
                .verify();

        // 查询考试是否存在
        Map<String, Object> exam = reportService.getExam(params);
        if (MapUtils.isEmpty(exam)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "考试不存在或已被删除!");
        }

        // 作业特殊处理(直接返回statId=0的报告)
        if (examService.isHomework(exam)) {
            return Collections.singletonList(MapUtil.of("statId", 0, "statName", "原始报告"));
        }

        List<Map<String, Object>> statList = null;

        // 自动公布的查询所有未被删除的报告(包括未公布的)
        if (DictUtil.isEquals(Integer.valueOf(exam.get("correctMode").toString()), "correctMode", "readByClass")) {
            if (DictUtil.isEquals(Integer.valueOf(exam.get("autoPublish").toString()), "autoPublish", "yes")) {
                Integer classStatus = reportService.getExamClassStatus(params);
                if (DictUtil.isEquals(classStatus, "examPaperStatus", "readComplete")) {
                    statList = reportService.getAllSchoolExamStat(params);
                }
            }
        }
        // 非自动公布的
        if (statList == null) {
            params.put("statStatus", DictUtil.getDictValue("statStatus", "statOpen"));
            statList = reportService.getAllSchoolExamStat(params);
        }

        if (CollectionUtils.isEmpty(statList)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "该考试未公开报告!");
        }

        // 从examStudentCourseStat查询包含该学生的报告
        Set<Long> statIdSet = statList.stream().map(i -> MapUtil.getLong(i, "statId")).collect(toSet());
        List<Document> documentList = mongo.getCollection("examStudentCourseStat")
                .find(and(
                    eq("studentId", MapUtil.getLong(params, "studentId")),
                    eq("examId", MapUtil.getLong(params, "examId")),
                    in("statId", statIdSet)
                ))
                .projection(fields(include("statId"), excludeId()))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(statList)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "该考试未公开报告!");
        }

        // 整理数据返回
        Set<Long> documentSet = documentList.stream().map(i -> i.get("statId", Long.class)).collect(toSet());
        return statList.stream().filter(i -> documentSet.contains(MapUtil.getLong(i, "statId"))).collect(toList());
    }
}
