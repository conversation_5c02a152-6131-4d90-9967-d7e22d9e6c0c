package com.dongni.analysis.view.monitor.school.controller;

import com.dongni.analysis.view.monitor.school.service.MonitorSchoolViewService;
import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Create by sapluk <br/>
 * time 14:26 2019/04/16 <br/>
 * description: <br/>
 *  监测 - 学校监测 - 班级监测
 */
@RestController
@RequestMapping("/analysis/view/monitor/school")
public class MonitorSchoolViewController extends BaseController {

    @Autowired
    private MonitorSchoolViewService monitorSchoolViewService;

    @GetMapping("/class")
    public Response getClassMonitor(Map<String, Object> params) {
        return new Response(monitorSchoolViewService.getClassMonitor(params));
    }

    @GetMapping("/student")
    public Response getStudentMonitor(Map<String, Object> params) {
        return new Response(monitorSchoolViewService.getStudentMonitor(params));
    }

    @GetMapping("/teacher")
    public Response getTeacherMonitor(Map<String, Object> params) {
        return new Response(monitorSchoolViewService.getTeacherMonitor(params));
    }

    @GetMapping("/student/record")
    public Response getStudentRecord(Map<String, Object> params) {
        return new Response(monitorSchoolViewService.getStudentRecord(params));
    }


    @PostMapping("/student/lefting")
    public Response getStudentScoreLifting(Map<String, Object> params) {
        return new Response(monitorSchoolViewService.getStudentScoreLifting(params));
    }
}
