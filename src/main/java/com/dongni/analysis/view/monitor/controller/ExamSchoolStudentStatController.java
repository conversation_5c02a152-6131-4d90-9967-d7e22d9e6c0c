package com.dongni.analysis.view.monitor.controller;

import com.dongni.analysis.bean.AnalysisConfig;
import com.dongni.analysis.view.monitor.bean.ExamSchStuCourseDataParam;
import com.dongni.analysis.view.monitor.service.ExamSchoolStudentStatService;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.entity.Response;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: hzw
 * @date: 2025/6/3
 * @description: 考试单个学校的学生成绩单、小题分
 */
@RestController
@RequestMapping(AnalysisConfig.CONTEXT_PATH + "/data/exam/school/student/course")
public class ExamSchoolStudentStatController extends BaseController {

	@Autowired
	private ExamSchoolStudentStatService examSchoolStudentStatService;

	/**
	 * 根据考试+学校获取原始报告中学生的成绩单数据
	 */
	@PostMapping("/result")
	@DongniNotRequireLogin
	public Response getExamSchStuCourseResult() {
		return new Response(examSchoolStudentStatService.getExamSchStuCourseResult(getAndCheckParam()));
	}

	/**
	 * 根据考试+学校获取原始报告中学生的小题分数据
	 */

	@PostMapping("/item")
	@DongniNotRequireLogin
	public Response getExamSchStuCourseItem() {
		return new Response(examSchoolStudentStatService.getExamSchStuCourseItem(getAndCheckParam()));
	}

	private ExamSchStuCourseDataParam getAndCheckParam() {
		ExamSchStuCourseDataParam examSchStuCourseDataParam = JSONUtil.parse(JSONUtil.toJson(getParameterMap()),
			ExamSchStuCourseDataParam.class);
		if (!ObjectUtil.isValidId(examSchStuCourseDataParam.getExamId()) || ObjectUtil.isBlank(examSchStuCourseDataParam.getSchoolEId())) {
			throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "参数异常");
		}
		return examSchStuCourseDataParam;
	}
}
