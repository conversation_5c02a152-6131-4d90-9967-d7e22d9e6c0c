package com.dongni.analysis.view.monitor.controller;

import com.dongni.analysis.security.ExamIdScope;
import com.dongni.analysis.view.monitor.service.ExamStatService;
import com.dongni.common.utils.PageUtil;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.annotation.DataScopeBy;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.dongni.basedata.enumeration.UserCategory.*;

/**
 * 考试（联考） 总分成绩接口
 *
 * <AUTHOR>
 * @date 2019/01/08 15:16
 */
@RestController
@RequestMapping("/analysis/view/monitor/exam/stat")
public class ExamStatController extends BaseController {

    @Autowired
    private ExamStatService examStatService;


    /**
     * @Description: 获取指定考试的所有考试报告
     * @param: examId,userId,userName,schoolId
     */
    @GetMapping("/list")
    public Response listExamStatByExamId(){
        return new Response(examStatService.getExamStatsByExamId(getParameterMap()));
    }

    /**
     * 非GET/POST治理 分析
    * @Description: 修改多个成绩报告的公布状态
    * @param: userId, userName,userType, schoolId, examId,statStatus,statIdList(多个statId以逗号隔开) (所有参数必填)
    */
    @PutMapping("/status")
    @Deprecated
    public Response updateExamStatStatusDeprecated(){
        examStatService.updateExamStatStatus(getParameterMap());
        return new Response();
    }

    /**
     * @Description: 修改多个成绩报告的公布状态
     * @param: userId, userName,userType, schoolId, examId,statStatus,statIdList(多个statId以逗号隔开) (所有参数必填)
     */
    @PostMapping("/status")
    public Response updateExamStatStatus(){
        examStatService.updateExamStatStatus(getParameterMap());
        return new Response();
    }

    /**
     * 教辅作业获取多个班级的报告公布状态
     */
    @GetMapping("/class/status")
    public Response getClassExamStatStatus(){
        return new Response(examStatService.getClassExamStatStatus(getParameterMap()));
    }

    /**
     * 教辅作业修改单个班级的报告公布状态
     */
    @PostMapping("/class/status")
    public Response updateClassExamStatStatus(){
        examStatService.updateClassExamStatStatus(getParameterMap());
        return new Response();
    }

    /**
     * 获取平均分指标信息
     *
     * params examBaseId artsScience
     * @return 基础指标
     */
    @GetMapping("/baseIndex")
    public Response getBaseIndex(){
        return new Response(examStatService.getBaseIndex(getParameterMap()));
    }

    /**
     * 获取平均分指标信息
     *
     * params examBaseId
     * @return 各科均分对比
     */
    @GetMapping("/class/baseIndex")
    public Response getClassBaseIndex(){
        return new Response(examStatService.getClassBaseIndex(getParameterMap()));
    }

    /**
     * 获取各学校各科最高分人数
     * 联考报告-学校对比-总分-最高分
     * params
     * @return
     */
    @DongniRequest(privilegeId = {"analysis:schoolContrast:union:getSchoolCourseTop"},
            userCategory = {teacher, government, consultant, maintainer},
            operationName = "联考报告-学校对比-获取最高分")
    @DataScopeBy({ExamIdScope.class})
    @GetMapping("/top")
    public Response getSchoolCourseTop(){
        return new Response(examStatService.getSchoolCourseTop(getParameterMap()));
    }

    /**
     * 获取前后N名分布
     *
     * params
     * @return
     */
    @GetMapping("/rank")
    public Response getBeforeAndAfterRank(){
        return new Response(examStatService.getBeforeAndAfterRank(getParameterMap()));
    }

    /**
     * 获取前后N名分布 班级
     *
     * params
     * @return 前后N名分布 班级
     */
    @DongniRequest(privilegeId = {"analysis:schoolContrast:union:getClassBeforeAndAfterRank"},
            userCategory = {teacher, government, consultant, maintainer},
            operationName = "联考报告-学校对比-班级-获取前后N名")
    @GetMapping("/class/rank")
    public Response getClassBeforeAndAfterRank(){
        return new Response(examStatService.getClassBeforeAndAfterRank(getParameterMap()));
    }

    /**
     * 获取题型均分
     *
     * params
     * @return
     */
    @GetMapping("/questionType/avg")
    public Response getQuestionTypeAvg(){
        return new Response(examStatService.getQuestionTypeAvg(getParameterMap()));
    }
    /**
     * 获取题型均分
     *
     * params
     * @return
     */
    @GetMapping("/class/questionType/avg")
    public Response getClassQuestionTypeAvg(){
        return new Response(examStatService.getClassQuestionTypeAvg(getParameterMap()));
    }

    /**
     * 获取 小题均分
     *
     * params
     * @return
     */
    @GetMapping("/question/avg")
    public Response getQuestionAvg(){
        return new Response(examStatService.getQuestionAvg(getParameterMap()));
    }

    /**
     * 获取小题均分
     *
     * params
     * @return
     */
    @GetMapping("/class/question/avg")
    public Response getClassQuestionAvg(){
        return new Response(examStatService.getClassQuestionAvg(getParameterMap()));
    }

    /**
     * 平均分（联考报告-校内分析）
     *
     * params
     * @return
     */
    @GetMapping("/school/avg")
    public Response getAvgRankInSchool(){
        return new Response(examStatService.getAvgRankInSchool(getParameterMap()));
    }

    /**
     * 最高分（联考报告-校内分析）
     *
     * params
     * @return
     */
    @GetMapping("/school/top")
    public Response getTopInSchool(){
        return new Response(examStatService.getTopInSchool(getParameterMap()));
    }

    /**
     * 前后N名（联考报告-校内分析）
     *
     * params
     * @return
     */
    @GetMapping("/school/rank")
    public Response getBeforeAndAfterInSchool(){
        return new Response(examStatService.getBeforeAndAfterInSchool(getParameterMap()));
    }

    /**
     * 获取考试得分率
     * params examBaseId artsScience
     * @return 考试得分率
     */
    @GetMapping("/scoreRate")
    public Response getScoreRate(){
        return new Response(examStatService.getScoreRate(getParameterMap()));
    }

    /**
     * 获取考试得分率 班级
     * params examBaseId artsScience
     * @return 考试得分率 班级
     */
    @GetMapping("/class/scoreRate")
    public Response getClassScoreRate(){
        return new Response(examStatService.getClassScoreRate(getParameterMap()));
    }

    /**
     * 获取考试得分分布
     * params examBaseId artsScience
     * @return 考试得分分布
     */
    @GetMapping("/scoreSection")
    public Response getScoreSection(){
        Map<String,Object> params = getParameterMap();
        List<Document> scoreSection = examStatService.getScoreSection(params);
        examStatService.dealScoreSection(scoreSection);

        Map<String,Object> rs = new HashMap<>();
        if(CollectionUtils.isEmpty(scoreSection)){
            rs.put("totalCount",0);
            rs.put("list",null);
            return new Response(rs);
        }

        // 分页
        Document r = scoreSection.remove(0);
        List<Document> ls = scoreSection;
        rs.put("totalCount", scoreSection.size()+1);
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))){
            ls = PageUtil.getPageList(scoreSection,Integer.parseInt(params.get("pageSize").toString()),
                    Integer.parseInt(params.get("currentIndex").toString()));
            if(CollectionUtils.isEmpty(ls)){
                ls = new ArrayList<>();
            }
        }

        ls.add(0,r);
        rs.put("list",ls);

        return new Response(rs);
    }

    /**
     * 获取考试得分分布
     * params examBaseId artsScience
     * @return 考试得分分布
     */
    @GetMapping("/class/scoreSection")
    public Response getClassScoreSection(){
        Map<String,Object> params = getParameterMap();
        Map<String,Object> scoreSection = examStatService.getClassScoreSection(params);
        List<Document> scoreSectionList=(List<Document>)scoreSection.get("list");
        examStatService.dealScoreSection(scoreSectionList);
        return new Response(scoreSection);
    }

}
