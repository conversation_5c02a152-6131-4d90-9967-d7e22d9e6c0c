package com.dongni.analysis.view.monitor.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.config.bean.StatDataDimensionEnum;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.analysis.view.monitor.bean.ExamSchoolStatUpdate;
import com.dongni.analysis.view.util.DifferenceRateUtil;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.export.teacher.service.CommonTeacherService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.exception.DongniException;
import com.dongni.commons.exception.ErrorCode;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.ExamSchoolCustomSortService;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolTeacherService;
import com.dongni.exam.common.mark.vo.ClassTeacherVO;
import com.dongni.exam.dispiay.bean.ExamCourseVisibility;
import com.dongni.exam.dispiay.service.DisplayExamService;
import com.dongni.exam.dispiay.util.DisplayExamUtil;
import com.dongni.exam.health.check.ExamStatus;
import com.dongni.exam.plan.service.ExamCourseService;
import com.dongni.exam.plan.service.ExamSchoolService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.newmark.bean.dto.ClassStatStatusDTO;
import com.dongni.newmark.manager.IClassExamStatManager;
import com.dongni.tiku.common.util.MapUtil;
import com.google.common.collect.Sets;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Sorts;

import java.util.*;

import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;

import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2019/01/08 11:32
 */
@Service("examStatViewService")
public class ExamStatService {
    private static final Logger log = LogManager.getLogger(ExamStatService.class);


    @Autowired
    private ExamRepository examRepository;

    @Autowired
    private CommonCourseService commonCourseService;

    @Autowired
    private DisplayExamService displayExamService;

    @Autowired
    private ExamSchoolService examSchoolService;
    /**
     * 获取Mongodb数据库对象
     */
    private MongoDatabase mongoDatabase;
    @Autowired
    private ExamConfigService examConfigService;
    @Autowired
    private ExamSchoolCustomSortService examSchoolCustomSortService;
    @Autowired
    private ExamService examService;
    @Autowired
    private IClassExamStatManager classExamStatManager;
    @Autowired
    private ISchoolTeacherService schoolTeacherService;
    @Autowired
    private ExamCourseService examCourseService;
    @Autowired
    private CommonTeacherService commonTeacherService;

    @Autowired
    public ExamStatService(AnalysisMongodb analysisMongodb) {
        this.mongoDatabase = analysisMongodb.getMongoDatabase();
    }

    /**
     * 获取已公布的报告
     *
     * @param examIds 考试id
     * @param schoolId 学校id
     * @return 已公布的报告
     */
    public List<Map<String, Object>> getPublishSchoolExamStat(List<Long> examIds, Long schoolId) {
        if (CollectionUtils.isEmpty(examIds)) {
            return Collections.emptyList();
        }

        Map<String, Object> params = MapUtil.of("examIds", examIds, "schoolId", schoolId);
        return examRepository.selectList("ExamStatReportMapper.getPublishSchoolExamStat", params);
    }

    /**
     * @Description: 获取指定考试的所有考试报告
     * @param: examId, userId, userName,schoolId
     */
    public Map<String,Object> getExamStatsByExamId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .verify();

        //获取处于开启状态的考试报告
        Map<String, Object> p=new HashMap<>();
        p.put("examId", params.get("examId"));
        p.put("isDisplay", DictUtil.getDictValue("isDisplay","on" ) );
        p.put("schoolId",params.get("schoolId"));

        List<Map<String, Object>> examStatList = examRepository.selectList("ExamStatReportMapper.getAllExamStatByExamIdList", p);

        return MapUtil.of("examStatList", examStatList);
    }
    
    /**
     * 获取指定考试的所有学校考试报告
     * @param params schoolId examIdList
     * @return examId statId statName statStatus isDisplay
     */
    public List<Map<String, Object>> getExamStatsByExamIdList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isNotEmptyCollections("examIdList")
                .verify();
        return examRepository.selectList("ExamStatReportMapper.getAllExamStatByExamIdList", params);
    }
    
    /**
     * @Description: 修改多个成绩报告的公布状态
     * @param: userId, userName,userType, schoolId, examId,statStatus,statIdList(多个statId以逗号隔开) (所有参数必填)
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateExamStatStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("schoolId")
                .isValidId("examId")
                .isNumeric("userType")
                .isNumeric("statStatus")
                .isNotBlank("statIdList")
                .verify();

        Map<String, Object> examMap = examRepository.selectOne("ExamSchoolMapper.getExamSchoolStatusInfo", params);
        if (MapUtils.isEmpty(examMap)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "不存在该考试");
        }

        //考试状态
        int statStatus = Integer.valueOf(params.get("statStatus").toString());

        //公布考试报告
        if (statStatus == DictUtil.getDictValue("statStatus","statOpen" )) {
            publishSchoolExamStats(params,examMap);
        }

        //撤销考试报告
        if (statStatus == DictUtil.getDictValue("statStatus","statClose" )) {
            unPublishSchoolExamStats(params);

            // 如果考试是按班级 + 自动公布，将其置为非自动公布，学生端便看不到考试
            if (MapUtil.getInt(examMap, "correctMode") == 0
                    && MapUtil.getInt(examMap, "autoPublish") == 1) {
                params.put("currentTime", DateUtil.getCurrentDateTime());
                examRepository.update("ExamMapper.closeExamAutoPublish", params);
            }
        }
        Integer examStatus = Integer.valueOf(examMap.get("examStatus").toString());
        if (!examStatus.equals(DictUtil.getDictValue("examStatus", "executing"))){

            Integer published = DictUtil.getDictValue("examStatus", "published");
            Integer unpublished = DictUtil.getDictValue("examStatus", "unpublished");
            //更新学校考试状态
            params.put("currentTime", DateUtil.getCurrentDateTime());
            List<Object> objects = examRepository.selectList("ExamSchoolMapper.getExamSchoolStatStatus", params);

            params.put("examSchoolStatus", CollectionUtils.isEmpty(objects) ? unpublished : published);

            examRepository.update("ExamSchoolMapper.updateExamSchoolStatusBySchoolId", params);
        }
    }


    //公布考试报告

    /**
     *
     * @param params {userId userName examId schoolId statIdList}
     * @param examMap {examStatus gradeType gradeYear}
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void publishSchoolExamStats(Map<String,Object> params, Map<String, Object> examMap){
        Verify.of(params)
                .isValidId("examId")
                .isNotBlank("statIdList")
                .isValidId("schoolId")
                .verify();
        Verify.of(examMap)
                .isNumeric("examStatus")
                .isNumeric("gradeType")
                .isNumeric("gradeYear")
                .verify();

        //要公布的考试报告id列表
        List<Long> statIdList = StringUtil.strToList(params.get("statIdList").toString(), ",", Long.class);
        if (CollectionUtils.isEmpty(statIdList)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "statIdList错误");
        }
        publishSchoolExamStat(params, statIdList);
//        Map<String, Integer> gradeType = DictUtil.getDictKeyValue("gradeType");
//
//        int examGradeType = Integer.valueOf(examMap.get("gradeType").toString());
//        //一年级
//        int firstGrade =gradeType.get("1");
//        //高一
//        int firstOfHigh = gradeType.get("10");
//
//        // 一年级到高一年级
//        if ((examGradeType >= firstGrade && examGradeType < firstOfHigh) || examGradeType>=13){
//            publishFirstToFirstYearOfHighSchool(params,statIdList);
//            return;
//        }
//
//        //高二
//        int secondOfHigh=gradeType.get("11");
//        //高三
//        int thirdOfHigh=gradeType.get("12");
//
//        if (examGradeType == firstOfHigh || examGradeType == secondOfHigh || examGradeType == thirdOfHigh) {
//            publishSecondYearOrThirdYearOfHighSchool(params,statIdList);
//        }

    }

    /**
     * 教辅作业获取多个班级的报告公布状态 目前只有教辅作业支持按班级公布报告
     */
    public List<ClassStatStatusDTO> getClassExamStatStatus(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isNotBlank("classIds")
          .verify();

        Long examId = MapUtils.getLong(params, "examId");
        if (!examService.isStudyGuideHomework(examId)) {
            // 目前只有教辅作业支持按班级公布报告
            return Collections.emptyList();
        }
        List<Long> classIds = StringUtil.strToList(params.get("classIds").toString(), ",", Long.class);
        return classExamStatManager.getClassStatStatus(examId, classIds);
    }

    /**
     * 公布考试的班级报告 目前只有教辅作业支持按班级公布报告
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void updateClassExamStatStatus(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isValidId("classId")
          .isNumeric("statStatus")
          .verify();

        // 校验是否能操作
        Long examId = MapUtils.getLong(params, "examId");
        Map<String, Object> examDetail = examService.getExamDetail(MapUtil.of("examId", examId));
        if (!examService.isStudyGuideHomework(examDetail)) {
            // 目前只有教辅作业支持按班级公布报告
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "非线下作业不支持此操作！");
        }
        List<Map<String, Object>> examCourseList = examCourseService.getExamCourseList(params);
        if(examCourseList.isEmpty()){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取课程信息失败，请联系管理员处理！examId：" + examId);
        }
        Long classId = MapUtils.getLong(params, "classId");
        List<Long> classIds = Collections.singletonList(classId);
        Long teacherId = commonTeacherService.getTeacherIdByUserId(params);
        if (!ObjectUtil.isValidId(teacherId)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取用户信息失败，请联系管理员处理！userId：" + params.get("userId"));
        }
        Set<Long> teacherClassIds = schoolTeacherService.listTeacherClass(classIds,
          Collections.singletonList(MapUtils.getLong(examCourseList.get(0), "courseId")),
          Collections.singletonList(teacherId)).stream().map(ClassTeacherVO::getClassId).collect(Collectors.toSet());
        if(!teacherClassIds.contains(classId)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "不支持的操作！");
        }

        // 公布班级的报告
        Integer statStatus = MapUtils.getInteger(params, "statStatus");
        classExamStatManager.updateStatStatus(examId, Collections.singletonList(MapUtils.getLong(params, "classId")),
          statStatus, MapUtils.getLong(params, "userId"), MapUtils.getString(params, "userName"));

        int updateExamStatus = ExamStatus.InProgress.getStatus();
        int updateStatStatus = DictUtil.getDictValue("statStatus", "statClose");
        // 不管是公布还是撤销班级报告，都需要去更新一下相关数据的状态
        // 作业还没结束时，t_exam_school、t_school_exam_stat的状态只能是进行中和未公布
        if (!ObjectUtil.isValueEquals(MapUtils.getInteger(examDetail, "examStatus"), updateExamStatus)) {
            if (classExamStatManager.getOnePublishedClassExamStatId(examId) != null) {
                // 有状态是已公布的班级
                updateExamStatus = ExamStatus.Published.getStatus();
                updateStatStatus = DictUtil.getDictValue("statStatus", "statOpen");
            } else {
                updateExamStatus = ExamStatus.ToPublish.getStatus();
            }
        }
        params.put("updateExamStatus", updateExamStatus);
        params.put("updateStatStatus", updateStatStatus);
        examRepository.update("ExamMapper.updateExamStatusForStudyGuide", params);
        examRepository.update("ExamSchoolMapper.updateExamSchoolStatusForStudyGuide", params);
        examRepository.update("ExamSchoolMapper.updateExamStatStatusForStudyGuide", params);
        examRepository.update("ExamSchoolMapper.updateExamSchoolStatStatusForStudyGuide", params);
    }

    /**
     * 教辅作业有班级阅完后，自动公布作业的班级报告 目前只有教辅作业支持按班级公布报告
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void autoPublishClassExamStat(Long examId, List<Long> classIds, Long userId, String userName) {
        if (!examService.isStudyGuideHomework(examId)) {
            // 目前只有教辅作业支持按班级公布报告
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "非线下作业不支持此操作！");
        }
        if (!examService.isExamAutoPublish(examId)) {
            // 不是阅卷完成后自动公布，直接返回
            return;
        }
        if (CollectionUtils.isEmpty(classIds)) {
            return;
        }
        classExamStatManager.updateStatStatus(examId, classIds, DictUtil.getDictValue("statStatus", "statOpen"), userId, userName);
    }

    /**
     * 公布一年级到高一的考试报告，同一场考试同时只能存在一份报告为公布状态
     * @param: statIdList 要公布的报告id
     */
    private void publishFirstToFirstYearOfHighSchool(Map<String,Object> params, List<Long> statIdList){

        //检测需要公布的报告数量
        if (statIdList.size() != 1){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "一年级到高一年级只能公布一份考试报告");
        }

        //撤回该考试的所有报告
        unPublishAllSchoolExamStat(params);

        //公布指定的考试报告
        publishSchoolExamStat(params,statIdList);
    }

    /**
     * 公布指定的考试报告
     * @param: userId
     * @param: userName
     * @param: examId
     * @param: schoolId
     * @param: statIdList 考试报告id列表
     */
    private void publishSchoolExamStat(Map<String,Object> params,List<Long> statIdList){
        Map<String, Object> map = MapUtil.of(
                "userId", params.get("userId"),
                "userName", params.get("userName"),
                "examId", params.get("examId"),
                "schoolId", params.get("schoolId"),
                "currentTime", DateUtil.getCurrentDateTime(),
                "statIdList", statIdList,
                "statStatus", DictUtil.getDictValue("statStatus","statOpen" )
        );

        examRepository.update("ExamStatReportMapper.updateByExamIdAndStatIdList", map);
    }



    /**
     * 公布高二高三的考试报告
     * @desc: 对于11-12年级(高二,高三文理科因素),同一场考试在同一时刻，考试报告为公布状态的，需要<=2份
     * @param statIdList 要公布的报告id
     */
    private void publishSecondYearOrThirdYearOfHighSchool(Map<String,Object> params, List<Long> statIdList){
        long examId=Long.valueOf(params.get("examId").toString());

        if (statIdList.size()>2){
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "公布的考试报告数量不能超过两份");
        }

        //获取指定考试的所有考试报告
        Map<String, Object> examStatParam = MapUtil.of(
                "examId", examId,
                "schoolId", params.get("schoolId"),
                "isDisplay", DictUtil.getDictValue("isDisplay", "on")
        );
        List<Map<String,Object>> examStatList = examRepository.selectList("ExamStatReportMapper.getAllExamStatByExamIdList", examStatParam);

        Integer statOpen = DictUtil.getDictValue("statStatus", "statOpen");
        //已公布的考试数量
        long publishedExamStatCount = examStatList.stream()
                .filter(examStatMap -> !statIdList.contains(Long.valueOf(examStatMap.get("statId").toString()))) // 去除要公布的报告
                .filter(examStatMap -> Integer.valueOf(examStatMap.get("statStatus").toString()).equals(statOpen))
                .count();

        //已公布的+将要公布的<=2
        if (publishedExamStatCount + statIdList.size() <= 2){
            //公布报告
            publishSchoolExamStat(params, statIdList);
            return;
        }

        if (statIdList.size() == 2){

            //撤销所有报告
            unPublishAllSchoolExamStat(params);

            //公布报告
            publishSchoolExamStat(params, statIdList);
            return;
        }

        throw new CommonException(ResponseStatusEnum.DATA_ERROR,"请先撤销"+(publishedExamStatCount-statIdList.size())+"考试报告");

    }


    /**
     *  设置指定的学校考试报告为不公布
     *
     * @param: userId
     * @param: userName
     * @param: examId
     * @param: schoolId
     * @param: statIdList 多个考试报告id以","分割
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void unPublishSchoolExamStats(Map<String,Object> params){
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .isNotBlank("statIdList")
                .verify();

        //考试报告的statId列表
        List<Long> statIdList = StringUtil.strToList(params.get("statIdList").toString(), ",", Long.class);
        unPublishSchoolExamStats(params,statIdList);
    }

    /**
     * 设置指定的学校考试报告为不公布
     * @param: userId
     * @param: userName
     * @param: examId
     * @param: schoolId
     * @param: statIdList 指定的考试报告id列表
     */
    private void unPublishSchoolExamStats(Map<String,Object> params, List<Long> statIdList){

        if (CollectionUtils.isEmpty(statIdList)){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"statIdList错误");
        }
        params.put("statIdList", statIdList);
        params.put("currentTime", DateUtil.getCurrentDateTime());
        params.put("statStatus", DictUtil.getDictValue("statStatus","statClose" ));
        examRepository.update("ExamStatReportMapper.updateByExamIdAndStatIdList", params);
    }

    /**
     * 设置学校考试所有报告为不公布
     * @param: userId
     * @param: userName
     * @param: examId
     * @param: schoolId
     */
    private void unPublishAllSchoolExamStat(Map<String,Object> params){
        Map<String, Object> map = MapUtil.of(
                "userId", params.get("userId"),
                "userName", params.get("userName"),
                "examId", params.get("examId"),
                "schoolId", params.get("schoolId"),
                "currentTime", DateUtil.getCurrentDateTime(),
                "statStatus", DictUtil.getDictValue("statStatus","statClose" )
        );
        examRepository.update("ExamStatReportMapper.updateByExamIdAndStatIdList", map);
    }

    /**
     * 关闭考试的所有学校报告
     */
    public void closeExamAllSchoolStat(long examId, long userId, String userName) {
        ExamSchoolStatUpdate examSchoolStatUpdate = new ExamSchoolStatUpdate();
        examSchoolStatUpdate.setExamId(examId);
        examSchoolStatUpdate.setStatStatus(DictUtil.getDictValue("statStatus", "statClose"));
        examSchoolStatUpdate.setUserId(userId);
        examSchoolStatUpdate.setUserName(userName);
        examRepository.update("ExamStatReportMapper.updateExamAllSchoolStatStatus", examSchoolStatUpdate);
    }

    /**
     * 获取联考报告总体课程的指标信息
     * @param params examId statId
     * @return 总分+所有单科的课程的基础指标信息
     */
    public List<Map<String, Object>> getExamStatCourseIndex(Map<String,Object> params) {

        Bson query = and(eq("examId", MapUtils.getLong(params,"examId")),
          eq("statId", MapUtils.getLong(params,"statId", 0L)));
        boolean isTotal = true;
        if(ObjectUtil.isValidId(params.get("courseId"))){
            isTotal = false;
            query = and(query, eq("courseId", MapUtils.getLong(params, "courseId")));
        }
        List<String> fields = new ArrayList<>(Arrays.asList("examId","statId","participationNumber","fullMark","highestScore",
          "averageScore","standardDeviation","excellentRate","goodRate","passRate","lowScoreRate"));

        // 查询考试总分平均分
        Map<String, Object> examTotalScore = new HashMap<>();
        if(isTotal){
            examTotalScore = mongoDatabase.getCollection("examStat")
              .find(query)
              .projection(fields(include(fields), excludeId()))
              .first();
        }

        fields.add("courseId");
        fields.add("courseName");
        // 查询考试课程平均分
        List<Map<String, Object>> examCourseScoreList = mongoDatabase.getCollection("examCourseStat")
          .find(query)
          .projection(fields(include(fields), excludeId()))
          .into(new ArrayList<>());
        if(CollectionUtils.isEmpty(examCourseScoreList)){
            examCourseScoreList = new ArrayList<>();
        }
        if(MapUtils.isNotEmpty(examTotalScore)){
            examCourseScoreList.add(0, examTotalScore);
        }
        return examCourseScoreList;
    }

    /**
     * 联考各个学校的优劣势学科对比
     *
     * @param params
     * @return
     */
    public List<Document> getStatCourseContrast(Map<String, Object> params) {
        //查询参数
        Bson query = and(eq("examId", MapUtils.getLong(params,"examId")),
          eq("statId", MapUtils.getLong(params,"statId")));
        Bson schoolQuery = query;
        if(ObjectUtil.isValidId(params.get("schoolId"))){
            schoolQuery = and(query, eq("schoolId", MapUtils.getLong(params, "schoolId")));
        }

        //考试总体数据
        Document examStat = mongoDatabase.getCollection("examStat").find(and(query))
          .projection(fields(include("TScore"), excludeId()))
          .first();
        //学校数据
        List<Document> examSchoolStat = mongoDatabase.getCollection("examSchoolStat").find(schoolQuery)
          .projection(fields(include("schoolId", "schoolName", "TScore"), excludeId()))
          .into(new ArrayList<>());

        //查询单个科目，不查综合科目数据
        query = and(query, eq("memberCount", 1));
        schoolQuery = and(schoolQuery, eq("memberCount", 1));
        //考试课程数据
        List<Document> examCourseStat = mongoDatabase.getCollection("examCourseStat").find(query)
          .projection(fields(include("courseId", "courseName", "TScore"), excludeId()))
          .into(new ArrayList<>());
        //学校科目数据
        Map<Long, List<Document>> examSchoolCourseStatMap = mongoDatabase.getCollection("examSchoolCourseStat").find(schoolQuery)
          .projection(fields(include("schoolId", "schoolName", "courseId", "courseName", "TScore"), excludeId()))
          .into(new ArrayList<>()).stream().collect(Collectors.groupingBy(x -> MapUtils.getLong(x,"schoolId")));

        if(MapUtils.isNotEmpty(examStat) && CollectionUtils.isNotEmpty(examCourseStat)){
            examStat.put("course",examCourseStat);
        }
        examSchoolStat.forEach(x -> {
            List<Document> schoolCourseStat = examSchoolCourseStatMap.getOrDefault(MapUtils.getLong(x, "schoolId"), new ArrayList<>());
            schoolCourseStat.forEach(y -> y.put("TScore", ObjectUtil.isBlank(y.get("TScore")) ? null : MapUtils.getDouble(y, "TScore")));
            x.put("course", schoolCourseStat);
            List<Document> list = schoolCourseStat.stream().filter(y -> y.get("TScore") != null)
              .sorted(Comparator.comparing(y -> y.get("TScore").toString())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(list)){
                x.put("advancedCourseName", list.get(list.size() - 1).get("courseName").toString());
                x.put("disadvantageCourseName", list.get(0).get("courseName").toString());
            }
        });
        if(CollectionUtils.isNotEmpty(examSchoolStat)){
            //以前的考试报告，examStat 和 examCourseStat 中都是没有T分数的数据的
            if (MapUtils.isNotEmpty(examStat)) {
                examSchoolStat.add(0, examStat);
            }
            return examSchoolStat;
        }
        return new ArrayList<>();
    }

    /**
     * 获取学校各科均分排名
     *
     * @param params examId statId
     * @return
     */
    public List<Map<String, Object>> getBaseIndex(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .verify();

        ExamCourseVisibility examCourseVisibility = displayExamService.getUserExamCourseVisibility(params);
        if (examCourseVisibility.isTotalScoreInvisible()) {
            //总分不可见时，直接返回
            return Collections.emptyList();
        }

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("statId", Long.valueOf(params.get("statId").toString())));

        Set<String> examIncludeFields = Sets.newHashSet("courseId", "courseName", "averageScore", "averageScoreRanking",
                "ranking", "passRate", "highestScore", "participationNumber", "allPassRate");
        Set<String> schoolIncludeFields = Sets.newHashSet("schoolId", "schoolName", "courseId", "courseName", "averageScore",
                "averageScoreRanking", "ranking", "passRate", "highestScore", "participationNumber", "allPassRate");

        // 参数设置配置了均分排名不可见-去除关于排名的字段
        Set<String> hiddenIndex = new HashSet<>(examConfigService.getHiddenIndex(params));
        if (hiddenIndex.contains("averageScoreRanking")) {
            examIncludeFields.remove("averageScoreRanking");
            examIncludeFields.remove("ranking");
            schoolIncludeFields.remove("averageScoreRanking");
            schoolIncludeFields.remove("ranking");
        }

        // 查询考试课程平均分
        List<Map<String, Object>> examCourseScoreList = mongoDatabase.getCollection("examCourseStat")
          .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
          .projection(fields(include(examIncludeFields.toArray(new String[0])),
            excludeId()))
          .into(new ArrayList<>());

        // 查询考试总分平均分
        Map<String, Object> examTotalScore = mongoDatabase.getCollection("examStat")
          .find(query)
          .projection(fields(include(examIncludeFields.toArray(new String[0])), excludeId()))
          .sort(ascending("averageScoreRanking"))
          .first();

        //联考进入联考报告选择单校进行导出时，会传schoolId，不知道直接用schoolId会不会影响到其他地方，所以改用schoolIdOfUnion
        if(ObjectUtil.isNotBlank(params.get("schoolIdOfUnion"))){
            query = and(query, eq("schoolId", MapUtils.getLong(params, "schoolIdOfUnion")));
        }
        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());

        // 查询学校课程平均分
        List<Map<String, Object>> schoolCourseScoreList = mongoDatabase.getCollection("examSchoolCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
                .projection(fields(include(schoolIncludeFields.toArray(new String[0])), excludeId()))
                .into(new ArrayList<>());

        // 查询学校总分平均分
        List<Map<String, Object>> schoolTotalScoreList = mongoDatabase.getCollection("examSchoolStat")
                .find(query)
                .projection(fields(include(schoolIncludeFields.toArray(new String[0])), excludeId()))
                .sort(ascending("averageScoreRanking"))
                .into(new ArrayList<>());

        Map<Long, List<Map<String, Object>>> schoolCourseGroup = schoolCourseScoreList.stream()
                .collect(groupingBy(item -> Long.valueOf(item.get("schoolId").toString())));


        // 组装数据
        List<Map<String, Object>> result = new ArrayList<>();
        if(examTotalScore == null){
            throw new DongniException(ErrorCode.USER_EXCEPTION, "考试不存在或考试报告还未生成，请稍后重试");
        }
        examTotalScore.put("course", examCourseScoreList);
        schoolTotalScoreList.forEach(item -> {
            item.put("course", schoolCourseGroup.getOrDefault(Long.valueOf(item.get("schoolId").toString()), Collections.emptyList()));
        });
        examSchoolCustomSortService.sort(schoolTotalScoreList, examId);

        result.add(examTotalScore);
        result.addAll(schoolTotalScoreList);

        return result;
    }

    /**
     * 获取学校各科均分排名
     *
     * @param params examId statId
     * @return
     */
    public Map<String, Object> getClassBaseIndex(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .verify();

        ExamCourseVisibility examCourseVisibility = displayExamService.getUserExamCourseVisibility(params);
        if (examCourseVisibility.isTotalScoreInvisible()) {
            //总分不可见时，直接返回
            return MapUtil.of("totalCount", 0, "list", Collections.emptyList());
        }

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("statId", Long.valueOf(params.get("statId").toString())));
        query = examConfigService.fillVisibleSchoolAndClass(params, query, examId, StatDataDimensionEnum.CLASS.getValue());

        MongoCollection<Document> examClassStat = mongoDatabase.getCollection("examClassStat");
        Map<String, Object> rs = new HashMap<>();
        long totalCount = examClassStat.countDocuments(query);
        rs.put("totalCount", totalCount);
        List<Document> list = new ArrayList<>();
        if (totalCount == 0) {
            rs.put("list", list);
            return rs;
        }

        Set<String> classIncludeFields = Stream.of("classId", "className","schoolId", "schoolName","resultStatus", "courseId",
                        "courseName", "averageScore","averageScoreRanking", "ranking")
                .collect(Collectors.toSet());
        Set<String> examIncludeFields = Stream.of("courseId", "courseName", "averageScore","averageScoreRanking", "ranking")
              .collect(Collectors.toSet());
        // 参数设置配置了均分排名不可见-去除关于排名的字段
        Set<String> hiddenIndex = new HashSet<>(examConfigService.getHiddenIndex(params));
        if (hiddenIndex.contains("averageScoreRanking")) {
            examIncludeFields.remove("averageScoreRanking");
            examIncludeFields.remove("ranking");
            classIncludeFields.remove("averageScoreRanking");
            classIncludeFields.remove("ranking");
        }

        //查询班级
        List<Document> examClass = examClassStat.find(query)
                .projection(fields(include(classIncludeFields.toArray(new String[0])), excludeId()))
                .into(new ArrayList<>());
        list = examClass;
        //分页
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            list = examClass.stream().skip(MapUtil.getLong(params, "currentIndex")).limit(MapUtil.getLong(params, "pageSize"))
                    .collect(Collectors.toList());
        }
        examSchoolCustomSortService.sort(list, examId);

        List<Long> classIds = new ArrayList<>();
        list.forEach(l->classIds.add(Long.valueOf(l.get("classId").toString())));
        query= and(query,in("classId",classIds));
        // 查询学校课程平均分
        List<Map<String, Object>> classCourseScoreList = mongoDatabase.getCollection("examClassCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
                .projection(fields(include(classIncludeFields.toArray(new String[0])), excludeId()))
                .into(new ArrayList<>());
        Map<Long, List<Map<String, Object>>> classCourseList = classCourseScoreList.stream().collect(groupingBy(c -> Long.valueOf(c.get("classId").toString())));
        list.forEach(l->{
            List<Map<String, Object>> course = classCourseList.getOrDefault(l.getLong("classId"), Collections.emptyList());
            course.sort(Comparator.comparing(c->Long.valueOf(c.get("courseId").toString())));
            l.put("course",course);
        });

        //查询联考
        // 查询考试课程平均分
        query = and(eq("examId", Long.valueOf(params.get("examId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())));
        List<Map<String, Object>> examCourseScoreList = mongoDatabase.getCollection("examCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
                .projection(fields(include(examIncludeFields.toArray(new String[0])), excludeId()))
                .into(new ArrayList<>());

        // 查询考试总分平均分
        Document examTotalScore = mongoDatabase.getCollection("examStat")
                .find(query)
                .projection(fields(include(examIncludeFields.toArray(new String[0])), excludeId()))
                .sort(ascending("averageScoreRanking"))
                .first();
        examTotalScore.put("course", examCourseScoreList);

        list.add(0,examTotalScore);

        rs.put("list",list);
        return rs;
    }

    /**
     * 获取考试得分率
     *
     * @param params examId artsScience
     * @return 考试得分率
     */
    public List<Document> getScoreRate(Map<String, Object> params) {

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        List<Bson> p = new ArrayList<>();
        p.add(eq("examId", examId));
        p.add(eq("statId", ObjectUtil.isValidId(params.get("statId")) ? Long.valueOf(params.get("statId").toString()) : 0L));

        // 查询字段
        List<String> fields = new ArrayList<>(Arrays.asList(
                "schoolId", "schoolName", "totalStudent","participationNumber","participationRate",
                "highestScore", "lowestScore","beforeAverageScore",
                "afterAverageScore","averageScore","averageSubtraction",
                "averageSubtractionRate", "avgRate", "averageScoreRanking", "excellentRate",
                "excellentNumber","excellentRateRanking","goodRate","goodNumber",
                "goodRateRanking","passRate", "passNumber","passRateRanking","lowScoreRate",
                "lowScoreNumber","lowScoreRateRanking","standardDeviation", "balanceRate",
                "fullMark", "quartileHighestScore", "quartileLowestScore", "range", "medianValue", "upperQuartile", "lowerQuartile"));

        //移除隐藏字段
        List<String> hiddenIndex = examConfigService.getHiddenIndex(params);
        if(CollectionUtils.isNotEmpty(hiddenIndex)){
            for(String field : hiddenIndex){
                fields.remove(field);
            }
        }

        Document examStat = mongoDatabase.getCollection("examStat")
                .find(and(p))
                .projection(fields(include(fields), excludeId())).first();

        if (examStat == null) {
            return null;
        }

        // 获取学校的总分得分率
        if (ObjectUtil.isValidId(params.get("schoolId"))) {
            p.add(eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        }
        p = examConfigService.fillVisibleSchool(params, p, examId, StatDataDimensionEnum.SCHOOL.getValue());

        List<Document> ls = mongoDatabase.getCollection("examSchoolStat")
                .find(and(p))
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(ls)) {
            return Arrays.asList(examStat);
        }

        examSchoolCustomSortService.sort(ls, examId);
        ls.add(0, examStat);

        return ls;
    }

    /**
     * 获取考试得分率 班级
     *
     * @param params examId
     * @return 考试得分率 班级
     */
    public Map<String,Object> getClassScoreRate(Map<String, Object> params) {

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .verify();
        Long examId = MapUtils.getLong(params, "examId");
        List<Bson> p = new ArrayList<>();
        p.add(eq("examId", examId));
        p.add(eq("statId", ObjectUtil.isValidId(params.get("statId")) ? Long.valueOf(params.get("statId").toString()) : 0L));

        // 查询字段
        List<String> fields = new ArrayList<>(Arrays.asList(
                "schoolId","schoolName", "classId", "classSort", "className","totalStudent","participationNumber","participationRate",
                "highestScore", "lowestScore","beforeAverageScore","afterAverageScore",
                "averageScore","averageSubtraction","averageSubtractionRate", "avgRate","averageScoreRanking",
                "excellentRate","excellentNumber","excellentRateRanking","goodRate","goodNumber",
                "goodRateRanking","passRate", "passNumber","passRateRanking","lowScoreRate","lowScoreNumber",
                "lowScoreRateRanking", "balanceRate"));

        Map<String, Object> rs = new HashMap<>();
        //移除隐藏字段
        List<String> hiddenIndex = examConfigService.getHiddenIndex(params);
        if(CollectionUtils.isNotEmpty(hiddenIndex)){
            for(String field : hiddenIndex){
                fields.remove(field);
                if(field.equals("beforeAfterAverageSubtraction")){
                    rs.put("前后均差",field);
                }
            }
        }

        //联考总分数据
        Document examStat = mongoDatabase.getCollection("examStat")
          .find(and(p))
          .projection(fields(include(fields), excludeId()))
          .first();
        if (examStat == null) {
            return null;
        }

        MongoCollection<Document> examClassStat = mongoDatabase.getCollection("examClassStat");

        p = examConfigService.fillVisibleSchoolAndClass(params, p, examId, StatDataDimensionEnum.CLASS.getValue());
        long totalCount = examClassStat.countDocuments(and(p));
        rs.put("totalCount", totalCount);
        List<Document> list = new ArrayList<>();
        if (totalCount == 0) {
            rs.put("list", list);
            return rs;
        }


        List<Document> examClass = examClassStat.find(and(p))
                .projection(fields(include(fields), excludeId()))
                        .into(new ArrayList<>());
        examClass.sort(examSchoolCustomSortService.getExamSchoolCustomSortComparator(examClass, examId)
                .thenComparing(s -> Long.valueOf(s.getOrDefault("classSort", 0).toString())));
        //分页
        list = examClass;
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            list = examClass.stream()
                    .skip(MapUtil.getLong(params, "currentIndex"))
                    .limit(MapUtil.getLong(params, "pageSize"))
                    .collect(Collectors.toList());
        }

        list.add(0,examStat);
        rs.put("list",list);
        return rs;
    }

    /**
     * 获取考试得分分布
     *
     * @param params examId artsScience
     * @return 考试得分分布
     */
    public List<Document> getScoreSection(Map<String, Object> params) {

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        List<Bson> p = new ArrayList<>();
        p.add(eq("examId", examId));
        p.add(eq("statId", ObjectUtil.isValidId(params.get("statId")) ? Long.valueOf(params.get("statId").toString()) : 0L));

        String[] queryFields = new String[]{
                "schoolId", "schoolName", "scoreSection","resultStatus", "totalStudent", "participationNumber", "fullMark"
        };

        Document examStat = mongoDatabase.getCollection("examStat")
                .find(and(p))
                .projection(fields(include(queryFields), excludeId()))
                .first();

        if (examStat == null) {
            return null;
        }

        // 获取学校的得分分布
        if (ObjectUtil.isValidId(params.get("schoolId"))) {
            p.add(eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        }
        p = examConfigService.fillVisibleSchool(params, p, examId, StatDataDimensionEnum.SCHOOL.getValue());

        List<Document> ls = mongoDatabase.getCollection("examSchoolStat")
                .find(and(p))
                .projection(fields(include(queryFields), excludeId()))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(ls)) {
            return Arrays.asList(examStat);
        }

        examSchoolCustomSortService.sort(ls, examId);
        ls.add(0, examStat);

        return ls;
    }

    /**
     * 获取考试得分分布
     *
     * @param params examId artsScience
     * @return 考试得分分布
     */
    public Map<String,Object> getClassScoreSection(Map<String, Object> params) {

        // 参数校验
        Verify.of(params)
                .isValidId("examId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        List<Bson> p = new ArrayList<>();
        p.add(eq("examId", examId));
        p.add(eq("statId", ObjectUtil.isValidId(params.get("statId")) ? Long.valueOf(params.get("statId").toString()) : 0L));

        String[] fields = new String[]{
                "classId", "className","classSort","schoolId", "schoolName", "resultStatus", "scoreSection","resultStatus", "totalStudent", "participationNumber", "fullMark"
        };

        //获取联考总体班级
        Document examStat = mongoDatabase.getCollection("examStat")
          .find(and(p))
          .projection(fields(include(fields), excludeId()))
          .first();
        if (examStat == null) {
            return null;
        }

        p = examConfigService.fillVisibleSchoolAndClass(params, p, examId, StatDataDimensionEnum.CLASS.getValue());
        MongoCollection<Document> examClassStat = mongoDatabase.getCollection("examClassStat");
        Map<String, Object> rs = new HashMap<>();
        long totalCount = examClassStat.countDocuments(and(p));
        rs.put("totalCount", totalCount);
        List<Document> list = new ArrayList<>();
        if (totalCount == 0) {
            rs.put("list", list);
            return rs;
        }
        List<Document> examClass = examClassStat.find(and(p))
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());
        examClass.sort(examSchoolCustomSortService.getExamSchoolCustomSortComparator(examClass, examId)
                .thenComparing(s -> Long.valueOf(s.getOrDefault("classSort", 0).toString())));
        list = examClass;
        //分页
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            list = examClass.stream().skip(MapUtil.getLong(params, "currentIndex")).limit(MapUtil.getLong(params, "pageSize"))
                    .collect(Collectors.toList());
        }

        list.add(0,examStat);
        rs.put("list",list);
        return rs;
    }


    /**
     * 获取各学校各科最高分人数
     *
     * @param params examId statId
     * @return
     */
    public List<Map<String, Object>> getSchoolCourseTop(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .verify();

        ExamCourseVisibility examCourseVisibility = displayExamService.getUnionStatCourseVisibility(params);
        if (examCourseVisibility.isTotalScoreInvisible()) {
            return Collections.emptyList();
        }
        Bson query = and(eq("examId", Long.valueOf(params.get("examId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())));

        // 查询总分最高分
        Map<String, Object> totalHighestStudent = mongoDatabase.getCollection("examStat")
                .find(query)
                .projection(fields(include("highestScore", "highestStudent"), excludeId()))
                .first();

        List<Map<String, Object>> resultTotalHighestStudentList = getHighestStudent(totalHighestStudent);
        totalHighestStudent.put("highestStudent", resultTotalHighestStudentList);

        // 查询课程最高分
        List<Map<String, Object>> courseHighestStudent = mongoDatabase.getCollection("examCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
                .projection(fields(include("courseId", "courseName", "highestScore", "highestStudent"), excludeId()))
                .into(new ArrayList<>());

        courseHighestStudent.forEach(item -> item.put("highestStudent", getHighestStudent(item)));

        List<Map<String, Object>> result = new ArrayList<>();
        result.add(totalHighestStudent);
        result.addAll(courseHighestStudent);

        return result;

    }

    /**
     * 获取最高分学生
     *
     * @param params
     * @return
     */
    private List<Map<String, Object>> getHighestStudent(Map<String, Object> params) {
        List<Map<String, Object>> totalHighestStudentList = MapUtil.getListMap(params, "highestStudent");
        Map<Long, List<Map<String, Object>>> schoolTotalGroup = totalHighestStudentList.stream()
                .collect(groupingBy(item -> Long.valueOf(item.get("schoolId").toString())));
        List<Map<String, Object>> resultTotalHighestStudentList = new ArrayList<>();
        schoolTotalGroup.forEach((schoolId, items) -> {
            Map<String, Object> resultItem = new HashMap<>();
            resultItem.put("schoolId", schoolId);
            resultItem.put("schoolName", items.get(0).get("schoolName"));
            resultItem.put("students", items.stream().map(x -> MapUtil.copy(x, "schoolId", "schoolName")).collect(Collectors.toList()));
            resultTotalHighestStudentList.add(resultItem);
        });
        return resultTotalHighestStudentList;
    }

    /**
     * 获取前后N名分布
     *
     * @param params examId statId [courseId]
     * @return
     */
    public List<Map<String, Object>> getBeforeAndAfterRank(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .verify();

        if (ObjectUtil.isBlank(params.get("courseId"))) {
            // 查询总分
            return getBeforeAndAfterRankByTotal(params);

        } else {
            // 查询课程
            return getBeforeAndAfterRankByCourse(params);

        }
    }

    /**
     * 获取前后N名分布
     *
     * @param params examId statId [courseId]
     * @return
     */
    public Map<String, Object> getClassBeforeAndAfterRank(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isNumeric("statId")
                .verify();

        if (ObjectUtil.isBlank(params.get("courseId"))) {
            // 查询总分
            return getClassBeforeAndAfterRankByTotal(params);

        } else {
            // 查询课程
            return getClassBeforeAndAfterRankByCourse(params);

        }
    }

    /**
     * 查询课程的前后N名分布
     *
     * @param params examId statId courseId
     * @return
     */
    private List<Map<String,Object>> getBeforeAndAfterRankByCourse(Map<String,Object> params) {

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", Long.valueOf(params.get("examId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())),
                eq("courseId", Long.valueOf(params.get("courseId").toString())));

        Document examStat = mongoDatabase.getCollection("examCourseStat")
                .find(query)
                .projection(fields(include("schoolId", "schoolName", "beforeUnionRank", "afterUnionRank","proportionBeforeRank",
                  "proportionAfterRank", "participationNumber"), excludeId()))
                .first();

        if (examStat == null) {
            return null;
        }

        //联考进入联考报告选择单校进行导出时，会传schoolId，不知道直接用schoolId会不会影响到其他地方，所以改用schoolIdOfUnion
        if(ObjectUtil.isNotBlank(params.get("schoolIdOfUnion"))){
            query = and(query, eq("schoolId", MapUtils.getLong(params, "schoolIdOfUnion")));
        }
        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());
        List<Map<String, Object>> ls = mongoDatabase.getCollection("examSchoolCourseStat")
                .find(query)
                .projection(fields(include("schoolId", "schoolName", "beforeUnionRank", "afterUnionRank","proportionBeforeRank",
                  "proportionAfterRank", "participationNumber", "resultStatus"), excludeId()))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(ls)) {
            return Arrays.asList(examStat);
        }

        examSchoolCustomSortService.sort(ls, examId);
        ls.add(0, examStat);

        return ls;

    }

    /**
     * 查询总分前后N名分布
     *
     * @param params examId statId
     * @return
     */
    private List<Map<String,Object>> getBeforeAndAfterRankByTotal(Map<String,Object> params) {

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("statId", Long.valueOf(params.get("statId").toString())));

        Document examStat = mongoDatabase.getCollection("examStat")
                .find(query)
                .projection(fields(include("schoolId", "schoolName", "beforeUnionRank", "afterUnionRank","proportionBeforeRank",
                  "proportionAfterRank", "participationNumber"), excludeId()))
                .first();

        if (examStat == null) {
            return null;
        }

        //联考进入联考报告选择单校进行导出时，会传schoolId，不知道直接用schoolId会不会影响到其他地方，所以改用schoolIdOfUnion
        if(ObjectUtil.isNotBlank(params.get("schoolIdOfUnion"))){
            query = and(query, eq("schoolId", MapUtils.getLong(params, "schoolIdOfUnion")));
        }
        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());
        List<Map<String, Object>> ls = mongoDatabase.getCollection("examSchoolStat")
                .find(query)
                .projection(fields(include("schoolId", "schoolName", "beforeUnionRank", "afterUnionRank","proportionBeforeRank",
                  "proportionAfterRank", "participationNumber", "resultStatus"), excludeId()))
                .into(new ArrayList<>());

        if (CollectionUtils.isEmpty(ls)) {
            return Arrays.asList(examStat);
        }

        examSchoolCustomSortService.sort(ls, examId);
        ls.add(0, examStat);

        return ls;
    }

    /**
     * 查询总分前后N名分布
     *
     * @param params examId statId
     * @return
     */
    private Map<String,Object> getClassBeforeAndAfterRankByTotal(Map<String,Object> params) {

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("statId", Long.valueOf(params.get("statId").toString())));

        // 查询字段
        List<String> fields = new ArrayList<>(Arrays.asList(
                "classId", "classSort", "className","schoolId", "schoolName", "resultStatus","beforeUnionRank", "afterUnionRank","proportionBeforeRank","proportionAfterRank"));

        //获取班级联考总体
        Document examStat = mongoDatabase.getCollection("examStat")
          .find(and(query))
          .projection(fields(include(fields), excludeId())).first();
        if (examStat == null) {
            return null;
        }

        query = examConfigService.fillVisibleSchoolAndClass(params, query, examId, StatDataDimensionEnum.CLASS.getValue());
        MongoCollection<Document> examClassStat = mongoDatabase.getCollection("examClassStat");
        Map<String, Object> rs = new HashMap<>();
        long totalCount = examClassStat.countDocuments(query);
        rs.put("totalCount", totalCount);
        List<Document> list = new ArrayList<>();
        if (totalCount == 0) {
            rs.put("list", list);
            return rs;
        }

        List<Document> examClass = examClassStat.find(query)
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());
        list = examClass;
        //分页
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            list = examClass.stream().skip(MapUtil.getLong(params, "currentIndex")).limit(MapUtil.getLong(params, "pageSize"))
                    .collect(Collectors.toList());
        }
        list.sort(examSchoolCustomSortService.getExamSchoolCustomSortComparator(list, examId)
                .thenComparing(s -> Long.valueOf(s.getOrDefault("classSort", 0).toString())));
        list.add(0,examStat);
        rs.put("list",list);
        return rs;
    }

    /**
     * 查询总分前后N名分布
     *
     * @param params examId statId
     * @return
     */
    private Map<String,Object> getClassBeforeAndAfterRankByCourse(Map<String,Object> params) {

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("statId", Long.valueOf(params.get("statId").toString())),
                eq("courseId", Long.valueOf(params.get("courseId").toString())));

        // 查询字段
        List<String> fields = new ArrayList<>(Arrays.asList(
                "classId","classSort", "className","schoolId", "schoolName", "resultStatus","courseId", "courseName", "beforeUnionRank", "afterUnionRank","proportionBeforeRank","proportionAfterRank"));

        //获取联考班级
        Document examStat = mongoDatabase.getCollection("examCourseStat")
          .find(and(query))
          .projection(fields(include(fields), excludeId())).first();
        if (examStat == null) {
            return null;
        }

        query = examConfigService.fillVisibleSchoolAndClass(params, query, examId, StatDataDimensionEnum.CLASS.getValue());
        MongoCollection<Document> examClassStat = mongoDatabase.getCollection("examClassCourseStat");
        Map<String, Object> rs = new HashMap<>();
        long totalCount = examClassStat.countDocuments(query);
        rs.put("totalCount", totalCount);
        List<Document> list = new ArrayList<>();
        if (totalCount == 0) {
            rs.put("list", list);
            return rs;
        }
        List<Document> examClass = examClassStat.find(query)
                .projection(fields(include(fields), excludeId()))
                .into(new ArrayList<>());
        list = examClass;
        //分页
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            list = examClass.stream().skip(MapUtil.getLong(params, "currentIndex")).limit(MapUtil.getLong(params, "pageSize"))
                    .collect(Collectors.toList());
        }
        list.sort(examSchoolCustomSortService.getExamSchoolCustomSortComparator(list, examId)
                .thenComparing(s -> Long.valueOf(s.getOrDefault("classSort", 0).toString())));
        list.add(0,examStat);
        rs.put("list",list);
        return rs;
    }

    /**
     * 获取题型均分
     *
     * @param params examId paperId statId
     * @return
     */
    public List<Map<String, Object>> getQuestionTypeAvg(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNumeric("statId")
                .isNotBlank("courseId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("paperId", Long.valueOf(params.get("paperId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())),
                eq("courseId",Long.valueOf(params.get("courseId").toString())));

        // 查询联考
        Map<String, Object> examPaperStat = mongoDatabase.getCollection("examPaperStat")
                .find(query)
                .projection(fields(include("averageScore", "averageRate", "questionType"), excludeId()))
                .first();

        // 记录联考总体每一道题的得分率
        Map<String, Object> allScoreRateMap = new HashMap<>();
        if (MapUtils.isNotEmpty(examPaperStat)) {
            // 总分的得分率
            allScoreRateMap.put("0", examPaperStat.get("averageRate"));
            // 各题型的得分率
            List<Map<String, Object>> questionTypeList = (List<Map<String, Object>>) examPaperStat.get("questionType");
            if (CollectionUtils.isNotEmpty(questionTypeList)) {
                for (Map<String, Object> item : questionTypeList) {
                    allScoreRateMap.put(item.get("questionTypeName").toString(), item.get("scoreRate"));
                }
            }
        }

        // 查询学校
        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());
        List<Map<String, Object>> examSchoolPaperStatList = mongoDatabase.getCollection("examSchoolPaperStat")
                .find(query)
                .projection(fields(include("schoolId", "schoolName", "averageScore", "averageRate", "questionType"), excludeId()))
                .into(new ArrayList<>());


        if (CollectionUtils.isEmpty(examSchoolPaperStatList)) {
            return Collections.emptyList();
        }

        if (MapUtils.isNotEmpty(examPaperStat)){
            List<String> questionTypeNameList = ((List<Map<String, Object>>) examPaperStat.get("questionType")).stream()
                    .map(a -> a.get("questionTypeName").toString()).collect(Collectors.toList());

            examSchoolPaperStatList.forEach(question->{
                Map<String, Map<String, Object>> questionTypeNameMap = ((List<Map<String, Object>>) question.get("questionType")).stream()
                        .collect(Collectors.toMap(a -> a.get("questionTypeName").toString(), Function.identity()));

                List<Map<String, Object>> questionList = new ArrayList<>();
                questionTypeNameList.forEach(questionTypeName->{
                    if (questionTypeNameMap.get(questionTypeName) != null) {
                        Map<String, Object> map = questionTypeNameMap.get(questionTypeName);
                        // 计算学校每一题型与联考总体的得分率差
                        if (MapUtils.isNotEmpty(allScoreRateMap)) {
                            String allScoreRate = allScoreRateMap.get(questionTypeName).toString();
                            String schoolScoreRate = map.get("scoreRate").toString();
                            DifferenceRateUtil.computeDifferenceRate(map, schoolScoreRate, allScoreRate);
                        }
                        questionList.add(map);
                    }
                });
                // 计算学校总分与联考总体的得分率差
                if (MapUtils.isNotEmpty(allScoreRateMap)) {
                    String allScoreRate = allScoreRateMap.get("0").toString();
                    String schoolScoreRate = question.get("averageRate").toString();
                    DifferenceRateUtil.computeDifferenceRate(question, schoolScoreRate, allScoreRate);
                }
                question.put("questionType", questionList);
            });
            examSchoolCustomSortService.sort(examSchoolPaperStatList, examId);
            examSchoolPaperStatList.add(0, examPaperStat);
        }
        return examSchoolPaperStatList;
    }

    /**
     * 获取题型均分
     *
     * @param params examId paperId statId
     *               [schoolId](得分率图表只能显示某个学校的班级对比)
     * @return
     */
    public Map<String, Object> getClassQuestionTypeAvg(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNumeric("statId")
                .isNotBlank("courseId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("paperId", Long.valueOf(params.get("paperId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())),
                eq("courseId",Long.valueOf(params.get("courseId").toString())));

        // 查询联考
        Document examPaperStat = mongoDatabase.getCollection("examPaperStat")
                .find(query)
                .projection(fields(include("averageScore", "averageRate", "questionType"), excludeId()))
                .first();

        // 记录联考总体每一道题的得分率
        Map<String, Object> allScoreRateMap = new HashMap<>();
        if (MapUtils.isNotEmpty(examPaperStat)) {
            // 总分的得分率
            allScoreRateMap.put("0", examPaperStat.get("averageRate"));
            // 各题型的得分率
            List<Map<String, Object>> questionTypeList = (List<Map<String, Object>>) examPaperStat.get("questionType");
            if (CollectionUtils.isNotEmpty(questionTypeList)) {
                for (Map<String, Object> item : questionTypeList) {
                    allScoreRateMap.put(item.get("questionType").toString(), item.get("scoreRate"));
                }
            }
        }

        // 如果有携带schoolId的话
        if (ObjectUtil.isValidId(params.get("schoolId"))) {
            query = and(query, eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        }
        params.put("isUnionStat", true);
        query = examConfigService.fillVisibleSchoolAndClass(params, query, examId, StatDataDimensionEnum.CLASS.getValue());
        MongoCollection<Document> examClassStat = mongoDatabase.getCollection("examClassPaperStat");
        Map<String, Object> rs = new HashMap<>();
        long totalCount = examClassStat.countDocuments(query);
        rs.put("totalCount", totalCount);
        List<Document> list = new ArrayList<>();
        if (totalCount == 0) {
            rs.put("list", list);
            return rs;
        }
        List<Document> examClass = examClassStat.find(query)
                .projection(fields(include("classId", "classSort","className","schoolId", "schoolName","resultStatus", "averageScore", "averageRate", "questionType"), excludeId()))
                .into(new ArrayList<>());
        examClass.sort(examSchoolCustomSortService.getExamSchoolCustomSortComparator(examClass, examId)
                .thenComparing(s -> Long.valueOf(s.getOrDefault("classSort", 0).toString())));
        list = examClass;
        //分页
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            list = examClass.stream().skip(MapUtil.getLong(params, "currentIndex")).limit(MapUtil.getLong(params, "pageSize"))
                    .collect(Collectors.toList());
        }


        // 计算与联考总体的得分率差值
        list.forEach(item -> {
            // 有些班级没有参加考试无需计算
            if (item.get("averageRate") != null) {
                // 先计算总分的
                String totalScoreClassAverageRate = item.get("averageRate").toString();
                if (allScoreRateMap.containsKey("0")) {
                    String totalScoreAverageRate = allScoreRateMap.get("0").toString();
                    DifferenceRateUtil.computeDifferenceRate(item, totalScoreClassAverageRate, totalScoreAverageRate);
                }

                // 计算每一题型的
                List<Map<String, Object>> questionTypeList = (List<Map<String, Object>>) item.get("questionType");
                if (CollectionUtils.isNotEmpty(questionTypeList)) {
                    for (Map<String, Object> questionType : questionTypeList) {
                        String type = questionType.get("questionType").toString();
                        if (allScoreRateMap.containsKey(type)) {
                            String classScoreRate = questionType.get("scoreRate").toString();
                            String allScoreRate = allScoreRateMap.get(type).toString();
                            DifferenceRateUtil.computeDifferenceRate(questionType, classScoreRate, allScoreRate);
                        }
                    }
                }
            }
        });

        if(examPaperStat!=null){
            list.add(0,examPaperStat);
        }
        rs.put("list",list);
        return rs;

    }

    /**
     * 获取小题均分
     *
     * @param params examId paperId statId
     * @return
     */
    public List<Map<String, Object>> getQuestionAvg(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNumeric("statId")
                .isNotBlank("courseId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("paperId", Long.valueOf(params.get("paperId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())),
                eq("courseId",Long.valueOf(params.get("courseId").toString())));

        // 查询联考总分、主客观题均分
        Map<String, Object> examPaperStat = mongoDatabase.getCollection("examPaperStat")
                .find(query)
                .projection(fields(include("averageScore", "averageRate", "objectiveAverageScore", "objectiveScoreRate",
                        "subjectiveAverageScore", "subjectiveScoreRate","participationNumber"), excludeId()))
                .first();

        // 查询联考小题均分
        List<Map<String, Object>> examQuestionStatList = mongoDatabase.getCollection("examQuestionStat")
                .find(query)
                .projection(fields(include("questionNumber", "structureNumber", "averageScore", "averageRate", "point"), excludeId()))
                .sort(ascending("questionNumber"))
                .into(new ArrayList<>());


        // 记录联考总体每一道题目的得分率(这里不要求显示总分的得分率，不记录总分的)
        Map<String, String> allScoreRateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(examQuestionStatList)) {
            for (Map<String, Object> item : examQuestionStatList) {
                String questionNumber = item.get("questionNumber").toString();
                String averageRate = item.get("averageRate").toString();
                allScoreRateMap.put(questionNumber, averageRate);

                // 如果存在point
                if (item.containsKey("point")) {
                    List<Map<String, Object>> pointList = (List<Map<String, Object>>) item.get("point");
                    for (Map<String, Object> point : pointList) {
                        allScoreRateMap.put(questionNumber + "-" + point.get("index"), point.get("averageRate").toString());
                    }
                }
            }
        }

        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());
        // 查询学校总分、主客观题均分
        List<Map<String, Object>> examSchoolPaperStatList = mongoDatabase.getCollection("examSchoolPaperStat")
                .find(query)
                .projection(fields(include("schoolId", "schoolName", "averageScore", "averageRate", "objectiveAverageScore", "objectiveScoreRate",
                        "subjectiveAverageScore", "subjectiveScoreRate","participationNumber"), excludeId()))
                .into(new ArrayList<>());

        // 查询学校小题均分
        List<Map<String, Object>> examSchoolQuestionStatList = mongoDatabase.getCollection("examSchoolQuestionStat")
                .find(query)
                .projection(fields(include("schoolId", "schoolName", "questionNumber",
                        "structureNumber", "averageScore", "averageRate", "point"), excludeId()))
                .sort(ascending("questionNumber"))
                .into(new ArrayList<>());

        Map<Long, List<Map<String, Object>>> schoolQuestionGroup = examSchoolQuestionStatList.stream()
                .collect(groupingBy(item -> Long.valueOf(item.get("schoolId").toString())));

        examSchoolPaperStatList.forEach(item -> {
            List<Map<String, Object>> list = schoolQuestionGroup.get(Long.valueOf(item.get("schoolId").toString()));
            // 为每一道题计算得分率差值
            if (MapUtils.isNotEmpty(allScoreRateMap)) {
                for (Map<String, Object> i : list) {
                    String questionNumber = i.get("questionNumber").toString();
                    String allScoreRate = allScoreRateMap.get(questionNumber);
                    String scoreRate = i.get("averageRate").toString();
                    DifferenceRateUtil.computeDifferenceRate(i, scoreRate, allScoreRate);

                    // 如果包含point
                    if (i.containsKey("point")) {
                        List<Map<String, Object>> pointList = (List<Map<String, Object>>) i.get("point");
                        for (Map<String, Object> point : pointList) {
                            String index = point.get("index").toString();
                            String schoolScoreRate1 = allScoreRateMap.get(i.get("questionNumber").toString() + "-" + index);
                            String pointScoreRate = point.get("averageRate").toString();
                            DifferenceRateUtil.computeDifferenceRate(point, pointScoreRate, schoolScoreRate1);
                        }
                    }
                }
            }
            item.put("questions", list);
        });

        List<Map<String, Object>> result = new ArrayList<>();
        if(examPaperStat!=null){
            examPaperStat.put("questions", examQuestionStatList);
            result.add(examPaperStat);
        }
        examSchoolCustomSortService.sort(examSchoolPaperStatList, examId);
        result.addAll(examSchoolPaperStatList);

        return result;

    }

    /**
     * 获取小题均分
     *
     * @param params examId paperId statId
     *               [schoolId](得分率图表只能显示某个学校的班级对比)
     * @return
     */
    public Map<String, Object> getClassQuestionAvg(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isNumeric("statId")
                .isNotBlank("courseId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        Bson query = and(eq("examId", examId),
                eq("paperId", Long.valueOf(params.get("paperId").toString())),
                eq("statId", Long.valueOf(params.get("statId").toString())),
                eq("courseId",Long.valueOf(params.get("courseId").toString())));

        // 查询联考总分、主客观题均分
        Document examPaperStat = mongoDatabase.getCollection("examPaperStat")
                .find(query)
                .projection(fields(include("averageScore", "averageRate", "objectiveAverageScore", "objectiveScoreRate",
                        "subjectiveAverageScore", "subjectiveScoreRate","participationNumber"), excludeId()))
                .first();

        // 查询联考小题均分
        List<Map<String, Object>> examQuestionStatList = mongoDatabase.getCollection("examQuestionStat")
                .find(query)
                .projection(fields(include("questionNumber", "structureNumber", "averageScore", "averageRate", "point"), excludeId()))
                .sort(ascending("questionNumber"))
                .into(new ArrayList<>());

        // 记录联考总体每一道题目的得分率(这里不要求显示总分的得分率，不记录总分的)
        Map<String, String> allScoreRateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(examQuestionStatList)) {
            for (Map<String, Object> item : examQuestionStatList) {
                String questionNumber = item.get("questionNumber").toString();
                String averageRate = item.get("averageRate").toString();
                allScoreRateMap.put(questionNumber, averageRate);

                // 如果存在point
                if (item.containsKey("point")) {
                    List<Map<String, Object>> pointList = (List<Map<String, Object>>) item.get("point");
                    for (Map<String, Object> point : pointList) {
                        allScoreRateMap.put(questionNumber + "-" + point.get("index"), point.get("averageRate").toString());
                    }
                }
            }
        }

        List<Document> list = new ArrayList<>();
        if (examPaperStat != null) {
            examPaperStat.put("questions", examQuestionStatList);
            list.add(examPaperStat);
        }

        // 如果有携带schoolId的话
        if (ObjectUtil.isValidId(params.get("schoolId"))) {
            query = and(query, eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        }
        params.put("isUnionStat", true);
        query = examConfigService.fillVisibleSchoolAndClass(params, query, examId, StatDataDimensionEnum.CLASS.getValue());

        //班级 分页用
        MongoCollection<Document> examClassStat = mongoDatabase.getCollection("examClassPaperStat");
        Map<String, Object> rs = new HashMap<>();
        long totalCount = examClassStat.countDocuments(query);
        rs.put("totalCount", totalCount+1);
        if (totalCount == 0) {
            rs.put("list", list);
            return rs;
        }
        List<Document> examClass = examClassStat.find(query)
                .projection(fields(include("classId","classSort","participationNumber","className","schoolId","schoolName","resultStatus","averageScore", "averageRate", "objectiveAverageScore", "objectiveScoreRate",
                        "subjectiveAverageScore", "subjectiveScoreRate","participationNumber"), excludeId()))
                .into(new ArrayList<>());
        //分页
        List<Document> classList = examClass;
        if (ObjectUtil.isNumeric(params.get("pageSize")) && ObjectUtil.isNumeric(params.get("currentIndex"))) {
            classList = examClass.stream().skip(MapUtil.getLong(params, "currentIndex")).limit(MapUtil.getInt(params, "pageSize"))
                    .collect(Collectors.toList());
        }
        classList.sort(examSchoolCustomSortService.getExamSchoolCustomSortComparator(classList, examId)
                        .thenComparing(s -> Long.valueOf(s.getOrDefault("classSort", 0).toString())));

        List<Long> classIds = new ArrayList<>();
        classList.forEach(l->classIds.add(l.getLong("classId")));

        query = and(query,in("classId",classIds));

        // 查询学校小题均分
        List<Map<String, Object>> examSchoolQuestionStatList = mongoDatabase.getCollection("examClassQuestionStat")
                .find(query)
                .projection(fields(include("classId","className","schoolId","schoolName","questionNumber",
                        "structureNumber", "averageScore","participationNumber", "averageRate", "point"), excludeId()))
                .into(new ArrayList<>());
        // 排序不放在mongo，异步联考发现内存不够
        examSchoolQuestionStatList.sort(Comparator.comparing(i -> MapUtil.getInt(i, "questionNumber")));

        Map<Long, List<Map<String, Object>>> schoolQuestionGroup = examSchoolQuestionStatList.stream()
                .collect(groupingBy(item -> Long.valueOf(item.get("classId").toString())));
        classList.forEach(item ->{
            List<Map<String, Object>> question = schoolQuestionGroup.get(Long.valueOf(item.get("classId").toString()));
            // 班级不参加考试时为null
            if (CollectionUtils.isNotEmpty(question)) {
                question.sort(Comparator.comparing(c->Long.valueOf(c.get("questionNumber").toString())));
                // 计算每一道题与联考的得分率差值
                if (MapUtils.isNotEmpty(allScoreRateMap)) {
                    for (Map<String, Object> i : question) {
                        String allScoreRate = allScoreRateMap.get(i.get("questionNumber").toString());
                        String classScoreRate = i.get("averageRate").toString();
                        DifferenceRateUtil.computeDifferenceRate(i, classScoreRate, allScoreRate);

                        // 如果包含point
                        if (i.containsKey("point")) {
                            List<Map<String, Object>> pointList = (List<Map<String, Object>>) i.get("point");
                            for (Map<String, Object> point : pointList) {
                                String index = point.get("index").toString();
                                String schoolScoreRate1 = allScoreRateMap.get(i.get("questionNumber").toString() + "-" + index);
                                String pointScoreRate = point.get("averageRate").toString();
                                DifferenceRateUtil.computeDifferenceRate(point, pointScoreRate, schoolScoreRate1);
                            }
                        }
                    }
                }
                item.put("questions", question);
                item.put("schoolName", question.get(0).get("schoolName"));
            }
        });


        list.addAll(classList);
        rs.put("list", list);
        return rs;
    }

    /**
     * 获取均分排名（校内分析）
     *
     * @param params examId schoolId [statId]
     * @return
     */
    public List<Map<String, Object>> getAvgRankInSchool(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        Long statId = ObjectUtil.isBlank(params.get("statId")) ? 0 : Long.valueOf(params.get("statId").toString());

        Bson query = and(eq("examId", examId),
                eq("statId", statId),
                eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());
        Bson unionQuery = and(eq("examId", examId), eq("statId", statId));

        List<Map<String, Object>> result = new ArrayList<>();
        ExamCourseVisibility examCourseVisibility = displayExamService.getUnionStatCourseVisibility(params);
        if (examCourseVisibility.isTotalScoreVisible()) {
            // 可以看到总分数据时，查询总分均分
            Map<String, Object> examSchoolStat = mongoDatabase.getCollection("examSchoolStat")
              .find(query)
              .projection(fields(include("participationNumber", "averageScore",
                  "averageRate", "passRate", "standardDeviation", "averageSubtraction", "averageSubtractionRate", "avgRate", "ranking"),
                excludeId()))
              .first();
            if (MapUtils.isNotEmpty(examSchoolStat)) {
                // 查询联考总分均分
                Map<String, Object> examStat = mongoDatabase.getCollection("examStat")
                  .find(unionQuery)
                  .projection(fields(include("averageScore", "averageRate", "passRate", "standardDeviation",
                      "averageSubtraction", "averageSubtractionRate", "avgRate", "ranking"),
                    excludeId()))
                  .first();
                if (MapUtils.isNotEmpty(examStat)) {
                    examSchoolStat.put("unionAverageScore", examStat.get("averageScore"));
                    examSchoolStat.put("unionAverageRate", examStat.get("averageRate"));
                }
                result.add(examSchoolStat);
            }
        }

        List<Map<String, Object>> examCourseStatList = mongoDatabase.getCollection("examCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, unionQuery))
                .projection(fields(include("courseId", "courseName", "participationNumber", "averageScore",
                        "averageRate", "passRate", "standardDeviation", "averageSubtraction", "averageSubtractionRate", "avgRate", "ranking"),
                        excludeId()))
                .into(new ArrayList<>());

        // 查询课程均分
        List<Map<String, Object>> examSchoolCourseStatList = mongoDatabase.getCollection("examSchoolCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
                .projection(fields(include("courseId", "courseName", "participationNumber", "averageScore",
                        "averageRate", "passRate", "standardDeviation", "averageSubtraction", "averageSubtractionRate", "avgRate", "ranking"),
                        excludeId()))
                .sort(Sorts.ascending("courseId"))
                .into(new ArrayList<>());

        Map<Long, Map<String, Object>> courseMap = examCourseStatList.stream().collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));
        examSchoolCourseStatList.forEach(item -> {
            Map<String, Object> course = courseMap.get(Long.valueOf(item.get("courseId").toString()));
            if (ObjectUtil.isBlank(course)){
                return;
            }
            item.put("unionAverageScore", course.get("averageScore"));
            item.put("unionAverageRate", course.get("averageRate"));
        });

        result.addAll(examSchoolCourseStatList);

        return result;

    }

    /**
     * 获取最高分（校内分析）
     *
     * @param params examId schoolId [statId]
     * @return
     */
    public List<Map<String, Object>> getTopInSchool(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        Long statId = ObjectUtil.isBlank(params.get("statId")) ? 0 : Long.valueOf(params.get("statId").toString());

        Bson query = and(eq("examId", examId),
                eq("statId", statId),
                eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());
        Bson unionQuery = and(eq("examId", examId), eq("statId", statId));

        List<Map<String, Object>> result = new ArrayList<>();
        ExamCourseVisibility examCourseVisibility = displayExamService.getUnionStatCourseVisibility(params);
        if(examCourseVisibility.isTotalScoreVisible()){
            // 可以看到总分数据时，查询总分最高分
            Map<String, Object> examSchoolStat = mongoDatabase.getCollection("examSchoolStat")
              .find(query)
              .projection(fields(include("highestStudent"), excludeId()))
              .first();
            if (MapUtils.isNotEmpty(examSchoolStat)) {
                // 查询联考总分最高分
                Map<String, Object> examStat = mongoDatabase.getCollection("examStat")
                  .find(unionQuery)
                  .projection(fields(include("highestScore"), excludeId()))
                  .first();
                if (MapUtils.isNotEmpty(examStat)) {
                    examSchoolStat.put("unionHighestScore", examStat.get("highestScore"));
                }
                result.add(examSchoolStat);
            }
        }

        // 查询联考课程最高分
        List<Map<String, Object>> examCourseStatList = mongoDatabase.getCollection("examCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, unionQuery))
                .projection(fields(include("courseId", "courseName", "highestScore"),
                        excludeId()))
                .into(new ArrayList<>());

        // 查询课程最高分
        List<Map<String, Object>> examSchoolCourseStatList = mongoDatabase.getCollection("examSchoolCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
                .projection(fields(include("courseId", "courseName", "highestStudent"), excludeId()))
                .sort(Sorts.ascending("courseId"))
                .into(new ArrayList<>());

        Map<Long, Map<String, Object>> courseMap = examCourseStatList.stream().collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));
        examSchoolCourseStatList.forEach(item -> {
            item.put("unionHighestScore",
              courseMap.getOrDefault(MapUtils.getLong(item, "courseId"), Collections.emptyMap()).get("highestScore"));
        });

        result.addAll(examSchoolCourseStatList);

        return result;

    }

    /**
     * 获取前后N名分布（校内分析）
     *
     * @param params examId schoolId [statId]
     * @return
     */
    public List<Map<String, Object>> getBeforeAndAfterInSchool(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("schoolId")
                .verify();

        Long examId = MapUtils.getLong(params, "examId");
        Long statId = ObjectUtil.isBlank(params.get("statId")) ? 0 : Long.valueOf(params.get("statId").toString());

        Bson query = and(eq("examId", examId),
                eq("statId", statId),
                eq("schoolId", Long.valueOf(params.get("schoolId").toString())));
        query = examConfigService.fillVisibleSchool(params, query, examId, StatDataDimensionEnum.SCHOOL.getValue());
        Bson unionQuery = and(eq("examId", examId), eq("statId", statId));

        List<Map<String, Object>> result = new ArrayList<>();
        ExamCourseVisibility examCourseVisibility = displayExamService.getUnionStatCourseVisibility(params);
        if(examCourseVisibility.isTotalScoreVisible()){
            // 可以看到总分数据时，查询总分前后N名分布
            Map<String, Object> examSchoolStat = mongoDatabase.getCollection("examSchoolStat")
              .find(query)
              .projection(fields(include("beforeUnionRank", "afterUnionRank", "proportionBeforeRank", "proportionAfterRank"), excludeId()))
              .first();
            if (MapUtils.isNotEmpty(examSchoolStat)) {
                // 查询联考总分前后N名分布
                Map<String, Object> examStat = mongoDatabase.getCollection("examStat")
                  .find(unionQuery)
                  .projection(fields(include("beforeUnionRank", "afterUnionRank"), excludeId()))
                  .first();
                if (MapUtils.isNotEmpty(examStat)) {
                    examSchoolStat.put("unionBeforeUnionRank", examStat.get("beforeUnionRank"));
                    examSchoolStat.put("unionAfterUnionRank", examStat.get("afterUnionRank"));
                }
                result.add(examSchoolStat);
            }
        }

        // 查询联考课程前后N名分布
        List<Map<String, Object>> examCourseStatList = mongoDatabase.getCollection("examCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, unionQuery))
                .projection(fields(include("courseId", "beforeUnionRank", "afterUnionRank"), excludeId()))
                .sort(Sorts.ascending("courseId"))
                .into(new ArrayList<>());

        // 查询课程前后N名分布
        List<Document> examSchoolCourseStatList = mongoDatabase.getCollection("examSchoolCourseStat")
                .find(DisplayExamUtil.fillVisibleCourse(examCourseVisibility, query))
                .projection(fields(include("courseId", "courseName", "beforeUnionRank", "afterUnionRank","proportionBeforeRank","proportionAfterRank"), excludeId()))
                .sort(Sorts.ascending("courseId"))
                .into(new ArrayList<>());

        Map<Long, Map<String, Object>> courseMap = examCourseStatList.stream().collect(toMap(item -> Long.valueOf(item.get("courseId").toString()), item -> item));
        examSchoolCourseStatList.forEach(item -> {
            Long itemCourseId = MapUtils.getLong(item, "courseId");
            item.put("unionBeforeUnionRank", courseMap.getOrDefault(itemCourseId, Collections.emptyMap()).get("beforeUnionRank"));
            item.put("unionAfterUnionRank", courseMap.getOrDefault(itemCourseId, Collections.emptyMap()).get("afterUnionRank"));
        });
        if(commonCourseService.needCourseSort(params)){
            commonCourseService.courseSortForWushi(examSchoolCourseStatList,null,false);
        }
        result.addAll(examSchoolCourseStatList);

        return result;

    }
    public void dealScoreSection(List<Document> scoreSection){
        for (Document d : scoreSection) {
            List<Map<String, Object>> scoreSectionList = (List<Map<String, Object>>) d.get("scoreSection");
            if (CollectionUtils.isEmpty(scoreSectionList)){
                continue;
            }
            scoreSectionList.sort((s1,s2)->{
                Double open1= Double.valueOf(s1.get("open").toString());
                Double open2= Double.valueOf(s2.get("open").toString());
                return open2.compareTo(open1);
            });

            Integer participationNumber ;
            if(d.get("participationNumber") == null){
                participationNumber = 0;
            }else {
                participationNumber = (Integer) d.get("participationNumber");
            }

            Integer cumulative = 0;
            for (Map<String, Object> map : scoreSectionList) {
                Integer total = (Integer) map.get("total");
                cumulative += total;
                map.put("cumulative", cumulative);
                if (participationNumber.equals(0)){
                    map.put("ratio",0);
                }else {
                    map.put("ratio",(float)total/participationNumber);
                }
            }
        }
    }

}
