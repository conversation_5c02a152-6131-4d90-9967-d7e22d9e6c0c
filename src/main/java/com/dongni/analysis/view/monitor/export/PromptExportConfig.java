package com.dongni.analysis.view.monitor.export;

import com.dongni.common.utils.spring.LishuCondition;
import com.dongni.common.utils.spring.SecondProductCondition;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;


@Configuration
public class PromptExportConfig {
    
    @Bean("lishuPrompt")
    @Conditional(LishuCondition.class)
    public Prompt lishuPrompt() {
        return new LishuPrompt();
    }

    @Bean("secondProductPrompt")
    @Conditional(SecondProductCondition.class)
    public Prompt secondProductPrompt() {
        return new SecondProductPrompt();
    }
    
    @Bean("normalPrompt")
    @ConditionalOnMissingBean(Prompt.class)
    public Prompt normalPrompt() {
        return new DongniPrompt();
    }

    
}
