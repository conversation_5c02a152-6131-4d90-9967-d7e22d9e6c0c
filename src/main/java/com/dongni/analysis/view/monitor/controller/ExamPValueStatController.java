package com.dongni.analysis.view.monitor.controller;

import com.dongni.analysis.view.monitor.service.ExamPValueStatService;
import com.dongni.analysis.view.monitor.service.ExamSchoolPValueStatService;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by scott
 * time: 9:58 2018/6/11
 * description:联考班级P值
 */
@RestController
@RequestMapping("/analysis/view/monitor/exam/pvalue")
public class ExamPValueStatController extends BaseController {


    @Autowired
    private ExamPValueStatService examPValueStatService;

    /**
     * 联考班级P值
     */
    @GetMapping("/class")
    public Response getPValue(){
        return new Response(examPValueStatService.getPValue(getParameterMap()));
    }

    /**
     * 联考班级各科P值对比
     */
    @GetMapping("/class/compare")
    public Response getPValueCompare(){
        return new Response(examPValueStatService.getPValueCompare(getParameterMap()));
    }

}
