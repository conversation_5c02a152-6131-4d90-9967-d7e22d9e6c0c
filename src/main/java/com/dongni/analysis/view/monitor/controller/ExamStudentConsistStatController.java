package com.dongni.analysis.view.monitor.controller;

import com.dongni.analysis.view.monitor.service.ExamStudentConsistStatService;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 学生档次等级
 */
@RestController
@RequestMapping("/analysis/view/monitor/exam/student/consist")
public class ExamStudentConsistStatController extends BaseController {

	@Autowired
	private ExamStudentConsistStatService examStudentConsistStatService;

	/**
	 * 学生查看原卷等级时，返回学生的档次
	 */
	@GetMapping()
	public Response getStudentConsist() {
		return new Response(examStudentConsistStatService.getStudentConsist(getParameterMap()));
	}

}
