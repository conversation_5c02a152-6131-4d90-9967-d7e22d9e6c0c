package com.dongni.analysis.view.mystudent.controller;

import com.dongni.analysis.view.mystudent.service.StudentStatService;
import com.dongni.commons.entity.Response;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.mvc.controller.BaseController;
import java.util.Collections;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by scott
 * time: 11:04 2018/1/16
 * description:
 */
@RestController
@RequestMapping("/analysis/view/myStudent/student")
public class StudentStatController extends BaseController {

    @Autowired
    private StudentStatService studentService;

    /**
     * 获取最新考试具有代表性的学生(进步最大和退步最大的12名学生)
     * params classId courseId
     * @return 最新考试以及代表的学生
     */
    @GetMapping("/representative")
    public Response getRepresentativeStudent(){
        return new Response(studentService.getRepresentativeStudent(getParameterMap()));
    }


    /**
     *  获取学生
     *  params classId
     * @return 学生
     */
    @GetMapping
    public Response getStudent(){
        return new Response(studentService.getStudent(getParameterMap()));
    }


    /**
     *  获取学生得分情况
     *  params classId studentId
     * @return 学生得分情况
     */
    @GetMapping("/score")
    public Response getStudentExamScore(){
        Map<String, Object> map = studentService.getStudentScore(getParameterMap());
        return map.get("error") == null
          ? new Response(map.getOrDefault("data", Collections.emptyList()))
          : new Response(ResponseStatusEnum.DATA_NOT_EXISTS,map.get("error").toString(),map.get("data"));
    }

    /**
     *  获取学生得分率
     *  params classId studentId courseId
     * @return 学生得分率
     */
    @GetMapping("/scoreRate")
    public Response getStudentScoreRate(){
        return new Response(studentService.getStudentScoreRate(getParameterMap()));
    }

    /**
     *  获取学生上线情况
     *  params classId studentId
     * @return 学生上线情况
     */
    @GetMapping("/line")
    public Response getStudentExamLine(){
        return new Response(studentService.getStudentLine(getParameterMap()));
    }

    /**
     *  获取学生击败率
     *  params classId studentId courseId
     * @return 学生击败率
     */
    @GetMapping("/beatRate")
    public Response getStudentBeatRate(){
        return new Response(studentService.getStudentBeatRate(getParameterMap()));
    }

}
