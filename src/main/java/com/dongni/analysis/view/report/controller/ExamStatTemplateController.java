package com.dongni.analysis.view.report.controller;

import com.dongni.analysis.view.report.bean.ExamStatTemplateParams;
import com.dongni.analysis.view.report.service.ExamStatTemplateService;
import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.commons.utils.JSONUtil;
import java.util.Map;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: hzw
 * @date: 2024/10/14
 * @description:
 */
@RestController
@RequestMapping("/analysis/view/report/exam/stat/template")
public class ExamStatTemplateController extends BaseController {

	@Autowired
	private ExamStatTemplateService examStatTemplateService;

	/**
	 * 获取报告模板列表
	 */
	@GetMapping("/list")
	public Response getExamStatTemplateList() {
		return new Response(examStatTemplateService.getExamStatTemplateList(getExamStatTemplateParams()));
	}

	/**
	 * 根据考试获取可用的报告模板列表
	 */
	@GetMapping("/visible/list")
	public Response getExamVisibleStatTemplateList() {
		return new Response(examStatTemplateService.getExamVisibleStatTemplateList(getExamStatTemplateParams()));
	}

	/**
	 * 获取单个报告模板详情
	 */
	@GetMapping("/detail")
	public Response getExamStatTemplateDetail() {
		return new Response(examStatTemplateService.getExamStatTemplateDetail(getExamStatTemplateParams().getExamStatTemplateId()));
	}

	/**
	 * 根据模板的课程获取模板默认的配置
	 */
	@PostMapping("/default")
	public Response getDefaultExamStatTemplate() {
		return new Response(examStatTemplateService.getDefaultExamStatTemplate(getExamStatTemplateParams()));
	}

	/**
	 * 保存报告模板
	 */
	@PostMapping("/save")
	public Response saveExamStatTemplate() {
		examStatTemplateService.saveExamStatTemplate(getExamStatTemplateParams());
		return new Response();
	}

	/**
	 * 更新报告模板
	 */
	@PostMapping("/update")
	public Response updateExamStatTemplate() {
		examStatTemplateService.updateExamStatTemplate(getExamStatTemplateParams());
		return new Response();
	}

	/**
	 * 删除报告模板
	 */
	@PostMapping("/delete")
	public Response deleteExamStatTemplate() {
		examStatTemplateService.deleteExamStatTemplate(getExamStatTemplateParams());
		return new Response();
	}

	private ExamStatTemplateParams getExamStatTemplateParams(){
		return JSONUtil.parse(JSONUtil.toJson(getParameterMap()), ExamStatTemplateParams.class);
	}

}
