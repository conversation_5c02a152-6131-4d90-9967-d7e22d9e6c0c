package com.dongni.analysis.view.report.bean;

import java.util.Date;
import java.util.StringJoiner;

/**
 * @author: hzw
 * @date: 2024/10/16
 * @description:
 */
public class ExamStatTemplateVO {

	/**
	 * 报告模板id
	 */
	private Long examStatTemplateId;

	/**
	 * 模板名称
	 */
	private String examStatTemplateName;

	/**
	 * 报告描述
	 */
	private String statDesc;

	/**
	 * 是否开启了赋分，false：未开启，true：已开启
	 */
	private boolean scoreChangeEnable;

	/**
	 * 最后修改时间
	 */
	private Date modifyDateTime;

	public Long getExamStatTemplateId() {
		return examStatTemplateId;
	}

	public void setExamStatTemplateId(Long examStatTemplateId) {
		this.examStatTemplateId = examStatTemplateId;
	}

	public String getExamStatTemplateName() {
		return examStatTemplateName;
	}

	public void setExamStatTemplateName(String examStatTemplateName) {
		this.examStatTemplateName = examStatTemplateName;
	}

	public String getStatDesc() {
		return statDesc;
	}

	public void setStatDesc(String statDesc) {
		this.statDesc = statDesc;
	}

	public boolean isScoreChangeEnable() {
		return scoreChangeEnable;
	}

	public void setScoreChangeEnable(boolean scoreChangeEnable) {
		this.scoreChangeEnable = scoreChangeEnable;
	}

	public Date getModifyDateTime() {
		return modifyDateTime;
	}

	public void setModifyDateTime(Date modifyDateTime) {
		this.modifyDateTime = modifyDateTime;
	}

	@Override
	public String toString() {
		return new StringJoiner(", ", ExamStatTemplateVO.class.getSimpleName() + "[", "]")
			.add("examStatTemplateId=" + examStatTemplateId)
			.add("examStatTemplateName='" + examStatTemplateName + "'")
			.add("statDesc='" + statDesc + "'")
			.add("scoreChangeEnable=" + scoreChangeEnable)
			.add("modifyDateTime=" + modifyDateTime)
			.toString();
	}
}
