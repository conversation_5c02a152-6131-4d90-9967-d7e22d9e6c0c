package com.dongni.tiku.holiday.task.bean.vo;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2024/6/27
 * @description:
 */
@Getter
@Setter
public class HolidaySingleHomeworkSubmitVO {

	/**
	 * 作业下各个分层的已提交学生名单
	 */
	private List<LevelSubmitListVO> submitList;

	/**
	 * 未提交学生名单
	 */
	private List<String> unSubmitList;

	@Getter
	@Setter
	public static class LevelSubmitListVO{
		/**
		 * 分层名称
		 */
		private String levelName;

		/**
		 * 分层顺序
		 */
		private int levelOrder;

		/**
		 * 分层下的已提交学生名单
		 */
		private List<String> levelSubmitList;
	}
}
