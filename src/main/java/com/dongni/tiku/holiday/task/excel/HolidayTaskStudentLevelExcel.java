package com.dongni.tiku.holiday.task.excel;

import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.common.utils.DictUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.wrong.book.bean.vo.WrongBookCourseVO;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.bson.Document;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.dongni.common.report.excel.ExcelUtil.createRow;

/**
 * <p>周期本-层次excel导出模板</p>
 *
 * <AUTHOR>
 * @className CycleWrongBookStudentLevelExcel
 * @since 2023/7/4 10:44
 */
public class HolidayTaskStudentLevelExcel extends ExportExcel {

    // 表头
    private List<String> headers;

    // 表头对应的字段
    private List<String> fields;

    // 课程数据
    private String courseName;

    // 周期本需要生成的学生
    private List<Document> holidayTaskStudentLevelStratificationList;

    // 排名趋势配置
    private int holidayTaskRankConfig;

    public HolidayTaskStudentLevelExcel(String sheetName,
                                        List<String> headers,
                                        List<String> fields,
                                        String courseName,
                                        List<Document> holidayTaskStudentLevelStratificationList,
                                        int holidayTaskRankConfig) {
        super(sheetName);
        this.headers = headers;
        this.fields = fields;
        this.courseName = courseName;
        this.holidayTaskStudentLevelStratificationList = holidayTaskStudentLevelStratificationList;
        this.holidayTaskRankConfig = holidayTaskRankConfig;
    }

    @Override
    protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
        CellStyle style = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
        Row firstRow = sheet.createRow(currentRow);
        Row secondRow = createRow(sheet,currentRow + 1);
        // 前四个字段的表头
        for (String header : headers) {
            ExcelUtil.addMergedRegion(sheet, firstRow.getRowNum(), secondRow.getRowNum(), currentCol, currentCol);
            ExcelUtil.createCell(firstRow, currentCol++, header, style);
        }

        // 层次表头
        int courseHeaderStartColumnIndex = 4;
        ExcelUtil.createCell(firstRow, courseHeaderStartColumnIndex, "层次", style);
        ExcelUtil.addMergedRegion(sheet, firstRow.getRowNum(), firstRow.getRowNum(), courseHeaderStartColumnIndex, courseHeaderStartColumnIndex);

        // 课程表头
        ExcelUtil.createCell(secondRow, courseHeaderStartColumnIndex++, courseName, style);

        return currentRow + 2;
    }

    @Override
    protected int genBody(Sheet sheet, int currentRow, int currentCol) {

        if (holidayTaskStudentLevelStratificationList == null || holidayTaskStudentLevelStratificationList.isEmpty()) {
            return currentRow;
        }

        Map<Long, Document> holidayTaskStudentLevelStratificationMap = holidayTaskStudentLevelStratificationList.stream()
                .collect(Collectors.toMap(item -> MapUtil.getLong(item, "studentId"), item -> item));

        CellStyle bodyStyle = ExcelStyle.getBodyStyle(sheet.getWorkbook());

        boolean classConfig = DictUtil.isEquals(holidayTaskRankConfig, "cycleWrongBookRankConfig", "class");

        for (Document holidayTaskStudentLevelStratification : holidayTaskStudentLevelStratificationList) {
            Row row =sheet.createRow(currentRow);

            // 设置学生信息数据
            for (String field : fields) {
                ExcelUtil.createCell(row, currentCol++, holidayTaskStudentLevelStratification.get(field), bodyStyle);
            }

            // 设置科目分层数据
            Long studentId = MapUtil.getLong(holidayTaskStudentLevelStratification, "studentId");
            Document holidayTaskStudentLevel = holidayTaskStudentLevelStratificationMap.get(studentId);
            if (MapUtils.isNotEmpty(holidayTaskStudentLevel)) {
                ExcelUtil.createCell(row, currentCol++, classConfig? MapUtil.getStringNullable(holidayTaskStudentLevel, "classLevel"): MapUtil.getStringNullable(holidayTaskStudentLevel, "gradeLevel"), bodyStyle);
            }
            ++currentRow;
            currentCol = 0;
        }

        return currentRow;
    }
}
