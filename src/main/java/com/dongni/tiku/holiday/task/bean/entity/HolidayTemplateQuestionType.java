package com.dongni.tiku.holiday.task.bean.entity;

import com.dongni.common.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/6/13 周四 下午 02:57
 * @Version 1.0.0
 */
@Setter
@Getter
public class HolidayTemplateQuestionType extends BaseEntity {
    private static final long serialVersionUID = -7160118201192240268L;

    /**
     * 模板试题题型id
     */
    private Long templateQuestionTypeId;

    /**
     * 模板试题id
     */
    private Long templateQuestionId;

    /**
     * 题型
     */
    private String questionType;

    /**
     * 题型名称
     */
    private String questionTypeName;

    /**
     * 主观题 or 客观题
     */
    private Integer readType;

    public static HolidayTemplateQuestionType of(long templateQuestionId,
                                                 String questionType,
                                                 String questionTypeName,
                                                 int readType,
                                                 long userId,
                                                 String userName,
                                                 LocalDateTime now) {
        HolidayTemplateQuestionType holidayTemplateQuestionType = new HolidayTemplateQuestionType();
        holidayTemplateQuestionType.setTemplateQuestionId(templateQuestionId);
        holidayTemplateQuestionType.setQuestionType(questionType);
        holidayTemplateQuestionType.setQuestionTypeName(questionTypeName);
        holidayTemplateQuestionType.setReadType(readType);
        holidayTemplateQuestionType.setCreatorId(userId);
        holidayTemplateQuestionType.setCreatorName(userName);
        holidayTemplateQuestionType.setCreateDateTime(now);
        holidayTemplateQuestionType.setModifierId(userId);
        holidayTemplateQuestionType.setModifierName(userName);
        holidayTemplateQuestionType.setModifyDateTime(now);
        return holidayTemplateQuestionType;
    }

    public static List<HolidayTemplateQuestionType> ofMulti(List<Long> templateQuestionIds,
                                                      String questionType,
                                                      String questionTypeName,
                                                      int readType,
                                                      long userId,
                                                      String userName,
                                                      LocalDateTime now) {
        return templateQuestionIds.stream()
                .map(id -> of(id, questionType, questionTypeName, readType, userId, userName, now))
                .collect(Collectors.toList());
    }
}
