package com.dongni.tiku.holiday.task.service;


import com.dongni.tiku.holiday.task.bean.params.HolidayTaskCourseParam;
import com.dongni.tiku.holiday.task.bean.vo.HolidayTaskCourseVO;

import java.util.List;

/**
 * <p>
 * 假期任务所支持的课程 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
public interface IHolidayTaskCourseService {

    /**
     * 获取假期本支持的课程列表
     * @param holidayTaskCourseParam stage
     * @return
     */
    List<HolidayTaskCourseVO> queryHolidayTaskCourseList(HolidayTaskCourseParam holidayTaskCourseParam);
}
