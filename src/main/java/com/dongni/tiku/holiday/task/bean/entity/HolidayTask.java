package com.dongni.tiku.holiday.task.bean.entity;

import java.time.LocalDateTime;

import cn.hutool.core.bean.BeanUtil;
import com.dongni.tiku.holiday.task.bean.params.SaveHolidayTaskParam;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Getter
@Setter
public class HolidayTask {

    private static final long serialVersionUID = 1L;

    /**
     * 假期本任务主键
     */
    private Long holidayTaskId;

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 年级ID
     */
    private Long gradeId;

    /**
     * 年级名称
     */
    private String gradeName;

    /**
     * 年级编码
     */
    private Integer gradeType;

    /**
     * 学段
     */
    private Integer stage;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 任务名称
     */
    private String holidayTaskName;

    /**
     * 任务类型（1-试用，2-正式）
     */
    private Integer holidayTaskType;

    /**
     * 任务所处步骤（1-参数设置，2-内容生成，3-内容调整，5-已完成）
     */
    private Integer holidayTaskStep;

    /**
     * 参数设置所处步骤（1-基本设定，2-章节选择范围，3-分层及题量设定，4-学情数据引入）
     */
    private Integer holidayConfigStep;

    /**
     * CRM编号
     */
    private String crmNumber;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createDateTime;

    /**
     * 修改人ID
     */
    private Long modifierId;

    /**
     * 修改人
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyDateTime;


    public static HolidayTask convert(SaveHolidayTaskParam saveHolidayTaskParam) {
        HolidayTask holidayTask = new HolidayTask();
        BeanUtil.copyProperties(saveHolidayTaskParam, holidayTask);
        return holidayTask;
    }
}
