package com.dongni.tiku.holiday.task.bean.params;

import com.dongni.commons.entity.BaseRequestParams;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/6/13 周四 下午 03:55
 * @Version 1.0.0
 */
@Setter
@Getter
public class BatchUpdateTemplateQuestionTypeParam extends BaseRequestParams {
    @NotEmpty(message = "模板试题ID列表不能为空")
    private List<Long> templateQuestionIds;

    @Valid
    @NotEmpty(message = "模板试题题型列表不能为空")
    private List<InsertTemplateParam.TemplateQuestionTypeParam> types;
}
