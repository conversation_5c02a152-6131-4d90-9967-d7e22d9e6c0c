package com.dongni.tiku.holiday.task.bean.dto;

import com.dongni.tiku.holiday.task.bean.vo.HolidayTemplateQuestionLevelVo;
import com.dongni.tiku.holiday.task.bean.vo.HolidayTemplateQuestionTypeVo;
import com.dongni.tiku.holiday.task.bean.vo.HolidayTemplateQuestionVo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/6/13 周四 下午 03:21
 * @Version 1.0.0
 */
@Getter
@Setter
public class HolidayTemplateQuestionCompositeDTO {
    /**
     * 试题信息
     */
    private HolidayTemplateQuestionVo holidayTemplateQuestion;

    /**
     * 试题对应的分层信息
     */
    private List<HolidayTemplateQuestionLevelVo> holidayTemplateQuestionLevels;

    /**
     * 试题对应的题型信息
     */
    private List<HolidayTemplateQuestionTypeVo> holidayTemplateQuestionTypes;
}
