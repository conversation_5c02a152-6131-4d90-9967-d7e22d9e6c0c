package com.dongni.tiku.third.yiqi.service;

import com.dongni.basedata.school.teacher.service.ITeacherService;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.service.TikuAccountService;
import com.dongni.tiku.common.service.TikuCourseRelationService;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Random;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/11/21 16:37
 */
@Service
public class YiqiConfigService {

    @Autowired
    private TikuAccountService tikuAccountService;

    @Autowired
    private TikuCourseRelationService tikuCourseRelationService;

    @Autowired
    private ITeacherService teacherService;

    @Autowired
    private TikuRepository repository;

    /**
     * 获取用户
     *
     * @param params courseId  userId [teacherId] [userType]
     */
    public Map<String, Object> getUser(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("userId")
                .verify();

        Map<String, Object> result;

        //根据courseId从mysql中获取课程信息
        Map<String, Object> course = tikuCourseRelationService.getCourse(params);
        if (ObjectUtil.isBlank(course)) {
            throw new CommonException((ResponseStatusEnum.PARAMETER_ERROR), "课程不存在");
        }

        //目前一起作业网只支持数理化生
        Set<String> subjectSet=new HashSet<>(Arrays.asList("math", "physics", "chemistry", "biology",
                "chinese", "history", "politics", "geography", "english"));
        if (!subjectSet.contains(course.get("relativeKey").toString())) {
            throw new CommonException((ResponseStatusEnum.PARAMETER_ERROR), "不支持目前课程");
        }

        //根据userId获取yiqi_username
        params.put("stage",course.get("stage"));
        Map<String, Object> yiqiAccountByUserId = tikuAccountService.getYiqiAccountByUserId(params);
        if (!ObjectUtil.isBlank(yiqiAccountByUserId)) {
            result = MapUtil.of("username", yiqiAccountByUserId.get("yiqiUsername"), "subject", course.get("relativeKey"));
        } else{
            //------------------------------------------------------------------------------------------
            //兼容性代码，兼容之前获取yiqi_username的方式。某个版本后修改为被注释代码（直接默认获取）。
            //params.put("stage", course.get("stage"));
            //String yiqiUsername = tikuAccountService.getDefaultYiqiUsernameByStage(params).get("yiqiUsername").toString();
            //result = MapUtil.of("username",yiqiUsername , "subject", course.get("relativeKey"));
            //------------------------------------------------------------------------------------------

            //判断请求参数是否含有teacherId,
            //如果没有根据stage获取默认username，

            if (ObjectUtil.isBlank(params.get("teacherId"))) {
                params.put("stage", course.get("stage"));
                result = MapUtil.of("username", tikuAccountService.getDefaultYiqiUsernameByStage(params).get("yiqiUsername"), "subject", course.get("relativeKey"));
            } else {
                //如果有从teacher表中获取schoolId
                Map<String, Object> teacherDetail = teacherService.getTeacherDetail(params);
                if (ObjectUtil.isBlank(teacherDetail)) {
                    throw new CommonException((ResponseStatusEnum.PARAMETER_ERROR), "老师不存在");
                }
                //根据schoolId获取用户信息
                params.put("schoolId", teacherDetail.get("schoolId"));
                Map<String, Object> user = tikuAccountService.getYiqiAccountBySchoolId(params);

                //判断学校是否开通yiqi作业网服务
                //如果没有开通根据stage获取默认username
                if (ObjectUtil.isBlank(user)) {
                    params.put("stage", course.get("stage"));
                    result = MapUtil.of("username", tikuAccountService.getDefaultYiqiUsernameByStage(params).get("yiqiUsername"), "subject", course.get("relativeKey"));
                } else if (MapUtil.getInt(user, "status") == 0) {
                    throw new CommonException((ResponseStatusEnum.PERMISSION_DENIED), "账号被禁用");
                } else {
                    result = MapUtil.of("username", user.get("yiqiUsername"), "subject", course.get("relativeKey"));
                }
            }
        }


        result.put("courseId", Long.valueOf(params.get("courseId").toString()));
        result.put("courseName", course.get("courseName").toString());
        result.put("stage", course.get("stage"));
        result.put("userId", params.get("userId"));
        result.put("userName", params.get("userName"));

        return result;

    }

    /**
     * 获取默认用户
     */
    public Map<String, Object> getDefaultUser() {

        //获取username
        Map<String, Object> usernameMap = tikuAccountService.getDefaultYiqiUsername();

        if (ObjectUtil.isBlank(usernameMap)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "未配置一起作业账号");
        }

        //随机获取subject(yiqi作业网目前只支持数理化生)
        String[] subjectArray = {"math", "physics", "chemistry", "biology"};
        String subject=subjectArray[new Random().nextInt(4)];

        return MapUtil.of("username",usernameMap.get("yiqiUsername"),"subject",subject);
    }

    /**
     * 获取对应的课程
     *
     * @param params courseId
     */
    public Map<String, Object> getCourse(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();

        return repository.selectOne("YiqiConfigMapper.getCourseByYiqi", params);
    }

    /**
     * 根据questionType获取默认的unitType
     */
    public int getDefaultUnitType(Map<String, Object> params) {
        return repository.selectOne("YiqiConfigMapper.getDefaultUnitType",params);
    }
}
