package com.dongni.tiku.third.jyeoo.service;

import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 菁优通用接口
 *
 * <AUTHOR>
 * @date 2019/05/10 17:07
 */
@Service
public class JyeooCommonService {

    @Autowired
    private CommonCourseService commonCourseService;
    
    /**
     * 获取菁优支持的课程
     *
     * @param params [stage]     例: "2"  "2,3"
     * @return 菁优支持的课程
     *         courseId, courseName, stage, courseCode, courseEnName, courseCnName,
     */
    public List<Map<String, Object>> getCourseList(Map<String, Object> params) {
        List<Map<String, Object>> data = getJyeooCourseList();
        if (!ObjectUtil.isBlank(params.get("stage"))) {
            List<String> stages = Arrays.asList(params.get("stage").toString().split(","));
            data = data.stream()
                    .filter(item -> stages.contains(item.get("stage").toString()))
                    .collect(Collectors.toList());
        }
        return data;
    }
    
    /**
     * 获取菁优同步知识点的课程，按stage分组
     * @param params nothing
     * @return stage -> [] courseId, courseName, stage, courseCode, courseEnName, courseCnName
     */
    public Map<Integer, List<Map<String, Object>>> getStageMapSyncCourseList(Map<String, Object> params) {
        return getJyeooCourseList().stream()
                .collect(groupingBy(item -> MapUtil.getInt(item, "stage")));
    }
    
    /**
     * 获取菁优支持的课程
     *
     * @param params [stage]
     * @return stage -> courseList
     */
    public Map<Integer, List<Map<String, Object>>> getStageMapCourseList(Map<String, Object> params) {
        return getCourseList(params).stream()
                .collect(groupingBy(item -> Integer.valueOf(item.get("stage").toString())));
    }
    
    /**
     * 获取懂你内置的课程信息
     * @return courseId courseName stage
     *         [courseCode courseEnName course_cn_name]
     */
    public List<Map<String, Object>> getDnInnerCourse() {
        return commonCourseService.getDnInnerCourse(MapUtil.of());
    }
    
    /**
     * 获取菁优课程
     *   FROM t_course WHERE course_code > 0
     * @return courseId, courseName, stage
     *         courseCode, courseEnName, courseCnName,
     */
    public List<Map<String, Object>> getJyeooCourseList() {
        return commonCourseService.getJyeooCourseList();
    }
    
    /**
     * 抹除非懂你内置单课程的菁优信息
     */
    public void unsetNotDnInnerCourseJyeooInfo() {
        commonCourseService.unsetNotDnInnerCourseJyeooInfo();
    }
    
    /**
     * 更新课程的菁优网信息
     * @param userInfo userId userName userType
     * @param updateList [{}, ...]
     *        courseId, courseCode, courseEnName, courseCnName
     */
    public void updateCourseJyeooInfo(Map<String, Object> userInfo, List<Map<String, Object>> updateList) {
        commonCourseService.updateCourseJyeooInfo(userInfo, updateList);
    }

}
