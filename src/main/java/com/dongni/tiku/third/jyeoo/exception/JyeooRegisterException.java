package com.dongni.tiku.third.jyeoo.exception;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/3/13 013 上午 10:48
 * @Version 1.0.0
 */
public class JyeooRegisterException extends CommonException {
    // 菁优网注册超量的
    public static final String JYEOO_REGISTER_ERRO_MSG = "注册失败，超过每日注册限量";

    public JyeooRegisterException() {
        super(ResponseStatusEnum.JYEOO_REQUEST_ERROR, JYEOO_REGISTER_ERRO_MSG);
    }

    public JyeooRegisterException(Exception e) {
        super(ResponseStatusEnum.JYEOO_REQUEST_ERROR, JYEOO_REGISTER_ERRO_MSG, e);
    }
}
