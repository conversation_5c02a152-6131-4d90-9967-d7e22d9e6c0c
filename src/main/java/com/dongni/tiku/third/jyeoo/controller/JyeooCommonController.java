package com.dongni.tiku.third.jyeoo.controller;

import com.dongni.commons.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.third.jyeoo.service.JyeooCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 菁优网通用控制器
 */
@RestController
@RequestMapping(value = TikuConfig.CONTEXT_PATH + "/third/jyeoo/common")
public class JyeooCommonController extends BaseController {

    @Autowired
    private JyeooCommonService jyeooCommonService;

    /**
     * 获取菁优网支持的课程
     *
     * @return
     */
    @GetMapping("/course")
    public Response getCourseList() {
        return new Response(jyeooCommonService.getStageMapCourseList(getParameterMap()));
    }

    /**
     * 获取含有courseEnName courseCode并且courseCode > 0的课程
     *
     * @return
     */
    @GetMapping("/sync/course")
    public Response getSyncCourseList() {
        return new Response(jyeooCommonService.getStageMapSyncCourseList(getParameterMap()));
    }
}
