package com.dongni.tiku.third.jyeoo.controller;

import com.dongni.commons.entity.Response;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.third.jyeoo.service.JyeooQuestionRequestLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/07/21 <br/>
 *   菁优计量方式统计页面
 */
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/third/jyeoo/question/request/log")
public class JyeooQuestionRequestLogController {
    
    @Autowired
    private JyeooQuestionRequestLogService requestLogService;
    
//    @GetMapping(value = "statistics/price")
//    public Response statisticsPrice(Map<String, Object> params) {
//        return new Response(requestLogService.getStatisticsPrice(params));
//    }
//
//    @GetMapping(value = "statistics/price/export")
//    public Response exportStatisticsPrice(Map<String, Object> params) {
//        return new Response(requestLogService.exportStatisticsPrice(params));
//    }
    
    /**
     * 菁优网统计访问量 总的 不受时间控制的 全表的统计数据
     * @return simpleCount detailCount
     */
    @GetMapping(value = "statistics/visit/all")
    public Response getStatisticsVisitAll(Map<String, Object> params) {
        return new Response(requestLogService.getStatisticsVisitAll());
    }
    
    /**
     * 菁优网统计访问量
     * 1. 筛选项: 当日(默认) 上月、本月、上周、本周、当日、自定义(具体到天)
     * 2. 统计维度: 学校 不以t_jyeoo_user t_jyeoo_school为准，需要跨库sys_user t_teacher t_school
     *                  获取不到的计入懂你学校(区域题库管理员等，可能存在的原用户现在找不到学校的情况)
     * 3. 统计信息: 题干访问量 解析访问量
     * @param params
     *   [startTimestamp] 时间开始时间戳 会被转换为天
     *   [endTimestamp]   时间结束时间戳 会被转换为天
     *   [pageSize pageNo currentIndex]  分页支持
     *   [sortField]     排序支持 排序字段 simpleCount(default)题干 detailCount解析
     *   [sortType]      排序支持 排序方式 DESC(default)降序        ASC升序
     * @return count list[{"schoolId", "schoolName", "simpleCount", "detailCount"},...]
     */
    @GetMapping(value = "statistics/visit")
    public Response statisticsVisit(Map<String, Object> params) {
        return new Response(requestLogService.getStatisticsVisit(params));
    }
    
    /**
     * 菁优网统计访问量导出 无视分页 使用默认排序
     * @param params {@link #statisticsVisit(Map)}
     * @return 导出文件url
     */
    @GetMapping(value = "statistics/visit/export")
    public Response exportStatisticsVisit(Map<String, Object> params) {
        return new Response(requestLogService.exportStatisticsVisit(params));
    }
}
