package com.dongni.tiku.third.jyeoo.service;

import com.dongni.basedata.admin.service.IBaseSchoolService;
import com.dongni.common.report.excel.ExcelReport;
import com.dongni.common.report.excel.simple.SimpleExcelHeader;
import com.dongni.common.report.excel.simple.SimpleExcelReport;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> <br/>
 * @date 2020/07/09 <br/>
 * t_jyeoo_question_request_log
 * 菁优网试题请求日志
 */
@Service
public class JyeooQuestionRequestLogService {
    
    @Autowired
    private TikuRepository tikuRepository;
    
    @Autowired
    private IBaseSchoolService baseSchoolService;
    
    @Autowired
    private JyeooQuestionRequestVisitService jyeooQuestionRequestVisitService;
    
    /** 接口调用类型的字典enName */
    private static final String REQUEST_TYPE_EN_NAME = "jyeooQuestionRequestType";
    /** 题干 接口调用类型的字典key */
    private static final String REQUEST_TYPE_KEY_SIMPLE = "simple";
    /** 解析 接口调用类型的字典key */
    private static final String REQUEST_TYPE_KEY_DETAIL = "detail";
    
    // --------------------------------------------------------------------------------------- 查询
    
    /**
     * 菁优网统计访问量 总的 不受时间控制的 全表的统计数据
     * @return simpleCount detailCount
     */
    public Map<String, Object> getStatisticsVisitAll() {
        jyeooQuestionRequestVisitService.cacheVisitCount();
        Map<String, Object> visitCount = jyeooQuestionRequestVisitService.getVisitCount();
        Map<String, Object> visitQuantityToday = getVisitQuantityToday();
        
        long simpleCount = 0;
        long detailCount = 0;
        
        if (MapUtils.isNotEmpty(visitCount)) {
            simpleCount += MapUtil.getLong(visitCount, "simpleCount");
            detailCount += MapUtil.getLong(visitCount, "detailCount");
        }
        if (MapUtils.isNotEmpty(visitQuantityToday)) {
            simpleCount += MapUtil.getLong(visitQuantityToday, "simpleCount");
            detailCount += MapUtil.getLong(visitQuantityToday, "detailCount");
        }
        
        return MapUtil.of("simpleCount", simpleCount, "detailCount", detailCount);
    }
    
    /**
     * 获取当天总的访问量
     * @return simpleCount detailCount createDateString(createDateString 2020-07-13)
     */
    public Map<String, Object> getVisitQuantityToday() {
        Map<String, Object> params = new HashMap<>();
        LocalDate todayLocalDate = LocalDate.now();
        // set startDateTimeString endDateTimeString
        setStartDateTimeString(params, todayLocalDate);
        setEndDateTimeString(params, todayLocalDate);
        
        List<Map<String, Object>> visitQuantityList = getVisitQuantityGroupByDate(params);
        if (CollectionUtils.isEmpty(visitQuantityList)) {
            return null;
        }
        return visitQuantityList.get(0);
    }
    
    /**
     * 获取指定的开始日期到昨天的访问数据
     * @param params
     *     [startEpochDay]   指定的开始时间 可以不提供
     * @return simpleCount detailCount createDateString(createDateString 2020-07-13)
     */
    public List<Map<String, Object>> getVisitQuantityGroupByDateForCache(Map<String, Object> params) {
        if (ObjectUtil.isLong(params.get("startEpochDay"))) {
            long startEpochDay = MapUtil.getLong(params, "startEpochDay");
            LocalDate startLocalDate = LocalDate.ofEpochDay(startEpochDay);
            setStartDateTimeString(params, startLocalDate);
        }
        
        // 昨天 23:59:59
        LocalDate yesterdayLocalDate = LocalDate.now().minusDays(1);
        setEndDateTimeString(params, yesterdayLocalDate);
        
        return getVisitQuantityGroupByDate(params);
    }
    
    /**
     * 获取访问量数据 以userId为维度 调用者再拿userId去查school进行分组
     * @param params  [startDateTimeString] [endDateTimeString]
     * @return simpleCount detailCount createDateString(createDateString 2020-07-13)
     */
    public List<Map<String, Object>> getVisitQuantityGroupByDate(Map<String, Object> params) {
        setQueryRequestTypeValue(params);
        return tikuRepository.selectList("JyeooQuestionRequestLogMapper.selectVisitQuantityGroupByDate", params);
    }
    
    /**
     * 菁优网统计访问量
     * 1. 筛选项: 当日(默认) 上月、本月、上周、本周、当日、自定义(具体到天)
     * 2. 统计维度: 学校 不以t_jyeoo_user t_jyeoo_school为准，需要跨库sys_user t_teacher t_school
     *                  获取不到的计入懂你学校(区域题库管理员等，可能存在的原用户现在找不到学校的情况)
     * 3. 统计信息: 题干访问量 解析访问量
     * @param params
     *   [startTimestamp] 时间开始时间戳 会被转换为天
     *   [endTimestamp]   时间结束时间戳 会被转换为天
     *   [pageSize pageNo currentIndex]  分页支持
     *   [sortField]     排序支持 排序字段 simpleCount(default)题干 detailCount解析
     *   [sortType]      排序支持 排序方式 DESC(default)降序        ASC升序
     * @return count list[{"schoolId", "schoolName", "simpleCount", "detailCount"},...]
     */
    public Map<String, Object> getStatisticsVisit(Map<String, Object> params) {
        // 第一个数据永远是全部 不管你分不分页 不管是否查到数据
        Map<String, Object> all = new HashMap<>(4);
        all.put("schoolId", 0);
        all.put("schoolName", "全部");
        List<Map<String, Object>> list = new ArrayList<>();
        list.add(all);
        Map<String, Object> result = new HashMap<>(2);
        result.put("list", list);
        
        List<Map<String, Object>> userVisitInfoList = getVisitQuantityGroupByUserId(params);
        if (CollectionUtils.isEmpty(userVisitInfoList)) {
            result.put("count", 0);
            all.put("simpleCount", 0);
            all.put("detailCount", 0);
            return result;
        }
        List<Long> userIdList = userVisitInfoList.stream()
                .map(item -> MapUtil.getLong(item, "userId"))
                .collect(Collectors.toList());
        
        // schoolInfoList.size < userIdList.size  因为userIdList不一定全部都是老师
        List<Map<String, Object>> schoolInfoList = baseSchoolService.getSchoolInfoByTeacherUserIdList(userIdList);
        // userId -> schoolInfo
        Map<Long, Map<String, Object>> userIdMapSchoolInfo = schoolInfoList.stream()
                .collect(toMap(item -> MapUtil.getLong(item, "userId"), item -> item));
        
        // 填充user的学校信息
        // userId  teacher                    schoolInfo
        // userId  not teacher or not exists  填充-1懂你学校
        for (Map<String, Object> userVisitInfo : userVisitInfoList) {
            long userId = MapUtil.getLong(userVisitInfo, "userId");
            Map<String, Object> schoolInfo = userIdMapSchoolInfo.get(userId);
            if (MapUtils.isNotEmpty(schoolInfo)) {
                MapUtil.copy(schoolInfo, userVisitInfo, "schoolId", "schoolName");
            } else {
                userVisitInfo.put("schoolId", -1L);
                userVisitInfo.put("schoolName", "懂你学校");
            }
        }
        
        // schoolId -> user visit count info
        Map<Long, List<Map<String, Object>>> schoolIdMapUserVisitCountList = userVisitInfoList.stream()
                .collect(groupingBy(item -> MapUtil.getLong(item, "schoolId")));
        
        // 学校的访问数据   将学校下的用户的访问数据累加
        int simpleCountAll = 0;
        int detailCountAll = 0;
        List<Map<String, Object>> schoolVisitInfoList = new ArrayList<>();
        for (Map.Entry<Long, List<Map<String, Object>>> entry : schoolIdMapUserVisitCountList.entrySet()) {
            int simpleCount = 0;
            int detailCount = 0;
            List<Map<String, Object>> userVisitList = entry.getValue();
            for (Map<String, Object> userVisit : userVisitList) {
                simpleCount += MapUtil.getInt(userVisit, "simpleCount");
                detailCount += MapUtil.getInt(userVisit, "detailCount");
            }
            Map<String, Object> schoolVisitInfo = new HashMap<>();
            schoolVisitInfo.put("schoolId", entry.getKey());
            schoolVisitInfo.put("schoolName", userVisitList.get(0).get("schoolName"));
            schoolVisitInfo.put("simpleCount", simpleCount);
            schoolVisitInfo.put("detailCount", detailCount);
            schoolVisitInfoList.add(schoolVisitInfo);
    
            simpleCountAll += simpleCount;
            detailCountAll += detailCount;
        }
        all.put("simpleCount", simpleCountAll);
        all.put("detailCount", detailCountAll);
    
        result.put("count", schoolVisitInfoList.size());
        
        // 排序
        setSortParams(params);
        String firstSortField = params.get("firstSortField").toString();
        String secondSortField = params.get("secondSortField").toString();
        String sortType = params.get("sortType").toString();
        if ("ASC".equalsIgnoreCase(sortType)) {
            schoolVisitInfoList.sort(Comparator
                    .comparing(o -> MapUtil.getLong((Map<String, Object>) o, firstSortField))
                    .thenComparing(o -> MapUtil.getLong((Map<String, Object>) o, secondSortField))
                    .thenComparing(o -> MapUtil.getLong((Map<String, Object>) o, "schoolId")));
        } else {
            schoolVisitInfoList.sort(Comparator
                    .comparing(o -> MapUtil.getLong((Map<String, Object>) o, firstSortField), Comparator.reverseOrder())
                    .thenComparing(o -> MapUtil.getLong((Map<String, Object>) o, secondSortField), Comparator.reverseOrder())
                    .thenComparing(o -> MapUtil.getLong((Map<String, Object>) o, "schoolId")));
        }
    
        // 分页
        Long pageSize = MapUtil.getLongNullable(params, "pageSize");
        Long currentIndex = MapUtil.getLongNullable(params, "currentIndex");
        if (pageSize != null && currentIndex != null) {
            schoolVisitInfoList = schoolVisitInfoList.stream()
                    .skip(currentIndex)
                    .limit(pageSize)
                    .collect(Collectors.toList());
        }
        
        list.addAll(schoolVisitInfoList);
        return result;
    }
    
    /**
     * 菁优网统计访问量导出 无视分页 使用默认排序
     * @param params {@link #getStatisticsVisit(Map)}
     * @return 导出文件url
     */
    public String exportStatisticsVisit(Map<String, Object> params) {
        params.remove("pageNo");
        params.remove("pageSize");
        params.remove("currentIndex");
        params.remove("sortField");
        params.remove("sortType");
        Map<String, Object> statisticsVisit = getStatisticsVisit(params);
        List<Map<String, Object>> list = (List<Map<String, Object>>) statisticsVisit.get("list");
        SimpleExcelHeader headers = new SimpleExcelHeader(
                Arrays.asList("index", "schoolId", "schoolName", "simpleCount", "detailCount"),
                Arrays.asList("序号", "学校编号", "学校名称", "题干访问数", "解析访问数")
        );
        ExcelReport report = new SimpleExcelReport(list, headers);
        return report.exportToFileStorage("菁优网访问统计");
    }
    
    /**
     * 获取访问量数据 以userId为维度 调用者再拿userId去查school进行分组
     * @param params
     *    [startTimestamp] 时间开始时间戳 会被转换为天
     *    [endTimestamp]   时间结束时间戳 会被转换为天
     * @return userId  simpleCount  detailCount
     */
    private List<Map<String, Object>> getVisitQuantityGroupByUserId(Map<String, Object> params) {
        setQueryRequestTypeValue(params);
        setVisitQueryDateString(params);
        return tikuRepository.selectList("JyeooQuestionRequestLogMapper.selectVisitQuantityGroupByUserId", params);
    }
    
    /**
     * 设置查询用的 requestTypeSimple requestTypeDetail
     */
    private void setQueryRequestTypeValue(Map<String, Object> params) {
        if (params.get("requestTypeSimple") == null) {
            params.put("requestTypeSimple", DictUtil.getDictValue(REQUEST_TYPE_EN_NAME, REQUEST_TYPE_KEY_SIMPLE));
        }
        if (params.get("requestTypeDetail") == null) {
            params.put("requestTypeDetail", DictUtil.getDictValue(REQUEST_TYPE_EN_NAME, REQUEST_TYPE_KEY_DETAIL));
        }
    }
    
    /**
     * 设置查询用的 [startDateTimeString endDateTimeString)
     *    例: startDateTimeString  2020-07-23 00:00:00
     *    例: endDateTimeString    2020-07-23 23:59:59
     * @param params
     *     [startTimestamp] 时间开始时间戳 时间开始时间戳 会被转换为天的维度
     *     [endTimestamp]   时间结束时间戳 时间结束时间戳 会被转换为天的维度
     *     如果未传递，设置当前天
     */
    private void setVisitQueryDateString(Map<String, Object> params) {
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate nowLocalDate = LocalDate.now();
        if (params.get("startDateTimeString") == null) {
            LocalDate startLocalDate;
            if (ObjectUtil.isLong(params.get("startTimestamp"))) {
                long startTimestamp = MapUtil.getLong(params, "startTimestamp");
                startLocalDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimestamp), zoneId).toLocalDate();
            } else {
                startLocalDate = nowLocalDate;
            }
            setStartDateTimeString(params, startLocalDate);
        }
        if (params.get("endDateTimeString") == null) {
            LocalDate endLocalDate;
            if (ObjectUtil.isLong(params.get("endTimestamp"))) {
                long endTimestamp = MapUtil.getLong(params, "endTimestamp");
                endLocalDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(endTimestamp), zoneId).toLocalDate();
            } else {
                endLocalDate = nowLocalDate;
            }
            setEndDateTimeString(params, endLocalDate);
        }
    }
    
    /**
     * 设置 startDateTimeString
     * @param params    set startDateTimeString 例 2020-07-23 00:00:00
     * @param localDate localDate
     */
    private void setStartDateTimeString(Map<String, Object> params, LocalDate localDate) {
        LocalTime startLocalTime = LocalTime.of(0, 0, 0);
        setQueryDateTimeString(params, localDate, startLocalTime, "startDateTimeString");
    }
    
    /**
     * 设置 endDateTimeString
     * @param params    set endDateTimeString 例 2020-07-23 23:59:59
     * @param localDate localDate
     */
    private void setEndDateTimeString(Map<String, Object> params, LocalDate localDate) {
        LocalTime endLocalTime = LocalTime.of(23, 59, 59);
        setQueryDateTimeString(params, localDate, endLocalTime, "endDateTimeString");
    }
    
    /**
     * 设置 fieldName 为时间查询字段
     * @param params    set fieldName 例 2020-07-23 00:00:00
     * @param localDate 日期
     * @param localTime 时间
     * @param fieldName 设置的字段key
     */
    private void setQueryDateTimeString(Map<String, Object> params,
                                        LocalDate localDate,
                                        LocalTime localTime,
                                        String fieldName) {
        LocalDateTime endLocalDateTime = LocalDateTime.of(localDate, localTime);
        Date endDate = Date.from(endLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
        String endDateTimeString = DateUtil.formatDateTime(endDate);
        params.put(fieldName, endDateTimeString);
    }
    
    /**
     * 设置查询用的排序字段及排序方式
     * set firstSortField secondSortField sortType
     */
    private void setSortParams(Map<String, Object> params) {
        if ("detailCount".equals(params.get("sortType"))) {
            params.put("firstSortField", "detailCount");
            params.put("secondSortField", "simpleCount");
        } else {
            params.put("firstSortField", "simpleCount");
            params.put("secondSortField", "detailCount");
        }
    
        String sortType = Optional.ofNullable(params.get("sortType")).map(Object::toString).orElse("");
        if ("ASC".equalsIgnoreCase(sortType)) {
            params.put("sortType", "ASC");
        } else {
            params.put("sortType", "DESC");
        }
    }
    
//    /**
//     * 菁优网计量数据
//     *     随着日志量的增大，会有性能问题
//     *     选定时间不会对开始时间之前的数据进行去重
//     * @param params
//     *   [startTimestamp] 时间开始时间戳
//     *   [endTimestamp]   时间结束时间戳
//     *   [pageSize pageNo currentIndex]  分页支持
//     *   [sortField sortType]      排序支持
//     *        sortField: simpleCount(default)题干 detailCount解析
//     *        sortType : DESC(default)   ASC
//     * @return count list[{"schoolId", "schoolName", "simpleCount", "detailCount"},...]
//     */
//    public Map<String, Object> getStatisticsPrice(Map<String, Object> params) {
//        Map<String, Object> all = new HashMap<>();
//        all.put("schoolId", 0);
//        all.put("schoolName", "全部");
//        all.put("schoolGroupName", "-");
//
//        List<Map<String, Object>> list = new ArrayList<>();
//        list.add(all);
//
//        Map<String, Object> result = new HashMap<>();
//        result.put("list", list);
//
//        long priceQuantityGroupBySchoolCount = getPriceQuantityGroupBySchoolCount(params);
//        result.put("count", priceQuantityGroupBySchoolCount);
//
//        if (priceQuantityGroupBySchoolCount == 0) {
//            all.put("simpleCount", 0);
//            all.put("detailCount", 0);
//            return result;
//        }
//
//        all.putAll(getPriceQuantityAll(params));
//        list.addAll(getPriceQuantityGroupBySchool(params));
//        return result;
//    }
//
//    /**
//     * 导出
//     * @param params
//     * @return
//     */
//    public String exportStatisticsPrice(Map<String, Object> params) {
//        params.remove("pageNo");
//        params.remove("pageSize");
//        params.remove("currentIndex");
//        Map<String, Object> statisticsPrice = getStatisticsPrice(params);
//        List<Map<String, Object>> list = (List<Map<String, Object>>) statisticsPrice.get("list");
//
//        list.forEach(item -> {
//            if (ObjectUtil.isValueEquals(item.get("schoolName"), item.get("schoolGroupName"))) {
//                item.put("schoolGroupName", "-");
//            }
//        });
//        SimpleExcelHeader headers = new SimpleExcelHeader(
//                Arrays.asList("index", "schoolId", "schoolName", "schoolGroupName", "simpleCount", "detailCount"),
//                Arrays.asList("序号", "学校编号", "学校名称", "学校分组名称(菁优注册学校名称)", "题干计量数", "解析计量数")
//        );
//        ExcelReport report = new SimpleExcelReport(list, headers);
//        return report.exportToFileStorage("菁优网计量统计");
//    }
//
//    /**
//     * 获取所有的计价量
//     * @param params
//     *     [startTimestamp] 时间开始时间戳
//     *     [endTimestamp]   时间结束时间戳
//     * @return simpleCount detailCount
//     */
//    private Map<String, Object> getPriceQuantityAll(Map<String, Object> params) {
//        setPriceQueryDateString(params);
//        setQueryRequestTypeValue(params);
//        return tikuRepository.selectOne("JyeooQuestionRequestLogMapper.selectPriceQuantityAll", params);
//    }
//
//    /**
//     * 获取学校计价量的学校数量
//     * @param params
//     *    [startTimestamp] 时间开始时间戳
//     *    [endTimestamp]   时间结束时间戳
//     * @return 学校计价量的学校数量
//     */
//    private long getPriceQuantityGroupBySchoolCount(Map<String, Object> params) {
//        setPriceQueryDateString(params);
//        return tikuRepository.selectOne("JyeooQuestionRequestLogMapper.selectPriceQuantityGroupBySchoolCount", params);
//    }
//
//    /**
//     * 获取学校计价量的学校数量
//     * @param params
//     *   [startTimestamp]          时间开始时间戳
//     *   [endTimestamp]            时间结束时间戳
//     *   [pageSize pageNo offset]  分页支持
//     *   [sortField sortType]      排序支持
//     *          sortField: simpleCount(default)题干 detailCount解析
//     *          sortType : DESC(default)   ASC
//     * @return
//     *   schoolId        学校id 如果对应不上为-1 肯定是数据出了问题
//     *   schoolName      学校名称 t_school.school_name 不一定与t_school的一致，比如t_school的更改了，找不到为 '[ERROR]unknown school'
//     *   schoolGroupName 学校名称 t_school.school_group_name 不一定与t_school的一致，比如t_school的更改了，找不到为 '[ERROR]unknown school group'
//     *   simpleCount     题干计量
//     *   detailCount     解析计量
//     */
//    private List<Map<String, Object>> getPriceQuantityGroupBySchool(Map<String, Object> params) {
//        setPriceQueryDateString(params);
//        setQueryRequestTypeValue(params);
//        setSortParams(params);
//        return tikuRepository.selectList("JyeooQuestionRequestLogMapper.selectPriceQuantityGroupBySchool", params);
//    }
//
//    /**
//     * 设置查询用的 [startDateTimeString endDateTimeString)
//     * @param params
//     *     [startTimestamp] 时间开始时间戳
//     *     [endTimestamp]   时间结束时间戳
//     */
//    private void setPriceQueryDateString(Map<String, Object> params) {
//        if (params.get("startDateTimeString") == null && params.get("startTimestamp") != null) {
//            long startTimestamp = MapUtil.getLong(params, "startTimestamp");
//            Date startDate = new Date(startTimestamp);
//            String startDateTimeString = DateUtil.formatDateTime(startDate);
//            params.put("startDateTimeString", startDateTimeString);
//        }
//        if (params.get("endDateTimeString") == null && params.get("endTimestamp") != null) {
//            long startTimestamp = MapUtil.getLong(params, "endTimestamp");
//            Date endDate = new Date(startTimestamp);
//            String endDateTimeString = DateUtil.formatDateTime(endDate);
//            params.put("endDateTimeString", endDateTimeString);
//        }
//    }
    
    // --------------------------------------------------------------------------------------- 插入
    
    /**
     * 插入日志 题干
     * @param userId  userId
     * @param jyeooQuestionId 菁优网的questionId  2021-04-01起废弃
     * @param jyeooQuestionSid 菁优的questionSid
     */
    public void insertRequestLogSimple(long userId, String jyeooQuestionId, String jyeooQuestionSid) {
        insertRequestLog(userId, REQUEST_TYPE_KEY_SIMPLE, jyeooQuestionId, jyeooQuestionSid);
    }
    
    /**
     * 插入日志 题干
     * @param userId  userId
     * @param jyeooQuestionIdInfo id 菁优网的questionId 2021-04-01起废弃
     *                            sid 菁优网的questionSid
     */
    public void insertRequestLogSimple(long userId, Map<String, String> jyeooQuestionIdInfo) {
        insertRequestLog(userId, REQUEST_TYPE_KEY_SIMPLE, jyeooQuestionIdInfo);
    }
    
    /**
     * 插入日志 题干
     * @param userId  userId
     * @param jyeooQuestionIdInfoList 菁优网的questionIdList
     *                                id 菁优网的questionId 2021-04-01起废弃
     *                                sid 菁优网的questionSid
     */
    public void insertRequestLogSimple(long userId, List<Map<String, String>> jyeooQuestionIdInfoList) {
        insertRequestLog(userId, REQUEST_TYPE_KEY_SIMPLE, jyeooQuestionIdInfoList);
    }
    
    /**
     * 插入日志 解析
     * @param userId  userId
     * @param jyeooQuestionId 菁优网的questionId  2021-04-01起废弃
     * @param jyeooQuestionSid 菁优的questionSid
     *                         id 菁优网的questionId 2021-04-01起废弃
     *                         sid 菁优网的questionSid
     */
    public void insertRequestLogDetail(long userId, String jyeooQuestionId, String jyeooQuestionSid) {
        insertRequestLog(userId, REQUEST_TYPE_KEY_DETAIL, jyeooQuestionId, jyeooQuestionSid);
    }
    
    /**
     * 插入日志 解析
     * @param userId  userId
     * @param jyeooQuestionIdInfo id 菁优网的questionId 2021-04-01起废弃
     *                            sid 菁优网的questionSid
     */
    public void insertRequestLogDetail(long userId, Map<String, String> jyeooQuestionIdInfo) {
        insertRequestLog(userId, REQUEST_TYPE_KEY_DETAIL, jyeooQuestionIdInfo);
    }
    
    /**
     * 插入日志 解析
     * @param userId  userId
     * @param jyeooQuestionIdList 菁优网的questionIdList
     *                            id 菁优网的questionId 2021-04-01起废弃
     *                            sid 菁优网的questionSid
     */
    public void insertRequestLogDetail(long userId, List<Map<String, String>> jyeooQuestionIdList) {
        insertRequestLog(userId, REQUEST_TYPE_KEY_DETAIL, jyeooQuestionIdList);
    }
    
    /**
     * 插入日志
     * @param userId userId
     * @param jyeooQuestionRequestTypeKey 请求类型的key  题干 / 解析 见 dict.jyeooQuestionRequestType / detail/simple
     * @param jyeooQuestionId 菁优网的questionId  2021-04-01起废弃
     * @param jyeooQuestionSid 菁优的questionSid
     */
    private void insertRequestLog(long userId, String jyeooQuestionRequestTypeKey, String jyeooQuestionId, String jyeooQuestionSid) {
        Map<String, String> jyeooQuestionIdInfo = new HashMap<>();
        jyeooQuestionIdInfo.put("id", jyeooQuestionId);
        jyeooQuestionIdInfo.put("sid", jyeooQuestionSid);
        insertRequestLog(userId, jyeooQuestionRequestTypeKey, jyeooQuestionIdInfo);
    }
    
    /**
     * 插入日志
     * @param userId userId
     * @param jyeooQuestionRequestTypeKey 请求类型的key  题干 / 解析 见 dict.jyeooQuestionRequestType / detail/simple
     * @param jyeooQuestionIdInfo 菁优网的questionIdInfo
     *                            id 菁优网的questionId 2021-04-01起废弃
     *                            sid 菁优网的questionSid
     */
    private void insertRequestLog(long userId, String jyeooQuestionRequestTypeKey, Map<String, String> jyeooQuestionIdInfo) {
        insertRequestLog(userId, jyeooQuestionRequestTypeKey, Collections.singletonList(jyeooQuestionIdInfo));
    }
    
    /**
     * 插入日志
     * @param userId userId
     * @param jyeooQuestionRequestTypeKey 请求类型的key  题干 / 解析 见 dict.jyeooQuestionRequestType / detail/simple
     * @param jyeooQuestionIdInfoList 菁优网的questionIdInfoList
     *                                id 菁优网的questionId 2021-04-01起废弃
     *                                sid 菁优网的questionSid
     */
    private void insertRequestLog(long userId, String jyeooQuestionRequestTypeKey, List<Map<String, String>> jyeooQuestionIdInfoList) {
        if (CollectionUtils.isEmpty(jyeooQuestionIdInfoList)) {
            return;
        }
        int jyeooQuestionRequestType = DictUtil.getDictValue(REQUEST_TYPE_EN_NAME, jyeooQuestionRequestTypeKey);
        Map<String, Object> insertParams = new HashMap<>();
        insertParams.put("userId", userId);
        insertParams.put("requestType", jyeooQuestionRequestType);
        insertParams.put("jyeooQuestionIdInfoList", jyeooQuestionIdInfoList);
        ParamsUtil.setCurrentTime(insertParams);
        tikuRepository.insert("JyeooQuestionRequestLogMapper.insertRequestLog", insertParams);
    }
}
