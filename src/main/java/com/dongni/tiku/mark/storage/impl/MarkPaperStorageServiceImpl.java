package com.dongni.tiku.mark.storage.impl;

import com.dongni.tiku.mark.storage.AbstractMarkPaperStorageService;
import com.dongni.tiku.own.service.OwnPaperService;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 普通业务 mongo.tiku.paper 操作
 * @date 2022年02月23日
 */
@Service
public class MarkPaperStorageServiceImpl extends AbstractMarkPaperStorageService {

    @Autowired
    private OwnPaperService ownPaperService;

    @Override
    public Document get(long paperId) {
        return ownPaperService.getPaper(paperId);
    }

    @Override
    public Document getNullable(long paperId) {
        return ownPaperService.getPaperNullable(paperId);
    }

    @Override
    public List<String> getQuestionIdsByPaper(Long paperId) {
        return ownPaperService.getQuestionIdsByPaper(paperId);
    }

    @Override
    public List<String> getQuestionIdsByPaper(Long paperId, Long subCourseId) {
        return ownPaperService.getQuestionIdsByPaper(paperId, subCourseId);
    }

    @Override
    public void update(long paperId, Bson updateBson) {
        ownPaperService.updatePaper(paperId, updateBson);
    }
}
