package com.dongni.tiku.mark.storage;

import org.bson.Document;
import org.bson.conversions.Bson;

import java.util.List;

/**
 * <AUTHOR>
 * @description  委托录题试题存储 普通业务 -> paper 金卷业务 -> jinjuanPaper
 * @date 2022年02月23日
 */

public abstract class AbstractMarkPaperStorageService {

    /**
     * 查询paper
     * @param paperId 试卷id
     * @return 试卷详细信息
     */
    abstract public Document get(long paperId);

    /**
     * 查询paper
     * @param paperId 试卷id
     * @return 试卷详细信息 or null
     */
    abstract public Document getNullable(long paperId);


    /**
     * 获取试卷对应的所有questionId列表
     * @param paperId 试卷ID
     * @return 试题ID列表
     */
    public abstract List<String> getQuestionIdsByPaper(Long paperId);

    /**
     * 获取试卷对应课程的questionId列表
     * @param paperId 试卷ID
     * @param subCourseId 课程ID
     * @return 试题ID列表
     */
    public abstract List<String> getQuestionIdsByPaper(Long paperId, Long subCourseId);

    /**
     * 更新试卷内容
     * @param paperId 试卷id
     * @param updateBson 更新数据
     */
    abstract public void update(long paperId, Bson updateBson);
}
