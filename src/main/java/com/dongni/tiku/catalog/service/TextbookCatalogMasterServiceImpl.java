package com.dongni.tiku.catalog.service;

import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import com.dongni.common.auth.DongniClient;
import com.dongni.common.distribution.service.IMasterLocalExecuteService;
import com.dongni.common.master.version.service.BaseMasterThirdService;
import com.dongni.common.utils.MongoCourseUtil;
import com.dongni.common.utils.MongoUtil;
import com.dongni.common.utils.TreeCodeUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.TextbookCatalogMasterManager;
import com.dongni.tiku.manager.impl.TextbookMasterManager;
import com.dongni.tiku.manager.impl.TextbookVersionMasterManager;
import com.dongni.tiku.own.service.TikuBaseDataVersionService;
import com.dongni.tiku.own.util.MongoDataConvertUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * 2023/03/10
 */
@Service
public class TextbookCatalogMasterServiceImpl implements IMasterLocalExecuteService {
    
    private static final Logger log = LoggerFactory.getLogger(TextbookCatalogMasterServiceImpl.class);
    
    @Autowired
    private TextbookVersionMasterManager textbookVersionMasterManager;
    @Autowired
    private TextbookMasterManager textbookMasterManager;
    @Autowired
    private TextbookCatalogMasterManager textbookCatalogMasterManager;
    @Autowired
    private TikuBaseDataVersionService tikuBaseDataVersionService;
    @Autowired
    private TextbookCatalogMasterThirdFactory textbookCatalogMasterThirdFactory;
    @Autowired
    private CourseServiceImpl courseService;
    @Autowired
    protected DongniClient dongniClient;
    
    public static String getDataVersionBusinessType() {
        return "textbookCatalog";
    }
    public static String getDataVersionBusinessKey(long courseId) {
        return String.valueOf(courseId);
    }
    public static String getDataVersionBusinessDesc(long courseId) {
        return "教材章节-courseId:" + courseId;
    }
    
    /**
     * 获取课程的教材章节信息
     * @param params courseId 课程id
     * @return 教材章节信息
     */
    public Map<String, List<Map<String, Object>>> getTextbookCatalogMasterListMap(Map<String, Object> params) {
        Verify.of(params).isValidId("courseId").verify();
        return get(
                () -> getTextbookCatalogMasterListMapRemote(params),
                () -> getTextbookCatalogMasterListMapLocal(params)
        );
    }
    
    /**
     * 获取课程的教材章节信息 远程
     * @param params courseId 课程id
     * @return 教材章节信息
     */
    public Map<String, List<Map<String, Object>>> getTextbookCatalogMasterListMapRemote(Map<String, Object> params) {
        String url = getUrl("/tiku/book/catalog/master/course/list/get/dongni/df081fa0-dd60-4025-bbdf-ffebe12b7850/system");
        return dongniClient.post(url, null, params);
    }
    
    /**
     * 获取课程的教材章节信息 本地
     * @param params courseId 课程id
     * @return 教材章节信息
     */
    public Map<String, List<Map<String, Object>>> getTextbookCatalogMasterListMapLocal(Map<String, Object> params) {
        long courseId = MapUtil.getLong(params, "courseId");
        return tikuBaseDataVersionService.executeMasterWithVersionCheck(
                getDataVersionBusinessType(),
                getDataVersionBusinessKey(courseId),
                () -> MapUtil.of(
                        "textbookVersionMasterList", textbookVersionMasterManager.getListMap(courseId),
                        "textbookMasterList", textbookMasterManager.getListMap(courseId),
                        "textbookCatalogMasterList", textbookCatalogMasterManager.getListMap(courseId)
                )
        );
    }
    
    /**
     * 同步课程的教材章节母版信息
     * @param params userId userName userType
     *               courseId 必须在syncCourseIdSet的范围内，会分发到各个平台进行处理
     */
    public void syncByCourseId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("userId")
                .isNotBlank("userName")
                .isInteger("userType")
                .verify();
        long courseId = MapUtil.getLong(params, "courseId");
        log.info("{} 开始: {}: {}", textbookCatalogMasterThirdFactory.getBusinessName(), textbookCatalogMasterThirdFactory.getKeyDesc(), courseId);
        Map<String, Object> courseInfo = courseService.getCourseDetailMustExist(params);
        BaseTextbookCatalogMasterThirdService bookCatalogMasterThirdService = textbookCatalogMasterThirdFactory.getService(courseId);
        textbookCatalogByCourseMustBeNotExists(courseId);
        String bookCatalogMasterLock = JedisUtil.getKey("bookCatalogMasterLock", String.valueOf(courseId));
        JedisTemplate.lockExecute(bookCatalogMasterLock, 0, () -> {
            String dataVersionBusinessType = getDataVersionBusinessType();
            String dataVersionBusinessKey = getDataVersionBusinessKey(courseId);
            String dataVersionBusinessDesc = getDataVersionBusinessDesc(courseId);
            tikuBaseDataVersionService.masterUpdating(dataVersionBusinessType, dataVersionBusinessKey, dataVersionBusinessDesc);
            
            textbookCatalogByCourseMustBeNotExists(courseId);
            MongoCourseUtil.CourseOperate courseOperate = new MongoCourseUtil.CourseOperate(
                    MapUtil.getLong(params, "userId"),
                    MapUtil.getTrim(params, "userName"),
                    MapUtil.getInt(courseInfo, "stage"),
                    MapUtil.getLong(courseInfo, "courseId"),
                    MapUtil.getString(courseInfo, "courseName")
            );
            
            List<BaseTextbookCatalogMasterThirdService.TextbookVersionDTO> bookVersionList = bookCatalogMasterThirdService.getTextbookCatalogInfo(courseInfo);
            String treeCode = String.valueOf(courseId);
            saveTextbookVersionAndOthers(courseOperate, treeCode, bookVersionList);
            
            tikuBaseDataVersionService.masterUpdated(dataVersionBusinessType, dataVersionBusinessKey, true);
            log.info("{} 结束: {}: {}", textbookCatalogMasterThirdFactory.getBusinessName(), textbookCatalogMasterThirdFactory.getKeyDesc(), courseId);
            return true;
        });
    }
    
    /**
     * 课程的教材章节必须不存在
     * @param courseId 课程id
     */
    private void textbookCatalogByCourseMustBeNotExists(long courseId) {
        // 如果 courseId 的教材章节已经存在，则不进行初始化，需要人为介入
        if (textbookCatalogMasterManager.exist(courseId)
            || textbookMasterManager.exist(courseId)
            || textbookVersionMasterManager.exist(courseId)) {
            tikuBaseDataVersionService.masterUpdated(getDataVersionBusinessType(), getDataVersionBusinessKey(courseId), false);
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "课程已经有初始化的教材章节母版了: " + courseId);
        }
    }
    
    /**
     * 该方法主要插入 textbookVersionMaster
     * @param courseOperate  课程信息及操作信息
     * @param parentTreeCode treeCode  "22" "22.0" "22.3.1" 对于每一层，都会在父级的code加.${index}
     * @param textbookVersionDTOList 教材版本列表
     */
    private void saveTextbookVersionAndOthers(MongoCourseUtil.CourseOperate courseOperate,
                                              String parentTreeCode,
                                              List<BaseTextbookCatalogMasterThirdService.TextbookVersionDTO> textbookVersionDTOList) {
        if (CollectionUtils.isEmpty(textbookVersionDTOList)) { return; }
        
        int textbookVersionSort = 0;
        for (BaseTextbookCatalogMasterThirdService.TextbookVersionDTO textbookVersionDTO : textbookVersionDTOList) {
            textbookVersionSort++;
            String treeCode = parentTreeCode + TreeCodeUtil.DELIMITER + textbookVersionSort;
            
            Map<String, Object> textbookVersion = textbookVersionDTO.getTextbookVersion();
            textbookVersion.put("stage", courseOperate.stage);
            textbookVersion.put("courseId", courseOperate.courseId);
            textbookVersion.put("courseName", courseOperate.courseName);
            textbookVersion.put("creatorId", courseOperate.userId);
            textbookVersion.put("creatorName", courseOperate.userName);
            textbookVersion.put("createDateTime", courseOperate.currentDateTime);
            textbookVersion.put("modifierId", courseOperate.userId);
            textbookVersion.put("modifierName", courseOperate.userName);
            textbookVersion.put("modifyDateTime", courseOperate.currentDateTime);
            textbookVersion.put("treeCode", treeCode);
            textbookVersion.put("sort", textbookVersionSort);
    
            Document document = convertDocumentTextbookVersion(textbookVersion);
            textbookVersionMasterManager.insertOne(document);
            String id = MapUtil.getString(document, "_id");
            saveTextbookAndOthers(courseOperate, treeCode, id, textbookVersionDTO.getBookDTOList());
        }
    }
    
    /**
     * 该方法主要插入 textbookMaster
     * @param courseOperate     课程信息及操作信息
     * @param parentTreeCode    treeCode  "22" "22.0" "22.3.1" 对于每一层，都会在父级的code加.${index}
     * @param textbookVersionId textbookVersionMaster._id
     * @param textbookDTOList   教材列表
     */
    private void saveTextbookAndOthers(MongoCourseUtil.CourseOperate courseOperate,
                                       String parentTreeCode,
                                       String textbookVersionId,
                                       List<BaseTextbookCatalogMasterThirdService.TextbookDTO> textbookDTOList) {
        if (CollectionUtils.isEmpty(textbookDTOList)) { return; }
        
        int textbookSort = 0;
        for (BaseTextbookCatalogMasterThirdService.TextbookDTO textbookDTO : textbookDTOList) {
            textbookSort++;
            String treeCode = parentTreeCode + TreeCodeUtil.DELIMITER + textbookSort;
            
            Map<String, Object> textbook = textbookDTO.getTextbook();
            textbook.put("stage", courseOperate.stage);
            textbook.put("courseId", courseOperate.courseId);
            textbook.put("courseName", courseOperate.courseName);
            textbook.put("creatorId", courseOperate.userId);
            textbook.put("creatorName", courseOperate.userName);
            textbook.put("createDateTime", courseOperate.currentDateTime);
            textbook.put("modifierId", courseOperate.userId);
            textbook.put("modifierName", courseOperate.userName);
            textbook.put("modifyDateTime", courseOperate.currentDateTime);
            textbook.put("textbookVersionId", textbookVersionId);
            textbook.put("treeCode", treeCode);
            textbook.put("sort", textbookSort);
            
            Document document = convertDocumentTextbook(textbook);
            textbookMasterManager.insertOne(document);
            String id = MapUtil.getString(document, "_id");
            saveTextbookCatalog(courseOperate, treeCode, id, textbookDTO.getBookCatalogDTOList());
        }
    }
    
    /**
     * 该方法主要插入 textbookCatalogMaster
     * @param courseOperate   课程信息及操作信息
     * @param parentTreeCode  treeCode  "22" "22.0" "22.3.1" 对于每一层，都会在父级的code加.${index}
     * @param textbookId      textbookMaster._id
     * @param textbookCatalogDTOList 教材章节列表
     */
    private void saveTextbookCatalog(MongoCourseUtil.CourseOperate courseOperate,
                                     String parentTreeCode,
                                     String textbookId,
                                     List<BaseTextbookCatalogMasterThirdService.TextbookCatalogDTO> textbookCatalogDTOList) {
        if (CollectionUtils.isEmpty(textbookCatalogDTOList)) {
            return;
        }
        List<Map<String, Object>> bookCatalogTreeList = textbookCatalogDTOList.stream()
                .map(BaseTextbookCatalogMasterThirdService.TextbookCatalogDTO::getTextbookCatalogTree)
                .collect(Collectors.toList());
        saveTextbookCatalog(courseOperate, parentTreeCode, textbookId, null, bookCatalogTreeList);
    }
    
    /**
     * 该方法主要插入 textbookCatalogMaster
     * @param courseOperate           课程信息及操作信息
     * @param parentTreeCode          treeCode  "22" "22.0" "22.3.1" 对于每一层，都会在父级的code加.${index}
     * @param textbookId              textbookMaster._id
     * @param parentId                教材章节父级id
     * @param bookCatalogTreeList     教材章节树形列表
     */
    private void saveTextbookCatalog(MongoCourseUtil.CourseOperate courseOperate,
                                     String parentTreeCode,
                                     String textbookId,
                                     String parentId,
                                     List<Map<String, Object>> bookCatalogTreeList) {
        if (CollectionUtils.isEmpty(bookCatalogTreeList)) {
            return;
        }
        
        int textbookCatalog = 0;
        for (Map<String, Object> bookCatalogTree : bookCatalogTreeList) {
            textbookCatalog++;
            String treeCode = parentTreeCode + TreeCodeUtil.DELIMITER + textbookCatalog;
            List<Map<String, Object>> children = MapUtil.getListMap(bookCatalogTree, "children");
            boolean leaf = CollectionUtils.isEmpty(children);
            
            bookCatalogTree.put("stage", courseOperate.stage);
            bookCatalogTree.put("courseId", courseOperate.courseId);
            bookCatalogTree.put("courseName", courseOperate.courseName);
            bookCatalogTree.put("creatorId", courseOperate.userId);
            bookCatalogTree.put("creatorName", courseOperate.userName);
            bookCatalogTree.put("createDateTime", courseOperate.currentDateTime);
            bookCatalogTree.put("modifierId", courseOperate.userId);
            bookCatalogTree.put("modifierName", courseOperate.userName);
            bookCatalogTree.put("modifyDateTime", courseOperate.currentDateTime);
            bookCatalogTree.put("parentId", parentId);
            bookCatalogTree.put("textbookId", textbookId);
            bookCatalogTree.put("treeCode", treeCode);
            bookCatalogTree.put("sort", textbookCatalog);
            bookCatalogTree.put("leaf", leaf);
    
            Document document = convertDocumentTextbookCatalog(bookCatalogTree);
            textbookCatalogMasterManager.insertOne(document);
            String id = MapUtil.getString(document, "_id");
            saveTextbookCatalog(courseOperate, treeCode, textbookId, id, children);
        }
    }
    
    /**
     * 转换存储文档 教材版本
     *    字段类型转换 字段顺序
     * @param oldDoc 文档数据
     * @return 转换后的文档数据
     */
    public Document convertDocumentTextbookVersion(Map<String, Object> oldDoc) {
        Document newDoc = new Document();
        MongoDataConvertUtil.setIfNewValuePresent(newDoc, oldDoc, "_id", MongoUtil::getMongoId);
        MongoDataConvertUtil.set(newDoc, oldDoc, "stage", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "textbookVersionName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "sort", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "treeCode", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "deleted", MapUtil::getBoolean);
        MongoDataConvertUtil.set(newDoc, oldDoc, BaseMasterThirdService.THIRD_KEY_FIELD, MapUtil::getString);
        textbookCatalogMasterThirdFactory.getDistributionServiceList().forEach(service -> service.convertDocumentTextbookVersion(newDoc, oldDoc));
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "createDateTime", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifyDateTime", MapUtil::getString);
        return newDoc;
    }
    
    /**
     * 转换存储文档 教材
     *    字段类型转换 字段顺序
     * @param oldDoc 文档数据
     * @return 转换后的文档数据
     */
    public Document convertDocumentTextbook(Map<String, Object> oldDoc) {
        Document newDoc = new Document();
        MongoDataConvertUtil.setIfNewValuePresent(newDoc, oldDoc, "_id", MongoUtil::getMongoId);
        MongoDataConvertUtil.set(newDoc, oldDoc, "stage", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "gradeType", MapUtil::getIntNullable);
        MongoDataConvertUtil.set(newDoc, oldDoc, "gradeTerm", MapUtil::getIntNullable);
        MongoDataConvertUtil.set(newDoc, oldDoc, "textbookName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "textbookFullName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "textbookVersionId", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "sort", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "treeCode", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "deleted", MapUtil::getBoolean);
        MongoDataConvertUtil.set(newDoc, oldDoc, BaseMasterThirdService.THIRD_KEY_FIELD, MapUtil::getString);
        textbookCatalogMasterThirdFactory.getDistributionServiceList().forEach(service -> service.convertDocumentTextbook(newDoc, oldDoc));
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "createDateTime", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifyDateTime", MapUtil::getString);
        return newDoc;
    }
    
    /**
     * 转换存储文档 教材章节
     *   字段类型转换 字段顺序
     * @param oldDoc 文档数据
     * @return 转换后的文档数据
     */
    public Document convertDocumentTextbookCatalog(Map<String, Object> oldDoc) {
        Document newDoc = new Document();
        MongoDataConvertUtil.setIfNewValuePresent(newDoc, oldDoc, "_id", MongoUtil::getMongoId);
        MongoDataConvertUtil.set(newDoc, oldDoc, "stage", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "textbookCatalogName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "parentId", MapUtil::getStringNullable);
        MongoDataConvertUtil.set(newDoc, oldDoc, "textbookId", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "sort", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "leaf", MapUtil::getBoolean);
        MongoDataConvertUtil.set(newDoc, oldDoc, "treeCode", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "deleted", MapUtil::getBoolean);
        MongoDataConvertUtil.set(newDoc, oldDoc, BaseMasterThirdService.THIRD_KEY_FIELD, MapUtil::getString);
        textbookCatalogMasterThirdFactory.getDistributionServiceList().forEach(service -> service.convertDocumentTextbookCatalog(newDoc, oldDoc));
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "createDateTime", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifyDateTime", MapUtil::getString);
        return newDoc;
    }
}
