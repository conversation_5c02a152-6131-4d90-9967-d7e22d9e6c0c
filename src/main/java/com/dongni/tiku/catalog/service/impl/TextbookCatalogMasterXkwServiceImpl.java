package com.dongni.tiku.catalog.service.impl;

import com.dongni.common.utils.AppendHashMap;
import com.dongni.common.utils.ComparatorEx;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.TreeUtil;
import com.dongni.tiku.catalog.service.BaseTextbookCatalogMasterThirdService;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.util.MongoDataConvertUtil;
import com.dongni.tiku.xkw.service.common.XkwCommonService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * 2023/03/02
 */
@Service
public class TextbookCatalogMasterXkwServiceImpl extends BaseTextbookCatalogMasterThirdService {
    
    public TextbookCatalogMasterXkwServiceImpl() {
        super("xkw-");
    }
    
    @Autowired
    private XkwCommonService xkwCommonService;
    
    private static final Set<Long> SYNC_COURSE_ID_SET = Collections.unmodifiableSet(Stream.of(
                            22, // 小学语文
                            23, // 小学数学
                            24, // 小学英语
                            28, // 小学科学
                            null
                    )
                    .filter(Objects::nonNull)
                    .map(Long::new)
                    .collect(Collectors.toSet())
    );
    
    @Override
    public Set<Long> getKeySet() {
        return SYNC_COURSE_ID_SET;
    }
    
    @Override
    public List<TextbookVersionDTO> getTextbookCatalogInfo(Map<String, Object> courseInfo) {
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfo(courseInfo);
        List<Map<String, Object>> textbookVersionList = getTextbookVersionList(relativeCourseInfo);
        List<TextbookVersionDTO> textbookVersionDTOList = new ArrayList<>();
        for (Map<String, Object> textbookVersion : textbookVersionList) {
            TextbookVersionDTO textbookVersionDTO = new TextbookVersionDTO(textbookVersion);
            textbookVersionDTOList.add(textbookVersionDTO);
            List<TextbookDTO> textbookDTOList = textbookVersionDTO.getBookDTOList();
            List<Map<String, Object>> textbookList = getTextbookList(relativeCourseInfo, textbookVersion);
            for (Map<String, Object> textbook : textbookList) {
                TextbookDTO textbookDTO = new TextbookDTO(textbook);
                textbookDTOList.add(textbookDTO);
                List<TextbookCatalogDTO> textbookCatalogDTOList = textbookDTO.getBookCatalogDTOList();
                List<Map<String, Object>> textbookCatalogTreeList = getTextbookCatalogTreeList(relativeCourseInfo, textbook);
                for (Map<String, Object> textbookCatalogTree : textbookCatalogTreeList) {
                    TextbookCatalogDTO textbookCatalogDTO = new TextbookCatalogDTO(textbookCatalogTree);
                    textbookCatalogDTOList.add(textbookCatalogDTO);
                }
            }
        }
        return textbookVersionDTOList;
    }
    
    @Override
    public void convertDocumentTextbookVersionFields(Map<String, Object> newDoc, Map<String, Object> oldDoc) {
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookVersionId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookVersionName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookVersionYear", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookVersionOrdinal", MapUtil::getInt);
    }
    
    @Override
    public void convertDocumentTextbookFields(Map<String, Object> newDoc, Map<String, Object> oldDoc) {
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwGradeId", MapUtil::getLongNullable);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookTerm", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookVersionId", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookVolume", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookOrdinal", MapUtil::getInt);
    }
    
    @Override
    public void convertDocumentTextbookCatalogFields(Map<String, Object> newDoc, Map<String, Object> oldDoc) {
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookCatalogId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookCatalogName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookCatalogParentId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwTextbookCatalogOrdinal", MapUtil::getInt);
    }
    
    /**
     * 获取懂你课程-学科网课程关联关系
     * @param courseInfo 懂你课程
     * @return 懂你-学科网 课程关联关系
     */
    private Map<String, Object> getRelativeCourseInfo(Map<String, Object> courseInfo) {
        List<Map<String, Object>> courseList = Stream.of(courseInfo).collect(Collectors.toList());
        List<Map<String, Object>> relativeCourseInfoList = xkwCommonService.getRelativeCourseInfoList(courseList);
        if (CollectionUtils.isEmpty(relativeCourseInfoList)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "课程不支持");
        }
        return relativeCourseInfoList.get(0);
    }
    
    
    /**
     * 获取教材版本信息
     * @param relativeCourseInfo {@link #getRelativeCourseInfo(Map)}
     * @return [{}]
     *         "${ITextbookCatalogMasterThirdService.THIRD_KEY_NAME}"="313"
     *         textbookVersionName=北师大版
     *         xkwCourseId=26,
     *         xkwCourseName=高中语文,
     *         xkwTextbookVersionId=313
     *         xkwTextbookVersionName=北师大版,
     *         xkwTextbookVersionYear=2005,
     *         xkwTextbookVersionOrdinal=5,
     */
    private List<Map<String, Object>> getTextbookVersionList(Map<String, Object> relativeCourseInfo) {
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        String xkwCourseName = MapUtil.getString(relativeCourseInfo, "xkwCourseName");
        List<Map<String, Object>> coursesTextbookVersionList = xkwCommonService.getCoursesTextbookVersionList(xkwCourseId);
        coursesTextbookVersionList.sort(ComparatorEx.ascNullLast(item -> MapUtil.getLongNullable(item,"xkwTextbookVersionOrdinal")));
        for (Map<String, Object> textbookVersion : coursesTextbookVersionList) {
            long xkwTextbookVersionId = MapUtil.getLong(textbookVersion, "xkwTextbookVersionId");
            String xkwTextbookVersionName = MapUtil.getTrim(textbookVersion, "xkwTextbookVersionName");
            String thirdKeyWithoutPrefix = String.valueOf(xkwTextbookVersionId);
            putRequireInfoTextbookVersion(textbookVersion, thirdKeyWithoutPrefix, xkwTextbookVersionName, false);
            textbookVersion.putIfAbsent("xkwCourseId", xkwCourseId);
            textbookVersion.put("xkwCourseName", xkwCourseName);
        }
        return coursesTextbookVersionList;
    }
    
    /**
     * 获取教材信息
     * @param relativeCourseInfo {@link #getRelativeCourseInfo(Map)}
     * @param bookVersion {@link #getTextbookVersionList(Map)}
     * @return [{}]
     *         "${ITextbookCatalogMasterThirdService.THIRD_KEY_NAME}"="798"
     *         textbookName=高中语文北师大版必修1
     *         xkwCourseId=26,
     *         xkwCourseName=高中语文,
     *         xkwGradeId=0,
     *         xkwTextbookId=798,
     *         xkwTextbookName=高中语文北师大版必修1,
     *         xkwTextbookVersionId=313,
     *         xkwTextbookVolume=必修1
     *         xkwTextbookTerm=null,
     *         xkwTextbookOrdinal=154,
     */
    private List<Map<String, Object>> getTextbookList(Map<String, Object> relativeCourseInfo, Map<String, Object> bookVersion) {
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        String xkwCourseName = MapUtil.getString(relativeCourseInfo, "xkwCourseName");
        long xkwBookVersionId = MapUtil.getLong(bookVersion, "xkwTextbookVersionId");
        List<Map<String, Object>> textbookList = xkwCommonService.getTextbookList(xkwCourseId, xkwBookVersionId);
        textbookList.sort(ComparatorEx.ascNullLast(item -> MapUtil.getLongNullable(item,"xkwTextbookOrdinal")));
        Map<String, Integer> gradeTermMap = MapUtil.of("LAST", 1, "NEXT", 2);
        for (Map<String, Object> textbook : textbookList) {
            long xkwTextbookId = MapUtil.getLong(textbook, "xkwTextbookId");
            String xkwTextbookName = MapUtil.getTrim(textbook, "xkwTextbookName");
            String xkwTextbookVolume = MapUtil.getTrim(textbook, "xkwTextbookVolume");
            Integer xkwGradeId = MapUtil.getIntNullable(textbook, "xkwGradeId");
            Integer xkwTextbookTerm = gradeTermMap.get(MapUtil.getTrimNullable(textbook, "xkwTextbookTerm"));
            String thirdKeyWithoutPrefix = String.valueOf(xkwTextbookId);
            putRequireInfoTextbook(textbook, thirdKeyWithoutPrefix, xkwTextbookVolume, xkwTextbookName, xkwGradeId, xkwTextbookTerm, false);
            textbook.putIfAbsent("xkwCourseId", xkwCourseId);
            textbook.put("xkwCourseName", xkwCourseName);
        }
        return textbookList;
    }
    
    /**
     * 获取教材的章节信息
     * @param relativeCourseInfo {@link #getRelativeCourseInfo(Map)}
     * @param book {@link #getTextbookList(Map, Map)}
     * @return [{}]
     *         "${ITextbookCatalogMasterThirdService.THIRD_KEY_NAME}"="20443"
     *         textbookCatalogName="第一单元 家国情怀"
     *         children: [{}]
     *         xkwCourseId=26,
     *         xkwCourseName=高中语文,
     *         "xkwTextbookId"             : 798,
     *         "xkwTextbookCatalogId"      : 20443,
     *         "xkwTextbookCatalogName"    : "第一单元 家国情怀",
     *         "xkwTextbookCatalogParentId": 0,
     *         "xkwTextbookCatalogOrdinal" : 0,
     */
    private List<Map<String, Object>> getTextbookCatalogTreeList(Map<String, Object> relativeCourseInfo, Map<String, Object> book) {
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        String xkwCourseName = MapUtil.getString(relativeCourseInfo, "xkwCourseName");
        long xkwTextbookId = MapUtil.getLong(book, "xkwTextbookId");
        List<Map<String, Object>> textbooksCatalogList = xkwCommonService.getTextbooksCatalogList(xkwTextbookId);
        textbooksCatalogList.sort(ComparatorEx.ascNullLast(item -> MapUtil.getLongNullable(item,"xkwTextbookCatalogOrdinal")));
        for (Map<String, Object> textbooksCatalog : textbooksCatalogList) {
            long xkwTextbookCatalogId = MapUtil.getLong(textbooksCatalog, "xkwTextbookCatalogId");
            String xkwTextbookCatalogName = MapUtil.getTrim(textbooksCatalog, "xkwTextbookCatalogName");
            String textbookCatalogName = getTextbookCatalogName(xkwTextbookCatalogId, xkwTextbookCatalogName);
            String thirdKeyWithoutPrefix = String.valueOf(xkwTextbookCatalogId);
            putRequireInfoTextbookCatalog(textbooksCatalog, thirdKeyWithoutPrefix, textbookCatalogName, false, new ArrayList<>());
            textbooksCatalog.putIfAbsent("xkwCourseId", xkwCourseId);
            textbooksCatalog.put("xkwCourseName", xkwCourseName);
        }
        return TreeUtil.list2Tree(textbooksCatalogList, "xkwTextbookCatalogId", "xkwTextbookCatalogParentId", "children");
    }
    
    /***
     * 处理一些名称 教材章节
     * @param xkwTextbookCatalogId   学科网教材章节id
     * @param xkwTextbookCatalogName 学科网教材章节名称
     * @return 懂你教材章节名称
     */
    private String getTextbookCatalogName(long xkwTextbookCatalogId, String xkwTextbookCatalogName) {
        Map<Long, String> xkwTextbookCatalogId2TextbookCatalogName = new AppendHashMap<Long, String>()
                // 小学数学 人教版(xkwTextbookVersionId=557) 四年级下册(xkwTextbookId=2953) "8 统计"
                .append(115836L, "8 平均数与条形统计图")
                // 小学数学 人教版(xkwTextbookVersionId=557) 五年级下册(xkwTextbookId=2955) "打电话"
                .append(187882L, "怎样通知更快")
                ;
        String textbookCatalogName = xkwTextbookCatalogId2TextbookCatalogName.get(xkwTextbookCatalogId);
        return Optional.ofNullable(textbookCatalogName).orElse(xkwTextbookCatalogName);
    }
    
}
