package com.dongni.tiku.student.question.mark.bean.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class StudentQuestionMarkWrongItemEntitySelectDTO {
    private Long studentQuestionMarkWrongItemId; // 学生试题标记错题项ID
    private Long studentQuestionMarkId; // 学生试题标记ID
    private Long studentQuestionMarkItemId; // 学生试题标记试题ID
    private Long studentId; // 学生ID
    private String sourceQuestionId; // 来源的考试试题ID
    private String questionId; // 试题ID
    private Integer belongType; // 试题所属题库（questionBankBelongType）
    private String structureNumber; // 前端展示的题号
    private Long courseId; // 所属课程ID
    private BigDecimal difficulty; // 试题难度
}
