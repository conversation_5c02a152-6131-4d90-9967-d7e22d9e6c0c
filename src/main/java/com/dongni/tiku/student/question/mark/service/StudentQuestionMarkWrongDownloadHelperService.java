package com.dongni.tiku.student.question.mark.service;

import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.exam.wrong.serevice.WrongExamService;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.render.bean.param.TikuRenderQuestionByQuestionTaskCreateParam;
import com.dongni.tiku.render.service.TikuRenderQuestionByQuestionsTaskService;
import com.dongni.tiku.student.question.mark.bean.dto.StudentQuestionMarkInsertDTO;
import com.dongni.tiku.student.question.mark.bean.dto.StudentQuestionMarkItemInsertDTO;
import com.dongni.tiku.student.question.mark.bean.dto.WrongDownloadQuestionDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/6/4 周三 下午 01:18
 * @Version 1.0.0
 */
@Service
public class StudentQuestionMarkWrongDownloadHelperService {
    @Autowired
    private CommonStudentService commonStudentService;
    @Autowired
    private WrongExamService wrongExamService;
    @Autowired
    private StudentQuestionMarkQRCodeService studentQuestionMarkQRCodeService;
    @Autowired
    private StudentQuestionMarkService studentQuestionMarkService;
    @Autowired
    private StudentQuestionMarkItemService studentQuestionMarkItemService;
    @Autowired
    private TikuRenderQuestionByQuestionsTaskService tikuRenderQuestionByQuestionsTaskService;

    /**
     * 获取重定向的二维码链接 - 考后巩固、薄弱点精准提升卷
     *
     * @param fromGuJiaoTong           是否来自鼓教通
     * @param wrongDownloadQuestionDTO 试题信息
     * @return 二维码链接
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public String getRedirectUrlWithWrongDownload(boolean fromGuJiaoTong, WrongDownloadQuestionDTO wrongDownloadQuestionDTO) {
        if (!studentQuestionMarkQRCodeService.canGenerateQRCodeWithWord(fromGuJiaoTong)) {
            return null;
        }

        // 先生成主表，获取studentQuestionMarkId，拼凑二维码链接
        StudentQuestionMarkInsertDTO studentQuestionMarkInsertDTO = convert2MarkInsertDTO(wrongDownloadQuestionDTO);
        Long studentQuestionMarkId = studentQuestionMarkService.insertStudentQuestionMark(studentQuestionMarkInsertDTO);
        Long schoolId = studentQuestionMarkInsertDTO.getSchoolId();
        String redirectUrl = studentQuestionMarkQRCodeService.getRedirectUrl(studentQuestionMarkId, wrongDownloadQuestionDTO.getStudentId(), schoolId);

        // 插入试题数据 - realStructureNumber是word渲染生成的题号，需要等word渲染完才能拿到
        List<StudentQuestionMarkItemInsertDTO> studentQuestionMarkItemInsertDTOS = convert2ItemInsertDTO(wrongDownloadQuestionDTO);
        studentQuestionMarkItemService.insertStudentQuestionMarkItem(studentQuestionMarkId, studentQuestionMarkItemInsertDTOS);

        // 渲染小力图片
        TikuRenderQuestionByQuestionTaskCreateParam param = new TikuRenderQuestionByQuestionTaskCreateParam();
        param.setFromType(DictUtil.getDictValue("tikuRenderQuestionByQuestionTaskFromType", "wrongQuestionDownload"));
        param.setFromPrimaryKey(wrongDownloadQuestionDTO.getWrongDocumentId());
        param.setTaskSource("错题下载");
        List<TikuRenderQuestionByQuestionTaskCreateParam.RenderQuestionInfoCreateDTO> renderQuestionInfoCreateDTOS = studentQuestionMarkItemInsertDTOS
                .stream()
                .filter(i -> DictUtil.isEquals(i.getQuestionSourceType(), "studentQuestionMarkQuestionSourceType", "similarQuestion"))
                .map(i -> {
                    TikuRenderQuestionByQuestionTaskCreateParam.RenderQuestionInfoCreateDTO qn =
                            new TikuRenderQuestionByQuestionTaskCreateParam.RenderQuestionInfoCreateDTO();
                    qn.setQuestionId(i.getQuestionId());
                    qn.setBelongType(i.getBelongType());
                    qn.setCourseId(i.getCourseId());
                    return qn;
                }).collect(Collectors.toList());
        param.setRenderQuestionInfoCreateDTOS(renderQuestionInfoCreateDTOS);
        tikuRenderQuestionByQuestionsTaskService.registerSynchronizationToCreateRenderQuestionByQuestionTask(param);

        return redirectUrl;
    }

    private StudentQuestionMarkInsertDTO convert2MarkInsertDTO(
            WrongDownloadQuestionDTO wrongDownloadQuestionDTO) {
        wrongDownloadQuestionDTO.verify();

        Long studentId = wrongDownloadQuestionDTO.getStudentId();
        Map<String, Object> studentInfo = commonStudentService.getStudent(MapUtil.of("studentId", studentId));
        if (MapUtils.isEmpty(studentInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "studentId：" + studentId + "学生信息不存在");
        }

        Long wrongDocumentId = wrongDownloadQuestionDTO.getWrongDocumentId();
        Map<String, Object> wrongDocument = wrongExamService.getExamDocumentInfoById(wrongDocumentId);
        String documentName = MapUtil.getString(wrongDocument, "documentName");

        // 主表数据
        StudentQuestionMarkInsertDTO studentQuestionMarkInsertDTO = new StudentQuestionMarkInsertDTO();
        studentQuestionMarkInsertDTO.setStudentId(studentId);
        studentQuestionMarkInsertDTO.setStudentName(MapUtil.getString(studentInfo, "studentName"));
        studentQuestionMarkInsertDTO.setSchoolId(MapUtil.getLong(studentInfo, "schoolId"));
        studentQuestionMarkInsertDTO.setSourceType(DictUtil.getDictValue("studentQuestionMarkSourceType", "wrongQuestionDownload"));
        studentQuestionMarkInsertDTO.setSourceId(wrongDocumentId);
        studentQuestionMarkInsertDTO.setName(documentName);

        return studentQuestionMarkInsertDTO;
    }

    private List<StudentQuestionMarkItemInsertDTO> convert2ItemInsertDTO(WrongDownloadQuestionDTO wrongDownloadQuestionDTO) {
        List<StudentQuestionMarkItemInsertDTO> studentQuestionMarkItemInsertDTOList = new ArrayList<>();

        List<Map<String, Object>> questionList = wrongDownloadQuestionDTO.getQuestionList();
        for (int i = 0; i < questionList.size(); i++) {
            Map<String, Object> questionItem = questionList.get(i);
            // 处理原题
            String sourceQuestionIdNullable = null;
            int questionSourceType;
            int wrongItemQuestionFrom = MapUtil.getInt(questionItem, "wrongItemQuestionFrom");
            if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "examWrongQuestion", "studyGuideWrongQuestion")) {
                questionSourceType = DictUtil.getDictValue("studentQuestionMarkQuestionSourceType", "examWrongQuestion");
            } else if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "similarQuestion")) {
                sourceQuestionIdNullable = MapUtil.getStringNullable(questionItem, "sourceQuestionId");
                questionSourceType = DictUtil.getDictValue("studentQuestionMarkQuestionSourceType", "similarQuestion");
            } else {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "wrongItemQuestionFrom=" + wrongItemQuestionFrom + "字典值不存在!");
            }
            StudentQuestionMarkItemInsertDTO sourceStudentQuestionMarkItemInsertDTO = new StudentQuestionMarkItemInsertDTO();
            sourceStudentQuestionMarkItemInsertDTO.setQuestionId(MapUtil.getString(questionItem, "questionId"));
            sourceStudentQuestionMarkItemInsertDTO.setBelongType(MapUtil.getInt(questionItem, "belongType"));
            sourceStudentQuestionMarkItemInsertDTO.setCourseId(MapUtil.getLong(questionItem, "courseId"));
            sourceStudentQuestionMarkItemInsertDTO.setDifficulty(new BigDecimal(MapUtil.getString(questionItem, "difficulty", "0")));
            sourceStudentQuestionMarkItemInsertDTO.setQuestionSourceType(questionSourceType);
            sourceStudentQuestionMarkItemInsertDTO.setSourceQuestionId(sourceQuestionIdNullable);
            String parentStructureNumber = String.valueOf(i + 1);
            sourceStudentQuestionMarkItemInsertDTO.setStructureNumber(parentStructureNumber);
            studentQuestionMarkItemInsertDTOList.add(sourceStudentQuestionMarkItemInsertDTO);

            // 处理举一反三
            List<Map<String, Object>> one2MoreList = MapUtil.getCast(questionItem, "one2MoreList");
            if (CollectionUtils.isNotEmpty(one2MoreList)) {
                for (int i1 = 0; i1 < one2MoreList.size(); i1++) {
                    Map<String, Object> one2MoreItem = one2MoreList.get(i1);
                    StudentQuestionMarkItemInsertDTO one2MoreStudentQuestionMarkItemInsertDTO = new StudentQuestionMarkItemInsertDTO();
                    one2MoreStudentQuestionMarkItemInsertDTO.setQuestionId(MapUtil.getString(one2MoreItem, "_id"));
                    one2MoreStudentQuestionMarkItemInsertDTO.setBelongType(DictUtil.getDictValue("questionBankBelongType", "dongni"));
                    one2MoreStudentQuestionMarkItemInsertDTO.setCourseId(MapUtil.getLong(one2MoreItem, "courseId"));
                    one2MoreStudentQuestionMarkItemInsertDTO.setDifficulty(new BigDecimal(MapUtil.getString(one2MoreItem, "difficulty")));
                    one2MoreStudentQuestionMarkItemInsertDTO.setQuestionSourceType(DictUtil.getDictValue("studentQuestionMarkQuestionSourceType", "similarQuestion"));
                    if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "examWrongQuestion", "studyGuideWrongQuestion")) {
                        one2MoreStudentQuestionMarkItemInsertDTO.setSourceQuestionId(MapUtil.getString(questionItem, "questionId"));
                    } else if (DictUtil.isEquals(wrongItemQuestionFrom, "wrongItemQuestionFrom", "studyGuideWrongQuestion")) {
                        one2MoreStudentQuestionMarkItemInsertDTO.setSourceQuestionId(sourceQuestionIdNullable); // 挂在原题的原题上
                    } else {
                        throw new CommonException(ResponseStatusEnum.DATA_ERROR, "wrongItemQuestionFrom=" + wrongItemQuestionFrom + "字典值不存在!");
                    }
                    one2MoreStudentQuestionMarkItemInsertDTO.setStructureNumber(parentStructureNumber + "." + (i1 + 1));
                    studentQuestionMarkItemInsertDTOList.add(one2MoreStudentQuestionMarkItemInsertDTO);
                }
            }
        }
        return studentQuestionMarkItemInsertDTOList;
    }
}
