package com.dongni.tiku.similar.v3;

import com.dongni.tiku.similar.StudentLevel;
import com.dongni.tiku.similar.enumeration.Difficulty;
import com.dongni.tiku.similar.utils.SimilarUtil;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 *
 * <AUTHOR>
 * 2022/09/21
 */
public class Similar3Util {
    
    /**
     * 反转试题关系
     * @param sourceCandidateList 原题备选题关系List
     * @return 备选题 -> 原题Set
     */
    static Map<Question3, Set<Question3>> getCandidate2SourceSet(List<SourceCandidate3> sourceCandidateList) {
        return SimilarUtil.getCandidate2SourceSet(new ArrayList<>(sourceCandidateList));
    }
    
    /**
     * 获取推题数量
     *   计算公式: (k+n)/(n+1)=K
     *       n=ROUNDUP((K-k)/(1-K))    1<=n<=3
     *       K 试题难度对应的知识点掌握度达标标准值
     *       k 试题的学生的知识点掌握度平均值
     * @param graspStandard 试题难度对应的知识点掌握度达标标准值
     * @param graspStudent 试题的学生的知识点掌握度平均值
     * @return 1<=n<=3
     */
    private static int getSimilarNum(double graspStandard, double graspStudent) {
        if (graspStudent >= graspStandard) { return 1; }
        if (graspStandard >= 1) { return 3; }  // 除0
        int n = (int) Math.ceil((graspStandard - graspStudent) / (1 - graspStandard));
        if (n > 3) { n = 3; }
        if (n < 1) { n = 1; }
        return n;
    }
    
    /**
     * 获取推题数量
     * @param graspStandard 试题难度对应的知识点掌握度达标标准值
     * @param graspStudent 试题的学生的知识点掌握度平均值
     * @param leftNum 还需要推几个
     * @return 0<=n<=3
     */
    private static int getSimilarNum(double graspStandard, double graspStudent, int leftNum) {
        if (leftNum <= 0) { return 0; }
        return Math.min(leftNum, getSimilarNum(graspStandard, graspStudent));
    }
    
    /**
     * 计算推题的数量
     * @param source 原题 {题型 课程 得分率 知识点信息}
     * @param sourceQuestionIdSimilarNum       推题数量 可能为空
     * @param difficulty2KnowledgeId2GraspRate 学生的知识点掌握程度 可能没考过找不到  难度->知识点->掌握度
     * @param knowledgeGraspRateTarget 知识点掌握度标准值  难度->掌握度标准  如简单->0.9
     * @param studentScoringRate 学生整体得分率
     * @param studentLevel 学生的层次
     * @return 难度 -> 需要推题的数量
     */
    static LinkedHashMap<Difficulty, Integer> getDifficulty2SimilarNum(
            Question3 source,
            Integer sourceQuestionIdSimilarNum,
            Map<Difficulty, Map<String, Double>> difficulty2KnowledgeId2GraspRate,
            Map<Difficulty, Double> knowledgeGraspRateTarget,
            double studentScoringRate,
            StudentLevel studentLevel
    ) {
        LinkedHashMap<Difficulty, Integer> result = new LinkedHashMap<>(3);
        
        // 原题id的推题数量 如果不需要则直接返回 这题不用推了
        if (sourceQuestionIdSimilarNum == null || sourceQuestionIdSimilarNum <= 0) { return result; }
        int similarNumLeft = sourceQuestionIdSimilarNum;
        
        // 原题难度 + 偏移量 -> 起始难度
        double scoringRate = source.getScoringRate();
        double scoringRateOffset = studentLevel.getScoringRateOffset();
        double initScoringRate = scoringRate + scoringRateOffset;
        Difficulty current = Difficulty.getByDifficult(initScoringRate);
        
        // 知识点信息 用来计算试题的学生的知识点掌握度平均值k 也有可能用不着
        Set<String> knowledgeIdSet = source.getKnowledgeIdSet();
        int knowledgeIdSize = knowledgeIdSet.size();
        
        while (true) {
            if (similarNumLeft <= 0) { break; }
            
            // 获取更难的题目 如果还有推题数量，则会一直递增难度
            Difficulty harder = Difficulty.getHarder(current);
            if (harder == null) {
                // 如果没有更难的难度，说明到顶了，则剩下的题目都是它了
                result.put(current, similarNumLeft);
                break;
            }
            
            // K 试题难度对应的知识点掌握度达标标准值
            double graspStandard = knowledgeGraspRateTarget.get(current);
            // 知识点掌握度默认值 如果知识点在对应难度没有考过，则设置为 K * 子课程维度计算整体的得分率
            double defaultGrasp = graspStandard * studentScoringRate;
            // 当前难度的知识点掌握度
            Map<String, Double> knowledgeId2GraspRate = difficulty2KnowledgeId2GraspRate.get(current);
            double graspStudentSum = 0.0;
            for (String knowledgeId : knowledgeIdSet) {
                Double knowledgeGrasp = Optional.ofNullable(knowledgeId2GraspRate)
                        .map(item -> item.get(knowledgeId))
                        .orElse(defaultGrasp);
                graspStudentSum += knowledgeGrasp;
            }
            double graspStudent = graspStudentSum / knowledgeIdSize;
            
            int similarNum = getSimilarNum(graspStandard, graspStudent, similarNumLeft);
            result.put(current, similarNum);
            
            similarNumLeft -= similarNum;
            current = harder;
        }
        return result;
    }
    
}
