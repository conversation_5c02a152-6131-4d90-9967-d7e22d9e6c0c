package com.dongni.tiku.similar.log;

import ch.qos.logback.core.OutputStreamAppender;

import java.io.OutputStream;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/9/19 周四 下午 04:04
 * @Version 1.0.0
 */
public class CustomOutputStreamAppender<E> extends OutputStreamAppender<E> {
    public CustomOutputStreamAppender(OutputStream outputStream) {
        super.setOutputStream(outputStream);
        super.setName("customOutputStreamAppender");
    }

    @Override
    public void start() {
        if (getOutputStream() == null) {
            return;
        }
        super.start();
    }
}
