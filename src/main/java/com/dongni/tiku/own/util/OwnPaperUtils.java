package com.dongni.tiku.own.util;

import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.enumeration.PaperAnswerStatus;
import com.dongni.tiku.common.util.MapUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 题库工具类
 * <AUTHOR>
 */
public final class OwnPaperUtils {
    private OwnPaperUtils() {}

    /**
     * 判断试卷答案是否不可编辑
     * @param paper 试卷详情
     * @return 是否不可编辑
     */
    public static boolean isPaperAnswerUnEditable(Map<String, Object> paper) {
        return ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS.getStatus());
    }

    /**
     * 获取更新试卷的mongo update语句
     *      -- 只遍历客观题
     *
     * @param paper 试卷详情
     * @param func 设置更新语句的方法
     * @return 返回更新语句对象
     */
    public static List<PaperUpdate> getPaperUpdateWithObjectQuestion(Document paper, PaperUpdateFunc func) {
        int readTypeObjective = DictUtil.getDictValue("readType", "objective");
        List<PaperUpdate> paperUpdateList = new ArrayList<>();

        List<Map<String, Object>> parts = MapUtil.getCast(paper, "parts");
        for (int p = 0; p < parts.size(); p++) {
            Map<String, Object> part = parts.get(p);
            List<Map<String, Object>> categories = MapUtil.getCast(part, "categories");
            for (int c = 0; c < categories.size(); c++) {
                Map<String, Object> category = categories.get(c);
                List<Map<String, Object>> questions = MapUtil.getCast(category, "questions");
                for (int q = 0; q < questions.size(); q++) {
                    Map<String, Object> question = questions.get(q);
                    String questionId = MapUtil.getStringNullable(question, "_id");
                    // 非0的为按大题，即非客观题的，不处理
                    if (MapUtil.getInt(question, "readType", -1) != 0) {
                        continue;
                    }
                    List<Map<String, Object>> structures = MapUtil.getCast(question, "structures");
                    if (CollectionUtils.isNotEmpty(structures)) {
                        for (int s = 0; s < structures.size(); s++) {
                            Map<String, Object> structure = structures.get(s);
                            if (MapUtil.getInt(structure, "readType", -1) != readTypeObjective) {
                                continue;  // 不是客观题的不处理
                            }
                            String path = "parts." + p + ".categories." + c + ".questions." + q + ".structures." + s + ".";
                            PaperUpdate paperUpdate = func.apply(questionId, structure, path);
                            if (paperUpdate != null) {
                                paperUpdateList.add(paperUpdate);
                            }
                        }
                    }
                }
            }
        }

        return paperUpdateList;
    }

    /**
     * 根据PaperUpdate对象构造更新paper的语句
     *
     * @param paperUpdateList 待处理对象
     * @param otherUpdate 额外的更新
     * @param userId 用户id
     * @param userName 用户名
     * @param date 更新时间
     * @return 更新语句
     */
    public static Document processPaperUpdate(List<PaperUpdate> paperUpdateList,
                                              Document otherUpdate,
                                              long userId,
                                              String userName,
                                              Date date) {
        Document setDoc = new Document();
        Document unsetDoc = new Document();
        for (PaperUpdate paperUpdate : paperUpdateList) {
            setDoc.putAll(paperUpdate.getSetDoc());
            List<String> unsetFields = paperUpdate.getUnsetFields();
            for (String unsetField : unsetFields) {
                unsetDoc.put(unsetField, 1);
            }
        }

        // 其他要更新的
        if (MapUtils.isNotEmpty(otherUpdate)) {
            setDoc.putAll(otherUpdate);
        }

        // 设置更新人、更新时间
        setDoc.put("modifierId", userId);
        setDoc.put("modifierName", userName);
        setDoc.put("modifyDateTime", date);

        Document update = new Document("$set", setDoc);
        if (!unsetDoc.isEmpty()) {
            update.append("$unset", unsetDoc);
        }
        return update;
    }
}
