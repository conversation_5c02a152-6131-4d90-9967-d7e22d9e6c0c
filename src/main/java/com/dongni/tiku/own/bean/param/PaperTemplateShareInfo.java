package com.dongni.tiku.own.bean.param;

import java.util.Objects;

public class PaperTemplateShareInfo {
    Long targetSchoolId ;
    Long targetCourseId;
    Long targetCreatorId ;
    Integer targetGradeType ;
    String targetTemplateName ;

    public Long getTargetSchoolId() {
        return targetSchoolId;
    }

    public void setTargetSchoolId(Long targetSchoolId) {
        this.targetSchoolId = targetSchoolId;
    }

    public Long getTargetCourseId() {
        return targetCourseId;
    }

    public void setTargetCourseId(Long targetCourseId) {
        this.targetCourseId = targetCourseId;
    }

    public Long getTargetCreatorId() {
        return targetCreatorId;
    }

    public void setTargetCreatorId(Long targetCreatorId) {
        this.targetCreatorId = targetCreatorId;
    }

    public Integer getTargetGradeType() {
        return targetGradeType;
    }

    public void setTargetGradeType(Integer targetGradeType) {
        this.targetGradeType = targetGradeType;
    }

    public String getTargetTemplateName() {
        return targetTemplateName;
    }

    public void setTargetTemplateName(String targetTemplateName) {
        this.targetTemplateName = targetTemplateName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PaperTemplateShareInfo that = (PaperTemplateShareInfo) o;
        return Objects.equals(targetSchoolId, that.targetSchoolId) && Objects.equals(targetCourseId, that.targetCourseId) && Objects.equals(targetCreatorId, that.targetCreatorId) && Objects.equals(targetGradeType, that.targetGradeType) && Objects.equals(targetTemplateName, that.targetTemplateName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(targetSchoolId, targetCourseId, targetCreatorId, targetGradeType, targetTemplateName);
    }

    @Override
    public String toString() {
        return "PaperTemplateShareInfo{" +
                "targetSchoolId=" + targetSchoolId +
                ", targetCourseId=" + targetCourseId +
                ", targetCreatorId=" + targetCreatorId +
                ", targetGradeType=" + targetGradeType +
                ", targetTemplateName='" + targetTemplateName + '\'' +
                '}';
    }
}
