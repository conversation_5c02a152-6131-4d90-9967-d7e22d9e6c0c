package com.dongni.tiku.own.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.annotation.DongniAuth;
import com.dongni.commons.annotation.DongniNotCheckUserInfo;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.tiku.own.service.AreaPaperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * Created by scott
 * time: 10:41 2019/8/6
 * description:
 */
@RestController
@RequestMapping("/tiku/area/paper")
public class AreaPaperController extends BaseController {


    @Autowired
    private AreaPaperService areaPaperService;

    /**
     * 子系统获取区域试卷
     *
     * @return 试卷
     */
    @GetMapping
    public Response getAreaPaper() {
        return new Response(areaPaperService.getAreaPaper(getParameterMap()));
    }

    /**
     * 子系统获取区域试卷详情
     *
     * @return 试卷
     */
    @GetMapping("/detail")
    public Response getAreaPaperDetail() {
        return new Response(areaPaperService.getAreaPaperDetail(getParameterMap()));
    }
    /**
     * 子系统获取区域试卷知识点
     *
     * @return 知识点
     */
    @GetMapping("/knowledge")
    public Response getAreaPaperKnowledge() {
        return new Response(areaPaperService.getAreaPaperKnowledge(getParameterMap()));
    }
    /**
     * 子系统获取区域试卷试题
     *
     * @return 区域试卷试题
     */
    @GetMapping("/question/map")
    public Response getAreaPaperQuestionMap() {
        return new Response(areaPaperService.getAreaPaperQuestionMap(getParameterMap()));
    }
    /**
     * 子系统获取区域试卷双向明细表
     *
     * @return 双向明细表
     */
    @GetMapping("/bidirectional/sheet")
    public Response getAreaPaperSheet() {
        return new Response(areaPaperService.getAreaPaperSheet(getParameterMap()));
    }

    /**
     * 系统之间内部调用，获取区域试卷
     *
     * @return 试卷
     */
    @GetMapping("/system")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response returnUnionPaper() {
        return new Response(areaPaperService.returnUnionPaper(getParameterMap()));
    }

    /**
     * 系统之间内部调用，获取区域试卷详情
     *
     * @return 试卷详情
     */
    @GetMapping("/system/detail")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response returnUnionPaperDetail() {
        return new Response(areaPaperService.returnUnionPaperDetail(getParameterMap()));
    }
    /**
     * 系统之间内部调用，获取区域试卷知识点
     *
     * @return 试卷详情
     */
    @GetMapping("/system/knowledge")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response returnKnowledge() {
        return new Response(areaPaperService.returnKnowledge(getParameterMap()));
    }
    /**
     * 系统之间内部调用，获取区域试卷
     *
     * @return 试卷详情
     */
    @GetMapping("/system/detail/list")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response returnAreaPaperDetailList() {
        return new Response(areaPaperService.returnAreaPaperDetailList(getParameterMap()));
    }

    /**
     * 系统之间内部调用，双向明细
     *
     * @return 双向明细
     */
    @GetMapping("/system/bidirectional/sheet")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response returnAreaPaperSheet() {
        return new Response(areaPaperService.returnAreaPaperSheet(getParameterMap()));
    }

    /**
     * 系统之间内部调用，知识点
     *
     * @return 知识点
     */
    @GetMapping("/system/knowledge/one")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response returnAreaPaperSheetKnowledge() {
        return new Response(areaPaperService.returnAreaPaperSheetKnowledge(getParameterMap()));
    }

    /**
     * 系统之间内部调用，知识点
     *
     * @return 知识点
     */
    @GetMapping("/system/question/map")
    @DongniAuth(scope = "dongni")
    @DongniNotCheckUserInfo
    public Response returnAreaPaperQuestionMap() {
        return new Response(areaPaperService.returnAreaPaperQuestionMap(getParameterMap()));
    }

    @PostMapping("/import/from/school/paper/template")
    public Response importUnionPaperFromSchoolPaperByTemplate(MultipartFile file) {
        return new Response(areaPaperService.importUnionPaperFromSchoolPaper(file));
    }

}
