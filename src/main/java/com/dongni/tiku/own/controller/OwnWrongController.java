package com.dongni.tiku.own.controller;

import com.dongni.common.entity.Response;
import com.dongni.commons.mvc.controller.BaseController;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.own.service.OwnWrongService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Created by JiJinDong
 * time:2019/3/11 14:41
 * description: 错题下载
 **/
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/own/wrong")
public class OwnWrongController extends BaseController {

    @Autowired
    private OwnWrongService ownWrongService;

    /**
     *
     * @param params questions
     * @return 错题下载
     */
    @PostMapping("/down")
    public Response downWrong(@RequestBody Map<String,Object> params){
        return new Response(ownWrongService.downWrong(params));
    }

}
