package com.dongni.tiku.own.controller;

import com.dongni.common.auth.DongniClientUtil;
import com.dongni.common.auth.impl.DongniClientAuthNicezhuanyeImpl;
import com.dongni.commons.annotation.DongniNotRequireLogin;
import com.dongni.commons.entity.Response;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.own.service.OwnKnowledgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/01/22 11:40
 */
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/own/knowledge")
public class OwnKnowledgeController {

    @Autowired
    private OwnKnowledgeService ownKnowledgeService;
    
    /**
     * 获取默认知识点
     *
     * @return 默认的知识点
     */
    @GetMapping("/default")
    public Response getDefaultKnowledge(Map<String, Object> params) {
        return new Response(ownKnowledgeService.getDefaultKnowledge(params));
    }
    
    /**
     * 查询课程的知识点树
     * @param params courseId 课程id
     * @return 知识点树
     */
    @GetMapping("/course/tree")
    public Response getCourseKnowledgeTree(Map<String, Object> params) {
        return new Response(ownKnowledgeService.getCourseKnowledgeTree(params));
    }
    
    /**
     * 新增课程知识点
     */
    @PostMapping("/course/add")
    public Response addCourseKnowledge(Map<String, Object> params) {
        ownKnowledgeService.addCourseKnowledge(params);
        return new Response();
    }
    
    /**
     * 编辑课程知识点
     */
    @PostMapping("/course/update")
    public Response updateCourseKnowledge(Map<String, Object> params) {
        ownKnowledgeService.updateCourseKnowledge(params);
        return new Response();
    }
    
    /**
     * 非GET/POST治理 题库
     * 编辑课程知识点
     * @deprecated 某些本地化不支持PUT/DELETE METHOD
     * @see #updateCourseKnowledge(Map)
     */
    @PutMapping("/course/update")
    @Deprecated    // 某些本地化不支持PUT/DELETE METHOD
    public Response updateCourseKnowledgeDeprecated(Map<String, Object> params) {
        ownKnowledgeService.updateCourseKnowledge(params);
        return new Response();
    }
    
    /**
     * 删除课程知识点
     */
    @PostMapping("/course/delete")
    public Response deleteCourseKnowledge(Map<String, Object> params) {
        ownKnowledgeService.deleteCourseKnowledge(params);
        return new Response();
    }
    
    /**
     * 非GET/POST治理 题库
     * 删除课程知识点
     * @deprecated 某些本地化不支持PUT/DELETE METHOD
     * @see #deleteCourseKnowledge(Map)
     */
    @DeleteMapping("/course/delete")
    @Deprecated    // 某些本地化不支持PUT/DELETE METHOD
    public Response deleteCourseKnowledgeDeprecated(Map<String, Object> params) {
        ownKnowledgeService.deleteCourseKnowledge(params);
        return new Response();
    }
    
    /**
     * 20230706 张英健
     * @param params courseId 课程id
     * @return 知识点信息
     */
    @DongniNotRequireLogin
    @PostMapping("/1f742681-9534-4cee-b3f2-92d6dcfc39f2/course")
    public Response getKnowledgeByCourseIdForNicezhuanye(HttpServletRequest request, Map<String, Object> params) {
        DongniClientUtil.valid(request, params, new DongniClientAuthNicezhuanyeImpl());
        return new Response(ownKnowledgeService.getKnowledgeByCourseId(params));
    }
    
    // ---------------------------------------------------------------- 疑似手动维护数据代码
    
    /**
     * 查询知识点树
     *    20230327 前端没有调用 可能是手动进行数据维护
     * @param params courseId
     * @return 知识点树
     */
    @GetMapping("/tree")
    public Response getKnowledgeTree(Map<String, Object> params) {
        return new Response(ownKnowledgeService.getKnowledgeTree(params));
    }

    /**
     * 知识点树构造父子节点索引
     *   20230327 前端没有调用 可能是手动进行数据维护
     * @param params courseId
     */
    @PostMapping("/tree/index")
    public Response knowledgeTreeAddIndex(Map<String, Object> params) {
        ownKnowledgeService.knowledgeTreeAddIndex(params);
        return new Response();
    }

    /**
     * 初始化所有知识点树索引
     *    20230327 前端没有调用 可能是手动进行数据维护
     */
    @PostMapping("/tree/index/all")
    public Response knowledgeTreeAddIndexAll() {
        ownKnowledgeService.knowledgeTreeAddIndexAll();
        return new Response();
    }

}
