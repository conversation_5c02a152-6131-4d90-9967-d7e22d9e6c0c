package com.dongni.tiku.own.service;

import com.dongni.common.utils.IdUtil;
import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.enumeration.FileStoragePathEnum;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.enumeration.PaperAnswerCardStatus;
import com.dongni.tiku.common.util.AnswerCardTemplateUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.manager.IAnswerCardTemplateManager;
import com.dongni.tiku.manager.impl.AnswerCardTemplateImageDeleteLogManager;
import com.google.common.collect.Lists;
import com.mongodb.client.model.ReplaceOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.currentDate;
import static com.mongodb.client.model.Updates.set;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> <br>
 * 2022/03/14 <br>
 * 除了库不同之外的一些通用的操作
 */
public abstract class CommonAnswerCardTemplateService {
    
    @Autowired
    protected AnswerCardTemplateImageDeleteLogManager answerCardTemplateImageDeleteLogManager;
    
    private IAnswerCardTemplateManager answerCardTemplateManager;
    private CommonPaperService commonPaperService;
    
    protected abstract IAnswerCardTemplateManager getAnswerCardTemplateManager();
    protected abstract CommonPaperService getCommonPaperService();
    
    @PostConstruct
    public void init() {
        answerCardTemplateManager = getAnswerCardTemplateManager();
        commonPaperService = getCommonPaperService();
    }
    
    /**
     * 获取答题卡模板
     * @param params paperId
     * @return answerCardTemplate or exception
     */
    public Document getAnswerCardTemplate(Map<String, Object> params) {
        Document answerCardTemplate = getAnswerCardTemplateNullable(params);
        if (answerCardTemplate == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "答题卡模板不存在");
        }
        return answerCardTemplate;
    }
    
    /**
     * 获取答题卡模板
     * @param params paperId
     * @return answerCardTemplate or null
     */
    public Document getAnswerCardTemplateNullable(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        return answerCardTemplateManager.getFirst(eq("paperId", paperId));
    }

    /**
     * 批量获取答题卡模板
     * @param params paperIdList
     * @return answerCardTemplateList
     */
    public List<Document> getAnswerCardTemplateList(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("paperIdList")
                .verify();
        List<Long> paperIdList = MapUtil.getListLong(params, "paperIdList");
        return Optional.ofNullable(answerCardTemplateManager.getList(in("paperId", paperIdList))).orElse(Lists.newArrayList());
    }
    
    /**
     * 保存多个答题卡模板
     */
    protected void saveAnswerCardTemplateList(Collection<Document> answerCardTemplateList) {
        if (CollectionUtils.isEmpty(answerCardTemplateList)) {
            return;
        }
        answerCardTemplateManager.insertMany(answerCardTemplateList);
    }
    
    /**
     * 替换一个答题卡模板
     * @param answerCardTemplate 答题卡模板
     */
    public void replace(Document answerCardTemplate) {
        answerCardTemplateManager.replace(answerCardTemplate);
    }
    
    /**
     * 修改答题卡模板中的试卷名称
     *    answerCardTemplate.state.dataSet.process.paperName
     * @param params paperId paperName userId userName
     */
    public void updatePaperName(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotBlank("paperName")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        // answerCardTemplate.state.dataSet.process.paperName
        Bson answerCardWhere = eq("paperId", paperId);
        Bson answerCardUpdate = combine(
                set("state.dataSet.process.paperName", MapUtil.getString(params, "paperName")),
                set("modifierId", MapUtil.getLong(params, "userId")),
                set("modifierName", MapUtil.getString(params, "userName")),
                currentDate("modifyDateTime")
        );
        answerCardTemplateManager.updateOne(answerCardWhere, answerCardUpdate);
    }

    /**
     * 修改答题卡模板中的试卷名称+年级
     *    answerCardTemplate.state.dataSet.process.paperName
     * @param params paperId paperName userId userName
     */
    public void updatePaperNameAndGradeType(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotBlank("paperName")
                .isNumeric("gradeType")
                .isValidId("userId")
                .isNotBlank("userName")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        // answerCardTemplate.state.dataSet.process.paperName
        Bson answerCardWhere = eq("paperId", paperId);
        Bson answerCardUpdate = combine(
                set("state.dataSet.process.paperName", MapUtil.getString(params, "paperName")),
                set("state.dataSet.process.gradeType", MapUtil.getInt(params, "gradeType")),
                set("modifierId", MapUtil.getLong(params, "userId")),
                set("modifierName", MapUtil.getString(params, "userName")),
                currentDate("modifyDateTime")
        );
        answerCardTemplateManager.updateOne(answerCardWhere, answerCardUpdate);
    }
    
    /**
     * 更新答题卡模板的courseId
     * @param params paperId courseId
     *              baseCourse 基础课程 or 综合课程
     */
    public void updateCourse(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isValidId("courseId")
                .isNotNull("baseCourse")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        Bson query = eq("paperId", paperId);
        Document answerCardTemplate = answerCardTemplateManager.getFirst(query);
        if (answerCardTemplate != null) {
            long courseId = MapUtil.getLong(params, "courseId");
            boolean baseCourse = MapUtil.getBoolean(params, "baseCourse");
            Map<String, Object> state = AnswerCardTemplateUtil.updateAnswerCardTemplateCourse(answerCardTemplate, courseId, baseCourse);
            answerCardTemplateManager.updateOne(query, combine(set("state", state)));
        }
    }
    
    /**
     * 更新答题卡的题号和分值
     *
     * @param paper 试卷文档
     */
    public void updateQuestionNumberAndScore(Map<String, Object> params, Map<String, Object> paper) {
        // 考试用卷不更新系统模板, 第三方答题卡除外
        boolean updatePaperForTemplateDraw = MapUtil.getBoolean(params, "__updatePaperForTemplateDraw");
        if (PaperUtil.isExamPaper(paper) && !updatePaperForTemplateDraw) {
            return;
        }

        List<Map<String, Object>> questionNumbers = PaperUtil.getPaperStructure(paper);
        Map<Integer, Map<String, Object>> questionMap = questionNumbers.stream()
                .collect(toMap(item -> Integer.valueOf(item.get("questionNumber").toString()), item -> item));

        Bson query = and(
                eq("paperId", MapUtil.getLong(paper, "paperId")),
                ne("isValid", 0)); // 无效的模板不更新

        Document answerCardTemplate = answerCardTemplateManager.getFirst(query);
        if (answerCardTemplate == null) {
            return;
        }
        
        // 更新答题卡题号和分值，这里做判断是因为存在历史版本答题卡，可能会没有state字段
        if (answerCardTemplate.containsKey("state")) {
            Document state = (Document) answerCardTemplate.get("state");
            Document dataSet = (Document) state.get("dataSet");
            Document process = (Document) dataSet.get("process");
            List<Document> unitParts = MapUtil.getCast(process, "unitParts");
            for (Document part : unitParts) {
                List<Document> unitList = MapUtil.getCast(part, "unitList");
                for (Document unit : unitList) {
                    List<Document> questions = MapUtil.getCast(unit, "questions");
                    for (Document question : questions) {
                        if (MapUtil.getInt(question, "unitType") != 12) {
                            Integer questionNumber = Integer.valueOf(question.get("questionNumber").toString());
                            Map < String, Object > currentQuestion = questionMap.get(questionNumber);
                            if (MapUtils.isEmpty(currentQuestion)) {
                                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "答题卡与模板不一致, questionNumber: "+ questionNumber);
                            }
                            question.put("structureNumber", currentQuestion.get("structureNumber"));
                            question.put("scoreValue", currentQuestion.get("scoreValue"));
                        }
                    }
                }
            }
        }
        // 更新答题卡模版
        answerCardTemplateManager.replaceOne(query, answerCardTemplate);
    }
    
    /**
     * 更新一个
     * @param filter 修改的条件
     * @param update 更新信息
     */
    public void updateOne(Bson filter, Bson update) {
        answerCardTemplateManager.updateOne(filter, update);
    }
    
    /**
     * 获取答题卡pdf路径
     *
     * @param params paperId
     * @return pdfUrl or null
     */
    public String getPdfUrl(Map<String, Object> params, boolean throwWhenNotExist) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();
        
        long paperId = MapUtil.getLong(params, "paperId");
        Document answerCard = answerCardTemplateManager
                .where(eq("paperId", paperId))
                .select("pdfUrl")
                .first();
        if (throwWhenNotExist && answerCard == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "答题卡不存在");
        }
        
        return MapUtil.getTrimNullable(answerCard, "pdfUrl");
    }
    
    /**
     * 更新答题卡图片路径
     * @param params paperId pdfUrl(怀疑是个临时文件)
     * @return pdfUrl
     */
    public String updatePdfUrl(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotBlank("pdfUrl")
                .verify();
        
        long paperId = MapUtil.getLong(params, "paperId");
        String oldPdfUrl = getPdfUrl(params, true);
        String tmpPdfUrl = MapUtil.getString(params, "pdfUrl");
        String newPdfUrl = "upload/answerCard/pdf/" + paperId + "/" + IdUtil.getId() + ".pdf";
        FileStorageTemplate.copy(fileStorageCopy -> {
            fileStorageCopy.setDestinationPath(newPdfUrl);
            fileStorageCopy.setSourcePath(tmpPdfUrl);
        });
        
        // 存在旧的pdf，删除旧的pdf文件
        if (ObjectUtil.isNotBlank(oldPdfUrl)) {
            FileStorageTemplate.delete(oldPdfUrl);
        }
        
        // 更新
        answerCardTemplateManager.updateOne(eq("paperId", paperId), set("pdfUrl", newPdfUrl));
        return newPdfUrl;
    }
    
    /**
     * 删除答题卡模板
     * @param params paperId
     */
    public void deleteAnswerCardTemplate(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        // 删除答题卡的图片
        FileStorageTemplate.deleteByDirPath(FileStoragePathEnum.ANSWER_CARD_TEMPLATE_IMAGE.getPath() + params.get("paperId").toString());
        // 删pdf
        String oldPdfUrl = getPdfUrl(params, false);
        // 存在旧的pdf，删除旧的pdf文件
        if (ObjectUtil.isNotBlank(oldPdfUrl)) {
            FileStorageTemplate.delete(oldPdfUrl);
        }
        // 删mongo
        answerCardTemplateManager.deleteOne(eq("paperId", paperId));
    }
    
    /**
     * 插入答题卡模板
     * @param params userId
     *               paperId
     *               answerCardTemplate 答题卡模板信息
     * @return answerCardTemplate
     */
    public Document insertAnswerCardTemplate(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isValidId("paperId")
                .isNotBlank("answerCardTemplate")
                .verify();
        Document answerCardTemplate = getAnswerCardTemplateDocument(params);
        long paperId = MapUtil.getLong(params, "paperId");
        Bson query = eq("paperId", paperId);
        answerCardTemplateManager.replaceOne(query, answerCardTemplate, new ReplaceOptions().upsert(true));
        // 有答题卡模板了
        commonPaperService.update(paperId, set("answerCardStatus", PaperAnswerCardStatus.HAS.getStatus()));
        return answerCardTemplate;
    }
    
    /**
     * 获取答题卡模版文档
     * @param params answerCardTemplate paperId userId
     * @return AnswerCardTemplateDocument
     */
    protected Document getAnswerCardTemplateDocument(Map<String, Object> params) {
        Verify.of(params).isValidId("userId").isValidId("paperId").isNotBlank("answerCardTemplate").verify();
        Document document = new Document(MapUtil.getMap(params, "answerCardTemplate"));
        document.put("paperId", MapUtil.getLong(params, "paperId"));
        document.put("creatorId", MapUtil.getLong(params, "userId"));
        return document;
    }
    
    
    /**
     * 删除制作答题卡中的图片
     *   邓冠文 答题卡删除oss图片接口，1.需要判断答题卡锁定状态，2.paperId与图片路径上的paperId需要对应上
     * @param params paperId
     *               imagePathList
     * @return paperId
     *         imagePrefix
     *         deleteImageList
     */
    public Map<String, Object> deleteAnswerCardTemplateImage(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotEmptyCollections("imagePathList")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        String imagePrefix = "upload/answerCard/template/image/" + paperId;
        Document answerCardTemplateImageDeleteLog = new Document()
                .append("paperId", paperId)
                .append("imagePathList", params.get("imagePathList"))
                .append("imagePrefix", imagePrefix)
                .append("creatorId", DongniUserInfoContext.get().getUserId())
                .append("creatorName", DongniUserInfoContext.get().getUserName())
                .append("userType", DongniUserInfoContext.get().getUserType())
                .append("createDateTimeFormat", DateUtil.getCurrentDateTime())
                .append("___expireDate", new Date(System.currentTimeMillis() + 1000L * 86400 * 366));
        List<String> imagePathList = MapUtil.getListType(params, "imagePathList", MapUtil::getStringNullable);
        List<Map<String, Object>> deleteImageList = new ArrayList<>(imagePathList.size());
        List<Map<String, Object>> maybeDeleteImageList = new ArrayList<>(imagePathList.size());
        answerCardTemplateImageDeleteLog.append("deleteImageList", deleteImageList);
        for (int i = 0, iLen = imagePathList.size(); i < iLen; i++) {
            String imagePath = imagePathList.get(i);
            Map<String, Object> deleteImage = new Document();
            deleteImage.put("index", i);
            deleteImage.put("imagePath", imagePath);
            deleteImageList.add(deleteImage);
            if (StringUtils.isBlank(imagePath)) {
                deleteImage.put("success", false);
                deleteImage.put("errMsg", "路径为空");
                continue;
            }
            imagePath = imagePath.trim();
            int indexOf = imagePath.indexOf(imagePrefix);
            if (indexOf == -1) {
                deleteImage.put("success", false);
                deleteImage.put("errMsg", "路径不合法，必须以(imagePrefix)开头");
                continue;
            }
            imagePath = imagePath.substring(indexOf);
            deleteImage.put("deletePath", imagePath);
            maybeDeleteImageList.add(deleteImage);
        }
        List<Map<String, Object>> needDeleteImageList = new ArrayList<>(maybeDeleteImageList.size());
        if (CollectionUtils.isNotEmpty(maybeDeleteImageList)) {
            Document answerCardTemplate = getAnswerCardTemplate(params);
            String answerCardTemplateJson = JSONUtil.toJson(answerCardTemplate);
            for (Map<String, Object> maybeDeleteImage : maybeDeleteImageList) {
                String deletePath = MapUtil.getTrim(maybeDeleteImage, "deletePath");
                if (answerCardTemplateJson.contains(deletePath)) {
                    maybeDeleteImage.put("success", false);
                    maybeDeleteImage.put("errMsg", "路径在模板中存在，不能删除");
                    continue;
                }
                needDeleteImageList.add(maybeDeleteImage);
            }
        }
        
        answerCardTemplateImageDeleteLogManager.insertOne(answerCardTemplateImageDeleteLog);
        ObjectId mongoId = MongoUtil.getMongoId(answerCardTemplateImageDeleteLog);
        Bson where = eq("_id", mongoId);
        for (Map<String, Object> needDeleteImage : needDeleteImageList) {
            int index = MapUtil.getInt(needDeleteImage, "index");
            String deletePath = MapUtil.getTrim(needDeleteImage, "deletePath");
            String errMsg = null;
            boolean success = true;
            try {
                FileStorageTemplate.delete(deletePath);
            } catch (Exception e) {
                success = false;
                errMsg = e.getMessage();
            }
            needDeleteImage.put("success", success);
            needDeleteImage.put("errMsg", errMsg);
            answerCardTemplateImageDeleteLogManager.updateOne(where, combine(
                            set("deleteImageList." + index + ".success", success),
                            set("deleteImageList." + index + ".errMsg", errMsg)
                    )
            );
        }
        
        answerCardTemplateImageDeleteLog.put("_id", answerCardTemplateImageDeleteLog.get("_id").toString());
        return answerCardTemplateImageDeleteLog;
    }
}
