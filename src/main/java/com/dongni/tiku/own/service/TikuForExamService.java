package com.dongni.tiku.own.service;

import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.IdUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.tiku.common.enumeration.PaperAnswerCardStatus;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.manager.impl.AnswerCardTemplateManager;
import com.dongni.tiku.manager.impl.PaperManager;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.function.Consumer;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.set;

/**
 * <AUTHOR>
 * @date 2024/08/15
 */
@Service
public class TikuForExamService {

    private static final Logger log = LoggerFactory.getLogger(TikuForExamService.class);
    @Autowired
    private PaperManager paperManager;
    @Autowired
    private AnswerCardTemplateManager answerCardTemplateManager;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private PaperObjQuestionAdditionalAnswerService paperObjQuestionAdditionalAnswerService;
    
    /**
     * 创建考试前的克隆
     * @param paperId 试卷id
     * @param copyOthersBeforeCopyPaperConsumer 其他复制的事务操作
     * @return 新的试卷id
     */
    public long cloneForExamCreate(long paperId, Consumer<Long> copyOthersBeforeCopyPaperConsumer) {
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        // 复制的数据
        Document paper = paperManager.getPaper(paperId);
        if (PaperUtil.isExamPaper(paper)) {
            if (PaperAnswerCardStatus.HAS_NOT.getStatus().equals(MapUtil.getInt(paper, "answerCardStatus"))) {
                // 第三方无答题卡发布考试-新建 没有系统答题卡 questionCount = 0  创建考试前调用接口生产空的试题结构
                // 第三方无法提卡发布考试-模板库范湖 没有系统答题卡 有试卷试题结构 创建考试前调用接口复制模板库的数据
                // 不需要复制 直接返回paperId 调用者需要对其进行判断 看是否需要复制t_question_structure t_question_mapping
                return paperId;
            }
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "试卷为考试用卷，不允许复制用于考试: paperId: " + paperId);
        }
        
        Document answerCardTemplate = answerCardTemplateManager.getFirst(eq("paperId", paperId));
        answerCardTemplateManager.required(answerCardTemplate, null, "paperId=" + paperId);
        Document paperObjQuAdditionalAnswer = paperObjQuestionAdditionalAnswerService.getByPaperId(paperId);
        
        Integer paperStatusUsed = DictUtil.getDictValue("paperStatus", "used");
        // 申请新的paperId
        long newPaperId = ownPaperService.getPaperId(new HashMap<>(0));
        
        // 试卷
        long sourcePaperId = MapUtil.getLong(paper, "sourcePaperId", paperId);
        paper.remove("_id");
        paper.put("paperId", newPaperId);
        paper.put("sourcePaperId", sourcePaperId);
        paper.put("paperStatus", paperStatusUsed);
        PaperUtil.setExamPaper(paper);
        paper.put("modifierId", dongniUserInfoContext.getUserId());
        paper.put("modifierName", dongniUserInfoContext.getUserName());
        paper.put("modifyDateTime", new Date());
        paper.entrySet().removeIf(entry -> entry.getKey().startsWith("downloadUrl"));
        
        // 系统答题卡
        answerCardTemplate.remove("_id");
        answerCardTemplate.put("paperId", newPaperId);
        answerCardTemplate.put("creatorId", dongniUserInfoContext.getUserId());
        answerCardTemplate.put("creator", dongniUserInfoContext.getUserName());
        answerCardTemplate.put("createDateTime", new Date());
        String pdfUrl = MapUtil.getTrimNullable(answerCardTemplate, "pdfUrl");
        // 尝试复制pdfUrl， 如果不存在或者复制失败，则不需要管他，直接移除即可
        answerCardTemplate.remove("pdfUrl");
        if (StringUtils.isNotBlank(pdfUrl) && FileStorageTemplate.exists(pdfUrl)) {
            try {
                String newPdfUrl = "upload/answerCard/pdf/" + newPaperId + "/" + IdUtil.getId() + ".pdf";
                FileStorageTemplate.copy(fileStorageCopy -> {
                    fileStorageCopy.setDestinationPath(newPdfUrl);
                    fileStorageCopy.setSourcePath(pdfUrl);
                });
                if (FileStorageTemplate.exists(newPdfUrl)) {
                    answerCardTemplate.put("pdfUrl", newPdfUrl);
                }
            } catch (Exception ignore) {
            }
        }

        // 客观题附加答案
        if (paperObjQuAdditionalAnswer != null) {
            paperObjQuAdditionalAnswer.remove("_id");
            paperObjQuAdditionalAnswer.put("paperId", newPaperId);
            paperObjQuAdditionalAnswer.put("creatorId", dongniUserInfoContext.getUserId());
            paperObjQuAdditionalAnswer.put("creatorName", dongniUserInfoContext.getUserName());
            paperObjQuAdditionalAnswer.put("createDateTime", new Date());
        }
        
        // 入库
        try {
            // 先入库外界的
            if (copyOthersBeforeCopyPaperConsumer != null) {
                copyOthersBeforeCopyPaperConsumer.accept(newPaperId);
            }
            paperManager.updatePaper(paperId, set("paperStatus", paperStatusUsed));
            answerCardTemplateManager.insertOne(answerCardTemplate);
            if (paperObjQuAdditionalAnswer != null) {
                paperObjQuestionAdditionalAnswerService.insertOne(paperObjQuAdditionalAnswer);
            }
            // paper永远在最后一位 防止回滚
            paperManager.insertOne(paper);
        } catch (Exception e) {
            String newPdfUrl = MapUtil.getTrimNullable(answerCardTemplate, "pdfUrl");
            if (StringUtils.isNotBlank(newPdfUrl)) {
                try {
                    FileStorageTemplate.delete(newPdfUrl);
                } catch (Exception delException) {
                    log.error("删除pdfUrl失败", delException);
                }
            }
            throw e;
        }
        
        return newPaperId;
    }
}
