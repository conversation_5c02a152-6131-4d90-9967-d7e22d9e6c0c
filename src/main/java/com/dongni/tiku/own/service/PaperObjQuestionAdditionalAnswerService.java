package com.dongni.tiku.own.service;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;

import com.dongni.tiku.bean.TikuMongodb;
import com.mongodb.client.MongoCollection;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: hzw
 * @date: 2024/4/17
 * @description: 试卷客观题附加答案处理接口
 */
@Service
public class PaperObjQuestionAdditionalAnswerService {

	private final MongoCollection<Document> collectionPaperObjQuestionAdditionalAnswer;

	@Autowired
	public PaperObjQuestionAdditionalAnswerService(TikuMongodb tikuMongodb) {
		this.collectionPaperObjQuestionAdditionalAnswer = tikuMongodb.getMongoDatabase()
			.getCollection(TikuMongodb.COLLECTION_PAPER_OBJ_QUESTION_ADDITIONAL_ANSWER);
	}

	public Document getByPaperId(Long paperId) {
		return collectionPaperObjQuestionAdditionalAnswer.find(eq("paperId", paperId)).first();
	}

	public List<Document> getByPaperIds(List<Long> paperIds) {
		return collectionPaperObjQuestionAdditionalAnswer.find(in("paperId", paperIds)).into(new ArrayList<>());
	}

	public void insertOne(Document paperObjQuestionAdditionalAnswer) {
		collectionPaperObjQuestionAdditionalAnswer.insertOne(paperObjQuestionAdditionalAnswer);
	}

	public void insertMany(List<Document> paperObjQuestionAdditionalAnswerList) {
		if (CollectionUtils.isEmpty(paperObjQuestionAdditionalAnswerList)) {
			return;
		}
		collectionPaperObjQuestionAdditionalAnswer.insertMany(paperObjQuestionAdditionalAnswerList);
	}

	public void updateByPaperId(Long paperId, Bson updateBson) {
		collectionPaperObjQuestionAdditionalAnswer.updateMany(eq("paperId", paperId), updateBson);
	}
}
