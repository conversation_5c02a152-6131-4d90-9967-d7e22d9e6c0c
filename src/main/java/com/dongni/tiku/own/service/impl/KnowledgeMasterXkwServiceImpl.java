package com.dongni.tiku.own.service.impl;

import com.dongni.common.master.version.service.BaseMasterThirdService;
import com.dongni.common.utils.AppendHashMap;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.enumeration.KnowledgeMappingType;
import com.dongni.tiku.own.service.BaseKnowledgeMasterThirdService;
import com.dongni.tiku.own.util.MongoDataConvertUtil;
import com.dongni.tiku.xkw.service.common.XkwCommonService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR> <br>
 * 2022/03/08 <br>
 * 学科网知识点 用于知识点母版数据
 */
@Service
public class KnowledgeMasterXkwServiceImpl extends BaseKnowledgeMasterThirdService {
    
    public KnowledgeMasterXkwServiceImpl() {
        super("xkw-");
    }
    
    @Autowired
    private XkwCommonService xkwCommonService;
    
    /** 同步的courseId -> 是否持续更新 */
    public static final Map<Long, Boolean> SYNC_COURSE_ID2_CONTINUOUS_UPDATE = Collections.unmodifiableMap(new AppendHashMap<Long, Boolean>()
            .append(   2L, true)    // 2 高中语文
            .append(   3L, true)    // 3 高中数学
            .append(   4L, true)    // 4 高中英语
            .append(   5L, true)    // 5 高中物理
            .append(   6L, true)    // 6 高中化学
            .append(   7L, true)    // 7 高中生物
            .append(   8L, true)    // 8 高中政治
            .append(   9L, true)    // 9 高中历史
            .append(  10L, true)    // 10 高中地理
            .append(1605L, true)    // 1605 初中科学
            .append(  25L, true)    // 25 高中信息技术
            .append(  26L, true)    // 26 高中通用技术
            .append(  12L, true)    // 12 初中语文
            .append(  13L, true)    // 13 初中数学
            .append(  14L, true)    // 14 初中英语
            .append(  15L, true)    // 15 初中物理
            .append(  16L, true)    // 16 初中化学
            .append(  17L, true)    // 17 初中生物
            .append(  18L, true)    // 18 初中道德与法治
            .append(  19L, true)    // 19 初中历史
            .append(  20L, true)    // 20 初中地理
            .append(  22L, true)    // 22 小学语文
            .append(  23L, true)    // 23 小学数学
            .append(  24L, true)    // 24 小学英语
            .append(  28L, true)    // 28 小学科学
            .append( 226L, true)    // 226 小学道德与法治
    );
    
    @Override
    public Set<Long> getKeySet() {
        return SYNC_COURSE_ID2_CONTINUOUS_UPDATE.keySet();
    }
    
    @Override
    public boolean continuousUpdate(long courseId) {
        Boolean continuous = SYNC_COURSE_ID2_CONTINUOUS_UPDATE.get(courseId);
        if (continuous == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "课程id不支持同步学科网知识点母版");
        }
        return continuous;
    }
    
    @Override
    public List<Map<String, Object>> getKnowledgeTreeList(Map<String, Object> courseInfo) {
        List<Map<String, Object>> courseKnowledgePointTreeList = xkwCommonService.getCourseKnowledgePointTree(courseInfo);
        List<Map<String, Object>> treeList = new ArrayList<>();
        transferTree(treeList, courseKnowledgePointTreeList);
        addCustomKnowledgeTree(treeList, courseInfo);
        return treeList;
    }
    
    @Override
    public void convertDocumentKnowledgeFields(Map<String, Object> newDoc, Map<String, Object> oldDoc) {
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwKnowledgeId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwKnowledgeName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwCourseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwKnowledgeParentId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwKnowledgeRootId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwKnowledgeType", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwKnowledgeOrdinal", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "xkwKnowledgeDepth", MapUtil::getInt);
    }
    
    
    private void transferTree(final List<Map<String, Object>> treeList,
                              final List<Map<String, Object>> xkwKnowledgeList) {
        if (CollectionUtils.isEmpty(xkwKnowledgeList)) {
            return;
        }
        
        for (Map<String, Object> xkwKnowledge : xkwKnowledgeList) {
            long xkwKnowledgeId = MapUtil.getLong(xkwKnowledge, "xkwKnowledgeId");
            String xkwKnowledgeName = MapUtil.getString(xkwKnowledge, "xkwKnowledgeName");
            String thirdKeyWithoutPrefix = String.valueOf(xkwKnowledgeId);
            
            List<Map<String, Object>> children = new ArrayList<>();
            putRequireInfoKnowledge(xkwKnowledge, thirdKeyWithoutPrefix, xkwKnowledgeName, false, children);
            
            treeList.add(xkwKnowledge);
            transferTree(children, MapUtil.getListMap(xkwKnowledge, "child"));
        }
    }
    
    @Override
    protected Map<String, Object> getKnowledgeMapping(Map<String, Object> knowledgeMasterDoc) {
        return new AppendHashMap<String, Object>()
                .append("fromType", KnowledgeMappingType.XKW.getType())
                .append("fromTypeName", KnowledgeMappingType.XKW.getTypeName())
                .append("fromKey", MapUtil.getLong(knowledgeMasterDoc, "xkwKnowledgeId"))
                .append("fromName", MapUtil.getString(knowledgeMasterDoc, "xkwKnowledgeName"))
                ;
    }
    
    /**
     * 添加自定义知识点
     * @param treeList   知识点树
     * @param courseInfo 课程信息 courseId
     */
    private void addCustomKnowledgeTree(List<Map<String, Object>> treeList, Map<String, Object> courseInfo) {
        long courseId = MapUtil.getLong(courseInfo, "courseId");
        List<Map<String, Object>> customKnowledgeTreeList = getCustomKnowledgeTreeList();
        Map<Long, List<Map<String, Object>>> courseId2CustomKnowledgeList = customKnowledgeTreeList.stream()
                .collect(groupingBy(item -> MapUtil.getLong(item, "courseId")));
        List<Map<String, Object>> courseCustomKnowledgeList = courseId2CustomKnowledgeList.get(courseId);
        if (CollectionUtils.isNotEmpty(courseCustomKnowledgeList)) {
            treeList.addAll(courseCustomKnowledgeList);
        }
    }
    
    /**
     * 获取自定义知识点
     * @return 自定义知识点
     */
    private List<Map<String, Object>> getCustomKnowledgeTreeList() {
        return getListMap(
                getCustomKnowledgeTree(4, "dongni-4-1", false, "完形填空", getListMap(
                        getCustomKnowledgeTree(4, "dongni-4-1-1", false, "选择型完形填空", null),
                        getCustomKnowledgeTree(4, "dongni-4-1-2", false, "非选择型完形填空", null),
                        null
                )),
                getCustomKnowledgeTree(4, "dongni-4-2", false, "阅读理解", getListMap(
                        getCustomKnowledgeTree(4, "dongni-4-2-1", false, "选择型阅读理解", null),
                        getCustomKnowledgeTree(4, "dongni-4-2-2", false, "非选择型阅读理解", null),
                        getCustomKnowledgeTree(4, "dongni-4-2-3", false, "阅读七选五", null),
                        getCustomKnowledgeTree(4, "dongni-4-2-4", false, "阅读六选四", null),
                        null
                )),
                getCustomKnowledgeTree(4, "dongni-4-3", false, "书面表达", getListMap(
                        getCustomKnowledgeTree(4, "dongni-4-3-1", false, "命题作文", null),
                        getCustomKnowledgeTree(4, "dongni-4-3-2", false, "题纲作文", null),
                        getCustomKnowledgeTree(4, "dongni-4-3-3", false, "看图作文", null),
                        getCustomKnowledgeTree(4, "dongni-4-3-4", false, "应用文写作", null),
                        getCustomKnowledgeTree(4, "dongni-4-3-5", false, "读后续写", null),
                        getCustomKnowledgeTree(4, "dongni-4-3-6", false, "概要写作", null),
                        null
                )),
                null
        );
    }
    
    /**
     * 获取自定义知识点
     * @param courseId      课程id
     * @param deleted       是否删除
     * @param knowledgeName 知识点名称
     * @param thirdKey      第三方key
     * @param children      子节点  可以为空
     * @return 自定义知识点
     */
    private Map<String, Object> getCustomKnowledgeTree(long courseId,
                                                       String thirdKey,
                                                       boolean deleted,
                                                       String knowledgeName,
                                                       List<Map<String, Object>> children) {
        Map<String, Object> customKnowledge = MapUtil.of(
                "courseId", courseId,
                BaseMasterThirdService.THIRD_KEY_FIELD, thirdKey,
                "deleted", deleted,
                "knowledgeName", knowledgeName,
                "children", children
        );
        checkRequireInfoKnowledge(customKnowledge);
        return customKnowledge;
    }
    
    @SafeVarargs
    private final <T> List<T> getListMap(T... maps) {
        return Stream.of(maps).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
