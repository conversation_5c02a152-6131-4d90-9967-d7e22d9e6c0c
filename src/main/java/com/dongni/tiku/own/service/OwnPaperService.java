package com.dongni.tiku.own.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.dongni.analysis.config.service.ExamConfigService;
import com.dongni.basedata.admin.service.ExaminerService;
import com.dongni.basedata.admin.service.IBaseUserService;
import com.dongni.basedata.export.area.service.CommonAreaService;
import com.dongni.basedata.export.clazz.service.CommonClassService;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.export.school.service.CommonSchoolService;
import com.dongni.basedata.export.teacher.service.ExamTeacherService;
import com.dongni.basedata.school.grade.service.impl.GradeServiceImpl;
import com.dongni.basedata.school.grade.util.GradeUtil;
import com.dongni.basedata.system.account.service.impl.UserRelativeServiceImpl;
import com.dongni.common.mongo.Order;
import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.IdUtil;
import com.dongni.common.utils.MongoUtil;
import com.dongni.common.utils.StreamUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.NumberFormatUtil;
import com.dongni.commons.utils.StringUtil;
import com.dongni.commons.utils.arithmetic.ScoreArithmetic;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.service.AnswerCardManualService;
import com.dongni.exam.common.mark.enums.UnitTypeEnum;
import com.dongni.exam.common.mark.serivice.basedata.ITodoTaskService;
import com.dongni.exam.common.mark.serivice.mark.IQnMappingClientService;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.serivice.paper.IPaperQsService;
import com.dongni.exam.common.mark.vo.KnowledgeInfoDTO;
import com.dongni.exam.common.mark.vo.PaperQuestionStructureInfoDTO;
import com.dongni.exam.common.mark.vo.QnMappingVO;
import com.dongni.exam.common.mark.vo.QuestionStructureVO;
import com.dongni.exam.mark.service.ExamMarkTodoService;
import com.dongni.exam.mark.service.IQnMappingService;
import com.dongni.exam.mark.service.IQuestionStructureService;
import com.dongni.exam.mark.service.impl.QuestionStructureServiceImpl;
import com.dongni.exam.mark.task.MarkUtil;
import com.dongni.exam.plan.service.ExamClassService;
import com.dongni.exam.plan.service.ExamCourseService;
import com.dongni.exam.plan.service.ExamHandleService;
import com.dongni.exam.plan.service.ExamInitService;
import com.dongni.exam.plan.service.ExamPaperService;
import com.dongni.exam.plan.service.ExamPlanTeacherService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.exam.question.service.ExamQuestionStructureService;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.enumeration.PaperAnswerCardStatus;
import com.dongni.tiku.common.enumeration.PaperAnswerStatus;
import com.dongni.tiku.common.enumeration.PaperQuestionStatus;
import com.dongni.tiku.common.util.KnowledgeUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.common.util.convert.TikuConvertUtil;
import com.dongni.tiku.common.util.question.QuestionUtil;
import com.dongni.tiku.entrust.service.dongni.EntrustQueryService;
import com.dongni.tiku.entrust.service.dongni.EntrustService;
import com.dongni.tiku.jinjuan.service.JinjuanPaperService;
import com.dongni.tiku.jinjuan.utils.JinjuanPaperUtil;
import com.dongni.tiku.manager.IPaperManager;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.manager.impl.QuestionManager;
import com.dongni.tiku.own.bean.param.UpdatePaperStructureParam;
import com.dongni.tiku.own.util.OwnPaperUtils;
import com.dongni.tiku.own.util.PaperUpdate;
import com.dongni.tiku.own.util.StudyGuideCopyUtil;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterFullInfoDTO;
import com.dongni.tiku.studyguide.service.StudyGuideChapterTikuService;
import com.dongni.tiku.render.service.TikuRenderQuestionByPaperTaskService;
import com.google.common.collect.Lists;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.ReplaceOptions;
import com.mongodb.client.model.Updates;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dongni.common.mongo.Order.Field.desc;
import static com.dongni.tiku.common.util.PaperUtil.validatePaperStructure;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Updates.addEachToSet;
import static com.mongodb.client.model.Updates.addToSet;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.pull;
import static com.mongodb.client.model.Updates.push;
import static com.mongodb.client.model.Updates.set;
import static com.mongodb.client.model.Updates.unset;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * 校本试卷
 *
 * <AUTHOR>
 * @date 2018/11/23 17:14
 */
@Service
public class OwnPaperService extends CommonPaperService implements IPaperQsService {

//    @Value("${dongni.windows.pdf}")
//    private String pdfServerUrl;

    private Logger log = LoggerFactory.getLogger(OwnPaperService.class);

    private MongoDatabase mongoDatabase;

    @Autowired
    public OwnPaperService(TikuMongodb tikuMongodb) {
        this.mongoDatabase = tikuMongodb.getMongoDatabase();
    }

    @Override
    protected IPaperManager getPaperManager() {
        return paperManager;
    }

    @Override
    protected CommonQuestionService getQuestionService() {
        return ownQuestionService;
    }

    @Override
    protected CommonAnswerCardTemplateService getAnswerCardTemplateService() {
        return answerCardTemplateService;
    }

    @Autowired
    private OwnPaperService selfService;
    @Autowired
    private PaperManager paperManager;
    @Autowired
    private OwnPaperCatalogService ownPaperCatalogService;
    @Autowired
    private JinjuanPaperService jinjuanPaperService;
    @Autowired
    private QuestionManager questionManager;
    @Autowired
    private OwnQuestionService ownQuestionService;
    @Autowired
    private AnswerCardTemplateService answerCardTemplateService;
    @Autowired
    private AnswerCardTemplateManualService answerCardTemplateManualService;
    @Autowired
    private CommonAreaService commonAreaService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private CommonSchoolService commonSchoolService;
    @Autowired
    private ExamHandleService examHandleService;
    @Autowired
    private ExamCourseService examCourseService;
    @Autowired
    private ExamQuestionStructureService questionStructureService;
    @Autowired
    private EntrustQueryService entrustQueryService;
    @Autowired
    private EntrustService entrustService;
    @Autowired
    private ExaminerService examinerService;
    @Autowired
    private ExamClassService examClassService;
    @Autowired
    private CommonClassService commonClassService;
    @Autowired
    private ExamTeacherService examTeacherService;
    @Autowired
    ExamRepository commonRepository;
    @Autowired
    ExamPlanTeacherService examPlanTeacherService;
    @Autowired
    private GradeServiceImpl gradeService;
    @Autowired
    private ExamInitService examInitService;
    @Autowired
    private UserRelativeServiceImpl userRelativeService;
    @Autowired
    private ExamService examService;
    @Autowired
    private PaperObjQuestionAdditionalAnswerService paperObjQuestionAdditionalAnswerService;

    @Autowired
    private ExamMarkTodoService examMarkTodoService;
    @Autowired
    private IBaseUserService baseUserService;
    @Autowired
    private MarkUtil markUtil;
    @Autowired
    private ITodoTaskService todoTaskService;
    @Autowired
    private IQsClientService iQsClientService;
    @Autowired
    private IQuestionStructureService iQuestionStructureService;
    @Autowired
    private IQnMappingService iQnMappingService;
    @Autowired
    private IQnMappingClientService qnMappingClientService;
    @Autowired
    private OwnUpdateAnswerService ownUpdateAnswerService;
    @Autowired
    private ExamConfigService examConfigService;
    
    @Autowired
    private StudyGuideChapterTikuService studyGuideChapterTikuService;
    @Autowired
    private QuestionStructureServiceImpl questionStructureServiceImpl;
    @Autowired
    private TikuRenderQuestionByPaperTaskService tikuRenderQuestionByPaperTaskService;
    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;

    /**
     * 更新试卷的试题分值
     *
     * @param params paperId questionNumber scoreValue
     */
    public void updatePaperQuestionScore(Map<String, Object> params) {
        Verify.of(params).isValidId("paperId").isNumeric("questionNumber").isNumeric("scoreValue").verify();

        // 参数初始化
        ScoreArithmetic sa = new ScoreArithmetic();
        String qn = params.get("questionNumber").toString();
        String scoreValue = params.get("scoreValue").toString();
        String originValue = null;

        // 想获取试卷
        long paperId = MapUtil.getLong(params, "paperId");
        Document paper = paperManager.getPaper(paperId, "试卷不存在或已被删除");
        Bson query = eq("paperId", Long.valueOf(params.get("paperId").toString()));

        String qnPath = null;
        String snPath = null;
        List<Map<String, Object>> parts = paper.get("parts", List.class);
        loop:
        for (int i = 0; i < parts.size(); i++) {
            Map<String, Object> part = parts.get(i);
            List<Map<String, Object>> categories = (List) part.get("categories");
            for (int j = 0; j < categories.size(); j++) {
                Map<String, Object> category = categories.get(j);

                // question
                List<Map<String, Object>> questions = (List) category.get("questions");
                for (int m = 0; m < questions.size(); m++) {
                    Map<String, Object> question = questions.get(m);
                    if (question.get("questionNumber").toString().equals(qn)) {
                        qnPath = "parts." + i + ".categories." + j + "." + "questions." + m + ".scoreValue";
                        originValue = question.get("scoreValue").toString();
                        break loop;
                    }

                    // structure
                    List<Map<String, Object>> structures = (List) question.get("structures");
                    for (int n = 0; n < structures.size(); n++) {
                        Map<String, Object> structure = structures.get(n);
                        if (structure.get("questionNumber").toString().equals(qn)) {
                            qnPath = "parts." + i + ".categories." + j + ".questions." + m + ".scoreValue";
                            snPath = "parts." + i + ".categories." + j + ".questions." + m + ".structures." + n + ".scoreValue";
                            originValue = structure.get("scoreValue").toString();
                            break loop;
                        }
                    }
                }
            }
        }

        if (qnPath == null || originValue == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "没有找到对应的试题");
        }

        String value = sa.subtract(scoreValue, originValue);

        Document qnScore = new Document("$set", new Document(qnPath, new Double(NumberFormatUtil.forMatScore(scoreValue))));
        paperManager.updatePaper(paperId, qnScore);

        if (snPath != null) {
            Document snScore = new Document("$set", new Document(snPath, new Double(NumberFormatUtil.forMatScore(scoreValue))).append(snPath.replace("scoreValue", "halfRight"), params.get("halfRight")));
            paperManager.updatePaper(paperId, snScore);
        }

        String originMark = paper.get("fullMark").toString();
        String fullMark = sa.add(originMark, value);
        String fullMarkFormat = new BigDecimal(fullMark).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        Document markScore = new Document("$set", new Document("fullMark", fullMarkFormat));
        paperManager.updatePaper(paperId, markScore);

        /*调整分值综合科目处理*/
        List<Map<String, Object>> newCourseFullMark = computeCourseFullMark(paperManager.getPaper(paperId));
        if (CollectionUtils.isNotEmpty(newCourseFullMark)) {
            update(paperId, set("courseFullMark", newCourseFullMark));
        }
    }

    /**
     * 生成试卷
     *
     * @param params userId userName paperName schoolId courseId courseName gradeType
     *               paperType year fullMark creationType areaId
     *               encryptStatus [password] parts
     * @return _id            试卷id 没啥用
     *         paperId        试卷id
     *         questionIdList 试题的idList
     */
    public Map<String, Object> insertPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNumeric("userType")
                .isNotBlank("paperName", "试卷名称不能为空")
                //.isNotNull("schoolIds") 联考员不需要
                .isValidId("courseId")
                .isNotBlank("courseName")
                .isNumeric("gradeType")
                .isNumeric("paperType")
                .isNumeric("year")
                .isDecimal("fullMark")
                .isNumeric("creationType")
                .isNumeric("areaId")
                .isNumeric("questionCount")
                .isNumeric("encryptStatus")
                .isNotNull("parts")
                .verify();

        Document paperDocument = getPaperDocument(params, false);

        // 如果是校本组卷，将试卷的区域设置为当前的学校区域
        if (ObjectUtil.isValueEquals(paperDocument.get("ascriptionType"), DictUtil.getDictValue("ascriptionType", "personal"))) {
            List<Long> schoolIds = (List<Long>) paperDocument.get("schoolIds");
            if (CollectionUtils.isNotEmpty(schoolIds) && schoolIds.size() == 1) {
                Map<String, Object> areaParams = new HashMap<>();
                areaParams.put("schoolId", schoolIds.get(0));
                Map<String, Object> schoolDetail = commonSchoolService.getSchoolDetail(areaParams);
                paperDocument.put("areaId", Long.valueOf(schoolDetail.get("areaId").toString()));
                paperDocument.put("areaCode", schoolDetail.get("areaCode").toString());
            }
        }

        if (ObjectUtil.isValidId(params.get("paperId"))) {
            paperDocument.put("paperId", Long.valueOf(params.get("paperId").toString()));
        }
        Map<String, Object> result = insertPaper(params, paperDocument);
        // 渲染试题图片任务
        long paperId = MapUtil.getLong(result, "paperId");
        try {
            myAsyncConfigurer.execute(() -> {
                tikuRenderQuestionByPaperTaskService.createRenderQuestionByPaperTask(paperId, "生成试卷");
            });
        } catch (TaskRejectedException e) {
            tikuRenderQuestionByPaperTaskService.createRenderQuestionByPaperTask(paperId, "生成试卷");
        }
        return result;
    }

    /**
     * 保存试卷啦
     * @param params        userType userId userName
     * @param paperDocument 试卷信息 直接存起来 请自行保证数据
     * @return _id paperId questionIdList
     */
    public Map<String, Object> insertPaper(Map<String, Object> params, Document paperDocument) {

        Map paperConfig = (Map) params.get("paperSetting");
        paperDocument.put("paperSetting",paperConfig);
        paperManager.insertOne(paperDocument);
        // 更新试题
        List<ObjectId> questionIdList = updateQuestionByPaper(params, paperDocument);

        Map<String, Object> result = new HashMap<>();
        result.put("_id", paperDocument.get("_id").toString());
        result.put("paperId", paperDocument.get("paperId").toString());
        result.put("questionIdList", questionIdList);

        // learnmore 2019年8月12日 保存试卷结构
        params.put("paperId", result.get("paperId"));
        questionStructureService.saveQuestionStructure(params);

        // 更新试卷组卷次数
        for (ObjectId questionId : questionIdList) {
            Map<String, Object> questionParams = new HashMap<>();
            questionParams.put("questionId", questionId.toString());
            ownQuestionService.incrementUsedCount(questionParams);
        }
        return result;
    }

//    public String wordToPdf(Map<String, Object> params) {
//        String docxUrl = (String) params.get("docxUrl");
//        String cdnHttp = CdnUtil.getCdnHttp();
//        docxUrl = cdnHttp + docxUrl;
//        RestTemplate restTemplate = new RestTemplate();
//        String url = pdfServerUrl;
//        //①：表单信息，需要放在MultiValueMap中，MultiValueMap相当于Map<String,List<String>>
//        HashMap body = new HashMap();
//        body.put("docxUrl",docxUrl);
//        //②：请求头
//        HttpHeaders headers = new HttpHeaders();
//        //调用set方法放入请求头
//        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
//        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
//        //③：请求实体：包含了请求体和请求头
//        HttpEntity httpEntity = new HttpEntity<>(body, headers);
//        //④：发送请求(url,请求实体，返回值需要转换的类型)
//        String result = restTemplate.postForObject(url, httpEntity, String.class);
//        HashMap parse = (HashMap) JSON.parse(result);
//        String pdfUrl = (String) parse.get("data");
//        return pdfUrl;
//    }

    /**
     * 考试-成绩导入模式 保存试卷
     * @param params 三件套
     * @param paperList 试卷 完整的数据 可以支持直接插入的数据
     */
    public void insertPaperForScoreImport(Map<String, Object> params, List<Document> paperList) {
        Verify.of(params).isValidId("userId").isNotBlank("userName").isInteger("userType").verify();
        if (CollectionUtils.isEmpty(paperList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷列表不能为空");
        }
        for (Document paper : paperList) {
            long paperId = paper.getLong("paperId");
            Document paperExist = paperManager.getPaperNullable(paperId);
            if (paperExist != null) {
                String paperName = paper.getString("paperName");
                String paperNameExist = paperExist.getString("paperName");
                if (!ObjectUtil.isValueEquals(paperName, paperNameExist)) {
                    throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                            "试卷id:" + paperId + "在题库已经存在，且试卷名称与本场考试不同，请联系管理员");
                }
                paper   .append("_id", new ObjectId(paperExist.get("_id").toString()))
                        .append("creatorId", paperExist.get("creatorId"))
                        .append("creatorName", paperExist.get("creatorName"))
                        .append("createDateTime", paperExist.get("createDateTime"));
            }
            paperManager.replacePaper(paperId, paper, new ReplaceOptions().upsert(true));
            Map<String, Object> saveQuestionStructureParams = MapUtil.copyWithUser(params, "paperId");
            saveQuestionStructureParams.put("paperId", paperId);
            questionStructureService.saveQuestionStructure(saveQuestionStructureParams);
        }
    }


    /**
     * 更新试题 插入试卷 or 更新试卷使用
     *   会将试题标记为已使用 (已使用的试题不允许删除)
     *
     * @param user userId userName userType
     * @param paperDocument 试卷信息
     * @return questionObjectIdList
     */
    private List<ObjectId> updateQuestionByPaper(Map<String, Object> user, Document paperDocument) {

        long userId = MapUtil.getLong(user, "userId");
        String userName = MapUtil.getString(user, "userName");
        int used = DictUtil.getDictValue("questionStatus", "used");
        List<Long> paperSchoolIds = MapUtil.getListLong(paperDocument, "schoolIds");
        String paperName = MapUtil.getString(paperDocument, "paperName");
        long areaId = MapUtil.getLong(paperDocument, "areaId");
        int gradeType = MapUtil.getInt(paperDocument, "gradeType");

        Set<ObjectId> paperQuestionIdSet = new HashSet<>();
        List<Map<String, Object>> insertQuestionList = new ArrayList<>();

        PaperUtil.forEachQuestion(paperDocument, item -> {
            Map<String, Object> paperQuestion = MapUtil.getMap(item, "question");
            String questionId = MapUtil.getStringNullable(paperQuestion, "_id");
            // 没有试题的不进行处理 下一个
            if (questionId == null) {return;}

            ObjectId objectId = new ObjectId(questionId);
            if (!paperQuestionIdSet.add(objectId)) { return; };

            Document question = questionManager.getNullable(objectId);

            // 题库中不存在 需要将试题插入到question
            if (MapUtils.isEmpty(question)) {
                Map<String, Object> insertQuestion = new HashMap<>(paperQuestion);
                insertQuestion.put("userId", userId);
                insertQuestion.put("userName", userName);
                QuestionUtil.setQuestionOperatorInfo(paperQuestion);
                insertQuestion.put("_id", objectId);
                insertQuestion.putIfAbsent("areaId", areaId);
                insertQuestion.putIfAbsent("gradeType", gradeType);
                insertQuestion.put("questionStatus", used);  // 已引用
                List<Long> questionSchoolIds = MapUtil.getListLong(insertQuestion, "schoolIds");
                questionSchoolIds.addAll(paperSchoolIds);
                questionSchoolIds = questionSchoolIds.stream().filter(Objects::nonNull).distinct().collect(toList());
                insertQuestion.put("schoolIds", questionSchoolIds);
                if (ObjectUtil.isBlank(insertQuestion.get("source"))) {
                    insertQuestion.put("source", paperName);
                }
                insertQuestionList.add(insertQuestion);
            }

            // 试题存在的 更新一些信息
            else {
                List<Bson> updateList = new ArrayList<>();
                Integer questionStatus = MapUtil.getIntNullable(question, "questionStatus");
                if (questionStatus == null || questionStatus != used) {
                    updateList.add(set("questionStatus", used));
                }
                if (ObjectUtil.isBlank(question.get("source"))) {
                    updateList.add(set("source", paperName));
                }
                Object schoolIds = question.get("schoolIds");
                if (schoolIds == null) {
                    if (question.containsKey("schoolIds")) {
                        updateList.add(set("schoolIds", paperSchoolIds));
                    } else {
                        updateList.add(addEachToSet("schoolIds", paperSchoolIds));
                    }
                } else {
                    List<Long> questionSchoolIds = MapUtil.getListLong(schoolIds);
                    List<Long> newQuestionSchoolIds = new ArrayList<>(paperSchoolIds);
                    newQuestionSchoolIds.removeAll(questionSchoolIds);
                    if (CollectionUtils.isNotEmpty(newQuestionSchoolIds)) {
                        updateList.add(addEachToSet("schoolIds", newQuestionSchoolIds));
                    }
                }
                if (CollectionUtils.isNotEmpty(updateList)) {
                    questionManager.update(objectId, combine(updateList));
                }
            }
        });

        for (Map<String, Object> insertQuestion : insertQuestionList) {
            Map<String, Object> question = QuestionUtil.getQuestion(insertQuestion);
            Document document = new Document(question);
            questionManager.replace(document, new ReplaceOptions().upsert(true));
        }

        return new ArrayList<>(paperQuestionIdSet);
    }

    /**
     * 生成试卷
     *
     * @param params userId userName paperName schoolId courseId courseName gradeType
     *               paperType year fullMark creationType areaId encryptStatus [password]
     * @return
     */
    public Map<String, Object> insertPaperBase(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNumeric("userType")
                .isNotBlank("paperName")
                // .isValidId("schoolId") 联考员不需要
                .isValidId("courseId")
                .isNotBlank("courseName")
                .isNumeric("gradeType")
                .isNumeric("paperType")
                .isNumeric("year")
                .isDecimal("fullMark")
                .isNumeric("creationType")
                .isNumeric("areaId")
                .isNumeric("questionCount")
                .isNumeric("encryptStatus")
                .verify();
        Document paperDocument = getPaperDocument(params, false);
        paperManager.insertOne(paperDocument);
        Map<String, Object> result = new HashMap<>();
        result.put("_id", paperDocument.get("_id").toString());
        result.put("paperId", paperDocument.get("paperId").toString());

        return result;
    }


    /**
     * 更新试卷的学校权限
     *
     * @param params paperId schoolIds
     */
    public void updatePaperSchool(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("paperId").isNotBlank("schoolIds").verify();

        // 更新试卷所属的学校
        long paperId = MapUtil.getLong(params, "paperId");
        Document paper = paperManager.getPaper(paperId, "试卷不存在，无法更新");

        // 更新试卷权限
        List<Long> schoolIds = MapUtil.getListLong(params, "schoolIds");
        Set<Long> ids = new HashSet<>(schoolIds);

        Bson updateDoc = combine(
                set("schoolIds", ids),
                set("modifierId", params.get("userId")),
                set("modifierName", params.get("userName")),
                set("modifyDateTime", TikuConvertUtil.convertDate2Long(new Date()))
        );

        paperManager.updatePaper(paperId, updateDoc);

        // 更新试题权限
        Set<String> questionIds = new HashSet<>();
        List<Document> parts = paper.get("parts", List.class);
        for (Document p : parts) {
            List<Document> categories = p.get("categories", List.class);
            for (Document c : categories) {
                List<Document> questions = c.get("questions", List.class);
                for (Document q : questions) {
                    Map question = (Map) q.get("question");
                    String questionId = (String) question.get("_id");
                    if (questionId != null) {
                        questionIds.add(questionId);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(questionIds)) {
            List<ObjectId> questionObjectIdList = questionIds.stream().map(ObjectId::new).collect(toList());
            // TODO  这里schoolIds有问题 如果试题有其他学校的，直接设置会丢失数据
            questionManager.update(questionObjectIdList, updateDoc);
        }
    }

    /**
     * 克隆试卷到指定学校(多个)
     *    paper
     *    [answerCardTemplate]
     *    [paperAdditionalAnswer]
     *
     * @param params paperId           克隆的原始paperId
     *               schoolTeacherList [{}] 克隆后的卷子的创建人信息
     *                   schoolId         克隆后的创建人归属学校
     *                   copyTeacherId    克隆后的创建人teacherId
     *                   copyTeacherName  克隆后的创建人teacherName
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void copyPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotEmptyCollections("schoolTeacherList")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        Document paper = paperManager.getPaper(paperId);
        List<Map<String, Object>> schoolTeacherList = MapUtil.getListMap(params, "schoolTeacherList");
        List<Long> schoolIds = schoolTeacherList.stream().map(item -> MapUtil.getLong(item, "schoolId")).distinct().collect(Collectors.toList());

        if (paper == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "试卷不存在");
        }

        if (ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS_NOT.getStatus())) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "答案未上传，无法共享");
        }

        long courseId = MapUtil.getLong(paper, "courseId");
        Map<String, Object> courseInfo = commonCourseService.getCourseById(MapUtil.of("courseId", courseId));
        if (MapUtils.isEmpty(courseInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "该试卷对应的课程不存在,请联系管理员处理");
        }
        int courseType = MapUtil.getInt(courseInfo, "courseType");
        if (ObjectUtil.isValueEquals(courseType, 2)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷对应的课程为校本课程,无法克隆");
        }


        // 将试卷的状态置为不可修改，避免共享过去以后被修改
        paper.append("paperStatus", DictUtil.getDictValue("paperStatus", "used"));
        paper.append("lockStatus", DictUtil.getDictValue("answerCardLockStatus", "lock"));
        paper.entrySet().removeIf(entry -> entry.getKey().startsWith("downloadUrl"));

        Document answerCardTemplate = answerCardTemplateService.getAnswerCardTemplateNullable(params);
        Document paperObjQuAdditionalAnswer = paperObjQuestionAdditionalAnswerService.getByPaperId(paperId);
        List<QuestionStructureVO> questionStructureVOList = questionStructureService.listQuestionStructure(paperId);
        List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(paperId);
        
        List<Document> paperList = new ArrayList<>();
        List<Document> answerCardTemplateList = new ArrayList<>();
        List<Document> paperObjQuAdditionalAnswerList = new ArrayList<>();


        List<Long> teacherIdList = schoolTeacherList.stream().filter(item -> ObjectUtil.isNotBlank(item.get("copyTeacherId"))).map(item -> MapUtil.getLong(item, "copyTeacherId")).distinct().collect(Collectors.toList());
        List<Map<String, Object>> teacherUserList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(teacherIdList)) {
            teacherUserList = baseUserService.getTeacherUserByTeacherIds(MapUtil.of("teacherIdList", teacherIdList));
        }
        Map<Long, Map<String, Object>> schoolTeacherMap = schoolTeacherList.stream().collect(toMap(item -> MapUtil.getLong(item, "schoolId"), item -> item));
        Map<Long, List<Map<String, Object>>> teacherUserListGroupByTeacherId = teacherUserList.stream().collect(groupingBy(item -> MapUtil.getLong(item, "teacherId")));


        for (Long schoolId : schoolIds) {
            Long newPaperId = IdUtil.getId();
            long creatorId = MapUtil.getLong(params, "userId");
            String creatorName = "【克隆】";
            // 试卷
            Document currentPaper = new Document(paper);
            List<Long> currentSchoolIds = Collections.singletonList(schoolId);
            Map<String, Object> schoolTeacher = Optional.ofNullable(schoolTeacherMap.get(schoolId)).orElse(new HashMap<>());
            if (MapUtils.isNotEmpty(schoolTeacher) && ObjectUtil.isNotBlank(schoolTeacher.get("copyTeacherId"))) {
                long copyTeacherId = MapUtil.getLong(schoolTeacher, "copyTeacherId");
                String copyTeacherName = MapUtil.getString(schoolTeacher, "copyTeacherName");
                List<Map<String, Object>> teacherUserItemList = Optional.ofNullable(teacherUserListGroupByTeacherId.get(copyTeacherId)).orElse(Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(teacherUserItemList)) {
                    creatorId = MapUtil.getLong(teacherUserItemList.get(0), "teacherId");
                    creatorName = copyTeacherName;
                }
            }
            currentPaper.put("schoolIds", currentSchoolIds);
            currentPaper.put("paperId", newPaperId);
            currentPaper.put("paperName", currentPaper.get("paperName"));
            currentPaper.put("paperStatus", 0);
            currentPaper.remove("_id");
            currentPaper.put("cloneStatus", DictUtil.getDictValue("paperCloneStatus", "cloned"));
            currentPaper.put("creatorId", creatorId);
            currentPaper.put("creatorName", creatorName);
            currentPaper.put("createDateTime", new Date());
            paperList.add(currentPaper);

            // 答题卡模版
            if (answerCardTemplate != null) {
                Document currentTemplate = new Document(answerCardTemplate);
                currentTemplate.put("schoolIds", currentSchoolIds);
                currentTemplate.put("paperId", newPaperId);
                currentTemplate.remove("_id");
                currentTemplate.put("creatorId", creatorId);
                currentTemplate.put("creatorName", creatorName);
                currentTemplate.put("createDateTime", new Date());
                answerCardTemplateList.add(currentTemplate);
            }

            // 客观题附加答案
            if (paperObjQuAdditionalAnswer != null) {
                Document currentAdditionalAnswer = new Document(paperObjQuAdditionalAnswer);
                currentAdditionalAnswer.put("paperId", newPaperId);
                currentAdditionalAnswer.remove("_id");
                currentAdditionalAnswer.put("creatorId", creatorId);
                currentAdditionalAnswer.put("creatorName", creatorName);
                currentAdditionalAnswer.put("createDateTime", new Date());
                paperObjQuAdditionalAnswerList.add(currentAdditionalAnswer);
            }
        }
        
        // learnmore 2019年8月12日 保存试卷结构
        for (Document currentPaper : paperList) {
            long newPaperId = MapUtil.getLong(currentPaper, "paperId");
            // t_question_structure
            iQuestionStructureService.saveQuestionStructure(newPaperId, questionStructureVOList);
            // t_question_mapping
            qnMappingClientService.saveQnMapping(newPaperId, qnMappingList);
        }
        // 生成新的试卷
        paperManager.insertMany(paperList);
        // 生成对应的答题卡模版
        answerCardTemplateService.saveAnswerCardTemplateList(answerCardTemplateList);
        // 生成对应的附加答案信息
        paperObjQuestionAdditionalAnswerService.insertMany(paperObjQuAdditionalAnswerList);
        
        // 试题添加学校数据 question.schoolIds
        List<ObjectId> questionIdList = Lists.newArrayList();
        List<Map<String, Object>> questions = PaperUtil.getQuestions(paper);
        questions.forEach(questionItem -> {
            String questionId = MapUtil.getString(questionItem, "_id");
            if (ObjectUtil.isNotBlank(questionId)) {
                ObjectId objectId = new ObjectId(questionId);
                questionIdList.add(objectId);
            }
        });

        List<Document> questionsInfoList = ownQuestionService.getQuestions(questionIdList);
        questionsInfoList.forEach(questionInfo -> {
            String id = MapUtil.getString(questionInfo, "_id");
            ObjectId objectId = new ObjectId(id);
            List<Long> questionSchoolIds = MapUtil.getListLong(questionInfo, "schoolIds");
            questionSchoolIds.addAll(schoolIds);
            questionSchoolIds = questionSchoolIds.stream().distinct().collect(toList());
            Bson updateBson = combine(set("schoolIds", questionSchoolIds));
            ownQuestionService.updateQuestion(objectId, updateBson);
        });
    }


    /**
     * 批量克隆
     * @param params paperIdList schoolIdList
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void batchCopyPaper(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("paperIdList")
                .isNotEmptyCollections("schoolTeacherList")
                .verify();

        List<Long> paperIdList = MapUtil.getListLong(params, "paperIdList");
        List<Map<String, Object>> schoolTeacherList = MapUtil.getListMap(params, "schoolTeacherList");
        List<Long> schoolIds = schoolTeacherList.stream().map(item -> MapUtil.getLong(item, "schoolId")).distinct().collect(Collectors.toList());
        List<Document> paperDocList = paperManager.getList(in("paperId", paperIdList));

        // 校验试卷是否支持copy
        checkPaper(paperDocList, params);

        // 组装paper和answerCardTemplate
        Map<String, List<Document>> paperAndAnswerCardTemplate = buildPaperAndAnswerCardTemplate(paperDocList, params);
        List<Document> paperList = paperAndAnswerCardTemplate.get("paperList");
        List<Document> answerCardTemplateList = paperAndAnswerCardTemplate.get("answerCardTemplateList");
        // 根据 oldPaperId - newPaperIds 复制客观题附加答案到克隆的试卷中
        Map<Long, List<Long>> oldPaperId2NewPaperIds = paperAndAnswerCardTemplate.get("paperRelationship").stream()
                .collect(toMap(x -> MapUtils.getLong(x, "oldPaperId"), x -> MapUtil.getListLong(x, "newPaperIds")));
        
        // learnmore 2019年8月12日 保存试卷结构
        for (Map.Entry<Long, List<Long>> entry : oldPaperId2NewPaperIds.entrySet()) {
            Long oldPaperId = entry.getKey();
            List<Long> newPaperIds = entry.getValue();
            if (CollectionUtils.isNotEmpty(newPaperIds)) {
                List<QuestionStructureVO> questionStructureVOList = questionStructureService.listQuestionStructure(oldPaperId);
                List<QnMappingVO> qnMappingList = qnMappingClientService.getQnMappingList(oldPaperId);
                for (Long newPaperId : newPaperIds) {
                    // t_question_structure
                    iQuestionStructureService.saveQuestionStructure(newPaperId, questionStructureVOList);
                    // t_question_mapping
                    qnMappingClientService.saveQnMapping(newPaperId, qnMappingList);
                }
            }
        }
        
        // 生成新的试卷
        paperManager.insertMany(paperList);
        // 生成对应的答题卡模版
        answerCardTemplateService.saveAnswerCardTemplateList(answerCardTemplateList);
        List<Document> paperAdditionalAnswers = paperObjQuestionAdditionalAnswerService.getByPaperIds(paperIdList);
        List<Document> paperObjQuAdditionalAnswerList = new ArrayList<>();
        long creatorId = MapUtil.getLong(params, "userId");
        String creatorName = "【克隆】";
        for (Document paperAdditionalAnswer : paperAdditionalAnswers) {
            Long oldPaperId = MapUtils.getLong(paperAdditionalAnswer, "paperId");
            for (Long newPaperId : oldPaperId2NewPaperIds.getOrDefault(oldPaperId, Collections.emptyList())) {
                Document currentAdditionalAnswer = new Document(paperAdditionalAnswer);
                currentAdditionalAnswer.put("paperId", newPaperId);
                currentAdditionalAnswer.remove("_id");
                currentAdditionalAnswer.put("creatorId", creatorId);
                currentAdditionalAnswer.put("creatorName", creatorName);
                currentAdditionalAnswer.put("createDateTime", new Date());
                paperObjQuAdditionalAnswerList.add(currentAdditionalAnswer);
            }
        }
        // 生成对应的附加答案信息
        paperObjQuestionAdditionalAnswerService.insertMany(paperObjQuAdditionalAnswerList);

        // 试题添加学校数据 question.schoolIds
        List<ObjectId> questionIdList = Lists.newArrayList();
        List<Map<String, Object>> questionList = Lists.newArrayList();
        paperDocList.forEach(paperDoc -> {
            List<Map<String, Object>> questions = PaperUtil.getQuestions(paperDoc);
            questionList.addAll(questions);
        });
        questionList.forEach(questionItem -> {
            String questionId = MapUtil.getString(questionItem, "_id");
            if (ObjectUtil.isNotBlank(questionId)) {
                ObjectId objectId = new ObjectId(questionId);
                questionIdList.add(objectId);
            }
        });

        List<Document> questionsInfoList = ownQuestionService.getQuestions(questionIdList);
        questionsInfoList.forEach(questionInfo -> {
            String id = MapUtil.getString(questionInfo, "_id");
            ObjectId objectId = new ObjectId(id);
            List<Long> questionSchoolIds = MapUtil.getListLong(questionInfo, "schoolIds");
            questionSchoolIds.addAll(schoolIds);
            questionSchoolIds = questionSchoolIds.stream().distinct().collect(toList());
            Bson updateBson = combine(set("schoolIds", questionSchoolIds));
            ownQuestionService.updateQuestion(objectId, updateBson);
        });
    }

    /**
     * 组装paper和answerCardTemplate
     *
     * @param paperDocList 需要copy的试卷列表
     * @param params       前端参数
     * @return
     */
    private Map<String, List<Document>> buildPaperAndAnswerCardTemplate(List<Document> paperDocList, Map<String, Object> params) {
        List<Map<String, Object>> schoolTeacherList = MapUtil.getListMap(params, "schoolTeacherList");
        List<Long> schoolIds = schoolTeacherList.stream().map(item -> MapUtil.getLong(item, "schoolId")).distinct().collect(Collectors.toList());

        // 将试卷的状态置为不可修改，避免共享过去以后被修改
        paperDocList.forEach(paper -> {
            paper.append("paperStatus", DictUtil.getDictValue("paperStatus", "used"));
            paper.append("lockStatus", DictUtil.getDictValue("answerCardLockStatus", "lock"));
            paper.entrySet().removeIf(entry -> entry.getKey().startsWith("downloadUrl"));
        });


        List<Document> answerCardTemplateDocList = answerCardTemplateService.getAnswerCardTemplateList(params);
        Map<Long, Document> answerCardTemplateDocMap = answerCardTemplateDocList.stream().collect(toMap(item -> MapUtil.getLong(item, "paperId"), item -> item));


        Map<String, List<Document>> paperAndAnswerCardTemplate = new HashMap<>(4);
        List<Document> paperList = new ArrayList<>();
        List<Document> answerCardTemplateList = new ArrayList<>();
        List<Document> paperRelationship = new ArrayList<>();
        paperAndAnswerCardTemplate.put("paperList", paperList);
        paperAndAnswerCardTemplate.put("answerCardTemplateList", answerCardTemplateList);
        paperAndAnswerCardTemplate.put("paperRelationship", paperRelationship);

        List<Long> teacherIdList = schoolTeacherList.stream().filter(item -> ObjectUtil.isNotBlank(item.get("copyTeacherId"))).map(item -> MapUtil.getLong(item, "copyTeacherId")).distinct().collect(Collectors.toList());
        List<Map<String, Object>> teacherUserList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(teacherIdList)) {
            teacherUserList = baseUserService.getTeacherUserByTeacherIds(MapUtil.of("teacherIdList", teacherIdList));
        }
        Map<Long, Map<String, Object>> schoolTeacherMap = schoolTeacherList.stream().collect(toMap(item -> MapUtil.getLong(item, "schoolId"), item -> item));
        Map<Long, List<Map<String, Object>>> teacherUserListGroupByTeacherId = teacherUserList.stream().collect(groupingBy(item -> MapUtil.getLong(item, "teacherId")));

        paperDocList.forEach(paper -> {
            long paperDocId = MapUtil.getLong(paper, "paperId");
            Document answerCardTemplateDoc = answerCardTemplateDocMap.get(paperDocId);
            List<Long> newPaperIds = new ArrayList<>();
            schoolIds.forEach(schoolId -> {
                Long paperId = IdUtil.getId();

                long creatorId = MapUtil.getLong(params, "userId");
                String creatorName = "【克隆】";

                // 试卷
                Document currentPaper = new Document(paper);
                List<Long> currentSchoolIds = Collections.singletonList(schoolId);
                Map<String, Object> schoolTeacher = Optional.ofNullable(schoolTeacherMap.get(schoolId)).orElse(new HashMap<>());
                if (MapUtils.isNotEmpty(schoolTeacher) && ObjectUtil.isNotBlank(schoolTeacher.get("copyTeacherId"))) {
                    long copyTeacherId = MapUtil.getLong(schoolTeacher, "copyTeacherId");
                    String copyTeacherName = MapUtil.getString(schoolTeacher, "copyTeacherName");
                    List<Map<String, Object>> teacherUserItemList = Optional.ofNullable(teacherUserListGroupByTeacherId.get(copyTeacherId)).orElse(Lists.newArrayList());
                    if (CollectionUtils.isNotEmpty(teacherUserItemList)) {
                        creatorId = MapUtil.getLong(teacherUserItemList.get(0), "teacherId");
                        creatorName = copyTeacherName;
                    }
                }

                currentPaper.put("schoolIds", currentSchoolIds);
                currentPaper.put("paperId", paperId);
                currentPaper.put("paperName", currentPaper.get("paperName"));
                currentPaper.put("paperStatus", 0);
                currentPaper.remove("_id");
                currentPaper.put("cloneStatus", DictUtil.getDictValue("paperCloneStatus", "cloned"));
                currentPaper.put("creatorId", creatorId);
                currentPaper.put("creatorName", creatorName);
                currentPaper.put("createDateTime", new Date());
                paperList.add(currentPaper);

                // 答题卡模版
                if (answerCardTemplateDoc != null) {
                    Document currentTemplate = new Document(answerCardTemplateDoc);
                    currentTemplate.put("schoolIds", currentSchoolIds);
                    currentTemplate.put("paperId", paperId);
                    currentTemplate.remove("_id");
                    currentTemplate.put("creatorId", creatorId);
                    currentTemplate.put("creatorName", creatorName);
                    currentTemplate.put("createDateTime", new Date());
                    answerCardTemplateList.add(currentTemplate);
                }

                newPaperIds.add(paperId);
            });
            paperRelationship.add(new Document().append("oldPaperId", paper.get("paperId")).append("newPaperIds", newPaperIds));
        });

        return paperAndAnswerCardTemplate;
    }

    /**
     * 校验试卷是否支持copy
     * @param paperDocList 需要copy的试卷列表
     * @param params       前端参数
     */
    private void checkPaper(List<Document> paperDocList, Map<String, Object> params) {

        List<Long> paperIdList = MapUtil.getListLong(params, "paperIdList");

        // 校验试卷是否存在
        List<Long> paperDocIdList = paperDocList.stream().map(paper -> MapUtil.getLong(paper, "paperId")).distinct().collect(toList());
        Collection<Long> paperIdDisjunctionList = CollUtil.disjunction(paperIdList, paperDocIdList);
        if (CollectionUtils.isNotEmpty(paperIdDisjunctionList)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, String.format("试卷%s不存在", paperIdDisjunctionList));
        }

        // 校验答案是否上传
        List<Long> paperIdNoAnswerList = paperDocList.stream()
                .filter(paper -> ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS_NOT.getStatus()))
                .map(paper -> MapUtil.getLong(paper, "paperId"))
                .distinct().collect(toList());
        if (CollectionUtils.isNotEmpty(paperIdNoAnswerList)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, String.format("试卷：%s 未上传答案，无法共享", paperIdNoAnswerList));
        }

        List<Long> courseIdList = paperDocList.stream().map(paper -> MapUtil.getLong(paper, "courseId")).distinct().collect(toList());
        Map<Long, Map<String, Object>> courseInfoMap = commonCourseService.getCourseById(courseIdList);
        List<Map<String, Object>> notExistCourseList = Lists.newArrayList();
        List<Map<String, Object>> schoolCourseList = Lists.newArrayList();
        paperDocList.forEach(paper -> {
            long courseId = MapUtil.getLong(paper, "courseId");
            String courseName = MapUtil.getString(paper, "courseName");
            long paperId = MapUtil.getLong(paper, "paperId");
            Map<String, Object> courseInfo = courseInfoMap.get(courseId);
            HashMap<String, Object> exceptionCourse = new HashMap<>(4);
            exceptionCourse.put("courseId", courseId);
            exceptionCourse.put("courseName", courseName);
            exceptionCourse.put("paperId", paperId);
            if (MapUtils.isEmpty(courseInfo)) {
                notExistCourseList.add(exceptionCourse);
                return;
            }

            int courseType = MapUtil.getInt(courseInfo, "courseType");
            if (ObjectUtil.isValueEquals(courseType, 2)) {
                schoolCourseList.add(exceptionCourse);
            }
        });
        if (CollectionUtils.isNotEmpty(notExistCourseList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, String.format("试卷%s 对应的课程不存在,请联系管理员处理",notExistCourseList));
        }

        if (CollectionUtils.isNotEmpty(notExistCourseList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, String.format("试卷%s 对应的课程为校本课程,无法克隆", schoolCourseList));
        }
    }
    
    
    /**
     * 更新试卷 第三方无答题卡发布考试专用
     *   {@link #updatePaper(Map)}
     *   包了一层 加锁 限制
     * @param params paperId
     * @return
     */
    public Map<String, Object> updatePaperForTemplateDraw(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();
        long paperId = MapUtil.getLong(params, "paperId");
        String lockKey = "TIKU:PAPER:UPDATE:FOR:TEMPLATE_DRAW:PAPER_ID:" + paperId;
        return JedisTemplate.lockExecute(lockKey, 0,"有其他人正在操作，请稍后再试", () -> {
            Document paper = paperManager.getPaperSimple(paperId);
            int questionCount = MapUtil.getInt(paper, "questionCount", 0);
            if (questionCount > 0) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "答题卡已经绘制过了，请刷新页面");
            }
            params.put("__updatePaperForTemplateDraw", true);
            Long examId = null;
            if(params.get("examId")!=null) {
                examId = Long.valueOf(params.get("examId").toString());
                params.remove("examId");
            }
            Map<String, Object> resultMap = updatePaper(params);
            if(examId!=null) {
                todoTaskService.addArrangeTodo4MarkManagers(examId, paperId);
            }
            return resultMap;
        });
    }
    
    /**
     * 更新试卷
     *
     * @param params userId userName _id paperName courseId gradeType
     *               paperStatus paperType year fullMark creationType areaId
     *               encryptStatus [password] parts
     *               [isCombineQuestion] 是否诊断合并-合并试题后调用的接口(前端传参)
     *                      -- 试题合并已经在com.dongni.exam.maintain.service.ExamCombineService.beforeCombineQuestionUpdatePaper这里修改questionStructure
     *               [__updatePaperForTemplateDraw] 第三方无答题卡发布考试专用更新试卷
     * @return
     */
    public Map<String, Object> updatePaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNumeric("userType")
                .isNotBlank("paperName")
                .isValidId("courseId")
                .isNotBlank("courseName")
                .isNumeric("gradeType")
                .isNumeric("paperType")
                .isNumeric("year")
                .isDecimal("fullMark")
                .isNumeric("creationType")
                .isNumeric("areaId")
                .isNumeric("questionCount")
                .isNotBlank("_id")
                .isNumeric("encryptStatus")
                .isNotNull("parts")
                .verify();

        Document paperDocument = getPaperDocument(params, true);


        Map paperConfig = (Map) params.get("paperSetting");
        paperDocument.put("paperSetting",paperConfig);
        paperDocument.put("_id", new ObjectId(params.get("_id").toString()));

        Bson query = getIdQueryParams(params);

        Document paper = paperManager.getFirst(query);

        if (paper == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "试卷不存在，无法更新");
        }

        // 获取旧试卷的试题和新试卷的试题
        List<Map<String, Object>> oldQuestions = PaperUtil.getQuestions(paper);
        List<Map<String, Object>> newQuestions = PaperUtil.getQuestions(paperDocument);

        // 诊断合并-合并试题调用的接口 - 断开与原始试卷的关系
        boolean isCombineQuestion = MapUtil.getBoolean(params, "isCombineQuestion");
        if (isCombineQuestion) {
            paperDocument.put("sourcePaperId", null);
            if (PaperUtil.isExamPaper(paperDocument)) {
                paperDocument.put("entrustId", null);
            }
        }
        paperDocument.put("paperId", paper.get("paperId"));
        paperManager.replaceOne(query, paperDocument);

        // 更新试题
        updateQuestionByPaper(params, paperDocument);

        // 更新答题卡状态为已修改锁定
        Bson updateBson = set("lockStatus", DictUtil.getDictValue("answerCardLockStatus", "modify"));
        paperManager.updateOne(query, updateBson);

        Map<String, Object> result = new HashMap<>();
        result.put("_id", paperDocument.get("_id").toString());
        result.put("paperId", paperDocument.get("paperId").toString());

        // learnmore 2019年8月12日 保存试卷结构
        params.put("paperId", result.get("paperId"));
        boolean isUpdatePaperAnswer = MapUtil.getBoolean(params, "isUpdatePaperAnswer");
        boolean updatePaperForTemplateDraw = MapUtil.getBoolean(params, "__updatePaperForTemplateDraw");
        if ((!isUpdatePaperAnswer && !isCombineQuestion) || updatePaperForTemplateDraw) {
            // 更新t_question_structure
            questionStructureService.saveQuestionStructure(params);
            // 更新答题卡的题号和分值
            answerCardTemplateService.updateQuestionNumberAndScore(params, paperDocument);
        }

        // 更新试题组卷次数
        Map<String, Map<String, Object>> newQuestionMap = newQuestions.stream()
                .collect(toMap(item -> item.get("_id").toString(), item -> item));
        Map<String, Map<String, Object>> oldQuestionMap = oldQuestions.stream()
                .collect(toMap(item -> item.get("_id").toString(), item -> item));
        Map<String, Object> questionParams = new HashMap<>();
        for (Map<String, Object> question : oldQuestions) {
            if (newQuestionMap.get(question.get("_id").toString()) == null) {
                // 删除的试题，组卷次数自减
                questionParams.put("questionId", question.get("_id").toString());
                ownQuestionService.decrementUsedCount(questionParams);
            }
        }
        for (Map<String, Object> question : newQuestions) {
            if (oldQuestionMap.get(question.get("_id").toString()) == null) {
                // 新增的试题，组卷次数自减
                questionParams.put("questionId", question.get("_id").toString());
                ownQuestionService.incrementUsedCount(questionParams);
            }
        }
        
        // 渲染试题图片任务
        long paperId = MapUtil.getLong(result, "paperId");
        try {
            myAsyncConfigurer.execute(() -> {
                tikuRenderQuestionByPaperTaskService.createRenderQuestionByPaperTask(paperId, "更新试卷");
            });
        } catch (TaskRejectedException e) {
            tikuRenderQuestionByPaperTaskService.createRenderQuestionByPaperTask(paperId, "更新试卷");
        }
        return result;
    }

    /**
     * 更新试卷
     *
     * @param params userId userName _id paperName courseId gradeType paperStatus paperType
     *               year fullMark creationType areaId encryptStatus [password]
     * @return
     */
    public Map<String, Object> updatePaperBase(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNumeric("userType")
                .isNotBlank("_id")
                .isNotBlank("paperName")
                .isNotBlank("schoolIds")
                .isValidId("courseId")
                .isNotBlank("courseName")
                .isNumeric("gradeType")
                .isNumeric("paperType")
                .isNumeric("year")
                .isDecimal("fullMark")
                .isNumeric("creationType")
                .isNumeric("areaId")
                .isNumeric("questionCount")
                .isNumeric("encryptStatus")
                .verify();

        Document paperDocument = getPaperDocument(params, true);
        paperDocument.put("_id", new ObjectId(params.get("_id").toString()));

        Bson query = getIdQueryParams(params);

        Document paper = paperManager.getFirst(query);

        if (paper == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "试卷不存在，无法更新");
        }

        paperManager.replaceOne(query, paperDocument);

        Map<String, Object> result = new HashMap<>();
        result.put("_id", paperDocument.get("_id").toString());
        result.put("paperId", paperDocument.get("paperId").toString());

        return result;

    }

    // /**
    //  * 更新答案
    //  *
    //  * @param params userId userName _id paperName schoolIds courseId courseName gradeType
    //  *               paperType year fullMark creationType areaId
    //  *               encryptStatus [password] parts [additionalInfo]
    //  * @return
    //  */
    // @DistributeLock(moduleName="EXAM", name="UpdatePaperStructure", argValueKeys = {"[0].paperId"}, expireTime = -1)
    // public void updatePaperAnswerOld(Map<String, Object> params) {
    //     Verify.of(params)
    //             .isValidId("userId")
    //             .isNotBlank("userName")
    //             .isNumeric("userType")
    //             .isNotBlank("_id")
    //             .isNotBlank("paperName")
    //             .isValidId("courseId")
    //             .isNotBlank("courseName")
    //             .isNumeric("gradeType")
    //             .isNumeric("paperType")
    //             .isNumeric("year")
    //             .isNumeric("lockStatus")
    //             .isDecimal("fullMark")
    //             .isNumeric("creationType")
    //             .isNumeric("areaId")
    //             .isNumeric("questionCount")
    //             .isNumeric("encryptStatus")
    //             .isNotNull("parts")
    //             .verify();
    //
    //     params.put("checkPaperStructure", true);
    //     List<Long> paperIds = paperService.getClonePaperIds(MapUtil.getLong(params, "paperId"));
    //
    //     for (Long paperId : paperIds) {
    //
    //         params.put("paperId", paperId);
    //
    //         // 更新试卷为有答案
    //         Bson query = getIdQueryParams(params);
    //
    //         Document paper = paperManager.getFirst(query);
    //
    //         if (paper == null) {
    //             log.error("试卷paperId:{}不存在，无法更新", paperId);
    //             continue;
    //         }
    //
    //         if (OwnPaperUtils.isPaperAnswerUnEditable(paper)) {
    //             log.info("当前试卷paperId:{}答案状态无法更新", paperId);
    //             continue;
    //         }
    //
    //         // 设置了客观题附加答案时，校验附加答案是否正确
    //         List<Map<String, Object>> additionalAnswer = Optional.ofNullable(MapUtil.getListMap(params, "additionalAnswer"))
    //                 .orElse(Collections.emptyList());
    //         PaperUtil.checkAdditionalAnswerAndFixScore(PaperUtil.getPaperStructure(PaperUtil.getPaper(params)),
    //                 additionalAnswer.stream().collect(Collectors.toMap(x -> MapUtils.getString(x, "questionNumber"), x -> x)));
    //
    //         // 更新试卷
    //         params.put("isUpdatePaperAnswer", true);
    //         updatePaper(params);
    //
    //         Document updateDoc = new Document();
    //         updateDoc.put("$set", new Document("answerStatus", PaperAnswerStatus.HAS.getStatus()));
    //         paperManager.updateOne(query, updateDoc);
    //
    //         // 更新答题卡锁定状态
    //         Bson updateBson = set("lockStatus", Integer.valueOf(params.get("lockStatus").toString()));
    //         paperManager.updateOne(query, updateBson);
    //
    //         // 保存附加答案
    //         if (CollectionUtils.isNotEmpty(additionalAnswer)) {
    //             Document additionalDoc = paperObjQuestionAdditionalAnswerService.getByPaperId(paperId);
    //             if (additionalDoc == null) {
    //                 additionalDoc = new Document().append("paperId", paperId).append("additionalAnswer", additionalAnswer)
    //                         .append("creatorId", params.get("userId")).append("creatorName", params.get("userName"))
    //                         .append("createDateTime", new Date());
    //                 paperObjQuestionAdditionalAnswerService.insertOne(additionalDoc);
    //             } else {
    //                 paperObjQuestionAdditionalAnswerService.updateByPaperId(paperId, set("additionalAnswer", additionalAnswer));
    //             }
    //         }
    //
    //         // 更新状态和待办
    //         examService.batchUpdateExamByUploadPaperAnswer(params, additionalAnswer);
    //     }
    // }

    /**
     * 更新答案
     *      1001    sourceId: null
     *      100101  sourceId: 1001
     *      100102  sourceId: 1001
     *      100103  sourceId: 1001 已经有答案了
     *    所有操作找到最来源的试卷id(1001)，对所有没有答案的试卷(1001 100101 100102)进行设置答案，对于已经有答案的100103不更新
     *
     * @param params paperId          试卷id
     *               fullMark         总分
     *               parts            前端设置了答案的实体结构信息
     *               additionalAnswer 附加答案信息
     *               [entrustId]      委托录题id
     */
    public void updatePaperAnswer(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isDecimal("fullMark")
                .isNotNull("parts")
                .verify();
        
        // [additionalAnswer] 附加答案数据参数
        List<Map<String, Object>> additionalAnswer = Optional.ofNullable(MapUtil.getListMap(params, "additionalAnswer"))
                .orElse(Collections.emptyList());
        Map<String, Map<String, Object>> questionNumber2AdditionalAnswer = additionalAnswer.stream()
                .collect(toMap(x -> MapUtils.getString(x, "questionNumber"), x -> x));
        // parts 前端传来的带答案的parts转为试题结构
        List<Map<String, Object>> hasAnswerQuestionStructureList = PaperUtil.getPaperStructure(params);
        // 校验附加答案是否正确
        PaperUtil.checkAdditionalAnswerAndFixScore(hasAnswerQuestionStructureList, questionNumber2AdditionalAnswer);
        // 客观题题号 -> 信息
        Map<Integer, Map<String, Object>> questionNumber2HasAnswerStructure  = hasAnswerQuestionStructureList.stream()
                .collect(toMap(structure -> MapUtil.getInt(structure, "questionNumber"), structure -> structure));
        
        // 总分
        String fullMark = MapUtil.getTrim(params, "fullMark");
        // 委托id
        Long entrustId = MapUtil.getLongNullable(params, "entrustId");
        
        // 获取最原始的paperId 并加锁执行
        long paramsPaperId = MapUtil.getLong(params, "paperId");
        Document paramsPaper = paperManager.getPaper(paramsPaperId);
        Long sourcePaperId = MapUtil.getLongNullable(paramsPaper, "sourcePaperId");
        
        Long lockRootPaperId = Optional.ofNullable(sourcePaperId).orElse(paramsPaperId);
        JedisTemplate.lockExecute("TIKU:PAPER:ANSWER:UPDATE:PAPER_ID:" + lockRootPaperId,
                2000, "有其他人正在上传答案，请稍后再试", () -> {
                    // 需要更新答案的试卷List
                    List<Document> allPaperList = getPaperListForUpdatePaperAnswer(paramsPaper);
                    // 获取需要更新的paper answerCardTemplate
                    List<Map<String, Object>> paperUpdateInfoList = getPaperUpdateInfoListForUpdatePaperAnswer(
                            paramsPaperId,
                            allPaperList,
                            fullMark,
                            entrustId,
                            hasAnswerQuestionStructureList,
                            questionNumber2HasAnswerStructure,
                            questionNumber2AdditionalAnswer
                    );
                    // 执行更新操作
                    for (Map<String, Object> paperUpdateInfo : paperUpdateInfoList) {
                        long paperId = MapUtil.getLong(paperUpdateInfo, "paperId");
                        List<Bson> paperSetBsonList = MapUtil.getCast(paperUpdateInfo, "paperSetBsonList");
                        if (CollectionUtils.isNotEmpty(paperSetBsonList)) {
                            paperManager.updatePaper(paperId, combine(paperSetBsonList));
                        }
                        List<Bson> answerCardTemplateSetBsonList = MapUtil.getCast(paperUpdateInfo, "answerCardTemplateSetBsonList");
                        if (CollectionUtils.isNotEmpty(answerCardTemplateSetBsonList)) {
                            answerCardTemplateService.updateOne(eq("paperId", paperId), combine(answerCardTemplateSetBsonList));
                        }
                        List<QuestionStructureVO> questionStructureUpdateList = MapUtil.getCast(paperUpdateInfo, "questionStructureUpdateList");
                        if (CollectionUtils.isNotEmpty(questionStructureUpdateList)) {
                            iQuestionStructureService.saveQuestionStructure(paperId, questionStructureUpdateList);
                        }
                        
                        // 保存附加答案
                        if (CollectionUtils.isNotEmpty(additionalAnswer)) {
                            Document additionalDoc = paperObjQuestionAdditionalAnswerService.getByPaperId(paperId);
                            if (additionalDoc == null) {
                                additionalDoc = new Document()
                                        .append("paperId", paperId)
                                        .append("additionalAnswer", additionalAnswer)
                                        .append("creatorId", params.get("userId"))
                                        .append("creatorName", params.get("userName"))
                                        .append("createDateTime", new Date());
                                paperObjQuestionAdditionalAnswerService.insertOne(additionalDoc);
                            } else {
                                paperObjQuestionAdditionalAnswerService.updateByPaperId(paperId, set("additionalAnswer", additionalAnswer));
                            }
                        }
                        // 更新状态和待办
                        try {
                            params.put("paperId", paperId);
                            examService.batchUpdateExamByUploadPaperAnswer(params, additionalAnswer);
                        } catch (Exception e) {
                            log.warn("上传答案后，批量更新试卷状态和客观题分数，并删除待办，出现异常: paramsPaperId: {}, paperId: {}", paramsPaperId, paperId, e);
                        }
                    }
                    return true;
                });
    }

    /**
     * 上传委托时设置答题卡的委托id
     *
     * @param params paperId          试卷id
     *               entrustId        委托录题id
     */
    public void setPaperEntrust(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isValidId("entrustId")
                .verify();

        // 委托id
        Long entrustId = MapUtil.getLong(params, "entrustId");

        // parts 前端传来的parts转为试题结构
        List<Map<String, Object>> hasAnswerQuestionStructureList = PaperUtil.getPaperStructure(params);

        // 获取最原始的paperId 并加锁执行
        long paramsPaperId = MapUtil.getLong(params, "paperId");
        Document paramsPaper = paperManager.getPaper(paramsPaperId);
        Long sourcePaperId = MapUtil.getLongNullable(paramsPaper, "sourcePaperId");

        Long lockRootPaperId = Optional.ofNullable(sourcePaperId).orElse(paramsPaperId);
        JedisTemplate.lockExecute("TIKU:PAPER:UPLOAD:ENTRUST:PAPER_ID:" + lockRootPaperId,
                2000, "有其他人正在上传委托，请稍后再试", () -> {
                    // 需要更新的试卷List
                    List<Document> allPaperList = ownUpdateAnswerService.getPaperListForUpdateQuestionAnswer(paramsPaperId);
                    // 先校验结构
                    for (Document paper : allPaperList) {
                        // 校验前端参数试卷和mongo里试卷的结构是否一致
                        List<Map<String, Object>> paperStructureList = PaperUtil.getPaperStructure(paper);
                        try {
                            validatePaperStructure(hasAnswerQuestionStructureList, paperStructureList, false);
                        } catch (Exception e) {
                            throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                                    "当前页面试卷试题结构与需要更新的试卷试题结构不一致: paperId: " + MapUtil.getLong(paper, "paperId"));
                        }
                    }

                    Date now = new Date();
                    for (Map<String, Object> paperUpdateInfo : allPaperList) {
                        // 如果关联的试卷已经有委托编号了不处理，不然会被设置为新的
                        Long currentPaperEntrustId = MapUtil.getLongNullable(paperUpdateInfo, "entrustId");
                        if (currentPaperEntrustId != null) {
                            continue;
                        }

                        long paperId = MapUtil.getLong(paperUpdateInfo, "paperId");

                        List<Bson> paperSetBsonList = new ArrayList<>();
                        // 更新答题卡锁定状态
                        Integer answerCardLockStatusLock = DictUtil.getDictValue("answerCardLockStatus", "lock");
                        if (!answerCardLockStatusLock.equals(MapUtil.getIntNullable(paperUpdateInfo, "lockStatus"))) {
                            paperSetBsonList.add(set("lockStatus", answerCardLockStatusLock));
                        }

                        // 委托id
                        paperSetBsonList.add(set("entrustId", entrustId));

                        // 如果答题卡全是主观题 - 设置答案已上传
                        List<Map<String, Object>> paperStructureList = PaperUtil.getPaperStructure(paperUpdateInfo);
                        boolean hasObjectiveQuestion = paperStructureList.stream()
                                .anyMatch(i -> DictUtil.isEquals(MapUtil.getInt(i, "readType"), "readType", "objective"));
                        if (!hasObjectiveQuestion) {
                            paperSetBsonList.add(set("answerStatus", PaperAnswerStatus.HAS.getStatus()));
                        }

                        paperSetBsonList.add(set("modifierId", MapUtil.getLong(params, "userId")));
                        paperSetBsonList.add(set("modifierName", MapUtil.getString(params, "userName")));
                        paperSetBsonList.add(set("modifyDateTime", now));

                        paperManager.updatePaper(paperId, combine(paperSetBsonList));
                    }
                    return true;
                });
    }
    
    /**
     * 获取所有更新答案操作需要的试卷信息List
     *   paperId: 1001    sourcePaperId: null answerStatus: 1
     *   paperId: 100101  sourcePaperId: 1001 answerStatus: 0
     *   paperId: 100102  sourcePaperId: 1001 answerStatus: 0
     *   paperId: 100103  sourcePaperId: 1001 answerStatus: 1
     *   -- 输入输出示例及说明:
     *   input: paramsPaperId: 1001    output: [1001, 100101, 100102        ]  说明: 100103已经有答案了不会被更新
     *   input: paramsPaperId: 100101  output: [      100101, 100102        ]  说明: 1001,100103已经有答案了不会被更新
     *   input: paramsPaperId: 100102  output: [      100101, 100102        ]  说明: 1001,100103已经有答案了不会被更新
     *   input: paramsPaperId: 100103  output: [      100101, 100102, 100103]  说明: 1001已经有答案了不会被更新
     * @param paper   需要更新答案的试卷信息 请求操作时的那个
     * @return 所有需要更新答案的试卷信息
     */
    protected List<Document> getPaperListForUpdatePaperAnswer(Document paper) {
        long paperId = MapUtil.getLong(paper, "paperId");
        Long sourcePaperId = MapUtil.getLongNullable(paper, "sourcePaperId");
        // 获取所有的需要更新答案的试卷 最原始的+复制出来的考试用卷 且 没有上传过答案的(指定的除外)
        List<Document> allPaperList = new ArrayList<>();
        if (sourcePaperId == null) {
            // 更新的为最原始试卷
            allPaperList.add(paper);
            // 找复制的试卷且没有答案的
            List<Document> hasNotAnswerList = paperManager.getList(and(
                    eq("sourcePaperId", paperId),
                    ne("answerStatus", PaperAnswerStatus.HAS.getStatus())
            ));
            allPaperList.addAll(hasNotAnswerList);
        } else {
            // 最原始试卷
            Document sourcePaper = paperManager.getPaper(sourcePaperId);
            if (!PaperUtil.hasAnswer(sourcePaper)) {
                // 没有上传过答案的
                allPaperList.add(sourcePaper);
            }
            // 复制的试卷且没有答案的
            List<Document> hasNotAnswerList = paperManager.getList(and(
                    eq("sourcePaperId", sourcePaperId),
                    ne("answerStatus", PaperAnswerStatus.HAS.getStatus())
            ));
            boolean paperInList = hasNotAnswerList.stream()
                    .map(hasNotAnswerPaper -> MapUtil.getLong(hasNotAnswerPaper, "paperId"))
                    .anyMatch(hasNotAnswerPaperId -> hasNotAnswerPaperId == paperId);
            if (!paperInList) {
                allPaperList.add(paper);
            }
            allPaperList.addAll(hasNotAnswerList);
        }
        return allPaperList;
    }
    
    /**
     * 获取更新信息
     * @param paramsPaperId 页面操作的上传答案的试卷id
     * @param allPaperList                      所有需要更新的试卷信息
     * @param hasAnswerQuestionStructureList    页面提交的含有答案的试题结构信息
     * @param questionNumber2HasAnswerStructure questionNumber -> 含有答案的结构信息
     * @param questionNumber2AdditionalAnswer   questionNumber -> 附加答案信息
     * @return item: 更新的操作信息
     *           paperId 试卷id
     *           paperSetBsonList 试卷需要更新的信息
     *           answerCardTemplateSetBsonList 系统答题卡需要更新的数据
     */
    private List<Map<String, Object>> getPaperUpdateInfoListForUpdatePaperAnswer(long paramsPaperId,
                                                                                 List<Document> allPaperList,
                                                                                 String newFullMark,
                                                                                 Long entrustId,
                                                                                 List<Map<String, Object>> hasAnswerQuestionStructureList,
                                                                                 Map<Integer, Map<String, Object>> questionNumber2HasAnswerStructure,
                                                                                 Map<String, Map<String, Object>> questionNumber2AdditionalAnswer) {
        
        
        List<Map<String, Object>> paperUpdateInfoList = new ArrayList<>();
        for (Document paper : allPaperList) {
            long paperId = MapUtil.getLong(paper, "paperId");
            boolean isOperatePaper = paramsPaperId == paperId;
            
            Map<String, Object> updateInfo = new HashMap<>();
            updateInfo.put("paperId", paperId);
            paperUpdateInfoList.add(updateInfo);
            
            List<Map<String, Object>> paperStructureList = PaperUtil.getPaperStructure(paper);
            // 校验前端参数试卷和mongo里试卷的结构是否一致
            try {
                validatePaperStructure(hasAnswerQuestionStructureList, paperStructureList, false);
            } catch (Exception e) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "当前页面试卷试题结构与需要更新的试卷试题结构不一致: paperId: " + paperId);
            }
            
            // 从mongo查出来的没有答案的试题结构，设置为参数传过来的答案
            for (int i = 0; i < paperStructureList.size(); i++) {
                Map<String, Object> paperStructure = paperStructureList.get(i);
                Map<String, Object> hasAnswerQuestionStructure = hasAnswerQuestionStructureList.get(i);
                paperStructure.put("answer", hasAnswerQuestionStructure.get("answer"));
            }
            
            // 检查附加答案信息
            PaperUtil.checkAdditionalAnswerAndFixScore(paperStructureList, questionNumber2AdditionalAnswer);
            
            // paper
            updateInfo.put("paperSetBsonList", getPaperSetBsonListForUpdatePaperAnswer(paper, newFullMark, entrustId, questionNumber2HasAnswerStructure, isOperatePaper));
            // answerCardTemplate 只会更新页面请求的试卷的对应关系
            if (isOperatePaper) {
                // 更新answerCardTemplate的分数
                Map<String, Object> update = answerCardTemplateService.getAnswerCardTemplateSetBsonListForUpdatePaperAnswer(paperId, questionNumber2HasAnswerStructure);
                updateInfo.put("answerCardTemplateSetBsonList", update.get("answerCardTemplateSetBsonList"));
                updateInfo.put("questionStructureUpdateList", update.get("questionStructureUpdateList"));
            }
        }
        return paperUpdateInfoList;
    }
    
    /**
     * 获取试卷需要更新的内容
     * @param paper   试卷信息
     * @param questionNumber2HasAnswerStructure questionNumber -> 含有答案的结构信息
     * @param isOperatePaper 是不是页面操作的试卷 是则需要更新分值 如果不是 只更新答案
     * @return 更新的list
     */
    private List<Bson> getPaperSetBsonListForUpdatePaperAnswer(Document paper,
                                                               String newFullMark,
                                                               Long entrustId,
                                                               Map<Integer, Map<String, Object>> questionNumber2HasAnswerStructure,
                                                               boolean isOperatePaper) {
        List<Bson> paperSetBsonList = new ArrayList<>();
        // 设置为有答案
        if (!PaperAnswerStatus.HAS.getStatus().equals(MapUtil.getIntNullable(paper, "answerStatus"))) {
            paperSetBsonList.add(set("answerStatus", PaperAnswerStatus.HAS.getStatus()));
        }
        // 更新答题卡锁定状态
        Integer answerCardLockStatusLock = DictUtil.getDictValue("answerCardLockStatus", "lock");
        if (!answerCardLockStatusLock.equals(MapUtil.getIntNullable(paper, "lockStatus"))) {
            paperSetBsonList.add(set("lockStatus", answerCardLockStatusLock));
        }
        // 委托id
        if (entrustId != null) {
            paperSetBsonList.add(set("entrustId", entrustId));
        }
        // 总分
        if (isOperatePaper && !ObjectUtil.isValueEquals(newFullMark, MapUtil.getTrim(paper, "fullMark"))) {
            paperSetBsonList.add(set("fullMark", newFullMark));
        }
        
        // 更新答案信息
        List<Map<String, Object>> parts = MapUtil.getCast(paper, "parts");
        for (int partIndex = 0, partSize = parts.size(); partIndex < partSize; partIndex++) {
            String partPath = "parts." + partIndex + ".";
            Map<String, Object> part = parts.get(partIndex);
            List<Map<String, Object>> categories = MapUtil.getCast(part, "categories");
            for (int categoryIndex = 0, categorySize = categories.size(); categoryIndex < categorySize; categoryIndex++) {
                String categoryPath = partPath + "categories." + categoryIndex + ".";
                Map<String, Object> category = categories.get(categoryIndex);
                List<Map<String, Object>> questions = MapUtil.getCast(category, "questions");
                for (int questionIndex = 0, questionSize = questions.size(); questionIndex < questionSize; questionIndex++) {
                    String questionPath = categoryPath + "questions." + questionIndex + ".";
                    Map<String, Object> question = questions.get(questionIndex);
                    int questionReadType = MapUtil.getInt(question, "readType", 0);
                    if (questionReadType > 0) {
                        int questionNumber = MapUtil.getInt(question, "questionNumber");
                        Map<String, Object> hasAnswerStructure = questionNumber2HasAnswerStructure.get(questionNumber);
                        handlerPaperSetBsonList(paperSetBsonList, isOperatePaper, questionPath, questionNumber, questionReadType, question, hasAnswerStructure);
                    } else {
                        List<Map<String, Object>> structures = MapUtil.getCast(question, "structures");
                        for (int structureIndex = 0, structureSize = structures.size(); structureIndex < structureSize; structureIndex++) {
                            String structurePath = questionPath + "structures." + structureIndex + ".";
                            Map<String, Object> structure = structures.get(structureIndex);
                            int structureReadType = MapUtil.getInt(structure, "readType", 0);
                            if (structureReadType > 0) {
                                int questionNumber = MapUtil.getInt(structure, "questionNumber");
                                Map<String, Object> hasAnswerStructure = questionNumber2HasAnswerStructure.get(questionNumber);
                                handlerPaperSetBsonList(paperSetBsonList, isOperatePaper, structurePath, questionNumber, structureReadType, structure, hasAnswerStructure);
                            }
                        }
                    }
                }
            }
        }
        return paperSetBsonList;
    }
    
    /**
     * 处理需要更新的信息
     * @param paperSetBsonList   更新操作list 发现需要更新的会往里面添加
     * @param isOperatePaper     是否页面操作的试卷
     * @param path               操作当前路径 "parts.0.categories.1.questions.3." or "parts.0.categories.1.questions.3.structures.1."
     * @param questionNumber     试题编号
     * @param readType           阅卷类型  2主观题 1客观题
     * @param structure          试题结构信息
     * @param hasAnswerStructure 前端上传上来的带有答案的结构信息
     */
    private void handlerPaperSetBsonList(List<Bson> paperSetBsonList,
                                         boolean isOperatePaper,
                                         String path, int questionNumber, int readType,
                                         Map<String, Object> structure,
                                         Map<String, Object> hasAnswerStructure) {
        if (MapUtils.isEmpty(hasAnswerStructure)) {
            String structureNumber = MapUtil.getTrim(structure, "structureNumber");
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "未提供" + structureNumber + "(" + questionNumber + ")的试题结构信息");
        }
        // 客观题
        if (readType == DictUtil.getDictValue("readType", "objective")) {
            // 答案
            if (!ObjectUtil.isValueEquals(structure.get("answer"), hasAnswerStructure.get("answer"))) {
                paperSetBsonList.add(set(path + "answer", hasAnswerStructure.get("answer")));
            }
        }
        // 分数 当前操作的试卷会更新分数 其他不会
        if (isOperatePaper && !ObjectUtil.isValueEquals(structure.get("scoreValue"), hasAnswerStructure.get("scoreValue"))) {
            paperSetBsonList.add(set(path + "scoreValue", hasAnswerStructure.get("scoreValue")));
        }
    }
    
    /**
     * 获取试卷文档
     *
     * @param params
     * @return
     */
    private Document getPaperDocument(Map<String, Object> params, boolean isUpdate) {
        Map<String, Object> paper = PaperUtil.getPaper(params);
        Date nowDate = new Date();

        // 老师默认私有题库
        if (DictUtil.getDictValue("userType", "teacher") == Integer.parseInt(params.get("userType").toString())) {
            if (params.get("ascriptionType") == null || StringUtils.isBlank(params.get("ascriptionType").toString())) {
                paper.put("ascriptionType", DictUtil.getDictValue("ascriptionType", "personal"));
            }
        }

        // 备课组长
        if (DictUtil.getDictValue("userType", "prepareLeader") == Integer.parseInt(params.get("userType").toString())) {
            if (params.get("ascriptionType") == null || StringUtils.isBlank(params.get("ascriptionType").toString())) {
                paper.put("ascriptionType", DictUtil.getDictValue("ascriptionType", "personal"));
            }
        }

        // 超级管理员默认公有题库
        if (DictUtil.getDictValue("userType", "admin") == Integer.parseInt(params.get("userType").toString())
                || DictUtil.getDictValue("userType", "subjectAdmin") == Integer.parseInt(params.get("userType").toString())) {
            if (params.get("ascriptionType") == null || StringUtils.isBlank(params.get("ascriptionType").toString())) {
                paper.put("ascriptionType", DictUtil.getDictValue("ascriptionType", "all"));
            }
        }

        paper.putIfAbsent("ascriptionType", DictUtil.getDictValue("ascriptionType", "personal"));

        if (!isUpdate) {
            // 组卷默认无答题卡有试题有答案
            if (DictUtil.getDictValue("paperCreationType", "normal") == Integer.parseInt(paper.get("creationType").toString())) {
                // 正常组卷
                paper.put("answerCardStatus", PaperAnswerCardStatus.HAS_NOT.getStatus());
                paper.put("questionStatus", PaperQuestionStatus.HAS.getStatus());
                paper.put("answerStatus", PaperAnswerStatus.HAS.getStatus());

                // 需要将一起试题转换为校本试题
                transformQuestion(params, paper);

            } else if (DictUtil.getDictValue("paperCreationType", "selfEdit") == Integer.parseInt(paper.get("creationType").toString())) {
                // 自主编辑
                paper.put("answerCardStatus", PaperAnswerCardStatus.HAS_NOT.getStatus());
                paper.put("questionStatus", PaperQuestionStatus.HAS_NOT.getStatus());
                paper.put("answerStatus", PaperAnswerStatus.HAS_NOT.getStatus());
            } else {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "creationType不正确，只能是0和1");
            }

            paper.put("paperStatus", DictUtil.getDictValue("paperStatus", "neverUsed"));
            paper.put("creatorId", params.get("userId"));
            paper.put("creatorName", params.get("userName"));
            paper.put("createDateTime", nowDate);

            Long paperId = IdUtil.getId();
            paper.put("paperId", paperId);

        }

        // todo 如果前端没有传版本号，则答题卡的版本号默认为1。这里需要把前端所有保存试卷接口都带上答题卡版本编码，然后去掉这行代码
        if (ObjectUtil.isBlank(paper.get("cardVersionCode"))) {
            paper.put("cardVersionCode", 1);
        }


        if (isUpdate) {
            paper.putIfAbsent("creatorId", params.get("userId"));
            paper.putIfAbsent("creatorName", params.get("userName"));
            Object createDateTimeObj = paper.get("createDateTime");
            Date createDateTime = createDateTimeObj == null ? nowDate : TikuConvertUtil.convertDate(createDateTimeObj);
            paper.put("createDateTime", createDateTime);
        }

        paper.put("modifierId", params.get("userId"));
        paper.put("modifierName", params.get("userName"));
        paper.put("modifyDateTime", new Date());

        return new Document(paper);
    }

    /**
     * 转换试题
     *
     * @param params
     * @param paper
     */
    private void transformQuestion(Map<String, Object> params, Map<String, Object> paper) {
//        Integer belongYiqi = DictUtil.getDictValue("questionBankBelongType", "yiqi");
        Integer belongDongni = DictUtil.getDictValue("questionBankBelongType", "dongni");
//        Integer belongJyeoo = DictUtil.getDictValue("questionBankBelongType", "jyeoo");

        Integer usedStatus = DictUtil.getDictValue("questionStatus", "used");

//        params.put("ascriptionType", paper.get("ascriptionType"));
//        params.put("schoolIds", paper.get("schoolIds") == null ? Collections.emptyList() : paper.get("schoolIds"));
//        params.put("source", paper.get("paperName"));
//        params.put("questionStatus", DictUtil.getDictValue("questionStatus", "used"));
//        params.put("currentTime", new Date());

        PaperUtil.forEachQuestion(paper, item -> {
            Map<String, Object> question = (Map<String, Object>) item.get("question");

            item.put("ascriptionType", paper.get("ascriptionType"));
            item.put("questionStatus", usedStatus);
            item.put("source", paper.get("paperName"));
            item.put("courseId", item.get("courseId"));
            item.put("courseName", item.get("courseName"));
            item.put("gradeType", paper.get("gradeType"));
            item.put("areaId", paper.get("areaId"));
            item.put("schoolIds", paper.get("schoolIds") == null ? Collections.emptyList() : paper.get("schoolIds"));
            item.put("belongType", belongDongni);
            item.put("question", question);
        });
    }

    /**
     * 获取试卷列表
     *
     * @param params pageNo pageSize userType schoolId [courseId] [gradeType] [search] [ascriptionType]
     *               [paperType] [areaCode] [year] [paperStatus] [creatorId]
     * @return
     */
    public Map<String, Object> getPaper(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("userType")
                .isNumeric("pageNo")
                .isNumeric("pageSize")
                .verify();
        long userId = MapUtil.getLong(params, "userId");
        Bson queryCreationTypeNormal = getQueryCreationTypeNormal();
        Bson queryNotExamPaper = getQueryNotExamPaper();
        Bson queryNotPaperTemplate = getQueryNotPaperTemplate();
        Bson query = getPaperQueryBson(params, queryCreationTypeNormal, queryNotExamPaper, queryNotPaperTemplate);
        long totalCount = paperManager.count(query);
        if (totalCount == 0) {
            return MapUtil.of("totalCount", 0, "list", new ArrayList<>());
        }

        List<Document> list = paperManager.getSimpleList(query,
                desc("createDateTime"),
                MapUtil.getInt(params, "currentIndex"), MapUtil.getInt(params, "pageSize")
        );
        
        // 设置收藏标志 用于前端展示
        list.forEach(item -> {
            List<Long> userIds = MapUtil.getCast(item, "userIds");
            boolean collect = userIds != null && userIds.contains(userId);
            item.put("collect", collect);
        });
        // 添加学校信息
        addSchoolInfo(list);
        return MapUtil.of("totalCount", totalCount, "list", list);
    }

    /**
     * 添加学校信息
     *
     * @param paperList
     */
    private void addSchoolInfo(List<Document> paperList) {
        if (CollectionUtils.isEmpty(paperList)) { return; }

        // 查询学校信息
        Set<Long> allSchoolIdSet = new HashSet<>();
        for (Map<String, Object> paper : paperList) {
            paper.put("schoolList", Collections.emptyList()); // 先占位
            List<Long> schoolIdList = MapUtil.getListLong(paper, "schoolIds")
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(toList());
            if (CollectionUtils.isNotEmpty(schoolIdList)) {
                allSchoolIdSet.addAll(schoolIdList);
            }
        }

        if (CollectionUtils.isEmpty(allSchoolIdSet)) { return; }
        
        List<Map<String, Object>> allSchoolList = commonSchoolService.getSchoolList(MapUtil.of("schoolIds", allSchoolIdSet));
        Map<Long, Map<String, Object>> schoolId2Info = allSchoolList.stream()
                .collect(toMap(item -> Long.valueOf(item.get("schoolId").toString()), item -> item));
        // 设置学校信息
        for (Map<String, Object> paper : paperList) {
            List<Map<String, Object>> schoolList = MapUtil.getListLong(paper, "schoolIds").stream()
                    .filter(Objects::nonNull)
                    .map(schoolId2Info::get)
                    .filter(Objects::nonNull)
                    .collect(toList());
            paper.put("schoolList", schoolList);
        }
    }

    /**
     * 获取区域试卷
     *
     * @param params pageNo pageSize areaId [courseId] [gradeType] [search] [year] [paperType] stage
     * @return
     */
    public Map<String, Object> getUnionPaper(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("pageNo")
                .isNumeric("pageSize")
                .isNumeric("stage")
                .verify();

        List<Long> queryCourseIdList;
        // 当前学段的公共课程
        List<Map<String, Object>> commonCourseList = commonCourseService.getCourseByStage(params);
        Set<Long> commonCourseIds = commonCourseList.stream()
                .map(item -> Long.valueOf(item.get("courseId").toString()))
                .collect(Collectors.toSet());

        // 有传courseId则使用传参的courseId查询
        if (!ObjectUtil.isBlank(params.get("courseId"))) {
            List<Long> paramsCourseIds = StringUtil.strToList(MapUtil.getString(params, "courseId"),
                    StrUtil.COMMA, Long.class);
            queryCourseIdList = paramsCourseIds.stream()
                    .filter(commonCourseIds::contains)
                    .collect(toList());
        }
        // 没传courseId查询该学段的全部公共课程
        else {
            queryCourseIdList = new ArrayList<>(commonCourseIds);
        }

        // TODO 知识点切换
        boolean xkwVersion20240614 = MapUtil.getBoolean(params, "xkwVersion20240614");
        if (!xkwVersion20240614) {
            // 如果不是最新体系的，只能查询大家公用的知识点体系
            Set<Long> willUpdateXkwVersion20240614CourseIdSet = KnowledgeUtil.willUpdateXkwVersion20240614CourseIdSet();
            queryCourseIdList = queryCourseIdList.stream()
                    .filter(courseId -> !willUpdateXkwVersion20240614CourseIdSet.contains(courseId))
                    .collect(toList());
        }
        if (CollectionUtils.isEmpty(queryCourseIdList)) {
            return MapUtil.of("totalCount", 0, "list", new ArrayList<>());
        }

        Bson query = new Document();

        //判断用户输入是否是可以转换为数字的字符串
        if (!ObjectUtil.isBlank(params.get("search"))) {
            if (Pattern.compile("[0-9]+").matcher(params.getOrDefault("search", "").toString()).matches()) {
                query = or(eq("paperId", Long.parseLong(params.get("search").toString())));
            } else {
                query = or(regex("paperName", params.getOrDefault("search", "").toString()));
            }
        }

        query = and(query, in("courseId", queryCourseIdList));

        if (!ObjectUtil.isBlank(params.get("gradeType"))) {
            query = and(query, eq("gradeType", Integer.valueOf(params.get("gradeType").toString())));
        }

        if (!ObjectUtil.isBlank(params.get("creationType"))) {
            query = and(query, eq("creationType", Integer.valueOf(params.get("creationType").toString())));
        }
        if (!ObjectUtil.isBlank(params.get("questionStatus"))) {
            query = and(query, eq("questionStatus", Integer.valueOf(params.get("questionStatus").toString())));
        }

        if (!ObjectUtil.isBlank(params.get("paperType"))) {
            String[] split = params.get("paperType").toString().split(",");
            List<Integer> types = new ArrayList<>();
            for (String s : split) {
                types.add(Integer.valueOf(s));
            }
            query = and(query, in("paperType", types));
        }

        if (!ObjectUtil.isBlank(params.get("year"))) {
            List<Integer> year = Arrays.stream(params.get("year").toString().split(",")).map(Integer::new).collect(Collectors.toList());
            query = and(query, in("year", year));
        }

//        query = and(query,
//                eq("unionPaperType", DictUtil.getDictValue("unionPaperType", "areaPaper")));

        // 获取所有父亲区域
        if (ObjectUtil.isValidId(params.get("areaId"))) {
            List<Map<String, Object>> areaList = commonAreaService.getParentArea(params);
            List<Long> areaIds = areaList.stream()
                    .map(item -> Long.valueOf(item.get("areaId").toString()))
                    .collect(Collectors.toList());
            query = and(query, in("areaIds", areaIds));
        }

        query = and(query, getQueryNotExamPaper(), getQueryNotPaperTemplate());
        
        // 查询数量
        Long totalCount = paperManager.count(query);

        // 查询列表
        List<Document> list = paperManager.getSimpleList(query,
                desc("year"),
                MapUtil.getInt(params, "currentIndex"), MapUtil.getInt(params, "pageSize")
        );

        list.forEach(item -> item.put("_id", item.get("_id").toString()));

        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("list", list);

        return result;
    }

    /**
     * 获取查询条件
     *
     * @param params
     * @return
     */
    private Bson getQueryParams(Map<String, Object> params) {

        Long paperId = 0L;
        try {
            paperId = Long.valueOf(params.getOrDefault("search", "0").toString());
        } catch (Exception e) {
            paperId = 0L;
        }

        Bson query = or(regex("paperName", params.getOrDefault("search", "").toString()),
                eq("paperId", paperId));
        if (!ObjectUtil.isBlank(params.get("courseId"))) {
            query = and(query, eq("courseId", Long.valueOf(params.get("courseId").toString())));
        }

        if (!ObjectUtil.isBlank(params.get("schoolId"))) {
            List<Integer> stages = commonSchoolService.getSchoolStage(params);
            params.put("stages", stages);
        }

        int userType = Integer.parseInt(params.get("userType").toString());
        int examiner = DictUtil.getDictValue("userType", "examiner");
        int subjectAdmin = DictUtil.getDictValue("userType", "subjectAdmin");

        if (ObjectUtil.isValueEquals(params.get("userType"), examiner)) {
            // 联考员只能查询自己创建的区域考和联考试卷
            query = and(query, eq("creatorId", Long.valueOf(params.get("userId").toString())),
                    in("unionPaperType", DictUtil.getDictValue("unionPaperType", "areaPaper"),
                            DictUtil.getDictValue("unionPaperType", "unionPaper")));

        } else if (!ObjectUtil.isValueEquals(params.get("userType"), subjectAdmin)) {
            // 非联考员，非题库管理员

            if (ObjectUtil.isBlank(params.get("ascriptionType"))
                    || ObjectUtil.isValueEquals(params.get("ascriptionType"), DictUtil.getDictValue("ascriptionType", "all"))) {
                // 查看所有的试卷
                Bson ascriptionTypeBson = eq("ascriptionType", DictUtil.getDictValue("ascriptionType", "all"));
                if (!ObjectUtil.isBlank(params.get("schoolId"))) {
                    Bson schoolIdBson = eq("schoolIds", Long.valueOf(params.get("schoolId").toString()));
                    query = and(query, or(ascriptionTypeBson, schoolIdBson));
                } else {
                    query = and(query, or(ascriptionTypeBson));
                }

            } else if (ObjectUtil.isValueEquals(params.get("ascriptionType"), DictUtil.getDictValue("ascriptionType", "personal"))
                    && userType != examiner) {
                // 查询个人试卷
                query = and(query, eq("ascriptionType", DictUtil.getDictValue("ascriptionType", "personal")),
                        eq("schoolIds", Long.valueOf(params.get("schoolId").toString())));
            }
        }

        if (!ObjectUtil.isBlank(params.get("creatorId"))) {
            query = and(query, eq("creatorId", Long.valueOf(params.get("creatorId").toString())));
        }

        if (!ObjectUtil.isBlank(params.get("gradeType"))) {
            query = and(query, eq("gradeType", Integer.valueOf(params.get("gradeType").toString())));
        } else if (!ObjectUtil.isBlank(params.get("stages"))) {
            if (ObjectUtil.isBlank(params.get("schoolId"))) {
                List<Integer> stages = (List<Integer>) params.get("stages");
                List<Map<String, Object>> schoolStageInfo = commonSchoolService.getSchoolStageInfo(params);
                //获取该学校的学制  1为6-3-3,2为5-4-3
                int educationSystem = getSchoolEducationSystem(schoolStageInfo);
                List<Integer> gradeTypes = new ArrayList<>();
                if (CollectionUtils.isEmpty(stages)) {
                    // 没有学校，默认12个年级
                    gradeTypes.addAll(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12));
                } else {
                    if (stages.contains(DictUtil.getDictValue("stage", "primary")) && educationSystem == 1) {
                        // 小学
                        gradeTypes.addAll(Arrays.asList(1, 2, 3, 4, 5, 6));
                    } else if (stages.contains(DictUtil.getDictValue("stage", "primary")) && educationSystem == 2) {
                        // 小学(5-4-3学制)
                        gradeTypes.addAll(Arrays.asList(1, 2, 3, 4, 5));
                    }
                    if (stages.contains(DictUtil.getDictValue("stage", "middle")) && educationSystem == 1) {
                        // 初中
                        gradeTypes.addAll(Arrays.asList(7, 8, 9));
                    } else if (stages.contains(DictUtil.getDictValue("stage", "middle")) && educationSystem == 2) {
                        // 初中(5-4-3学制)
                        gradeTypes.addAll(Arrays.asList(6, 7, 8, 9));
                    }
                    if (stages.contains(DictUtil.getDictValue("stage", "high"))) {
                        // 高中
                        gradeTypes.addAll(Arrays.asList(10, 11, 12));
                    }
                }
                query = and(query, in("gradeType", gradeTypes));

            } else {
                List<Integer> gradeTypes = gradeService.getGradeTypeBySchoolIdAndStage(params);
                query = and(query, in("gradeType", gradeTypes));
            }

        }

        if (!ObjectUtil.isBlank(params.get("unionPaperType"))) {
            query = and(query, in("unionPaperType", StringUtil.strToList(params.get("unionPaperType").toString(), ",", Integer.class)));
        }

//        if (!ObjectUtil.isBlank(params.get("paperType"))) {
//            query = and(query, eq("paperType", Integer.valueOf(params.get("paperType").toString())));
//        }
        if (!ObjectUtil.isBlank(params.get("paperStatus"))) {
            query = and(query, eq("paperStatus", Integer.valueOf(params.get("paperStatus").toString())));
        }
        if (!ObjectUtil.isBlank(params.get("year"))) {
            query = and(query, eq("year", Integer.valueOf(params.get("year").toString())));
        }

        if (ObjectUtil.isValidId(params.get("areaId"))) {
            // 查询区域试卷
            List<Map<String, Object>> areaList = commonAreaService.getAreaChild(params);
            List<Long> areaIds = areaList.stream().map(item -> Long.valueOf(item.get("areaId").toString())).collect(Collectors.toList());
            query = and(query, in("areaId", areaIds));
        }

        if (!ObjectUtil.isBlank(params.get("answerCardStatus"))) {
            query = and(query, eq("answerCardStatus", Integer.valueOf(params.get("answerCardStatus").toString())));
        }

        if (!ObjectUtil.isBlank(params.get("isCollect")) && params.get("isCollect").equals("true")) {
            Long userId = Long.valueOf(params.getOrDefault("userId", 0).toString());
            query = and(query, eq("userIds", userId));
        }

        // 答题卡版本号编码，用于过滤掉不兼容的答题卡，避免被引用
        if (!ObjectUtil.isBlank(params.get("cardVersionCode"))) {
            int versionCode = Integer.parseInt(params.getOrDefault("cardVersionCode", 0).toString());
            query = and(query, gte("cardVersionCode", versionCode));
        }
        //试卷类型
        if (!ObjectUtil.isBlank(params.get("paperType"))) {
            String[] split = params.get("paperType").toString().split(",");
            List<Integer> types = new ArrayList<>();
            for (String s : split) {
                types.add(Integer.valueOf(s));
            }
            query = and(query, in("paperType", types));
        }

        return query;
    }

    private int getSchoolEducationSystem(List<Map<String, Object>> schoolStageInfo) {
        Map<Object, Map<String, Object>> stages = schoolStageInfo.stream().collect(toMap(stage -> stage.get("stage"), t -> t));
        if (stages.containsKey(1) && 6 == Integer.valueOf(stages.get(1).get("gradeNumber").toString()) ||
                stages.containsKey(2) && 3 == Integer.valueOf(stages.get(2).get("gradeNumber").toString())) {
            return 1;
        } else if (stages.containsKey(1) && 5 == Integer.valueOf(stages.get(1).get("gradeNumber").toString()) ||
                stages.containsKey(2) && 4 == Integer.valueOf(stages.get(2).get("gradeNumber").toString())) {
            return 2;
        } else {
            return 1;
        }
    }

    /**
     * 获取答题卡列表
     *
     * @param params [schoolId] [search] [ascriptionType] [paperType] [areaId] [year] [answerCard] [isReference]
     * @return
     */
    public Map<String, Object> getAnswerCard(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("userType")
                .isIntegerPositive("pageNo")
                .isIntegerPositive("pageSize")
                .isIntegerNatural("currentIndex")
                .verify();
        long userId = MapUtil.getLong(params, "userId");
        int currentIndex = MapUtil.getInt(params, "currentIndex");
        int pageSize =  MapUtil.getInt(params, "pageSize");
        
        // 正常组卷并且有答题卡 or 自主编辑的
        Bson queryAnswerCard = getQueryAnswerCard();
        // 不是考试用卷
        Bson queryNotExamPaper = getQueryNotExamPaper();
        Bson queryNotPaperTemplate = getQueryNotPaperTemplate();
        // [startDate] -> query or null
        Bson queryGteCreateDateTime = getQueryGteCreateDateTime(params);
    
        Bson query = getPaperQueryBson(params, queryAnswerCard, queryNotExamPaper, queryNotPaperTemplate, queryGteCreateDateTime);
        
        long totalCount = paperManager.count(query);
        if (totalCount == 0 || currentIndex >= totalCount) {
            return MapUtil.of("totalCount", 0, "list", new ArrayList<>());
        }
        
        Order order = getAnswerCardOrder(params);
        // 查询列表
        List<Document> list = paperManager.getSimpleList(query, order, currentIndex, pageSize);
        
        list.forEach(item -> {
            item.putIfAbsent("cardReferenceCount", 0);
            List<Long> userIds = MapUtil.getCast(item, "userIds");
            boolean collect = userIds != null && userIds.contains(userId);
            item.put("collect", collect);
        });

        // 添加学校信息
        addSchoolInfo(list);
        return MapUtil.of("totalCount", totalCount, "list", list);
    }


    /**
     * 获取联考员答题卡列表
     *
     * @param params [search] [ascriptionType] [paperType] [areaId] [year]
     * @return
     */
    public Map<String, Object> getExaminerAnswerCard(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("userType")
                .isIntegerPositive("pageNo")
                .isIntegerPositive("pageSize")
                .isIntegerNatural("currentIndex")
                .verify();
        params.put("creatorId", params.get("userId"));
        
        int currentIndex = MapUtil.getInt(params, "currentIndex");
        int pageSize =  MapUtil.getInt(params, "pageSize");
        Bson queryAnswerCard = getQueryAnswerCard();
        Bson queryNotExamPaper = getQueryNotExamPaper();
        Bson queryNotPaperTemplate = getQueryNotPaperTemplate();
        
        Bson query = getPaperQueryBson(params, queryAnswerCard, queryNotExamPaper, queryNotPaperTemplate);
        
        long totalCount = paperManager.count(query);
        if (totalCount == 0 || currentIndex >= totalCount) {
            return MapUtil.of("totalCount", 0, "list", new ArrayList<>());
        }
        
        Order order = getAnswerCardOrder(params);
        // 查询列表
        List<Document> list = paperManager.getSimpleList(query, order, currentIndex, pageSize);
        list.forEach(item -> item.putIfAbsent("cardReferenceCount", 0));
        
        // 添加学校信息
        addSchoolInfo(list);
        return MapUtil.of("totalCount", totalCount, "list", list);
    }

    /**
     * 获取试卷 直查 不做任何处理 如果没有不会抛异常
     *
     * @param params paperId
     * @return 试卷信息 没有返回null
     */
    public Document getPaperSource(Map<String, Object> params) {
        Verify.of(params).isNotBlank("paperId").verify();
        return getPaperNullable(MapUtil.getLong(params, "paperId"));
    }

    /**
     * 获取试卷 直查 不做任何处理 如果没有会抛异常
     *
     * @param paperId 试卷id
     * @return 试卷信息 没有抛出异常
     */
    public Document getPaper(long paperId) {
        return paperManager.getPaper(paperId);
    }

    /**
     * 获取试卷 直查 不做任何处理 如果没有不会抛异常
     *
     * @param paperId 试卷id
     * @return 试卷信息 没有返回null
     */
    public Document getPaperNullable(long paperId) {
        return paperManager.getPaperNullable(paperId);
    }

    /**
     * 获取试卷详情
     *
     * @param params paperId
     * @return
     */
    public Map<String, Object> getPaperDetail(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();

        Document paper = paperManager.getPaper(MapUtil.getLong(params, "paperId"));
        paper.put("_id", paper.get("_id").toString());

        // 过滤掉无效知识点
        List<Map<String, Object>> questions = PaperUtil.getQuestions(paper);
        ownQuestionService.filterKnowledge(questions);
        ownQuestionService.setQuestionInfo(questions);

        return paper;
    }

    /**
     * 获取试卷详情（无知识点）
     *
     * @param params paperId
     * @return
     */
    public Map<String, Object> getPaperDetailNotKnowledge(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();
        Document paper = paperManager.getPaper(MapUtil.getLong(params, "paperId"));
        paper.put("_id", paper.get("_id").toString());
        return paper;
    }

    /**
     * 获取试卷详情
     * 去除试题的答案/解析/知识点等信息
     *
     * @param params paperId
     * @return
     */
    public Map<String, Object> getPaperDetailWithoutAnswer(Map<String, Object> params) {
        Map<String, Object> paperDetail = getPaperDetail(params);
        PaperUtil.forEachQuestion(paperDetail, item -> {
            Map question = (Map) item.get("question");
            List<Map<String, Object>> questionQuestions = (List<Map<String, Object>>) question.get("questions");
            List<Map<String, Object>> questionStructures = (List<Map<String, Object>>) item.get("structures");
            if (questionQuestions != null) {
                questionQuestions.forEach(o -> {
                    o.remove("answer");
                    o.remove("explain");
                });
            }
            questionStructures.forEach(o -> {
                o.remove("answer");
            });
        });
        return paperDetail;
    }

    /**
     * 获取试卷详情
     *
     * @param params paperId
     * @return
     */
    public Map<String, Object> getPapersDetail(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperIds")
                .verify();
        List<Long> paperIds = Arrays.stream(params.get("paperIds").toString().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        List<Document> paper = paperManager.getPaper(paperIds);
        paper.forEach(o -> o.put("_id", o.get("_id").toString()));

        return MapUtil.of(
                "count", paper.size(),
                "paperList", paper
        );
    }

    /**
     * 获取试卷详情列表
     *
     * @param params paperIds
     * @return
     */
    public List<Map<String, Object>> getPaperDetailList(Map<String, Object> params) {
        Verify.of(params)
                .isNotNull("paperIds")
                .verify();

        List<Long> paperIds = new ArrayList<>();
        for (String id : StringUtil.strToList(params.get("paperIds").toString(), ",", String.class)) {
            paperIds.add(Long.valueOf(id));
        }

        List<Map<String, Object>> paperList = new ArrayList<>();
        paperManager.getPaper(paperIds).forEach(x -> {
            x.put("_id", x.get("_id").toString());
            paperList.add(new HashMap<>(x));
        });

        return paperList;
    }

    /**
     * 获取试卷详情（没题目信息）
     *
     * @param params paperId
     * @return
     */
    public Map<String, Object> getSimplePaperDetail(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();

        Document paper = paperManager.getPaperSimpleNullable(MapUtil.getLong(params, "paperId"));

        if (paper == null) {
            return new HashMap<>();
        }

        paper.put("_id", paper.get("_id").toString());

        return paper;
    }

    /**
     * 删除试卷
     *
     * @param params paperId [password]
     */
    public void deletePaper(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();

        // 验证试卷密码
        validatePaperPassword(params);

        long paperId = MapUtil.getLong(params, "paperId");
        Map<String, Object> paper = paperManager.getPaper(paperId, "试卷不存在，不需要删除");
        // learnmore 2019年8月12日 保存试卷结构
        questionStructureService.saveQuestionStructure(params);

        paperManager.deletePaper(paperId);

        // 更新组卷次数，自减
        Map<String, Object> questionParams = new HashMap<>();
        for (Map<String, Object> question : PaperUtil.getQuestions(paper)) {
            questionParams.put("questionId", question.get("_id").toString());
            ownQuestionService.decrementUsedCount(questionParams);
        }
    }

    /**
     * 软删除试卷
     *
     * @param params paperId [password]
     */
    public void softDeletePaper(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();

        // 验证试卷密码
        validatePaperPassword(params);

        long paperId = MapUtil.getLong(params, "paperId");
        Map<String, Object> paper = paperManager.getPaper(paperId, "试卷不存在，不需要删除");
        // learnmore 2019年8月12日 保存试卷结构
        questionStructureService.saveQuestionStructure(params);

        softDelete(params);

        // 更新组卷次数，自减
        Map<String, Object> questionParams = new HashMap<>();
        for (Map<String, Object> question : PaperUtil.getQuestions(paper)) {
            questionParams.put("questionId", question.get("_id").toString());
            ownQuestionService.decrementUsedCount(questionParams);
        }
    }

    /**
     * 更新试卷状态及试题的状态为已引用(即已发布考试)，并且同时将试卷的lockStatus改成已锁定
     * @param paperId 试卷id
     */
    public void updatePaperAndQuestionStatusToUsed(Long paperId) {
        updatePaperAndQuestionStatusToUsed(MapUtil.of("paperIds", Collections.singletonList(paperId)));
    }

    /**
     * 更新试卷状态及试题的状态为已引用(即已发布考试)，并且同时将试卷的lockStatus改成已锁定
     * 校内考使用
     * @param params paperIds
     */
    public void updatePaperAndQuestionStatusToUsed(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperIds")
                .verify();

        List<Long> paperIds = new ArrayList<>();
        for (Object paperId : (List) params.get("paperIds")) {
            paperIds.add(Long.valueOf(paperId.toString()));
        }
        // 当前试卷如果存在父试卷，需要将父试卷也设置为已使用
        List<Document> simplePaperList = paperManager.getSimpleList(in("paperId", paperIds));
        List<Long> parentPaperIdList = simplePaperList.stream()
                .map(i -> MapUtil.getLongNullable(i, "sourcePaperId"))
                .filter(Objects::nonNull)
                .distinct()
                .collect(toList());

        Integer paperStatus = DictUtil.getDictValue("paperStatus", "used");
        Bson setBson = combine(set("paperStatus", paperStatus), set("lockStatus", DictUtil.getDictValue("answerCardLockStatus", "lock")));
        parentPaperIdList.addAll(paperIds);
        paperManager.updatePapers(parentPaperIdList, setBson);

            // 设置试题被使用
            List<Document> paperList = paperManager.getPaper(paperIds);
            List<ObjectId> questionIds = new ArrayList<>();
            paperList.forEach(paper -> {
                PaperUtil.forEachQuestion(paper, item -> {
                    Map question = (Map) item.get("question");
                    String questionId = null;
                    if (question != null) {
                        questionId = (String) question.get("_id");
                    }
                    if (StringUtils.isNotBlank(questionId)) {
                        questionIds.add(new ObjectId(questionId));
                    }
                });
            });
            ownQuestionService.updateQuestionUsed(questionIds);

    }

    /**
     * 更新试卷状态及试题的状态为已引用(即已发布考试)，并且同时将试卷的lockStatus改成已锁定
     * 联考使用
     * @param params paperId areaId
     */
    public void updatePaperAndQuestionStatusToUsedByUnion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isValidId("areaId")
                .verify();

        // 查询试卷详情
        long paperId = MapUtil.getLong(params, "paperId");
        Document paper = paperManager.getPaper(paperId);

        // 获取试题ID列表
        List<Map<String, Object>> questions = PaperUtil.getQuestions(paper);
        List<ObjectId> questionIds = questions.stream()
                .filter(item -> item.get("_id") != null)
                .map(item -> new ObjectId(item.get("_id").toString()))
                .collect(Collectors.toList());

        // 设置试题areaId
        Bson questionBson = set("areaId", Long.valueOf(params.get("areaId").toString()));

        Integer paperStatus = DictUtil.getDictValue("paperStatus", "used");
        Bson paperBson = combine(set("areaId", MapUtils.getLong(params, "areaId")),
          set("paperStatus", paperStatus), set("lockStatus", DictUtil.getDictValue("answerCardLockStatus", "lock")));

        // 更新试题
        questionManager.update(questionIds, questionBson);

        // 更新试卷
        List<Long> updatePaperIdList = new ArrayList<>(2);
        updatePaperIdList.add(paperId);

        Long sourcePaperId = MapUtil.getLongNullable(paper, "sourcePaperId");
        if (sourcePaperId != null) {
            // 如果存在父试卷，也需要更新
            updatePaperIdList.add(sourcePaperId);
        }

        paperManager.updatePapers(updatePaperIdList, paperBson);

    }

    /**
     * 下载试卷  不包含答案解析等
     *
     * @param params {@link #downPaper(Map)}
     * @return
     */
    public String downPaperWithoutAnswer(Map<String, Object> params) {
        params.put("reviewType", DictUtil.getDictValue("reviewType", "question"));
        return downPaper(params);
    }

    /**
     * 修改试卷的答案
     *
     * @param params - paperId
     *               - question
     *                 - questionNumber
     *                 - answer    新的答案
     *                 - unitType  新的unitType
     */
    public void updateAnswer(Map<String, Object> params) {
        PaperUtil.updateAnswer(paperManager, params);
    }

    /**
     * <pre>
     * 修改试卷课程 - 包含父子全部试卷
     * 零、校验
     * 全部父子答题卡只发布了一场考试
     *
     * 每张试卷的内容包括：
     * 一、mongo试卷、模板
     *     1. 修改为基础课程
     *       （1）paper.courseId/courseName
     *       （2）paper.xxx.question.courseId/courseName
     *       （3）paper.xxx.question.structure.courseId/courseName
     *       （4）tqs.courseId/courseName
     *       （5）answerCardTemplate.courseId/courseName
     *       （6）answerCardTemplate.state....courseId/courseName
     *     2. 修改为综合课程
     *       （1）paper.courseId/courseName
     *       （2）answerCardTemplate.courseId/courseName
     * 二、考试相关改动
     *     ......
     * 三、委托
     *     修改t_entrust.courseId/courseName
     * </pre>
     *
     * @param params - paperId
     *               - courseId
     */
    @Transactional(ExamRepository.TRANSACTION)
    @Override
    public void updatePaperCourse(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isValidId("courseId")
                .verify();
        final long paramsPaperId = MapUtil.getLong(params, "paperId");

        // 校验没有发布多场考试
        List<Document> paperList = ownUpdateAnswerService.getPaperListForUpdateQuestionAnswer(paramsPaperId);
        List<Long> allPaperIdList = paperList.stream().map(i -> MapUtil.getLong(i, "paperId")).collect(toList());
        List<Map<String, Object>> relationExamList = examPaperService.getRelationExamByPaperIdList(allPaperIdList);
        if (CollectionUtils.size(relationExamList) > 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "当前答题卡已用于多场考试，暂不支持修改课程");
        }

        Document oldPaper = paperManager.getPaperSimple(paramsPaperId);
        Long sourcePaperId = MapUtil.getLongNullable(oldPaper, "sourcePaperId");
        long lockRootPaperId = Optional.ofNullable(sourcePaperId).orElse(paramsPaperId);
        JedisTemplate.lockExecute("EXAM:PAPER:COURSE:UPDATE:PAPER_ID:" + lockRootPaperId,
                0, "有其他人在正在修改试卷或试题课程,请稍后刷新页面再试",
                () -> {
                    // mongo没有事务,目前很难做到更新某张试卷报错后,全部数据回滚(排除报错后,重新执行本方法)
                    for (Document paper : paperList) {
                        Map<String, Object> tmpParams = new HashMap<>(params);
                        tmpParams.put("paperId", paper.getLong("paperId"));

                        // 试卷相关修改
                        super.updatePaperCourse(tmpParams);
                        // 考试相关修改
                        examPaperService.updateExamPaperCourse(tmpParams, oldPaper);
                        // 委托相关修改
                        entrustService.updateEntrustCourse(tmpParams);
                        
                        // 试题渲染任务
                        tikuRenderQuestionByPaperTaskService.registerSynchronizationToCreateRenderQuestionByPaperTask(lockRootPaperId, "修改试卷课程");
                    }
                    return true;
                }
        );
    }

    /**
     * 更新试题课程
     * 更改试卷中所有的见 {@link #updatePaperCourse(Map)}
     *
     * @param params paperId questionNumber courseId
     */
    @Transactional(value = TikuRepository.TRANSACTION, propagation = Propagation.REQUIRED)
    @Override
    public void updateQuestionCourse(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotBlank("questionNumber")
                .isValidId("courseId")
                .verify();

        super.updateQuestionCourse(params);

        // 查询当前试卷关联的考试
        List<Map<String, Object>> examList = examPaperService.getRelationExamByPaperId(params);
        if (CollectionUtils.isNotEmpty(examList)) {
            Map<String, Object> userInfo = MapUtil.of("userId", params.get("userId"), "userName", params.get("userName"));
            for (Map<String, Object> exam : examList) {
                params.putAll(exam);

                // 更新t_paper_read
                examHandleService.updatePaperReadCourse(params);

                //更新考试相关及重置考试配置
                Map<String, Object> map = new HashMap<>(userInfo);
                map.put("examId", params.get("examId"));
                map.put("paperId", params.get("paperId"));
                examCourseService.updateExamCourse(map);
            }
        }
    }

    /**
     * 更新试题课程
     * 更改试卷中所有的见 {@link #updatePaperCourse(Map)}
     *
     * @param params paperId questionNumber courseId
     */
    @Transactional(value = TikuRepository.TRANSACTION, propagation = Propagation.REQUIRED)
    public void updatePaperQuestionCourseAndResetReport(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isNotBlank("questionNumber")
                .isValidId("courseId")
                .verify();

        super.updateQuestionCourse(params);

        // 查询当前试卷关联的考试
        List<Map<String, Object>> examList = examPaperService.getRelationExamByPaperId(params);
        if (CollectionUtils.isNotEmpty(examList)) {
            for (Map<String, Object> exam : examList) {
                //重置原始报告配置
                Long examId = MapUtil.getLong(exam, "examId");
                Map<String, Object> copy = MapUtil.copy(params, "userId", "userName");
                copy.put("examId", examId);
                examConfigService.resetExamConfigInfo(copy);
            }
        }
    }

    /**
     * 校验试卷知识点是否校正完成
     *
     * @param params paperId
     * @return
     */
    public boolean checkPaperKnowledge(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();

        List<Map<String, Object>> entrustList = entrustQueryService.getEntrustByPaperId(params);
        if (CollectionUtils.isEmpty(entrustList)) {
            // 没有委托
            Map<String, Object> paperDetail = getPaperDetail(params);
            List<Map<String, Object>> questions = PaperUtil.getQuestions(paperDetail);
            if (CollectionUtils.isEmpty(questions)) {
                return false;
            }

            boolean hasKnowledge = false;
            for (Map<String, Object> item : questions) {
                List<String> knowledgeList = QuestionUtil.getKnowledgeIdList(item);
                if (CollectionUtils.isNotEmpty(knowledgeList)) {
                    hasKnowledge = true;
                    break;
                }
            }

            return hasKnowledge;

        } else {
            // 存在委托
            boolean isFinish = false;
            if (CollectionUtils.isNotEmpty(entrustList)) {
                int entrustFinish = DictUtil.getDictValue("entrustStatus", "complete");
                for (Map<String, Object> entrust : entrustList) {
                    if (ObjectUtil.isValueEquals(entrust.get("entrustStatus"), entrustFinish)) {
                        isFinish = true;
                    }
                }
            }
            return isFinish;

        }
    }

    /**
     * 获取解答题拆分试卷
     *
     * @param params
     * @return
     */
    public Map<String, Object> getRecognitionPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("recognitionId")
                .verify();

        return mongoDatabase.getCollection("recognitionPaper")
                .find(eq("recognitionId", Long.valueOf(params.get("recognitionId").toString())))
                .first();
    }

    /**
     * 替换试卷和模版
     *
     * @param params
     */
    public void replacePaperAndTemplate(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("paperId")
                .isValidId("recognitionId")
                .verify();

        // 原始试卷和答题卡模版
        long paperId = MapUtil.getLong(params, "paperId");
//        Document paper = paperManager.getPaper(paperId);

        Document answerCardTemplate = answerCardTemplateService.getAnswerCardTemplateNullable(params);

        List<Document> answerCardTemplateManualList = answerCardTemplateManualService.getList(params);

        // 拆分的试卷和答题卡模版
//        Document recognitionPaper = mongoDatabase.getCollection("recognitionPaper")
//                .find(eq("recognitionId", Long.valueOf(params.get("recognitionId").toString())))
//                .first();

        Document recognitionTemplate = null;
        List<Document> recognitionTemplateManualList = mongoDatabase.getCollection("recognitionTemplateManual")
                .find(eq("recognitionId", Long.valueOf(params.get("recognitionId").toString())))
                .into(new ArrayList<>());
        Map<String, Document> recognitionTemplateManualMap = recognitionTemplateManualList.stream()
                .collect(toMap(item -> item.get("templateCode").toString(), item -> item));

//        List<Map<String, Object>> questionList = PaperUtil.getPaperStructure(paper);
//        List<Map<String, Object>> recognitionPaperQuestionList = PaperUtil.getPaperStructure(recognitionPaper);
//        paper.put("parts", recognitionPaper.get("parts"));
//        // 更新questionCount
//        paper.put("questionCount", recognitionPaperQuestionList.size());
//        PaperUtil.updatePaperAnswer(paper, questionList);


        if(answerCardTemplate!=null && answerCardTemplate.size() > 0) {
            recognitionTemplate = mongoDatabase.getCollection("recognitionTemplate")
                    .find(eq("recognitionId", Long.valueOf(params.get("recognitionId").toString())))
                    .first();
            if(recognitionTemplate != null) {
                answerCardTemplate.put("setting", recognitionTemplate.get("setting"));
                answerCardTemplate.put("state", recognitionTemplate.get("state"));
                if (isRecognitionTemplateEqualNewQuestionNumbers(recognitionTemplate.get("template", List.class), params)) {
                    mergeRecognitionTemplate2Template(recognitionTemplate.get("template", List.class),
                            answerCardTemplate.get("template", List.class), params);
                } else {
                    answerCardTemplate.put("template", recognitionTemplate.get("template"));
                }
            }
        }

        List<Document> newAnswerCardTemplateManualList = new ArrayList<>();
        for (Map<String, Object> answerCardTemplateManual : answerCardTemplateManualList) {
            Document recognitionTemplateManual = recognitionTemplateManualMap.get(answerCardTemplateManual.get("templateCode").toString());
            if (recognitionTemplateManual == null) {
                continue;
            }

            Document templateManual = (Document) answerCardTemplateManual;
            mergeRecognitionTemplate2Template(
                    recognitionTemplateManual.get("answerCardTemplate", Document.class).get("template", List.class),
                    templateManual.get("answerCardTemplate", Document.class).get("template", List.class), params);
            Document item = new Document(answerCardTemplateManual);
            //item.put("answerCardTemplate", recognitionTemplateManual.get("answerCardTemplate"));
            newAnswerCardTemplateManualList.add(item);
        }

        //试题拆分/合并
        Map<Integer, List<Integer>> newQn2OldQn = (Map<Integer, List<Integer>>) params.get("newQn2OldQn");
        Map<Integer, QuestionStructureVO> newQn2QsMap = (Map<Integer, QuestionStructureVO>)params.get("newQn2QsMap");
        if(!MapUtils.isEmpty(newQn2OldQn)) {
            iQnMappingService.updateMapping(paperId, newQn2OldQn);
        }
        questionStructureAdjust(params, paperId, newQn2OldQn, newQn2QsMap);

        //试题新增
        List<QuestionStructureVO> addQsList = (List<QuestionStructureVO>) params.get("addQsList");
        if(CollectionUtils.isNotEmpty(addQsList)){
            iQsClientService.saveQuestionStructure(paperId, addQsList);
        }

        // 更新
//        paperManager.replace(paper);
        if(answerCardTemplate!=null && answerCardTemplate.size() > 0) {
            answerCardTemplateService.replace(answerCardTemplate);
        }
        answerCardTemplateManualService.replace(newAnswerCardTemplateManualList);

        // 更新t_question_structure
//        examQuestionStructureService.saveQuestionStructure(params);

    }

    private void questionStructureAdjust(Map<String, Object> params, long paperId, Map<Integer, List<Integer>> newQn2OldQn, Map<Integer, QuestionStructureVO> newQn2QsMap) {
        if(newQn2OldQn !=null && newQn2OldQn.size() > 0){
            List<QuestionStructureVO> questionStructureVOS = iQsClientService.listQuestionStructure(paperId);
            Map<Integer, QuestionStructureVO> qn2QuestionStructureVOMap = new HashMap<>();
            List<Integer> deleteQns = new ArrayList<>();
            List<QuestionStructureVO> addQsVOs = new ArrayList<>();
            for(QuestionStructureVO questionStructureVO: questionStructureVOS){
                qn2QuestionStructureVOMap.put(questionStructureVO.getQuestionNumber(), questionStructureVO);
            }
            for(Map.Entry<Integer, List<Integer>> entry: newQn2OldQn.entrySet()){
                Integer newQn = entry.getKey();
                List<Integer> oldQns = entry.getValue();
                if(CollectionUtils.isEmpty(oldQns)){
                    log.error("newQn="+newQn +", oldQn is null! params="+ params);
                    continue;
                }
                QuestionStructureVO newQsVO = new QuestionStructureVO();
                newQsVO.setQuestionNumber(newQn);
                QuestionStructureVO vo = newQn2QsMap.get(newQn);
                newQsVO.setStructureNumber(vo.getStructureNumber());
                newQsVO.setScoreValue(vo.getScoreValue());
                boolean firstFlag = true;
                for(Integer oldQn:oldQns) {
                    QuestionStructureVO oldQsVO = qn2QuestionStructureVOMap.get(oldQn);
                    deleteQns.add(oldQn);
                    if(firstFlag){
                        newQsVO.setQuestionType(oldQsVO.getQuestionType());
                        newQsVO.setPaperId(oldQsVO.getPaperId());
                        newQsVO.setCourseId(oldQsVO.getCourseId());
                        newQsVO.setCourseName(oldQsVO.getCourseName());
                        newQsVO.setHalfRight(oldQsVO.getHalfRight());
                        newQsVO.setQuestionIndex(oldQsVO.getQuestionIndex());
                        newQsVO.setQuestionTypeName(oldQsVO.getQuestionTypeName());
                        newQsVO.setReadType(oldQsVO.getReadType());
                        newQsVO.setUnitType(oldQsVO.getUnitType());
                        firstFlag=false;
                    }
                }
                addQsVOs.add(newQsVO);
            }
            iQsClientService.deleteQuestionStructure(paperId, deleteQns);
            iQsClientService.saveQuestionStructure(paperId, addQsVOs);
        }
    }


    private void mergeRecognitionTemplate2Template(List<Document> recognitionTemplates, List<Document> templates, Map<String, Object> params) {
        List<Integer> oldQuestionNumbers = (List<Integer>) params.get("oldQuestionNumbers");

        // 同一题可能会有多个区块.
        if (oldQuestionNumbers.size() > 0) {
            oldQuestionNumbers.stream().forEach(questionNumber -> {
                Integer number = questionNumber;
                templates.stream().forEach(template -> {
                    List<Document> cardContent = template.get("cardContent", List.class);
                    for (int i = 0; i < cardContent.size(); i++) {
                        Document questionInfo = cardContent.get(i);
                        Integer _questionNumber = questionInfo.get("questionNumber", Integer.class);
                        if (_questionNumber != null && _questionNumber.equals(number)) {
                            cardContent.remove(i);
                            i--;
                        }
                    }
                });
            });
        }

        // merge recognitionTemplates to tmeplates
        for (Document template : templates) {
            Integer pageNumber = template.get("pageNumber", Integer.class);
            int idx = -1;
            for (int i = 0; i < recognitionTemplates.size(); i++) {
                if (pageNumber.equals(recognitionTemplates.get(i).get("pageNumber", Integer.class))) {
                    idx = i;
                    break;
                }
            }
            if (idx != -1) {
                List<Document> oldCardContent = template.get("cardContent", List.class);
                List<Document> newCardContent = recognitionTemplates.get(idx).get("cardContent", List.class);
                if (newCardContent.size() > 0) {
                    oldCardContent.addAll(newCardContent);
                }
            }
        }
    }

    // 判断异常模板是不是只有拆分的题号，这样的话，就需要合并
    private boolean isRecognitionTemplateEqualNewQuestionNumbers(List<Document> recognitionTemplates, Map<String, Object> params) {
        boolean isEquals = true;
        List<Integer> newQuestionNumbers = (List<Integer>) params.get("newQuestionNumbers");

        for (int i = 0; i < recognitionTemplates.size(); i++) {
            Document template = recognitionTemplates.get(i);
            List<Document> cardContent = template.get("cardContent", List.class);
            for (int j = 0; j < cardContent.size(); j++) {
                Document questionInfo = cardContent.get(j);
                Integer questionNumber = questionInfo.get("questionNumber", Integer.class);
                Long size = newQuestionNumbers.stream().filter(number -> number.equals(questionNumber)).count();
                if (size.equals(0L)) {
                    isEquals = false;
                    break;
                }
            }
            if (!isEquals) {
                break;
            }
        }
        return isEquals;
    }

    /**
     * 获取作业的试卷URL
     *
     * @param params
     * @return
     */
    public String getHomeworkFileUrlByPaperId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();
        Map<String, Object> studentDownloadPaper = getStudentDownloadPaper(params);
        return (String) studentDownloadPaper.get("fileUrl");
    }


    /**
     * 获取 考生用卷 信息
     * 试卷优先级:
     * 1. (第一优先级) 答题卡发布作业上传的学生用卷
     * ~~2. (第二优先级) 委托录题标记的学生用卷 删除 since 20200506~~
     * 3. (第三优先级) 委托录题自动生成的试卷
     * 4. (第四优先级) 委托录题已完成可生成
     *
     * @param params paperId    试卷id
     * @return fileUrl    文件地址， 空则表示没有试卷可以下载
     * fileName   文件名     fileUrl不为空而fileName为空则表示自动生成
     * deletable  是否可删除 只有在答题卡页面上传的为1，委托录题上传的文档请自行在委托录题页操作
     * generable  是否可生成，当fileUrl/fileName为空是有该字段
     */
    public Map<String, Object> getStudentDownloadPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();

        // 获取paper必要信息    studentPaper{}  downloadUrl
        String downloadUrlQuestionFiled = "downloadUrl" + DictUtil.getDictValue("reviewType", "question");
        Document paper = paperManager.getPaperSimple(MapUtil.getLong(params, "paperId"));

        // 1. (第一优先级) 答题卡发布作业上传的学生用卷
        if (paper.get("studentPaper") != null) {
            Map<String, Object> studentPaperInfo = (Map<String, Object>) paper.get("studentPaper");
            return MapUtil.of(
                    "fileUrl", studentPaperInfo.get("fileUrl").toString(),
                    "fileName", studentPaperInfo.get("fileName").toString(),
                    "deletable", 1
            );
        }

        // 3. (第三优先级) 委托录题自动生成的试卷 需要判断是否还存在
        if (paper.get(downloadUrlQuestionFiled) != null) {
            String tempUrl = paper.get(downloadUrlQuestionFiled).toString();
            if (!ObjectUtil.isBlank(tempUrl) && FileStorageTemplate.exists(tempUrl)) {
                return MapUtil.of(
                        "fileUrl", tempUrl,
                        "fileName", null,
                        "deletable", 0
                );
            }
        }

        // 4. (第四优先级) 委托录题已完成可生成
        Map<String, Object> entrust = entrustQueryService.getEntrustById(MapUtil.of(
                "answerPaperId", params.get("paperId")
        ), false);
        if (MapUtils.isNotEmpty(entrust)) {
            if (DictUtil.isEquals(Integer.parseInt(entrust.get("entrustStatus").toString()), "entrustStatus",
                    "yiqiQuestionPushing", "yiqiProofreading", "yiqiProofreadingFinish", "complete")) {
                return MapUtil.of("generable", 1);
            }
        }

        return MapUtil.of("fileUrl", null, "fileName", null, "deletable", 0, "generable", 0);
    }

    /**
     * 上传 考生用卷(第一优先级)
     * 存放于 mongo.paper.studentPaper{fileUrl, fileName}
     *
     * @param params paperId              试卷id
     *               studentPaperFileUrl  url
     *               studentPaperFileName 文件名称
     */
    public void uploadStudentDownloadPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNumeric("userType")
                .isValidId("paperId")
                .isNotBlank("studentPaperFileUrl")
                .isNotBlank("studentPaperFileName")
                .verify();

        // 判断url对应的文件是否存在文件服务中
        if (!FileStorageTemplate.exists(params.get("studentPaperFileUrl").toString())) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "学生用卷不存在");
        }

        // 判断paperId是否存在
        long paperId = MapUtil.getLong(params, "paperId");
        Document paper = paperManager.getPaperSimple(paperId);

        // 如果已经存在需要删除
        String oldFileUrl = null;
        if (paper.get("studentPaper") != null) {
            Map<String, Object> studentPaper = (Map<String, Object>) paper.get("studentPaper");
            oldFileUrl = studentPaper.get("fileUrl").toString();
        }

        // 设置新的studentPaperInfo
        String newFileUrl = params.get("studentPaperFileUrl").toString();
        Map<String, Object> studentPaper = MapUtil.of(
                "fileUrl", newFileUrl,
                "fileName", params.get("studentPaperFileName").toString()
        );
        Bson update = combine(
                set("modifierId", Long.parseLong(params.get("userId").toString())),
                set("modifierName", params.get("userName").toString()),
                set("modifyDateTime", new Date()),
                set("studentPaper", studentPaper)
        );
        paperManager.updatePaper(paperId, update);

        // 旧的文件不为空且旧文件路径跟新文件路径不一样时，删除旧文件
        if (oldFileUrl != null && !ObjectUtil.isValueEquals(newFileUrl, oldFileUrl)) {
            FileStorageTemplate.delete(oldFileUrl);
        }
    }

    /**
     * 删除 考生用卷(第一优先级)
     * 存放于 mongo.paper.studentPaper{fileUrl, fileName}
     *
     * @param params paperId          试卷id
     */
    public void deleteStudentDownloadPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .isNotBlank("userName")
                .isNumeric("userType")
                .isValidId("paperId")
                .verify();

        // 判断paperId是否存在
        long paperId = MapUtil.getLong(params, "paperId");
        Document paper = paperManager.getPaperSimple(paperId);

        if (!paper.containsKey("studentPaper")) {
            return;
        }

        // 如果已经存在需要删除旧文件
        Map<String, Object> studentPaper = (Map<String, Object>) paper.get("studentPaper");
        String oldFileUrl = Optional.ofNullable(studentPaper).map(o -> o.get("fileUrl").toString()).orElse(null);

        Bson update = combine(
                set("modifierId", Long.parseLong(params.get("userId").toString())),
                set("modifierName", params.get("userName").toString()),
                set("modifyDateTime", new Date()),
                unset("studentPaper")
        );
        paperManager.updatePaper(paperId, update);

        if (oldFileUrl != null) {
            FileStorageTemplate.delete(oldFileUrl);
        }
    }

    /**
     * 获取试卷信息 原本的数据 不做任何处理
     *
     * @param paperId 试卷id
     * @return 试卷信息 找不到返回null
     */
    public Document getPaperSource(long paperId) {
        return paperManager.getPaper(paperId);
    }

    /**
     * 更新整张试卷信息
     */
    public void replacePaper(Document paper) {
        if (MapUtils.isEmpty(paper)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷信息必须提供");
        }
        ObjectId _id = new ObjectId(paper.get("_id").toString());
        paper.put("_id", _id);
        paperManager.replace(_id, paper);
    }

    /**
     * 替换整张试卷
     * @param paperId 试卷id
     * @param paper 试卷文档
     */
    public void replacePaper(long paperId, Document paper) {
        paperManager.replacePaper(paperId, paper);
    }

    /**
     * 删除试卷 使用mongo的_id进行删除
     */
    public void deletePaper(String paper_id) {
        if (ObjectUtil.isBlank(paper_id)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷ObjectId必须提供");
        }
        deletePaper(MongoUtil.getMongoId(paper_id));
    }

    /**
     * 删除试卷 使用mongo的_id进行删除
     */
    public void deletePaper(ObjectId paper_id) {
        if (paper_id == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "试卷ObjectId必须提供");
        }
        paperManager.delete(paper_id);
    }
    
    /**
     * 删除试卷
     */
    public void deletePaper(long paperId) {
        paperManager.deletePaper(paperId);
    }

    /**
     * 更改试卷类型
     * |paperId|试卷ID|
     * |paperType|试卷类型|
     * |unionPaperType|联考类型|
     * |examinerId|联考员ID|
     */
    public void updatePaperType(Map<String, Object> params) {

        Verify.of(params)
                .isValidId("paperId")
                .isValidId("examinerId")
                .isNumeric("paperType")
                .isNumeric("unionPaperType")
                .verify();

        long paperId = MapUtil.getLong(params, "paperId");

        Map<String, Object> em = examinerService.getExaminerUser(params);

        Bson update = combine(
                set("creatorId", Long.parseLong(em.get("userId").toString())),
                set("creatorName", em.get("userName").toString()),
                set("modifierId", Long.parseLong(em.get("userId").toString())),
                set("modifierName", em.get("userName").toString()),
                set("modifyDateTime", new Date()),
                set("paperType", Long.parseLong(params.get("paperType").toString())),
                set("unionPaperType", Long.parseLong(params.get("unionPaperType").toString()))
        );
        paperManager.updatePaper(paperId, update);
    }

    /**
     * 获取试卷列表
     *
     * @param params paperId      123,456
     * @return
     */
    public List<Document> getPaperList(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();
        List<Long> paperIdList = StringUtil.strToList(params.get("paperId").toString(), ",", Long.class);
        return paperManager.getPaperSimple(paperIdList);
    }

    /**
     * 获取答题卡结构
     *
     * @param params paperId
     * @return
     */
    public List<Map<String, Object>> getAnswerCardStructure(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();
        List<Long> paperIdList = StringUtil.strToList(params.get("paperId").toString(), ",", Long.class);
        List<Map<String, Object>> data = new ArrayList<>();
        List<Document> docs = paperManager.getPaper(paperIdList);
        for (Document doc : docs) {

            List<Map<String, Object>> rs = new ArrayList<>();
            List<Document> parts = doc.get("parts", List.class);
            for (Document p : parts) {
                List<Document> categories = p.get("categories", List.class);
                for (Document c : categories) {
                    List<Document> questions = c.get("questions", List.class);
                    for (Document q : questions) {
                        Map question = (Map) q.get("question");
                        String questionId = (String) question.get("_id");
                        if (q.get("readType") != null && Integer.parseInt(q.get("readType").toString()) > 0) {
                            q.put("questionId", questionId);
                            rs.add(getQuestionItem(q));
                        }
                        List<Document> structures = q.get("structures", List.class);
                        for (Document s : structures) {
                            if (s.get("readType") != null && Integer.parseInt(s.get("readType").toString()) > 0) {
                                s.put("questionId", questionId);
                                rs.add(getQuestionItem(s));
                            }
                        }
                    }
                }
            }
            Long paperId = Long.valueOf(doc.get("paperId").toString());
            String paperName = doc.get("paperName").toString();
            rs.forEach(r -> {
                r.put("paperId", paperId);
                r.put("paperName", paperName);
            });
            data.addAll(rs);
        }

        return data;
    }

    /**
     * 获取问题
     *
     * @param params
     * @return
     */
    private Map<String, Object> getQuestionItem(Map<String, Object> params) {
        Map<String, Object> item = new HashMap<>();
        item.put("questionId", params.get("questionId"));
        item.put("questionNumber", params.get("questionNumber"));
        item.put("structureNumber", params.get("structureNumber"));
        item.put("questionIndex", params.get("questionIndex") == null ? -1 : params.get("questionIndex"));
        item.put("questionType", params.get("questionType"));
        item.put("questionTypeName", params.get("questionTypeName"));
        item.put("readType", params.get("readType"));
        item.put("answer", params.get("answer"));
        item.put("scoreValue", params.get("scoreValue"));
        return item;
    }



    public Document getSimplePaper(long paperId) {
        return paperManager.getPaperSimple(paperId);
    }

    public Document getSimplePaperNullable(long paperId) {
        return paperManager.getPaperSimpleNullable(paperId);
    }

    public void updatePaper(long paperId, Bson updateBson) {
        paperManager.updatePaper(paperId, updateBson);
    }

    public List<Document> getPaperListByEntrustId(long entrustId) {
        return paperManager.getList(eq("entrustId", entrustId));
    }

    /**
     * 获取校本试卷试题ID列表
     * @param paperId      试卷ID
     * @param subCourseId  子课程ID
     * @return 试题ID列表
     */
    public List<String> getQuestionIdsByPaper(Long paperId, Long subCourseId) {
        Document paper = getPaper(paperId);
        List<Map<String, Object>> questions = PaperUtil.getQuestionNumbers(paper);
        Stream<Map<String, Object>> stream = questions.stream();
        if (ObjectUtil.isNotBlank(subCourseId)) {
            stream = stream.filter(item -> !ObjectUtil.isBlank(MapUtil.getLongNullable(item, "courseId")))
                    .filter(item -> ObjectUtil.isValueEquals(subCourseId, MapUtil.getLong(item, "courseId")));
        }

        return stream.map(item -> MapUtil.getString(item, "questionId")).distinct().collect(Collectors.toList());
    }

    /**
     * 获取校本试卷试题ID列表
     * @param paperId      试卷ID
     * @return 试题ID列表
     */
    public List<String> getQuestionIdsByPaper(Long paperId) {
        return getQuestionIdsByPaper(paperId, null);
    }

    /**
     * <pre>
     * 将金卷题库的信息复制到校本题库
     *   jinjuanPaper -> paper
     *      1. jinjuanPaperId-unionPaperType
     *          用于联考员发布考试使用试卷的复制，
     *          所有使用的学校加入到schoolIds
     *      2. jinjuanPaperId-unionPaperType==null-schoolIds
     *          用于学校私有的加入到校本题库，
     *          schoolIds仅会存在该学校的id，
     *          即各个学校复制了一份
     *   jinjuanQuestion -> question
     *      试题的复制只复制一份，question._id = jinjuanId._id
     *                         question.jinjuanId = jinjuanId._id
     *   answerCardTemplate -> answerCardTemplate
     *      答题卡模板跟随paper.paperId
     * </pre>
     * @param params jinjuanPaperId   金卷的paperId
     *               schoolId         学校id 会加入到schoolIds
     *               areaId           会写入到areaId字段
     *               [unionPaperType] 视情况决定是否传入
     * @return mongo.paper.paperId 校本的paperId
     */
    public long copyFromJinjuanPaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("jinjuanPaperId")
                .isLong("schoolId")
                .isLong("areaId")
                .isValidId("userId")
                .isNotBlank("userName")
                .isInteger("userType")
                .verify();
        long jinjuanPaperId = MapUtil.getLong(params, "jinjuanPaperId");
        return JedisTemplate.lockExecute(
                JedisUtil.getKey("jinjuanPaperCopyLock", String.valueOf(jinjuanPaperId)),
                30000, "有其他用户在复制金卷试卷，请稍后再重试",
                () -> _copyFromJinjuanPaper(params)
        );
    }
    
    /**
     * <pre>
     * 将教辅的信息复制到校本题库
     *   studyGuidePaper -> paper
     *      1. 全局仅复制一份 uk: studyGuidePaperId
     *      2. studyGuidePaperId=studyGuidePaper.paperId
     *   studyGuideQuestion -> question
     *      1. 全局仅复制一份 uk: studyGuideId
     *      试题的复制只复制一份，question._id = studyGuideQuestion._id
     *                         question.studyGuideId = studyGuideQuestion._id
     *   studyGuideTemplate -> answerCardTemplate
     *      答题卡模板跟随paper.paperId
     *   t_study_guide_template_structure -> exam.t_question_structure
     *   t_study_guide_question_mapping   -> exam.t_question_mapping
     * </pre>
     * @param params studyGuidePaperId 教辅的paperId
     *               schoolId         学校id 会加入到schoolIds
     *               areaId           会写入到areaId字段
     * @return mongo.paper.paperId 校本的paperId
     */
    public long copyFromStudyGuidePaper(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("studyGuidePaperId")
                .isValidId("schoolId")
                .isValidId("areaId")
                .verify();
        long studyGuidePaperId = MapUtil.getLong(params, "studyGuidePaperId");
        long schoolId = MapUtil.getLong(params, "schoolId");
        long areaId = MapUtil.getLong(params, "areaId");
        return copyFromStudyGuidePaper(studyGuidePaperId, schoolId, areaId);
    }
    
    /**
     * 复制教辅数据到主库用于考试
     *   发布考试时请调用 {@link #copyFromStudyGuidePaper(Map)}
     *   这个方法是给教辅那边数据有更改时再次同步到主库的
     */
    public long copyFromStudyGuidePaper(long studyGuidePaperId, Long schoolId, Long areaId) {
        return JedisTemplate.lockExecute(
                JedisUtil.getKey("studyGuidePaperCopyLock", String.valueOf(studyGuidePaperId)),
                30000, "有其他用户在复制教辅试卷，请稍后再重试",
                () -> selfService.copyFromStudyGuidePaperInner(studyGuidePaperId, schoolId, areaId)
        );
    }
    
    /**
     * 【仅供本类中调用】 别的地方别调用 其他说明见{@link #copyFromStudyGuidePaper(Map)}
     * @param studyGuidePaperId 教辅试卷id
     * @param schoolId 学校id 如果没有则来自教辅的调用，将数据重新同步到主库中
     * @param areaId 区域id 如果没有则来自教辅的调用，将数据重新同步到主库中
     * @return paper.paperId or 0(教辅数据变更的调用不需要操作返回0)
     */
    @Transactional(value = ExamRepository.TRANSACTION, rollbackFor = Exception.class)
    public long copyFromStudyGuidePaperInner(long studyGuidePaperId, Long schoolId, Long areaId) {
        boolean copyForExam = schoolId != null && areaId != null;
        Document paperExistNullable = paperManager.getFirst(eq("studyGuidePaperId", studyGuidePaperId));
        boolean studyGuidePaperCopied = MapUtils.isNotEmpty(paperExistNullable);
        if (!copyForExam && !studyGuidePaperCopied) {
            return 0;  // 来自教辅的调用将数据重新同步到主库中 教辅没被用过不需要同步数据
        }
        StudyGuideChapterFullInfoDTO chapterFullInfoDTO = studyGuideChapterTikuService.getFullInfoForCopy(studyGuidePaperId);
        String studyGuideChapterErrorMsg = chapterFullInfoDTO.getErrorMsg();
        if (StringUtils.isNotBlank(studyGuideChapterErrorMsg)) {
            if (copyForExam) {  // 给考试用的复制数据 章节的信息必须完整
                throw new CommonException(ResponseStatusEnum.DATA_ERROR, studyGuideChapterErrorMsg);
            }
            return 0; // 来自教辅的调用，将数据重新同步到主库中 数据不完整则不同步
        }
        Document templateManualExitNullable = answerCardTemplateManualService.getAnswerCardTemplateManualForCopyStudyGuide(studyGuidePaperId);
        // paperId 已有的使用旧的 新同步的产生新的paperId 由于模板先入库 所以模板可能已经产生过paperId
        long paperId;
        if (MapUtils.isNotEmpty(templateManualExitNullable)) {
            paperId = MapUtil.getLong(templateManualExitNullable, "paperId");
        } else if (studyGuidePaperCopied) {
            paperId = MapUtil.getLong(paperExistNullable, "paperId");
        } else {
            paperId = getPaperId(null);
        }
        // 开始转换数据
        Document paper = StudyGuideCopyUtil.convertToPaper(studyGuidePaperId, paperId, chapterFullInfoDTO.getStudyGuidePaper());
        Document templateManual = StudyGuideCopyUtil.convertToTemplate(studyGuidePaperId, paperId, chapterFullInfoDTO.getStudyGuideTemplate());
        List<Document> questionList = StudyGuideCopyUtil.convertToQuestionList(chapterFullInfoDTO.getStudyGuideQuestionList());
        List<QnMappingVO> qnMappings = StudyGuideCopyUtil.convertToQnMappingList(chapterFullInfoDTO.getStudyGuideQuestionMappingList());
        List<QuestionStructureVO> qsVOList = StudyGuideCopyUtil.convertToQsVoList(paperId, chapterFullInfoDTO.getStudyGuideTemplateStructureList());
        // 开始入库
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.getPossible();
        try {
            Integer userTypeSubjectAdmin = DictUtil.getDictValue("userType", "subjectAdmin");
            DongniUserInfoContext.set(new DongniUserInfoContext(2L, "题库管理员(教辅)", userTypeSubjectAdmin));
            // t_study_guide_question_mapping   -> exam.t_question_mapping   有事务的先走
            qnMappingClientService.saveQnMapping(paperId, qnMappings);
            // t_study_guide_template_structure -> exam.t_question_structure 有事务的先走
            if (studyGuidePaperCopied) {
                questionStructureServiceImpl.delByPaperId(paperId);
            }
            questionStructureServiceImpl.saveQuestionStructure(paperId, qsVOList);
        } finally {
            DongniUserInfoContext.set(dongniUserInfoContext);
        }
        // question
        for (Document question : questionList) {
            saveQuestionForStudyGuideCopy(schoolId, question);
        }
        // answerCardTemplateManual
        answerCardTemplateManualService.saveAnswerCardTemplateManualForStudyGuideCopy(templateManual, templateManualExitNullable);
        // paper 最后入库试卷 防止缺少其他信息 如果失败 会残留一个paperId的其他数据
        savePaperForStudyGuideCopy(schoolId, areaId, paper, paperExistNullable);
        return paperId;
    }
    
    /** 复制试卷数据给考试用 */
    private void savePaperForStudyGuideCopy(Long schoolId, Long areaId, Document paper, Document paperExistNullable) {
        if (MapUtils.isEmpty(paperExistNullable)) {
            MapUtil.<List<Long>>getCast(paper, "schoolIds").add(schoolId);
            MapUtil.<List<Long>>getCast(paper, "areaIds").add(areaId);
            paperManager.insertOne(paper);
        } else {
            List<Bson> updateList = new ArrayList<>();
            if (schoolId != null) {
                List<Long> existSchoolIdList = MapUtil.getCast(paperExistNullable, "schoolIds");
                if (!existSchoolIdList.contains(schoolId)) {
                    updateList.add(addToSet("schoolIds", schoolId));
                }
            }
            if (areaId != null) {
                List<Long> existAreaIdList = MapUtil.getCast(paperExistNullable, "areaIds");
                if (!existAreaIdList.contains(areaId)) {
                    updateList.add(addToSet("areaIds", areaId));
                }
            }
            Document updateDocument = new Document(paper);
            updateDocument.remove("_id");
            updateDocument.remove("schoolIds");
            updateDocument.remove("areaIds");
            for (Map.Entry<String, Object> entry : updateDocument.entrySet()) {
                updateList.add(set(entry.getKey(), entry.getValue()));
            }
            long paperId = MapUtil.getLong(paper, "paperId");
            paperManager.updatePaper(paperId, combine(updateList));
        }
    }
    
    /** 复制试题数据给考试用 */
    private void saveQuestionForStudyGuideCopy(Long schoolId, Document question) {
        String studyGuideQuestionId = MapUtil.getTrim(question, "_id");
        JedisTemplate.lockExecute(
                JedisUtil.getKey("studyGuideQuestionCopyLock", studyGuideQuestionId),
                3000, "有其他人正在同步请稍后再试，教辅试题: " + studyGuideQuestionId,
                () -> {
                    ObjectId questionObjectId = new ObjectId(studyGuideQuestionId);
                    Document questionNullable = questionManager.getSimpleNullable(questionObjectId);
                    boolean questionExist = MapUtils.isNotEmpty(questionNullable);
                    if (questionExist) {
                        List<Bson> updateList = new ArrayList<>();
                        if (schoolId != null) {
                            List<Long> existSchoolIdList = MapUtil.getCast(questionNullable, "schoolIds");
                            if (!existSchoolIdList.contains(schoolId)) {
                                updateList.add(addToSet("schoolIds", schoolId));
                            }
                        }
                        Document updateDocument = new Document(question);
                        // 移除掉一些考试会产生的数据 这些不能更新
                        updateDocument.remove("_id");
                        updateDocument.remove("usedCount");
                        updateDocument.remove("answerCount");
                        updateDocument.remove("difficulty");
                        updateDocument.remove("examList");
                        updateDocument.remove("userIds");
                        updateDocument.remove("schoolIds");
                        for (Map.Entry<String, Object> entry : updateDocument.entrySet()) {
                            updateList.add(set(entry.getKey(), entry.getValue()));
                        }
                        questionManager.update(questionObjectId, combine(updateList));
                    } else {
                        if (schoolId != null) {
                            List<Long> schoolIds = MapUtil.getCast(question, "schoolIds");
                            schoolIds.add(schoolId);
                        }
                        questionManager.insertOne(question);
                    }
                    return true;
                });
    }
    
    public long copyFromTemplateLib(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("templateLibPaperId")
                .isValidId("userId")
                .isNotBlank("userName")
                .isInteger("userType")
                .verify();
        long templateLibPaperId = MapUtil.getLong(params, "templateLibPaperId");
        return JedisTemplate.lockExecute(
                JedisUtil.getKey("jinjuanPaperCopyLock", String.valueOf(templateLibPaperId)),
                30000,
                () -> _copyFromTemplateLib(params)
        );
    }



    private long _copyFromTemplateLib(Map<String, Object> params) {
        //@TODO 在这里实现从模板库获取paper、answerCardTemplateManual、阅卷结构和questionMapping，生成考试用的paper、answerCardTemplateManual、阅卷结构和questionMapping
        //@大佬 @建昆 @卢衡
        // 1. 读取tiku.t_paper_template表获取模板库模板信息 paperid + templateCode等
        // 2. copy生成一份新的paper、answerCardTemplateManual、阅卷结构和questionMapping

        return -1L;
    }
    
    private long _copyFromJinjuanPaper(Map<String, Object> params) {
        long jinjuanPaperId = MapUtil.getLong(params, "jinjuanPaperId");
        long schoolId = MapUtil.getLong(params, "schoolId");
        long areaId = MapUtil.getLong(params, "areaId");
        int userType = MapUtil.getInt(params, "userType");
        
        Integer unionPaperType = MapUtil.getIntNullable(params, "unionPaperType");
        boolean singleSchool = unionPaperType == null;
        
        List<Bson> queryList = new ArrayList<>();
        queryList.add(eq("jinjuanPaperId", jinjuanPaperId));
        queryList.add(eq("unionPaperType", unionPaperType));
        if (singleSchool) {
            // 单校的，需要添加schoolId进行查询
            // 联考则不需要，因为联考的要将要添加的schoolId塞到paper中
            queryList.add(eq("schoolIds", schoolId));
        }
        
        Document schoolPaper = paperManager.getFirst(and(queryList));
        
        // 已经存在
        if (schoolPaper != null) {
            long paperId = MapUtil.getLong(schoolPaper, "paperId");
            if (!singleSchool) {
                // 联考的要将要添加的schoolId塞到paper中
                List<Long> schoolIds = MapUtil.getListLong(schoolPaper, "schoolIds");
                if (!schoolIds.contains(schoolId)) {
                    paperManager.addToSetPaper(paperId, "schoolIds", schoolId);
                }
            }
            answerCardTemplateService.copyFromJinjuanAnswerCardTemplate(jinjuanPaperId, paperId);
            return paperId;
        }
        
        // 需要复制试卷 申请一个试卷id
        long paperId = getPaperId(null);
        // jinjuanPaper
        Map<String, Object> jinjuanParams = MapUtil.of("paperId", jinjuanPaperId, "userType", userType);
        Document jinjuanPaper = jinjuanPaperService.getPaperSource(jinjuanParams);
        Map<String, Object> paper = JinjuanPaperUtil.transferToPaper(jinjuanPaper);
        paper.put("unionPaperType", unionPaperType);
        paper.put("areaId", areaId);
        paper.put("paperId", paperId);
        paper = PaperUtil.getPaper(paper);
        
        // copy question 必须先复制试题
        ownQuestionService.copyByPaperFromJinjuanQuestion(params, jinjuanPaper);
        // copy paper
        insertPaper(params, new Document(paper));
        paperManager.addToSetPaper(paperId, "schoolIds", schoolId);
        // copy answerCardTemplate
        answerCardTemplateService.copyFromJinjuanAnswerCardTemplate(jinjuanPaperId, paperId);
        return paperId;
    }

    /**
     * 软删除试卷-仅产品顾问、老师、联考员使用
     * 产品顾问、老师-paper里面的schoolIds字段去除对应的schoolId
     * 联考员-置paper的creatorId=1、creatorName=’删除‘、schoolIds清空
     *
     * @param params paperId [schoolId]
     */
    public void softDelete(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .isInteger("userType")
                .verify();

        int userType = MapUtil.getInt(params, "userType");
        if (!DictUtil.isEquals(userType, "userType", "product", "teacher", "examiner")) {
            throw new CommonException(ResponseStatusEnum.PERMISSION_DENIED, "仅允许产品顾问、老师、联考员访问!");
        }


        // 联考员
        if (DictUtil.isEquals(userType, "userType", "examiner")) {
            paperManager.updateOne(eq("paperId", MapUtil.getLong(params, "paperId")),
                    combine(set("creatorId", 1L),
                            set("creatorName", "删除"),
                            set("schoolIds", new ArrayList<>())));
        } else {
            paperManager.updateOne(eq("paperId", MapUtil.getLong(params, "paperId")),
                pull("schoolIds", MapUtil.getLong(params, "schoolId")));
        }
    }
    
    /**
     * 获取试卷答题卡列表查询条件
     *
     * 详见docs/tiku/own/试卷答题卡列表查询条件
     *
     * 用于替换 {@link #getQueryParams(Map)}
     * @param params 见注释
     * @param otherAndFilters 其他的and查询条件
     * @return 可能为null
     */
    private Bson getPaperQueryBson(Map<String, Object> params, final Bson... otherAndFilters) {
        long userId = MapUtil.getLong(params, "userId");
        int userType = MapUtil.getInt(params, "userType");
        
        String userTypeDictEnName = "userType";
        Map<String, Integer> userTypeKey2Value = DictUtil.getDictKeyValue(userTypeDictEnName);
        int userTypeSubjectAdmin = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value, "subjectAdmin");                //  7 题库管理员
        int userTypeProduct = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value,"product");                           //  1 产品顾问
        int userTypeTeacher = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value,"teacher");                           //  3 老师
        int userTypeExaminer = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value,"examiner");                         // 13 联考员
        int userTypeOperatorReviewer = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value,"operatorReviewer");         // 18 运营-审核员
        int userTypeOperatorProofreader = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value,"operatorProofreader");   // 19 运营-校对员
        int userTypePrepareLeader = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value,"prepareLeader");               // 12 备课组长
        int userTypeSchoolCourseDirector = DictUtil.getDictValue(userTypeDictEnName, userTypeKey2Value,"schoolCourseDirector"); // 20 教研组长

        // 身份不在列表中的 不让查
        boolean validUserType = userType == userTypeSubjectAdmin || userType == userTypeProduct
                || userType == userTypeTeacher || userType == userTypeExaminer
                || userType == userTypeOperatorReviewer || userType == userTypeOperatorProofreader
                || userType == userTypePrepareLeader || userType == userTypeSchoolCourseDirector;
        if (!validUserType) { return returnInvalidQuery(); }
    
        List<Bson> queryAndList = new ArrayList<>();
        
        // -------------------------------- 有所关联的
        
        Long schoolId = MapUtil.getLongNullable(params, "schoolId");
        Integer stage = MapUtil.getIntNullable(params, "stage");
        List<Integer> gradeTypeList = null;
        if (userType == userTypeTeacher || userType == userTypeProduct ||
                userType == userTypePrepareLeader || userType == userTypeSchoolCourseDirector) {
            if (schoolId == null) { return returnInvalidQuery(); } // 老师/产品顾问必须提供schoolId
    
            List<Map<String, Object>> userSchoolList = userRelativeService.getUserSchool(params);
            boolean validSchoolId = userSchoolList.stream()
                    .anyMatch(userSchool -> MapUtil.getLong(userSchool, "schoolId") == schoolId);
            if (!validSchoolId) { return returnInvalidQuery(); }   // 老师找不到学校/产品顾问不维护指定的学校
    
            gradeTypeList = stage == null
                    ? gradeService.getGradeTypeBySchoolId(params)
                    : gradeService.getGradeTypeBySchoolIdAndStage(params);
            if (CollectionUtils.isEmpty(gradeTypeList)) { return returnInvalidQuery(); }  // stage不在学校学段范围内
        } else if (userType == userTypeExaminer) {
            if (stage != null) {
                int educationSystem = DictUtil.getDictValue("educationSystem", "ordinary");
                gradeTypeList = GradeUtil.getGradeType(educationSystem, stage);
            }
        }
        
        // [gradeType] 年级
        Integer gradeType = MapUtil.getIntNullable(params, "gradeType");
        if (gradeType == null) {
            if (CollectionUtils.isNotEmpty(gradeTypeList)) {
                // 不指定年级的，限制在关联的学校-学段的年级列表中
                queryAndList.add(in("gradeType", gradeTypeList));
            }
        } else {
            // 指定年级的
            if (CollectionUtils.isNotEmpty(gradeTypeList)) {
                // 需要校验的年级列表 否则返回无效的查询条件
                if (gradeTypeList.contains(gradeType)) {
                    queryAndList.add(eq("gradeType", gradeType));
                } else {
                    return returnInvalidQuery();
                }
            } else {
                // 不需要校验的直接查
                queryAndList.add(eq("gradeType", gradeType));
            }
        }
        
        // [areaId] 区域id获取到其所有的子id进行查询
        Long areaId = MapUtil.getLongNullable(params, "areaId");
        if (areaId != null) {
            List<Map<String, Object>> areaList = commonAreaService.getAreaChild(params);
            if (CollectionUtils.isEmpty(areaList)) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "区域id不存在: " + areaId);
            }
            List<Long> areaIdList = areaList.stream()
                    .map(item -> MapUtil.getLong(item, "areaId"))
                    .collect(Collectors.toList());
            queryAndList.add(in("areaId", areaIdList));
        }
        
        // 关联章节
        Object catalogRelatedObj = params.get("catalogRelated");
        if (catalogRelatedObj != null) {
            boolean catalogRelated = MapUtil.getBoolean(catalogRelatedObj);
            String textbookCatalogIdListFieldName = "textbookCatalogIdList";
            if (catalogRelated) {
                // 查询已关联章节的试卷
                String textbookCatalogIds = MapUtil.getTrimNullable(params, "textbookCatalogIds");
                List<String> textbookCatalogIdList = null;
                if (textbookCatalogIds != null) {
                    textbookCatalogIdList = Arrays.stream(textbookCatalogIds.split(","))
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(toList());
                }
                if (CollectionUtils.isEmpty(textbookCatalogIdList)) {
                    // 已关联但没查询条件的
                    queryAndList.add(and(
                            ne(textbookCatalogIdListFieldName, null),
                            ne(textbookCatalogIdListFieldName, new ArrayList<>())
                    ));
                } else {
                    // 已关联且有查询条件的
                    List<Map<String, Object>> textbookCatalogAndGrandchildren =
                            ownPaperCatalogService.getTextbookCatalogAndGrandchildren(textbookCatalogIdList);
                    List<String> textbookCatalogAndGrandchildrenIdList = textbookCatalogAndGrandchildren.stream()
                            .map(textbookCatalog -> MapUtil.getTrim(textbookCatalog, "_id"))
                            .distinct()
                            .collect(toList());
                    queryAndList.add(in(textbookCatalogIdListFieldName, textbookCatalogAndGrandchildrenIdList));
                }
            } else {
                // 查询未关联章节的试卷
                queryAndList.add(or(
                        eq(textbookCatalogIdListFieldName, null),
                        eq(textbookCatalogIdListFieldName, new ArrayList<>())
                ));
            }
        }
        
        // -------------------------------- 直接查字段的
        
        // [courseId]
        Long courseId = MapUtil.getLongNullable(params, "courseId");
        if (courseId != null) {
            queryAndList.add(eq("courseId", courseId));
        }
    
        // [creatorId] [otherUserCreate] 全部 或 我的试卷/答题卡(creatorId)不用参数的 用userId的 防止改接口参数 或 别人的试卷/答题卡(otherUserCreate)
        // 备课组长、教研组长也只能查询自己创建的
        if (userType == userTypeExaminer || userType == userTypePrepareLeader || userType == userTypeSchoolCourseDirector) {
            // 联考员只能查询自己创建的
            queryAndList.add(eq("creatorId", userId));
        } else {
            Long creatorId = MapUtil.getLongNullable(params, "creatorId");
            if (creatorId != null) {
                // 查询自己的
                queryAndList.add(eq("creatorId", userId));
            } else if (MapUtil.getBoolean(params, "otherUserCreate")) {
                // 查询别人的
                queryAndList.add(ne("creatorId", userId));
            }
        }
        
        // [isCollect] 我的收藏
        boolean isCollect = MapUtil.getBoolean(params, "isCollect");
        if (isCollect) {
            queryAndList.add(eq("userIds", userId));
        }
        // [paperStatus] 发布状态 未引用/已引用
        Integer paperStatus = MapUtil.getIntNullable(params, "paperStatus");
        if (paperStatus != null) {
            queryAndList.add(eq("paperStatus", paperStatus));
        }
        // [answerCardStatus] 是否有答题卡模板
        Integer answerCardStatus = MapUtil.getIntNullable(params, "answerCardStatus");
        if (answerCardStatus != null) {
            queryAndList.add(eq("answerCardStatus", answerCardStatus));
        }
        // [cardVersionCode] 答题卡版本号编码，用于过滤掉不兼容的答题卡，避免被引用
        Integer cardVersionCode = MapUtil.getIntNullable(params, "cardVersionCode");
        if (cardVersionCode != null) {
            queryAndList.add(gte("cardVersionCode", cardVersionCode));
        }
        // [year]
        Integer year = MapUtil.getIntNullable(params, "year");
        if (year != null) {
            queryAndList.add(eq("year", year));
        }
        
        // [search]: paperName模糊 or paperId精准
        String search = MapUtil.getTrimNullable(params, "search");
        if (search != null) {
            Bson queryRegexPaperName = regex("paperName", search);
            Long searchLong = MapUtil.getLongNullable(search);
            if (searchLong == null) {
                queryAndList.add(queryRegexPaperName);
            } else {
                queryAndList.add(or(
                        queryRegexPaperName,
                        eq("paperId", searchLong)
                ));
            }
        }
        
        // [paperType] 试卷类型
        String paperType = MapUtil.getTrimNullable(params, "paperType");
        if (paperType != null) {
            List<Integer> paperTypeList = Arrays.stream(paperType.split(","))
                    .map(MapUtil::getIntNullable)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(toList());
            if (CollectionUtils.isNotEmpty(paperTypeList)) {
                queryAndList.add(in("paperType", paperTypeList));
            }
        }
        
        // [ascriptionType] [schoolIds]
        if (userType != userTypeExaminer && userType != userTypeSubjectAdmin) {
            // 非联考员，非题库管理员
            // ascriptionType schoolIds
            int ascriptionTypeAll = DictUtil.getDictValue("ascriptionType", "all");
            int ascriptionType = MapUtil.getInt(params, "ascriptionType", ascriptionTypeAll);
            Bson queryAscriptionType = eq("ascriptionType", ascriptionType);
            if (ascriptionType == ascriptionTypeAll) {
                // 查询所有试卷
                if (schoolId != null) {
                    queryAndList.add(or(queryAscriptionType, eq("schoolIds", schoolId)));
                } else {
                    queryAndList.add(queryAscriptionType);
                }
            } else {
                // 查询个人试卷
                if (schoolId == null) {
                    throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "查询个人试卷时schoolId必须提供");
                }
                queryAndList.add(queryAscriptionType);
                queryAndList.add(eq("schoolIds", schoolId));
            }
        }
    
        // [unionPaperType] 区域试卷类型
        if (userType == userTypeExaminer) {
            // 联考员只能查区域考和联考试卷
            int unionPaperTypeAreaPaper = DictUtil.getDictValue("unionPaperType", "areaPaper");
            int unionPaperTypeUnionPaper = DictUtil.getDictValue("unionPaperType", "unionPaper");
            queryAndList.add(in("unionPaperType", unionPaperTypeAreaPaper, unionPaperTypeUnionPaper));
        }
        
        // 附加其他的查询条件
        if (otherAndFilters != null && otherAndFilters.length > 0) {
            for (Bson otherAndFilter : otherAndFilters) {
                if (otherAndFilter != null) {
                    queryAndList.add(otherAndFilter);
                }
            }
        }
        
        return CollectionUtils.isEmpty(queryAndList) ? null : and(queryAndList);
    }
    
    /**
     * 一个无效的查询
     * @return 无效的查询
     */
    private Bson returnInvalidQuery() {
        return eq("paperId", Long.MIN_VALUE);
    }
    
    /**
     * 获取查询正常组卷的查询条件
     * @return 查询正常组卷的查询条件
     */
    private Bson getQueryCreationTypeNormal() {
        return eq("creationType", DictUtil.getDictValue("paperCreationType", "normal"));
    }
    
    /**
     * 获取查询正常组卷的查询条件
     * @return 查询正常组卷的查询条件
     */
    public Bson getQueryNotExamPaper() {
        return ne(PaperUtil.PAPER_EXAM_PAPER_TAG_FILED, true);
    }
    
    /**
     * 获取查询正常组卷的查询条件
     * @return 查询正常组卷的查询条件
     */
    public Bson getQueryNotPaperTemplate() {
        return ne(PaperUtil.PAPER_TEMPLATE_TAG_FILED, true);
    }
    
    /**
     * 获取查询答题卡的条件
     *    正常组卷并且有答题卡 或 自主编辑的试卷
     * @return 查询答题卡的条件
     */
    private Bson getQueryAnswerCard() {
        // 正常组卷并且有答题卡
        Bson queryNormalHasAnswerCard = and(
                getQueryCreationTypeNormal(),
                eq("answerCardStatus", PaperAnswerCardStatus.HAS.getStatus())
        );
        // 自主编辑的试卷
        Bson querySelfEdit = and(eq("creationType", DictUtil.getDictValue("paperCreationType", "selfEdit")), eq("answerCardStatus", PaperAnswerCardStatus.HAS.getStatus()));
        return or(queryNormalHasAnswerCard, querySelfEdit);
    }
    
    /**
     * 获取答题卡的排序规则
     *
     * 排序1. 按照创建时间逆序排序（默认）
     *        参数: sortKey=createDateTime or null
     *              sortType=desc          or null
     * 排序2. 按照创建时间顺序排序
     *        参数: sortKey=createDateTime
     *              sortType=asc
     * 排序3. 按照引用次数逆序排序,创建时间逆序排序
     *        参数: sortKey=cardReferenceCount
     *              sortType=desc or null
     * 排序4: 按照引用次数顺序排序,创建时间逆序排序
     *        参数: sortKey=cardReferenceCount
     *              sortType=asc
     * @param params [sortKey] [sortType]
     * @return 排序
     */
    private static Order getAnswerCardOrder(Map<String, Object> params) {
        List<String> sortKeyList = Stream.of("createDateTime", "cardReferenceCount").collect(toList());
        List<String> sortTypeList = Stream.of("desc", "asc").collect(toList());
        String sortKeyDefault = sortKeyList.get(0);
        String sortTypeDefault = sortTypeList.get(0);
        
        String sortKey = MapUtil.getTrim(params, "sortKey", "");
        if (!sortKeyList.contains(sortKey) && !sortKeyDefault.equals(sortKey)) {
            sortKey = sortKeyDefault;
        }
        String sortType = MapUtil.getTrim(params, "sortType", "").toLowerCase();
        if (!sortTypeList.contains(sortType) && !sortTypeDefault.equals(sortType)) {
            sortType = sortTypeDefault;
        }
        
        List<Order> orderList = new ArrayList<>();
        if ("desc".equals(sortType)) {
            orderList.add(Order.Field.desc(sortKey));
        } else {
            orderList.add(Order.Field.asc(sortKey));
        }
        
        if (!sortKeyDefault.equals(sortKey)) {
            if ("desc".equals(sortTypeDefault)) {
                orderList.add(Order.Field.desc(sortKeyDefault));
            } else {
                orderList.add(Order.Field.asc(sortKeyDefault));
            }
        }
        
        return Order.by(orderList.toArray(new Order[0]));
    }
    
    /**
     * 获取查询条件 createDateTime > xxx
     * @param params [startDate]
     * @return query or null
     */
    private @Nullable Bson getQueryGteCreateDateTime(Map<String, Object> params) {
        // 创建时间
        Bson queryGteCreateDateTime = null;
        String startDate = MapUtil.getTrimNullable(params, "startDate");
        if (startDate != null) {
            try {
                queryGteCreateDateTime = gte("createDateTime", DateUtil.parseDateTime(startDate));
            } catch (ParseException e) {
                log.error("startDate参数不正确", e);
            }
        }
        return queryGteCreateDateTime;
    }

    /**
     * 更新paper和answerCardTemplate的structureNumber
     * @param updatePaperStructureParam paperId questionNumber structureNumber userId userName
     */
    public void updatePaperStructureNumberByQuestionNumber(UpdatePaperStructureParam updatePaperStructureParam) {

        // 更新paper的structureNumber
        updatePaperStructureNumber(updatePaperStructureParam);

        // 更新answerCardTemplate的structureNumber
        answerCardTemplateService.updateAnswerCardTemplateStructureNumber(updatePaperStructureParam);

    }

    /**
     * 更新paper的structureNumber
     * @param updatePaperStructureParam paperId questionNumber structureNumber
     */
    private void updatePaperStructureNumber(UpdatePaperStructureParam updatePaperStructureParam) {
        Long paperId = updatePaperStructureParam.getPaperId();
        Integer questionNumber = updatePaperStructureParam.getQuestionNumber();
        String structureNumber = updatePaperStructureParam.getStructureNumber();
        Long userId = updatePaperStructureParam.getUserId();
        String userName = updatePaperStructureParam.getUserName();
        LocalDateTime now = LocalDateTime.now();

        Document paperDoc = getPaper(paperId);
        if (MapUtils.isEmpty(paperDoc)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "试卷不存在");
        }
        Document updatePaper = new Document();
        List<Map<String, Object>> parts = MapUtil.getListMap(paperDoc, "parts");
        cycleFlag:
        for (int partIndex = 0; partIndex < parts.size(); partIndex++) {
            List<Map<String, Object>> categories = MapUtil.getListMap(parts.get(partIndex), "categories");
            for (int categoryIndex = 0; categoryIndex < categories.size(); categoryIndex++) {
                List<Map<String, Object>> questions4paper = MapUtil.getListMap(categories.get(categoryIndex), "questions");
                for (int questionIndex = 0; questionIndex < questions4paper.size(); questionIndex++) {
                    Map<String, Object> paperQuestion = questions4paper.get(questionIndex);
                    int outerQuestionNumber4Paper = MapUtil.getInt(paperQuestion, "questionNumber");
                    // 判断外层的questionNumber是否相等
                    if (ObjectUtil.isValueEquals(outerQuestionNumber4Paper, questionNumber)) {
                        String questionPath = "parts." + partIndex + ".categories." + categoryIndex + ".questions." + questionIndex + ".structureNumber";
                        updatePaper.append(questionPath, structureNumber);
                        break;
                    }
                    List<Map<String, Object>> structureList = MapUtil.getListMap(paperQuestion, "structures");
                    for (int structureIndex = 0; structureIndex < structureList.size(); structureIndex++) {
                        String questionPath = "parts." + partIndex + ".categories." + categoryIndex + ".questions." + questionIndex + ".structures." + structureIndex + ".structureNumber";
                        Map<String, Object> structure = structureList.get(structureIndex);
                        Long innerQuestionNumber4Structure = MapUtil.getLong(structure, "questionNumber");
                        if (ObjectUtil.isValueEquals(innerQuestionNumber4Structure, questionNumber)) {
                            updatePaper.append(questionPath, structureNumber);
                            break cycleFlag;
                        }
                    }
                }
            }
        }

        if (MapUtils.isNotEmpty(updatePaper)) {
            updatePaper.put("modifierId", userId);
            updatePaper.put("modifierName", userName);
            updatePaper.put("modifyDateTime", now);
            updatePaper(paperId, new Document("$set", updatePaper));
        }
    }
    
    /**
     * 获取试卷的试题结构信息
     * @param paperId 试卷id
     * @return 试题结构信息
     */
    public List<PaperQuestionStructureInfoDTO> getQuestionStructureInfo(long paperId) {
        List<PaperQuestionStructureInfoDTO> questionStructureList = new ArrayList<>();
        Document paper = paperManager.getPaper(paperId);
        AtomicInteger questionStructureIndexAtomic = new AtomicInteger();
        PaperUtil.forEachQuestion(paper, paperQuestion -> {
            int questionStructureIndex = questionStructureIndexAtomic.incrementAndGet();
            long courseId = MapUtil.getLong(paperQuestion, "courseId");
            Map<String, Object> questionRealInfo = MapUtil.getCast(paperQuestion, "question");
            String questionId = MapUtil.getStringNullable(questionRealInfo, "_id");
            boolean optional = MapUtil.getBoolean(paperQuestion, "isOptional");
            Integer optionalGroup = MapUtil.getIntNullable(paperQuestion, "optionalGroup");
            Integer optionalCount = MapUtil.getIntNullable(paperQuestion, "optionalCount");
            int readType = MapUtil.getInt(paperQuestion, "readType", -1);
            if (readType > 0) {
                int questionNumber = MapUtil.getInt(paperQuestion, "questionNumber");
                String structureNumber = MapUtil.getString(paperQuestion, "structureNumber");
                List<KnowledgeInfoDTO> knowledgeInfoList = getSubQuestionKnowledgeInfoList(questionRealInfo);
                PaperQuestionStructureInfoDTO info = new PaperQuestionStructureInfoDTO();
                info.setPaperId(paperId);
                info.setQuestionStructureIndex(questionStructureIndex);
                info.setQuestionNumber(questionNumber);
                info.setStructureNumber(structureNumber);
                info.setStructureIndex(null);
                info.setReadType(readType);
                info.setCourseId(courseId);
                info.setQuestionId(questionId);
                info.setOptional(optional);
                info.setOptionalGroup(optionalGroup);
                info.setOptionalCount(optionalCount);
                info.setKnowledgeInfoList(knowledgeInfoList);
                info.setUnitType(MapUtil.getInt(paperQuestion, "unitType", 0));
                info.setOptionsCount(MapUtil.getIntNullable(paperQuestion, "optionsCount"));
                questionStructureList.add(info);
            } else {
                List<Map<String, Object>> paperQuestionStructureList = MapUtil.getCast(paperQuestion, "structures");
                List<Map<String, Object>> subQuestionRealInfoList = MapUtil.getCast(questionRealInfo, "questions");
                
                for (int index = 0, structureSize = paperQuestionStructureList.size(); index < structureSize; index++) {
                    Map<String, Object> paperQuestionStructure = paperQuestionStructureList.get(index);
                    readType = MapUtil.getInt(paperQuestionStructure, "readType", -1);
                    if (readType > 0) {
                        int questionNumber = MapUtil.getInt(paperQuestionStructure, "questionNumber");
                        String structureNumber = MapUtil.getString(paperQuestionStructure, "structureNumber");
                        Map<String, Object> subQuestionRealInfo = null;
                        if (subQuestionRealInfoList != null) {
                            if (subQuestionRealInfoList.size() != structureSize) {
                                throw new CommonException(ResponseStatusEnum.DATA_ERROR, "试卷试题结构与试题的小题数量不一致");
                            }
                            subQuestionRealInfo = subQuestionRealInfoList.get(index);
                        }
                        List<KnowledgeInfoDTO> knowledgeInfoList = getQuestionAndSubQuestionKnowledgeInfoList(subQuestionRealInfo);
                        PaperQuestionStructureInfoDTO info = new PaperQuestionStructureInfoDTO();
                        info.setPaperId(paperId);
                        info.setQuestionStructureIndex(questionStructureIndex);
                        info.setQuestionNumber(questionNumber);
                        info.setStructureNumber(structureNumber);
                        info.setStructureIndex(index);
                        info.setReadType(readType);
                        info.setCourseId(courseId);
                        info.setQuestionId(questionId);
                        info.setOptional(optional);
                        info.setOptionalGroup(optionalGroup);
                        info.setOptionalCount(optionalCount);
                        info.setKnowledgeInfoList(knowledgeInfoList);
                        info.setUnitType(MapUtil.getInt(paperQuestionStructure, "unitType", 0));
                        info.setOptionsCount(MapUtil.getInt(paperQuestionStructure, "optionsCount", 0));
                        questionStructureList.add(info);
                    }
                }
            }
        });
        return questionStructureList;
    }
    
    /**
     * 获取试题及小题的知识点信息
     * @param questionRealInfo 试题信息
     * @return 试题及小题的知识点信息
     */
    private List<KnowledgeInfoDTO> getQuestionAndSubQuestionKnowledgeInfoList(Map<String, Object> questionRealInfo) {
        List<KnowledgeInfoDTO> knowledgeInfoDTOList = new ArrayList<>();
        List<Map<String, Object>> knowledgeList = MapUtil.getCast(questionRealInfo, "knowledgeList");
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            for (Map<String, Object> knowledgeMap : knowledgeList) {
                KnowledgeInfoDTO knowledgeInfoDTO = new KnowledgeInfoDTO();
                knowledgeInfoDTO.setKnowledgeId(MapUtil.getString(knowledgeMap, "_id"));
                knowledgeInfoDTO.setKnowledgeName(MapUtil.getString(knowledgeMap, "name"));
                knowledgeInfoDTOList.add(knowledgeInfoDTO);
            }
        }
        knowledgeInfoDTOList.addAll(getSubQuestionKnowledgeInfoList(questionRealInfo));
        
        return knowledgeInfoDTOList.stream()
                .filter(StreamUtil.distinctByKey(KnowledgeInfoDTO::getKnowledgeId))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取小题的知识点信息
     * @param questionRealInfo 试题信息
     * @return 小题的知识点信息
     */
    private List<KnowledgeInfoDTO> getSubQuestionKnowledgeInfoList(Map<String, Object> questionRealInfo) {
        List<KnowledgeInfoDTO> knowledgeInfoDTOList = new ArrayList<>();
        List<Map<String, Object>> subQuestionList = MapUtil.getCast(questionRealInfo, "questions");
        if (CollectionUtils.isNotEmpty(subQuestionList)) {
            for (Map<String, Object> subQuestion : subQuestionList) {
                knowledgeInfoDTOList.addAll(getQuestionAndSubQuestionKnowledgeInfoList(subQuestion));
            }
        }
        return knowledgeInfoDTOList.stream()
                .filter(StreamUtil.distinctByKey(KnowledgeInfoDTO::getKnowledgeId))
                .collect(Collectors.toList());
    }


    /**
     * 获取试卷结构
     * @param paperId 试卷ID
     * @return 试卷的小题结构
     */
    public List<Map<String, Object>> getPaperStructureById(Long paperId) {
        Document paper = getPaper(paperId);
        return PaperUtil.getPaperStructure(paper);
    }


    /**
     * 获取试卷结构
     * @param paperIdList 试卷ID列表
     * @return 试卷的小题结构
     */
    public List<Map<String, Object>> getPaperStructureByIds(List<Long> paperIdList) {

        List<Map<String, Object>> paperStructureList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(paperIdList)) {
            return Lists.newArrayList();
        }
        List<Document> paperList = paperManager.getList(in("paperId", paperIdList));
        paperList.forEach(paper -> {
            List<Map<String, Object>> singlePaperStructureList = PaperUtil.getPaperStructure(paper);
            singlePaperStructureList.forEach(paperStructure -> paperStructure.put("paperId", MapUtil.getLong(paper, "paperId")));
            paperStructureList.addAll(singlePaperStructureList);
        });
        return paperStructureList;
    }
    
    /**
     * 获取所有考试用卷相关的试卷idList
     * @param paperId 试卷id 可以是来源的也可以是考试用卷
     * @return 试卷isList
     */
    public List<Long> getExamPaperCopyRelationPaperIdList(long paperId) {
        List<Long> paperIdList = new ArrayList<>();
        Document paperSimple = paperManager.getPaperSimple(paperId);
        long sourcePaperId = MapUtil.getLong(paperSimple, "sourcePaperId", paperId);
        paperIdList.add(sourcePaperId);
        List<Document> paperSimpleList = paperManager.getSimpleList(eq("sourcePaperId", sourcePaperId));
        paperSimpleList.forEach(paper -> paperIdList.add(MapUtil.getLong(paper, "paperId")));
        return paperIdList;
    }
    
    /**
     * 内部使用的保存卷子
     * @param paper 试卷信息 直接存储 注意格式
     */
    void savePaperInner(Document paper) {
        paperManager.insertOne(paper);
    }

    void saveAnswerCardTemplateManual(Map<String, Object> params){
        long paperId = MapUtil.getLong(params, "paperId");
        long templateCode = MapUtil.getLong(params, "templateCode");
        mongoDatabase.getCollection("answerCardTemplateManual")
                .updateOne(and(
                        eq("paperId", paperId),
                        eq("templateCode", templateCode)),
                        Updates.combine(set("answerCardTemplate",params.get("answerCardTemplate")),
                                set("modifierId",params.get("userId")),
                                set("modifierName",params.get("userName")),
                                set("modifyDateTime", DateUtil.getCurrentDateTime())));
    }


    /**
     * 诊断管理-试题设置-添加试题-识别完成
     *    答题卡添加临时试题-给委托关联试题使用
     * 调用方加锁执行-防止多个添加试题
     *
     * @param paperId 试卷id
     */
    public String insertTmpSubjectQuestion(long paperId) {
        Map<String, Object> paperDetail = getPaperDetail(MapUtil.of("paperId", paperId));
        int questionCount = MapUtil.getInt(paperDetail, "questionCount");
        List<Map<String, Object>> paperStructure = PaperUtil.getPaperStructure(paperDetail);
        // 试卷有主观题不用加
        if (paperStructure.stream().anyMatch(i -> MapUtil.getInt(i, "readType") == 2)) {
            return "试卷有主观题-不用加";
        }
        // answerCardStatus!=0 不用加
        int answerCardStatus = MapUtil.getInt(paperDetail, "answerCardStatus", 0);
        if (answerCardStatus != 0) {
            return "有系统答题卡-不用加";
        }

        // 有委托但是answerPaperId不是当前试卷 || 委托已经合并不用加
        Long entrustId = MapUtil.getLongNullable(paperDetail, "entrustId");
        if (entrustId != null) {
            Map<String, Object> entrust = entrustQueryService.getEntrustById(MapUtil.of("entrustId", entrustId), false);
            if (MapUtils.isNotEmpty(entrust)) {
                long answerPaperId = MapUtil.getLong(entrust, "answerPaperId");
                int entrustStatus = MapUtil.getInt(entrust, "entrustStatus");
                if (answerPaperId != paperId
                        || DictUtil.isEquals(entrustStatus, "entrustStatus", "complete", "yiqiQuestionPushing", "yiqiProofreading", "yiqiProofreadingFinish")) {
                    return "有委托但是answerPaperId不是当前试卷或者委托已经合并-不用加";
                }
            }
        }

        long courseId = MapUtil.getLong(paperDetail, "courseId");
        Map<String, Object> courseDetail = commonCourseService.getCourseByCourseId(MapUtil.of("courseId", courseId));
        List<Map<String, Object>> subCourseInfoList = MapUtil.getCast(courseDetail, "subCourseInfoList");
        int maxQuestionNumber = paperStructure.stream().map(i -> MapUtil.getInt(i, "questionNumber")).max(Integer::compare)
                .orElse(0);
        // 构造需要新增的试题数据并更新到试卷
        Document newPart = getTmpSubjectQuestionPart(subCourseInfoList, maxQuestionNumber);

        Bson query = and(eq("paperId", paperId), eq("questionCount", questionCount));
        Bson update = combine(push("parts", newPart), set("questionCount", questionCount + subCourseInfoList.size()));
        paperManager.updateOne(query, update);

        return "试题添加成功";
    }

    private Document getTmpSubjectQuestionPart(List<Map<String, Object>> subCourseInfoList, int maxQuestionNumber) {
        int newQuestionNumber = maxQuestionNumber + 100_0000;

        List<Document> questions = new ArrayList<>();
        for (int i = 0; i < subCourseInfoList.size(); i++) {
            Map<String, Object> subCourse = subCourseInfoList.get(i);
            long courseId = MapUtil.getLong(subCourse, "courseId");
            String courseName = MapUtil.getString(subCourse, "courseName");
            Document question = new Document()
                    .append("courseId", courseId)
                    .append("courseName", courseName)
                    .append("questionType", 3)
                    .append("questionTypeName", "解答题")
                    .append("belongType", 0)
                    .append("difficulty", 1)
                    .append("partType", 2)
                    .append("readType", 2)
                    .append("numberType", 2)
                    .append("isSetStructureNumber", false)
                    .append("questionNumber", i * 100 + newQuestionNumber)
                    .append("structureNumber", "新增的试题" + courseId)
                    .append("scoreValue", 5)
                    .append("unitType", 5)
                    .append("structures", CollectionUtils.emptyCollection())
                    .append("question", new Document().append("courseId", courseId).append("courseName", courseName));
            questions.add(question);
        }

        Document newCategory = new Document()
                .append("questionType", 3)
                .append("questionTypeName", "解答题")
                .append("categoryTitle", "二、解答题")
                .append("categoryType", 1)
                .append("isSetStructureNumber", false)
                .append("questions", questions);

        return new Document()
                .append("partType", 2)
                .append("partTitle", "新增的卷")
                .append("categories", Collections.singletonList(newCategory));
    }
    
    /**
     * 从校本题库导入区域题库试卷
     * @param importInfo 导入信息
     *                   schoolPaperId
     *                   paperYear year
     *                   paperTypeName
     *                   areaNames areaNameList areaIdList
     * @return 导入错误信息 没有消息就是最好的消息
     */
    public String importUnionPaperFromSchoolPaper(Map<String, Object> importInfo,
                                                  Map<Integer, Map<String, Integer>> stage2label2paperTypeValue,
                                                  Map<Long, Integer> commonCourseId2Stage) {
        long schoolPaperId = MapUtil.getLong(importInfo, "schoolPaperId");
        String lockKey = "TIKU:AREA:PAPER:IMPORT_FROM_SCHOOL_PAPER:" + schoolPaperId;
        String errMsg = JedisTemplate.lockExecute(lockKey, 10_000, () -> {
            // 查当前的校本试卷 校验一些东西
            Document paper = getPaperNullable(schoolPaperId);
            if (MapUtils.isEmpty(paper)) {
                return "[校本题库试卷不存在]";
            }
            long courseId = MapUtil.getLong(paper, "courseId");
            importInfo.put("courseId", courseId);
            importInfo.put("courseName", paper.get("courseName"));
            Integer stage = commonCourseId2Stage.get(courseId);
            if (stage == null) {
                return "[课程不支持]仅支持公共课程, 当前试卷课程: " + paper.get("courseName") + "(" + courseId + ")";
            }
            importInfo.put("stage", stage);
            
            String paperTypeName = MapUtil.getTrim(importInfo, "paperTypeName");
            Integer paperType = Optional.ofNullable(stage2label2paperTypeValue)
                    .map(item -> item.get(stage))
                    .map(item -> item.get(paperTypeName))
                    .orElse(null);
            if (paperType == null) {
                return "[试卷类型]试卷类型不存在2, 当前试卷学段为: " + stage;
            }
            
            if (0 != MapUtil.getInt(paper, "creationType", -1)) {
                return "[试卷状态校验]creationType != 0";
            }
            if (1 != MapUtil.getInt(paper, "questionStatus", -1)) {
                return "[试卷状态校验]questionStatus != 1";
            }
            if (1 != MapUtil.getInt(paper, "answerStatus", -1)) {
                return "[试卷状态校验]answerStatus != 1";
            }
            
            int year = MapUtil.getInt(importInfo, "year");
            List<Long> areaIdList = MapUtil.getCast(importInfo, "areaIdList");
            
            // 已经加入到区域题库了，改写 年份 试卷类型 区域信息
            Document unionPaper = paperManager.getFirst(eq("schoolPaperId", schoolPaperId));
            if (MapUtils.isNotEmpty(unionPaper)) {
                long unionPaperId = MapUtil.getLong(unionPaper, "paperId");
                importInfo.put("unionPaperId", unionPaperId);
                int unionPaperCourseId = MapUtil.getInt(unionPaper, "courseId");
                if (unionPaperCourseId != courseId) {
                    return "[课程不一致]校本题库已经导入过区域题库，但是现在两张卷子课程不一致";
                }
                List<Bson> updateSetList = new ArrayList<>();
                int unionPaperPaperType = MapUtil.getInt(unionPaper, "paperType");
                if (unionPaperPaperType != paperType) {
                    updateSetList.add(set("paperType", paperType));
                }
                int unionPaperYear = MapUtil.getInt(unionPaper, "year");
                if (unionPaperYear != year) {
                    updateSetList.add(set("year", year));
                }
                List<Long> unionPaperAreaIdList = MapUtil.getCast(unionPaper, "areaIds");
                if (!areaIdList.equals(unionPaperAreaIdList)) {
                    updateSetList.add(set("areaIds", areaIdList));
                }
                if (CollectionUtils.isNotEmpty(updateSetList)) {
                    updateSetList.add(set("modifierId", DongniUserInfoContext.get().getUserId()));
                    updateSetList.add(set("modifierName", DongniUserInfoContext.get().getUserName()));
                    updateSetList.add(set("modifyDateTime", new Date()));
                    paperManager.updatePaper(unionPaperId, combine(updateSetList));
                    importInfo.put("successMsg", "已经导入过，更新试卷类型或年份或区域信息");
                } else {
                    importInfo.put("successMsg", "已经导入过，不需要更新信息");
                }
                return "";
            }
            
            DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
            long userId = dongniUserInfoContext.getUserId();
            String userName = dongniUserInfoContext.getUserName();
            Date now = new Date();
            long unionPaperId = getPaperId(null);
            paper.remove("_id");
            paper.remove("sourcePaperId");
            paper.remove("entrustId");
            paper.remove("cloneStatus");
            paper.remove("dropEntrustId");
            paper.remove("password");
            paper.remove("cardVersionCode");
            paper.remove("jinjuanPaperId");
            
            paper.append("paperId", unionPaperId);
            paper.append("schoolPaperId", schoolPaperId);
            paper.append("year", year);
            paper.append("paperType", paperType);
            paper.append("unionPaperType", 2);
            paper.append("areaIds", areaIdList);
            paper.append("schoolIds", new ArrayList<>());
            
            paper.append("lockStatus", 1);
            paper.append("answerCardStatus", 0);
            paper.append("ascriptionType", 0);
            paper.append("encryptStatus", 0);
            paper.append("paperStatus", 0);
            paper.append("examPaperTag", false);
            paper.append("paperTemplateTag", false);
            
            paper.append("creatorId", userId);
            paper.append("creatorName", userName);
            paper.append("createDateTime", now);
            paper.append("modifierId", userId);
            paper.append("modifierName", userName);
            paper.append("modifyDateTime", now);
            
            paperManager.insertOne(paper);
            importInfo.put("unionPaperId", unionPaperId);
            importInfo.put("successMsg", "导入成功");
            return "";
        });
        return Optional.ofNullable(errMsg).orElse("[并发操作冲突]可能有其他人正在操作该校本试卷导入到区域试卷");
    }

    /**
     * 判断试卷的状态是否是已上传答案
     * @param paperId 试卷id
     * @return true：试卷的状态是已上传答案 false：试卷的状态是未上传答案
     */
    public boolean paperHasAnswer(long paperId) {
        Document paper = paperManager.getPaperSimple(paperId);
        return ObjectUtil.isValueEquals(paper.get("answerStatus"), PaperAnswerStatus.HAS.getStatus());
    }

    /**
     * 客观题批量更新题型
     *
     * @param paperId 试卷id
     * @param updateQuestionNumbers 需要更新的questionNumber列表
     * @param newQuestionType 新的题型
     * @param newQuestionTypeName 新的题型名称
     * @param newUnitType 新的unitType
     */
    public void batchUpdateQuestionType4ObjectiveQuestion(long paperId,
                                                          List<Integer> updateQuestionNumbers,
                                                          int newQuestionType,
                                                          String newQuestionTypeName,
                                                          int newUnitType) {
        if (CollectionUtils.isEmpty(updateQuestionNumbers)) {
            return;
        }
        Set<Integer> updateQuestionNumberSet = new HashSet<>(updateQuestionNumbers);
        boolean multipleChoice = UnitTypeEnum.MultipleQn.getUnitType() == newUnitType;
        Document paper = getPaper(paperId);

        // 遍历每道客观题
        List<PaperUpdate> paperUpdateList = OwnPaperUtils.getPaperUpdateWithObjectQuestion(paper, (questionId, structure, pathPrefix) -> {
            int questionNumber = MapUtil.getInt(structure, "questionNumber");
            PaperUpdate paperUpdate = null;
            if (updateQuestionNumberSet.remove(questionNumber)) {
                paperUpdate = new PaperUpdate();
                paperUpdate.set(pathPrefix + "questionType", newQuestionType);
                paperUpdate.set(pathPrefix + "questionTypeName", newQuestionTypeName);
                paperUpdate.set(pathPrefix + "unitType", newUnitType);
                if (multipleChoice) {
                    int halfRight = (int) Double.parseDouble(structure.get("scoreValue").toString()) / 2;
                    paperUpdate.set(pathPrefix + "halfRight", halfRight);
                }
            }
            return paperUpdate;
        });

        if (CollectionUtils.isNotEmpty(updateQuestionNumberSet)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                    "试卷(paperId: " + paperId + ")要修改的阅卷题号对应的试卷题号不存在: " + updateQuestionNumberSet);
        }

        // 更新试卷
        DongniUserInfoContext userInfoContext = DongniUserInfoContext.get();
        Document update = OwnPaperUtils.processPaperUpdate(
                paperUpdateList,
                null,
                userInfoContext.getUserId(),
                userInfoContext.getUserName(),
                new Date());
        update(paperId, update);
    }

    /**
     * 客观题批量更新题号
     *
     * @param paperId 试卷id
     * @param questionNumber2NewStructureNumber 需要更新的questionNumber -> 新的structureNumber
     */
    public void batchUpdateStructureNumber4ObjectiveQuestion(long paperId,
                                                             Map<Integer, String> questionNumber2NewStructureNumber) {
        if (MapUtils.isEmpty(questionNumber2NewStructureNumber)) {
            return;
        }

        Set<Integer> updateQuestionNumberSet = new HashSet<>(questionNumber2NewStructureNumber.keySet());
        Document paper = getPaper(paperId);
        // 遍历每道客观题
        List<PaperUpdate> paperUpdateList = OwnPaperUtils.getPaperUpdateWithObjectQuestion(paper, (questionId, structure, pathPrefix) -> {
            int questionNumber = MapUtil.getInt(structure, "questionNumber");
            PaperUpdate paperUpdate = null;
            if (updateQuestionNumberSet.remove(questionNumber)) {
                paperUpdate = new PaperUpdate();
                paperUpdate.set(pathPrefix + "structureNumber", questionNumber2NewStructureNumber.get(questionNumber));
            }
            return paperUpdate;
        });

        if (CollectionUtils.isNotEmpty(updateQuestionNumberSet)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                    "试卷(paperId: " + paperId + ")要修改的阅卷题号对应的试卷题号不存在: " + updateQuestionNumberSet);
        }

        // 更新试卷
        DongniUserInfoContext userInfoContext = DongniUserInfoContext.get();
        Document update = OwnPaperUtils.processPaperUpdate(
                paperUpdateList,
                null,
                userInfoContext.getUserId(),
                userInfoContext.getUserName(),
                new Date());
        update(paperId, update);
    }

    /**
     * 客观题批量分值
     *
     * @param paperId 试卷id
     * @param updateQuestionNumbers 需要更新的questionNumber列表
     * @param newScoreValue 新的分值
     */
    public void batchUpdateScoreValue4ObjectiveQuestion(long paperId,
                                                        List<Integer> updateQuestionNumbers,
                                                        double newScoreValue) {
        if (CollectionUtils.isEmpty(updateQuestionNumbers)) {
            return;
        }
        Set<Integer> updateQuestionNumberSet = new HashSet<>(updateQuestionNumbers);
        Map<Integer, Double> questionNumber2OldScoreValue = new HashMap<>();
        int multipleQnUnitType = UnitTypeEnum.MultipleQn.getUnitType();
        Document paper = getPaper(paperId);

        final NumberFormat format = new DecimalFormat("#.00");
		format.setMaximumFractionDigits(2);
        String newScoreValueStr = format.format(newScoreValue);

        // 遍历每道客观题
        List<PaperUpdate> paperUpdateList = OwnPaperUtils.getPaperUpdateWithObjectQuestion(paper, (questionId, structure, pathPrefix) -> {
            int questionNumber = MapUtil.getInt(structure, "questionNumber");
            PaperUpdate paperUpdate = null;
            if (updateQuestionNumberSet.remove(questionNumber)) {
                paperUpdate = new PaperUpdate();
                double oldScoreValue = MapUtil.getDouble(structure, "scoreValue");
                questionNumber2OldScoreValue.put(questionNumber, oldScoreValue);

                paperUpdate.set(pathPrefix + "scoreValue", new Double(newScoreValueStr));
                // 多选题重置半对分值
                if (ObjectUtil.isValueEquals(structure.get("unitType"), multipleQnUnitType)) {
                    int halfRight = (int) Double.parseDouble(newScoreValueStr) / 2;
                    paperUpdate.set(pathPrefix + "halfRight", halfRight);
                }
            }
            return paperUpdate;
        });

        if (CollectionUtils.isNotEmpty(updateQuestionNumberSet)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                    "试卷(paperId: " + paperId + ")要修改的阅卷题号对应的试卷题号不存在: " + updateQuestionNumberSet);
        }

        // 计算新的总分
        double oldFullMark = MapUtil.getDouble(paper, "fullMark");
        double sub = 0;
        for (Double oldScoreValue : questionNumber2OldScoreValue.values()) {
            sub += newScoreValue - oldScoreValue;
        }
        double newFullMark = oldFullMark + sub;
        double formatedNewFullMark = new Double(format.format(newFullMark));

        // 更新试卷
        Document otherUpdate = new Document("fullMark", formatedNewFullMark);
        DongniUserInfoContext userInfoContext = DongniUserInfoContext.get();
        Document update = OwnPaperUtils.processPaperUpdate(
                paperUpdateList,
                otherUpdate,
                userInfoContext.getUserId(),
                userInfoContext.getUserName(),
                new Date());
        update(paperId, update);

        // 重新计算courseFullMark
        List<Map<String, Object>> newCourseFullMark = computeCourseFullMark(getPaper(paperId));
        if (CollectionUtils.isNotEmpty(newCourseFullMark)) {
            update(paperId, set("courseFullMark", newCourseFullMark));
        }
    }

    /**
     * 客观题批量选项个数
     *
     * @param paperId 试卷id
     * @param questionNumber2OptionsCount 需要更新的questionNumber -> 新的选项个数
     */
    public void batchUpdateOptionsCount4ObjectiveQuestion(long paperId,
                                                          Map<Integer, Integer> questionNumber2OptionsCount) {
        if (MapUtils.isEmpty(questionNumber2OptionsCount)) {
            return;
        }
        // 处理关联的父子试卷 - 只处理没有未录题的
        List<Document> allPaperList = ownUpdateAnswerService.getPaperListForUpdateQuestionAnswer(paperId);
        allPaperList = allPaperList.stream()
                .filter(i -> !ObjectUtil.isValueEquals(i.get("paperId"), paperId))
                .filter(i -> !DictUtil.isEquals(MapUtil.getIntNullable(i, "questionStatus"), "questionStatus", "used"))
                .collect(toList());
        // 处理当前试卷
        Document paper = getPaper(paperId);
        allPaperList.add(paper);

        Map<Long, List<PaperUpdate>> updateMap = new HashMap<>(allPaperList.size());

        for (Document currentPaper : allPaperList) {
            Set<Integer> optionsCountUpdateQuestionNumberSet = new HashSet<>(questionNumber2OptionsCount.keySet());
            List<PaperUpdate> paperUpdateList = OwnPaperUtils.getPaperUpdateWithObjectQuestion(paper, (questionId, structure, pathPrefix) -> {
                int questionNumber = MapUtil.getInt(structure, "questionNumber");
                PaperUpdate paperUpdate = null;
                if (optionsCountUpdateQuestionNumberSet.remove(questionNumber)) {
                    paperUpdate = new PaperUpdate();
                    paperUpdate.set(pathPrefix + "optionsCount", questionNumber2OptionsCount.get(questionNumber));
                }
                return paperUpdate;
            });
            Long currentPaperId = currentPaper.getLong("paperId");
            if (CollectionUtils.isNotEmpty(optionsCountUpdateQuestionNumberSet)) {
                throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                        "试卷(paperId: " + currentPaperId + ")要修改的阅卷题号对应的试卷题号不存在: " + optionsCountUpdateQuestionNumberSet);
            }
            updateMap.put(currentPaperId, paperUpdateList);
        }

        // 更新试卷
        DongniUserInfoContext userInfoContext = DongniUserInfoContext.get();
        Long userId = userInfoContext.getUserId();
        String userName = userInfoContext.getUserName();
        Date date = new Date();
        for (Map.Entry<Long, List<PaperUpdate>> entry : updateMap.entrySet()) {
            Document update = OwnPaperUtils.processPaperUpdate(
                    entry.getValue(),
                    null,
                    userId,
                    userName,
                    date);
            update(entry.getKey(), update);
        }
    }

    /**
     * 客观题批量更新半对分值
     *
     * @param paperId 试卷id
     * @param questionNumber2NewHalfRight 需要更新的questionNumber -> 新的半对分值
     */
    public void batchUpdateHalfRight4ObjectiveQuestion(long paperId,
                                                       Map<Integer, Double> questionNumber2NewHalfRight) {
        if (MapUtils.isEmpty(questionNumber2NewHalfRight)) {
            return;
        }

        int multipleQnUnitType = UnitTypeEnum.MultipleQn.getUnitType();
        Set<Integer> updateQuestionNumberSet = new HashSet<>(questionNumber2NewHalfRight.keySet());
        Document paper = getPaper(paperId);
        // 遍历每道客观题
        List<PaperUpdate> paperUpdateList = OwnPaperUtils.getPaperUpdateWithObjectQuestion(paper, (questionId, structure, pathPrefix) -> {
            int questionNumber = MapUtil.getInt(structure, "questionNumber");
            PaperUpdate paperUpdate = null;
            if (updateQuestionNumberSet.remove(questionNumber)) {
                int unitType = MapUtil.getInt(structure, "unitType");
                String structureNumber = MapUtil.getString(structure, "structureNumber");
                if (unitType != multipleQnUnitType) {
                    throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                            "试卷(paperId: " + paperId + ")题号" + structureNumber + "非多选题无法设置半对分值");
                }

                double scoreValue = MapUtil.getDouble(structure, "scoreValue");
                Double halfRight = questionNumber2NewHalfRight.get(questionNumber);
                if (halfRight > scoreValue) {
                    throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                            "试卷(paperId: " + paperId + ")题号" + structureNumber + "要设置的半对分值大于试题分值");
                }

                paperUpdate = new PaperUpdate();
                paperUpdate.set(pathPrefix + "halfRight", halfRight);
            }
            return paperUpdate;
        });

        if (CollectionUtils.isNotEmpty(updateQuestionNumberSet)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                    "试卷(paperId: " + paperId + ")要修改的阅卷题号对应的试卷题号不存在: " + updateQuestionNumberSet);
        }

        // 更新试卷
        DongniUserInfoContext userInfoContext = DongniUserInfoContext.get();
        Document update = OwnPaperUtils.processPaperUpdate(
                paperUpdateList,
                null,
                userInfoContext.getUserId(),
                userInfoContext.getUserName(),
                new Date());
        update(paperId, update);
    }
}
