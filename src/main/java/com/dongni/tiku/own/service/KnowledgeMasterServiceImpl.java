package com.dongni.tiku.own.service;

import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import com.dongni.common.auth.DongniClient;
import com.dongni.common.distribution.service.IMasterLocalExecuteService;
import com.dongni.common.master.version.service.BaseMasterThirdService;
import com.dongni.common.utils.MongoCourseUtil;
import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.KnowledgeMasterManager;
import com.dongni.tiku.own.util.MongoDataConvertUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * 2023/03/10
 * 生成和提供知识点母版的地方
 * 常见于使用第三方知识点体系，而第三方知识点体系又不提供唯一的_id
 *     一起作业网的知识点不在此处，因为一起的_id这边直接用了
 *     菁优的知识点的小学语数英在此处同步，并写入knowledgeMaster，所有系统都从这获取，并存入knowledge
 */
@Service
public class KnowledgeMasterServiceImpl implements IMasterLocalExecuteService {
    
    private static final Logger log = LoggerFactory.getLogger(KnowledgeMasterServiceImpl.class);
    
    @Autowired
    private KnowledgeMasterThirdFactory knowledgeMasterThirdFactory;
    @Autowired
    private KnowledgeMasterManager knowledgeMasterManager;
    @Autowired
    private TikuBaseDataVersionService tikuBaseDataVersionService;
    @Autowired
    private CourseServiceImpl courseService;
    @Autowired
    protected DongniClient dongniClient;
    
    public static String getDataVersionBusinessType() {
        return "knowledge";
    }
    public static String getDataVersionBusinessKey(long courseId) {
        return String.valueOf(courseId);
    }
    public static String getDataVersionBusinessDesc(long courseId) {
        return "知识点-courseId:" + courseId;
    }
    
    /**
     * 获取课程的知识点信息
     * @param params courseId 课程id
     * @return 知识点信息
     */
    public List<Map<String, Object>> getKnowledgeMasterList(Map<String, Object> params) {
        Verify.of(params).isValidId("courseId").verify();
        return get(
                () -> getKnowledgeMasterListRemote(params),
                () -> getKnowledgeMasterListLocal(params)
        );
    }
    
    /**
     * 获取课程的知识点信息 远程
     * @param params courseId 课程id
     * @return 知识点信息
     */
    private List<Map<String, Object>> getKnowledgeMasterListRemote(Map<String, Object> params) {
        params.put("xkwVersion20240614", true);
        String url = getUrl("/tiku/knowledge/master/course/list/get/dongni/9b1699c3-6062-4147-8149-ebeb0e39e711/system");
        return dongniClient.post(url, null, params);
    }
    
    /**
     * 获取课程的知识点信息 本地
     * @param params courseId 课程id
     * @return 知识点信息
     */
    private List<Map<String, Object>> getKnowledgeMasterListLocal(Map<String, Object> params) {
        long courseId = MapUtil.getLong(params, "courseId");
        return tikuBaseDataVersionService.executeMasterWithVersionCheck(
                getDataVersionBusinessType(),
                getDataVersionBusinessKey(courseId),
                () -> knowledgeMasterManager.getListMap(courseId)
        );
    }
    
    /**
     * 从第三方同步知识点母版信息
     */
    public void syncKnowledgeAll() {
        // 仅在主平台执行
        if (isMaster()) {
            for (long courseId : knowledgeMasterThirdFactory.getKeySet()) {
                try {
                    syncKnowledgeByCourseId(MapUtil.of(
                            "userId", 2L,
                            "userName", "系统:题库管理员",
                            "userType", 7,
                            "courseId", courseId
                    ));
                } catch (CommonException e) {
                    log.warn("知识点同步失败: courseId: {}; cause: {}", courseId, e.getMessage());
                } catch (Exception e) {
                    log.warn("知识点同步失败: courseId: {}; cause: {}", courseId, e.getMessage(), e);
                }
            }
        }
    }
    
    /**
     * 同步课程的知识点母版信息
     * @param params userId userName userType
     *               courseId 必须在syncCourseIdSet的范围内，会分发到各个平台进行处理
     */
    public void syncKnowledgeByCourseId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("userId")
                .isNotBlank("userName")
                .isInteger("userType")
                .verify();
        long courseId = MapUtil.getLong(params, "courseId");
        log.info("{} 开始: {}: {}", knowledgeMasterThirdFactory.getBusinessName(), knowledgeMasterThirdFactory.getKeyDesc(), courseId);
        Map<String, Object> courseInfo = courseService.getCourseDetailMustExist(params);
        BaseKnowledgeMasterThirdService knowledgeMasterThirdService = knowledgeMasterThirdFactory.getService(courseId);
        boolean continuousUpdate = knowledgeMasterThirdService.continuousUpdate(courseId);
        knowledgeByCourseMustBeNotExists(courseId, continuousUpdate);
        String knowledgeMasterLock = JedisUtil.getKey("knowledgeMasterLock", String.valueOf(courseId));
        JedisTemplate.lockExecute(knowledgeMasterLock, 0, () -> {
            String knowledgeMasterDataVersionBusinessType = getDataVersionBusinessType();
            String knowledgeMasterDataVersionBusinessKey = getDataVersionBusinessKey(courseId);
            String knowledgeMasterDataVersionBusinessDesc = getDataVersionBusinessDesc(courseId);
            tikuBaseDataVersionService.masterUpdating(knowledgeMasterDataVersionBusinessType, knowledgeMasterDataVersionBusinessKey, knowledgeMasterDataVersionBusinessDesc);
            String knowledgeMappingDataVersionBusinessType = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessType();
            String knowledgeMappingDataVersionBusinessKey = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessKey(courseId);
            String knowledgeMappingDataVersionBusinessDesc = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessDesc(courseId);
            tikuBaseDataVersionService.masterUpdating(knowledgeMappingDataVersionBusinessType, knowledgeMappingDataVersionBusinessKey, knowledgeMappingDataVersionBusinessDesc);
            knowledgeByCourseMustBeNotExists(courseId, continuousUpdate);
            
            List<Document> oldKnowledgeList = knowledgeMasterManager.getList(courseId);
            // 判断现有的母版文件是否来自需要的平台  如现在是thirdKeyValuePrefix=xkw-,而之前是jyeoo-,则需要删除该课程的所有知识点母版
            String thirdKeyValuePrefix = knowledgeMasterThirdService.thirdKeyValuePrefix;
            boolean thirdKeyMatch = oldKnowledgeList.stream()
                    .map(item -> MapUtil.getTrim(item, BaseMasterThirdService.THIRD_KEY_FIELD))
                    .anyMatch(thirdKey -> thirdKey.startsWith(thirdKeyValuePrefix));
            if (!thirdKeyMatch) {
                // 都不是当前平台的 需要删除掉
                knowledgeMasterManager.deleteMany(eq("courseId", courseId));
                oldKnowledgeList = new ArrayList<>();
            }
            Map<String, Document> thirdKey2OldKnowledge = oldKnowledgeList.stream()
                    .collect(toMap(item -> MapUtil.getTrim(item, BaseMasterThirdService.THIRD_KEY_FIELD), item -> item));
            
            List<Map<String, Object>> knowledgeTreeList = knowledgeMasterThirdService.getKnowledgeTreeList(courseInfo);
            
            MongoCourseUtil.CourseOperate courseOperate = new MongoCourseUtil.CourseOperate(
                    MapUtil.getLong(params, "userId"),
                    MapUtil.getTrim(params, "userName"),
                    MapUtil.getInt(courseInfo, "stage"),
                    MapUtil.getLong(courseInfo, "courseId"),
                    MapUtil.getString(courseInfo, "courseName")
            );
            
            String treeCode = String.valueOf(courseId);
            LongAdder knowledgeMasterInsertCountAdder = new LongAdder();
            LongAdder knowledgeMasterModifyCountAdder = new LongAdder();
            LongAdder knowledgeMappingMasterInsertCountAdder = new LongAdder();
            LongAdder knowledgeMappingMasterModifyCountAdder = new LongAdder();
            saveKnowledge(
                    knowledgeMasterThirdService,
                    knowledgeMasterInsertCountAdder, knowledgeMasterModifyCountAdder,
                    knowledgeMappingMasterInsertCountAdder, knowledgeMappingMasterModifyCountAdder,
                    courseOperate, knowledgeTreeList, thirdKey2OldKnowledge,
                    treeCode, null, new HashMap<>(), 1
            );
            long knowledgeMasterInsertCount= knowledgeMasterInsertCountAdder.sum();
            long knowledgeMasterModifyCount = knowledgeMasterModifyCountAdder.sum();
            long knowledgeMasterDeleteCount = deleteKnowledge(
                    knowledgeMasterThirdService,
                    knowledgeMappingMasterInsertCountAdder, knowledgeMappingMasterModifyCountAdder,
                    courseOperate, thirdKey2OldKnowledge
            );
            
            boolean knowledgeMasterVersionIncrement = knowledgeMasterInsertCount > 0
                    || knowledgeMasterModifyCount > 0
                    || knowledgeMasterDeleteCount > 0;
            tikuBaseDataVersionService.masterUpdated(
                    knowledgeMasterDataVersionBusinessType,
                    knowledgeMasterDataVersionBusinessKey,
                    knowledgeMasterVersionIncrement
            );
            
            long knowledgeMappingMasterInsertCount = knowledgeMappingMasterInsertCountAdder.sum();
            long knowledgeMappingMasterModifyCount = knowledgeMappingMasterModifyCountAdder.sum();
            boolean knowledgeMappingVersionIncrement = knowledgeMappingMasterInsertCount > 0
                    || knowledgeMappingMasterModifyCount > 0;
            tikuBaseDataVersionService.masterUpdated(
                    knowledgeMappingDataVersionBusinessType,
                    knowledgeMappingDataVersionBusinessKey,
                    knowledgeMappingVersionIncrement
            );
            log.info("{} 结束: {}: {};" +
                            " 知识点新增: {}; 知识点更新: {}; 知识点删除: {}; " +
                            " 知识点映射新增: {}; 知识点映射更新: {}; ",
                    knowledgeMasterThirdFactory.getBusinessName(), knowledgeMasterThirdFactory.getKeyDesc(), courseId,
                    knowledgeMasterInsertCount, knowledgeMasterModifyCount, knowledgeMasterDeleteCount,
                    knowledgeMappingMasterInsertCount, knowledgeMappingMasterModifyCount
            );
            return true;
        });
    }
    
    
    /**
     * 课程的知识点必须不存在
     * @param courseId 课程id
     * @param continuousUpdate 是否持续更新 true时不检查直接返回
     */
    private void knowledgeByCourseMustBeNotExists(long courseId, boolean continuousUpdate) {
        if (continuousUpdate) { return; }
        // 如果 courseId 的知识点已经存在，则不进行初始化，需要人为介入
        if (knowledgeMasterManager.exist(courseId)) {
            tikuBaseDataVersionService.masterUpdated(
                    getDataVersionBusinessType(),
                    getDataVersionBusinessKey(courseId),
                    false
            );
            tikuBaseDataVersionService.masterUpdated(
                    KnowledgeMappingMasterServiceImpl.getDataVersionBusinessType(),
                    KnowledgeMappingMasterServiceImpl.getDataVersionBusinessKey(courseId),
                    false
            );
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "课程已经有初始化的知识点了: " + courseId);
        }
    }
    
    private static final Set<String> NOT_CHECK_KNOWLEDGE_KEY_SET = Stream.of(
            "_id",
            "creatorId", "creatorName", "createDateTime",
            "modifierId", "modifierName", "modifyDateTime"
    ).collect(Collectors.toSet());
    
    /**
     * 递归插入知识点
     * @param courseOperate      课程信息及操作信息
     * @param knowledgeList      knowledgeName "${thirdKeyName}" Children others
     * @param thirdKey2OldKnowledge thirdKey -> 已经在数据库中的数据
     * @param parentTreeCode     treeCode  "22" "22.0" "22.3.1" 对于每一层，都会在父级的code加.${index}
     * @param parentId           父级的_id
     * @param deep2count         深度的知识点数量 用来算sort 反正怪怪的
     * @param deep               当前深度 第几层
     */
    private void saveKnowledge(final BaseKnowledgeMasterThirdService knowledgeMasterThirdService,
                               final LongAdder knowledgeMasterInsertCountAdder,
                               final LongAdder knowledgeMasterModifyCountAdder,
                               final LongAdder knowledgeMappingMasterInsertCountAdder,
                               final LongAdder knowledgeMappingMasterModifyCountAdder,
                               final MongoCourseUtil.CourseOperate courseOperate,
                               final List<Map<String, Object>> knowledgeList,
                               final Map<String, Document> thirdKey2OldKnowledge,
                               final String parentTreeCode,
                               final String parentId,
                               final Map<Integer, Integer> deep2count,
                               final int deep) {
        if (CollectionUtils.isEmpty(knowledgeList)) {
            return;
        }
        
        for (int i = 0, iLen = knowledgeList.size(); i < iLen; i++) {
            Map<String, Object> knowledge = knowledgeList.get(i);
            List<Map<String, Object>> children = MapUtil.getListMap(knowledge, "children");
            // 没有孩子节点就是叶子节点了
            boolean leaf = CollectionUtils.isEmpty(children);
            // 好像没有用到，该字段很奇怪，好像是按照深度的维度单独计算的
            int sort = deep2count.merge(deep, 1, Integer::sum);
            // treeCode index 检索用
            String treeCode = parentTreeCode + "." + i;
    
            String thirdKey = MapUtil.getTrim(knowledge, BaseMasterThirdService.THIRD_KEY_FIELD);
            Document oldKnowledge = thirdKey2OldKnowledge.remove(thirdKey);
            boolean exist = MapUtils.isNotEmpty(oldKnowledge);
            ObjectId objectId = MongoUtil.getMongoId(oldKnowledge);
            
            long userId = Optional.ofNullable(oldKnowledge).map(item -> MapUtil.getLong(item, "creatorId")).orElse(courseOperate.userId);
            String userName = Optional.ofNullable(oldKnowledge).map(item -> MapUtil.getString(item, "creatorName")).orElse(courseOperate.userName);
            String createDateTime = Optional.ofNullable(oldKnowledge).map(item -> MapUtil.getString(item, "createDateTime")).orElse(courseOperate.currentDateTime.toString());
            
            knowledge.put("_id", objectId);
            knowledge.put("stage", courseOperate.stage);
            knowledge.put("courseId", courseOperate.courseId);
            knowledge.put("courseName", courseOperate.courseName);
            knowledge.put("parentId", parentId);
            knowledge.put("deep", deep);
            knowledge.put("sort", sort);
            knowledge.put("leaf", leaf);
            knowledge.put("treeCode", treeCode);
            knowledge.put("creatorId", userId);
            knowledge.put("creatorName", userName);
            knowledge.put("createDateTime", createDateTime);
            knowledge.put("modifierId", courseOperate.userId);
            knowledge.put("modifierName", courseOperate.userName);
            knowledge.put("modifyDateTime", courseOperate.currentDateTime);
    
            Document newKnowledge = convertDocumentKnowledge(knowledge);
            if (exist) {
                boolean modify = newKnowledge.keySet().stream()
                        .filter(key -> !NOT_CHECK_KNOWLEDGE_KEY_SET.contains(key))
                        .anyMatch(key -> {
                            Object newValue = newKnowledge.get(key);
                            Object oldValue = oldKnowledge.get(key);
                            if (!Objects.equals(newValue, oldValue)) { return true; }
                            if (newValue == null) { return false; }
                            return newValue.getClass() != Optional.ofNullable(oldValue).map(Object::getClass).orElse(null);
                        });
                if (modify) {
                    knowledgeMasterModifyCountAdder.increment();
                    knowledgeMasterManager.replace(newKnowledge);
                }
            } else {
                knowledgeMasterInsertCountAdder.increment();
                knowledgeMasterManager.insertOne(newKnowledge);
            }
            
            knowledgeMasterThirdService.saveKnowledgeMapping(
                    knowledgeMappingMasterInsertCountAdder,
                    knowledgeMappingMasterModifyCountAdder,
                    newKnowledge
            );
            
            String id = MapUtil.getString(newKnowledge, "_id");
            saveKnowledge(
                    knowledgeMasterThirdService,
                    knowledgeMasterInsertCountAdder, knowledgeMasterModifyCountAdder,
                    knowledgeMappingMasterInsertCountAdder, knowledgeMappingMasterModifyCountAdder,
                    courseOperate, children, thirdKey2OldKnowledge,
                    treeCode, id, deep2count, deep + 1
            );
        }
    }
    
    /**
     * 删除知识点 将知识点标志为删除
     * @param courseOperate         操作信息
     * @param thirdKey2OldKnowledge 剩下没有出现在接口获取的数据
     * @return deleteCount
     */
    private int deleteKnowledge(BaseKnowledgeMasterThirdService knowledgeMasterThirdService,
                                LongAdder knowledgeMappingMasterInsertCountAdder,
                                LongAdder knowledgeMappingMasterModifyCountAdder,
                                final MongoCourseUtil.CourseOperate courseOperate,
                                Map<String, Document> thirdKey2OldKnowledge) {
        int deleteCount = 0;
        for (Document deleteExistKnowledge : thirdKey2OldKnowledge.values()) {
            if (!MapUtil.getBoolean(deleteExistKnowledge, "deleted")) {
                ObjectId objectId = MongoUtil.getMongoId(deleteExistKnowledge);
                knowledgeMasterManager.update(objectId, combine(
                        set("deleted", true),
                        set("modifierId", courseOperate.userId),
                        set("modifierName", courseOperate.userName),
                        set("modifyDateTime", courseOperate.currentDateTime)
                ));
                deleteCount++;
            }
            knowledgeMasterThirdService.saveKnowledgeMapping(
                    knowledgeMappingMasterInsertCountAdder,
                    knowledgeMappingMasterModifyCountAdder,
                    deleteExistKnowledge
            );
        }
        return deleteCount;
    }
    
    /**
     * 转换存储文档 知识点
     *   字段类型转换 字段顺序
     * @param oldDoc 文档数据
     * @return 转换后的文档数据
     */
    public Document convertDocumentKnowledge(Map<String, Object> oldDoc) {
        Document newDoc = new Document();
        MongoDataConvertUtil.setIfNewValuePresent(newDoc, oldDoc, "_id", MongoUtil::getMongoId);
        MongoDataConvertUtil.set(newDoc, oldDoc, "stage", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "courseName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "knowledgeName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "parentId", MapUtil::getStringNullable);
        MongoDataConvertUtil.set(newDoc, oldDoc, "deep", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "sort", MapUtil::getInt);
        MongoDataConvertUtil.set(newDoc, oldDoc, "leaf", MapUtil::getBoolean);
        MongoDataConvertUtil.set(newDoc, oldDoc, "treeCode", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "deleted", MapUtil::getBoolean);
        MongoDataConvertUtil.set(newDoc, oldDoc, BaseMasterThirdService.THIRD_KEY_FIELD, MapUtil::getString);
        knowledgeMasterThirdFactory.getDistributionServiceList().forEach(service -> service.convertDocumentKnowledge(newDoc, oldDoc));
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "creatorName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "createDateTime", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierId", MapUtil::getLong);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifierName", MapUtil::getString);
        MongoDataConvertUtil.set(newDoc, oldDoc, "modifyDateTime", MapUtil::getString);
        return newDoc;
    }
}
