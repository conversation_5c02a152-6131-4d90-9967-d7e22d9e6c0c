package com.dongni.tiku.own.service;

import com.dongni.tiku.manager.impl.KnowledgeMappingMissManager;
import com.dongni.tiku.own.enumeration.KnowledgeMappingType;
import com.mongodb.client.model.UpdateOptions;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.addToSet;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 *
 * <AUTHOR>
 * 2023/04/11
 */
@Service
public class KnowledgeMappingMissService {

    @Autowired
    private KnowledgeMappingMissManager knowledgeMappingMissManager;
    
    /**
     * 保存映射不到的知识点
     * @param courseId          课程id
     * @param fromType          知识点来源平台
     * @param fromKnowledgeId   知识点id
     * @param fromKnowledgeName 知识点名称 nullable
     * @param questionId        试题id    nullable
     */
    void saveKnowledgeMappingMiss(long courseId, KnowledgeMappingType fromType,
                                  String fromKnowledgeId, String fromKnowledgeName,
                                  String questionId) {
        Bson query = and(
                eq("courseId", courseId),
                eq("fromType", fromType.getType()),
                eq("fromKey", fromKnowledgeId)
        );
        List<Bson> updateList = new ArrayList<>();
        updateList.add(set("courseId", courseId));
        updateList.add(set("fromType", fromType.getType()));
        updateList.add(set("fromKey", fromKnowledgeId));
        if (fromKnowledgeName != null) {
            updateList.add(addToSet("fromNameList", fromKnowledgeName));
        }
        if (questionId != null) {
            updateList.add(addToSet("questionIdList", questionId));
        }
        knowledgeMappingMissManager.updateOne(query, combine(updateList), new UpdateOptions().upsert(true));
    }
    
}
