package com.dongni.tiku.part.time.review.controller;

import com.dongni.common.entity.Response;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.part.time.review.bean.params.*;
import com.dongni.tiku.part.time.review.service.IQueryService;
import com.dongni.tiku.part.time.review.trigger.Context;
import com.dongni.tiku.part.time.review.trigger.Trigger;
import com.dongni.tiku.part.time.review.trigger.event.Event;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/11/10 下午 02:21
 * @Version 1.0.0
 */
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/part-time/review")
public class PartTimeReviewTaskController {
    @Autowired
    private IQueryService queryService;

    @Autowired
    private Trigger trigger;

    @PostMapping("/assign")
    public Response assign(@Validated AssignTaskParam assignTaskParam) {
        Context context = new Context();
        context.setCourseId(assignTaskParam.getCourseId());
        context.setTaskType(assignTaskParam.getTaskType());
        context.setPartTimeReviewerId(assignTaskParam.getPartTimeReviewerId());
        context.setRelativeTaskId(assignTaskParam.getRelativeTaskId());

        trigger.fire(Event.ASSIGN, context);
        return new Response();
    }

    @PostMapping("/reAssign")
    public Response reAssign(@Validated AssignTaskParam assignTaskParam) {
        Context context = new Context();
        context.setCourseId(assignTaskParam.getCourseId());
        context.setTaskType(assignTaskParam.getTaskType());
        context.setPartTimeReviewerId(assignTaskParam.getPartTimeReviewerId());
        context.setRelativeTaskId(assignTaskParam.getRelativeTaskId());

        trigger.fire(Event.RE_ASSIGN, context);
        return new Response();
    }

    @GetMapping("/pool")
    public Response pool(@Validated PoolParam poolParam) {
        return new Response(queryService.getPoolInfo(poolParam));
    }

    @GetMapping("/receive")
    public Response receive(@Validated ReceiveTaskParam receiveTaskParam) {
        Context context = new Context();
        context.setTaskType(receiveTaskParam.getTaskType());
        context.setCourseId(receiveTaskParam.getCourseId());
        context.put("receiveNumber", receiveTaskParam.getReceiveNumber());
        context.setPartTimeReviewerId(receiveTaskParam.getPartTimeReviewerId());

        trigger.fire(Event.RECEIVE, context);
        return new Response();
    }

    @GetMapping("/task/list")
    public Response taskList(@Validated TaskListParam taskListParam) {
        return new Response(queryService.taskList(taskListParam));
    }

    @GetMapping("task/export")
    public Response taskExport(@Validated TaskListParam taskListParam) {
        return new Response(queryService.taskExport(taskListParam));
    }

    @GetMapping("/statistics")
    public Response statistics(@Validated StatisticsParam statisticsParam) {
        return new Response(queryService.statisticsList(statisticsParam));
    }
}
