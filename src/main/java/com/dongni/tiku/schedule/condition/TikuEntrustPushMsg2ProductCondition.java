package com.dongni.tiku.schedule.condition;

import com.dongni.common.wechat.utils.WeChatTypeEnumeration;
import com.dongni.common.wechat.utils.WeChatUtils;
import com.dongni.commons.utils.verify.ObjectUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2019/12/05 <br/>
 *
 */
public class TikuEntrustPushMsg2ProductCondition implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        Map<String, String> weChatConfig = WeChatUtils.getWeChatConfig(WeChatTypeEnumeration.DONG_NI.getValue());
        String entrustWarningTemplateId = weChatConfig.get("entrustWarningTemplateId");
        return !StringUtils.isBlank(entrustWarningTemplateId)
                && !ObjectUtil.isValueEquals(entrustWarningTemplateId, "@entrust_warning_notice_template_id@");
    }
}
