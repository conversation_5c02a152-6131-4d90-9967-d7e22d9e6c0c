package com.dongni.tiku.schedule;

import com.dongni.commons.lock.DistributeLock;
import com.dongni.tiku.own.service.UnionPaperEntrustService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/5/30.
 *
 * 联考试卷的委托分配
 */
@Service
public class UnionPaperEntrustAssignSchedule {

    // 日志记录
    private final static Logger log = LoggerFactory.getLogger(UnionPaperEntrustAssignSchedule.class);

    @Autowired
    private UnionPaperEntrustService unionPaperEntrustService;

    /**
     * 把联考的试卷自动分配给对应联考组的一所学校
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    @DistributeLock(moduleName = "TIKU", name = "UNION_PAPER_ENTRUST_ASSIGN", expireTime = -1, waitTime = 0)
    public void assign(){
        Map<String,Object> params = new HashMap<>();
        params.put("userId",1);
        params.put("userName","admin");
        int sum = unionPaperEntrustService.assign(params);
        log.info("定时更新，把联考的试卷自动分配给对应联考组的一所学校，完成{}条",sum);
    }

}
