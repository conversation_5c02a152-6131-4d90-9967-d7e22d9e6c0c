/**
 * <AUTHOR> <br/>
 * @date 2021/08/02 <br/>
 *  试题结构
 */
package com.dongni.tiku.mongo;

/*
{
  _id              ObjectId       mongo主键
  yiqiId           String         一起作业网的试题id 如果该题与一起作业网有关，则会存在值
                                  从一起题库组卷存入/懂你录题后交一起标注知识点
  source           String         来源
  structureNumber  String         试题用于展示的题号???
  
  ascriptionType   Int            归属 0公共 1学校私有 私有的仅schoolIds中的学校可见
  schoolIds        List<Long>     试题归属为学校私有时的归属学校
  userIds          List<Long>     收藏题目
  
  areaId           Long           区域id
  gradeType        Int            年级
  courseId         Long           课程id
  courseName       String         课程名称
  questionType     Int            试题类型
  questionTypeName String         试题类型
  unitType         Int            好像是识别那边用的，指示了该题应该如何识别
  questionStatus   Int            1used 表示已经被组卷使用过了 不能删除
  
  answerCount      Int            作答次数
  usedCount        Int            使用次数 被组一次卷子记一次，试卷删除会减掉
  difficulty       Double         试题难度，即得分率，由examList计算得出，考试越多越精准
  examList         List<Document> 考试信息，用于计算难度
    {
      difficulty          String(Double) 难度 该场考试的得分率
      examId              Long           考试id
      participationNumber String(Int)    参加人数
    }
  
  knowledgeList    List<Document> 试题知识点(一起作业网) 题干上可能没有知识点，都在小题里面
    {
        _id  String 一起作业网知识点id
        name String 知识点名称
    }
  knowledgeIdList  List<String>   试题知识点idList(一起作业网) 来源与题干及小题的知识点id汇总
  
  search           String         查询字段，由小题desc及题干stem拼接出来的
  stem             String         试题大题描述
  questions: [{}]  List<Document> 试题小题结构
    {
        unitType  Int                好像是识别那边用的，指示了该题应该如何识别
        answer    String             答案 基本上来说都是客观题答案，主观题的答案大部分是在解析里面
        desc      String             小题描述
        explain   String             试题详解
        options   List<String>       客观题的试题选项
        questions List<Document>     小题的小题 理论上这一层没了
        score     Int                分数 理论上来说，试题上不应该有分数
        knowledgeList List<Document> 试题知识点(一起作业网)
           {
              _id  String 一起作业网知识点id
              name String 知识点名称
           }
    }
  
  creatorId        Long           创建信息
  creatorName      String         创建信息
  createDateTime   Long           创建信息
  modifierId       Long           修改信息
  modifierName     String         修改信息
  modifyDateTime   Date           修改信息
}

*/










