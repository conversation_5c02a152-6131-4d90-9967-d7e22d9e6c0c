package com.dongni.tiku.render.service;


import com.dongni.common.mongo.IManager;
import com.dongni.tiku.render.bean.dto.IReceiveDTO;
import com.dongni.tiku.render.bean.param.IAckParam;
import com.dongni.tiku.render.bean.param.ICreateParam;


/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/6/5 周四 下午 04:29
 * @Version 1.0.0
 */
public interface ITikuRenderQuestionTaskService<T extends ICreateParam<?>, R extends IReceiveDTO<?>, X extends IAckParam<?>> {
    // ------------------------任务流程所需要的方法------------------------
    void createTask(T param);

    R receiveTask();

    boolean ackTask(X param);

    void rePendingProcessTimeoutTask();


    // ------------------------辅助方法------------------------
    String getIdFieldName();

    IManager getManager();

    R newReceiveDTO();
}
