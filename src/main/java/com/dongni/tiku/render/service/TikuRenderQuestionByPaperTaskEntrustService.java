package com.dongni.tiku.render.service;

import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.entrust.util.EntrustUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/04/22
 */
@Service
public class TikuRenderQuestionByPaperTaskEntrustService {

    @Autowired
    private TikuRenderQuestionByPaperTaskService tikuRenderQuestionByPaperTaskService;
    
    /**
     * 注册创建任务
     * @param entrustInfo  entrustId           委托id
     *                     entrustBusinessType 业务类型 校本课程才需要生成
     *                     answerPaperId or paperPaperId 试卷id 一般来说这个委托信息是提前获取的，
     *                                                   获取的时候可能answerPaperId是空的(直接上传文件委托)
     *                                                   但是业务处理到已完成之后，两个值会变成相同的
     */
    public void registerSynchronizationToCreateRenderQuestionByPaperTask(Map<String, Object> entrustInfo) {
        Verify.of(entrustInfo)
                .isValidId("entrustId")
                .isInteger("entrustBusinessType")
                .isValidId("courseId")
                .verify();
        if (!EntrustUtil.isSchoolBusiness(entrustInfo)) {
            return;   // 校本课程才需要生成
        }
        long entrustId = MapUtil.getLong(entrustInfo, "entrustId");
        String taskSource = "委托(" + entrustId + ")录题进入已完成";
        long paperId = MapUtil.getLong(entrustInfo, "answerPaperId", MapUtil.getLong(entrustInfo, "paperPaperId"));
        tikuRenderQuestionByPaperTaskService.registerSynchronizationToCreateRenderQuestionByPaperTask(paperId, taskSource);
    }
    
}
