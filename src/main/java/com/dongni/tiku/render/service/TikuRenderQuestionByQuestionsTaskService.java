package com.dongni.tiku.render.service;

import com.dongni.common.mongo.IManager;
import com.dongni.common.mongo.Order;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.utils.verify.Verify2;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.RenderQuestionByQuestionTaskManager;
import com.dongni.tiku.manager.impl.RenderQuestionRecordManager;
import com.dongni.tiku.render.bean.param.TikuRenderQuestionByQuestionTaskCreateParam;
import com.dongni.tiku.render.bean.dto.TikuRenderQuestionByQuestionTaskReceiveDTO;
import com.dongni.tiku.render.bean.entity.RenderQuestionInfoEntity;
import com.dongni.tiku.render.bean.entity.RenderQuestionRecordEntity;
import com.dongni.tiku.render.bean.param.TikuRenderQuestionByQuestionTaskAckParam;
import com.dongni.tiku.wrong.book.service.RecommendationSimilarityService;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.*;

/**
 * copy from {@link TikuRenderQuestionByPaperTaskService}
 *
 * 会将待渲染的试题记录在mongo.renderQuestionRecord
 *
 * <AUTHOR>
 * @Date 2025/6/4 周三 下午 04:32
 * @Version 1.0.0
 */
@Service
public class TikuRenderQuestionByQuestionsTaskService extends AbstractTikuRenderQuestionTaskService
        <TikuRenderQuestionByQuestionTaskCreateParam,
        TikuRenderQuestionByQuestionTaskReceiveDTO,
        TikuRenderQuestionByQuestionTaskAckParam> {
    private static final Logger log = LoggerFactory.getLogger(TikuRenderQuestionByQuestionsTaskService.class);

    @Autowired
    private TikuRenderQuestionByPaperTaskService tikuRenderQuestionByPaperTaskService;
    @Autowired
    private RenderQuestionRecordManager renderQuestionRecordManager;
    @Autowired
    private RenderQuestionByQuestionTaskManager renderQuestionByQuestionTaskManager;
    @Autowired
    private RecommendationSimilarityService recommendationSimilarityService;

    @Autowired
    @Lazy
    private TikuRenderQuestionByQuestionsTaskService _self;

    /**
     * 注册创建任务
     */
    public void registerSynchronizationToCreateRenderQuestionByQuestionTask(
            TikuRenderQuestionByQuestionTaskCreateParam tikuRenderQuestionByQuestionTaskCreateParam) {
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    createRenderQuestionByPaperTask(tikuRenderQuestionByQuestionTaskCreateParam);
                }
            });
        } else {
            createRenderQuestionByPaperTask(tikuRenderQuestionByQuestionTaskCreateParam);
        }
    }

    /**
     * 创建任务
     */
    public void createRenderQuestionByPaperTask(TikuRenderQuestionByQuestionTaskCreateParam param) {
        try {
            List<TikuRenderQuestionByQuestionTaskCreateParam.RenderQuestionInfoCreateDTO> renderQuestionInfos =
                    param.getRenderQuestionInfoCreateDTOS();
            List<TikuRenderQuestionByQuestionTaskCreateParam.RenderQuestionInfoCreateDTO> supportRenderQuestionInfoList =
                    Optional.ofNullable(renderQuestionInfos).map(i -> i.stream()
                            .filter(item -> tikuRenderQuestionByPaperTaskService.courseSupport(item.getCourseId()))
                            .collect(Collectors.toList())
                    ).orElseGet(ArrayList::new);

            if (CollectionUtils.isEmpty(supportRenderQuestionInfoList)) {
                return;
            }

            param.setRenderQuestionInfoCreateDTOS(supportRenderQuestionInfoList);
            param.setRenderQuestionRecordId(param.getFromType() + "-" + param.getFromPrimaryKey());
            _self.createTask(param);
        } catch (Exception e) {
            log.error("通过试题渲染试题图片失败: fromType: {}; fromPrimaryId: {}",
                    param.getFromType(),
                    param.getFromPrimaryKey(), e);
        }
    }

     /**
     * 创建任务
     *    1. 新建待执行任务 pending
     *    2. 新建取消任务  canceled  如果renderQuestionRecordId在等待执行了，则不添加执行任务，直接将任务置为取消废弃状态
     */
    @DistributeLock(moduleName = "TIKU", name = "renderQuestionByQuestionTask",
            argValueKeys = { "[0].renderQuestionRecordId" }, waitTime = 5)
    @Override
    public void createTask(TikuRenderQuestionByQuestionTaskCreateParam param) {
        Verify2.of(param)
                .isNotBlank(TikuRenderQuestionByQuestionTaskCreateParam::getRenderQuestionRecordId)
                .isNotEmpty(TikuRenderQuestionByQuestionTaskCreateParam::getRenderQuestionInfoCreateDTOS)
                .verify();

        Set<String> existsQuestionIds = new HashSet<>();
        List<TikuRenderQuestionByQuestionTaskCreateParam.RenderQuestionInfoCreateDTO> renderQuestionInfoCreateDTOS
                = param.getRenderQuestionInfoCreateDTOS();
        renderQuestionInfoCreateDTOS = renderQuestionInfoCreateDTOS.stream()
                .filter(item -> existsQuestionIds.add(item.getBelongType() + "_" + item.getQuestionId()))
                .collect(Collectors.toList());

        // 保存试题记录 - 同个来源任务多次保存,数据都会保留,实际渲染时查询最新一份数据
        RenderQuestionRecordEntity renderQuestionRecordEntity = new RenderQuestionRecordEntity();
        renderQuestionRecordEntity.setRenderQuestionRecordId(param.getRenderQuestionRecordId());
        renderQuestionRecordEntity.setRenderQuestionInfos(
                renderQuestionInfoCreateDTOS.stream()
                        .map(item -> {
                            RenderQuestionInfoEntity renderQuestionInfoEntity = new RenderQuestionInfoEntity();
                            renderQuestionInfoEntity.setBelongType(item.getBelongType());
                            renderQuestionInfoEntity.setQuestionId(item.getQuestionId());
                            renderQuestionInfoEntity.setCourseId(item.getCourseId());
                            return renderQuestionInfoEntity;
                        })
                        .collect(Collectors.toList())
        );
        Document renderQuestionRecordEntityDocument = renderQuestionRecordEntity.toDocument();
        renderQuestionRecordManager.insertOne(renderQuestionRecordEntityDocument);

        doCreateTask(param);
    }

    /**
     * 保存执行结果
     */
    @Override
    public boolean ackTask(TikuRenderQuestionByQuestionTaskAckParam param) {
        Verify2.of(param)
                .isNotBlank(TikuRenderQuestionByQuestionTaskAckParam::getTaskId)
                .isNotBlank(TikuRenderQuestionByQuestionTaskAckParam::getRenderQuestionRecordId)
                .verify();

        return doAckTask(param);
    }

    /**
     * 获取需要渲染的试题信息
     */
    public List<Map<String, Object>> getQuestionInfo(String renderQuestionRecordId) {
        // 获取最新一份试题记录
        Bson query = eq("renderQuestionRecordId", renderQuestionRecordId);
        Order order = Order.Field.desc("_id");
        Document first = renderQuestionRecordManager.getFirst(query, order);
        if (first == null) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> renderQuestionInfos = MapUtil.getCast(first, "renderQuestionInfos");
        if (CollectionUtils.isEmpty(renderQuestionInfos)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> searchQnList = renderQuestionInfos.stream()
                .map(i -> {
                    Map<String, Object> qn = new HashMap<>();
                    qn.put("belongType", i.get("belongType"));
                    qn.put("similarQuestionId", i.get("questionId"));
                    return qn;
                }).collect(Collectors.toList());
        Map<String, Map<String, Object>> questionInfoMap = recommendationSimilarityService.getQuestionInfoList(searchQnList);

        for (Map<String, Object> renderQuestionInfo : renderQuestionInfos) {
            String questionId = MapUtil.getString(renderQuestionInfo, "questionId");
            renderQuestionInfo.put("question", questionInfoMap.get(questionId));
        }

        return renderQuestionInfos;
    }

    @Override
    public String getIdFieldName() {
        return "renderQuestionRecordId";
    }

    @Override
    public IManager getManager() {
        return renderQuestionByQuestionTaskManager;
    }

    @Override
    public TikuRenderQuestionByQuestionTaskReceiveDTO newReceiveDTO() {
        return new TikuRenderQuestionByQuestionTaskReceiveDTO();
    }
}
