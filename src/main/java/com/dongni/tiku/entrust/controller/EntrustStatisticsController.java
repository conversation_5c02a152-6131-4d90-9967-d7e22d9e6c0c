package com.dongni.tiku.entrust.controller;

import com.dongni.commons.entity.Response;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.entrust.service.dongni.EntrustStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/05/26 <br/>
 *
 */
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/own/entrust/statistics")
public class EntrustStatisticsController {
    
    @Autowired
    private EntrustStatisticsService entrustStatisticsService;
    
    /**
     * 查询超时统计
     *
     * @return
     */
    @GetMapping("/timeout")
    public Response getEntrustStatisticsTimeout(Map<String, Object> params) {
        return new Response(entrustStatisticsService.getEntrustStatisticsTimeout(params));
    }
    
    /**
     * 委托超时统计导出
     *
     * @return
     */
    @GetMapping("/timeout/export")
    public Response exportEntrustTimeout(Map<String, Object> params) {
        return new Response(entrustStatisticsService.exportEntrustStatisticsTimeout(params));
    }
}
