package com.dongni.tiku.entrust.controller;


import com.dongni.basedata.log.aspect.DongniNotAccessLog;
import com.dongni.commons.entity.Response;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.entrust.service.dongni.EntrustAppointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/05/06 <br/>
 * 委托派题
 *   录题人员领任务 / 顾问指派录题人员
 */
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/own/entrust/appoint")
public class EntrustAppointController {

    @Autowired
    private EntrustAppointService entrustAppointService;
    
    /**
     * 获取指派中的委托数量
     * t_entrust.entrust_status = entrustStatus.autoAppoint
     */
    @GetMapping("/count")
    public Response getEntrustRemainderCount(Map<String, Object> params) {
        return new Response(entrustAppointService.getEntrustAutoAppointCount(params));
    }
    
    /**
     * 录题人员领取任务 受同时录入中任务次数限制
     * @param params
     *   limitMax 领取数量 领取数量不能大于次数限制
     */
    @PostMapping("/auto")
    @DongniNotAccessLog
    public Response autoAppointEntrusts(Map<String, Object> params) {
        entrustAppointService.autoAppointEntrusts(params);
        return new Response();
    }
    
    /**
     * 手动指派委托任务
     */
    @PostMapping("/manual")
    public com.dongni.commons.entity.Response manualAppointEntrust(Map<String, Object> params) {
        entrustAppointService.manualAppointEntrust(params);
        return new Response();
    }
}
