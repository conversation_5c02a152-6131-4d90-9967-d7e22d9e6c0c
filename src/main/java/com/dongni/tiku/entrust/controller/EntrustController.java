package com.dongni.tiku.entrust.controller;

import com.dongni.commons.entity.Response;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.entrust.service.dongni.EntrustMarkService;
import com.dongni.tiku.entrust.service.dongni.EntrustQueryService;
import com.dongni.tiku.entrust.service.dongni.EntrustService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/05/06 <br/>
 *
 */
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/own/entrust")
public class EntrustController {
    
    @Autowired
    private EntrustService entrustService;
    
    @Autowired
    private EntrustQueryService entrustQueryService;
    
    @Autowired
    private EntrustMarkService entrustMarkService;
    
    // -------------------------------------------------------------------- 查询 start
    /**
     * 获取委托列表
     *   老师角色: 根据userId -> entrusterUserId 查询
     *   金卷题库管理员 跟老师一样是委托人
     *   产品顾问: 先获取产品顾问负责的schoolIds, 再进行查询，如果提供schoolId查询指定学校则校验是否为该学校的产品顾问
     *   题库管理员: 任意权限
     *   录题人员:  获取与自己相关的委托记录
     */
    @GetMapping("/list")
    public Response getList(Map<String, Object> params) {
        return new Response(entrustQueryService.getEntrustList(params));
    }
    
    /**
     * 产品顾问 运营人员 审核时获取下一个entrustInfo
     * @param params
     *   - userType          用户类型
     *   - entrustMarkStatus 当前委托的标记状态
     *   - entrustId         当前委托的id
     *   - sortFieldValue    当前的排序字段的值
     *   - sortField         entrustId or startTimestampSecond or createDateTime
     *   - [sortType]        asc / desc(default)
     *   - [courseId]        课程id
     *   - [schoolId]        学校id
     *   - [entrustStatus]   委托状态 多个用空格隔开 123,456,798
     *   - [search]          entrustId 考试名称 答题卡id 创建人名称
     * @return count entrustList
     *    entrustId,           委托id
     *    createDateTime,      创建时间
     *    startTimestampSecond 当前状态开始时间
     *    entrustMarkStatus    委托录题标记状态
     */
    @GetMapping("/manager/processing/next")
    public Response getNextEntrustForManagerForProcessing(Map<String, Object> params) {
        return new Response(entrustQueryService.getNextEntrustForManagerForProcessing(params));
    }
    
    /**
     * 获取学校列表 带虚拟学校的
     * @param params userType
     * @return Map{ count, schoolList[{schoolId, schoolName}]}
     */
    @GetMapping("/school")
    public Response getEntrustSchool(Map<String, Object> params) {
        return new Response(entrustQueryService.getEntrustSchool(params));
    }
    
    /**
     * 根据id查询委托
     * @param params
     *   - entrustId     至少提供一个
     *   - answerPaperId 至少提供一个
     * @return 没有会抛异常
     */
    @GetMapping("")
    public Response getEntrust(Map<String, Object> params) {
        return new Response(entrustQueryService.getEntrustById(params));
    }
    
    /**
     * 根据id查询委托详情
     * @param params
     *   - entrustId     至少提供一个
     *   - answerPaperId 至少提供一个
     * @return 没有可能返回null或者异常
     */
    @GetMapping("/detail")
    public Response getEntrustDetail(Map<String, Object> params) {
        return new Response(entrustQueryService.getEntrustDetail(params));
    }


    /**
     * 获取关联委托的列表
     * @param params schoolId courseId
     * @return
     */
    @GetMapping("/relation")
    public Response getRelationEntrust(Map<String, Object> params) {
        return new Response(entrustQueryService.getRelationEntrust(params));
    }
    
    // -------------------------------------------------------------------- 查询 end
    
    // -------------------------------------------------------------------- 增删改 end
    
    /**
     * 创建委托记录 1草稿<br/>
     *  t_entrust  <br/>
     *  如果answerPaperId提供且存在该委托，则直接返回entrust记录 <br/>
     * @param params -
     *               schoolId   学校id 区域管理员会传虚拟的学校id=-${stage}<br/>
     *               schoolName 学校名称 <br/>
     *               gradeType  委托录入的试卷适用年级 <br/>
     *               gradeName  委托录入的试卷的使用年级名称 <br/>
     *               courseId   课程id <br/>
     *               fullMark   满分 <br/>
     *               [entrustBusinessType] 委托录题业务类型 1校本业务 2废弃 3金卷业务<br>
     *               [entrustPaperType]    委托试卷类型  金卷业务必须提供 其他可不提供<br>
     *               [entrustMarkStatus]   委托标记状态  0默认(不标记) 1不录入 2不校对
     *               [paperYear]           归属年份，不提供为当前年份<br>
     *               [answerPaperId]     自主编辑使用 <br/>
     *               [entrusterUserId]   老师不需要提供 <br/>
     *               [entrusterUserName] 老师不需要提供 <br/>
     *               [areaIdList]        试题所属区域,可暂时不提供,提交审核时强制校验<br/>
     *               [catalogList]       章节，目前仅支持小学设置<br/>
     *               [entrustCreateFromType]  委托创建来源类型 1正常(默认值)  2题卡合一
     *               [combinedPaperName]      从题卡合一创建时提供的试卷名称
     * @return entrustId 委托录题id
     */
    @PostMapping("/create")
    public Response createEntrust(Map<String, Object> params) {
        return new Response(entrustService.createEntrust(params));
    }
    
    /**
     * 创建委托记录
     *    提供一份有试题的试卷，到知识点标注环节
     * @return entrustId 委托录题id
     */
    @PostMapping("/create/knowledge/mark")
    public Response createEntrustKnowledgeMark(Map<String, Object> params) {
        return new Response(entrustService.createEntrustKnowledgeMark(params));
    }
    
    /**
     * 删除委托 实际是将委托置为审核不通过rejected WHAT?????? 还有这种操作
     * @param params
     *   entrustId       委托记录id
     */
    @PostMapping("/delete")
    public Response deleteEntrust(Map<String, Object> params) {
        entrustService.deleteEntrust(params);
        return new Response();
    }
    
    /**
     * 更新委托信息
     * @param params
     *  - entrustId
     *  - 其他信息详见调用接口
     */
    @PostMapping("/update")
    public Response updateEntrust(Map<String, Object> params) {
        entrustService.updateEntrust(params);
        return new Response();
    }
    
    /**
     * 提交审核
     *   委托人操作委托文档之后 提交审核
     *   由于进入指派池后的流程需要提供录题的属性支持，如截图/文字 有解析/无解析 可复制/不可复制等，取消区域委托的不需要审核流程
     * @param params
     *  - entrustId
     */
    @PostMapping("/submit")
    public Response submitToPendingReview(Map<String, Object> params) {
        entrustService.submit(params);
        return new Response();
    }
    
    /**
     * 审核通过
     * @param params
     *   - entrustId     委托id
     */
    @PostMapping("/pass")
    public Response passEntrust(Map<String, Object> params) {
        entrustService.passEntrust(params);
        return new Response();
    }
    
    /**
     * 审核不通过
     * @param params
     *   - entrustId     委托id
     *   - comment       拒绝理由
     */
    @PostMapping("/reject")
    public Response rejectEntrust(Map<String, Object> params) {
        entrustService.rejectEntrust(params);
        return new Response();
    }
    
    /**
     * 终止委托任务
     *   录题人员、题库管理员、校对员、顾问进行终止任务
     * @param params
     *   - entrustId     委托id
     *   - comment       退回理由
     */
    @PostMapping("/stop")
    public Response updateEntrustToStop(Map<String, Object> params) {
        entrustService.updateEntrustToStop(params);
        return new Response();
    }
    
    /**
     * 录入完成 进入校对中
     * @param params
     *   - entrustId     委托id
     */
    @PostMapping("/processed")
    public Response processed(Map<String, Object> params) {
        entrustService.processed(params);
        return new Response();
    }
    
    /**
     * 校对不通过 退回到录入中
     * @param params
     *   - entrustId     委托id
     *   - comment
     */
    @PostMapping("/processing/reject")
    public Response processingReject(Map<String, Object> params) {
        entrustService.processingReject(params);
        return new Response();
    }
    
    /**
     * 校对通过 合并试卷答题卡
     * @param params
     *   - entrustId     委托id
     */
    @PostMapping("/merge")
    public Response mergePaperAndAnswerCard(Map<String, Object> params) {
        return new Response(entrustService.mergePaperAndAnswerCard(params));
    }
    
    /**
     * 解绑
     * @param params
     * @return
     */
    @PostMapping("/unbind")
    public Response unbindAnswerPaper(Map<String, Object> params) {
        entrustService.unbindAnswerPaper(params);
        return new Response();
    }
    
    /**
     * 强制退回
     * @param params
     *   - entrustId
     *   - comment
     * @return
     */
    @PostMapping("back/force")
    public Response forceBackEntrust(Map<String, Object> params) {
        entrustService.forceBackEntrust(params);
        return new Response();
    }
    
    /**
     * 推送
     * @param params
     *   - entrustId
     * @return
     */
    @PostMapping("push")
    public Response pushEntrustByEntrustId(Map<String, Object> params) {
        entrustMarkService.pushMarking(params, true);
        return new Response();
    }
    
    /**
     * 同步 同步知识点
     * @param params
     *   - entrustId
     * @return
     */
    @PostMapping("pull")
    public Response pullByEntrustIdAndInvokeCompleteMargePaper(Map<String, Object> params) {
        return new Response(entrustService.pullByEntrustIdAndInvokeCompleteMargePaper(params));
    }

    /**
     * 标记委托/取消标记委托委托
     * @param params entrustId entrustMarkStatus
     * @return
     */
    @PostMapping("/mark")
    public Response markEntrust(Map<String, Object> params) {
        entrustService.markEntrustMarkStatus(params);
        return new Response();
    }

    /**
     * 超时退回\超时不退回
     * @param params entrustId entrustMarkStatus
     */
    @PostMapping("/overtimeReturn")
    public Response overtimeReturn(Map<String, Object> params) {
        entrustService.markOvertimeReturn(params);
        return new Response();
    }

    /**
     * 加急程度
     * @param params entrustId entrustMarkStatus
     */
    @PostMapping("/urgency")
    public Response urgency(Map<String, Object> params) {
        entrustService.markEntrustUrgency(params);
        return new Response();
    }

    /**
     * 修改委托标注方式和标注人
     * @param params entrustId paperMarkType markTeacherId markTeacherName
     * @return
     */
    @PostMapping("/marker")
    public Response updateEntrustMarker(Map<String, Object> params) {
        entrustService.updateEntrustMarker(params);
        return new Response();
    }

    /**
     * 创建委托(文件在此一起保存)并submit - 题卡合一专用接口
     */
    @PostMapping("/create/and/submit")
    public Response createEntrustWithFileThenSubmit(Map<String, Object> params) {
        return new Response(entrustService.createEntrustWithFileThenSubmit(params));
    }


//
//    /**
//     * 同步 从一起作业网同步数据到懂你
//     * @param params
//     *   - entrustId
//     * @return
//     */
//    @PostMapping("pull/test")
//    public Response pullFrom17ByEntrustIdAndInvokeCompleteMargePaper1(Map<String, Object> params) {
//        List entrustIdList = (List) params.get("entrustIdList");
//        for (Object item : entrustIdList) {
//            Long entrustId = Long.parseLong(item.toString());
//            Map<String, Object> map = MapUtil.copy(params, "userId", "userName", "userType", "entrustStatusList");
//            map.put("entrustId", entrustId);
//            entrustService.pullFrom17ByEntrustIdAndInvokeCompleteMargePaper(map);
//        }
//        return new Response();
//    }
//
}
