package com.dongni.tiku.entrust.service.dongni;

import com.dongni.basedata.admin.service.IBaseSchoolService;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.school.config.service.IModuleConfigService;
import com.dongni.basedata.school.operator.service.impl.SchoolOperatorServiceImpl;
import com.dongni.basedata.system.account.service.IUserService;
import com.dongni.common.utils.CommonUtil;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.ParamsUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.FileUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.plan.service.ExamPaperService;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.entrust.service.virtual.AbstractEntrustVirtualInfo;
import com.dongni.tiku.entrust.util.EntrustParamsUtil;
import com.dongni.tiku.entrust.util.EntrustUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.own.service.OwnPaperService;
import com.dongni.tiku.proofread.service.task.ProofreadTaskService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.*;
import static java.util.stream.Collectors.*;

/**
 * <AUTHOR> <br/>
 * @date 2020/04/24 <br/>
 *
 */
@Service
public class EntrustQueryService {
    
    @Autowired
    private TikuRepository tikuRepository;
    
    @Autowired
    private ExamPaperService examPaperService;
    
    @Autowired
    private IBaseSchoolService baseSchoolService;
    
    @Autowired
    private IModuleConfigService moduleConfigService;
    
    @Autowired
    private AbstractEntrustVirtualInfo entrustVirtualInfo;
    
    @Autowired
    private SchoolOperatorServiceImpl schoolOperatorService;
    
    @Autowired
    private IUserService userService;
    
    @Autowired
    private OwnPaperService ownPaperService;
    
    @Autowired
    private EntrustTypistService entrustTypistService;

    @Autowired
    private EntrustCatalogService entrustCatalogService;

    @Autowired
    private EntrustMarkService entrustMarkService;
    
    @Autowired
    private EntrustPaperService entrustPaperService;

    @Autowired
    private ProofreadTaskService proofreadTaskService;

    @Autowired
    private CommonCourseService commonCourseService;

    @Autowired
    private PaperManager paperManager;
    
    /**
     * 查询委托信息 仅 t_entrust
     * @return 不会抛异常
     */
    public List<Map<String, Object>> getEntrust(Map<String, Object> params) {
        List<Map<String, Object>> entrustList = tikuRepository.selectList("EntrustQueryMapper.getEntrust", params);
        EntrustParamsUtil.handlerEntrustAreaIdsForQuery(entrustList);
        return entrustList;
    }
    
    /**
     * 查询委托信息 仅 t_entrust
     * @return 不会抛异常
     */
    public List<Map<String, Object>> getEntrustByPaperId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("paperId")
                .verify();
        return tikuRepository.selectList("EntrustQueryMapper.getEntrustByPaperId", params);
    }
    
    /**
     * 根据entrustId查询委托
     * @param entrustId 委托id
     * @return 没有会抛异常
     */
    public Map<String, Object> getEntrustByEntrustId(long entrustId) {
        return getEntrustById(MapUtil.of("entrustId", entrustId), true);
    }

    /**
     * 根据id查询委托
     *   entrustId / answerPaperId
     * @param params
     *   - entrustId     至少提供一个
     *   - answerPaperId 至少提供一个
     * @return 没有会抛异常
     */
    public Map<String, Object> getEntrustById(Map<String, Object> params) {
        return getEntrustById(params, true);
    }
    
    /**
     * 根据id查询委托
     *   entrustId / answerPaperId
     * @param params
     *   - entrustId     至少提供一个
     *   - answerPaperId 至少提供一个
     * @param throwIfNotExist true 如果查询不到会抛异常
     * @return entrustInfo
     */
    public Map<String, Object> getEntrustById(Map<String, Object> params, boolean throwIfNotExist) {
        if (ObjectUtil.isBlank(params.get("entrustId")) && ObjectUtil.isBlank(params.get("answerPaperId"))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "委托id或者答题卡id必须提供");
        }
        List<Map<String, Object>> entrustList = getEntrust(MapUtil.copyWithoutNull(params, "entrustId", "answerPaperId"));
        if (CollectionUtils.isEmpty(entrustList)) {
            if (throwIfNotExist) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "委托不存在，请刷新重试");
            } else {
                return null;
            }
        }
        return entrustList.get(0);
    }
    
    /**
     * 获取委托信息
     * @param params
     *   - [entrustId]
     *   - entrustStatusList
     * @return 如果获取不到返回null
     */
    public List<Map<String, Object>> getEntrustByStatus(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("entrustStatusList")
                .verify();
        return getEntrust(params);
    }
    
    /**
     * 获取需要从17作业网同步知识点的委托信息
     * @param params [entrustId]
     * @return entrustInfo [{entrustId}]
     */
    public List<Map<String, Object>> getEntrustForPullFrom17(Map<String, Object> params) {
        Map<String, Object> query = new HashMap<>(params);
        // 标注类型为一起only的，新版本的会直接推送到公司标注
        query.put("paperMarkType", DictUtil.getDictValue("entrustPaperMarkType", "yiqiOnly"));
        // 状态为标注中或标注完成的
        query.put("entrustStatusList", Stream.of(
                DictUtil.getDictValue("entrustStatus", "yiqiProofreading"),
                DictUtil.getDictValue("entrustStatus", "yiqiProofreadingFinish")
        ).collect(toList()));
        return tikuRepository.selectList("EntrustQueryMapper.getEntrustForPullFrom17", query);
    }

    /**
     * 获取需要从懂你标注同步知识点的委托信息
     * @param params [entrustId]
     * @return entrustInfo [{entrustId}]
     */
    public List<Map<String, Object>> getEntrustForPullFromDongni(Map<String, Object> params) {
        Map<String, Object> query = new HashMap<>(params);
        // 标注类型不是一起only的
        query.put("notPaperMarkType", DictUtil.getDictValue("entrustPaperMarkType", "yiqiOnly"));
        // 状态为标注完成的
        query.put("entrustStatus", DictUtil.getDictValue("entrustStatus", "yiqiProofreadingFinish"));
        return tikuRepository.selectList("EntrustQueryMapper.getEntrustForPullFromDongni", query);
    }

    /**
     * 获取委托信息
     * @param params
     *   - entrustId
     *   - entrustStatusList
     * @return 如果获取不到返回null
     */
    public Map<String, Object> getEntrustByEntrustIdAndStatus(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("entrustId")
                .isNotEmptyCollections("entrustStatusList")
                .verify();
        List<Map<String, Object>> entrustList = getEntrust(params);
        if (CollectionUtils.isEmpty(entrustList)) {
            return null;
        }
        return entrustList.get(0);
    }
    
    /**
     * 获取委托当前状态值
     * @param params
     *   - entrustId     至少提供一个
     *   - answerPaperId 至少提供一个
     * @return 委托当前状态值
     */
    public Integer getCurrentEntrustStatusValue(Map<String, Object> params) {
        Map<String, Object> entrustInfo = getEntrustById(params);
        return Integer.parseInt(entrustInfo.get("entrustStatus").toString());
    }
    
    /**
     * 委托录题 查看委托详细信息
     * 包含委托信息     t_entrust
     * 老师上传的文档   t_entrust_upload
     * 录题人员        typist
     * @param params
     *    - entrustId     至少提供一个
     *    - answerPaperId 至少提供一个
     * @return 没有可能返回null或者异常
     */
    public Map<String, Object> getEntrustDetail(Map<String, Object> params) {
        if ((!ObjectUtil.isValidId(params.get("entrustId"))) && (!ObjectUtil.isValidId(params.get("answerPaperId")))) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "委托id或者答题卡id必须提供");
        }
        
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("fileNotDeleted", DictUtil.getDictValue("entrustFileStatus", "normal"));
        if (ObjectUtil.isValidId(params.get("entrustId"))) {
            queryParams.put("entrustId", params.get("entrustId"));
        } else {
            long answerPaperId = MapUtil.getLong(params, "answerPaperId");
            Document answerCard = entrustPaperService.getAnswerCardByAnswerPaperId(answerPaperId);
            if (MapUtils.isEmpty(answerCard)) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "答题卡不存在 paperId: " + answerPaperId);
            }
            answerPaperId = MapUtil.getLong(answerCard, "sourcePaperId", answerPaperId);
            queryParams.put("answerPaperId", answerPaperId);
            params.put("answerPaperId", answerPaperId);
        }
        Map<String, Object> entrustDetail = tikuRepository.selectOne("EntrustQueryMapper.getEntrustDetail", queryParams);
        // 限制兼职校对员的权限-只能看到自己协助校对的任务
        if (DictUtil.isEquals(MapUtil.getInt(params, "userType"), "userType", "partTimeProofreader")) {
            Map<String, Object> proofreadTask = proofreadTaskService.getProofreadTaskByEntrustId(params);
            if (MapUtils.isEmpty(proofreadTask)
                    || MapUtil.getLong(proofreadTask, "partTimeProofreaderUserId") != MapUtil.getLong(params, "userId")) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "您无权限查看该委托任务!");
            }
        }
        
        // 降级重试 克隆的试卷可能会有entrustId
        if (entrustDetail == null) {
            if (ObjectUtil.isValidId(params.get("answerPaperId"))) {
                // 只有paper是有克隆的试卷的  jinjuanPaper是没有的
                Map<String, Object> originPaper = ownPaperService.getSimplePaperDetail(MapUtil.of(
                        "paperId", params.get("answerPaperId")
                ));
                if (ObjectUtil.isValidId(originPaper.get("entrustId"))) {
                    queryParams.put("entrustId", originPaper.get("entrustId"));
                    queryParams.remove("answerPaperId");
                    entrustDetail = tikuRepository.selectOne("EntrustQueryMapper.getEntrustDetail", queryParams);
                } else {
                    return null;
                }
            } else {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,
                        "找不到委托编号为[" + params.get("entrustId") + "]的委托录题记录");
            }
        }
        EntrustParamsUtil.handlerEntrustAreaIdsForQuery(entrustDetail);
        
        
        params.putIfAbsent("entrustId",  entrustDetail.get("entrustId"));
        entrustTypistService.validEntrustTypist(params);

        long entrustId = Long.parseLong(entrustDetail.get("entrustId").toString());
        Map<String, Object> entrustTypistsByEntrustId = entrustTypistService.getEntrustTypistsByEntrustId(MapUtil.of(
                "entrustId", entrustId,
                "userId", params.get("userId"),
                "userName", params.get("userName")
        ));
        if (MapUtils.isNotEmpty(entrustTypistsByEntrustId)) {
            // 兼职校对员隐藏录题人员信息
            if (DictUtil.isEquals(MapUtil.getInt(params, "userType"), "userType", "partTimeProofreader")) {
                entrustTypistsByEntrustId.put("typistUserId", null);
                entrustTypistsByEntrustId.put("typistUserUserName", "-");
                List<Map<String, Object>> typist = MapUtil.getCast(entrustTypistsByEntrustId, "typist");
                if (CollectionUtils.isNotEmpty(typist)) {
                    typist.forEach(i -> {
                        i.put("typistUserId", null);
                        i.put("typistId", null);
                        i.put("typistUserUserName", "-");
                        i.put("typistUserName", "-");
                        i.put("mobilePhone", "-");
                        i.put("mobilePhoneAes", "-");
                    });
                }
            }
            entrustDetail.putAll(entrustTypistsByEntrustId);
        }

        // 录题人员调用接口需要修改文件名称 年级+课程
        changeFileName4Typist(params, entrustDetail);

        // 增加章节关联信息
        List<Map<String, Object>> entrustCatalogList = entrustCatalogService.getEntrustCatalogInfoListByEntrustId(params);
        entrustDetail.put("catalogList", Optional.of(entrustCatalogList).orElse(Lists.newArrayList()));

        // 增加学校标注人员
        List<Map<String, Object>> entrustMarkerList = entrustMarkService.getEntrustMarkerList(params);
        entrustDetail.put("entrustMarkerList", entrustMarkerList);

        return entrustDetail;
    }

    /**
     * 录题人员调用接口需要修改文件名称 年级+课程
     * @param params  userType
     * @param entrustDetail 委托详情
     */
    private void changeFileName4Typist(Map<String, Object> params, Map<String, Object> entrustDetail) {

        int userType = MapUtil.getInt(params, "userType");
        boolean isSubjectEntry = DictUtil.isEquals(userType, "userType", "subjectEntry");

        if (ObjectUtil.isNotBlank(entrustDetail) && isSubjectEntry) {
            if (ObjectUtil.isNotBlank(entrustDetail.get("answerPaperId"))) {
                List<Map<String, Object>> files = MapUtil.getListMap(entrustDetail, "files");
                Integer gradeType = MapUtil.getInt(entrustDetail, "gradeType");
                String courseName = MapUtil.getString(entrustDetail, "courseName");

                files.forEach(file -> {
                    String extensionName = FileUtil.getExtensionName(MapUtil.getString(file, "fileName"));

                    String fileName = DictUtil.getDictLabel("gradeType", gradeType) + courseName + "试卷";
                    file.put("fileName", fileName + "." + extensionName);
                });
            }
        }
    }
    
    /**
     * 获取自动指派中的委托数量
     *   t_entrust.entrust_status = entrustStatus.autoAppoint
     *   entrustUrgencyList 加急程度列表
     *   courseIdList 课程id列表 录题人员的公共课程id
     */
    long getEntrustAutoAppointCount(Map<String, Object> params) {
        params.put("entrustStatus", getEntrustStatus("autoAppoint"));
        List<Long> courseIdList = MapUtil.getCast(params, "courseIdList");
        Set<Long> courseIdSet = new HashSet<>(courseIdList);

        List<Long> entrustCourseIdList = tikuRepository.selectList("EntrustQueryMapper.getEntrustAutoAppointCount", params);
        // 录题人员绑定课程的委托
        long typistCommonCourseEntrustCount = entrustCourseIdList.stream()
                .filter(courseIdSet::contains)
                .count();

        // 非录题人员绑定课程的委托,查询其中为校本课程的委托
        List<Long> notTypistCourseIds = entrustCourseIdList.stream()
                .filter(i -> !courseIdSet.contains(i))
                .distinct()
                .collect(toList());
        Set<Long> singleCourseIds = new HashSet<>(commonCourseService.checkSchoolCourses(notTypistCourseIds));
        long singleCourseEntrustCount = entrustCourseIdList.stream()
                .filter(singleCourseIds::contains)
                .count();

        return typistCommonCourseEntrustCount + singleCourseEntrustCount;
    }
    
    /**
     * 获取自动指派中的委托
     *   t_entrust.entrust_status = entrustStatus.autoAppoint
     *   entrustUrgencyList 加急程度列表
     *   courseIdList 课程id列表 录题人员的公共课程id
     */
    List<Map<String, Object>> getEntrustAutoAppoint(Map<String, Object> params) {
        params.put("entrustStatus", getEntrustStatus("autoAppoint"));
        List<Long> courseIdList = MapUtil.getCast(params, "courseIdList");
        Set<Long> courseIdSet = new HashSet<>(courseIdList);

        List<Map<String, Object>> entrustList = tikuRepository.selectList("EntrustQueryMapper.getEntrustAutoAppoint", params);
        // 录题人员绑定课程的委托
        List<Map<String, Object>> typistCommonCourseEntrusts = entrustList.stream()
                .filter(i -> courseIdSet.contains(MapUtil.getLong(i, "courseId")))
                .collect(toList());
        // 非录题人员绑定课程的委托,查询其中为校本课程的委托
        List<Long> notTypistCourseIds = entrustList.stream()
                .map(i -> MapUtil.getLong(i, "courseId"))
                .filter(i -> !courseIdSet.contains(i))
                .distinct()
                .collect(toList());
        Set<Long> singleCourseIds = new HashSet<>(commonCourseService.checkSchoolCourses(notTypistCourseIds));
        entrustList.stream()
                .filter(i -> singleCourseIds.contains(MapUtil.getLong(i, "courseId")))
                .forEach(typistCommonCourseEntrusts::add);

        return typistCommonCourseEntrusts;
    }
    
    /**
     * 委托录题 获取列表
     * 老师角色: 仅获取自己创建的委托记录列表 根据userId -> entrusterUserId 查询
     * - 委托状态: 所有
     * 产品顾问: 先获取产品顾问负责的schoolIds, 再进行查询，如果提供schoolId查询指定学校则校验是否为该学校的产品顾问
     * - 委托状态: 查询草稿状态以外的委托记录
     * 题库管理员: 任意权限
     * - 委托状态: 查询草稿状态以外的委托记录
     * 录题人员:  获取与自己相关的委托记录
     * - 委托状态: 自动派送中, 懂你录入中, 校对中, 已完成
     *
     * @param params userType        用户角色
     *               [typistId]
     *               [schoolId]      学校id，查询全部不需要提供
     *               [entrustStatus] 委托状态，查询全部不需要提供，老师会返回草稿状态的记录，其他不会返回草稿状态的记录 多个状态用,分割  如123,456,789
     *               [courseId]      课程id，查询全部不需要提供
     *               [search]        查询字段 委托编号id/委托人名称/试卷名称
     * @return {count, entrustList}
     */
    public Map<String, Object> getEntrustList(Map<String, Object> params) {
        Verify.of(params)
                .isNumeric("userType")
                .verify();
        int currentUserType = Integer.parseInt(params.get("userType").toString());
        // 委托人
        if (DictUtil.isEquals(currentUserType, "userType", "teacher", "areaSubjectAdmin", "examiner", "jinJuanAdmin")) {
            return getListForEntrustor(params);
        }
        // 录题人员
        if (DictUtil.isEquals(currentUserType, "userType", "subjectEntry")) {
            return getListForTypist(params);
        }
        // 题库管理员 校对员
        if (DictUtil.isEquals(currentUserType, "userType", "subjectAdmin")) {
            return getListForSubjectAdmin(params);
        }
        // 管理人员 产品顾问 运营人员
        if (DictUtil.isEquals(currentUserType, "userType", "product", "operatorReviewer","operatorProofreader")) {
            return getListForManager(params);
        }
        
        return MapUtil.of("count", 0, "entrustList", new ArrayList<>());
    }
    
    /**
     * 委托人的列表
     *   仅获取自己的
     * @param params
     *   - userId
     *   - [courseId] 课程id
     *   - [search]  entrustId 模糊查
     *   - [sortField] entrustId(default)/createDateTime
     *   - [sortType]  asc / desc(default)
     * @return count entrustList
     *    entrustId,         委托id
     *    entrustStatus,     委托状态
     *    entrustStatusOld,  委托旧状态
     *    courseId,          课程id
     *    courseName,        课程名称
     *    entrusterUserId,   委托人
     *    entrusterUserName, 委托人
     *    createDateTime,    委托创建时间
     */
    private Map<String, Object> getListForEntrustor(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .verify();
        Map<String, Object> queryParams = new HashMap<>();
        MapUtil.copy(params, queryParams, "pageSize", "pageNo", "currentIndex", "sortField", "sortType");
        
        // 委托人只查自己的
        queryParams.put("entrusterUserId", params.get("userId"));
        
        // 允许查询的委托状态
        List<Integer> validQueryStatusList = getAllEntrustStatusList();
        // 查询的状态
        List<Integer> queryEntrustStatusList = checkAndGetQueryStatusList(params, validQueryStatusList);
        queryParams.put("queryEntrustStatusList", queryEntrustStatusList);
    
        // courseId 课程id
        if (ObjectUtil.isValidId(params.get("courseId"))) {
            queryParams.put("courseId", params.get("courseId"));
        }
    
        // search entrustId 模糊查
        if (!ObjectUtil.isBlank(params.get("search"))) {
            queryParams.put("search", params.get("search").toString().trim());
        }
    
        // sortField sortType
        Map<String, String> sortFieldMap = new HashMap<>();
        sortFieldMap.put("entrustId", "entrust_id");
        sortFieldMap.put("createDateTime", "create_date_time");
        sortFieldMap.put(ParamsUtil.SORT_DEFAULT_KEY, "entrust_id");
        ParamsUtil.handlerSortParam(queryParams, sortFieldMap, false);
        
        int count = tikuRepository.selectOne("EntrustQueryMapper.getEntrustListForEntrusterCount", queryParams);
        if (count == 0) {
            return MapUtil.of("count", count, "entrustList", Collections.emptyList());
        }
        List<Map<String, Object>> entrustList =
                tikuRepository.selectList("EntrustQueryMapper.getEntrustListForEntruster", queryParams);
        return MapUtil.of("count", count, "entrustList", entrustList);
    }
    
    /**
     * 录题人员的列表
     * @param params
     *   - userId
     *   - [search]  entrustId 模糊查
     *   - [sortField] entrustId
     *   - [sortType]  asc / desc(default)
     * @return count entrustList
     *    entrustId,         委托id
     *    entrustStatus,     委托状态
     *    entrustStatusOld,  委托旧状态
     *    courseId,          课程id
     *    courseName,        课程名称
     *    timeoutTimestampSecond       状态的超时时间
     *    isTimeout                    状态是否超时
     *    requireFinishTimestampSecond 要求完成时间 录入中会存在
     *    managerUserId      负责人
     *    managerUserName    负责人
     *    managerUserPhone   负责人
     */
    private Map<String, Object> getListForTypist(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("userId")
                .verify();
        
        Map<String, Object> queryParams = new HashMap<>();
        MapUtil.copy(params, queryParams, "pageSize", "pageNo", "currentIndex", "sortField", "sortType");
        // 只查自己的任务
        queryParams.put("typistUserId", Long.parseLong(params.get("userId").toString()));
        queryParams.put("entrustTypistStatusInvalid", DictUtil.getDictValue("entrustTypistStatus", "invalid"));
    
        // 允许查询的委托状态
        List<Integer> validQueryStatusList = getEntrustStatusList(
                "processingRejected",
                "dnProcessing",
                "proofreading",
                "yiqiQuestionPushing",
                "yiqiProofreading",
                "yiqiProofreadingFinish",
                "complete"
        );
        // 查询的状态
        List<Integer> queryEntrustStatusList = checkAndGetQueryStatusList(params, validQueryStatusList);
        queryParams.put("queryEntrustStatusList", queryEntrustStatusList);
        
        // search entrustId 模糊查询
        if (!ObjectUtil.isBlank(params.get("search"))) {
            queryParams.put("search", params.get("search").toString().trim());
        }
        // 委托的紧急程度
        if (ObjectUtil.isNotBlank(params.get("entrustUrgency"))) {
            queryParams.put("entrustUrgency", MapUtil.getInt(params, "entrustUrgency"));
        }
    
        // sortField sortType
        Map<String, String> sortFieldMap = new HashMap<>();
        sortFieldMap.put("entrustId", "entrust_id");
        sortFieldMap.put(ParamsUtil.SORT_DEFAULT_KEY, "entrust_id");
        ParamsUtil.handlerSortParam(queryParams, sortFieldMap, false);
        
        int count = tikuRepository.selectOne("EntrustQueryMapper.getEntrustListForTypistCount", queryParams);
        if (count == 0) {
            return MapUtil.of("count", count, "entrustList", Collections.emptyList());
        }
        
        // timeoutTimestampSecond   来自timeout表 如果没有超时时间会被设置为 LONG.MAX - entrustId
        // requireFinishTimestampSecond 来自log表 如果没有超时时间会被设置为 0
        List<Map<String, Object>> entrustList =
                tikuRepository.selectList("EntrustQueryMapper.getEntrustListForTypist", queryParams);
        
        // 获取任务负责人信息
        List<Long> schoolIdList = entrustList.stream()
                .map(o -> Long.parseLong(o.get("schoolId").toString()))
                .distinct()
                .collect(toList());
        List<Map<String, Object>> operatorBySchoolIdList = schoolOperatorService.getOperatorBySchoolIdList(schoolIdList);
        // schoolId -> userType -> operatorInfo
        Map<Long, Map<Integer, Map<String, Object>>> schoolId2UserType2OperatorInfo = operatorBySchoolIdList.stream()
                .collect(groupingBy(o -> MapUtil.getLong(o, "schoolId"),
                        toMap(o -> MapUtil.getInt(o, "userType"), o -> o))
                );
        
        boolean canDecrypt = userService.checkUser(params);
        long currentTimestampSecond = System.currentTimeMillis() / 1000;
        int operatorReviewerUserType = DictUtil.getDictValue("userType", "operatorReviewer");
        int operatorProofreaderUserType = DictUtil.getDictValue("userType", "operatorProofreader");
        
        // 填充负责人信息
        for (Map<String, Object> entrust : entrustList) {
            int entrustStatus = MapUtil.getInt(entrust, "entrustStatus");
            // 要求完成时间 针对录入中
            if (!DictUtil.isEquals(entrustStatus, "entrustStatus", "dnProcessing")) {
                entrust.remove("requireFinishTimestampSecond");
            }
            
            // 判断是否超时
            boolean isTimeout = false;
            if (ObjectUtil.isValidId(entrust.get("timeoutTimestampSecond"))) {
                long timeoutTimestampSecond = MapUtil.getLong(entrust, "timeoutTimestampSecond");
                if (timeoutTimestampSecond > 0 && currentTimestampSecond > timeoutTimestampSecond) {
                    isTimeout = true;
                }
            }
            entrust.put("isTimeout", isTimeout);
            
            // 不是这些状态的 录入中 校对中 负责人 不需要添加负责人信息
            if (!DictUtil.isEquals(entrustStatus, "entrustStatus",
                    "dnProcessing", "processingRejected", "proofreading")) {
                continue;
            }
            
            // 任务负责人: 默认题库管理员 如果没有配置就是他了
            long managerUserId = 0L;
            String managerUserName = "题库管理员";
            String managerUserPhone = "";
            long schoolId = Long.parseLong(entrust.get("schoolId").toString());
            // userType -> operatorInfo
            Map<Integer, Map<String, Object>> userType2OperatorInfo = schoolId2UserType2OperatorInfo.get(schoolId);
            if (MapUtils.isNotEmpty(userType2OperatorInfo)) {
                Map<String, Object> operatorInfo = null;
                // 录入中 负责人 审核员
                if (DictUtil.isEquals(entrustStatus, "entrustStatus", "dnProcessing", "processingRejected")) {
                    operatorInfo = userType2OperatorInfo.get(operatorReviewerUserType);
                }
                // 校对中 负责人 校对员
                else if (DictUtil.isEquals(entrustStatus, "entrustStatus", "proofreading")) {
                    operatorInfo = userType2OperatorInfo.get(operatorProofreaderUserType);
                }
                
                if (MapUtils.isNotEmpty(operatorInfo)) {
                    managerUserId = Long.parseLong(operatorInfo.get("userId").toString());
                    managerUserName = operatorInfo.get("userName").toString();
                    managerUserPhone = canDecrypt ?
                            SensitiveInfoUtil.aesDecrypt(operatorInfo.get("accountNameAes").toString())
                            : operatorInfo.get("accountName").toString();
                }
            }
            entrust.put("managerUserId", managerUserId);
            entrust.put("managerUserName", managerUserName);
            entrust.put("managerUserPhone", managerUserPhone);
        }
        
        return MapUtil.of("count", count, "entrustList", entrustList);
    }
    
    /**
     * 产品顾问 运营人员 的列表
     * @param params
     *   - userType   用户类型
     *   - [courseId] 课程id
     *   - [schoolId] 学校id
     *   - [entrustStatus] 委托状态 多个用空格隔开 123,456,798
     *   - [proofreadStatus] 协助校对状态
     *   - [entrustUrgency] 优先级
     *   - [deliverType] 交付类型
     *   - [startTimeLeft][startTimeRight] 开始时间
     *   - [searchFiled] 关键字查询类型 partTimeProofreaderUserName(校对人姓名)、entrusterUserName(委托人姓名)、typistUserName(录入员姓名)、entrustId(录题编号)
     *   - [search]   关键字查询值
     *   - [sortField] entrustId(default)/startTimestampSecond
     *   - [sortType]  asc / desc(default)
     * @return count entrustList
     *    entrustId,         委托id
     *    entrustStatus,     委托状态
     *    entrustStatusOld,  委托旧状态
     *    schoolId           学校
     *    schoolName         学校
     *    courseId,          课程id
     *    courseName,        课程名称
     *    entrusterUserId    委托人
     *    entrusterUserName  委托人
     *    createDateTime     创建时间
     *    timeoutTimestampSecond  状态的超时时间
     *    isTimeout               状态是否超时
     *    examName
     *    paperName
     *    personInChargeUserId,    负责人 录入状态需要显示录入员
     *    personInChargeUserName   负责人 录入状态需要显示录入员
     */
    private Map<String, Object> getListForManager(Map<String, Object> params) {
        
        Map<String, Object> queryParams = getEntrustQueryParamsForManager(params);
        queryParams.put("entrustTypistStatusInvalid", DictUtil.getDictValue("entrustTypistStatus", "invalid"));

        int count = tikuRepository.selectOne("EntrustQueryMapper.getEntrustListForManagerCount", queryParams);
        if (count == 0) {
            return MapUtil.of("count", count, "entrustList", Collections.emptyList());
        }

        // 先查entrustId 再去查详细的信息 因为有性能问题
        List<Long> entrustIdList = tikuRepository.selectList("EntrustQueryMapper.getEntrustIdListForManager", queryParams);
        if (CollectionUtils.isEmpty(entrustIdList)) {
            return MapUtil.of("count", count, "entrustList", Collections.emptyList());
        }
        queryParams.put("entrustIdList",  entrustIdList);
        List<Map<String, Object>> entrustList =
                tikuRepository.selectList("EntrustQueryMapper.getEntrustListForManager", queryParams);
    
        setEntrustTimeout(entrustList);
    
        // 填充考试信息
        List<Long> paperIdsForQueryExamInfo = entrustList.stream()
                .filter(o -> o.get("answerPaperId") != null)
                .map(o -> Long.parseLong(o.get("answerPaperId").toString()))
                .collect(toList());
        if (CollectionUtils.isNotEmpty(paperIdsForQueryExamInfo)) {
            List<Map<String, Object>> examPaperList = examPaperService.getExamPaperInPaperIds(MapUtil.of(
                    "paperIds", paperIdsForQueryExamInfo
            ));
            Map<Long, Map<String, Object>> examPaperMap = examPaperList.stream()
                    .collect(toMap(item -> Long.valueOf(item.get("paperId").toString()), item -> item));
            for (Map<String, Object> entrust : entrustList) {
                if (entrust.get("answerPaperId") != null) {
                    Map<String, Object> examPaper = examPaperMap.get(Long.valueOf(entrust.get("answerPaperId").toString()));
                    if (MapUtils.isNotEmpty(examPaper)) {
                        entrust.putAll(examPaper);
                    }
                }
            }
        }
    
        return MapUtil.of("count", count, "entrustList", entrustList);
    }
    
    
    /**
     * 产品顾问 运营人员 审核时获取下一个entrustInfo
     * @param params
     *   - userType          用户类型
     *   - entrustMarkStatus 当前委托的标记状态
     *   - entrustId         当前委托的id
     *   - sortFieldValue    当前的排序字段的值
     *   - sortField         entrustId or startTimestampSecond or createDateTime
     *   - [sortType]        asc / desc(default)
     *   - [courseId]        课程id
     *   - [schoolId]        学校id
     *   - [entrustStatus]   委托状态 多个用空格隔开 123,456,798
     *   - [search]          entrustId 考试名称 答题卡id 创建人名称
     * @return count entrustList
     *    entrustId,           委托id
     *    createDateTime,      创建时间
     *    startTimestampSecond 当前状态开始时间
     *    entrustMarkStatus    委托录题标记状态
     */
    public Map<String, Object> getNextEntrustForManagerForProcessing(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("sortField")
                .isNotBlank("sortFieldValue")
                .isInteger("entrustMarkStatus")
                .isValidId("entrustId")
                .verify();
        String sortField = MapUtil.getTrim(params, "sortField");
        Set<String> sortFieldSet = Stream.of("entrustId", "startTimestampSecond", "createDateTime").collect(toSet());
        if (!sortFieldSet.contains(sortField)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "排序字段不支持");
        }
        Map<String, Object> queryParams = getEntrustQueryParamsForManager(params);
        MapUtil.copy(params, queryParams, "sortFieldValue", "entrustMarkStatus", "entrustId");
        return tikuRepository.selectOne("EntrustQueryMapper.getNextEntrustForManagerForProcessing", queryParams);
    }
    
    /**
     * 获取到管理员的查询参数
     * @param params
     *      - userType   用户类型
     *      - [courseId] 课程id
     *      - [schoolId] 学校id
     *      - [entrustStatus] 委托状态 多个用空格隔开 123,456,798
     *      - [proofreadStatus] 协助校对状态
     *      - [entrustUrgency] 优先级
     *      - [deliverType] 交付类型
     *      - [startTimeLeft][startTimeRight] 开始时间
     *      - [searchFiled] 关键字查询类型 partTimeProofreaderUserName(校对人姓名)、entrusterUserName(委托人姓名)、typistUserName(录入员姓名)、entrustId(录题编号)
     *      - [search]   关键字查询值
     *      - [sortField]   默认entrustId   startTimestampSecond createDateTime
     *      - [sortType]    默认 asc / desc(default)
     * @return 查询参数
     */
    private Map<String, Object> getEntrustQueryParamsForManager(Map<String, Object> params) {
        int currentUserType = Integer.parseInt(params.get("userType").toString());
        Map<String, Object> queryParams = new HashMap<>();
        MapUtil.copy(params, queryParams, "pageSize", "pageNo", "currentIndex", "sortField", "sortType",
                "proofreadStatus", "entrustUrgency", "deliverType");
        
        // 允许查询的委托状态
        List<Integer> validQueryStatusList = getEntrustStatusList(
                "draft",             // 1草稿
                "rejected",              // 10被退回
                "pendingReview",         // 4待审核
                "autoAppoint",           // 11指派池
                "dnProcessing",          // 12录入中
                "processingRejected",    // 18录入退回
                "proofreading",          // 8校对中
                "yiqiQuestionPushing",   // 15校正推送中
                "yiqiProofreading",      // 16校正中
                "yiqiProofreadingFinish",// 17校正完成
                "complete"               // 9已完成
        );
        // 查询的状态
        List<Integer> queryEntrustStatusList = checkAndGetQueryStatusList(params, validQueryStatusList);
        queryParams.put("queryEntrustStatusList", queryEntrustStatusList);
        
        // courseId
        if (ObjectUtil.isValidId(params.get("courseId"))) {
            queryParams.put("courseId", params.get("courseId"));
        }
        
        // 学校id处理 先加虚拟学校的 这个大家都有
        List<Long> schoolIdList = entrustVirtualInfo.getVirtualSchoolIdList();
        if (DictUtil.isEquals(currentUserType, "userType", "product")) {
            // 获取产品顾问所负责的所有学校schoolIdList
            schoolIdList.addAll(baseSchoolService.selectMaintainSchoolIdsByUserId(MapUtil.of("userId", params.get("userId"))));
        } else {
            // 运营人员负责的所有学校schoolIdList
            schoolIdList.addAll(schoolOperatorService.getSchoolIdListByUserId(MapUtil.of("userId", params.get("userId"))));
        }
        // 校验学校id，如果没有提供，则使用schoolIdList，如果提供，则校验schoolId是否合法
        if (params.get("schoolId") != null && ObjectUtil.isLong(params.get("schoolId"))) {
            Long schoolId = Long.parseLong(params.get("schoolId").toString());
            if (!schoolIdList.contains(schoolId)) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "你没有维护该学校，不允许查看");
            }
            queryParams.put("schoolId", schoolId);
        } else {
            queryParams.put("schoolIdList", schoolIdList);
        }
        if (DictUtil.isEquals(currentUserType, "userType", "operatorProofreader")) {
            queryParams.remove("schoolIdList");
        }
            // 开始时间
        if (!ObjectUtil.isBlank(params.get("startTimeLeft")) && !ObjectUtil.isBlank(params.get("startTimeRight"))) {
            CommonUtil.handleDateTimeParams(params, "startTimeLeft", "startTimeRight");
            Date startTimeLeftDate;
            Date startTimeRightDate;
            try {
                startTimeLeftDate = DateUtil.parseDateTime(MapUtil.getString(params, "startTimeLeft"));
                startTimeRightDate = DateUtil.parseDateTime(MapUtil.getString(params, "startTimeRight"));
            } catch (ParseException e) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "开始时间解析失败!");
            }
            queryParams.put("startTimeLeft", startTimeLeftDate.getTime() / 1000);
            queryParams.put("startTimeRight", startTimeRightDate.getTime() / 1000);
        }
        
        // search 查询字段 模糊查询 entrustId answerPaperId entrusterUserName
        if (!ObjectUtil.isBlank(params.get("search")) && !ObjectUtil.isBlank(params.get("searchField"))) {
            queryParams.put("search", params.get("search").toString().trim());
            queryParams.put("searchField", params.get("searchField").toString().trim());
        }
//        if (search != null) {
//            List<Long> paperIdList = tikuRepository.selectList("EntrustQueryMapper.getAnswerPaperId", queryParams);
//            if (CollectionUtils.isNotEmpty(paperIdList)) {
//                List<Long> matchPaperIdList = BatchDataUtil.submit(paperIdList, (paperIds) ->
//                        examPaperService.getPaperIdByPaperIdsAndExamName(MapUtil.of(
//                                "paperIdList", paperIds,
//                                "search", queryParams.get("search")
//                        )));
//
//                if (CollectionUtils.isNotEmpty(matchPaperIdList)) {
//                    queryParams.put("answerPaperIdList", matchPaperIdList);
//                }
//            }
//        }
    
        // sortField sortType
        Map<String, String> sortFieldMap = new HashMap<>();
        sortFieldMap.put("entrustId", "entrust_id");
        sortFieldMap.put("startTimestampSecond", "start_timestamp_second");
        sortFieldMap.put("createDateTime", "create_date_time");
        sortFieldMap.put(ParamsUtil.SORT_DEFAULT_KEY, "entrust_id");
        ParamsUtil.handlerSortParam(queryParams, sortFieldMap, false);
        
        return queryParams;
    }
    
    /**
     * 题库管理员的列表
     * @param params
     *   - [courseId] 课程id
     *   - [schoolId] 学校id
     *   - [entrustStatus] 委托状态 多个用空格隔开 123,456,798
     *   - [proofreadStatus] 协助校对状态
     *   - [entrustUrgency] 优先级
     *   - [deliverType] 交付类型
     *   - [createDateLeft][createDateRight] 创建时间
     *   - [searchField] 关键字查询类型 partTimeProofreaderUserName(校对人姓名)、entrusterUserName(委托人姓名)、typistUserName(录入员姓名)、entrustId(录题编号)
     *   - [search]   关键字查询值
     *   - [sortField] entrustId(default)/createDateTime
     *   - [sortType]  asc / desc(default)
     * @return count entrustList
     *    entrustId,         委托id
     *    entrustStatus,     委托状态
     *    entrustStatusOld,  委托旧状态
     *    schoolId           学校
     *    schoolName         学校
     *    examName           考试名称
     *    paperName          试卷名称
     *    courseId,          课程id
     *    courseName,        课程名称
     *    entrusterUserId    委托人
     *    entrusterUserName  委托人
     *    createDateTime     创建时间
     *    timeoutTimestampSecond  状态的超时时间
     *    isTimeout               状态是否超时
     */
    private Map<String, Object> getListForSubjectAdmin(Map<String, Object> params) {
        Map<String, Object> queryParams = new HashMap<>();
        MapUtil.copy(params, queryParams, "pageSize", "pageNo", "currentIndex", "sortField",
                "sortType", "proofreadStatus", "entrustUrgency", "deliverType", "createDateLeft", "createDateRight","startTimeLeft","startTimeRight");
    
        // 允许查询的委托状态
        List<Integer> validQueryStatusList = getAllEntrustStatusList();
        // 查询的状态
        List<Integer> queryEntrustStatusList = checkAndGetQueryStatusList(params, validQueryStatusList);
        queryParams.put("queryEntrustStatusList", queryEntrustStatusList);
        
        // courseId
        if (ObjectUtil.isValidId(params.get("courseId"))) {
            queryParams.put("courseId", params.get("courseId"));
        }
        // schoolId
        if (ObjectUtil.isNumeric(params.get("schoolId"))) {
            queryParams.put("schoolId", params.get("schoolId"));
        }
        // search entrustId 模糊查
        if (!ObjectUtil.isBlank(params.get("search")) && !ObjectUtil.isBlank(params.get("searchField"))) {
            queryParams.put("search", params.get("search").toString().trim());
            queryParams.put("searchField", params.get("searchField").toString().trim());
        }

        Integer complete = DictUtil.getDictValue("entrustStatus", "complete");
        boolean isComplete = false;
        if(params.get("entrustStatus") != null){
            int entrustStatus = MapUtil.getInt(params, "entrustStatus");
            if(complete == entrustStatus){
                isComplete = true;
            }
        }else {
            isComplete = true;
        }

        // 创建时间
        if (!ObjectUtil.isBlank(queryParams.get("createDateLeft")) && !ObjectUtil.isBlank(queryParams.get("createDateRight"))) {
            CommonUtil.handleDateTimeParams(queryParams, "createDateLeft", "createDateRight");
            boolean isMoreThanSixMonths = isMoreThanSixMonths(queryParams.get("createDateLeft").toString(), queryParams.get("createDateRight").toString());
            if(isMoreThanSixMonths && isComplete){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "状态为全部/已完成时，筛选时间跨度超过6个月，请缩短时间跨度重新筛选");
            }
        }
        // 发现校对员是这个参数
        if (!ObjectUtil.isBlank(queryParams.get("startTimeLeft")) && !ObjectUtil.isBlank(queryParams.get("startTimeRight"))) {
            CommonUtil.handleDateTimeParams(queryParams, "startTimeLeft", "startTimeRight");
            // 调用方法进行比较
            boolean isMoreThanSixMonths = isMoreThanSixMonths(queryParams.get("startTimeLeft").toString(), queryParams.get("startTimeRight").toString());
            if(isMoreThanSixMonths && isComplete){
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "状态为全部/已完成时，筛选时间跨度超过6个月，请缩短时间跨度重新筛选");
            }
        }

        // sortField sortType
        Map<String, String> sortFieldMap = new HashMap<>();
        sortFieldMap.put("entrustId", "entrust_id");
        sortFieldMap.put("createDateTime", "create_date_time");
        sortFieldMap.put(ParamsUtil.SORT_DEFAULT_KEY, "entrust_id");
        ParamsUtil.handlerSortParam(queryParams, sortFieldMap, false);

        // 关联的录题人员的信息 不查询非当前录题人员的
        queryParams.put("entrustTypistStatusInvalid", DictUtil.getDictValue("entrustTypistStatus", "invalid"));
        
        // 总数
        int count = tikuRepository.selectOne("EntrustQueryMapper.getEntrustListForSubjectAdminCount", queryParams);
        if (count == 0) {
            return MapUtil.of("count", count, "entrustList", Collections.emptyList());
        }
        
        // 先查entrustIdList 因为性能问题
        List<Long> entrustIdList = tikuRepository.selectList("EntrustQueryMapper.getEntrustIdListForSubjectAdmin", queryParams);
        if (CollectionUtils.isEmpty(entrustIdList)) {
            return MapUtil.of("count", count, "entrustList", Collections.emptyList());
        }
        queryParams.put("entrustIdList", entrustIdList);
        
        List<Map<String, Object>> entrustList =
                tikuRepository.selectList("EntrustQueryMapper.getEntrustListForSubjectAdmin", queryParams);
    
        setEntrustTimeout(entrustList);
        
        return MapUtil.of("count", count, "entrustList", entrustList);
    }


    /**
     * 判断两个日期字符串是否相差超过6个月
     *
     * @param dateStr1 第一个日期字符串
     * @param dateStr2 第二个日期字符串
     * @return 如果日期间隔大于6个月，返回 true；否则返回 false
     */
    public static boolean isMoreThanSixMonths(String dateStr1, String dateStr2) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将字符串解析为 LocalDateTime
        LocalDateTime date1 = LocalDateTime.parse(dateStr1, formatter);
        LocalDateTime date2 = LocalDateTime.parse(dateStr2, formatter);

        // 确保 date1 是较晚的日期
        if (date1.isBefore(date2)) {
            LocalDateTime temp = date1;
            date1 = date2;
            date2 = temp;
        }

        // 计算月份差异
        long monthsBetween = ChronoUnit.MONTHS.between(date2, date1);

        // 返回是否大于6个月
        return monthsBetween > 6;
    }
    
    /**
     * 获取学校列表 带虚拟学校的
     * @param params userType
     * @return Map{ count, schoolList[{schoolId, schoolName}]}
     */
    public Map<String, Object> getEntrustSchool(Map<String, Object> params) {
        int currentUserType = Integer.parseInt(params.get("userType").toString());
        
        List<Map<String, Object>> schoolInfoList = new ArrayList<>(entrustVirtualInfo.getVirtualSchoolInfoList());
        
        // 产品顾问 仅获取自己负责的学校
        if (DictUtil.isEquals(currentUserType, "userType", "product")) {
            schoolInfoList.addAll(baseSchoolService.selectMaintainSchoolInfo(MapUtil.of(
                    "userId", Long.valueOf(params.get("userId").toString()))
            ));
        }
        
        // 运营人员
        else if (DictUtil.isEquals(currentUserType, "userType", "operatorReviewer")) {
            // 获取产品顾问所负责的所有学校schoolIdList
            schoolInfoList.addAll(schoolOperatorService.getSchoolInfoListByUserId(MapUtil.of(
                    "userId", Long.valueOf(params.get("userId").toString()))
            ));
        }
        
        // 题库管理员 所有学校
        else if (DictUtil.isEquals(currentUserType, "userType", "admin", "subjectAdmin","operatorProofreader")) {
            schoolInfoList.addAll(baseSchoolService.selectAllSchoolInfo());
        } else {
            schoolInfoList = new ArrayList<>();
        }
        
        //过滤未开启题库的学校
        if (CollectionUtils.isNotEmpty(schoolInfoList)) {
            Map<String, Object> query = new HashMap<>();
            query.put("key", "tiku_enable");
            query.put("artsScience", 0);
            query.put("school", schoolInfoList);
            List<Map<String, Object>> schoolConfigList = moduleConfigService.getAllModuleConfig(query);
            schoolConfigList = schoolConfigList.stream().filter(e -> ObjectUtil.isValueEquals(e.get("status"), "0")).collect(toList());
            if (CollectionUtils.isNotEmpty(schoolConfigList)) {
                Map<Long, List<Map<String, Object>>> schoolMap = schoolConfigList.stream().collect(groupingBy(s -> Long.valueOf(s.get("schoolId").toString())));
                schoolInfoList.removeIf(item -> schoolMap.containsKey(Long.valueOf(item.get("schoolId").toString())));
            }
        }
        
        return MapUtil.of(
                "count", schoolInfoList.size(),
                "schoolList", schoolInfoList);
    }
    
    /**
     * 获取需要推送的entrustIdList
     * @return
     */
    public List<Long> getEntrustIdListForPush() {
        Map<String, Object> params = MapUtil.of(
                "entrustStatusYiqiQuestionPushing", getEntrustStatus("yiqiQuestionPushing"));
        return tikuRepository.selectList("EntrustQueryMapper.getEntrustIdListForPush", params);
    }
    
    /**
     * 校验并获取查询的状态list
     * @param params    [entrustStatus] 查询的委托状态 如果有多个 用逗号隔开 例:  1,4,5
     * @param validQueryStatusList 合法的entrustStatusList
     * @return 查询的entrustStatusList 必定有值 如果没有合法的查询entrustStatus会抛异常
     */
    private List<Integer> checkAndGetQueryStatusList(Map<String, Object> params, List<Integer> validQueryStatusList) {
        List<Integer> queryEntrustStatusList;
        if (params.get("entrustStatus") != null) {
            queryEntrustStatusList = Stream
                    .of(params.get("entrustStatus").toString().split(","))
                    .map(Integer::parseInt)
                    .distinct()
                    .collect(toList());
            queryEntrustStatusList.retainAll(validQueryStatusList);
        } else {
            queryEntrustStatusList = validQueryStatusList;
        }
        if (CollectionUtils.isEmpty(queryEntrustStatusList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "您无法查询需要的委托状态的对应信息");
        }
        return queryEntrustStatusList;
    }
    
    /**
     * 获取所有的entrustStatus值List
     * @return 所有的entrustStatus值List
     */
    private List<Integer> getAllEntrustStatusList() {
        return getEntrustStatusList(
                "draft",                  // 1草稿
                "rejected",               // 10被退回
                "pendingReview",          // 4待审核
                "autoAppoint",            // 11指派池
                "processingRejected",     // 18录入退回
                "dnProcessing",           // 12录入中
                "proofreading",           // 8校对中
                "yiqiQuestionPushing",    // 15校正推送中
                "yiqiProofreading",       // 16校正中
                "yiqiProofreadingFinish", // 17校正完成
                "complete"                // 9已完成
        );
    }
    
    /**
     * 获取委托状态entrustStatusList
     * @param entrustStatusEnNames 详见 entrustStatus.enName
     * @return 委托状态entrustStatusList
     */
    private List<Integer> getEntrustStatusList(String... entrustStatusEnNames) {
        List<Integer> validQueryStatusList = new ArrayList<>();
        if (entrustStatusEnNames == null || entrustStatusEnNames.length == 0) {
            return validQueryStatusList;
        }
        for (String entrustStatusEnName : entrustStatusEnNames) {
            validQueryStatusList.add(getEntrustStatus(entrustStatusEnName));
        }
        return validQueryStatusList;
    }
    
    /**
     * 获取委托状态值
     * @param entrustStatusEnName entrustStatus.enName
     * @return 委托状态值
     */
    private int getEntrustStatus(String entrustStatusEnName) {
        return DictUtil.getDictValue("entrustStatus", entrustStatusEnName);
    }
    /**
     * 设置超时标记
     *   对于每一个entrust
     *  如果有 timeoutTimestampSecond超时时间戳字段>0 且当前时间>timeoutTimestampSecond 则为超时
     * @param entrustList
     *   对每一个entrust插入isTimeout字段 true/false
     */
    private void setEntrustTimeout(List<Map<String, Object>> entrustList) {
        long currentTimestampSecond = System.currentTimeMillis() / 1000;
        // 判断是否超时
        for (Map<String, Object> entrust : entrustList) {
            boolean isTimeout = false;
            if (ObjectUtil.isValidId(entrust.get("timeoutTimestampSecond"))) {
                long timeoutTimestampSecond = Long.parseLong(entrust.get("timeoutTimestampSecond").toString());
                if (currentTimestampSecond > timeoutTimestampSecond) {
                    isTimeout = true;
                }
            }
            entrust.put("isTimeout", isTimeout);
        }
    }

    /**
     * 通过paperId批量查询
     * @param params
     * @return
     */
    public List<Map<String, Object>> getEntrustByPaperIds(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("paperIdList")
                .verify();
        return tikuRepository.selectList("EntrustQueryMapper.getEntrustByPaperIds", params);
    }

    /**
     * 获取委托录题的业务类型
     * @param entrustId entrustId
     * @return entrustBusinessType
     */
    public int getEntrustBusinessType(long entrustId) {
        Map<String, Object> entrust = getEntrustByEntrustId(entrustId);
        return EntrustUtil.getEntrustBusinessType(entrust);
    }

    /**
     * 是不是金卷业务
     * @param entrustId entrustId
     * @return true 是
     */
    public boolean isJinjuanBusiness(long entrustId) {
        int businessType = getEntrustBusinessType(entrustId);
        return EntrustUtil.isJinjuanBusiness(businessType);
    }


    /**
     * 获取关联委托的列表
     * @param params schoolId courseId
     * @return
     */
    public Map<String, Object> getRelationEntrust(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("courseId")
                .verify();

        Map<String, Object> queryParams = new HashMap<>(params);
        Map<String, Object> result = new HashMap<>();

        int pageSize = MapUtil.getInt(params, "pageSize");
        queryParams.remove("pageNo");
        queryParams.remove("pageSize");

        List<Integer> validQueryStatusList = getEntrustStatusList(
                "pendingReview",         // 4待审核
                "proofreading",          // 8校对中
                "autoAppoint",           // 11指派池
                "dnProcessing",          // 12录入中
                "processingRejected"     // 18录入退回
        );

        List<Integer> validExamPaperQueryStatusList = getEntrustStatusList(
                "yiqiQuestionPushing",   // 15校正推送中
                "yiqiProofreading",      // 16校正中
                "yiqiProofreadingFinish",// 17校正完成
                "complete"               // 9已完成
        );

        queryParams.put("validQueryStatusList", validQueryStatusList);
        queryParams.put("validExamPaperQueryStatusList", validExamPaperQueryStatusList);

        // 录入阶段的委托
        List<Map<String, Object>> relationEntrustList4Input = tikuRepository.selectList("EntrustQueryMapper.getRelationEntrust4Input", queryParams);

        // 校正阶段的委托
        List<Map<String, Object>> relationEntrustList4Check = tikuRepository.selectList("EntrustQueryMapper.getRelationEntrust4Check", queryParams);

        List<Long> answerPaperIdList = relationEntrustList4Check.stream()
                .filter(item -> ObjectUtil.isNotBlank(item.get("answerPaperId")))
                .map(item -> MapUtil.getLong(item, "answerPaperId"))
                .collect(Collectors.toList());
        final List<Map<String, Object>> entrustList = Lists.newArrayList(relationEntrustList4Input);
        // 查询t_exam_paper是否有值
        if (CollectionUtils.isNotEmpty(answerPaperIdList)) {
            // 获取关联的父子试卷
            Bson query = or(in("paperId", answerPaperIdList), in("sourcePaperId", answerPaperIdList));
            List<Document> allPaper = paperManager.getList(query, new String[]{"paperId", "sourcePaperId"}, new String[]{"_id"});
            List<Long> allPaperIds = allPaper.stream()
                    .map(i -> MapUtil.getLong(i, "paperId"))
                    .distinct()
                    .collect(toList());
            Map<Long, Set<Long>> paperHierarchy = mapToHierarchy(allPaper);

            if (CollectionUtils.isNotEmpty(allPaperIds)) {
                List<Map<String, Object>> examPaperList = examPaperService.getExamPaperInPaperIds(MapUtil.of("paperIds", allPaperIds));
                Set<Long> paperIdSet = examPaperList.stream().map(item -> MapUtil.getLong(item, "paperId")).collect(toSet());

                // 如果当前试卷或其关联试卷发布过考试 - 剔除
                for (Map<String, Object> relationEntrustItem : relationEntrustList4Check) {
                    // 取不到默认-1L吧,少写个判断
                    Long answerPaperId = MapUtil.getLong(relationEntrustItem, "answerPaperId", -1L);
                    Set<Long> relationPaperId = paperHierarchy.getOrDefault(answerPaperId, Sets.newHashSet(answerPaperId));
                    if (Sets.intersection(relationPaperId, paperIdSet).isEmpty()) {
                        entrustList.add(relationEntrustItem);
                    }
                }
            }
        }

        int size = entrustList.size();
        List<Map<String, Object>> resultEntrustList = entrustList.stream()
                .sorted(Comparator.comparing(item -> MapUtil.getLong(item, "entrustId"), Comparator.reverseOrder()))
                .skip(MapUtil.getInt(params, "currentIndex"))
                .limit(pageSize)
                .collect(toList());

        result.put("count", size);
        result.put("entrustList", resultEntrustList);

        return result;
    }

    /**
     * 按照父子关系划分数据
     *
     * @param paperList 试卷 paperId sourcePaperId
     * @return 父试卷paperId -> 关联的父子试卷paperId
     */
    private Map<Long, Set<Long>> mapToHierarchy(List<Document> paperList) {
        Map<Long, Set<Long>> result = new HashMap<>();
        for (Document paper : paperList) {
            Long sourcePaperId = MapUtil.getLongNullable(paper, "sourcePaperId");
            Long paperId = MapUtil.getLongNullable(paper, "paperId");

            if (sourcePaperId == null) {
                result.computeIfAbsent(paperId, k -> new HashSet<>()).add(paperId);
            } else {
                result.computeIfAbsent(sourcePaperId, k -> new HashSet<>()).add(paperId);
            }
        }

        return result;
    }

    /**
     * <pre>
     * 对委托进行排序
     * 排序字段: entrustUrgency==100一级加急 200个册加急 300二级加急 400低优先级 500无标记
     *          entrustMarkStatus==1不校对
     * 实际上entrustMarkStatus==1不校对 与 entrustUrgency 俩个字段会同时存在, 但排序以entrustMarkStatus==1不校对为准
     * -------------------------------------------------------------------------
     * 规则: 一级加急 > 个册加急 > 二级加急 > 无标记 > 低优先级 > 不校对
     *      各阶段相同再按entrustId升序排序
     * </pre>
     * @param entrustList -entrustMarkStatus  -entrustId  -entrustUrgency
     */
    public void sort(List<Map<String, Object>> entrustList) {
        // 挑出不校对的按entrustId排序
        List<Map<String, Object>> noProofreadList = new ArrayList<>();
        entrustList.removeIf(i -> {
            if (MapUtil.getInt(i, "entrustMarkStatus") == 1) {
                noProofreadList.add(i);
                return true;
            }
            return false;
        });
        noProofreadList.sort(Comparator.comparing(i -> MapUtil.getLong(i, "entrustId")));
        // 按加急程度和entrustId排序
        entrustList.sort(Comparator.comparing((Map<String, Object> i) -> MapUtil.getInt(i, "entrustUrgency"))
                .thenComparing(i -> MapUtil.getLong(i, "entrustId")));
        // 将不校对的放在末尾
        entrustList.addAll(noProofreadList);
    }

    /**
     * 获取需要更新个册等级的任务
     *
     * @param dateTime 修改时间起始值
     * @return entrustId paperId deliverType
     */
    public List<Map<String, Object>> getNeedUpdateTask(Date dateTime) {
        // 个册加急任务 + 为完成的任务
        List<Map<String, Object>> l1 = tikuRepository.selectList("EntrustQueryMapper.getWrongBookUrgencyNotComplete");
        // 个册加急任务 + 已完成的任务，且modifyDateTime >= dateTime
        List<Map<String, Object>> l2 = tikuRepository.selectList("EntrustQueryMapper.getWrongBookUrgencyComplete", dateTime);
        l1.addAll(l2);
        return l1;
    }

    /**
     * 根据entrust检查试卷是否有处于”内容生产“状态下的个册任务
     *
     * @param entrustIdList 知识点标注任务id列表
     * @return 有”内容生产“状态下的个册任务的 知识点标注任务id列表
     */
    public List<Long> checkWrongBookStatusByEntrustIds(List<Long> entrustIdList) {
        if (CollectionUtils.isEmpty(entrustIdList)) {
            return Collections.emptyList();
        }

        return tikuRepository.selectList("EntrustQueryMapper.checkWrongBookStatusByEntrustIds", entrustIdList);
    }
}
