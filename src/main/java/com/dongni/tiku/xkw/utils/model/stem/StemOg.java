/*
 * xkw.com Inc. Copyright (c) 2022 All Rights Reserved.
 */
package com.dongni.tiku.xkw.utils.model.stem;

import com.dongni.commons.utils.JSONUtil;

import java.util.List;

/**
 *
 * 选项组
 *
 * <AUTHOR>
 * @version 1.0
 * date 2022年05月24日
 */
public class StemOg {
    /**
     * 每行显示的选项个数
     */
    private Integer cols = 1;
    /**
     * 选项组
     */
    private List<StemOgOp> ogOps;
    
    public Integer getCols() {
        return cols;
    }
    
    public StemOg setCols(Integer cols) {
        this.cols = cols;
        return this;
    }
    
    public List<StemOgOp> getOgOps() {
        return ogOps;
    }
    
    public StemOg setOgOps(List<StemOgOp> ogOps) {
        this.ogOps = ogOps;
        return this;
    }
    
    @Override
    public String toString() {
        return JSONUtil.toJson(this);
    }
}
