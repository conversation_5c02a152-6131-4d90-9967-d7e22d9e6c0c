package com.dongni.tiku.xkw.client;

import com.dongni.commons.utils.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 * 2022/07/20
 */
public class XkwJson {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_EMPTY)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false)
            .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE)
            ;
    
    public static String toJson(Object obj) {
        if (obj == null) { return null; }
        return JSONUtil.toJson(OBJECT_MAPPER, obj);
    }
    
    
    /**
     * 将json字符串转换为对象 javaType形式
     *
     * @param s json字符串
     */
    public static <T> T parse(String s, JavaType javaType) {
        if (StringUtils.isBlank(s)) { return null; }
        return JSONUtil.parse(OBJECT_MAPPER, s, javaType);
    }
    
    /**
     * 将json字符串转换为对象 javaType形式
     *
     * @param s json字符串
     */
    public static <T> T parse(String s, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(s)) { return null; }
        return JSONUtil.parse(OBJECT_MAPPER, s, typeReference);
    }
}
