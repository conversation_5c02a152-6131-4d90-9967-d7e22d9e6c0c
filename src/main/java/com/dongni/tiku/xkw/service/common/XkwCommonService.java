package com.dongni.tiku.xkw.service.common;

import com.dongni.basedata.export.area.service.CommonAreaService;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.TreeUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.xkw.client.XkwPager;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Tuple;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toMap;

/**
 *
 * <AUTHOR>
 * 2022/07/21
 */
@Service
public class XkwCommonService {
    
    @Autowired
    private XkwCommonApi xkwCommonApi;
    
    @Autowired
    private CommonCourseService commonCourseService;
    
    @Autowired
    private CommonAreaService commonAreaService;
    
    /**
     * 缓存命名空间
     * 所有缓存都会放在该命名空间下 如课程信息 TIKU:XOP:COMMON:V1:COURSES
     * 如果有更改缓存的数据字段，需要使用超管账户清除缓存，或者等待缓存过期
     * 本地修改测试时，可更改该命名空间
     */
    private static final String CACHE_NAMESPACE = "TIKU:XOP:COMMON:V1:";
    
    /**
     * 获取关联的课程信息 懂你-学科网
     *    关联课程仅局限于 k12课程及初中科学+高中信息技术+高中通用技术 见{@link #getSupportDongniCourseList()}
     * @return {@link #getRelativeCourseInfoList(List)}
     */
    public List<Map<String, Object>> getRelativeCourseInfoListCacheable() {
        String cacheDataName = "COURSES";
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP, this::getRelativeCourseInfoList);
    }
    
    /**
     * 获取关联的课程信息 懂你-学科网
     *    关联课程仅局限于 k12课程及初中科学+高中信息技术+高中通用技术 见{@link #getSupportDongniCourseList()}
     * @return {@link #getRelativeCourseInfoList(List)}
     */
    public List<Map<String, Object>> getRelativeCourseInfoList() {
        return getRelativeCourseInfoList(getSupportDongniCourseList());
    }
    
    /**
     * 获取关联的课程信息 懂你-学科网
     * @param dongniCourseList [{courseId courseName stage}] 懂你课程 用于匹配学科网的课程
     * @return [{}]
     *     "courseId",  2,
     *     "courseName", "语文",
     *     "stage", 3,
     *     "stageName": "高中",
     *     "xkwCourseId", 26,
     *     "xkwCourseName", "高中语文",
     *     "xkwStageId", 4,
     *     "xkwSubjectId", 1
     *     "xkwStageName", "高中"
     *     "xkwStageCode", "SENIOR_MIDDLE"
     *     "xkwSubjectName", "语文"
     *
     *     注:  xkwSubjectName 政治（品德）- xkwCourseName 高中政治
     *     注:  xkwSubjectName 政治（品德）- xkwCourseName 初中道德与法治
     *     注:  stageName 初中 courseName 生物 - xkwCourseName 初中生物学
     *     注:  stageName 高中 courseName 生物 - xkwCourseName 高中生物学
     *     注:  stageName 高中 courseName 政治 - xkwCourseName 高中思想政治
     */
    public List<Map<String, Object>> getRelativeCourseInfoList(List<Map<String, Object>> dongniCourseList) {
        if (CollectionUtils.isEmpty(dongniCourseList)) { return new ArrayList<>(); }
        
        Map<Integer, String> stage2Name = getDnStage2Name();
        Map<String, Map<String, Object>> xkwCourseName2Info = getXkwCourseName2Info();
        Map<Long, Map<String, Object>> xkwSubjectId2Info = getXkwSubjectId2Info();
        Map<Long, Map<String, Object>> xkwStageId2Info = getXkwStageId2Info();
        
        Map<String, String> dnStageCourseName2xkwCourseName = new HashMap<>(16);
        dnStageCourseName2xkwCourseName.put("初中生物", "初中生物学");
        dnStageCourseName2xkwCourseName.put("高中生物", "高中生物学");
        dnStageCourseName2xkwCourseName.put("高中政治", "高中思想政治");
        
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> dongniCourse : dongniCourseList) {
            String courseName = MapUtil.getString(dongniCourse, "courseName");
            int stage = MapUtil.getInt(dongniCourse, "stage");
            String stageName = stage2Name.get(stage);
            if (stageName == null) {
                continue;
            }
            // 小学语文 学科网的课程名称
            String stageCourseName = stageName + courseName;
            Map<String, Object> xkwCourseInfo = xkwCourseName2Info.get(stageCourseName);
            if (MapUtils.isEmpty(xkwCourseInfo)) {
                // 如果获取不到 可能需要映射，这样做之后，学科网要是将初中生物学改成初中生物也能成功映射了
                String xkwCourseName = dnStageCourseName2xkwCourseName.get(stageCourseName);
                if (StringUtils.isBlank(xkwCourseName)) {
                    continue;
                }
                xkwCourseInfo = xkwCourseName2Info.get(xkwCourseName);
                if (MapUtils.isEmpty(xkwCourseInfo)) {
                    continue;
                }
            }
        
            long xkwSubjectId = MapUtil.getLong(xkwCourseInfo, "subjectId");
            long xkwStageId = MapUtil.getLong(xkwCourseInfo, "stageId");
            Map<String, Object> xkwSubjectInfo = xkwSubjectId2Info.get(xkwSubjectId);
            Map<String, Object> xkwStageInfo = xkwStageId2Info.get(xkwStageId);
        
            Map<String, Object> result = new HashMap<>(11);
            
            result.put("courseId", MapUtil.getLong(dongniCourse, "courseId"));
            result.put("courseName", courseName);
            result.put("stage", stage);
            result.put("stageName", stageName);
        
            result.put("xkwCourseId", MapUtil.getLong(xkwCourseInfo, "id"));
            result.put("xkwCourseName", MapUtil.getString(xkwCourseInfo, "name"));
            result.put("xkwStageId", xkwStageId);
            result.put("xkwSubjectId", xkwSubjectId);
            result.put("xkwStageName", MapUtil.getStringNullable(xkwStageInfo, "name"));
            result.put("xkwStageCode", MapUtil.getStringNullable(xkwStageInfo, "code"));
            result.put("xkwSubjectName", MapUtil.getStringNullable(xkwSubjectInfo, "name"));
        
            resultList.add(result);
        }
    
        return resultList;
    }
    
    /**
     * 获取懂你年段信息map
     * @return 年段 -> 名称
     *         如: 1->小学   2->初中
     */
    public Map<Integer, String> getDnStage2Name() {
        return getDnDickKey2StageInfo().values().stream()
                .map(MapUtil::getMap)
                .collect(toMap(stageInfo -> MapUtil.getInt(stageInfo, "value"), stageInfo -> MapUtil.getString(stageInfo, "label")));
    }
    
    /**
     * 获取懂你年段信息map
     * @return 名称 -> 年段
     *         如: 小学->1  初中->2
     */
    public Map<String, Integer> getDnStageName2Stage() {
        return getDnDickKey2StageInfo().values().stream()
                .map(MapUtil::getMap)
                .collect(toMap(stageInfo -> MapUtil.getString(stageInfo, "label"), stageInfo -> MapUtil.getInt(stageInfo, "value")));
    }
    
    private Map<String, Object> getDnDickKey2StageInfo() {
        // sys_dict.en_name = stage
        Map<String, Object> key2stageInfo = DictUtil.getDict("stage");
        if (MapUtils.isEmpty(key2stageInfo)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "字典stage未配置");
        }
        return key2stageInfo;
    }
    
    /**
     * 获取学科网课程信息map
     * @return id(courseId) -> id(courseId) name stageId subjectId
     */
    public Map<String, Map<String, Object>> getXkwCourseName2Info() {
        return xkwCommonApi.getCoursesAll().stream()
                .collect(toMap(item -> MapUtil.getString(item, "name"), item -> item));
    }
    
    /**
     * 获取学科网的学科信息map
     * @return id(subjectId) -> id(subjectId) name
     */
    public Map<Long, Map<String, Object>> getXkwSubjectId2Info() {
        return xkwCommonApi.getSubjects().stream()
                .collect(toMap(item -> MapUtil.getLong(item, "id"), item -> item));
    }
    
    /**
     * 获取学科网的学段信息map
     * @return id(stageId) -> id(stageId) name code
     */
    public Map<Long, Map<String, Object>> getXkwStageId2Info() {
        return xkwCommonApi.getStages().stream()
                .collect(toMap(item -> MapUtil.getLong(item, "id"), item -> item));
    }
    
    /**
     * 获取关联的课程信息 懂你-学科网
     * @param params courseId 懂你的courseId
     * @return 关联的课程信息 or null
     */
    public Map<String, Object> getRelativeCourseInfoCacheableNullable(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();
        long courseId = MapUtil.getLong(params, "courseId");
        List<Map<String, Object>> relativeCourseInfoList = getRelativeCourseInfoListCacheable();
        return relativeCourseInfoList.stream()
                .filter(item -> courseId == MapUtil.getLong(item, "courseId"))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取关联的课程信息 懂你-学科网
     * @param params courseId 懂你的courseId
     * @return 关联的课程信息 or null
     */
    public Map<String, Object> getRelativeCourseInfoNullable(Map<String, Object> params) {
        Verify.of(params).isValidId("courseId").verify();
        long courseId = MapUtil.getLong(params, "courseId");
        List<Map<String, Object>> relativeCourseInfoList = getRelativeCourseInfoList();
        return relativeCourseInfoList.stream()
                .filter(item -> courseId == MapUtil.getLong(item, "courseId"))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取关联的课程信息 懂你-学科网
     * @param params courseId 懂你的courseId
     * @return 关联的课程信息 or 异常
     */
    public Map<String, Object> getRelativeCourseInfoCacheable(Map<String, Object> params) {
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfoCacheableNullable(params);
        if (MapUtils.isEmpty(relativeCourseInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学科网不支持该课程");
        }
        return relativeCourseInfo;
    }
    
    /**
     * 获取关联的课程信息 懂你-学科网
     * @param params courseId 懂你的courseId
     * @return 关联的课程信息 or 异常
     */
    public Map<String, Object> getRelativeCourseInfo(Map<String, Object> params) {
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfoNullable(params);
        if (MapUtils.isEmpty(relativeCourseInfo)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "学科网不支持该课程");
        }
        return relativeCourseInfo;
    }
    
    /**
     * 获取懂你支持的课程list
     *     k12课程 + 小学科学 + 小学道德与法治 + 初中科学 + 高中信息技术 + 高中通用技术
     * @return [{courseId courseName stage}]
     */
    private List<Map<String, Object>> getSupportDongniCourseList() {
        List<Map<String, Object>> courseList = commonCourseService.getK12Course();
        List<Long> courseIdList = Stream.of(
                28L,    // 小学科学
                226L,   // 小学道德与法治
                1605L,  // 初中科学
                25L,    // 高中信息技术
                26L     // 高中通用技术
        ).collect(Collectors.toList());
        List<Map<String, Object>> otherCourseList = commonCourseService.getCourseInfoListByCourseIdList(courseIdList);
        if (CollectionUtils.isNotEmpty(otherCourseList)) {
            courseList.addAll(otherCourseList);
        }
        return courseList;
    }
    
    /**
     * 获取行政区域 懂你只用到省级(PROVINCE)
     * {@link XkwCommonApi#getAreasAll()}
     * @return [{}]
     *     areaId=440000,
     *     areaName=广东省,
     *     areaCode=44,
     *     level=1,
     *     parentAreaId=1,
     *     parentAreaCode=null,
     *     xkwAreaId=440000,
     *     xkwAreaName=广东省,
     *     xkwAreaShortName=广东,
     *     xkwAreaParentId=0,
     *     xkwLevel=PROVINCE,
     *     xkwAreaOrdinal=44,
     */
    public List<Map<String, Object>> getRelativeAreaListCacheable() {
        String cacheDataName = "AREAS";
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> {
                    List<Map<String, Object>> xkwAllAreaList = xkwCommonApi.getAreasAll();
                    // 懂你只用到省级
                    List<Map<String, Object>> xkwProvinceAreaList = xkwAllAreaList.stream()
                            .filter(item -> "PROVINCE".equals(MapUtil.getTrimNullable(item, "level")))
                            .collect(Collectors.toList());
                    
                    // areaId, areaName, parentAreaId, parentAreaCode, level, areaCode
                    List<Map<String, Object>> allAreaList = commonAreaService.getAllArea();
                    List<Map<String, Object>> level1AreaList = allAreaList.stream()
                            .filter(areaInfo -> 1 == MapUtil.getInt(areaInfo, "level"))
                            .collect(Collectors.toList());
                    // areaName -> areaInfo
                    Map<String, Map<String, Object>> areaName2AreaInfo = level1AreaList.stream()
                            .collect(toMap(item -> MapUtil.getString(item, "areaName"), item -> item));
    
                    List<Map<String, Object>> resultList = new ArrayList<>();
                    for (Map<String, Object> xkwProvinceArea : xkwProvinceAreaList) {
                        // 学科网 广东省         懂你 广东省
                        // 学科网 广西壮族自治区  懂你 广西自治区
                        // 学科网 北京时         懂你 北京
                        String xkwAreaName = MapUtil.getString(xkwProvinceArea, "name");
                        // 广西
                        String xkwAreaShortName = MapUtil.getString(xkwProvinceArea, "shortName");
                        // 先进行名称全等匹配
                        Map<String, Object> areaInfo = areaName2AreaInfo.remove(xkwAreaName);
                        if (MapUtils.isEmpty(areaInfo)) {
                            // 再进行包含匹配
                            Set<String> areaNameSet = new HashSet<>(areaName2AreaInfo.keySet());
                            for (String areaName : areaNameSet) {
                                if (areaName.contains(xkwAreaShortName)) {
                                    areaInfo = areaName2AreaInfo.remove(areaName);
                                    break;
                                }
                            }
                        }
                        if (MapUtils.isEmpty(areaInfo)) {
                            continue;
                        }
    
                        // areaId, areaName, parentAreaId, parentAreaCode, level, areaCode
                        Map<String, Object> result = new HashMap<>(areaInfo);
                        result.put("xkwAreaId", MapUtil.getString(xkwProvinceArea, "id"));
                        result.put("xkwAreaName", xkwAreaName);
                        result.put("xkwAreaShortName", xkwAreaShortName);
                        result.put("xkwLevel", MapUtil.getString(xkwProvinceArea, "level"));
                        result.put("xkwAreaParentId", MapUtil.getString(xkwProvinceArea, "parentId"));
                        result.put("xkwAreaOrdinal", MapUtil.getLong(xkwProvinceArea, "ordinal"));
                        resultList.add(result);
                    }
                    return resultList;
                }
        );
    }
    
    /**
     * 获取试题难度等级列表
     * @return [{}]
     *     xkwQuestionDifficultyId       难度id
     *     xkwQuestionDifficultyName     难度名称
     *     xkwQuestionDifficultyFlooring 难度档下限值
     *     xkwQuestionDifficultyCeiling  难度档上限值
     */
    public List<Map<String, Object>> getQuestionDifficultyListCacheable() {
        String cacheDataName = "QD";
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> {
                    List<Map<String, Object>> questionDifficultyList = xkwCommonApi.getQuestionDifficulties();
                    List<Map<String, Object>> resultList = new ArrayList<>();
                    for (Map<String, Object> questionDifficulty : questionDifficultyList) {
                        Map<String, Object> result = new HashMap<>(4);
                        result.put("xkwQuestionDifficultyId", MapUtil.getLong(questionDifficulty, "id"));
                        result.put("xkwQuestionDifficultyName", MapUtil.getString(questionDifficulty, "name"));
                        result.put("xkwQuestionDifficultyFlooring", MapUtil.getDouble(questionDifficulty, "flooring"));
                        result.put("xkwQuestionDifficultyCeiling", MapUtil.getDouble(questionDifficulty, "ceiling"));
                        resultList.add(result);
                    }
                    return resultList;
                }
                );
    }
    
    /**
     * 获取课程的题型
     * {@link XkwCommonApi#getQuestionTypes(Long)}
     * @param params courseId 懂你的courseId
     * @return [{}]
     *   courseId=2,
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwQuestionTypeId=261701,
     *   xkwQuestionTypeName=选择类,
     *   xkwQuestionTypeParentId=2617
     *   xkwQuestionTypeObjective=false,
     *   xkwQuestionTypeOrdinal=1,
     */
    public List<Map<String, Object>> getQuestionTypeListCacheable(Map<String, Object> params) {
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfoCacheable(params);
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        String cacheDataName = "QT:" + xkwCourseId;
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> {
                    List<Map<String, Object>> questionTypeList = xkwCommonApi.getQuestionTypes(xkwCourseId);
                    List<Map<String, Object>> resultList = new ArrayList<>();
                    for (Map<String, Object> questionType : questionTypeList) {
                        Map<String, Object> result = new HashMap<>(9);
                        result.put("courseId", MapUtil.getLong(relativeCourseInfo, "courseId"));
                        result.put("courseName", MapUtil.getString(relativeCourseInfo, "courseName"));
                        result.put("xkwCourseId", xkwCourseId);
                        result.put("xkwCourseName", MapUtil.getString(relativeCourseInfo, "xkwCourseName"));
                        result.put("xkwQuestionTypeId", MapUtil.getString(questionType, "id"));
                        result.put("xkwQuestionTypeParentId", MapUtil.getString(questionType, "parentId"));
                        result.put("xkwQuestionTypeName", MapUtil.getString(questionType, "name"));
                        result.put("xkwQuestionTypeObjective", MapUtil.getBoolean(questionType, "objective"));
                        result.put("xkwQuestionTypeOrdinal", MapUtil.getInt(questionType, "ordinal"));
                        resultList.add(result);
                    }
                    return resultList;
                }
        );
    }
    
    /**
     * 获取课程的题型树形
     * {@link XkwCommonApi#getQuestionTypes(Long)}
     * @param params courseId 懂你的courseId
     * @return [{}]
     *   courseId=2,
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwQuestionTypeId=261701,
     *   xkwQuestionTypeName=选择类,
     *   xkwQuestionTypeParentId=2617
     *   xkwQuestionTypeObjective=false,
     *   xkwQuestionTypeOrdinal=1,
     *   child: [{}]
     */
    public List<Map<String, Object>> getQuestionTypeTreeCacheable(Map<String, Object> params) {
        List<Map<String, Object>> questionTypeList = getQuestionTypeListCacheable(params);
        return TreeUtil.list2Tree(questionTypeList, "xkwQuestionTypeId", "xkwQuestionTypeParentId", "child");
    }
    
    
    /**
     * 获取知识点
     * {@link XkwCommonApi#getCourseKnowledgePoints(long)}
     * @param params courseId 懂你的courseId
     * @return [{}]
     *   courseId=2
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwKnowledgeId=81434,
     *   xkwKnowledgeName=小康·扶贫,
     *   xkwKnowledgeRootId=81254,
     *   xkwKnowledgeParentId=81425,
     *   xkwKnowledgeType=TESTING_POINT,
     *   xkwKnowledgeDepth=3,
     *   xkwKnowledgeOrdinal=8,
     */
    public List<Map<String, Object>> getCourseKnowledgePointListCacheable(Map<String, Object> params) {
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfoCacheable(params);
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        String cacheDataName = "KP:" + xkwCourseId;
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> getCourseKnowledgePointListByRelativeCourseInfo(relativeCourseInfo)
        );
    }
    
    /**
     * 获取知识点 没有缓存 每次都查
     * {@link XkwCommonApi#getCourseKnowledgePoints(long)}
     * @param params courseId 懂你的courseId
     * @return [{}]
     *   courseId=2
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwKnowledgeId=81434,
     *   xkwKnowledgeName=小康·扶贫,
     *   xkwKnowledgeRootId=81254,
     *   xkwKnowledgeParentId=81425,
     *   xkwKnowledgeType=TESTING_POINT,
     *   xkwKnowledgeDepth=3,
     *   xkwKnowledgeOrdinal=8,
     */
    public List<Map<String, Object>> getCourseKnowledgePointList(Map<String, Object> params) {
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfo(params);
        return getCourseKnowledgePointListByRelativeCourseInfo(relativeCourseInfo);
    }
    
    /**
     * 获取知识点
     * {@link XkwCommonApi#getCourseKnowledgePoints(long)}
     * @param relativeCourseInfo courseId courseName xkwCourseId xkwCourseName
     * @return [{}]
     *   courseId=2
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwKnowledgeId=81434,
     *   xkwKnowledgeName=小康·扶贫,
     *   xkwKnowledgeRootId=81254,
     *   xkwKnowledgeParentId=81425,
     *   xkwKnowledgeType=TESTING_POINT,
     *   xkwKnowledgeDepth=3,
     *   xkwKnowledgeOrdinal=8,
     */
    private List<Map<String, Object>> getCourseKnowledgePointListByRelativeCourseInfo(Map<String, Object> relativeCourseInfo) {
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        List<Map<String, Object>> courseKnowledgePointList = xkwCommonApi.getCourseKnowledgePoints(xkwCourseId);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> knowledgePoint : courseKnowledgePointList) {
            Map<String, Object> result = new HashMap<>(11);
            result.put("courseId", MapUtil.getLong(relativeCourseInfo, "courseId"));
            result.put("courseName", MapUtil.getString(relativeCourseInfo, "courseName"));
            result.put("xkwCourseId", xkwCourseId);
            result.put("xkwCourseName", MapUtil.getString(relativeCourseInfo, "xkwCourseName"));
            result.put("xkwKnowledgeId", MapUtil.getLong(knowledgePoint, "id"));
            result.put("xkwKnowledgeParentId", MapUtil.getLong(knowledgePoint, "parentId"));
            result.put("xkwKnowledgeRootId", MapUtil.getLong(knowledgePoint, "rootId"));
            result.put("xkwKnowledgeName", MapUtil.getString(knowledgePoint, "name"));
            result.put("xkwKnowledgeType", MapUtil.getString(knowledgePoint, "type"));
            result.put("xkwKnowledgeOrdinal", MapUtil.getInt(knowledgePoint, "ordinal"));
            result.put("xkwKnowledgeDepth", MapUtil.getInt(knowledgePoint, "depth"));
            resultList.add(result);
        }
        return resultList;
    }
    
    /**
     * 获取知识点 树形
     * {@link XkwCommonApi#getCourseKnowledgePoints(long)}
     * @param params courseId 懂你的courseId
     * @return [{}]
     *   courseId=2
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwKnowledgeId=81434,
     *   xkwKnowledgeName=小康·扶贫,
     *   xkwKnowledgeRootId=81254,
     *   xkwKnowledgeParentId=81425,
     *   xkwKnowledgeType=TESTING_POINT,
     *   xkwKnowledgeDepth=3,
     *   xkwKnowledgeOrdinal=8,
     *   child: [{}]
     */
    public List<Map<String, Object>> getCourseKnowledgePointTreeCacheable(Map<String, Object> params) {
        List<Map<String, Object>> courseKnowledgePointList = getCourseKnowledgePointListCacheable(params);
        return TreeUtil.list2Tree(courseKnowledgePointList, "xkwKnowledgeId", "xkwKnowledgeParentId", "child");
    }
    
    /**
     * 获取知识点 树形 没有缓存 每次都查
     * {@link XkwCommonApi#getCourseKnowledgePoints(long)}
     * @param params courseId 懂你的courseId
     * @return [{}]
     *   courseId=2
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwKnowledgeId=81434,
     *   xkwKnowledgeName=小康·扶贫,
     *   xkwKnowledgeRootId=81254,
     *   xkwKnowledgeParentId=81425,
     *   xkwKnowledgeType=TESTING_POINT,
     *   xkwKnowledgeDepth=3,
     *   xkwKnowledgeOrdinal=8,
     *   child: [{}]
     */
    public List<Map<String, Object>> getCourseKnowledgePointTree(Map<String, Object> params) {
        List<Map<String, Object>> courseKnowledgePointList = getCourseKnowledgePointList(params);
        return TreeUtil.list2Tree(courseKnowledgePointList, "xkwKnowledgeId", "xkwKnowledgeParentId", "child");
    }
    
    /**
     * 获取课程的教材版本列表
     *    下一步拿versionId 跟 courseId 去获取章节列表
     * {@link XkwCommonApi#getCoursesTextbookVersions(long)}
     * @param params courseId 懂你的courseId
     * @return [{}]
     *   courseId=2,
     *   courseName=语文,
     *   xkwCourseId=26,
     *   xkwCourseName=高中语文,
     *   xkwTextbookVersionId=313
     *   xkwTextbookVersionName=北师大版,
     *   xkwTextbookVersionYear=2005,
     *   xkwTextbookVersionOrdinal=5,
     */
    public List<Map<String, Object>> getCoursesTextbookVersionListCacheable(Map<String, Object> params) {
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfoCacheable(params);
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        String cacheDataName = "TEXTBOOK:VERSION:" + xkwCourseId;
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> {
                    long courseId = MapUtil.getLong(relativeCourseInfo, "courseId");
                    String courseName = MapUtil.getString(relativeCourseInfo, "courseName");
                    String xkwCourseName = MapUtil.getString(relativeCourseInfo, "xkwCourseName");
                    List<Map<String, Object>> coursesTextbookVersionList = getCoursesTextbookVersionList(xkwCourseId);
                    coursesTextbookVersionList.forEach(item -> {
                        item.put("courseId", courseId);
                        item.put("courseName", courseName);
                        item.put("xkwCourseName", xkwCourseName);
                    });
                    return coursesTextbookVersionList;
                }
        );
    }
    
    /**
     * 获取学科网的教材版本信息
     *    下一步拿versionId 跟 courseId 去获取章节列表
     * @param xkwCourseId 学科网的courseId
     * @return [{}]
     *         xkwCourseId=26,
     *         xkwTextbookVersionId=313
     *         xkwTextbookVersionName=北师大版,
     *         xkwTextbookVersionYear=2005,
     *         xkwTextbookVersionOrdinal=5,
     */
    public List<Map<String, Object>> getCoursesTextbookVersionList(long xkwCourseId) {
        List<Map<String, Object>> coursesTextbookVersionList = xkwCommonApi.getCoursesTextbookVersions(xkwCourseId);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> textbookVersion : coursesTextbookVersionList) {
            resultList.add(MapUtil.of(
                    "xkwCourseId", xkwCourseId,
                    "xkwTextbookVersionId", MapUtil.getLong(textbookVersion, "id"),
                    "xkwTextbookVersionName", MapUtil.getString(textbookVersion, "name"),
                    "xkwTextbookVersionYear", MapUtil.getInt(textbookVersion, "year"),
                    "xkwTextbookVersionOrdinal", MapUtil.getInt(textbookVersion, "ordinal")
            ));
        }
        return resultList;
    }
    
    /**
     * 获取课程-教材版本对应的教材列表
     * {@link XkwCommonApi#getTextbooks(Long, Long, Long, Integer, Integer)}
     * @param params courseId 懂你课程id
     *               xkwTextbookVersionId 学科网的教材版本id
     * @return [{}]
     *     courseId=2,
     *     courseName=语文,
     *     xkwCourseId=26,
     *     xkwCourseName=高中语文,
     *     xkwGradeId=0,
     *     xkwTextbookId=798,
     *     xkwTextbookName=高中语文北师大版必修1,
     *     xkwTextbookVersionId=313,
     *     xkwTextbookVolume=必修1
     *     xkwTextbookTerm=null,
     *     xkwTextbookOrdinal=154,
     */
    public List<Map<String, Object>> getTextbookListCacheable(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isValidId("xkwTextbookVersionId")
                .verify();
        Map<String, Object> relativeCourseInfo = getRelativeCourseInfoCacheable(params);
        long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
        long xkwTextbookVersionId = MapUtil.getLong(params, "xkwTextbookVersionId");
        String cacheDataName = "TEXTBOOK:" + xkwCourseId + ":" + xkwTextbookVersionId;
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> {
                    long courseId = MapUtil.getLong(relativeCourseInfo, "courseId");
                    String courseName = MapUtil.getString(relativeCourseInfo, "courseName");
                    String xkwCourseName = MapUtil.getString(relativeCourseInfo, "xkwCourseName");
                    List<Map<String, Object>> textbookList = getTextbookList(xkwCourseId, xkwTextbookVersionId);
                    textbookList.forEach(item -> {
                        item.put("courseId", courseId);
                        item.put("courseName", courseName);
                        item.put("xkwCourseName", xkwCourseName);
                    });
                    return textbookList;
                }
        );
    }
    
    /**
     * 获取课程-教材版本对应的教材列表
     * {@link XkwCommonApi#getTextbooks(Long, Long, Long, Integer, Integer)}
     * @param xkwCourseId 学科网courseId
     * @param xkwTextbookVersionId 学科网的教材版本id
     * @return [{}]
     *     xkwCourseId=26,
     *     xkwGradeId=0,
     *     xkwTextbookId=798,
     *     xkwTextbookName=高中语文北师大版必修1,
     *     xkwTextbookVersionId=313,
     *     xkwTextbookVolume=必修1
     *     xkwTextbookTerm=null,
     *     xkwTextbookOrdinal=154,
     */
    public List<Map<String, Object>> getTextbookList(long xkwCourseId, long xkwTextbookVersionId) {
        List<Map<String, Object>> textbookList = new ArrayList<>();
        int pageSize = 1000;
        int pageNo = 1;
        while (true) {
            XkwPager<Map<String, Object>> textbookPager = xkwCommonApi.getTextbooks(xkwCourseId, null, xkwTextbookVersionId, pageNo, pageSize);
            List<Map<String, Object>> items = textbookPager.getItems();
            if (CollectionUtils.isNotEmpty(items)) {
                textbookList.addAll(items);
            }
            if (Objects.equals(textbookPager.getPageIndex(), textbookPager.getTotalPage())) {
                break;
            }
            pageNo++;
        }
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> textbook : textbookList) {
            Map<String, Object> result = new HashMap<>(11);
            result.put("xkwCourseId", xkwCourseId);
            // course_id = 3, version_id = 592 获取到的数据该字段为null
            result.put("xkwGradeId", MapUtil.getLongNullable(textbook, "gradeId"));
            result.put("xkwTextbookId", MapUtil.getLong(textbook, "id"));
            result.put("xkwTextbookName", MapUtil.getString(textbook, "name"));
            result.put("xkwTextbookVolume", MapUtil.getString(textbook, "volume"));
            result.put("xkwTextbookTerm", MapUtil.getStringNullable(textbook, "term"));
            result.put("xkwTextbookVersionId", MapUtil.getLong(textbook, "versionId"));
            result.put("xkwTextbookOrdinal", MapUtil.getInt(textbook, "ordinal"));
            resultList.add(result);
        }
        return resultList;
    }
    
    /**
     * 获取指定教材的章节列表
     * {@link XkwCommonApi#getTextbooksCatalog(long)}
     * @param params xkwTextbookId 学科网的教材id
     * @return [{}]
     *     "xkwTextbookId"             : 798,
     *     "xkwTextbookCatalogId"      : 20443,
     *     "xkwTextbookCatalogName"    : "第一单元 家国情怀",
     *     "xkwTextbookCatalogParentId": 0,
     *     "xkwTextbookCatalogOrdinal" : 0,
     */
    public List<Map<String, Object>> getTextbooksCatalogListCacheable(Map<String, Object> params) {
        Verify.of(params).isValidId("xkwTextbookId").verify();
        long xkwTextbookId = MapUtil.getLong(params, "xkwTextbookId");
        String cacheDataName = "TEXTBOOK:CATALOG:" + xkwTextbookId;
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> getTextbooksCatalogList(xkwTextbookId)
        );
    }
    
    /**
     * 获取指定教材的章节列表
     * {@link XkwCommonApi#getTextbooksCatalog(long)}
     * @param xkwTextbookId 学科网的教材id
     * @return [{}]
     *     "xkwTextbookId"             : 798,
     *     "xkwTextbookCatalogId"      : 20443,
     *     "xkwTextbookCatalogName"    : "第一单元 家国情怀",
     *     "xkwTextbookCatalogParentId": 0,
     *     "xkwTextbookCatalogOrdinal" : 0,
     */
    public List<Map<String, Object>> getTextbooksCatalogList(long xkwTextbookId) {
        List<Map<String, Object>> textbooksCatalogList = xkwCommonApi.getTextbooksCatalog(xkwTextbookId);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> textbooksCatalog : textbooksCatalogList) {
            Map<String, Object> result = new HashMap<>(5);
            result.put("xkwTextbookId", MapUtil.getInt(textbooksCatalog, "textbookId"));
            result.put("xkwTextbookCatalogId", MapUtil.getLong(textbooksCatalog, "id"));
            result.put("xkwTextbookCatalogName", MapUtil.getString(textbooksCatalog, "name"));
            result.put("xkwTextbookCatalogParentId", MapUtil.getInt(textbooksCatalog, "parentId"));
            result.put("xkwTextbookCatalogOrdinal", MapUtil.getInt(textbooksCatalog, "ordinal"));
            resultList.add(result);
        }
        return resultList;
    
    }
    
    /**
     * 获取指定教材的章节列表 树形
     * {@link XkwCommonApi#getTextbooksCatalog(long)}
     * @param params xkwTextbookId 学科网的教材id
     * @return [{}]
     *      "xkwTextbookId"             : 798,
     *      "xkwTextbookCatalogId"      : 20443,
     *      "xkwTextbookCatalogName"    : "第一单元 家国情怀",
     *      "xkwTextbookCatalogParentId": 0,
     *      "xkwTextbookCatalogOrdinal" : 0,
     *      "child": [{}]
     */
    public List<Map<String, Object>> getTextbooksCatalogTreeCacheable(Map<String, Object> params) {
        List<Map<String, Object>> textbooksCatalogList = getTextbooksCatalogListCacheable(params);
        return TreeUtil.list2Tree(textbooksCatalogList, "xkwTextbookCatalogId", "xkwTextbookCatalogParentId", "child");
    }
    
    /**
     * 获取章节和知识点的映射关系
     * {@link XkwCommonApi#getTextbooksCatalogKnowledge}
     * @param params xkwTextbookId 学科网的教材id
     * @return [{}]
     *     direction             方向(EQUAL：等价, CONTAIN：包含, BELONG：属于, INTERSECT：交叉),可用值:EQUAL,CONTAIN,BELONG,INTERSECT
     *     xkwTextbookCatalogId  目录节点ID
     *     xkwKnowledgeId        知识点ID
     *     xkwKnowledgeName      知识点名称
     */
    public List<Map<String, Object>> getTextbooksCatalogKnowledgeListCacheable(Map<String, Object> params) {
        Verify.of(params).isValidId("xkwTextbookId").verify();
        long xkwTextbookId = MapUtil.getLong(params, "xkwTextbookId");
        String cacheDataName = "TEXTBOOK:CATALOG:KP:" + xkwTextbookId;
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                () -> getTextbooksCatalogKnowledgeList(xkwTextbookId)
        );
    }
    
    /**
     * 获取章节和知识点的映射关系
     * {@link XkwCommonApi#getTextbooksCatalogKnowledge}
     * @param xkwTextbookId 学科网的教材id
     * @return [{}]
     *     direction             方向(EQUAL：等价, CONTAIN：包含, BELONG：属于, INTERSECT：交叉),可用值:EQUAL,CONTAIN,BELONG,INTERSECT
     *     xkwTextbookCatalogId  目录节点ID
     *     xkwKnowledgeId        知识点ID
     *     xkwKnowledgeName      知识点名称
     */
    public List<Map<String, Object>> getTextbooksCatalogKnowledgeList(long xkwTextbookId) {
        List<Map<String, Object>> textbooksCatalogKnowledgeList = xkwCommonApi.getTextbooksCatalogKnowledge(null, xkwTextbookId, null);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> textbooksCatalogKnowledge : textbooksCatalogKnowledgeList) {
            Map<String, Object> result = new HashMap<>(4);
            result.put("direction", MapUtil.getString(textbooksCatalogKnowledge, "direction"));
            result.put("xkwTextbookCatalogId", MapUtil.getLong(textbooksCatalogKnowledge, "catalogId"));
            result.put("xkwKnowledgeId", MapUtil.getLong(textbooksCatalogKnowledge, "kpointId"));
            result.put("xkwKnowledgeName", MapUtil.getString(textbooksCatalogKnowledge, "kpointName"));
            resultList.add(result);
        }
        return resultList;
    }
    
    /**
     * 获取试卷类型列表
     * @return [{}]
     * {
     *     "xkwPaperTypeId": 39,
     *     "xkwPaperTypeName": "随堂练习",
     *     "xkwPaperTypeDescription": "课堂中使用的用于检测本课学习效果的试题合集，小测卷，思维导图，知识总结等",
     *     "xkwPaperTypeParentId": 0,
     *     "stage": 0,
     *     "stageName": "通用",
     *     "xkwStageId": 0,
     * }
     * {
     *     "xkwPaperTypeId": 21,
     *     "xkwPaperTypeName": "一模",
     *     "xkwPaperTypeDescription": "高考一模",
     *     "xkwPaperTypeParentId": 9,
     *     "stage": 3,
     *     "stageName": "高中",
     *     "xkwStageId": 4,
     * }
     */
    public List<Map<String, Object>> getPaperTypeListCacheable() {
        String cacheDataName = "PAPER:TYPE";
        return getCacheable(cacheDataName, JSONUtil.TYPE_REFERENCE_LIST_MAP,
                this::getPaperTypeList
        );
    }
    
    /**
     * 获取试卷类型列表
     * @return [{}]
     * {
     *     "xkwPaperTypeId": 39,
     *     "xkwPaperTypeName": "随堂练习",
     *     "xkwPaperTypeDescription": "课堂中使用的用于检测本课学习效果的试题合集，小测卷，思维导图，知识总结等",
     *     "xkwPaperTypeParentId": 0,
     *     "stage": 0,
     *     "stageName": "通用",
     *     "xkwStageId": 0,
     * }
     * {
     *     "xkwPaperTypeId": 21,
     *     "xkwPaperTypeName": "一模",
     *     "xkwPaperTypeDescription": "高考一模",
     *     "xkwPaperTypeParentId": 9,
     *     "stage": 3,
     *     "stageName": "高中",
     *     "xkwStageId": 4,
     * }
     */
    public List<Map<String, Object>> getPaperTypeList() {
        Map<String, Integer> dnStageName2Stage = getDnStageName2Stage();
        Map<Long, Map<String, Object>> xkwStageId2Info = getXkwStageId2Info();
        List<Map<String, Object>> paperTypeList = xkwCommonApi.getPaperTypes();
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> paperType : paperTypeList) {
            long xkwStageId = MapUtil.getLong(paperType, "stageId");
            int stage = 0;
            String stageName = "通用";
            if (xkwStageId != 0) {
                Map<String, Object> xkwStageInfo = xkwStageId2Info.get(xkwStageId);
                if (MapUtils.isEmpty(xkwStageInfo)) {
                    continue;
                }
                String xkwStageName = MapUtil.getTrim(xkwStageInfo, "name");
                Integer dnStageTmp = dnStageName2Stage.get(xkwStageName);
                if (dnStageTmp == null) {
                    continue;
                }
                stage = dnStageTmp;
                stageName = xkwStageName;
            }
            
            Map<String, Object> result = new HashMap<>(7);
            result.put("stage", stage);
            result.put("stageName", stageName);
            result.put("xkwPaperTypeId", MapUtil.getLong(paperType, "id"));
            result.put("xkwPaperTypeName", MapUtil.getTrim(paperType, "name"));
            result.put("xkwPaperTypeParentId", MapUtil.getLong(paperType, "parentId"));
            result.put("xkwPaperTypeDescription", MapUtil.getStringNullable(paperType, "description"));
            result.put("xkwStageId", xkwStageId);
            resultList.add(result);
        }
        return resultList;
    }
    
    /**
     * 缓存获取数据
     * 获取缓存数据key: "${keyPrefix}:${version}"
     * 获取请求数据key: "${keyPrefix}:LOCK"
     * @param cacheDataName 缓存数据名称
     * @param typeReference 数据类型
     * @param dataSupplier  数据获取
     * @return 数据
     */
    private <T> T getCacheable(String cacheDataName, TypeReference<T> typeReference, Supplier<T> dataSupplier) {
        // 缓存获取到的直接返回 不续命
        String cacheKey = CACHE_NAMESPACE + cacheDataName;
        T dataCache = get(cacheKey, typeReference);
        if (dataCache != null) {
            return dataCache;
        }
        
        // 拿不到的加锁获取
        String lockKey = cacheDataName + ":LOCK";
        int waitLockMilliseconds = 10_000;
        String lockErrorMessage = "服务忙，请稍后再操作";
        return JedisTemplate.lockExecute(lockKey, waitLockMilliseconds, lockErrorMessage,
                () -> {
                    // 抢到锁后再次尝试获取缓存
                    T dataCacheTmp = get(cacheKey, typeReference);
                    if (dataCacheTmp != null) {
                        return dataCacheTmp;
                    }
                    // 调用获取实时数据
                    T data = dataSupplier.get();
                    if (data != null) {
                        int expireSecond = 1800;
                        JedisTemplate.execute(jedis -> {
                            // 将缓存的key也进行管理起来 方便出问题的时候直接清理掉
                            manageCacheKey(jedis, cacheKey, expireSecond);
                            // 缓存数据
                            jedis.setex(cacheKey, expireSecond, JSONUtil.toJson(data));
                            return true;
                        });
                    }
                    return data;
                }
        );
    }
    
    /**
     * 获取缓存数据 不再续命 防止数据版本落后实际数据
     * @param key           缓存key
     * @param typeReference 数据类型
     * @return 缓存值 or null
     */
    private <T> T get(String key, TypeReference<T> typeReference) {
        String dataJson = JedisTemplate.execute(jedis -> jedis.get(key));
        return JSONUtil.parse(dataJson, typeReference);
    }
    
    private static final String MANAGE_KEY = "TIKU:XOP:CACHE_KEY_MANAGE";
    
    /**
     * 缓存"缓存的key"
     */
    private void manageCacheKey(Jedis jedis, String cacheKey, int expireSecond) {
        long expireTimeMillis = System.currentTimeMillis() / 1000 + expireSecond;
        jedis.zadd(MANAGE_KEY, expireTimeMillis, cacheKey);
        jedis.expire(MANAGE_KEY, expireSecond * 2);
    }
    
    /**
     * 管理缓存的key，将已经过期的key清理掉 他们不需要再被管理了
     * 有定时任务在跑，一般来说不需要管他
     */
    public void cleanCacheKey() {
        long expireTimeMillis = System.currentTimeMillis() / 1000;
        // delete where manage.memberScore <= currentTimestampSecond
        JedisTemplate.execute(jedis -> jedis.zremrangeByScore(MANAGE_KEY, 0, expireTimeMillis));
    }
    
    /**
     * 清理所有的缓存
     * 当数据数据异常时需要清掉数据
     */
    public void removeAllCache() {
        cleanCacheKey();
        JedisTemplate.execute(jedis -> {
            long expireTimeMillis = System.currentTimeMillis() / 1000;
            // select where manage.memberScore >= currentTimestampSecond
            Set<Tuple> cacheKeyWithScoreSet = jedis.zrangeByScoreWithScores(MANAGE_KEY, expireTimeMillis, Double.MAX_VALUE);
            if (CollectionUtils.isNotEmpty(cacheKeyWithScoreSet)) {
                // delete manage, delete manage.memberKey where currentScore = oldScore
                String script = "local manageKey = KEYS[1]; " +
                        " local memberKey = KEYS[2]; " +
                        " local memberScore = tonumber(ARGV[1]); " +
                        " if redis.call('zscore', manageKey, memberKey) == memberScore " +
                        " then redis.call('del', memberKey); redis.call('zrem', manageKey, memberKey); return 1 " +
                        " else return 0 " +
                        " end";
                for (Tuple cacheKeyWithScore : cacheKeyWithScoreSet) {
                    String memberKey = cacheKeyWithScore.getElement();
                    double score = cacheKeyWithScore.getScore();
                    List<String> evalKeys = Arrays.asList(MANAGE_KEY, memberKey);
                    List<String> evalArgv = Collections.singletonList(String.valueOf(score));
                    jedis.eval(script, evalKeys, evalArgv);
                }
            }
            return true;
        });
    }
}
