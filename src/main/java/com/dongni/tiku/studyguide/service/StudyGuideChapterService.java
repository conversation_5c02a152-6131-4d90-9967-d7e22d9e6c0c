package com.dongni.tiku.studyguide.service;

import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcelTemplate;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.commons.utils.verify.Verify2;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideBookDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideBookQrcodeDownloadDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterImportJsonItemDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterImportJsonResultDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterImportJsonResultItemDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterImportTemplateResultDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterImportTemplateResultItemDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterListResultDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterQrcodeCheckResultDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterQrcodeDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterQrcodeListResultDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterSortDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideFullCodeDTO;
import com.dongni.tiku.studyguide.bean.mybatis.BookChapterIdByNameGetQO;
import com.dongni.tiku.studyguide.bean.mybatis.StudyGuideChapterByEntrustPaperGetQO;
import com.dongni.tiku.studyguide.bean.mybatis.StudyGuideChapterGetQO;
import com.dongni.tiku.studyguide.bean.mybatis.StudyGuideChapterSortListGetQO;
import com.dongni.tiku.studyguide.bean.mybatis.StudyGuideChapterSortUpdateByOffsetQO;
import com.dongni.tiku.studyguide.bean.param.BaseStudyGuidePublisherBookChapterParam;
import com.dongni.tiku.studyguide.bean.param.ChapterEntrustIdUpdateParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideBookChapterMaxCodeGetParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideBookQrcodeDownloadParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterAddParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterImportJsonParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterImportTemplateParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterListGetParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterQrCodeCheckParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterQrCodeListGetParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterQrcodeAddParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterUpdateParam;
import com.dongni.tiku.studyguide.bean.param.StudyGuideChapterUpdateSortParam;
import com.dongni.tiku.studyguide.bean.po.StudyGuideChapterPO;
import com.dongni.tiku.studyguide.bean.po.StudyGuideChapterQrcodePO;
import com.dongni.tiku.studyguide.utils.SaveSortUtil;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.DecodeHintType;
import com.google.zxing.LuminanceSource;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.NotFoundException;
import com.google.zxing.Result;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toMap;

/**
 *
 * <AUTHOR>
 * @date 2025/03/14
 */
@Service
public class StudyGuideChapterService {
    
    @Autowired
    private TikuRepository tikuRepository;
    @Autowired
    private StudyGuidePaperService studyGuidePaperService;
    @Autowired
    private StudyGuideBookService studyGuideBookService;
    
    /** 加锁执行 删除教辅的时候要防止并发新增章节 */
    @DistributeLock(moduleName = "TIKU", name = "operateStudyGuideChapter", argValueKeys = { "[0]" }, waitTime = 0)
    public <T> T operateStudyGuideChapterLockExecute(long studyGuideBookId, Supplier<T> supplier) {
        return supplier.get();
    }
    
    
    /**
     * 获取教辅章节列表 不分页
     */
    public StudyGuideChapterListResultDTO getStudyGuideChapterList(StudyGuideChapterListGetParam param) {
        Verify2.of(param)
                .isValidId(StudyGuideChapterListGetParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideChapterListGetParam::getStudyGuideBookId)
                .verify();
        StudyGuideBookDTO studyGuideBookDTO = studyGuideBookService.getStudyGuideBookInfo(param);
        List<StudyGuideChapterDTO> chapterDTOList = tikuRepository.selectList("StudyGuideChapterMapper.getStudyGuideChapterList", param);
        StudyGuideChapterListResultDTO resultDTO = new StudyGuideChapterListResultDTO();
        resultDTO.setStudyGuideBook(studyGuideBookDTO);
        resultDTO.setStudyGuideChapterList(chapterDTOList);
        return resultDTO;
    }
    
    /**
     * 获取教辅章节信息
     * @return 教辅章节信息 获取不到会抛异常
     */
    protected StudyGuideChapterDTO getStudyGuideChapterInfo(BaseStudyGuidePublisherBookChapterParam param) {
        param.verify();
        StudyGuideChapterGetQO getQO = new StudyGuideChapterGetQO();
        getQO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        getQO.setStudyGuideBookId(param.getStudyGuideBookId());
        getQO.setStudyGuideChapterId(param.getStudyGuideChapterId());
        StudyGuideChapterDTO studyGuideChapterDTO = tikuRepository.selectOne("StudyGuideChapterMapper.getStudyGuideChapter", getQO);
        if (studyGuideChapterDTO == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "教辅章节不存在: "
                    + getQO.getStudyGuidePublisherId() + "-"
                    + getQO.getStudyGuideBookId() + "-"
                    + getQO.getStudyGuideChapterId()
            );
        }
        return studyGuideChapterDTO;
    }
    
    /**
     * 获取教辅章节信息
     * @return 教辅章节信息 获取不到会抛异常
     */
    public StudyGuideChapterDTO getStudyGuideChapterInfo(long entrustId, long paperId) {
        StudyGuideChapterByEntrustPaperGetQO getQO = new StudyGuideChapterByEntrustPaperGetQO();
        getQO.setEntrustId(entrustId);
        getQO.setPaperId(paperId);
        StudyGuideChapterDTO studyGuideChapterDTO = tikuRepository.selectOne("StudyGuideChapterMapper.getStudyGuideChapterByEntrustPaper", getQO);
        if (studyGuideChapterDTO == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "教辅章节不存在: "
                    + "entrustId: " + entrustId + "; "
                    + "paperId: " + paperId
            );
        }
        return studyGuideChapterDTO;
    }
    
    /**
     * 获取教辅章节信息
     * @return 教辅章节信息 获取不到会抛异常
     */
    public StudyGuideChapterDTO getStudyGuideChapterInfoForCopy(long paperId) {
        StudyGuideChapterDTO studyGuideChapterDTO = tikuRepository.selectOne("StudyGuideChapterMapper.getStudyGuideChapterByPaperId", paperId);
        if (studyGuideChapterDTO == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "教辅章节不存在: paperId: " + paperId);
        }
        return studyGuideChapterDTO;
    }
    
    /**
     * 获取书籍章节最大的chapterCode
     * @return null表示书籍下没有任何章节
     */
    public Integer getStudyGuideBookChapterMaxCode(StudyGuideBookChapterMaxCodeGetParam param) {
        Verify2.of(param)
                .isValidId(StudyGuideBookChapterMaxCodeGetParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideBookChapterMaxCodeGetParam::getStudyGuideBookId)
                .verify();
        studyGuideBookService.getStudyGuideBookInfo(param);
        return tikuRepository.selectOne("StudyGuideChapterMapper.getStudyGuideBookChapterMaxCode", param);
    }
    
    /**
     * 新增教辅章节
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    @DistributeLock(moduleName = "TIKU", name = "operateStudyGuideChapter", argValueKeys = { "[0].studyGuideBookId" }, waitTime = 0)
    public void addStudyGuideChapter(StudyGuideChapterAddParam param) {
        Verify2.of(param)
                .isValidId(StudyGuideChapterAddParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideChapterAddParam::getStudyGuideBookId)
                .isNotBlank(StudyGuideChapterAddParam::getChapterName)
                .verify();
        param.setChapterName(param.getChapterName().trim());
        String chapterName = param.getChapterName();
        if (chapterName.length() > 50) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "教辅名称不能大于50个字");
        }
        
        StudyGuideBookChapterMaxCodeGetParam maxCodeGetParam = new StudyGuideBookChapterMaxCodeGetParam();
        maxCodeGetParam.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        maxCodeGetParam.setStudyGuideBookId(param.getStudyGuideBookId());
        Integer maxCode = getStudyGuideBookChapterMaxCode(maxCodeGetParam);
        if (maxCode == null) {
            maxCode = 0;
        }
        if (maxCode >= 99) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "教辅的章节数量已达上限(99)");
        }
        int chapterCode = maxCode + 1;
        
        // 教辅的章节名称是否重复
        BookChapterIdByNameGetQO getQO = new BookChapterIdByNameGetQO();
        getQO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        getQO.setStudyGuideBookId(param.getStudyGuideBookId());
        getQO.setChapterName(chapterName);
        Long existChapterId = tikuRepository.selectOne("StudyGuideChapterMapper.getBookChapterIdByName", getQO);
        if (existChapterId != null) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "教辅章节名称已经存在(" + existChapterId + ")");
        }
        
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        Date currentTime = new Date();
        StudyGuideChapterPO chapterPO = new StudyGuideChapterPO();
        chapterPO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        chapterPO.setStudyGuideBookId(param.getStudyGuideBookId());
        chapterPO.setChapterCode(chapterCode);
        chapterPO.setChapterName(chapterName);
        chapterPO.setChapterStatus(DictUtil.getDictValue("studyGuideChapterStatus", "doing"));
        chapterPO.setPaperId(studyGuidePaperService.getPaperId());
        chapterPO.setEntrustId(0L);
        chapterPO.setTemplateStatus(DictUtil.getDictValue("studyGuideTemplateStatus", "notStart"));
        chapterPO.setAnswerStatus(DictUtil.getDictValue("studyGuideAnswerStatus", "notUpload"));
        chapterPO.setSort(chapterCode);
        chapterPO.setCreatorId(dongniUserInfoContext.getUserId());
        chapterPO.setCreatorName(dongniUserInfoContext.getUserName());
        chapterPO.setCreateDateTime(currentTime);
        chapterPO.setModifierId(dongniUserInfoContext.getUserId());
        chapterPO.setModifierName(dongniUserInfoContext.getUserName());
        chapterPO.setModifyDateTime(currentTime);
        tikuRepository.insert("StudyGuideChapterMapper.insertStudyGuideChapter", chapterPO);
        
        StudyGuideChapterQrcodeAddParam qrcodeAddParam = new StudyGuideChapterQrcodeAddParam();
        qrcodeAddParam.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        qrcodeAddParam.setStudyGuideBookId(param.getStudyGuideBookId());
        qrcodeAddParam.setStudyGuideChapterId(chapterPO.getStudyGuideChapterId());
        addStudyGuideChapterQrcode(qrcodeAddParam);
    }
    
    /**
     * 编辑教辅章节
     */
    @DistributeLock(moduleName = "TIKU", name = "operateStudyGuideChapter", argValueKeys = { "[0].studyGuideBookId" }, waitTime = 0)
    public void updateStudyGuideChapter(StudyGuideChapterUpdateParam param) {
        Verify2.of(param)
                .isValidId(StudyGuideChapterUpdateParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideChapterUpdateParam::getStudyGuideBookId)
                .isValidId(StudyGuideChapterUpdateParam::getStudyGuideChapterId)
                .isNotBlank(StudyGuideChapterUpdateParam::getChapterName)
                .verify();
        param.setChapterName(param.getChapterName().trim());
        
        Long publisherId = param.getStudyGuidePublisherId();
        Long bookId = param.getStudyGuideBookId();
        Long chapterId = param.getStudyGuideChapterId();
        String chapterName = param.getChapterName();
        
        StudyGuideChapterDTO chapterDTO = getStudyGuideChapterInfo(param);
        if (chapterName.equals(chapterDTO.getChapterName())) {
            return;
        }
        if (chapterName.length() > 50) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "教辅章节名称不能大于50个字");
        }
        
        // 教辅的章节名称是否重复
        BookChapterIdByNameGetQO getQO = new BookChapterIdByNameGetQO();
        getQO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        getQO.setStudyGuideBookId(param.getStudyGuideBookId());
        getQO.setChapterName(chapterName);
        Long existChapterId = tikuRepository.selectOne("StudyGuideChapterMapper.getBookChapterIdByName", getQO);
        if (existChapterId != null && !existChapterId.equals(chapterId)) {
            throw new CommonException(ResponseStatusEnum.DATA_DUPLICATE, "教辅章节名称已经存在(" + existChapterId + ")");
        }
        
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        StudyGuideChapterPO chapterPO = new StudyGuideChapterPO();
        chapterPO.setStudyGuidePublisherId(publisherId);
        chapterPO.setStudyGuideBookId(bookId);
        chapterPO.setStudyGuideChapterId(chapterId);
        chapterPO.setChapterName(chapterName);
        chapterPO.setModifierId(dongniUserInfoContext.getUserId());
        chapterPO.setModifierName(dongniUserInfoContext.getUserName());
        chapterPO.setModifyDateTime(new Date());
        tikuRepository.update("StudyGuideChapterMapper.updateStudyGuideChapter", chapterPO);
    }
    

    /**
     * 保存教辅章节排序
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateStudyGuideChapterSort(StudyGuideChapterUpdateSortParam param) {
        Verify2.of(param)
                .isValidId(StudyGuideChapterUpdateSortParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideChapterUpdateSortParam::getStudyGuideBookId)
                .isNotEmpty(StudyGuideChapterUpdateSortParam::getStudyGuideChapterIdSortList)
                .verify();
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        Long userId = dongniUserInfoContext.getUserId();
        String userName = dongniUserInfoContext.getUserName();
        Date currentTime = new Date();
        
        String sortOperateName = "教辅章节";
        List<Long> chapterIdSortList = param.getStudyGuideChapterIdSortList();
        Supplier<List<SaveSortUtil.KeySortDTO<Long>>> oldKeySortListSupplier = () -> {
            StudyGuideChapterSortListGetQO qo = new StudyGuideChapterSortListGetQO();
            qo.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
            qo.setStudyGuideBookId(param.getStudyGuideBookId());
            List<StudyGuideChapterSortDTO> oldChapterSortList =
                    tikuRepository.selectList("StudyGuideChapterMapper.getStudyGuideChapterSortList", qo);
            return oldChapterSortList.stream()
                    .map(chapterSortDTO -> {
                        SaveSortUtil.KeySortDTO<Long> longKeySortDTO = new SaveSortUtil.KeySortDTO<>();
                        longKeySortDTO.setKey(chapterSortDTO.getStudyGuideChapterId());
                        longKeySortDTO.setSort(chapterSortDTO.getSort());
                        return longKeySortDTO;
                    })
                    .collect(Collectors.toList());
        };
        Consumer<Map<Long, Integer>> key2sortConsumer = key2sort -> {
            for (Map.Entry<Long, Integer> entry : key2sort.entrySet()) {
                StudyGuideChapterPO chapterPO = new StudyGuideChapterPO();
                chapterPO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
                chapterPO.setStudyGuideBookId(param.getStudyGuideBookId());
                chapterPO.setStudyGuideChapterId(entry.getKey());
                chapterPO.setSort(entry.getValue());
                chapterPO.setModifierId(userId);
                chapterPO.setModifierName(userName);
                chapterPO.setModifyDateTime(currentTime);
                tikuRepository.update("StudyGuideChapterMapper.updateStudyGuideChapterSort", chapterPO);
            }
        };
        Consumer<Map<Integer, List<Long>>> sortOffset2KeyListConsumer = sortOffset2KeyList -> {
            for (Map.Entry<Integer, List<Long>> entry : sortOffset2KeyList.entrySet()) {
                Integer sortOffset = entry.getKey();
                List<Long> studyGuideChapterIdList = entry.getValue();
                StudyGuideChapterSortUpdateByOffsetQO qo = new StudyGuideChapterSortUpdateByOffsetQO();
                qo.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
                qo.setStudyGuideBookId(param.getStudyGuideBookId());
                qo.setSortOffset(sortOffset);
                qo.setStudyGuideChapterIdList(studyGuideChapterIdList);
                qo.setModifierId(userId);
                qo.setModifierName(userName);
                qo.setModifyDateTime(currentTime);
                tikuRepository.update("StudyGuideChapterMapper.updateStudyGuideChapterSortByOffset", qo);
            }
        };
        SaveSortUtil.sort(sortOperateName, chapterIdSortList, oldKeySortListSupplier, key2sortConsumer, sortOffset2KeyListConsumer);
    }
    
    /**
     * 模板绘制完成
     */
    protected void updateChapterTemplateCompleted(BaseStudyGuidePublisherBookChapterParam param) {
        param.verify();
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        StudyGuideChapterPO chapterPO = new StudyGuideChapterPO();
        chapterPO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        chapterPO.setStudyGuideBookId(param.getStudyGuideBookId());
        chapterPO.setStudyGuideChapterId(param.getStudyGuideChapterId());
        chapterPO.setTemplateStatus(DictUtil.getDictValue("studyGuideTemplateStatus", "completed"));
        chapterPO.setModifierId(dongniUserInfoContext.getUserId());
        chapterPO.setModifierName(dongniUserInfoContext.getUserName());
        chapterPO.setModifyDateTime(new Date());
        tikuRepository.update("StudyGuideChapterMapper.updateStudyGuideTemplateCompleted", chapterPO);
    }
    
    /**
     * 答案上传状态未上传
     */
    public void updateChapterAnswerStatusNotUpload(BaseStudyGuidePublisherBookChapterParam param) {
        updateChapterAnswerStatus(param, DictUtil.getDictValue("studyGuideAnswerStatus", "notUpload"));
    }
    
    /**
     * 答案上传状态为已上传
     */
    public void updateChapterAnswerStatusUploaded(BaseStudyGuidePublisherBookChapterParam param) {
        updateChapterAnswerStatus(param, DictUtil.getDictValue("studyGuideAnswerStatus", "uploaded"));
    }
    
    /**
     * 更新上传答案状态
     */
    private void updateChapterAnswerStatus(BaseStudyGuidePublisherBookChapterParam param, int studyGuideAnswerStatus) {
        param.verify();
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        StudyGuideChapterPO chapterPO = new StudyGuideChapterPO();
        chapterPO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        chapterPO.setStudyGuideBookId(param.getStudyGuideBookId());
        chapterPO.setStudyGuideChapterId(param.getStudyGuideChapterId());
        chapterPO.setAnswerStatus(studyGuideAnswerStatus);
        chapterPO.setModifierId(dongniUserInfoContext.getUserId());
        chapterPO.setModifierName(dongniUserInfoContext.getUserName());
        chapterPO.setModifyDateTime(new Date());
        tikuRepository.update("StudyGuideChapterMapper.updateChapterAnswerStatus", chapterPO);
    }
    
    /**
     * 更新委托id
     */
    public void updateChapterEntrustId(ChapterEntrustIdUpdateParam param) {
        param.verify();
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        StudyGuideChapterPO chapterPO = new StudyGuideChapterPO();
        chapterPO.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        chapterPO.setStudyGuideBookId(param.getStudyGuideBookId());
        chapterPO.setStudyGuideChapterId(param.getStudyGuideChapterId());
        chapterPO.setEntrustId(param.getEntrustId());
        chapterPO.setModifierId(dongniUserInfoContext.getUserId());
        chapterPO.setModifierName(dongniUserInfoContext.getUserName());
        chapterPO.setModifyDateTime(new Date());
        tikuRepository.update("StudyGuideChapterMapper.updateChapterEntrustId", chapterPO);
    }
    
    /**
     * 获取章节二维码
     */
    public StudyGuideChapterQrcodeListResultDTO getStudyGuideChapterQrcodeList(StudyGuideChapterQrCodeListGetParam param) {
        Verify2.of(param)
                .isValidId(StudyGuideChapterQrCodeListGetParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideChapterQrCodeListGetParam::getStudyGuideBookId)
                .isValidId(StudyGuideChapterQrCodeListGetParam::getStudyGuideChapterId)
                .verify();
        getStudyGuideChapterInfo(param);
        List<StudyGuideChapterQrcodeDTO> qrcodeDTOList = tikuRepository.selectList("StudyGuideChapterMapper.getStudyGuideChapterQrcodeList", param);
        StudyGuideChapterQrcodeListResultDTO resultDTO = new StudyGuideChapterQrcodeListResultDTO();
        resultDTO.setQrcodeList(qrcodeDTOList);
        return resultDTO;
    }

    public StudyGuideChapterQrcodeCheckResultDTO checkStudyGuideChapterQrcode(StudyGuideChapterQrCodeCheckParam param) {
        // 校验参数
        Verify2.of(param)
                .isValidId(StudyGuideChapterQrCodeCheckParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideChapterQrCodeCheckParam::getStudyGuideBookId)
                .isValidId(StudyGuideChapterQrCodeCheckParam::getStudyGuideChapterId)
                .isNotEmpty(StudyGuideChapterQrCodeCheckParam::getAllPictures)
                .isNatural(StudyGuideChapterQrCodeCheckParam::getSizeNumber)
                .verify();
        // 获取章节信息（必要时，可进行后续校验）
        getStudyGuideChapterInfo(param);

        // 获取章节中存在的二维码集合
        List<StudyGuideChapterQrcodeDTO> qrcodeDTOList = tikuRepository.selectList("StudyGuideChapterMapper.getStudyGuideChapterQrcodeList", param);
        Set<String> validQrcodes = qrcodeDTOList.stream()
                .map(StudyGuideChapterQrcodeDTO::getQrcode)
                .collect(Collectors.toSet());

        // 用于存储每一页识别到的二维码（页码 -> 二维码）
        Map<Integer, String> pageQrCodeMap = new HashMap<>();
        // 用于记录二维码识别失败的页面
        List<Integer> missingQrPages = new ArrayList<>();

        List<Map> allPictures = param.getAllPictures();
        int pageNumber = 1;
        for (Map<String, Object> picMap : allPictures) {
//            Integer pageNumber = Integer.valueOf(picMap.get("pageNumber").toString());
            String templateUrl = picMap.get("templateUrl").toString();
            // 调用辅助方法识别二维码
            String qrCode = decodeQrCode(templateUrl);
            if (qrCode == null || qrCode.trim().isEmpty()) {
                missingQrPages.add(pageNumber);
            } else {
                pageQrCodeMap.put(pageNumber, qrCode);
            }
            pageNumber++;
        }

        // 检查识别出的二维码是否存在于章节中
        List<Integer> notInChapterPages = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : pageQrCodeMap.entrySet()) {
            if (!validQrcodes.contains(entry.getValue())) {
                notInChapterPages.add(entry.getKey());
            }
        }

        // 检测重复二维码（构造二维码 -> 页码列表映射）
        Map<String, List<Integer>> qrCodeToPagesMap = new HashMap<>();
        for (Map.Entry<Integer, String> entry : pageQrCodeMap.entrySet()) {
            qrCodeToPagesMap.computeIfAbsent(entry.getValue(), k -> new ArrayList<>()).add(entry.getKey());
        }
        List<String> duplicateErrors = new ArrayList<>();
        for (Map.Entry<String, List<Integer>> entry : qrCodeToPagesMap.entrySet()) {
            List<Integer> pages = entry.getValue();
            if (pages.size() > 1) {
                Collections.sort(pages);
                String pagesStr = pages.stream().map(String::valueOf).collect(Collectors.joining("、"));
                duplicateErrors.add("第" + pagesStr + "面二维码重复");
            }
        }

        // 构造错误信息列表
        List<String> errorMsg = new ArrayList<>();
        Integer sizeNumber = param.getSizeNumber();
        if (!missingQrPages.isEmpty()) {
            if(sizeNumber==1 || (sizeNumber == 2 && missingQrPages.size() * 2 > allPictures.size())){
                Collections.sort(missingQrPages);
                String pagesStr = missingQrPages.stream().map(String::valueOf).collect(Collectors.joining("、"));
                errorMsg.add("第" + pagesStr + "面二维码不存在");
            }
        }
        if (!notInChapterPages.isEmpty()) {
            Collections.sort(notInChapterPages);
            String pagesStr = notInChapterPages.stream().map(String::valueOf).collect(Collectors.joining("、"));
            errorMsg.add("第" + pagesStr + "面二维码不存在于章节中");
        }
        errorMsg.addAll(duplicateErrors);

        boolean isValidSuccess = errorMsg.isEmpty();

        // 构造返回结果
        StudyGuideChapterQrcodeCheckResultDTO resultDTO = new StudyGuideChapterQrcodeCheckResultDTO();
        resultDTO.setIsValidSuccess(isValidSuccess);
        resultDTO.setErrorMsg(errorMsg);
        return resultDTO;
    }

    /**
     * 辅助方法：通过给定的图片 URL 读取文件并使用 zxing 解析二维码
     */
    private String decodeQrCode(String templateUrl) {
        Map<String, String> resultMap = new HashMap<>();

        FileStorageTemplate.get(templateUrl, file -> {
                try {
                    BufferedImage image = ImageIO.read(file);
                    if (image == null) {
                        return;
                    }
                    LuminanceSource source = new BufferedImageLuminanceSource(image);
                    BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));
                    MultiFormatReader reader = new MultiFormatReader();
                    Map<DecodeHintType, Object> hints = new HashMap<>();
                    hints.put(DecodeHintType.POSSIBLE_FORMATS, EnumSet.of(BarcodeFormat.QR_CODE));
                    Result result = reader.decode(bitmap, hints);
                    if( result != null) {
                        resultMap.put("result", result.getText() );
                    }
                } catch (IOException | NotFoundException e) {
                }
            });

        return resultMap.get("result");
    }


    /**
     * 添加章节二维码
     */
    @DistributeLock(moduleName = "TIKU", name = "operateStudyGuideChapterQrcode", argValueKeys = { "[0].studyGuideChapterId" }, waitTime = 0)
    public void addStudyGuideChapterQrcode(StudyGuideChapterQrcodeAddParam param) {
        Verify2.of(param)
                .isValidId(StudyGuideChapterQrcodeAddParam::getStudyGuidePublisherId)
                .isValidId(StudyGuideChapterQrcodeAddParam::getStudyGuideBookId)
                .isValidId(StudyGuideChapterQrcodeAddParam::getStudyGuideChapterId)
                .verify();
        
        StudyGuideFullCodeDTO fullCodeDTO = tikuRepository.selectOne("StudyGuideChapterMapper.getStudyGuideFullCodeForAddQrcode", param);
        if (fullCodeDTO == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "教辅章节不存在: "
                    + param.getStudyGuidePublisherId() + "-"
                    + param.getStudyGuideBookId() + "-"
                    + param.getStudyGuideChapterId()
            );
        }
        
        Integer maxPageCode = fullCodeDTO.getMaxPageCode();
        if (maxPageCode == null) {
            maxPageCode = 0;
        }
        if (maxPageCode >= 4) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "教辅章节的二维码数量已达上限(4)");
        }
        int pageCode = maxPageCode + 1;
        String bookCode = fullCodeDTO.getBookCode();
        String chapterCode = String.format("%02d", fullCodeDTO.getChapterCode());
        String qrcode = bookCode + chapterCode + pageCode;
        
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        Date currentTime = new Date();
        StudyGuideChapterQrcodePO qrcodePO = new StudyGuideChapterQrcodePO();
        qrcodePO.setStudyGuideBookId(param.getStudyGuideBookId());
        qrcodePO.setStudyGuideChapterId(param.getStudyGuideChapterId());
        qrcodePO.setPageCode(pageCode);
        qrcodePO.setQrcode(qrcode);
        qrcodePO.setCreatorId(dongniUserInfoContext.getUserId());
        qrcodePO.setCreatorName(dongniUserInfoContext.getUserName());
        qrcodePO.setCreateDateTime(currentTime);
        qrcodePO.setModifierId(dongniUserInfoContext.getUserId());
        qrcodePO.setModifierName(dongniUserInfoContext.getUserName());
        qrcodePO.setModifyDateTime(currentTime);
        tikuRepository.insert("StudyGuideChapterMapper.insertStudyGuideChapterQrcode", qrcodePO);
    }
    
    /**
     * 获取书籍的所有二维码(order by chapter.sort, qrcode.)用于下载用
     */
    protected List<StudyGuideBookQrcodeDownloadDTO> getStudyGuideBookAllQrcodeList(StudyGuideBookQrcodeDownloadParam param) {
        param.verify();
        return tikuRepository.selectList("StudyGuideChapterMapper.getAllBookQrcodeList", param);
    }
    
    /**
     * 导出批量新增章节的excel模板
     *     章节名称 二维码数量
     * @return 路径
     */
    public String getExportChapterTemplate() {
        return new ExportExcelTemplate("study-guide/batchImportChapter.xlsx", new HashMap<>())
                .exportToFileStorage("教辅批量新增章节", null);
    }
    
    /**
     * 导入批量新增章节的excel模板 但不真正入库
     *    此处下载excel并读取，校验，并返回读取的内容
     *    前端点击确认之后，调用batchImportChapter进行保存操作(会再校验一次)
     * @return 章节信息
     */
    public StudyGuideChapterImportTemplateResultDTO importChapterTemplate(StudyGuideChapterImportTemplateParam param) {
        param.verify();
        Verify2.of(param)
                .isNotBlank(StudyGuideChapterImportTemplateParam::getImportTemplateFileUrl)
                .verify();
        StudyGuideChapterListGetParam chapterListGetParam = new StudyGuideChapterListGetParam();
        chapterListGetParam.setStudyGuidePublisherId(param.getStudyGuidePublisherId());
        chapterListGetParam.setStudyGuideBookId(param.getStudyGuideBookId());
        StudyGuideChapterListResultDTO chapterListResultDTO = getStudyGuideChapterList(chapterListGetParam);
        
        // 读文件
        List<String> header = new ArrayList<>();
        List<Map<String, String>> bodyList = new ArrayList<>();
        FileStorageTemplate.get(param.getImportTemplateFileUrl(), file -> {
            header.addAll(ExcelUtil.getHeader(file));
            bodyList.addAll(ExcelUtil.getBody(file, Arrays.asList("chapterName", "qrcodeQuantity")));
        });
        
        // 校验文件数据
        StudyGuideChapterImportTemplateResultDTO chapterImportTemplateResultDTO = new StudyGuideChapterImportTemplateResultDTO();
        if (CollectionUtils.isEmpty(header) || header.size() < 2
                || !"章节名称".equals(header.get(0))
                || !"二维码数量".equals(header.get(1))) {
            chapterImportTemplateResultDTO.setValid(false);
            chapterImportTemplateResultDTO.getInvalidMessageList().add("表头信息不完整，从左往右依次排列为：章节名称、二维码数量");
            return chapterImportTemplateResultDTO;
        }
        // 导入的章节数据
        List<StudyGuideChapterImportJsonItemDTO> chapterImportJsonList = new ArrayList<>();
        List<StudyGuideChapterImportTemplateResultItemDTO> chapterImportTemplateItemList = chapterImportTemplateResultDTO.getChapterImportTemplateItemList();
        for (int i = 0; i < bodyList.size(); i++) {
            Map<String, String> item = bodyList.get(i);
            int index = i + 1;
            String chapterNameImport = item.get("chapterName");
            String chapterName = MapUtil.getTrim(chapterNameImport, "");
            String qrcodeQuantityImport = item.get("qrcodeQuantity");
            Integer qrcodeQuantity = MapUtil.getIntNullable(qrcodeQuantityImport);
            
            StudyGuideChapterImportTemplateResultItemDTO templateResultItemDTO = new StudyGuideChapterImportTemplateResultItemDTO();
            chapterImportTemplateItemList.add(templateResultItemDTO);
            templateResultItemDTO.setIndex(index);
            templateResultItemDTO.setChapterNameImport(chapterNameImport);
            templateResultItemDTO.setChapterName(chapterName);
            templateResultItemDTO.setQrcodeQuantityImport(qrcodeQuantityImport);
            templateResultItemDTO.setQrcodeQuantity(qrcodeQuantity);
            
            StudyGuideChapterImportJsonItemDTO chapterImportJsonItemDTO = new StudyGuideChapterImportJsonItemDTO();
            chapterImportJsonList.add(chapterImportJsonItemDTO);
            chapterImportJsonItemDTO.setIndex(index);
            chapterImportJsonItemDTO.setChapterName(chapterName);
            chapterImportJsonItemDTO.setQrcodeQuantity(qrcodeQuantity);
        }
        // 校验数据是否正常
        StudyGuideChapterImportJsonResultDTO chapterImportJsonResultDTO = validChapterImport(chapterListResultDTO, chapterImportJsonList);
        Map<Integer, StudyGuideChapterImportJsonResultItemDTO> index2JsonResultItemDTO = chapterImportJsonResultDTO.getChapterImportJsonItemList().stream()
                .collect(toMap(StudyGuideChapterImportJsonResultItemDTO::getIndex, item -> item));
        
        // 迁移校验数据
        boolean chapterItemValid = true;
        for (StudyGuideChapterImportTemplateResultItemDTO templateResultItemDTO : chapterImportTemplateItemList) {
            int index = templateResultItemDTO.getIndex();
            StudyGuideChapterImportJsonResultItemDTO jsonResultItemDTO = index2JsonResultItemDTO.get(index);
            templateResultItemDTO.getChapterNameInvalidMessageList().addAll(jsonResultItemDTO.getChapterNameInvalidMessageList());
            templateResultItemDTO.getQrcodeQuantityInvalidMessageList().addAll(jsonResultItemDTO.getQrcodeQuantityInvalidMessageList());
            templateResultItemDTO.setChapterNameValid(CollectionUtils.isEmpty(templateResultItemDTO.getChapterNameInvalidMessageList()));
            templateResultItemDTO.setQrcodeQuantityValid(CollectionUtils.isEmpty(templateResultItemDTO.getQrcodeQuantityInvalidMessageList()));
            boolean chapterValid = templateResultItemDTO.getChapterNameValid() && templateResultItemDTO.getQrcodeQuantityValid();
            templateResultItemDTO.setValid(chapterValid);
            if (!chapterValid) {
                chapterItemValid = false;
            }
        }
        chapterImportTemplateResultDTO.getInvalidMessageList().addAll(chapterImportJsonResultDTO.getInvalidMessageList());
        boolean resultValid = CollectionUtils.isEmpty(chapterImportTemplateResultDTO.getInvalidMessageList());
        chapterImportTemplateResultDTO.setValid(resultValid && chapterItemValid);
        
        return chapterImportTemplateResultDTO;
    }
    
    /**
     * 批量插入章节
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    @DistributeLock(moduleName = "TIKU", name = "operateStudyGuideChapter", argValueKeys = { "[0].studyGuideBookId" }, waitTime = 0)
    public StudyGuideChapterImportJsonResultDTO importChapterJson(StudyGuideChapterImportJsonParam param) {
        param.verify();
        StudyGuideChapterImportJsonResultDTO resultDTO = new StudyGuideChapterImportJsonResultDTO();
        List<StudyGuideChapterImportJsonItemDTO> chapterList = param.getChapterList();
        if (CollectionUtils.isEmpty(chapterList)) {
            resultDTO.setValid(true);
            return resultDTO;
        }
        long publisherId = param.getStudyGuidePublisherId();
        long bookId = param.getStudyGuideBookId();
        StudyGuideChapterListGetParam chapterListGetParam = new StudyGuideChapterListGetParam();
        chapterListGetParam.setStudyGuidePublisherId(publisherId);
        chapterListGetParam.setStudyGuideBookId(bookId);
        StudyGuideChapterListResultDTO chapterListResultDTO = getStudyGuideChapterList(chapterListGetParam);
        
        StudyGuideChapterImportJsonResultDTO importJsonResultDTO = validChapterImport(chapterListResultDTO, chapterList);
        if (importJsonResultDTO.getValid()) {
            // 校验通过统一入库
            int studyGuideChapterStatusDoing = DictUtil.getDictValue("studyGuideChapterStatus", "doing");
            int studyGuideTemplateStatusNotStart = DictUtil.getDictValue("studyGuideTemplateStatus", "notStart");
            Integer studyGuideAnswerStatusNotUpload = DictUtil.getDictValue("studyGuideAnswerStatus", "notUpload");
            DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
            Date currentTime = new Date();
            int insertChapterCode = chapterListResultDTO.getStudyGuideChapterList()
                    .stream()
                    .map(StudyGuideChapterDTO::getChapterCode)
                    .max(Comparator.naturalOrder())
                    .orElse(0);
            List<StudyGuideChapterImportJsonResultItemDTO> chapterImportJsonItemList = importJsonResultDTO.getChapterImportJsonItemList();
            Map<String, Integer> chapterName2QrcodeQuantity = chapterImportJsonItemList.stream()
                    .collect(toMap(StudyGuideChapterImportJsonResultItemDTO::getChapterName,
                            StudyGuideChapterImportJsonResultItemDTO::getQrcodeQuantity));
            List<StudyGuideChapterPO> insertChapterPOList = new ArrayList<>();
            for (StudyGuideChapterImportJsonResultItemDTO resultItemDTO : chapterImportJsonItemList) {
                insertChapterCode++;
                StudyGuideChapterPO chapterPO = new StudyGuideChapterPO();
                chapterPO.setStudyGuidePublisherId(publisherId);
                chapterPO.setStudyGuideBookId(bookId);
                chapterPO.setChapterCode(insertChapterCode);
                chapterPO.setChapterName(resultItemDTO.getChapterName());
                chapterPO.setChapterStatus(studyGuideChapterStatusDoing);
                chapterPO.setPaperId(studyGuidePaperService.getPaperId());
                chapterPO.setEntrustId(0L);
                chapterPO.setTemplateStatus(studyGuideTemplateStatusNotStart);
                chapterPO.setAnswerStatus(studyGuideAnswerStatusNotUpload);
                chapterPO.setSort(insertChapterCode);
                chapterPO.setCreatorId(dongniUserInfoContext.getUserId());
                chapterPO.setCreatorName(dongniUserInfoContext.getUserName());
                chapterPO.setCreateDateTime(currentTime);
                chapterPO.setModifierId(dongniUserInfoContext.getUserId());
                chapterPO.setModifierName(dongniUserInfoContext.getUserName());
                chapterPO.setModifyDateTime(currentTime);
                insertChapterPOList.add(chapterPO);
            }
            tikuRepository.insert("StudyGuideChapterMapper.insertStudyGuideChapterList", insertChapterPOList);
            
            String bookCode = chapterListResultDTO.getStudyGuideBook().getBookCode();
            List<StudyGuideChapterQrcodePO> insertQrcodePOList = new ArrayList<>();
            for (StudyGuideChapterPO studyGuideChapterPO : insertChapterPOList) {
                long studyGuideChapterId = studyGuideChapterPO.getStudyGuideChapterId();
                String chapterName = studyGuideChapterPO.getChapterName();
                String chapterCode = String.format("%02d", studyGuideChapterPO.getChapterCode());
                int qrcodeQuantity = chapterName2QrcodeQuantity.get(chapterName);
                for (int pageCode = 1; pageCode <= qrcodeQuantity; pageCode++) {
                    String qrcode = bookCode + chapterCode + pageCode;
                    StudyGuideChapterQrcodePO qrcodePO = new StudyGuideChapterQrcodePO();
                    qrcodePO.setStudyGuideBookId(bookId);
                    qrcodePO.setStudyGuideChapterId(studyGuideChapterId);
                    qrcodePO.setPageCode(pageCode);
                    qrcodePO.setQrcode(qrcode);
                    qrcodePO.setCreatorId(dongniUserInfoContext.getUserId());
                    qrcodePO.setCreatorName(dongniUserInfoContext.getUserName());
                    qrcodePO.setCreateDateTime(currentTime);
                    qrcodePO.setModifierId(dongniUserInfoContext.getUserId());
                    qrcodePO.setModifierName(dongniUserInfoContext.getUserName());
                    qrcodePO.setModifyDateTime(currentTime);
                    insertQrcodePOList.add(qrcodePO);
                }
            }
            tikuRepository.insert("StudyGuideChapterMapper.insertStudyGuideChapterQrcodeList", insertQrcodePOList);
        }
        return importJsonResultDTO;
    }
    
    /**
     * 校验导入的数据
     */
    private StudyGuideChapterImportJsonResultDTO validChapterImport(StudyGuideChapterListResultDTO chapterListResultDTO,
                                                                    List<StudyGuideChapterImportJsonItemDTO> chapterList) {
        Map<String, Long> chapterName2Id = chapterListResultDTO.getStudyGuideChapterList().stream()
                .collect(toMap(StudyGuideChapterDTO::getChapterName, StudyGuideChapterDTO::getStudyGuideChapterId));
        StudyGuideChapterImportJsonResultDTO resultDTO = new StudyGuideChapterImportJsonResultDTO();
        List<StudyGuideChapterImportJsonResultItemDTO> chapterImportJsonResultItemList = resultDTO.getChapterImportJsonItemList();
        for (StudyGuideChapterImportJsonItemDTO chapterJsonItem : chapterList) {
            StudyGuideChapterImportJsonResultItemDTO resultItemDTO = new StudyGuideChapterImportJsonResultItemDTO();
            chapterImportJsonResultItemList.add(resultItemDTO);
            Integer index = chapterJsonItem.getIndex();
            String chapterName = Optional.ofNullable(chapterJsonItem.getChapterName()).map(String::trim).orElse(null);
            Integer qrcodeQuantity = chapterJsonItem.getQrcodeQuantity();
            resultItemDTO.setIndex(index);
            resultItemDTO.setChapterName(chapterName);
            resultItemDTO.setQrcodeQuantity(qrcodeQuantity);
            if (index == null) {
                resultItemDTO.getIndexInvalidMessageList().add("教辅章节导入序号必须提供");
            }
            if (StringUtils.isBlank(chapterName)) {
                resultItemDTO.getChapterNameInvalidMessageList().add("教辅章节名称不能为空");
            } else if (chapterName.length() > 50) {
                resultItemDTO.getChapterNameInvalidMessageList().add("教辅章节名称不能大于50个字");
            } else if (chapterName2Id.get(chapterName) != null) {
                resultItemDTO.getChapterNameInvalidMessageList().add("教辅章节名称已经存在(" + chapterName2Id.get(chapterName) + ")");
            }
            if (qrcodeQuantity == null || qrcodeQuantity < 1 || qrcodeQuantity > 4) {
                resultItemDTO.getQrcodeQuantityInvalidMessageList().add("二维码数量必须为1~4");
            }
        }
        
        // 校验导入的教辅章节是否重复
        Map<String, List<StudyGuideChapterImportJsonResultItemDTO>> chapterName2ImportItemList = chapterImportJsonResultItemList.stream()
                .collect(groupingBy(StudyGuideChapterImportJsonResultItemDTO::getChapterName));
        for (Map.Entry<String, List<StudyGuideChapterImportJsonResultItemDTO>> entry : chapterName2ImportItemList.entrySet()) {
            String chapterName = entry.getKey();
            if (StringUtils.isBlank(chapterName)) {
                continue;  // 空的不在这校验
            }
            List<StudyGuideChapterImportJsonResultItemDTO> importItemList = entry.getValue();
            if (importItemList.size() < 2) {
                continue;
            }
            for (StudyGuideChapterImportJsonResultItemDTO resultItemDTO : importItemList) {
                Integer index = resultItemDTO.getIndex();
                String duplicateIndexStr = importItemList.stream()
                        .map(StudyGuideChapterImportJsonResultItemDTO::getIndex)
                        .filter(item -> index != null && !item.equals(index))
                        .map(Object::toString)
                        .collect(joining(","));
                resultItemDTO.getChapterNameInvalidMessageList().add("教辅章节名称重复序号: " + duplicateIndexStr);
            }
        }
        
        boolean chapterItemValid = true;
        for (StudyGuideChapterImportJsonResultItemDTO resultItemDTO : chapterImportJsonResultItemList) {
            resultItemDTO.setIndexValid(CollectionUtils.isEmpty(resultItemDTO.getIndexInvalidMessageList()));
            resultItemDTO.setChapterNameValid(CollectionUtils.isEmpty(resultItemDTO.getChapterNameInvalidMessageList()));
            resultItemDTO.setQrcodeQuantityValid(CollectionUtils.isEmpty(resultItemDTO.getQrcodeQuantityInvalidMessageList()));
            boolean chapterValid = resultItemDTO.getIndexValid() && resultItemDTO.getChapterNameValid() && resultItemDTO.getQrcodeQuantityValid();
            resultItemDTO.setValid(chapterValid);
            if (!chapterValid) {
                chapterItemValid = false;
            }
        }
        
        int existChapterSize = chapterName2Id.size();
        int importChapterSize = chapterList.size();
        if (existChapterSize + importChapterSize > 99) {
            resultDTO.getInvalidMessageList().add("教辅的章节数量上限(99)，"
                    + "现有" + existChapterSize + "个章节，"
                    + "导入" + importChapterSize + "个章节; "
                    + "导入后超过上限"
            );
        }
        boolean resultValid = CollectionUtils.isEmpty(resultDTO.getInvalidMessageList());
        resultDTO.setValid(resultValid && chapterItemValid);
        
        return resultDTO;
    }
}
