package com.dongni.tiku.studyguide.service;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuidePaperStructureDTO;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideTemplateStructureDTO;
import com.dongni.tiku.studyguide.bean.po.StudyGuideTemplateStructurePO;
import com.dongni.tiku.studyguide.utils.StudyGuidePaperUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025/03/14
 */
@Service
public class StudyGuideTemplateStructureService {
    
    @Autowired
    private TikuRepository tikuRepository;
    
    /**
     * 获取模板结构
     * @param paperId 试卷id
     * @return 模板结构
     */
    public List<StudyGuideTemplateStructureDTO> getTemplateStructureList(long paperId) {
        return tikuRepository.selectList("StudyGuideTemplateStructureMapper.getStructureListByPaperId", paperId);
    }
    
    /**
     * 保存模板结构
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public List<StudyGuideTemplateStructurePO> saveTemplateStructure(StudyGuideChapterDTO chapterDTO, Map<String, Object> paper) {
        List<StudyGuideTemplateStructurePO> templateStructurePOList = getTemplateStructure(chapterDTO, paper);
        // 先删
        tikuRepository.delete("StudyGuideTemplateStructureMapper.deleteStructureByPaperId", chapterDTO.getPaperId());
        // 再插
        if (CollectionUtils.isNotEmpty(templateStructurePOList)) {
            tikuRepository.insert("StudyGuideTemplateStructureMapper.insertStructureList", templateStructurePOList);
        }
        return templateStructurePOList;
    }
    
    /**
     * 获取模板结构信息
     */
    protected List<StudyGuideTemplateStructurePO> getTemplateStructure(StudyGuideChapterDTO chapterDTO, Map<String, Object> paper) {
        List<StudyGuidePaperStructureDTO> templatePaperStructureList = StudyGuidePaperUtil.getPaperStructure(paper);
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        Date currentTime = new Date();
        long paperId = chapterDTO.getPaperId();
        List<StudyGuideTemplateStructurePO> templateStructurePOList = new ArrayList<>();
        for (StudyGuidePaperStructureDTO structureDTO : templatePaperStructureList) {
            StudyGuideTemplateStructurePO structurePO = new StudyGuideTemplateStructurePO();
            structurePO.setPaperId(paperId);
            structurePO.setCourseId(structureDTO.getCourseId());
            structurePO.setCourseName(structureDTO.getCourseName());
            structurePO.setQuestionNumber(structureDTO.getQuestionNumber());
            String structureNumber = structureDTO.getStructureNumber();
            if (structureNumber.length() > 128) {
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "前端显示题号structureNumber长度超过128");
            }
            structurePO.setStructureNumber(structureNumber);
            structurePO.setReadType(structureDTO.getReadType());
            structurePO.setQuestionType(structureDTO.getQuestionType());
            structurePO.setQuestionTypeName(structureDTO.getQuestionTypeName());
            structurePO.setQuestionIndex(structureDTO.getQuestionIndex());
            structurePO.setOptionsCount(structureDTO.getOptionsCount());
            structurePO.setCorrectAnswer(structureDTO.getCorrectAnswer());
            structurePO.setScoreValue(structureDTO.getScoreValue());
            structurePO.setUnitType(structureDTO.getUnitType());
            structurePO.setOptionalGroup(structureDTO.getOptionalGroup());
            structurePO.setOptionalCount(structureDTO.getOptionalCount());
            structurePO.setCreatorId(dongniUserInfoContext.getUserId());
            structurePO.setCreatorName(dongniUserInfoContext.getUserName());
            structurePO.setCreateDateTime(currentTime);
            structurePO.setModifierId(dongniUserInfoContext.getUserId());
            structurePO.setModifierName(dongniUserInfoContext.getUserName());
            structurePO.setModifyDateTime(currentTime);
            templateStructurePOList.add(structurePO);
        }
        return templateStructurePOList;
    }
    
    /**
     * 保存答案到mysql中
     * @param paperId                     试卷id
     * @param templateStructureUpdateList 结构带答案 questionNumber correctAnswer
     */
    public void updateTemplateStructureAnswer(long paperId, List<StudyGuideTemplateStructurePO> templateStructureUpdateList) {
        if (CollectionUtils.isEmpty(templateStructureUpdateList)) {
            return;
        }
        DongniUserInfoContext dongniUserInfoContext = DongniUserInfoContext.get();
        Date currentTime = new Date();
        for (StudyGuideTemplateStructurePO structurePO : templateStructureUpdateList) {
            structurePO.setPaperId(paperId);
            structurePO.setModifierId(dongniUserInfoContext.getUserId());
            structurePO.setModifierName(dongniUserInfoContext.getUserName());
            structurePO.setModifyDateTime(currentTime);
            tikuRepository.update("StudyGuideTemplateStructureMapper.updateStructureAnswer", structurePO);
        }
    }
}
