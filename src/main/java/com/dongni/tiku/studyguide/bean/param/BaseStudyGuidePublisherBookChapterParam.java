package com.dongni.tiku.studyguide.bean.param;

import com.dongni.commons.utils.verify.Verify2;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 * @date 2025/03/27
 */
@Getter
@Setter
public class BaseStudyGuidePublisherBookChapterParam extends BaseStudyGuidePublisherBookParam {
    
    private static final long serialVersionUID = -57540229444859392L;
    
    /** 教辅章节id */
    private Long studyGuideChapterId;
    
    public BaseStudyGuidePublisherBookChapterParam() {
    }
    
    public BaseStudyGuidePublisherBookChapterParam(BaseStudyGuidePublisherParam param) {
        super(param);
    }
    
    public BaseStudyGuidePublisherBookChapterParam(BaseStudyGuidePublisherBookParam param) {
        super(param);
    }
    
    public BaseStudyGuidePublisherBookChapterParam(BaseStudyGuidePublisherBookChapterParam param) {
        super(param);
        this.studyGuideChapterId = param.getStudyGuideChapterId();
    }
    
    /** 校验参数 */
    @Override
    public void verify() {
        Verify2.of(this)
                .isValidId(BaseStudyGuidePublisherBookChapterParam::getStudyGuidePublisherId)
                .isValidId(BaseStudyGuidePublisherBookChapterParam::getStudyGuideBookId)
                .isValidId(BaseStudyGuidePublisherBookChapterParam::getStudyGuideChapterId)
                .verify();
    }
}
