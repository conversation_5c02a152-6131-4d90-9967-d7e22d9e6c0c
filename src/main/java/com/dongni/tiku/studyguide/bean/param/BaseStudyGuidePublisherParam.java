package com.dongni.tiku.studyguide.bean.param;

import com.dongni.commons.entity.BaseRequestParams;
import com.dongni.commons.utils.verify.Verify2;
import lombok.Getter;
import lombok.Setter;

/**
 *
 * <AUTHOR>
 * @date 2025/03/27
 */
@Getter
@Setter
public class BaseStudyGuidePublisherParam extends BaseRequestParams {
    
    private static final long serialVersionUID = -57540229444859392L;
    
    /** 教辅商id */
    private Long studyGuidePublisherId;
    
    public BaseStudyGuidePublisherParam() {
    }
    
    public BaseStudyGuidePublisherParam(BaseStudyGuidePublisherParam param) {
        this.studyGuidePublisherId = param.studyGuidePublisherId;
    }
    
    /** 校验参数 */
    public void verify() {
        Verify2.of(this)
                .isValidId(BaseStudyGuidePublisherParam::getStudyGuidePublisherId)
                .verify();
    }
}
