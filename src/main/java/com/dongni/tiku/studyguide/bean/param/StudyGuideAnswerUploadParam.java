package com.dongni.tiku.studyguide.bean.param;

import com.dongni.tiku.studyguide.bean.dto.StudyGuidePaperStructureDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2025/03/25
 */
@Setter
@Getter
public class StudyGuideAnswerUploadParam extends BaseStudyGuidePublisherBookChapterParam {
    
    private static final long serialVersionUID = 4101232542915059925L;
    
    /** 是否为模板试卷 获取列表供上传操作时提供 原样带回来 */
    private Boolean templatePaper;
    /** 结构答案 */
    private List<StudyGuidePaperStructureDTO> structureList;
}
