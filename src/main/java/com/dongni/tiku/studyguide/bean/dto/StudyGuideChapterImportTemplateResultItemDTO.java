package com.dongni.tiku.studyguide.bean.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
public class StudyGuideChapterImportTemplateResultItemDTO {
    
    /** 导入的序号 一般来说 1对应了excel的第二行 */
    private Integer index;
    
    /** 导入数据是否有效 */
    private Boolean valid;
    
    /** 章节名称 */
    private String chapterName;
    /** 章节名称是否有效 */
    private Boolean chapterNameValid;
    /** 导入的章节错误信息 没有消息就是最好的消息 */
    private List<String> chapterNameInvalidMessageList = new ArrayList<>();
    /** 导入的章节名称 */
    private String chapterNameImport;
    
    /** 二维码数量 */
    private Integer qrcodeQuantity;
    /** 二维码数量是否有效 */
    private Boolean qrcodeQuantityValid;
    /** 二维码数量错误信息 没有消息就是最好的消息 */
    private List<String> qrcodeQuantityInvalidMessageList = new ArrayList<>();
    /** 导入的二维码数量 */
    private String qrcodeQuantityImport;
    
}
