package com.dongni.tiku.manager.impl;

import com.dongni.common.mongo.IManager;
import com.dongni.tiku.bean.TikuMongodb;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2025/03/24
 */
@Service
public class StudyGuideTemplateManager extends IManager {
    
    protected StudyGuideTemplateManager(TikuMongodb tikuMongodb) {
        super(tikuMongodb, TikuMongodb.COLLECTION_STUDY_GUIDE_TEMPLATE, "教辅模板");
        deleteBackExpireMilliseconds = 1000L * 86400 * 30;
    }
}
