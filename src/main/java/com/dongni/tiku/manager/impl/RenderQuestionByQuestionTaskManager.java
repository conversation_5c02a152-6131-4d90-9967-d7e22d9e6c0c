package com.dongni.tiku.manager.impl;

import com.dongni.common.mongo.IManager;
import com.dongni.tiku.bean.TikuMongodb;
import com.mongodb.client.model.IndexOptions;
import org.bson.Document;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2025/04/21
 */
@Service
public class RenderQuestionByQuestionTaskManager extends IManager {

    public RenderQuestionByQuestionTaskManager(TikuMongodb tikuMongodb) {
        super(tikuMongodb, TikuMongodb.COLLECTION_RENDER_QUESTION_BY_QUESTION_TASK, "通过试题渲染试题图片任务");
        
        initIndex("renderQuestionRecordId_status", new Document("renderQuestionRecordId", 1.0).append("status", 1.0));
        initIndex("status_createTimestamp", new Document("status", 1.0).append("createTimestamp", 1.0));
        initIndex("status_progressTimeoutTimestamp", new Document("status", 1.0).append("progressTimeoutTimestamp", 1.0), new IndexOptions().sparse(true));
    }
}
