package com.dongni.tiku.wusan.inside.bean;

import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/07/23
 */
@Data
public class WusanAccountUserDTO {
    
    /** 主键id */
    private Long userId;
    
    /** 学校id */
    private Long schoolId;
    /** 学校名称 */
    private String schoolName;
    
    /** 五三用户名称 */
    private String wusanAccount;
    /** 五三用户accountId 创建后存储 */
    private String wusanAccountId;
    /** 五三用户名称 创建后存储 */
    private String wusanAccountName;
    
    /** 五三机构名称 */
    private String wusanAdminAccount;
    /** 五三机构用户id */
    private String wusanAdminAccountId;
    /** 五三机构机构编码 */
    private String wusanAdminAccountCode;
    /** 五三机构名称 */
    private String wusanAdminAccountName;
    /** 五三机构token */
    private String wusanAdminAccountToken;
    /** 五三机构AES密钥 */
    private String wusanAdminAccountSecretKey;
    /** 五三机构AES偏移量 */
    private String wusanAdminAccountIv;
    
}
