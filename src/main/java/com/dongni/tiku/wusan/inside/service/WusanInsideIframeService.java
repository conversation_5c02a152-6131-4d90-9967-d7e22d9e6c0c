package com.dongni.tiku.wusan.inside.service;

import com.dongni.basedata.bean.dto.UserDTO;
import com.dongni.common.auth.DongniClient;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.verify.Verify2;
import com.dongni.tiku.wusan.inside.bean.WusanAccountUserDTO;
import com.dongni.tiku.wusan.inside.bean.WusanInsideIframeParam;
import com.dongni.tiku.wusan.inside.bean.WusanInsideIframeParamParams;
import com.dongni.tiku.wusan.inside.controller.WusanInsideIframeController;
import com.dongni.tiku.wusan.inside.utils.WusanInsideAesUtil;
import com.dongni.tiku.wusan.inside.utils.WusanInsideUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2024/07/19
 */
@Service
public class WusanInsideIframeService {
    
    private static final String ALIYUN_PRODUCT_HOST = "https://www.dongni100.com/api";
    
    @Autowired
    private DongniClient dongniClient;
    @Autowired
    private WusanInsideAccountService wusanInsideAccountService;
    
    /**
     * 获取iframe参数
     *   1. 参数: u, schoolId  返回 u       在www.dongni100.com续命     用于组卷后从五三跳转到生产后跳转会子系统
     *   2. 参数: r, schoolId  返回 c p u r 需要在www.dongni100.com注册 用于组卷后从五三跳转到生产后跳转会子系统
     *   3. 参数:    schoolId  返回 c p     直接返回  组卷后从五三跳转到生产就结束了
     * @param params 见上面说明
     * @return 见上面说明
     */
    public WusanInsideIframeParam getIframeParam(WusanInsideIframeParamParams params) {
        Verify2.of(params)
                .isNotNull(WusanInsideIframeParamParams::getSchoolId)
                .optional(WusanInsideIframeParamParams::getU)
                .optional(WusanInsideIframeParamParams::getR)
                .verify();
        UserDTO userDTO = UserDTO.get();
        
        WusanInsideIframeParam result = new WusanInsideIframeParam();
        
        // 1. 参数: u    返回 u       在www.dongni100.com续命
        String randomId = params.getU();
        if (StringUtils.isNotBlank(randomId)) {
            result.setU(randomId);
            // expireIframeParamInProductAliyun
            String url = ALIYUN_PRODUCT_HOST
                    + WusanInsideIframeController.WUSAN_INSIDE_IFRAME_URL_PREFIX
                    + WusanInsideIframeController.WUSAN_INSIDE_IFRAME_URL_EXPIRE_PARAM_IN_MAIN;
            dongniClient.post(url, null, result.toMap());
            return result;
        }
        
        // 获取 c p
        Long schoolId = params.getSchoolId();
        WusanAccountUserDTO wusanAccountUserDTO = wusanInsideAccountService.getWusanAccountUserDTO(userDTO, schoolId);
        String accountCode = wusanAccountUserDTO.getWusanAdminAccountCode();
        String account = wusanAccountUserDTO.getWusanAccount();
        String secretKey = wusanAccountUserDTO.getWusanAdminAccountSecretKey();
        String iv = wusanAccountUserDTO.getWusanAdminAccountIv();
        if (StringUtils.isBlank(secretKey) || StringUtils.isBlank(iv)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR,
                    "五三机构accountId:" + wusanAccountUserDTO.getWusanAccountId() + ",加密字段为空,请联系管理员!");
        }
        result.setC(accountCode);
        result.setP(WusanInsideAesUtil.encrypt(account, secretKey, iv));
        
        // r 提供则需要再www.dongni100.com注册
        String redirect = params.getR();
        if (StringUtils.isNotBlank(redirect)) {
            result.setR(redirect);
            // saveIframeParamInProductAliyun
            String url = ALIYUN_PRODUCT_HOST
                    + WusanInsideIframeController.WUSAN_INSIDE_IFRAME_URL_PREFIX
                    + WusanInsideIframeController.WUSAN_INSIDE_IFRAME_URL_SAVE_PARAM_IN_MAIN;
            Map<String, Object> responseMap = dongniClient.post(url, null, result.toMap());
            WusanInsideIframeParam registerResult = WusanInsideIframeParam.of(responseMap);
            result.setU(registerResult.getU());
        }
        
        return result;
    }
    
    /**
     * 在生产环境保存iframe参数
     * @param iframeParam r
     * @return u
     */
    public WusanInsideIframeParam saveIframeParamInProductAliyun(WusanInsideIframeParam iframeParam) {
        Verify2.of(iframeParam)
                .isNotBlank(WusanInsideIframeParam::getR)
                .verify();
        String randomId = WusanInsideUtil.generateIframeParamU();
        iframeParam.setU(randomId);
        String json = JSONUtil.toJson(iframeParam);
        String iframeParamKey = getIframeParamKey(randomId);
        JedisTemplate.execute(jedis -> jedis.setex(iframeParamKey, 60 * 60, json));
        return iframeParam;
    }
    
    /**
     * 给生产环境的iframe参数续命
     * @param iframeParam u
     * @return u
     */
    public WusanInsideIframeParam expireIframeParamInProductAliyun(WusanInsideIframeParam iframeParam) {
        Verify2.of(iframeParam)
                .isNotBlank(WusanInsideIframeParam::getU)
                .verify();
        String randomId = iframeParam.getU();
        String iframeParamKey = getIframeParamKey(randomId);
        JedisTemplate.execute(jedis -> jedis.expire(iframeParamKey, 60 * 60));
        return iframeParam;
    }
    
    /**
     * 从生产环境获取iframe参数
     * @param iframeParam u
     * @return iframeParam 存储在redis中的数据 获取之后 其存活时间会降低到10分钟
     */
    public WusanInsideIframeParam getIframeRedirectInProductAliyun(WusanInsideIframeParam iframeParam) {
        Verify2.of(iframeParam)
                .isNotBlank(WusanInsideIframeParam::getU)
                .verify();
        String randomId = iframeParam.getU();
        String iframeParamKey = getIframeParamKey(randomId);
        String json = JedisTemplate.execute(jedis -> jedis.get(iframeParamKey));
        if (StringUtils.isBlank(json)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "跳转信息不存在: " + randomId);
        }
        JedisTemplate.execute(jedis -> jedis.expire(iframeParamKey, 60 * 10));
        
        return JSONUtil.parse(json, WusanInsideIframeParam.class);
    }
    
    private String getIframeParamKey(String randomId) {
        return "TIKU:WUSAN:IFRAME:U:" + randomId;
    }

}
