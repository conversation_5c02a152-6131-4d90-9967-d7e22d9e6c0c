package com.dongni.tiku.wusan.inside.client;

import org.springframework.stereotype.Component;

/**
 * 五三身份互认
 *   仅处理部分错误，其他错误不会直接抛异常，需要调用方进行处理
 *      因为防止调用失败或者存储失败，需要重试，重试会产生如“账号已存在”的异常，这些是正常的
 *
 *  3100002 方法参数校验异常,返回异常信息为具体的异常字段数据
 *  4320008 账号不存在！
 *  4320016 账号已存在！
 *  4320003 账号异常，请联系管理员！
 *  4320015 账号审核中！
 *  4310008 业务类型不存在！
 *  4320017 管理员账号无法修改！
 *  4320018 管理员账号不存在！
 *  4320019 管理员账号无法移除！
 *  4320020 当前机构下账号不存在！
 *  4320021 用户无关联机构，无法修改！
 *  4360006 业务方标识不存在！
 *
 * <AUTHOR>
 * @date 2024/07/19
 */
@Component
public class WusanInsideAuthClient extends BaseWusanInsideClient {
    
    private static final String HOST = "https://open.53inside.com/api/auth";
    
    @Override
    protected String getFullUri(String uri) {
        return HOST + uri;
    }
    
    @Override
    protected boolean isCodeFail(String code) {
        // 系统开了个小差，请稍后再试！
        return "3100001".equals(code);
    }
}
