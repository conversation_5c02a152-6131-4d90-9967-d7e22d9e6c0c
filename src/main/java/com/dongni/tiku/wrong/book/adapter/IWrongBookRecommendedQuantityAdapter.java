package com.dongni.tiku.wrong.book.adapter;

import com.dongni.tiku.wrong.book.bean.dto.WrongBookPaperRecommendedQuantity;

/**
 * <p>
 *     推荐配置转化类
 * </p>
 *
 * <AUTHOR>
 * @className IWrongBookRecommendedQuantityAdapter
 * @since 2024/9/12 11:36
 */
public interface IWrongBookRecommendedQuantityAdapter {

    /**
     *   类题未推完，进入类题的时候进行初始化
     *   初始化逻辑：
     *   1.获取历史建议推题数量结构 wrongBookPaperRecommendNumberList
     *   2.将原题进行难度映射 (0.7, 1.0] 简单 3    (0.4, 0.7] 中等 2    [0, 0.4] 困难 1
     *   3.与系统进行难度对比 系统区间<原题转化区间：转化为拓展题，系统区间>=原题区间，转化为巩固题，转化后若巩固/拓展大于2时，则设置为最大值2
     *   4.取所有试题巩固/拓展的最大值
     * @param recommendationId  推题任务ID
     * @param wrongBookPaperId  个册试卷任务ID
     * @return v4.0推题建议数量
     */
    WrongBookPaperRecommendedQuantity adaptWrongBookRecommendedQuantityByRecommendationTask(Long recommendationId, Long wrongBookPaperId);

    /**
     *   类题可能已经推完，进入选题的时候进行初始化
     *   初始化逻辑：
     *   1.获取历史建议推题数量结构 wrongBookPaperRecommendNumberList
     *   2.将原题进行难度映射 (0.7, 1.0] 简单 3    (0.4, 0.7] 中等 2    [0, 0.4] 困难 1
     *   3.与系统进行难度对比 系统区间<原题转化区间：转化为拓展题，系统区间>=原题区间，转化为巩固题，转化后若巩固/拓展大于2时，则设置为最大值2
     *   4.取所有试题巩固/拓展的最大值
     * @param wrongBookId       个册任务ID
     * @param wrongBookPaperId  个册试卷任务ID
     * @return v4.0推题建议数量
     */
    WrongBookPaperRecommendedQuantity adaptWrongBookRecommendedQuantityByWrongBookTask(Long wrongBookId, Long wrongBookPaperId);


    /**
     * 初始化推题
     *
     * @param recommendationId       推荐任务
     * @param recommendationTaskType 任务类型
     */
    void initRecommendedSimilarQuestionDifficultyTypeAndIndex(Long recommendationId, int recommendationTaskType);

    /**
     * 初始化推题
     * @param wrongBookId  个册任务ID
     */
    void initWrongBookSimilarQuestionDifficultyTypeAndIndex(Long wrongBookId);

    /**
     * 初始化分层，将V4.0的分层映射为V4.1的分层
     *
     * @param wrongBookId      个册任务ID
     * @param wrongBookPaperId 个册子任务ID
     */
    void initWrongBookSimilarQuestionLevelForV41(Long wrongBookId, long wrongBookPaperId);
}
