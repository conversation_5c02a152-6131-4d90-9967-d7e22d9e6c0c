package com.dongni.tiku.wrong.book.handler.grasp;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.plan.service.ExamPaperService;
import com.dongni.exam.question.service.ExamQuestionStructureService;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.holiday.task.manager.IHolidayTaskGraspCompositeManager;
import com.dongni.tiku.holiday.task.manager.IHolidayTaskStudentLevelManager;
import com.dongni.tiku.holiday.task.service.IHolidayTaskConfigService;
import com.dongni.tiku.manager.impl.KnowledgeManager;
import com.dongni.tiku.own.service.OwnPaperService;
import com.dongni.tiku.own.service.OwnQuestionService;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbExamPaper;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbExamPaperQuestion;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbExamPaperQuestionRate;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbQuestion;
import com.dongni.tiku.wrong.book.bean.cycle.knowledge.CycleWbGraspComposite;
import com.dongni.tiku.wrong.book.bean.cycle.knowledge.CycleWbGraspHelper;
import com.dongni.tiku.wrong.book.bean.dto.CycleWbGraspHelperDTO;
import com.dongni.tiku.wrong.book.bean.dto.WrongBookParticipateExamDTO;
import com.dongni.tiku.wrong.book.bean.param.WrongBookClassParam;
import com.dongni.tiku.wrong.book.manager.ICycleClassGraspCompositeManager;
import com.dongni.tiku.wrong.book.service.IWrongBookQuestionStatService;
import com.dongni.tiku.wrong.book.service.WrongBookService;
import com.dongni.tiku.wrong.book.service.WrongBookStudentService;
import com.dongni.tiku.wrong.book.service.impl.IWrongBookPaperStructureService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.*;
import static com.mongodb.client.model.Sorts.ascending;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <p>班级周期本知识点处理类</p>
 *
 * <AUTHOR>
 * @className ClassCycleGraspHandler
 * @since 2023/7/10 17:30
 */
@Service
public class HolidayTaskGraspHandler extends AbstractCycleGraspHandler{

    private static final Logger log = LogManager.getLogger(HolidayTaskGraspHandler.class);

    public HolidayTaskGraspHandler(AnalysisMongodb analysisMongodb) {
        super(analysisMongodb);
    }

    @Autowired
    private IHolidayTaskConfigService holidayTaskConfigService;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private ExamQuestionStructureService examQuestionStructureService;
    @Autowired
    private OwnQuestionService ownQuestionService;
    @Autowired
    private IWrongBookPaperStructureService wrongBookPaperStructureService;
    @Autowired
    private IHolidayTaskStudentLevelManager holidayTaskStudentLevelManager;
    @Autowired
    private IHolidayTaskGraspCompositeManager holidayTaskGraspCompositeManager;
    @Autowired
    private KnowledgeManager knowledgeManager;
    @Autowired
    private IWrongBookQuestionStatService wrongBookQuestionStatService;

    @Override
    public String getCycleGraspType() {
        return "holidayTask";
    }


    @Override
    public void createCycleWbGraspHelper(long orgId,
                                         String orgName,
                                         Map<String, Object> holidayTaskInfo,
                                         List<Document> questionDocList,
                                         Map<String, Object> frontParams) {
        List<CycleWbExamPaperQuestion> cycleWbExamPaperQuestionList = Lists.newArrayList();
        List<Document> cycleWbExamPaperQuestionRateDocList = Lists.newArrayList();
        CycleWbGraspHelper cycleWbGraspHelper = new CycleWbGraspHelper();
        // 获取配置项
        Long holidayTaskId = orgId;
        Document holidayTaskConfig = holidayTaskConfigService.getHolidayTaskConfig(holidayTaskId);
        if (MapUtils.isEmpty(holidayTaskConfig)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "配置项不存在");
        }
        long holidayTaskCourseId = MapUtil.getLong(holidayTaskConfig, "courseId");
        List<Map<String, Object>> selectedLayeredExamList = MapUtil.getListMap(holidayTaskConfig, "selectedLayeredExamList");
        List<Long> examIdList = selectedLayeredExamList.stream().map(item -> MapUtil.getLong(item, "examId")).collect(Collectors.toList());
        // 获取试卷
        List<Map<String, Object>> examPaperList = examPaperService.getExamPaperByExamIds(MapUtil.of("examIdList", examIdList));
        List<Long> courseIds = examPaperList.stream().map(item -> MapUtil.getLong(item, "courseId")).distinct().collect(Collectors.toList());
        List<Map<String, Object>> courseList = commonCourseService.getCourseByCourseIds(MapUtil.of("courseIds", courseIds));
        Map<Long, Map<String, Object>> courseMap = courseList.stream()
                .collect(toMap(item -> MapUtil.getLong(item, "courseId"), item -> item));

        List<CycleWbExamPaper> cycleWbExamPaperList = Lists.newArrayList();
        Set<String> examNameList = Sets.newHashSet();
        for (Map<String, Object> examPaper : examPaperList) {
            List<CycleWbExamPaper> cycleWbExamPaperItemList = Lists.newArrayList();
            String examName = MapUtil.getString(examPaper, "examName");
            long examId = MapUtil.getLong(examPaper, "examId");
            long paperId = MapUtil.getLong(examPaper, "paperId");
            String examDate = MapUtil.getString(examPaper, "startDate");
            long dateLongValue = DateUtil.getDateLongValue(examDate);
            long examPaperCourseId = MapUtil.getLong(examPaper, "courseId");
            Map<String, Object> courseInfo = courseMap.get(examPaperCourseId);
            if (MapUtils.isEmpty(courseInfo)) {
                continue;
            }
            List<Map<String, Object>> subCourseInfoList = MapUtil.getListMap(courseInfo, "subCourseInfoList");
            List<Long> subCourseIdList = subCourseInfoList.stream().map(subCourseInfo -> MapUtil.getLong(subCourseInfo, "courseId"))
                    .distinct()
                    .collect(Collectors.toList());

            if (!subCourseIdList.contains(holidayTaskCourseId)) {
                continue;
            }

            for (Map<String, Object> subCourseInfo : subCourseInfoList) {
                long examPaperSubCourseId = MapUtil.getLong(subCourseInfo, "courseId");
                if (ObjectUtil.isValueEquals(examPaperSubCourseId, holidayTaskCourseId)) {
                    CycleWbExamPaper cycleWbExamPaper = CycleWbExamPaper.of(examId, dateLongValue, paperId);
                    cycleWbExamPaperItemList.add(cycleWbExamPaper);
                }
            }
            if (CollectionUtils.isEmpty(cycleWbExamPaperItemList)) {
                examNameList.add(examName);
            }
            cycleWbExamPaperList.addAll(cycleWbExamPaperItemList);
        }

        if (CollectionUtils.isNotEmpty(examNameList)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, String.format("所选考试：%s 对应科目未录题，无法生成学情分析，请选择其他考试后重试", examNameList));
        }

        // 构建CycleWbQuestion
        List<Long> paperIdList = cycleWbExamPaperList.stream().map(CycleWbExamPaper::getPaperId).collect(Collectors.toList());
        List<Map<String, Object>> questionStructureList = wrongBookPaperStructureService.getPaperStructureListByIds(paperIdList);
        Map<Long, List<Map<String, Object>>> questionStructureGroup = questionStructureList.stream()
                .filter(item -> ObjectUtil.isNotBlank(item.get("questionId")))
                .filter(item -> ObjectUtil.isValueEquals(holidayTaskCourseId, item.get("courseId")))
                .collect(groupingBy(item -> MapUtil.getLong(item, "paperId")));

        List<ObjectId> questionObjIdList = questionStructureList.stream()
                .filter(item -> ObjectUtil.isNotBlank(item.get("questionId")))
                .map(item -> new ObjectId(MapUtil.getString(item, "questionId")))
                .distinct()
                .collect(Collectors.toList());
        List<Document> examPaperQuestionDocList = ownQuestionService.getQuestions(questionObjIdList);
        Map<String, Document> questionDocMap = examPaperQuestionDocList.stream()
                .collect(toMap(item -> MapUtil.getString(item, "_id"), item -> item));

        for (CycleWbExamPaper cycleWbExamPaper : cycleWbExamPaperList) {
            long paperId = cycleWbExamPaper.getPaperId();
            List<Map<String, Object>> questionStructureItemList = questionStructureGroup.get(paperId);
            for (Map<String, Object> questionStructureItem : questionStructureItemList) {
                String questionId = MapUtil.getString(questionStructureItem, "questionId");
                Document questionDoc = questionDocMap.get(questionId);
                if (MapUtils.isEmpty(questionDoc)) {
                    continue;
                }
                double difficulty = MapUtil.getDouble(questionDoc, "difficulty");
                List<String> knowledgeIdList = MapUtil.getListType(questionDoc, "knowledgeIdList", MapUtil::getString);
                CycleWbQuestion cycleWbQuestion = CycleWbQuestion.of(questionId, difficulty, Sets.newHashSet(knowledgeIdList));
                // 构建CycleWbExamPaperQuestion
                CycleWbExamPaperQuestion cycleWbExamPaperQuestion = CycleWbExamPaperQuestion.of(cycleWbExamPaper, cycleWbQuestion);
                cycleWbExamPaperQuestionList.add(cycleWbExamPaperQuestion);
            }
        }

        // 获取得分率
        List<Map<String, Object>> examQuestionDocList = getCycleWbGraspHelperScoreRate(orgName, orgId, cycleWbExamPaperQuestionList, holidayTaskInfo);
        Map<String, List<Map<String, Object>>> examQuestionDocMap = examQuestionDocList.stream()
                .collect(Collectors.groupingBy(item -> MapUtil.getString(item, "examId") + ":" +
                        MapUtil.getString(item, "paperId") + ":" +
                        MapUtil.getString(item, "questionId")));
        cycleWbExamPaperQuestionList.forEach(cycleWbExamPaperQuestion -> {
            // 构建CycleWbExamPaperQuestionRate
            CycleWbExamPaper examPaper = cycleWbExamPaperQuestion.getExamPaper();
            CycleWbQuestion question = cycleWbExamPaperQuestion.getQuestion();
            long examId = examPaper.getExamId();
            long paperId = examPaper.getPaperId();
            String questionId = question.getQuestionId();
            String uniqueKey = examId + ":" + paperId + ":" + questionId;
            List<Map<String, Object>> examQuestionItemDocList = examQuestionDocMap.get(uniqueKey);
            double averageRate = 0.0D;
            if (CollectionUtils.isNotEmpty(examQuestionItemDocList)) {
                averageRate = examQuestionItemDocList.stream()
                        .map(item -> new BigDecimal(MapUtil.getString(item, "averageRate")))
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(examQuestionItemDocList.size()), 4, RoundingMode.HALF_UP)
                        .doubleValue();
            }

            CycleWbExamPaperQuestionRate cycleWbExamPaperQuestionRate = CycleWbExamPaperQuestionRate.of(examPaper, question, orgName, orgId, averageRate);
            cycleWbGraspHelper.addRate(cycleWbExamPaperQuestionRate);
        });
        // 转换成document
        List<Document> documentList = CycleWbGraspComposite.toDocument(cycleWbGraspHelper.getGraspCompositeList());
        documentList.forEach(document -> {
            document.put("courseId", holidayTaskCourseId);
        });
        cycleWbExamPaperQuestionRateDocList.addAll(documentList);

        afterCreateCycleWbGraspHelper(cycleWbExamPaperQuestionRateDocList, frontParams);
    }

    @Override
    public CycleWbGraspHelper getCycleWbGraspHelper(long orgId, CycleWbGraspHelperDTO cycleWbGraspHelperDTO, Map<String, Object> frontParams) {
        Bson query = and(eq("holidayTaskId", orgId));
        List<Document> holidayTaskGraspCompositeDocList = holidayTaskGraspCompositeManager.getList(query);
        List<CycleWbGraspComposite> holidayTaskGraspCompositeList = CycleWbGraspComposite.fromDocument(holidayTaskGraspCompositeDocList);
        CycleWbGraspHelper cycleWbGraspHelper = new CycleWbGraspHelper();
        cycleWbGraspHelper.addGraspComposite(holidayTaskGraspCompositeList);
        cycleWbGraspHelperDTO.setClassCycleWbGraspHelper(cycleWbGraspHelper);
        return cycleWbGraspHelper;
    }

    @Override
    protected List<Map<String, Object>> getCycleWbGraspHelperScoreRate(String orgName, Long orgId, List<CycleWbExamPaperQuestion> cycleWbExamPaperQuestionList, Map<String, Object> holidayTaskInfo) {

        List<Map<String, Object>> examQuestionDocList = Lists.newArrayList();
        Document holidayTaskConfig = holidayTaskConfigService.getHolidayTaskConfig(orgId);
        int holidayTaskRankConfig = MapUtil.getInt(holidayTaskConfig, "holidayTaskRankConfig");
        boolean classRank = DictUtil.isEquals(holidayTaskRankConfig, "cycleWrongBookRankConfig", "class");
        // 获取分层学生信息
        Bson levelQuery = and(eq("holidayTaskId", orgId), eq("selected", true), eq(classRank? "classLevel": "gradeLevel", orgName));
        List<Document> holidayTaskStudentLevelList = holidayTaskStudentLevelManager.getList(levelQuery);
        List<Long> studentIdList = holidayTaskStudentLevelList.stream()
                .map(item -> MapUtil.getLong(item, "studentId"))
                .distinct()
                .collect(Collectors.toList());

        // 获取考试+学生的试题得分率
        List<Bson> subQueryList = Lists.newArrayList();
        List<Map<String, Object>> cycleWbExamPaperParamsList = cycleWbExamPaperQuestionList.stream().map(item -> {
            CycleWbExamPaper examPaper = item.getExamPaper();
            Map<String, Object> examPaperDocParam = new HashMap<>();
            examPaperDocParam.put("examId", examPaper.getExamId());
            examPaperDocParam.put("paperId", examPaper.getPaperId());
            return examPaperDocParam;
        }).distinct().collect(Collectors.toList());
        for (Map<String, Object> cycleWbExamPaperParam : cycleWbExamPaperParamsList) {
            long examId = MapUtil.getLong(cycleWbExamPaperParam, "examId");
            long paperId = MapUtil.getLong(cycleWbExamPaperParam, "paperId");
            Bson subQuery = and(eq("examId", examId), eq("paperId", paperId), eq("statId", 0L), in("studentId", studentIdList));
            subQueryList.add(subQuery);
        }

        Bson query = or(subQueryList);
        List<Document> examStudentQuestionDocList = wrongBookQuestionStatService.queryExamStudentQuestionStatList(query);

        examStudentQuestionDocList.forEach(item -> item.put("averageRate", MapUtil.getString(item, "scoreRate")));
        List<Map<String, Object>> examStudentQuestionList = mergeSubQuestion(examStudentQuestionDocList);

        examStudentQuestionList.forEach(examStudentQuestion -> {
            Map<String, Object> examQuestionDoc = new HashMap<>(examStudentQuestion);
            examQuestionDoc.put("classId", orgId);
            examQuestionDoc.put("className", orgName);
            examQuestionDocList.add(examQuestionDoc);
        });

        if (CollectionUtils.isEmpty(examQuestionDocList)) {
            log.info("分层[名称：{}，id：{}]试题得分率为空", orgName, orgId);
        }

        return examQuestionDocList;
    }

    @Override
    protected void afterCreateCycleWbGraspHelper(List<Document> holidayTaskGraspCompositeDocList, Map<String, Object> frontParams) {
        long holidayTaskId = MapUtil.getLong(frontParams, "holidayTaskId");
        String holidayTaskLevel = MapUtil.getString(frontParams, "holidayTaskLevel");
        Map<String, Object> userInfo = MapUtil.copyUserInfo(frontParams);
        LocalDateTime now = LocalDateTime.now();

        List<ObjectId> knowledgeIdObjSet = holidayTaskGraspCompositeDocList.stream()
                .map(item -> new ObjectId(MapUtil.getString(item, "knowledgeId")))
                .distinct()
                .collect(Collectors.toList());
        List<Document> knowledgeDocList = knowledgeManager.get(knowledgeIdObjSet);
        Map<String, Document> knowledgeDocMap = Optional.ofNullable(knowledgeDocList).orElse(Lists.newArrayList()).stream()
                .collect(toMap(item -> MapUtil.getString(item, "_id"), item -> item));


        // 存储数据
        holidayTaskGraspCompositeDocList.forEach(item -> {
            String knowledgeId = MapUtil.getString(item, "knowledgeId");
            Document knowledgeDoc = knowledgeDocMap.get(knowledgeId);
            if (knowledgeDoc == null) {
                return;
            }
            String knowledgeName = MapUtil.getString(knowledgeDoc, "knowledgeName");
            item.put("knowledgeName", knowledgeName);
            item.put("holidayTaskLevel", holidayTaskLevel);
            item.put("holidayTaskId", holidayTaskId);
            item.put("createDateTime", now);
            item.putAll(userInfo);
        });
        Bson deleteParams = and(eq("holidayTaskId", holidayTaskId), eq("holidayTaskLevel", holidayTaskLevel));
        holidayTaskGraspCompositeManager.deleteMany(deleteParams);
        holidayTaskGraspCompositeManager.insertMany(holidayTaskGraspCompositeDocList);
    }

    @Override
    protected void buildOrgParticipateExamIdList(long orgId, Map<String, Object> frontParams) {

    }
}
