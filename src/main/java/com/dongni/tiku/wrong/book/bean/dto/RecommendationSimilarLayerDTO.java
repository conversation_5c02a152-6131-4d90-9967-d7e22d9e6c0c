package com.dongni.tiku.wrong.book.bean.dto;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className RecommendationSimilarLayerDTO
 * @since 2023/8/3 15:44
 */
public class RecommendationSimilarLayerDTO {

    /**
     * 主键
     */
    private Long recommendationSimilarLayerId;

    /**
     * 分层推题类型
     */
    private Integer similarLayerType;

    /**
     * 推题类型
     */
    private Integer wrongQuestionType;

    /**
     * 简单题数量
     */
    private Integer easyCount;

    /**
     * 中等题数量
     */
    private Integer middleCount;

    /**
     * 困难题数量
     */
    private Integer difficultyCount;

    public Long getRecommendationSimilarLayerId() {
        return recommendationSimilarLayerId;
    }

    public void setRecommendationSimilarLayerId(Long recommendationSimilarLayerId) {
        this.recommendationSimilarLayerId = recommendationSimilarLayerId;
    }

    public Integer getSimilarLayerType() {
        return similarLayerType;
    }

    public void setSimilarLayerType(Integer similarLayerType) {
        this.similarLayerType = similarLayerType;
    }

    public Integer getWrongQuestionType() {
        return wrongQuestionType;
    }

    public void setWrongQuestionType(Integer wrongQuestionType) {
        this.wrongQuestionType = wrongQuestionType;
    }

    public Integer getEasyCount() {
        return easyCount;
    }

    public void setEasyCount(Integer easyCount) {
        this.easyCount = easyCount;
    }

    public Integer getMiddleCount() {
        return middleCount;
    }

    public void setMiddleCount(Integer middleCount) {
        this.middleCount = middleCount;
    }

    public Integer getDifficultyCount() {
        return difficultyCount;
    }

    public void setDifficultyCount(Integer difficultyCount) {
        this.difficultyCount = difficultyCount;
    }

    @Override
    public String toString() {
        return "RecommendationSimilarLayerDTO{" +
                "recommendationSimilarLayerId=" + recommendationSimilarLayerId +
                ", similarLayerType=" + similarLayerType +
                ", wrongQuestionType=" + wrongQuestionType +
                ", easyCount=" + easyCount +
                ", middleCount=" + middleCount +
                ", difficultyCount=" + difficultyCount +
                '}';
    }
}
