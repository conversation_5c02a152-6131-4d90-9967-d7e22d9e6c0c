package com.dongni.tiku.wrong.book.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className WrongBookStudentRosterDTO
 * @since 2024/10/24 11:46
 */
@Getter
@Setter
public class WrongBookStudentRosterDTO {


    /**
     * 错题本学生管理ID
     */
    private Long wrongBookStudentRosterId;


    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 年级ID
     */
    private Long gradeId;

    /**
     * 年级名称
     */
    private String gradeName;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 学生名称
     */
    private String studentName;

    /**
     * 学号
     */
    private String studentNum;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 导入状态
     */
    private Integer wrongBookStudentRosterStatus;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createDateTime;

    /**
     * 修改人ID
     */
    private Long modifierId;

    /**
     * 修改人
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyDateTime;

}
