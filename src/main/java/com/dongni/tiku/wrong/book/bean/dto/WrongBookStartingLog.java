package com.dongni.tiku.wrong.book.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className WrongBookStartingLog
 * @since 2024/4/7 15:00
 */
@Getter
@Setter
public class WrongBookStartingLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 个册任务主键ID
     */
    private Long wrongBookId;

    /**
     * 个册子任务主键ID
     */
    private Long wrongBookPaperId;

    /**
     * 个册试卷任务类型
     */
    private Integer wrongBookPaperTaskType;

    /**
     * 个册试卷任务开始时间
     */
    private LocalDateTime wrongBookPaperTaskStartDatetime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createDateTime;

    /**
     * 修改人ID
     */
    private Long modifierId;

    /**
     * 修改人
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyDateTime;
}
