package com.dongni.tiku.wrong.book.listener;

import com.dongni.tiku.wrong.book.bean.param.WrongBookLogInitParam;
import com.dongni.tiku.wrong.book.event.WrongBookPaperTaskCreateEvent;
import com.dongni.tiku.wrong.book.service.IWrongBookLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className WrongBookLogInitListener
 * @since 2024/4/9 16:01
 */
@Component
public class WrongBookLogInitListener {

    private final Logger log = LoggerFactory.getLogger(WrongBookLogInitListener.class);

    @Autowired
    private IWrongBookLogService wrongBookLogService;

    @EventListener
    public void handleWrongBookInitEvent(WrongBookPaperTaskCreateEvent event) {
        log.info("初始化个册监控数据 - {}", event);
        List<WrongBookLogInitParam> wrongBookLogInitParamList = WrongBookLogInitParam.convertToBean(event.getWrongBookPaperMessageList());
        wrongBookLogService.init(wrongBookLogInitParamList);
    }
}
