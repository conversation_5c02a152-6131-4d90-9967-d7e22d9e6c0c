package com.dongni.tiku.wrong.book.utils;

import com.dongni.common.utils.DictUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.question.QuestionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/2/20 下午 04:45
 * @Version 1.0.0
 */
public class WrongBookKnowledgeUtil {
    /**
     * 提取试题的知识点, 放入questionInfo.knowledgeTotalList中
     * 格式为[{knowledgeId: "xxx", knowledgeName: "xxx"}]
     * 参数
     * - belongType 试题来源的平台
     * - questionInfo 试题的信息
     *
     * @param questionList 提取知识点列表后
     */
    public static void summariseKnowledge(List<Map<String, Object>> questionList) {
        int yiqiType = DictUtil.getDictValue("questionBankBelongType", "yiqi");
        int dongni = DictUtil.getDictValue("questionBankBelongType", "dongni");
        int wusanType = DictUtil.getDictValue("questionBankBelongType", "wusan");
        int jyeooType = DictUtil.getDictValue("questionBankBelongType", "jyeoo");
        int xkwType = DictUtil.getDictValue("questionBankBelongType", "xuekewang");

        for (Map<String, Object> question : questionList) {
            Integer belongType = MapUtils.getInteger(question, "belongType");
            Map<String, Object> questionInfo = MapUtil.getCast(question, "questionInfo");

            List<Map<String, Object>> knowledgeTotalList = new ArrayList<>();
            if (belongType == yiqiType || belongType == dongni || belongType == wusanType || belongType == xkwType) {
                knowledgeTotalList.addAll(QuestionUtil.getKnowledgeList(questionInfo));
            } else if (belongType == jyeooType) {
                knowledgeTotalList.addAll(extractJyeooKnowledgeList(questionInfo));
            }

            questionInfo.put("knowledgeTotalList", knowledgeTotalList);
        }
    }

    /**
     * 菁优网试题提取知识点
     */
    private static List<Map<String, Object>> extractJyeooKnowledgeList(Map<String, Object> questionInfo) {
        List<Map<String, Object>> result = new ArrayList<>();

        List<Map<String, Object>> questions = MapUtil.getCast(questionInfo, "questions");
        for (Map<String, Object> question : questions) {
            innerExtractJyeooKnowledgeList(result, question);
        }
        return result.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 递归方法获取菁优网 小题的知识点信息
     */
    private static void innerExtractJyeooKnowledgeList(List<Map<String, Object>> result, Map<String, Object> question) {
        if (MapUtils.isEmpty(question)) {
            return;
        }

        List<Map<String, Object>> knowledgeList = MapUtil.getCast(question, "knowledgeList");
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            List<Map<String, Object>> collect = knowledgeList.stream().map(i -> MapUtil.of("knowledgeId", MapUtil.getTrim(i, "_id"),
                    "knowledgeName", (Object) MapUtil.getTrim(i, "name"))).collect(Collectors.toList());
            result.addAll(collect);
        }

        List<Map<String, Object>> innerQuestions = MapUtil.getCast(question, "questions");
        if (CollectionUtils.isNotEmpty(innerQuestions)) {
            for (Map<String, Object> innerQuestion : innerQuestions) {
                innerExtractJyeooKnowledgeList(result, innerQuestion);
            }
        }
    }
}
