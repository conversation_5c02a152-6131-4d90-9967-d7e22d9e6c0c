package com.dongni.tiku.wrong.book.service;


import com.dongni.tiku.wrong.book.bean.dto.ConvertWrongBookSelectionSimilarQuestionLevelDTO;
import com.dongni.tiku.wrong.book.bean.entity.WrongBookSelectionSimilarLevel;
import com.dongni.tiku.wrong.book.bean.param.WrongBookSelectionSimilarQuestionLevelParam;
import com.dongni.tiku.wrong.book.bean.param.WrongBookSelectionSimilarQuestionLogicParam;
import com.dongni.tiku.wrong.book.bean.vo.WrongBookSelectionSimilarLevelVO;

import java.util.List;

/**
 * <p>
 * 选题-类题层次 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
public interface IWrongBookSelectionSimilarLevelService {

    /**
     * 批量新增对应子任务的类题分层
     * @param wrongBookSelectionSimilarLevelList 选题-类题分层数据
     */
    void batchInsertWrongBookSelectionSimilarLevel(List<WrongBookSelectionSimilarLevel> wrongBookSelectionSimilarLevelList);

    /**
     * 删除对应子任务的类题分层
     * @param wrongBookId  个册任务ID
     * @param wrongBookPaperId  个册子任务ID
     */
    void deleteWrongBookSelectionSimilarLevel(long wrongBookId, long wrongBookPaperId);

    /**
     * 删除对应子任务的类题分层
     * @param wrongBookId  个册任务ID
     * @param wrongBookPaperId  个册子任务ID
     * @param questionId  原题ID
     */
    void deleteWrongBookSelectionSimilarLevelByQid(long wrongBookId, long wrongBookPaperId, String questionId);

    /**
     * 删除对应子任务的类题分层
     * @param wrongBookId  个册任务ID
     * @param wrongBookPaperId  个册子任务ID
     * @param similarQuestionId  类题ID
     */
    void deleteWrongBookSelectionSimilarLevel(long wrongBookId, long wrongBookPaperId, String similarQuestionId);

    /**
     * 删除对应子任务的类题分层
     * @param wrongBookId  个册任务ID
     * @param wrongBookPaperId  个册子任务ID
     * @param similarQuestionIdList  类题ID列表
     */
    void deleteWrongBookSelectionSimilarLevel(long wrongBookId, long wrongBookPaperId, List<String> similarQuestionIdList);

    /**
     * 删除对应子任务的类题分层
     * @param wrongBookId  个册任务ID
     * @param wrongBookPaperId  个册子任务ID
     * @param questionId  原题ID
     * @param similarQuestionId  类题ID
     */
    void deleteWrongBookSelectionSimilarLevel(long wrongBookId, long wrongBookPaperId, String questionId, String similarQuestionId);

    /**
     * 获取对应子任务的类题分层
     * @param wrongBookSelectionSimilarQuestionLogicParam wrongBookId wrongBookPaperId [similarQuestionId]
     * @return
     */
    List<WrongBookSelectionSimilarLevelVO> getWrongBookSelectionSimilarQuestionLevel(WrongBookSelectionSimilarQuestionLogicParam wrongBookSelectionSimilarQuestionLogicParam);

    /**
     * 获取有分层的类题列表
     * @param wrongBookSelectionSimilarQuestionLogicParam wrongBookId wrongBookPaperId [similarQuestionId]
     * @return
     */
    List<WrongBookSelectionSimilarLevelVO> getHasLevelWrongBookSelectionSimilarQuestionList(WrongBookSelectionSimilarQuestionLogicParam wrongBookSelectionSimilarQuestionLogicParam);

    /**
     * 更新选题-类题分层
     * @param wrongBookSimilarQuestionLevelParam
     */
    void updateWrongBookSelectionSimilarQuestionLevel(WrongBookSelectionSimilarQuestionLevelParam wrongBookSimilarQuestionLevelParam);

    /**
     * 转换分层(5层转3层或3层转5层)
     * @param wrongBookSelectionSimilarLevelList    类题分层源数据
     * @param wrongBookSimilarQuestionLevelQuantity 需要转化的层次
     */
    List<WrongBookSelectionSimilarLevel> convertWrongBookSelectionSimilarQuestionLevel(List<WrongBookSelectionSimilarLevel> wrongBookSelectionSimilarLevelList, int wrongBookSimilarQuestionLevelQuantity);
}
