package com.dongni.tiku.wrong.book.bean.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 错题本选题原题表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
public class WrongBookSelectionQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long wrongBookSelectionQuestionId;

    /**
     * 错题本任务主键
     */
    private Long wrongBookId;

    /**
     * 错题本子任务主键
     */
    private Long wrongBookPaperId;

    /**
     * 试题ID
     */
    private String questionId;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createDateTime;

    /**
     * 修改人id
     */
    private Long modifierId;

    /**
     * 修改人名称
     */
    private String modifierName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyDateTime;


    public Long getWrongBookSelectionQuestionId() {
        return wrongBookSelectionQuestionId;
    }

    public void setWrongBookSelectionQuestionId(Long wrongBookSelectionQuestionId) {
        this.wrongBookSelectionQuestionId = wrongBookSelectionQuestionId;
    }

    public Long getWrongBookId() {
        return wrongBookId;
    }

    public void setWrongBookId(Long wrongBookId) {
        this.wrongBookId = wrongBookId;
    }

    public Long getWrongBookPaperId() {
        return wrongBookPaperId;
    }

    public void setWrongBookPaperId(Long wrongBookPaperId) {
        this.wrongBookPaperId = wrongBookPaperId;
    }

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public LocalDateTime getCreateDateTime() {
        return createDateTime;
    }

    public void setCreateDateTime(LocalDateTime createDateTime) {
        this.createDateTime = createDateTime;
    }

    public Long getModifierId() {
        return modifierId;
    }

    public void setModifierId(Long modifierId) {
        this.modifierId = modifierId;
    }

    public String getModifierName() {
        return modifierName;
    }

    public void setModifierName(String modifierName) {
        this.modifierName = modifierName;
    }

    public LocalDateTime getModifyDateTime() {
        return modifyDateTime;
    }

    public void setModifyDateTime(LocalDateTime modifyDateTime) {
        this.modifyDateTime = modifyDateTime;
    }

    public static final String WRONG_BOOK_SELECTION_QUESTION_ID = "wrong_book_selection_question_id";

    public static final String WRONG_BOOK_ID = "wrong_book_id";

    public static final String WRONG_BOOK_PAPER_ID = "wrong_book_paper_id";

    public static final String QUESTION_ID = "question_id";

    public static final String CREATOR_ID = "creator_id";

    public static final String CREATOR_NAME = "creator_name";

    public static final String CREATE_DATE_TIME = "create_date_time";

    public static final String MODIFIER_ID = "modifier_id";

    public static final String MODIFIER_NAME = "modifier_name";

    public static final String MODIFY_DATE_TIME = "modify_date_time";

    @Override
    public String toString() {
        return "WrongBookSelectionQuestion{" +
        "wrongBookSelectionQuestionId=" + wrongBookSelectionQuestionId +
        ", wrongBookId=" + wrongBookId +
        ", wrongBookPaperId=" + wrongBookPaperId +
        ", questionId=" + questionId +
        ", creatorId=" + creatorId +
        ", creatorName=" + creatorName +
        ", createDateTime=" + createDateTime +
        ", modifierId=" + modifierId +
        ", modifierName=" + modifierName +
        ", modifyDateTime=" + modifyDateTime +
        "}";
    }
}
