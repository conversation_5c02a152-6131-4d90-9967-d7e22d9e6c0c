package com.dongni.tiku.wrong.book.service;

import com.dongni.basedata.export.todo.service.todo.TodoWrongBookPaperAssignmentService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.common.service.TikuCommonService;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.wrong.book.adapter.IWrongBookRecommendedQuantityAdapter;
import com.dongni.tiku.wrong.book.bean.dto.*;
import com.dongni.tiku.wrong.book.bean.param.WrongBookListParam;
import com.dongni.tiku.wrong.book.bean.param.WrongBookSelectionRecycleQuestionQuantityParam;
import com.dongni.tiku.wrong.book.bean.param.WrongBookSelectionSimilarQuestionQuantityParam;
import com.dongni.tiku.wrong.book.bean.vo.WrongBookSelectionQuestionRecommendedQuantity;
import com.dongni.tiku.wrong.book.bean.vo.WrongBookSelectionRecycleQuestionQuantity;
import com.dongni.tiku.wrong.book.bean.vo.WrongBookSelectionSimilarQuestionQuantity;
import com.dongni.tiku.wrong.book.bean.vo.WrongBookVO;
import com.dongni.tiku.wrong.book.utils.WrongBookUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static com.dongni.common.utils.StreamUtil.distinctByKey;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @description 错题本-试题维度
 * @date 2021年08月06日
 */
@Service
public class WrongBookQuestionService {

    private final static Logger log = LoggerFactory.getLogger(WrongBookQuestionService.class);

    @Autowired
    private TikuRepository commonRepository;

    @Autowired
    private TikuCommonService tikuCommonService;

    @Autowired
    private WrongBookService wrongBookService;

    @Autowired
    private WrongBookPaperService wrongBookPaperService;

    @Autowired
    private RecommendationSimilarityService recommendationSimilarityService;

    @Autowired
    private WrongBookSelectionQuestionService wrongBookSelectionQuestionService;

    @Autowired
    private WrongBookSelectionRecycleQuestionService wrongBookSelectionRecycleQuestionService;

    @Autowired
    private RecommendationOriginalService recommendationOriginalService;

    @Autowired
    private WrongBookPaperConfigService wrongBookPaperConfigService;

    @Autowired
    private IWrongBookRecommendedQuantityAdapter wrongBookRecommendedQuantityAdapter;

    @Autowired
    private IWrongBookDingDingRobotMessageService wrongBookDingDingRobotMessageService;

    /**
     * 批量保存错题本-试卷维度
     * @param params
     */
    public void batchInsertWrongBookQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isNotEmptyCollections("list")
                .verify();

        commonRepository.batchInsert("WrongBookQuestionMapper.batchInsertWrongBookQuestion", params);
    }



    /**
     * 根据wrongBookId查询试题
     * @param params wrongBookId [paperId] [courseId]
     * @return
     */
    public List<Map<String, Object>> getQuestionByWrongBookId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("wrongBookId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getQuestionByWrongBookId", params);
    }

    /**
     * 根据wrongBookId wrongBookPaperId查询试题
     * @param params wrongBookId
     * @return
     */
    public List<Map<String, Object>> getQuestionByWrongBookPaperId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .isValidId("wrongBookPaperId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getQuestionByWrongBookId", params);
    }

    /**
     * 根据wrongBookPaperId查询试题 关联原题库
     * @param params wrongBookPaperId
     * @return
     */
    @Deprecated
    public List<Map<String, Object>> getQuestionInfoByWrongBookPaperId(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("wrongBookPaperId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getQuestionInfoByWrongBookPaperId", params);
    }

    /**
     * 存储学生原题与推题结果
     * @param wrongBookStudentQuestionList 学生原题与推题结果
     */
    public void batchInsertWrongBookStudentQuestion(List<Map<String, Object>> wrongBookStudentQuestionList) {
        if (CollectionUtils.isNotEmpty(wrongBookStudentQuestionList)) {
            commonRepository.batchInsert("WrongBookQuestionMapper.batchInsertWrongBookStudentQuestion", wrongBookStudentQuestionList);
        }
    }

    /**
     * 删除学生原题与推题结果
     * @param params wrongBookId
     */
    public void deleteWrongBookStudentQuestion(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();
        commonRepository.delete("WrongBookQuestionMapper.deleteWrongBookStudentQuestion", params);
    }

    /**
     * 获取学生错题映射关系 - 类题+班级共性错题
     * @param params wrongBookId  [wrongBookPaperId] [classId] [questionId] [questionIdList]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentQuestionList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();
        return commonRepository.selectList("WrongBookQuestionMapper.getWrongBookStudentQuestionList", params);
    }

    /**
     * 获取学生错题映射关系 - 班级共性错题的必练题
     * @param params wrongBookId  [wrongBookPaperId] [classId] [questionId] [questionIdList]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentSimilarEssentialQuestionList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();
        List<Map<String, Object>> result = commonRepository.selectList(
                "WrongBookQuestionMapper.getWrongBookStudentSimilarEssentialQuestionList", params);

        // 标记这些必练题是来自班级共性错题的
        result.forEach(i -> i.put("fromClassConsolidation", true));
        return result;
    }

    /**
     * 获取学生错题映射关系包含试题信息 - 类题+班级共性错题
     * @param params wrongBookId  [wrongBookPaperId]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentQuestionInfoList(Map<String, Object> params) {
        List<Map<String, Object>> wrongBookStudentQuestionList = getWrongBookStudentQuestionList(params);

        List<Map<String, Object>> wrongBookStudentQuestionList2BelongType = wrongBookStudentQuestionList.stream()
                .filter(item -> ObjectUtil.isNotBlank(item.get("belongType")))
                .collect(Collectors.toList());

        List<Map<String, Object>> wrongBookStudentQuestionListNotBelongType = wrongBookStudentQuestionList.stream()
                .filter(item -> ObjectUtil.isBlank(item.get("belongType")))
                .collect(Collectors.toList());

        recommendationSimilarityService.buildSimilarInfo(wrongBookStudentQuestionList2BelongType);

        List<Map<String, Object>> resultList = Lists.newArrayList();
        resultList.addAll(wrongBookStudentQuestionList2BelongType);
        resultList.addAll(wrongBookStudentQuestionListNotBelongType);
        return resultList;
    }

    /**
     * 获取学生错题映射关系包含试题信息 - 班级共性错题的必练题
     * @param params wrongBookId  [wrongBookPaperId]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentSimilarEssentialQuestionInfoList(Map<String, Object> params) {
        List<Map<String, Object>> wrongBookStudentQuestionList = getWrongBookStudentSimilarEssentialQuestionList(params);

        List<Map<String, Object>> wrongBookStudentQuestionList2BelongType = wrongBookStudentQuestionList.stream()
                .filter(item -> ObjectUtil.isNotBlank(item.get("belongType")))
                .collect(Collectors.toList());

        List<Map<String, Object>> wrongBookStudentQuestionListNotBelongType = wrongBookStudentQuestionList.stream()
                .filter(item -> ObjectUtil.isBlank(item.get("belongType")))
                .collect(Collectors.toList());

        recommendationSimilarityService.buildSimilarInfo(wrongBookStudentQuestionList2BelongType);

        List<Map<String, Object>> resultList = Lists.newArrayList();
        resultList.addAll(wrongBookStudentQuestionList2BelongType);
        resultList.addAll(wrongBookStudentQuestionListNotBelongType);
        return resultList;
    }


    /**
     * 获取学生错题映射关系包含试题信息 - 拔高题
     *
     * @param params wrongBookId  [wrongBookPaperId] [classId] [questionId] [questionIdList] [distinct]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentQuestionInfoOnlyEnhanced(Map<String, Object> params) {
        List<Map<String, Object>> wrongBookStudentQuestionOnlyEnhanced = getWrongBookStudentQuestionOnlyEnhanced(params);

        // 根据similarQuestionId+belongType去重 - 教师讲义获取全部拔高题使用
        boolean distinct = MapUtil.getBoolean(params, "distinct");
        if (distinct) {
            wrongBookStudentQuestionOnlyEnhanced = new ArrayList<>(
                    wrongBookStudentQuestionOnlyEnhanced.stream()
                            .collect(toMap(i -> MapUtil.getString(i, "similarQuestionId") + MapUtil.getInt(i, "belongType"), i -> i, (i1, i2) -> i1))
                            .values()
            );
        }

        recommendationSimilarityService.buildSimilarInfo(wrongBookStudentQuestionOnlyEnhanced);
        return wrongBookStudentQuestionOnlyEnhanced;
    }

    /**
     * 获取学生错题关系 - 拔高题
     *
     * @param params wrongBookId  [wrongBookPaperId] [classId] [questionId] [questionIdList]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentQuestionOnlyEnhanced(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getWrongBookStudentQuestionOnlyEnhanced", params);
    }

    /**
     * 获取被选择的错题
     * @param params wrongBookId
     * @return questionId
     */
    @Deprecated
    public List<Map<String, Object>> getNotChooseWrongBookQuestionIdList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getNotChooseWrongBookQuestionIdList", params);
    }

    /**
     * 根据原题ID获取类题
     * @param params questionIdList wrongBookId [wrongBookPaperId]
     * @return
     */
    public List<Map<String, Object>> getSimilarList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .isNotEmptyCollections("questionIdList")
                .verify();

        List<Map<String, Object>> similarQuestionResultList = Lists.newArrayList();

        List<Map<String, Object>> similarQuestionList = recommendationSimilarityService.getSimilarListByQuestionIds(params);
        Map<String, List<Map<String, Object>>> similarQuestionMap = similarQuestionList.stream()
                .collect(groupingBy(item -> MapUtil.getString(item, "questionId")));
        // 获取选择后的类题
        List<Map<String, Object>> chooseSimilarQuestionList = geOptionalWrongBookQuestionList(params);
        if (CollectionUtils.isNotEmpty(chooseSimilarQuestionList)) {
            Map<String, List<Map<String, Object>>> chooseSimilarQuestionMap = chooseSimilarQuestionList.stream()
                    .collect(groupingBy(item -> MapUtil.getString(item, "questionId")));

            similarQuestionMap.forEach((questionId, similarQuestionItemList) -> {
                List<Map<String, Object>> chooseSimilarQuestionItemList = chooseSimilarQuestionMap.get(questionId);
                if (CollectionUtils.isNotEmpty(chooseSimilarQuestionItemList)) {
                    Map<String, Map<String, Object>> similarQuestionItemMap = similarQuestionItemList.stream()
                            .collect(Collectors.toMap(item -> MapUtil.getString(item, "similarQuestionId"), item -> item));

                    chooseSimilarQuestionItemList.forEach(chooseSimilarQuestionItem -> {
                        String similarQuestionId = MapUtil.getString(chooseSimilarQuestionItem, "similarQuestionId");
                        Map<String, Object> similarQuestionItem = similarQuestionItemMap.get(similarQuestionId);
                        if (MapUtils.isNotEmpty(similarQuestionItem)) {
                            similarQuestionResultList.add(similarQuestionItem);
                        }
                    });

                    return;
                }
                similarQuestionResultList.addAll(similarQuestionItemList);
            });
            return similarQuestionResultList;
        } else {
            return similarQuestionList;
        }
    }

    /**
     * 获取类题结果
     * @param params wrongBookId  [wrongBookPaperId] [questionIdList]
     * @return
     */
    public List<Map<String, Object>> geOptionalWrongBookQuestionList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.geOptionalWrongBookQuestionList", params);
    }

    @Deprecated
    public List<Map<String, Object>> getQuestionInfoByWrongBookIddsadas(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getQuestionInfoByWrongBookId", params);
    }


    /**
     * 获取错题本试题
     * @param params [wrongBookId] [wrongBookPaperId] [paperId] [courseId]
     * @return
     */
    public List<Map<String, Object>> getCommonWrongBookQuestion(Map<String, Object> params) {
        return commonRepository.selectList("WrongBookQuestionMapper.getCommonWrongBookQuestion", params);
    }

    /**
     * 获取错题本试题的推题状态 [V3.4]
     * @param params wrongBookPaperId recommendationTaskType [recommendationWrongBookPaperId 学校老师传]
     * @return
     */
    public List<Map<String, Object>> getWrongBookRecommendationQuestionList(Map<String, Object> params) {
        return commonRepository.selectList("WrongBookQuestionMapper.getWrongBookRecommendationQuestionList", params);
    }

    /**
     * 获取个性化手册试题的推题类题
     * @param params recommendationTaskType [questionId] [questionIdList] [wrongBookPaperId 学校老师传]
     * @return
     */
    public List<Map<String, Object>> getWrongBookRecommendationSimilarQuestionList(Map<String, Object> params) {
        Verify.of(params)
                .isInteger("recommendationTaskType")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getWrongBookRecommendationSimilarQuestionList", params);
    }

    /**
     * 获取个性化手册试题的推题原题
     * @param params recommendationTaskType [questionId] [questionIdList]
     * @return
     */
    public List<Map<String, Object>> getRecommendationQuestionList(Map<String, Object> params) {
        Verify.of(params)
                .isInteger("recommendationTaskType")
                .verify();

        return recommendationOriginalService.getListByQuestionIds(params);
    }

    /**
     * 获取原题的推题数量
     * @param params wrongBookId wrongBookPaperId questionId
     * @return
     */
    public WrongBookSelectionQuestionRecommendedQuantity getWrongBookQuestionRecommendNumber(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .isValidId("wrongBookPaperId")
                .isNotBlank("questionId")
                .verify();
        long wrongBookId = MapUtil.getLong(params, "wrongBookId");
        long wrongBookPaperId = MapUtil.getLong(params, "wrongBookPaperId");
        String questionId = MapUtil.getString(params, "questionId");

        WrongBookSelectionQuestionRecommendedQuantity wrongBookSelectionQuestionRecommendedQuantity = new WrongBookSelectionQuestionRecommendedQuantity();

        Map<String, Object> wrongBookPaperRecommendedQuantityDoc = wrongBookPaperConfigService.getWrongBookPaperRecommendedQuantity(wrongBookPaperId);
        if (MapUtils.isEmpty(wrongBookPaperRecommendedQuantityDoc)) {
            WrongBookPaperRecommendedQuantity wrongBookPaperRecommendedQuantity = wrongBookRecommendedQuantityAdapter.adaptWrongBookRecommendedQuantityByWrongBookTask(wrongBookId, wrongBookPaperId);
            wrongBookSelectionQuestionRecommendedQuantity.setConsolidateRecommendedQuantity(wrongBookPaperRecommendedQuantity.getConsolidateQuantity());
            wrongBookSelectionQuestionRecommendedQuantity.setExpandRecommendedQuantity(wrongBookPaperRecommendedQuantity.getExpandQuantity());
        } else {
            wrongBookSelectionQuestionRecommendedQuantity.setConsolidateRecommendedQuantity(MapUtil.getInt(wrongBookPaperRecommendedQuantityDoc, "consolidateQuantity"));
            wrongBookSelectionQuestionRecommendedQuantity.setExpandRecommendedQuantity(MapUtil.getInt(wrongBookPaperRecommendedQuantityDoc, "consolidateQuantity"));
        }

        // 统计选题拓展题和巩固题数量和总数量
        WrongBookSelectionSimilarQuestionQuantityParam wrongBookSelectionSimilarQuestionQuantityParam = new WrongBookSelectionSimilarQuestionQuantityParam();
        wrongBookSelectionSimilarQuestionQuantityParam.setWrongBookId(wrongBookId);
        wrongBookSelectionSimilarQuestionQuantityParam.setWrongBookPaperId(wrongBookPaperId);
        wrongBookSelectionSimilarQuestionQuantityParam.setQuestionId(questionId);

        WrongBookSelectionSimilarQuestionQuantity wrongBookSelectionSimilarQuestionQuantity =
                Optional.ofNullable(wrongBookSelectionQuestionService.countWrongBookSelectionSimilarQuestionByDifficultyType(wrongBookSelectionSimilarQuestionQuantityParam)).orElse(new WrongBookSelectionSimilarQuestionQuantity());

        Integer selectionSimilarNumber = Optional.ofNullable(wrongBookSelectionSimilarQuestionQuantity.getSelectionSimilarNumber()).orElse(0);
        Integer consolidateQuantity = Optional.ofNullable(wrongBookSelectionSimilarQuestionQuantity.getConsolidateQuantity()).orElse(0);
        Integer expandQuantity = Optional.ofNullable(wrongBookSelectionSimilarQuestionQuantity.getExpandQuantity()).orElse(0);
        wrongBookSelectionQuestionRecommendedQuantity.setSelectionSimilarNumber(selectionSimilarNumber);
        wrongBookSelectionQuestionRecommendedQuantity.setConsolidateQuantity(consolidateQuantity);
        wrongBookSelectionQuestionRecommendedQuantity.setExpandQuantity(expandQuantity);

        // 获取试题选题回收站的数量
        WrongBookSelectionRecycleQuestionQuantityParam wrongBookSelectionRecycleQuestionQuantityParam = new WrongBookSelectionRecycleQuestionQuantityParam();
        wrongBookSelectionRecycleQuestionQuantityParam.setWrongBookId(wrongBookId);
        wrongBookSelectionRecycleQuestionQuantityParam.setWrongBookPaperId(wrongBookPaperId);
        wrongBookSelectionRecycleQuestionQuantityParam.setQuestionId(questionId);
        WrongBookSelectionRecycleQuestionQuantity wrongBookSelectionRecycleQuestionQuantity =
                Optional.ofNullable(wrongBookSelectionRecycleQuestionService.countWrongBookSelectionRecycleSimilarQuestion(wrongBookSelectionRecycleQuestionQuantityParam)).orElse(new WrongBookSelectionRecycleQuestionQuantity());

        Integer recycleSimilarQuestionNumber = Optional.ofNullable(wrongBookSelectionRecycleQuestionQuantity.getRecycleSimilarQuestionNumber()).orElse(0);
        Integer recycleConsolidateQuantity = Optional.ofNullable(wrongBookSelectionRecycleQuestionQuantity.getRecycleConsolidateQuantity()).orElse(0);
        Integer recycleExpandQuantity = Optional.ofNullable(wrongBookSelectionRecycleQuestionQuantity.getRecycleExpandQuantity()).orElse(0);
        wrongBookSelectionQuestionRecommendedQuantity.setRecycleSimilarQuestionNumber(recycleSimilarQuestionNumber);
        wrongBookSelectionQuestionRecommendedQuantity.setRecycleConsolidateQuantity(recycleConsolidateQuantity);
        wrongBookSelectionQuestionRecommendedQuantity.setRecycleExpandQuantity(recycleExpandQuantity);

        wrongBookSelectionQuestionRecommendedQuantity.setWrongQuestionRecommendNumberMax(50);
        return wrongBookSelectionQuestionRecommendedQuantity;
    }

    public List<Map<String, Object>> filterQuestionList4NeedCreateRecommendationTask(List<Map<String, Object>> wrongBookQuestionList) {
        List<Map<String, Object>> needCreateRecommendationTaskQuestionList = Lists.newArrayList();
        List<String> needCreateRecommendationTaskQuestionIdList = Lists.newArrayList();

        Integer partTimeTeacher = DictUtil.getDictValue("recommendationMode", "partTimeTeacher");
        List<Map<String, Object>> partTimeTeacherSimilarQuestionList = getRecommendationQuestionListByQuestionIdList(wrongBookQuestionList, partTimeTeacher);
        Map<String, List<Map<String, Object>>> partTimeTeacherSimilarQuestionMap = partTimeTeacherSimilarQuestionList
                .stream()
                .collect(groupingBy(
                        item -> MapUtil.getString(item, "questionId")
                ));


        Integer xkw = DictUtil.getDictValue("recommendationMode", "xkw");
        List<Map<String, Object>> xkwSimilarQuestionList = getRecommendationQuestionListByQuestionIdList(wrongBookQuestionList, xkw);
        Map<String, List<Map<String, Object>>> xkwSimilarQuestionMap = xkwSimilarQuestionList
                .stream()
                .collect(groupingBy(
                        item -> MapUtil.getString(item, "questionId")
                ));

        for (Map<String, Object> wrongBookQuestion : wrongBookQuestionList) {
            String questionId = MapUtil.getString(wrongBookQuestion, "questionId");
            int recommendationMode = MapUtil.getInt(wrongBookQuestion, "recommendationMode");
            boolean xkwMode = DictUtil.isEquals(recommendationMode, "recommendationMode", "xkw");
            boolean partTimeTeacherMode = DictUtil.isEquals(recommendationMode, "recommendationMode","partTimeTeacher");
            boolean schoolTeacherMode = DictUtil.isEquals(recommendationMode, "recommendationMode","schoolTeacher");
            boolean notRecommend = DictUtil.isEquals(recommendationMode, "recommendationMode","notRecommend");

            if (notRecommend) {
                continue;
            }

            // 多场考试可能存在重复的试题，只添加一次
            if (needCreateRecommendationTaskQuestionIdList.contains(questionId)) {
                continue;
            }
            // 老师推题可以重复，不用去重
            if (!schoolTeacherMode) {
                needCreateRecommendationTaskQuestionIdList.add(questionId);
            }

            if (xkwMode) {
                List<Map<String, Object>> xkwSimilarQuestionItemList = xkwSimilarQuestionMap.get(questionId);
                if (CollectionUtils.isEmpty(xkwSimilarQuestionItemList)) {
                    needCreateRecommendationTaskQuestionList.add(wrongBookQuestion);
                    continue;
                }
            }

            if (partTimeTeacherMode) {
                List<Map<String, Object>> partTimeTeacherSimilarQuestionItemList = partTimeTeacherSimilarQuestionMap.get(questionId);
                if (CollectionUtils.isEmpty(partTimeTeacherSimilarQuestionItemList)) {
                    needCreateRecommendationTaskQuestionList.add(wrongBookQuestion);
                    continue;
                }
            }

            if (schoolTeacherMode) {
                needCreateRecommendationTaskQuestionList.add(wrongBookQuestion);
                continue;
            }
        }

        return needCreateRecommendationTaskQuestionList;
    }

    /**
     * 过滤出指定类型并存在于推荐任务中的试题
     * @param wrongBookQuestionList  试题列表 需包含questionId recommendationMode
     * @param partTimeTeacher  指定任务类型
     * @return
     */
    public List<Map<String, Object>> getRecommendationQuestionListByQuestionIdList(List<Map<String, Object>> wrongBookQuestionList,
                                                                                    Integer partTimeTeacher) {

        if (CollectionUtils.isEmpty(wrongBookQuestionList)) {
            return Lists.newArrayList();
        }

        List<Map<String, Object>> recommendationSimilarQuestionList = Lists.newArrayList();
        List<String> partTimeTeacherModeQuestionIdList = wrongBookQuestionList.stream()
                .filter(wrongBookQuestion -> {
                    int recommendationMode = MapUtil.getInt(wrongBookQuestion, "recommendationMode");
                    return ObjectUtil.isValueEquals(recommendationMode, partTimeTeacher);
                })
                .map(item -> MapUtil.getString(item, "questionId"))
                .distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(partTimeTeacherModeQuestionIdList)) {
            Map<String, Object> recommendationSimilarParams = new HashMap<>(4);
            recommendationSimilarParams.put("questionIdList", partTimeTeacherModeQuestionIdList);
            recommendationSimilarParams.put("recommendationTaskType", WrongBookUtil.convertRecommendationMode2RecommendationTaskType(partTimeTeacher));
            recommendationSimilarQuestionList = getRecommendationQuestionList(recommendationSimilarParams);
        }

        return recommendationSimilarQuestionList;
    }

    /**
     * 获取学生错题映射关系包含试题信息
     * @param params wrongBookId  [wrongBookPaperId]
     * @return
     */
    public List<Map<String, Object>> getWrongBookStudentGroupQuestionInfoList(Map<String, Object> params) {
        List<Map<String, Object>> wrongBookStudentQuestionList = getWrongBookStudentQuestionForTeacherFile(params);

        List<Map<String, Object>> wrongBookStudentQuestionList2BelongType = wrongBookStudentQuestionList.stream()
                .filter(item -> ObjectUtil.isNotBlank(item.get("belongType")))
                .filter(item -> ObjectUtil.isNotBlank(item.get("similarQuestionId")))
                .collect(Collectors.toList());

        recommendationSimilarityService.buildSimilarInfo(wrongBookStudentQuestionList2BelongType);

        List<Map<String, Object>> resultList = Lists.newArrayList();
        resultList.addAll(wrongBookStudentQuestionList2BelongType);
        return resultList;
    }

    /**
     * 获取学生已推类题
     * @param params wrongBookId wrongBookPaperId
     * @return
     */
    private List<Map<String, Object>> getWrongBookStudentQuestionForTeacherFile(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .isValidId("wrongBookPaperId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getWrongBookStudentQuestionForTeacherFile", params);
    }

    /**
     * 获取错题本任务试题信息
     * @param params wrongBookId [courseId]
     * @return
     */
    public List<Map<String, Object>> getWrongBookQuestionInfoByWrongBookId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("wrongBookId")
                .verify();

        return commonRepository.selectList("WrongBookQuestionMapper.getWrongBookQuestionInfoByWrongBookId", params);
    }


    /**
     * 获取个册子任务试题量
     * @param wrongBookPaperIdList
     * @return
     */
    public List<WrongBookQuestionQuantity> countWrongBookQuestionByWrongBookPaperIds(List<Long> wrongBookPaperIdList) {
        return commonRepository.selectList("WrongBookQuestionMapper.countWrongBookQuestionByWrongBookPaperIds", wrongBookPaperIdList);
    }

    /**
     * 委托合并后自动生成生成推荐任务
     * @param autoGenerateRecommendationTaskDTO
     */
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void autoGenerateRecommendationTask(@Validated AutoGenerateRecommendationTaskDTO autoGenerateRecommendationTaskDTO) {
        log.info("自动生成推题任务 - {} ", autoGenerateRecommendationTaskDTO);
        // 1.通过合并的paperId获取对应的个册子任务
        List<WrongBookPaperDTO> wrongBookPaperList = wrongBookPaperService.getWrongBookPaperList(autoGenerateRecommendationTaskDTO.getPaperId());
        if (CollectionUtils.isEmpty(wrongBookPaperList)) {
            return;
        }

        // 2.获取子任务的题量进行判断，保存试卷试题
        List<Long> wrongBookPaperIdList = wrongBookPaperList.stream().map(WrongBookPaperDTO::getWrongBookPaperId).distinct().collect(Collectors.toList());
        List<WrongBookQuestionQuantity> wrongBookQuestionQuantityList = countWrongBookQuestionByWrongBookPaperIds(wrongBookPaperIdList);
        Map<Long, WrongBookQuestionQuantity> wrongBookQuestionQuantityMap = Optional.ofNullable(wrongBookQuestionQuantityList).orElse(Lists.newArrayList()).stream()
                .collect(toMap(WrongBookQuestionQuantity::getWrongBookPaperId, item -> item));

        wrongBookPaperList.forEach(wrongBookPaperDTO -> {
            WrongBookQuestionQuantity wrongBookQuestionQuantity = wrongBookQuestionQuantityMap.get(wrongBookPaperDTO.getWrongBookPaperId());
            if (ObjectUtil.isBlank(wrongBookQuestionQuantity)) {
                wrongBookPaperDTO.setWrongBookQuestionQuantity(0);
            } else {
                wrongBookPaperDTO.setWrongBookQuestionQuantity(wrongBookQuestionQuantity.getWrongBookQuestionQuantity());
            }
        });

        List<WrongBookPaperDTO> wrongBookPaperForNotEntrustList = wrongBookPaperList.stream()
                .filter(item -> ObjectUtil.isValueEquals(0, item.getWrongBookQuestionQuantity()))
                .collect(Collectors.toList());

        List<Map<String, Object>> wrongBookQuestionList = initWrongBookQuestionList(wrongBookPaperForNotEntrustList, autoGenerateRecommendationTaskDTO);
        Map<Long, List<Map<String, Object>>> wrongBookQuestionMap = wrongBookQuestionList.stream()
                .collect(groupingBy(item -> MapUtil.getLong(item, "wrongBookPaperId")));

        // 3.按类型产生任务
        // 3.1 如果题量为0，首次合并-获取试卷试题
        List<WrongBookPaperDTO> wrongBookPaperForCreateRecommendationTaskList = Lists.newArrayList();
        List<WrongBookPaperDTO> wrongBookPaperForPartTimeTeacherTypeAndNotEntrustList = wrongBookPaperForNotEntrustList.stream()
                .filter(item -> DictUtil.isEquals(item.getRecommendationMode(), "recommendationMode", "partTimeTeacher"))
                .filter(distinctByKey(WrongBookPaperDTO::uniqueKey))
                .collect(Collectors.toList());

        List<WrongBookPaperDTO> wrongBookPaperForSchoolTeacherTypeAndNotEntrustList = wrongBookPaperForNotEntrustList.stream()
                .filter(item -> DictUtil.isEquals(item.getRecommendationMode(), "recommendationMode", "schoolTeacher"))
                .filter(distinctByKey(WrongBookPaperDTO::uniqueKey))
                .collect(Collectors.toList());
        wrongBookPaperForCreateRecommendationTaskList.addAll(wrongBookPaperForPartTimeTeacherTypeAndNotEntrustList);
        wrongBookPaperForCreateRecommendationTaskList.addAll(wrongBookPaperForSchoolTeacherTypeAndNotEntrustList);

        List<Map<String, Object>> wrongBookQuestionForCreateRecommendationTaskList = Lists.newArrayList();
        wrongBookPaperForCreateRecommendationTaskList.forEach(wrongBookPaperDTO -> {
            List<Map<String, Object>> wrongBookItemQuestionForCreateRecommendationTaskList = Optional.ofNullable(wrongBookQuestionMap.get(wrongBookPaperDTO.getWrongBookPaperId())).orElse(Lists.newArrayList());
            wrongBookQuestionForCreateRecommendationTaskList.addAll(wrongBookItemQuestionForCreateRecommendationTaskList);
        });

        // 获取个册任务基础信息
        if (CollectionUtils.isNotEmpty(wrongBookQuestionForCreateRecommendationTaskList)) {
            List<Long> wrongBookIdList = wrongBookQuestionForCreateRecommendationTaskList.stream()
                    .map(item -> MapUtil.getLong(item, "wrongBookId"))
                    .distinct()
                    .collect(Collectors.toList());

            Map<Long, List<WrongBookPaperDTO>> wrongBookPaperForCreateRecommendationTaskMap = wrongBookPaperForCreateRecommendationTaskList.stream()
                    .collect(groupingBy(WrongBookPaperDTO::getWrongBookId));

            Map<Long, List<Map<String, Object>>> wrongBookQuestionForCreateRecommendationTaskMap = wrongBookQuestionForCreateRecommendationTaskList.stream()
                    .collect(groupingBy(item -> MapUtil.getLong(item, "wrongBookId")));

            WrongBookListParam wrongBookListParam = new WrongBookListParam();
            wrongBookListParam.setWrongBookIdList(wrongBookIdList);
            List<WrongBookVO> wrongBookList = wrongBookService.getWrongBookList(wrongBookListParam);
            wrongBookList.forEach(wrongBookVO -> {
                List<WrongBookPaperDTO> wrongBookPaperItemForCreateRecommendationTaskList = wrongBookPaperForCreateRecommendationTaskMap.get(wrongBookVO.getWrongBookId());
                List<Map<String, Object>> wrongBookQuestionItemForCreateRecommendationTaskList = wrongBookQuestionForCreateRecommendationTaskMap.get(wrongBookVO.getWrongBookId());

                Map<String, Object> frontParams = new HashMap<>(8);
                frontParams.put("wrongBookId", wrongBookVO.getWrongBookId());
                frontParams.put("schoolId", wrongBookVO.getSchoolId());
                frontParams.put("gradeType", wrongBookVO.getGradeType());
                frontParams.put("stage", wrongBookVO.getStage());
                frontParams.put("userId", wrongBookVO.getCreatorId());
                frontParams.put("userName", wrongBookVO.getCreatorName());

                wrongBookPaperService.generateRecommendationTask(WrongBookPaperDTO.batchCovertToMap(wrongBookPaperItemForCreateRecommendationTaskList), wrongBookQuestionItemForCreateRecommendationTaskList, frontParams);
            });
        }
        // 2.2 如果题量不为0，退回后再次合并-V3.14先不管了，用户创建新的任务，后续版本优化

    }


    /**
     * 未录题的答题卡在录题合并后填充试题信息
     * @param wrongBookPaperDTOList
     */
    private List<Map<String, Object>> initWrongBookQuestionList(List<WrongBookPaperDTO> wrongBookPaperDTOList, AutoGenerateRecommendationTaskDTO autoGenerateRecommendationTaskDTO) {
        if (CollectionUtils.isEmpty(wrongBookPaperDTOList)) {
            return Lists.newArrayList();
        }

        List<Map<String, Object>> wrongBookPaperParamList = WrongBookPaperDTO.batchCovertToMap(wrongBookPaperDTOList);
        String currentTime = DateUtil.getCurrentDateTime();
        // 查询试卷详情
        List<Map<String, Object>> wrongBookQuestionList = wrongBookService.getWrongBookQuestion(wrongBookPaperParamList);
        for (Map<String, Object> wrongBookQuestion : wrongBookQuestionList) {
            wrongBookQuestion.put("userId", autoGenerateRecommendationTaskDTO.getUserId());
            wrongBookQuestion.put("userName", autoGenerateRecommendationTaskDTO.getUserName());
            wrongBookQuestion.put("currentTime", currentTime);
        }

        batchInsertWrongBookQuestion(MapUtil.of("list", wrongBookQuestionList));
        return wrongBookQuestionList;
    }

}
