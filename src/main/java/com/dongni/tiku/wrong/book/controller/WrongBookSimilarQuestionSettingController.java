package com.dongni.tiku.wrong.book.controller;


import com.dongni.common.entity.Response;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.config.TikuConfig;
import com.dongni.tiku.wrong.book.bean.param.WrongBookSimilarQuestionSettingParam;
import com.dongni.tiku.wrong.book.service.IWrongBookSimilarQuestionSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 错题本-类题自定义设置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@RestController
@RequestMapping(TikuConfig.CONTEXT_PATH + "/wrong/book/similar/question/setting")
public class WrongBookSimilarQuestionSettingController {

    @Autowired
    private IWrongBookSimilarQuestionSettingService wrongBookSimilarQuestionSettingService;

    /**
     *  错题本-类题自定义设置列表
     * @param wrongBookSimilarQuestionSettingParam courseId 课程ID必传
     *                                             settingType 自定义类型 和settingTypeList 二选一
     *                                             settingTypeList 自定义类型列表 和settingType 二选一
     * @return
     */
    @PostMapping("/list")
    public Response listWrongBookSimilarQuestionSetting(WrongBookSimilarQuestionSettingParam wrongBookSimilarQuestionSettingParam) {
        return new Response(wrongBookSimilarQuestionSettingService.listWrongBookSimilarQuestionSetting(wrongBookSimilarQuestionSettingParam));
    }


}
