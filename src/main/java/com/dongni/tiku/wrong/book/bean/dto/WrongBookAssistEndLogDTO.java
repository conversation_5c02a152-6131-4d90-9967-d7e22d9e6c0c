package com.dongni.tiku.wrong.book.bean.dto;

import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/4/7 007 下午 05:31
 * @Version 1.0.0
 */
@Getter
@Setter
public class WrongBookAssistEndLogDTO implements Serializable {
    private static final long serialVersionUID = -434768856473047532L;

    private Long paperId;

    /**
     * 委托可以不带subCourseId
     */
    private @Nullable Long subCourseId;

    private Integer wrongBookPaperTaskType;

    private LocalDateTime wrongBookPaperTaskEndDatetime;

    private Long modifierId;

    private String modifierName;

    private LocalDateTime modifyDateTime;
}
