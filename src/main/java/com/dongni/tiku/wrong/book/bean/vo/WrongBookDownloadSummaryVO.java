package com.dongni.tiku.wrong.book.bean.vo;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className WrongBookDownloadSummaryVO
 * @since 2024/4/26 17:59
 */
@Getter
@Setter
public class WrongBookDownloadSummaryVO {

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件总页数
     */
    private Integer filePages;

    /**
     * 答案总页数
     */
    private Integer answerFilePages;

    /**
     * 必练题总页数
     */
    private Integer essentialFilePages;

    /**
     * 文件总数量
     */
    private Integer totalCount;

    /**
     * 异常数量
     */
    private Integer errorCount;

    /**
     * 已完成数量
     */
    private Integer finishCount;

    /**
     * 生成状态
     */
    private Integer generateStatus;

    /**
     * 文件地址
     */
    private String fileUrl;

    /**
     * 学校ID
     */
    private Long schoolId;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 年级ID
     */
    private Long gradeId;

    /**
     * 年级名称
     */
    private String gradeName;

    /**
     * 错题本任务ID
     */
    private Long wrongBookId;

    /**
     * 错题本文件类型（1-个册，2-阶段本，3-教师讲义）
     */
    private Integer wrongBookFileType;

    /**
     * 懂你订单ID
     */
    private String wrongBookOrderId;

    /**
     * 打印状态
     */
    private Integer wrongBookPrintingStatus;

    /**
     * 课程列表
     */
    private List<Course> courseList;

    /**
     * 任务的更新时间
     */
    private Timestamp modifyDateTime;

    @Getter
    @Setter
    public static class Course{
        private Long courseId;
        private String courseName;
    }

    public static List<WrongBookDownloadSummaryVO.Course> convert(List<WrongBookPaperCourseVO> wrongBookPaperCourseList) {
        List<WrongBookDownloadSummaryVO.Course> courseList = Lists.newArrayList();
        wrongBookPaperCourseList.forEach(wrongBookPaperCourseVO -> {
            Course course = new Course();
            course.setCourseId(wrongBookPaperCourseVO.getSubCourseId());
            course.setCourseName(wrongBookPaperCourseVO.getSubCourseName());
            courseList.add(course);
        });
        return courseList;
    }
}
