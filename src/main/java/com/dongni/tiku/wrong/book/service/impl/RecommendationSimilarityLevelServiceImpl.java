package com.dongni.tiku.wrong.book.service.impl;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.bean.TikuRepository;
import com.dongni.tiku.wrong.book.bean.entity.RecommendationSimilarityLevel;
import com.dongni.tiku.wrong.book.bean.entity.WrongBookSelectionSimilarLevel;
import com.dongni.tiku.wrong.book.bean.param.DeleteRecommendationSimilarityLevelParam;
import com.dongni.tiku.wrong.book.bean.param.RecommendationSimilarQuestionLevelParam;
import com.dongni.tiku.wrong.book.bean.param.RecommendationSimilarQuestionLogicParam;
import com.dongni.tiku.wrong.book.bean.param.WrongBookRecommendationSimilarQuestionLevelParam;
import com.dongni.tiku.wrong.book.bean.vo.RecommendationSimilarityLevelVO;
import com.dongni.tiku.wrong.book.enumerate.ConvertWrongBookSelectionSimilarQuestionLevelEnum;
import com.dongni.tiku.wrong.book.service.IRecommendationSimilarityLevelService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 类题分层表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Service
public class RecommendationSimilarityLevelServiceImpl implements IRecommendationSimilarityLevelService {

    @Autowired
    private TikuRepository tikuRepository;

    @Override
    public void batchInsertRecommendationSimilarityLevel(List<RecommendationSimilarityLevel> recommendationSimilarityLevelList) {
        if (CollectionUtils.isNotEmpty(recommendationSimilarityLevelList)) {
            tikuRepository.insert("RecommendationSimilarityLevelMapper.batchInsertRecommendationSimilarityLevel", recommendationSimilarityLevelList);
        }
    }

    @Override
    public void deleteRecommendationSimilarityLevelByRecommendationId(Long recommendationId) {
        DeleteRecommendationSimilarityLevelParam deleteRecommendationSimilarityLevelParam = new DeleteRecommendationSimilarityLevelParam();
        deleteRecommendationSimilarityLevelParam.setRecommendationId(recommendationId);
        deleteRecommendationSimilarityLevel(deleteRecommendationSimilarityLevelParam);
    }

    @Override
    public void deleteRecommendationSimilarityLevelByRidAndQid(Long recommendationId, String questionId) {
        DeleteRecommendationSimilarityLevelParam deleteRecommendationSimilarityLevelParam = new DeleteRecommendationSimilarityLevelParam();
        deleteRecommendationSimilarityLevelParam.setRecommendationId(recommendationId);
        deleteRecommendationSimilarityLevelParam.setQuestionId(questionId);
        deleteRecommendationSimilarityLevel(deleteRecommendationSimilarityLevelParam);
    }

    @Override
    public void deleteRecommendationSimilarityLevel(Long recommendationId, String similarQuestionId) {
        DeleteRecommendationSimilarityLevelParam deleteRecommendationSimilarityLevelParam = new DeleteRecommendationSimilarityLevelParam();
        deleteRecommendationSimilarityLevelParam.setRecommendationId(recommendationId);
        deleteRecommendationSimilarityLevelParam.setSimilarQuestionId(similarQuestionId);
        deleteRecommendationSimilarityLevel(deleteRecommendationSimilarityLevelParam);
    }

    @Override
    public void deleteRecommendationSimilarityLevel(Long recommendationId, List<String> similarQuestionIdList) {
        DeleteRecommendationSimilarityLevelParam deleteRecommendationSimilarityLevelParam = new DeleteRecommendationSimilarityLevelParam();
        deleteRecommendationSimilarityLevelParam.setRecommendationId(recommendationId);
        deleteRecommendationSimilarityLevelParam.setSimilarQuestionIdList(similarQuestionIdList);
        deleteRecommendationSimilarityLevel(deleteRecommendationSimilarityLevelParam);
    }

    @Override
    public void deleteRecommendationSimilarityLevel(Long recommendationId, String questionId, String similarQuestionId) {
        DeleteRecommendationSimilarityLevelParam deleteRecommendationSimilarityLevelParam = new DeleteRecommendationSimilarityLevelParam();
        deleteRecommendationSimilarityLevelParam.setRecommendationId(recommendationId);
        deleteRecommendationSimilarityLevelParam.setQuestionId(questionId);
        deleteRecommendationSimilarityLevelParam.setSimilarQuestionId(similarQuestionId);
        deleteRecommendationSimilarityLevel(deleteRecommendationSimilarityLevelParam);
    }

    private void deleteRecommendationSimilarityLevel(DeleteRecommendationSimilarityLevelParam deleteRecommendationSimilarityLevelParam) {
        tikuRepository.delete("RecommendationSimilarityLevelMapper.deleteRecommendationSimilarityLevel", deleteRecommendationSimilarityLevelParam);
    }

    @Override
    public List<RecommendationSimilarityLevelVO> getHasLevelRecommendationSimilarQuestionList(RecommendationSimilarQuestionLogicParam recommendationSimilarQuestionLogicParam) {
        if (recommendationSimilarQuestionLogicParam.getRecommendationId() == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        return tikuRepository.selectList("RecommendationSimilarityLevelMapper.getHasLevelRecommendationSimilarQuestionList", recommendationSimilarQuestionLogicParam);
    }

    @Override
    @Transactional(value = TikuRepository.TRANSACTION, rollbackFor = Exception.class)
    public void updateSimilarQuestionLevel(RecommendationSimilarQuestionLevelParam recommendationSimilarQuestionLevelParam) {
        DeleteRecommendationSimilarityLevelParam deleteRecommendationSimilarityLevelParam = new DeleteRecommendationSimilarityLevelParam();
        deleteRecommendationSimilarityLevelParam.setRecommendationId(recommendationSimilarQuestionLevelParam.getRecommendationId());
        deleteRecommendationSimilarityLevelParam.setQuestionId(recommendationSimilarQuestionLevelParam.getQuestionId());
        deleteRecommendationSimilarityLevelParam.setSimilarQuestionId(recommendationSimilarQuestionLevelParam.getSimilarQuestionId());
        deleteRecommendationSimilarityLevel(deleteRecommendationSimilarityLevelParam);

        List<String> similarLevelList = recommendationSimilarQuestionLevelParam.getSimilarLevelList();
        if (CollectionUtils.isNotEmpty(similarLevelList)) {
            List<RecommendationSimilarityLevel> recommendationSimilarityLevelList = RecommendationSimilarityLevel.init(recommendationSimilarQuestionLevelParam);
            batchInsertRecommendationSimilarityLevel(recommendationSimilarityLevelList);
        }
    }

    @Override
    public List<RecommendationSimilarityLevelVO> getWrongBookRecommendationSimilarQuestionLevelList(WrongBookRecommendationSimilarQuestionLevelParam wrongBookRecommendationSimilarQuestionLevelParam) {
        return tikuRepository.selectList("RecommendationSimilarityLevelMapper.getWrongBookRecommendationSimilarQuestionLevelList", wrongBookRecommendationSimilarQuestionLevelParam);
    }

    @Override
    public List<RecommendationSimilarityLevel> convertRecommendationSelectionSimilarQuestionLevel(List<RecommendationSimilarityLevel> recommendationSimilarityLevelList, int wrongBookSimilarQuestionLevelQuantity) {
        if (CollectionUtils.isEmpty(recommendationSimilarityLevelList)) {
            return Lists.newArrayList();
        }

        Map<String, Map<String, List<RecommendationSimilarityLevel>>> recommendedSelectionSimilarLevelGroup = recommendationSimilarityLevelList.stream()
                                    .collect(Collectors.groupingBy(RecommendationSimilarityLevel::getQuestionId, Collectors.groupingBy(RecommendationSimilarityLevel::getSimilarQuestionId)));
        List<RecommendationSimilarityLevel> insertRecommendationSelectionSimilarLevelList = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        if (ObjectUtil.isValueEquals(ConvertWrongBookSelectionSimilarQuestionLevelEnum.FIVE_LEVEL_CONVERT_THREE_LEVEL.getTransferNumber(), wrongBookSimilarQuestionLevelQuantity)) {
            // 五转三，只保留ABC层的数据
            recommendedSelectionSimilarLevelGroup.forEach((questionId, similarQuestionLevelMap) -> {
                similarQuestionLevelMap.forEach((similarQuestionId, similarQuestionLevelList) -> {
                    List<String> levelList = similarQuestionLevelList.stream().map(RecommendationSimilarityLevel::getLevel).collect(Collectors.toList());
                    List<RecommendationSimilarityLevel> recommendedSelectionSimilarABCLevelList = similarQuestionLevelList.stream()
                            .filter(item -> ObjectUtil.isValueEquals("A", item.getLevel()) || ObjectUtil.isValueEquals("B", item.getLevel()) || ObjectUtil.isValueEquals("C", item.getLevel()))
                            .collect(Collectors.toList());
                    insertRecommendationSelectionSimilarLevelList.addAll(recommendedSelectionSimilarABCLevelList);
                    // 包含D或者E但不包含C，需要将DE转化为C
                    boolean needCovert = (levelList.contains("D") || levelList.contains("E")) && !levelList.contains("C");
                    if (needCovert) {
                        RecommendationSimilarityLevel recommendedSelectionSimilarLevel = similarQuestionLevelList.get(0);
                        RecommendationSimilarityLevel recommendedSelectionSimilarLevelForC = new RecommendationSimilarityLevel();
                        recommendedSelectionSimilarLevelForC.setRecommendationId(recommendedSelectionSimilarLevel.getRecommendationId());
                        recommendedSelectionSimilarLevelForC.setQuestionId(recommendedSelectionSimilarLevel.getQuestionId());
                        recommendedSelectionSimilarLevelForC.setSimilarQuestionId(recommendedSelectionSimilarLevel.getSimilarQuestionId());
                        recommendedSelectionSimilarLevelForC.setLevel("C");
                        recommendedSelectionSimilarLevelForC.setCreatorId(1L);
                        recommendedSelectionSimilarLevelForC.setCreatorName("转化数据");
                        recommendedSelectionSimilarLevelForC.setCreateDateTime(now);
                        recommendedSelectionSimilarLevelForC.setModifierId(1L);
                        recommendedSelectionSimilarLevelForC.setModifierName("转化数据");
                        recommendedSelectionSimilarLevelForC.setModifyDateTime(now);
                        insertRecommendationSelectionSimilarLevelList.add(recommendedSelectionSimilarLevelForC);
                    }
                });
            });
        } else if (ObjectUtil.isValueEquals(ConvertWrongBookSelectionSimilarQuestionLevelEnum.THREE_LEVEL_CONVERT_FIVE_LEVEL.getTransferNumber(), wrongBookSimilarQuestionLevelQuantity)) {
            // 三转五，含有C层的转CDE层
            recommendedSelectionSimilarLevelGroup.forEach((questionId, similarQuestionLevelMap) -> {
                similarQuestionLevelMap.forEach((similarQuestionId, similarQuestionLevelList) -> {
                    List<String> levelList = similarQuestionLevelList.stream().map(RecommendationSimilarityLevel::getLevel).collect(Collectors.toList());
                    List<RecommendationSimilarityLevel> recommendedSelectionSimilarABCLevelList = similarQuestionLevelList.stream()
                            .filter(item -> ObjectUtil.isValueEquals("A", item.getLevel()) || ObjectUtil.isValueEquals("B", item.getLevel()) || ObjectUtil.isValueEquals("C", item.getLevel()))
                            .collect(Collectors.toList());
                    insertRecommendationSelectionSimilarLevelList.addAll(recommendedSelectionSimilarABCLevelList);
                    RecommendationSimilarityLevel recommendedSelectionSimilarLevel = similarQuestionLevelList.get(0);

                    if (levelList.contains("C")) {
                        RecommendationSimilarityLevel recommendedSelectionSimilarLevelForD = new RecommendationSimilarityLevel();
                        recommendedSelectionSimilarLevelForD.setRecommendationId(recommendedSelectionSimilarLevelForD.getRecommendationId());
                        recommendedSelectionSimilarLevelForD.setQuestionId(recommendedSelectionSimilarLevelForD.getQuestionId());
                        recommendedSelectionSimilarLevelForD.setSimilarQuestionId(recommendedSelectionSimilarLevelForD.getSimilarQuestionId());
                        recommendedSelectionSimilarLevelForD.setLevel("D");
                        recommendedSelectionSimilarLevelForD.setCreatorId(1L);
                        recommendedSelectionSimilarLevelForD.setCreatorName("自动转化");
                        recommendedSelectionSimilarLevelForD.setCreateDateTime(now);
                        recommendedSelectionSimilarLevelForD.setModifierId(1L);
                        recommendedSelectionSimilarLevelForD.setModifierName("自动化");
                        recommendedSelectionSimilarLevelForD.setModifyDateTime(now);
                        insertRecommendationSelectionSimilarLevelList.add(recommendedSelectionSimilarLevelForD);

                        RecommendationSimilarityLevel wrongBookSelectionSimilarLevelForE = new RecommendationSimilarityLevel();
                        recommendedSelectionSimilarLevelForD.setRecommendationId(recommendedSelectionSimilarLevelForD.getRecommendationId());
                        recommendedSelectionSimilarLevelForD.setQuestionId(recommendedSelectionSimilarLevelForD.getQuestionId());
                        recommendedSelectionSimilarLevelForD.setSimilarQuestionId(recommendedSelectionSimilarLevelForD.getSimilarQuestionId());
                        recommendedSelectionSimilarLevelForD.setLevel("E");
                        recommendedSelectionSimilarLevelForD.setCreatorId(1L);
                        recommendedSelectionSimilarLevelForD.setCreatorName("自动转化");
                        recommendedSelectionSimilarLevelForD.setCreateDateTime(now);
                        recommendedSelectionSimilarLevelForD.setModifierId(1L);
                        recommendedSelectionSimilarLevelForD.setModifierName("自动化");
                        recommendedSelectionSimilarLevelForD.setModifyDateTime(now);
                        insertRecommendationSelectionSimilarLevelList.add(wrongBookSelectionSimilarLevelForE);
                    }
                });
            });
        } else {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "未知转换类型");
        }
        return insertRecommendationSelectionSimilarLevelList;
    }

    @Override
    public List<RecommendationSimilarityLevelVO> getRecommendationSimilarQuestionLevel(RecommendationSimilarQuestionLogicParam recommendationSimilarQuestionLogicParam) {
        if (recommendationSimilarQuestionLogicParam.getRecommendationId() == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR);
        }
        return tikuRepository.selectList("RecommendationSimilarityLevelMapper.getRecommendationSimilarQuestionLevel", recommendationSimilarQuestionLogicParam);
    }
}
