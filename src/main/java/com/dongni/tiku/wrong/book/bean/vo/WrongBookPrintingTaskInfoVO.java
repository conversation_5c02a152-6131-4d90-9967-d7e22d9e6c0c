package com.dongni.tiku.wrong.book.bean.vo;

import cn.hutool.core.bean.BeanUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className WrongBookPrintingTaskInfoVO
 * @since 2024/4/28 16:28
 */
@Getter
@Setter
public class WrongBookPrintingTaskInfoVO extends WrongBookDownloadSummaryVO {

    /**
     * 总页数
     */
    private Integer fileTotalPages;

    /**
     * 文件总数量
     */
    private Integer fileTotalCount;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 文件类型
     */
    private Integer wrongBookFileType;

    /**
     * 纸张版式
     */
    private Integer wrongBookPaperSize;

    public static WrongBookPrintingTaskInfoVO convert(WrongBookDownloadSummaryVO wrongBookDownloadSummaryVO) {
        WrongBookPrintingTaskInfoVO wrongBookPrintingTaskInfoVO = new WrongBookPrintingTaskInfoVO();
        BeanUtil.copyProperties(wrongBookDownloadSummaryVO, wrongBookPrintingTaskInfoVO);
        return wrongBookPrintingTaskInfoVO;
    }

}
