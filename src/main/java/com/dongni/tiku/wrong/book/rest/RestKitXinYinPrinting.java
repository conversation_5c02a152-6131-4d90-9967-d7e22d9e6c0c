package com.dongni.tiku.wrong.book.rest;

import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.dongni.common.rest.kit.impl.RestKitTemplate;
import com.dongni.common.wechat.service.WeChatService;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.wrong.book.schedule.WrongBookTaskSchedule;
import com.dongni.tiku.wrong.book.utils.WrongBookOrderUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @className RestKitXinYinPrinting
 * @since 2024/5/7 18:30
 */
@Component
public class RestKitXinYinPrinting extends RestKitTemplate {

    private final  static int EXPIRE_SECONDS = 60 * 60 * 24;

    private static final Logger log = LogManager.getLogger(RestKitXinYinPrinting.class);

    @Autowired
    private RestTemplate restTemplate;

    @Override
    protected void before(String url, Map<String, Object> queryParams, HttpHeaders headers) {
        String xinYinToken = refreshXinYinPrintingToken();
        headers.set("token", xinYinToken);
        log.info("新印打印参数 - {}", JSONUtil.toJsonStr(queryParams));
    }

    @Override
    protected <T> T after(String url, Map<String, Object> queryParams, ResponseEntity<T> responseEntity) {
        return responseEntity.getBody();
    }

    public String refreshXinYinPrintingToken() {
        String url = WrongBookOrderUtil.host() + "/auth/login";
        // 请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<String, Object>();
        postParameters.add("username", WrongBookOrderUtil.username());
        postParameters.add("password", DigestUtil.sha256Hex(WrongBookOrderUtil.password()));
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(postParameters, headers);

        ResponseEntity<String> entity = restTemplate.postForEntity(url, httpEntity, String.class);
        if (entity.getStatusCode() == HttpStatus.UNAUTHORIZED) {
            log.error("新印token请求失败");
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "新印token刷新失败", entity);
        }
        if (entity.getStatusCode() == HttpStatus.OK) {
            String body = entity.getBody();
            Map<String, Object> result = (Map<String, Object>) JSONUtil.toBean(body, Map.class);
            String token = MapUtil.getString(result, "data");
            JedisTemplate.execute(jedis -> jedis.setex(JedisUtil.getKey("xinYinToken"), EXPIRE_SECONDS, token));
            return token;
        } else {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "新印token刷新失败", entity);
        }
    }

}
