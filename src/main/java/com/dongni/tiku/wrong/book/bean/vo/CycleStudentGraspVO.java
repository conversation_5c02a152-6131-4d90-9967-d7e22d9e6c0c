package com.dongni.tiku.wrong.book.bean.vo;

import com.dongni.tiku.similar.enumeration.Difficulty;

import java.util.List;

/**
 * <p>周期本知识点提升对象</p>
 *
 * <AUTHOR>
 * @className CycleGraspVO
 * @since 2023/7/10 20:10
 */
public class CycleStudentGraspVO {

        private Difficulty difficulty;

        private List<CycleStudentGrasp> cycleStudentGraspList;

        public Difficulty getDifficulty() {
                return difficulty;
        }

        public void setDifficulty(Difficulty difficulty) {
                this.difficulty = difficulty;
        }

        public List<CycleStudentGrasp> getCycleStudentGraspList() {
                return cycleStudentGraspList;
        }

        public void setCycleStudentGraspList(List<CycleStudentGrasp> cycleStudentGraspList) {
                this.cycleStudentGraspList = cycleStudentGraspList;
        }

        public static class CycleStudentGrasp {
                private String knowledgeId;

                private String knowledgeName;

                private int checkCount;

                private double studentGrasp;

                private double classGrasp;

                private double gradeGrasp;

                public String getKnowledgeId() {
                        return knowledgeId;
                }

                public void setKnowledgeId(String knowledgeId) {
                        this.knowledgeId = knowledgeId;
                }

                public String getKnowledgeName() {
                        return knowledgeName;
                }

                public void setKnowledgeName(String knowledgeName) {
                        this.knowledgeName = knowledgeName;
                }

                public int getCheckCount() {
                        return checkCount;
                }

                public void setCheckCount(int checkCount) {
                        this.checkCount = checkCount;
                }

                public double getStudentGrasp() {
                        return studentGrasp;
                }

                public void setStudentGrasp(double studentGrasp) {
                        this.studentGrasp = studentGrasp;
                }

                public double getClassGrasp() {
                        return classGrasp;
                }

                public void setClassGrasp(double classGrasp) {
                        this.classGrasp = classGrasp;
                }

                public double getGradeGrasp() {
                        return gradeGrasp;
                }

                public void setGradeGrasp(double gradeGrasp) {
                        this.gradeGrasp = gradeGrasp;
                }
        }


        @Override
        public String toString() {
                return "CycleStudentGraspVO{" +
                        "difficulty=" + difficulty +
                        ", cycleStudentGraspList=" + cycleStudentGraspList +
                        '}';
        }
}
