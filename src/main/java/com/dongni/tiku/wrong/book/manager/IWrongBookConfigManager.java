package com.dongni.tiku.wrong.book.manager;

import com.dongni.common.mongo.IManager;
import com.dongni.commons.mongodb.MongoClientManager;

/**
 * <p>错题本配置抽象类 拆封到IWrongBookTeacherConfigManager 和 IWrongBookStudentConfigManager</p>
 *
 * <AUTHOR>
 * @since 2022/8/19 18:08
 */
public class IWrongBookConfigManager extends IManager {

    protected IWrongBookConfigManager(MongoClientManager mongoClientManager, String collectionName, String collectionCnName) {
        super(mongoClientManager, collectionName, collectionCnName);
    }
}
