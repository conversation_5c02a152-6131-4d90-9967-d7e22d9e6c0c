package com.dongni.tiku.urgency.service.impl;

import com.dongni.common.utils.DictUtil;
import com.dongni.exam.plan.service.ExamPaperService;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.urgency.service.AbstractWrongBookUrgencyService;
import com.dongni.tiku.wrong.book.service.RecommendationService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import static java.util.stream.Collectors.toList;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/12/20 下午 03:28
 * @Version 1.0.0
 */
@Service
public class RecommendationWrongBookUrgencyService extends AbstractWrongBookUrgencyService {
    @Autowired
    private RecommendationService recommendationService;

    /**
     * 排序任务
     * 1.按交付类型 + 阅卷状态排序
     * 2.再按recommendationId排序
     *
     * @param autoAppointList recommendationId paperId deliverType
     */
    public void urgencySorted(List<Map<String, Object>> autoAppointList) {
        if (CollectionUtils.isEmpty(autoAppointList)) {
            return;
        }
        List<Long> paperIdList = autoAppointList.stream()
                .filter(i -> MapUtil.getLongNullable(i, "paperId") != null)
                .map(i -> MapUtil.getLong(i, "paperId"))
                .collect(toList());
        Map<Long, Integer> paperStatus = getPaperStatus(paperIdList);
        autoAppointList.forEach(i -> {
            Long answerPaperId = MapUtil.getLongNullable(i, "paperId");
            // 默认为 multipleFormalDeliveries
            int deliverType = MapUtil.getInt(i, "deliverType", DictUtil.getDictValue("wrongBookDeliverType", "multipleFormalDeliveries"));
            // 没有考试的默认为 未阅卷完成
            int status = answerPaperId == null ? 1 : paperStatus.get(answerPaperId);
            i.put("sortField1", toSortValue(deliverType, status));
        });
        // 排序
        autoAppointList.sort(Comparator.comparing(i -> MapUtil.getInt((Map<String, Object>) i, "sortField1"))
                .thenComparing(i -> MapUtil.getLong((Map<String, Object>) i, "taskId")));
    }

    @Override
    public void updateWrongBookLevel(List<Map<String, Object>> task, Map<Long, Integer> paperStatus) {
        Map<Integer, List<Long>> updateParams = covertTaskUpdateParams(task, paperStatus, "recommendationId");
        update(updateParams);
    }

    @Override
    public List<Map<String, Object>> getNeedUpdateTask(Date dateTime) {
        return recommendationService.getNeedUpdateTask(dateTime);
    }

    @Override
    protected void doUpdateWrongBookLevel(Integer wrongBookLevel, List<Long> idList) {
        Map<String, Object> params = MapUtil.of("wrongBookLevel", wrongBookLevel, "idList", idList);
        recommendationService.updateWrongBookLevel(params);
    }
}
