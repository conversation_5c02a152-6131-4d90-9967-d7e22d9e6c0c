package com.dongni.tiku.common.util.docx;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Docx file import utility class
 * <AUTHOR>
 * @date 2025/06/23
 */
public class DocxImportUtil {

    private static final Logger log = LogManager.getLogger(DocxImportUtil.class);

    public static void main(String[] args) {
        String docxPath = "D:/home/<USER>/docxToHtml.docx";
        String exportHtmlDemo = "D:/home/<USER>/exportHtmlDemo.html";
        String htmlResult = docxToHtml(docxPath);
        System.out.println(htmlResult);
    }

    /**
     * Read docx file content and convert to html
     * @param docxPath docx file path
     * @return html
     */
    public static String docxToHtml(String docxPath) {
        return "";
    }

}
