package com.dongni.tiku.common.util.question;

import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.MapBuilder;
import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.convert.QuestionStructUtil;
import com.dongni.tiku.common.util.convert.TikuConvertUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 教辅试题
 * <AUTHOR>
 * @date 2025/03/28
 */
public class StudyGuideQuestionUtil {
    
    // ---------------------------------------------------------------------------------------------- 试题类型转换 start
    
    /**
     * 用于类型转换
     * @param studyGuideQuestion studyGuideQuestion
     * @return 字段类型转换后的studyGuideQuestion
     */
    static Map<String, Object> getQuestion(Map<String, Object> studyGuideQuestion) {
        return MapBuilder.ofLinked(studyGuideQuestion)
                // id
                .setIfNotAbsent("_id", MongoUtil::getMongoId)                     // 试题id 如果为null是不能设置的 mongo插入时不能提供null
                .set("complete", StudyGuideQuestionUtil::convertComplete)         // 是否已经完成 0未完成 1已完成 知识点都标注完成才是已完成
                // 试题信息
                .set("questionStatus", QuestionStructUtil::convertQuestionStatus) // 题目状态 1used 表示已经被组卷了，不能删除
                .set("gradeType", MapUtil::getInt)                                // 年级
                .set("courseId", MapUtil::getLong)                                // 课程id
                .set("courseName", MapUtil::getString)                            // 课程名称
                .set("questionType", MapUtil::getInt)                             // 试题类型
                .set("questionTypeName", MapUtil::getString)                      // 题型名称
                .set("difficulty", MapUtil::getDouble)                            // 难度
                .set("source", TikuConvertUtil::convertStringDefaultBlank)        // 试题来源 一般来说是paperName
                .set("areaId", MapUtil::getInt)                                   // 试题的归属区域
                .setByParams("search", QuestionUtil::getSearch)                   // 检索字段
                .setByParams("knowledgeIdList", QuestionStructUtil::convertKnowledgeIdList) // 知识点idList 从小题中抽出来的
                .set("stem", TikuConvertUtil::convertStringDefaultBlank)                           // 题干
                .set("questions", MapUtil::getListMap)                                             // 小题
                // 试题创建修改信息
                // 试题的 createDateTime 和 modifyDateTime 全部都解析为 Long类型，原因是试题列表需要排序
                .set("creatorId", MapUtil::getLong)
                .set("creatorName", MapUtil::getString)
                .set("createDateTime", TikuConvertUtil::convertDate2Long)
                .set("modifierId", MapUtil::getLong)
                .set("modifierName", MapUtil::getString)
                .set("modifyDateTime", TikuConvertUtil::convertDate2Long)
                .build();
    }
    
    /**
     * 获取标注完成状态 默认未完成
     * @param completeObj complete default 0
     * @return complete 1已完成 0未完成 知识点都标注完成才是已完成
     */
    private static int convertComplete(Object completeObj) {
        return MapUtil.getInt(completeObj, 0);
    }
    
    
    /**
     * 获取归属区域 教辅的归属地区不允许为空
     * @param areaIdsObj areaIdsObj
     * @return areaIds 不允许为空
     */
    private static List<Long> convertAreaIds(Object areaIdsObj) {
        List<Long> areaIds = TikuConvertUtil.convertListLong(areaIdsObj);
        if (CollectionUtils.isEmpty(areaIds)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "教辅试题归属区域不允许为空");
        }
        return areaIds;
    }
    
    // ---------------------------------------------------------------------------------------------- 试题类型转换 end
    
    /**
     * studyGuideQuestion -> question
     * @param studyGuideQuestion studyGuideQuestion
     * @return question
     */
    static Map<String, Object> toDongni(Map<String, Object> studyGuideQuestion) {
        if (MapUtil.getInt(studyGuideQuestion, "complete") != 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "教辅试题转校本试题失败: 试题还没完成标注");
        }
        Map<String, Object> tmp = new HashMap<>(studyGuideQuestion);
        tmp.put("studyGuideId", studyGuideQuestion.get("_id").toString());
        tmp.put("questionStatus", DictUtil.getDictValue("questionStatus", "used"));     // 已引用 引用的不能删除
        tmp.put("ascriptionType", DictUtil.getDictValue("ascriptionType", "personal"));  // 私人的
        tmp.put("userIds", new ArrayList<Long>());
        tmp.put("schoolIds", new ArrayList<Long>());
        tmp.put("usedCount", 0);
        tmp.put("answerCount", 0);
        tmp.put("creatorId", 2L);
        tmp.put("creatorName", "题库管理员(教辅)");
        tmp.put("createDateTime", TikuConvertUtil.convertDate2Long(studyGuideQuestion.get("createDateTime")));
        tmp.put("modifierId", 2L);
        tmp.put("modifierName", "题库管理员(教辅)");
        tmp.put("modifyDateTime", TikuConvertUtil.convertDate2Long(studyGuideQuestion.get("modifyDateTime")));
        Map<String, Object> question = DongniQuestionUtil.getQuestion(tmp);
        question.put("_id", studyGuideQuestion.get("_id"));
        return question;
    }
}
