package com.dongni.tiku.common.util;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import org.docx4j.jaxb.Context;
import org.docx4j.openpackaging.exceptions.InvalidFormatException;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.ObjectFactory;
import org.docx4j.wml.RPr;

import java.util.Map;

/**
 * Created by Heweipo on 2016/11/29.
 */
public class PaperDocxExport {


    // 格式类型，1 公式，2 图片
    protected int outputType;
    // 内容类型，1 题目，2 解析，3 作答， 4 举一反三
    protected int[] reviewType;
    // 标识题号
    protected int index;
    // DOCX 对象工厂
    protected ObjectFactory factory;
    // DOCX 对象包
    protected WordprocessingMLPackage wordMLPackage;
    // 文档对象
    protected MainDocumentPart mdp;
    // 字体属性
    protected RPr titleRPr;

    /**
     * 初始化错题的内容类型和格式类型
     * @param reviewType  内容类型，1 题目，2 解析，3 作答， 4 举一反三
     * @param outputType  格式类型，1 公式，2 图片
     */
    public PaperDocxExport(int[] reviewType, int outputType) {
        this.reviewType = reviewType;
        this.outputType = outputType;
        try {
            factory = Context.getWmlObjectFactory();
            titleRPr = DocxExportUtil.getTitleRPr();
            wordMLPackage = WordprocessingMLPackage.createPackage();
        } catch (InvalidFormatException e) {
            throw new CommonException(ResponseStatusEnum.FAILURE, e);
        }
        mdp = wordMLPackage.getMainDocumentPart();
    }

    /**
     * 生成文件直接至文件系统
     *
     * @param fileName 文件名称
     * @param title    文件抬头
     * @param paper   试卷内容
     * @return 返回文件对象
     */
    public String generateFileToFileStorage(String fileName, String title, Map<String, Object> paper) {
        QuestionDocxExport export = new QuestionDocxExport(reviewType, outputType);
        export.reviewSize = (String) paper.get("reviewSize");
        export.reviewFont = (String)paper.get("reviewFont");
        return export.generateFileToFileStorage1(fileName, title, PaperUtil.getQuestions(paper),(Map)paper.get("paperSetting"));
    }
    
}
