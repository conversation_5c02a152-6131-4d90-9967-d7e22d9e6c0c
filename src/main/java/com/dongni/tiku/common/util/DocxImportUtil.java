package com.dongni.tiku.common.util;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.docx4j.openpackaging.exceptions.Docx4JException;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

/**
 * Docx file import utility class
 * <AUTHOR>
 * @date 2025/06/23
 */
public class DocxImportUtil {

    private static final Logger log = LogManager.getLogger(DocxImportUtil.class);

    public static void main(String[] args) {
        String docxPath = "D:/home/<USER>/docxToHtml.docx";
        String exportHtmlDemo = "D:/home/<USER>/exportHtmlDemo.html";

        System.out.println("Starting docx to HTML conversion...");
        String htmlResult = docxToHtml(docxPath);

        if (htmlResult != null) {
            System.out.println("Conversion successful!");
            System.out.println("HTML length: " + htmlResult.length());
            System.out.println("HTML preview:");
            System.out.println(htmlResult.substring(0, Math.min(1000, htmlResult.length())));
        } else {
            System.out.println("Conversion failed!");
        }
    }

    /**
     * Read docx file content and convert to html
     * @param docxPath docx file path
     * @return html
     */
    public static String docxToHtml(String docxPath) {
        if (StringUtils.isBlank(docxPath)) {
            log.error("docx file path cannot be empty");
            return null;
        }

        File docxFile = new File(docxPath);
        if (!docxFile.exists()) {
            log.error("docx file does not exist: {}", docxPath);
            return null;
        }

        try {
            // Load docx file
            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(new FileInputStream(docxFile));

            // Get main document part
            MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();

            // Convert to HTML
            return convertToHtml(mainDocumentPart);

        } catch (Docx4JException | IOException e) {
            log.error("Failed to read docx file: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Convert MainDocumentPart to HTML
     * @param mainDocumentPart main document part
     * @return HTML string
     */
    private static String convertToHtml(MainDocumentPart mainDocumentPart) {
        try {
            StringBuilder htmlBuilder = new StringBuilder();
            htmlBuilder.append("<!DOCTYPE html>\n");
            htmlBuilder.append("<html>\n");
            htmlBuilder.append("<head>\n");
            htmlBuilder.append("<meta charset=\"UTF-8\">\n");
            htmlBuilder.append("<title>Document</title>\n");
            htmlBuilder.append("<style>\n");
            htmlBuilder.append("body { font-family: 'Times New Roman', serif; margin: 20px; }\n");
            htmlBuilder.append("p { margin: 6px 0; }\n");
            htmlBuilder.append("table { border-collapse: collapse; width: 100%; }\n");
            htmlBuilder.append("td, th { border: 1px solid #ddd; padding: 8px; }\n");
            htmlBuilder.append("</style>\n");
            htmlBuilder.append("</head>\n");
            htmlBuilder.append("<body>\n");

            // Get document content
            Document document = mainDocumentPart.getJaxbElement();
            Body body = document.getBody();

            // Process document content
            if (body != null) {
                List<Object> bodyElements = body.getContent();
                for (Object element : bodyElements) {
                    processElement(element, htmlBuilder);
                }
            }

            htmlBuilder.append("</body>\n");
            htmlBuilder.append("</html>");

            return htmlBuilder.toString();

        } catch (Exception e) {
            log.error("Failed to convert to HTML: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Process document element
     * @param element document element
     * @param htmlBuilder HTML builder
     */
    private static void processElement(Object element, StringBuilder htmlBuilder) {
        if (element instanceof P) {
            // Process paragraph
            processParagraph((P) element, htmlBuilder);
        } else if (element instanceof Tbl) {
            // Process table
            processTable((Tbl) element, htmlBuilder);
        } else {
            // Handle other element types
            log.debug("Unhandled element type: {}", element.getClass().getSimpleName());
        }
    }

    /**
     * Process paragraph
     * @param paragraph paragraph object
     * @param htmlBuilder HTML builder
     */
    private static void processParagraph(P paragraph, StringBuilder htmlBuilder) {
        htmlBuilder.append("<p>");

        List<Object> paragraphContent = paragraph.getContent();
        for (Object content : paragraphContent) {
            if (content instanceof R) {
                // Process text run
                processRun((R) content, htmlBuilder);
            }
        }

        htmlBuilder.append("</p>\n");
    }

    /**
     * Process text run
     * @param run text run object
     * @param htmlBuilder HTML builder
     */
    private static void processRun(R run, StringBuilder htmlBuilder) {
        List<Object> runContent = run.getContent();

        // Check text formatting
        RPr rPr = run.getRPr();
        boolean isBold = false;
        boolean isItalic = false;
        boolean isUnderline = false;

        if (rPr != null) {
            isBold = rPr.getB() != null && rPr.getB().isVal();
            isItalic = rPr.getI() != null && rPr.getI().isVal();
            isUnderline = rPr.getU() != null;
        }

        // Apply format tags
        if (isBold) htmlBuilder.append("<strong>");
        if (isItalic) htmlBuilder.append("<em>");
        if (isUnderline) htmlBuilder.append("<u>");

        // Process text content
        for (Object content : runContent) {
            if (content instanceof Text) {
                Text text = (Text) content;
                String textValue = text.getValue();
                if (textValue != null) {
                    // HTML escape
                    textValue = textValue.replace("&", "&amp;")
                                       .replace("<", "&lt;")
                                       .replace(">", "&gt;")
                                       .replace("\"", "&quot;")
                                       .replace("'", "&#39;");
                    htmlBuilder.append(textValue);
                }
            } else if (content instanceof Br) {
                // Handle line break
                htmlBuilder.append("<br>");
            } else if (content instanceof R.Tab) {
                // Handle tab
                htmlBuilder.append("&nbsp;&nbsp;&nbsp;&nbsp;");
            }
        }

        // Close format tags
        if (isUnderline) htmlBuilder.append("</u>");
        if (isItalic) htmlBuilder.append("</em>");
        if (isBold) htmlBuilder.append("</strong>");
    }

    /**
     * Process table
     * @param table table object
     * @param htmlBuilder HTML builder
     */
    private static void processTable(Tbl table, StringBuilder htmlBuilder) {
        htmlBuilder.append("<table>\n");

        List<Object> tableContent = table.getContent();
        for (Object content : tableContent) {
            if (content instanceof Tr) {
                // Process table row
                processTableRow((Tr) content, htmlBuilder);
            }
        }

        htmlBuilder.append("</table>\n");
    }

    /**
     * Process table row
     * @param row table row object
     * @param htmlBuilder HTML builder
     */
    private static void processTableRow(Tr row, StringBuilder htmlBuilder) {
        htmlBuilder.append("<tr>\n");

        List<Object> rowContent = row.getContent();
        for (Object content : rowContent) {
            if (content instanceof Tc) {
                // Process table cell
                processTableCell((Tc) content, htmlBuilder);
            }
        }

        htmlBuilder.append("</tr>\n");
    }

    /**
     * Process table cell
     * @param cell table cell object
     * @param htmlBuilder HTML builder
     */
    private static void processTableCell(Tc cell, StringBuilder htmlBuilder) {
        htmlBuilder.append("<td>");

        List<Object> cellContent = cell.getContent();
        for (Object content : cellContent) {
            if (content instanceof P) {
                // Process paragraph in cell without p tags
                processCellParagraph((P) content, htmlBuilder);
            }
        }

        htmlBuilder.append("</td>\n");
    }

    /**
     * Process paragraph in cell (without p tags)
     * @param paragraph paragraph object
     * @param htmlBuilder HTML builder
     */
    private static void processCellParagraph(P paragraph, StringBuilder htmlBuilder) {
        List<Object> paragraphContent = paragraph.getContent();
        for (Object content : paragraphContent) {
            if (content instanceof R) {
                // Process text run
                processRun((R) content, htmlBuilder);
            }
        }
    }

}
