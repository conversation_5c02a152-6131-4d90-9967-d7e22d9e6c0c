package com.dongni.open.mq.test;

import com.dongni.common.entity.Response;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.open.mq.producer.ExamScoreUpdateProducer;
import com.dongni.open.mq.producer.HomeworkCorrectChangeProducer;
import com.dongni.open.mq.producer.HomeworkFinishProducer;
import com.dongni.open.mq.service.ThirdTransferUserIdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR> <br/>
 * @date 2019/11/14 <br/>
 *  TODO 测试代码
 */
// @DongniNotRequireLogin
@RestController
@RequestMapping("mq")
public class MqTestController {
    
    @Autowired(required = false)
    private ThirdTransferUserIdService thirdTransferUserIdService;

    @PostMapping("/send/homework/finished")
    public Response sendHomeworkFinished(Map<String, Object> params) {
        
        params.put("examId", 123456);
        params.put("publishUserId", 123456);
        
        HomeworkFinishProducer.send(params);
        return new Response();
    }
    
    @PostMapping("/send/homework/correctChange")
    public Response sendHomeworkCorrectChange(Map<String, Object> params) {
    
        params.put("examId", 123456);
        params.put("teacherUserId", 123456);
        params.put("status", new Random().nextBoolean() ? 1 : 0);
        
        HomeworkCorrectChangeProducer.send(params);
        return new Response();
    }
    
    @PostMapping("/send/exam/scoreUpdate")
    public Response sendExamScoreUpdate(Map<String, Object> params) {
        ExamScoreUpdateProducer.send(params);
        return new Response();
    }
    
    
    @GetMapping("getThirdUserId")
    public Response getThirdUserId(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("dongniUserId")
                .isNotBlank("clientId")
                .verify();
        long dongniUserId = Long.parseLong(params.get("dongniUserId").toString());
        String clientId = params.get("clientId").toString();
    
        Object thirdUserId = thirdTransferUserIdService.getThirdUserId(dongniUserId, clientId);
        
        return new Response(thirdUserId);
    }
}
