package com.dongni.open.mq.consumer;

import com.dongni.commons.mq.common.condition.MqCondition;
import com.dongni.commons.mq.common.service.IMqQueueInfoService;
import com.dongni.commons.utils.IdCheckUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.common.utils.spring.LocalDisabledCondition;
import com.dongni.exam.plan.service.ExamSchoolService;
import com.dongni.open.mq.producer.HomeworkFinishProducer;
import com.dongni.open.mq.service.ThirdTransferUserIdService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> <br/>
 * @date 2019/10/31 <br/>
 * 作业完成消息推送  消费者
 *    homework-finished
 *
 * 消息内容:
 *    - examId  作业examId
 *    - userId  发布人用户id
 */
@Component
@Conditional({MqCondition.class, LocalDisabledCondition.class})
public final class HomeworkFinishConsumer extends AbstractThirdConsumer {

    @Autowired
    private ExamSchoolService examSchoolService;

    @Autowired
    private ThirdTransferUserIdService thirdTransferUserIdService;

    private static final IMqQueueInfoService QUEUE_INFO = HomeworkFinishProducer.QUEUE_INFO;

    @Override
    IMqQueueInfoService getMqQueueInfoService() {
        return QUEUE_INFO;
    }

    @Override
    String checkParams(Map<String, Object> messageMap) {
        return Verify.of(messageMap)
                .isValidId("examId")
                .isValidId("userId")
                .verifyNotThrow();
    }

    @Override
    List<Long> getSchoolIdList(Map<String, Object> messageMap) {
        List<Map<String, Object>> examSchoolList = examSchoolService.getExamSchool(messageMap);
        if (CollectionUtils.isEmpty(examSchoolList)) {
            return null;
        }

        return examSchoolList.stream()
                .map(o -> Long.parseLong(o.get("schoolId").toString()))
                .collect(toList());
    }

    @Override
    Map<String, Object> getSendData(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("examId")
                .isValidId("userId")
                .verify();

        long examId = Long.parseLong(params.get("examId").toString());
        Long examIdEncrypt = IdCheckUtil.encrypt("examId", examId);

        long userId = Long.parseLong(params.get("userId").toString());
        Long userIdEncrypt = IdCheckUtil.encrypt("userId", userId);

        Map<String, Object> sendData = new HashMap<>(2);
        sendData.put("examId", examIdEncrypt);
        sendData.put("userId", userIdEncrypt);

        return sendData;
    }

    @Override
    Map<String, Object> getSpecialSendData(Map<String, Object> sendData, Map<String, Object> params, String clientId) {
        Verify.of(params).isValidId("userId").verify();

        long dongniUserId = Long.parseLong(params.get("userId").toString());

        Object thirdUserId = thirdTransferUserIdService.getThirdUserId(dongniUserId, clientId);
        if (thirdUserId != null) {
            sendData.put("userId", thirdUserId);
        }

        return sendData;
    }
}
