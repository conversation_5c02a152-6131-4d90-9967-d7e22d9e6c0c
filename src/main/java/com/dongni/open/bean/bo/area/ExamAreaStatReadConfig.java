package com.dongni.open.bean.bo.area;

import java.util.Collection;

/**
 * 区域总分统计
 *
 * <AUTHOR>
 * @date 2023/4/3 17:44
 */
public class ExamAreaStatReadConfig extends AbstractAreaStatReadConfig {
    @Override
    public String getCollectionName() {
        return "examAreaStat";
    }

    @Override
    public String getJsonPath() {
        return "examAreaStat.json";
    }

    @Override
    public FilePath getFiePath() {
        return FilePath.AREA;
    }

    @Override
    public String getAreaPipelineStr(String[] instanceIds) {
        return "[\n" +
                "    {\n" +
                "        $match: {\n" +
                "            examId: " + instanceIds[0] + ",\n" +
                "            statId: " + instanceIds[1] + "\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        $project: {\n" +
                "            _id: 0,\n" +
                "            examId: 1,\n" +
                "            statId: 1,\n" +
                "            areaId: 1,\n" +
                "            areaName: 1,\n" +
                "            examName: 1,\n" +
                "            statName: 1,\n" +
                "            averageScore: 1,\n" +
                "            averageSubtraction: 1,\n" +
                "            averageSubtractionRate: 1,\n" +
                "            absentNumber: 1,\n" +
                "            excellentNumber: 1,\n" +
                "            excellentRate: 1,\n" +
                "            goodRate: 1,\n" +
                "            passRate: 1,\n" +
                "            allPassRate: 1,\n" +
                "            standardDeviation: 1,\n" +
                "            highestScore: 1,\n" +
                "            lowestScore: 1,\n" +
                "            participationNumber: 1,\n" +
                "            passNumber: 1,\n" +
                "            lowScoreNumber: 1,\n" +
                "            upperQuartile: 1,\n" +
                "            medianValue: 1,\n" +
                "            lowerQuartile: 1,\n" +
                "            lowScoreRate: 1,\n" +
                "            range: 1,\n" +
                "        beforeAfterRank: {\n" +
                "            $concatArrays: [{\n" +
                "                $map: {\n" +
                "                    input: '$beforeRank',\n" +
                "                    as: 'item',\n" +
                "                    in: {\n" +
                "                        '名次段': {\n" +
                "                            $concat: ['前', {\n" +
                "                                '$convert': {\n" +
                "                                    'input': '$$item.ranking',\n" +
                "                                    'to': 'string',\n" +
                "                                    'onError': '$$item.ranking',\n" +
                "                                    'onNull': ''\n" +
                "                                }\n" +
                "                            }, '名']\n" +
                "                        }, '人数': '$$item.total'\n" +
                "                    }\n" +
                "                }\n" +
                "            }, {\n" +
                "                $map: {\n" +
                "                    input: '$afterRank',\n" +
                "                    as: 'item',\n" +
                "                    in: {\n" +
                "                        '名次段': {\n" +
                "                            $concat: ['后', {\n" +
                "                                '$convert': {\n" +
                "                                    'input': '$$item.ranking',\n" +
                "                                    'to': 'string',\n" +
                "                                    'onError': '$$item.ranking',\n" +
                "                                    'onNull': ''\n" +
                "                                }\n" +
                "                            }, '名']\n" +
                "                        }, '人数': '$$item.total'\n" +
                "                    }\n" +
                "                }\n" +
                "            }]\n" +
                "        },\n" +
                "        proportionBeforeAfterRank: {\n" +
                "            $concatArrays: [{\n" +
                "                $map: {\n" +
                "                    input: '$proportionBeforeRank',\n" +
                "                    as: 'item',\n" +
                "                    in: {\n" +
                "                        '名次段': {\n" +
                "                            $concat: ['前', {\n" +
                "                                '$convert': {\n" +
                "                                    'input': '$$item.ranking',\n" +
                "                                    'to': 'string',\n" +
                "                                    'onError': '$$item.ranking',\n" +
                "                                    'onNull': ''\n" +
                "                                }\n" +
                "                            }, '%']\n" +
                "                        }, '人数': '$$item.total'\n" +
                "                    }\n" +
                "                }\n" +
                "            }, {\n" +
                "                $map: {\n" +
                "                    input: '$proportionAfterRank',\n" +
                "                    as: 'item',\n" +
                "                    in: {\n" +
                "                        '名次段': {\n" +
                "                            $concat: ['后', {\n" +
                "                                '$convert': {\n" +
                "                                    'input': '$$item.ranking',\n" +
                "                                    'to': 'string',\n" +
                "                                    'onError': '$$item.ranking',\n" +
                "                                    'onNull': ''\n" +
                "                                }\n" +
                "                            }, '%']\n" +
                "                        }, '人数': '$$item.total'\n" +
                "                    }\n" +
                "                }\n" +
                "            }]\n" +
                "        },\n" +
                "            scoreSection: {\n" +
                "                $map: {\n" +
                "                    input: '$scoreSection',\n" +
                "                    as: 'item',\n" +
                "                    in: {\n" +
                "                        '分数段': {\n" +
                "                            $cond: {\n" +
                "                                if: {$eq: [{$toString: '$$item.close'}, null]},\n" +
                "                                then: {$concat: [{$toString: '$$item.open'}]},\n" +
                "                                else: {$concat: ['(', {$toString: '$$item.close'}, '-', {$toString: '$$item.open'}, ']']}\n" +
                "                            }\n" +
                "                        }, '人数': '$$item.total'\n" +
                "                    }\n" +
                "                }\n" +
                "\n" +
                "            },\n" +
                "            rankSection: {\n" +
                "                $map: {\n" +
                "                    input: '$rankSection',\n" +
                "                    as: 'item',\n" +
                "                    in: {\n" +
                "                        '名次段': {\n" +
                "                            $concat: [{\n" +
                "                                '$convert': {\n" +
                "                                    'input': '$$item.open',\n" +
                "                                    'to': 'string',\n" +
                "                                    'onError': '$$item.ranking',\n" +
                "                                    'onNull': ''\n" +
                "                                }\n" +
                "                            }, '-', {\n" +
                "                                '$convert': {\n" +
                "                                    'input': '$$item.close',\n" +
                "                                    'to': 'string',\n" +
                "                                    'onError': '$$item.ranking',\n" +
                "                                    'onNull': ''\n" +
                "                                }\n" +
                "                            }]\n" +
                "                        }, '人数': '$$item.total'\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "]";
    }
}
