package com.dongni.open.bean.bo;

/**
 * 班级试题
 *
 * <AUTHOR>
 * @date 2023/7/3 14:47
 */
public class ExamClassQuestionStatReadConfig extends StatReadConfig {

	@Override
	public String getCollectionName() {
		return "examClassQuestionStat";
	}

	@Override
	public String getHeadersJsonFilePath() {
		return statJsonPath.concat("/examClassQuestionStat.json");
	}

	@Override
	public String getPipelineStr(long schoolId, String[] instanceIDStr) {
		return "[\n" +
				"    {\n" +
				"        $match: {\n" +
				"            examId: " + instanceIDStr[0] + ",\n" +
				"            schoolId: " + schoolId + ",\n" +
				"            statId: " + instanceIDStr[1] + "\n" +
				"        }\n" +
				"    },\n" +
				"    {\n" +
				"        $project: {\n" +
				"            _id: 0,\n" +
				"            schoolId: 1,\n" +
				"            examId: 1,\n" +
				"            statId: 1,\n" +
				"            paperName: 1,\n" +
				"            courseId: 1,\n" +
				"            courseName: 1,\n" +
				"            classId: 1,\n" +
				"            className: 1,\n" +
				"            structureNumber: 1,\n" +
				"            questionTypeName: 1,\n" +
				"            averageScore: 1,\n" +
				"            fullScoreRate: 1,\n" +
				"            zeroScoreRate: 1,\n" +
				"            passRate: 1\n" +
				"        }\n" +
				"    }\n" +
				"]";
	}
}
