package com.dongni.open.bean.bo.area.school;

import com.dongni.open.bean.bo.area.AbstractAreaStatReadConfig;

import java.util.Collection;
import java.util.List;

/**
 * 区域班级档次统计
 * <AUTHOR>
 * @date 2023/4/20 17:13
 */
public class AreaExamClassConsistStatReadConfig extends AbstractAreaStatReadConfig {

    @Override
    public String getCollectionName() {
        return "examClassConsistStat";
    }

    @Override
    public String getJsonPath() {
        return "areaExamClassConsistStat.json";
    }

    @Override
    public FilePath getFiePath() {
        return FilePath.SCH;
    }

    @Override
    public String getAreaPipelineStr(String[] instanceIds) {
        return "[\n" +
                "    {\n" +
                "        $match: {\n" +
                "            examId: " + instanceIds[0] + ", statId: " + instanceIds[1] + "\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        $project: {\n" +
                "            _id: 0,\n" +
                "            examId: 1, schoolId: 1, schoolName: 1, statId: 1, classId: 1, className: 1,\n" +
                "            participationNumber: 1, totalStudent: 1, highestScore: 1, lowestScore: 1, averageScore: 1, consist: {$concatArrays: [{\n" +
                "                    $map: {\n" +
                "                        input: '$consist',\n" +
                "                        as: 'item',\n" +
                "                        in: {\n" +
                "                            '档次': '$$item.name',\n" +
                "                            '最大分值': '$$item.maxScore',\n" +
                "                            '最小分值': '$$item.minScore',\n" +
                "                            '人数': '$$item.number',\n" +
                "                            '比例': '$$item.rate'\n" +
                "                        }\n" +
                "                    }\n" +
                "                }]\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "]";
    }
}
