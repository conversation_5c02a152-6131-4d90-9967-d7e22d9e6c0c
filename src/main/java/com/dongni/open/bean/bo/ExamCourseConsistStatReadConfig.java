package com.dongni.open.bean.bo;

/**
 * 学科档次
 *
 * <AUTHOR>
 * @date 2023/6/25 18:21
 */
public class ExamCourseConsistStatReadConfig extends StatReadConfig {
	@Override
	public String getCollectionName() {
		return "examCourseConsistStat";
	}

	@Override
	public String getHeadersJsonFilePath() {
		return statJsonPath.concat("/examCourseConsistStat.json");
	}

	@Override
	public String getPipelineStr(long schoolId, String[] instanceIDStr) {
		return "[\n" +
				"    {\n" +
				"        $match: {\n" +
				"            examId: " + instanceIDStr[0] + ", statId: " + instanceIDStr[1] + "\n" +
				"        }\n" +
				"    },\n" +
				"    {\n" +
				"        $project: {\n" +
				"            _id: 0,\n" +
				"            examId: 1, statId: 1, courseId: 1, courseName: 1,\n" +
				"            participationNumber: 1, totalStudent: 1, highestScore: 1, lowestScore: 1, averageScore: 1, consist: {\n" +
				"                $concatArrays: [{\n" +
				"                    $map: {\n" +
				"                        input: '$consist',\n" +
				"                        as: 'item',\n" +
				"                        in: {\n" +
				"                            '档次': '$$item.name',\n" +
				"                            '最大分值': '$$item.maxScore',\n" +
				"                            '最小分值': '$$item.minScore',\n" +
				"                            '人数': '$$item.number',\n" +
				"                            '比例': '$$item.rate'\n" +
				"                        }\n" +
				"                    }\n" +
				"                }]\n" +
				"            }\n" +
				"        }\n" +
				"    }\n" +
				"]";
	}
}
