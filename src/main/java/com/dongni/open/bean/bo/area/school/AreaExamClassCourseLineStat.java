package com.dongni.open.bean.bo.area.school;

import com.dongni.open.bean.bo.area.AbstractAreaStatReadConfig;

import java.util.Collection;

/**
 * 区域班级课程上线统计
 *
 * <AUTHOR>
 * @date 2023/4/20 18:16
 */
public class AreaExamClassCourseLineStat extends AbstractAreaStatReadConfig {
    @Override
    public String getCollectionName() {
        return "examClassCourseLineStat";
    }

    @Override
    public String getJsonPath() {
        return "areaExamClassCourseLineStat.json";
    }

    @Override
    public FilePath getFiePath() {
        return FilePath.SCH;
    }

    @Override
    public String getAreaPipelineStr(String[] instanceIds) {
        return "[\n" +
                "    {\n" +
                "        $match: {\n" +
                "            examId: " + instanceIds[0] + ", statId: " + instanceIds[1] + "\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        $project: {\n" +
                "            examId: 1,\n" +
                "            schoolId: 1,\n" +
                "            schoolName: 1,\n" +
                "            statId: 1,\n" +
                "            statName: 1,\n" +
                "            classId: 1,\n" +
                "            className: 1,\n" +
                "            totalStudent: 1,\n" +
                "            courseId: 1,\n" +
                "            courseName: 1,\n" +
                "            participationNumber: 1,\n" +
                "            highestScore: 1,\n" +
                "            averageScore: 1,\n" +
                "            lines: {$objectToArray: '$$ROOT'}\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        $project: {\n" +
                "            examId: 1,\n" +
                "            schoolId: 1,\n" +
                "            schoolName: 1,\n" +
                "            statId: 1,\n" +
                "            classId: 1,\n" +
                "            className: 1,\n" +
                "            totalStudent: 1,\n" +
                "            courseId: 1,\n" +
                "            courseName: 1,\n" +
                "            participationNumber: 1,\n" +
                "            highestScore: 1,\n" +
                "            averageScore: 1,\n" +
                "            lines: {\n" +
                "                $filter: {\n" +
                "                    input: '$lines',\n" +
                "                    as: 'e',\n" +
                "                    cond: {\n" +
                "                        $regexMatch: {input: \"$$e.k\", regex: /^line/}\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        $project: {\n" +
                "            _id: 0,\n" +
                "            examId: 1,\n" +
                "            schoolId: 1,\n" +
                "            schoolName: 1,\n" +
                "            statId: 1,\n" +
                "            statName: 1,\n" +
                "            classId: 1,\n" +
                "            className: 1,\n" +
                "            totalStudent: 1,\n" +
                "            courseId: 1,\n" +
                "            courseName: 1,\n" +
                "            participationNumber: 1,\n" +
                "            highestScore: 1,\n" +
                "            averageScore: 1,\n" +
                "            lines: {\n" +
                "                $concatArrays: [{\n" +
                "                    $map: {\n" +
                "                        input: '$lines',\n" +
                "                        as: 'item',\n" +
                "                        in: {\n" +
                "                            '线次': {\n" +
                "                                $substr: ['$$item.k', 4, 1]\n" +
                "                            },\n" +
                "                            '单科分数线': '$$item.v.score',\n" +
                "                            '单上线': '$$item.v.singleUp',\n" +
                "                            '双上线': '$$item.v.doubleUp',\n" +
                "                            '单科上线率': '$$item.v.upRate',\n" +
                "                            '命中率': '$$item.v.hitRate',\n" +
                "                            '贡献率': '$$item.v.contributionRate'\n" +
                "                        }\n" +
                "                    }\n" +
                "                }]\n" +
                "            }\n" +
                "        }\n" +
                "    },\n" +
                "    {$project: {fields: 0}}\n" +
                "]";
    }
}
