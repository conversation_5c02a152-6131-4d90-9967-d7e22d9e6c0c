package com.dongni.open.bean.bo.area.school;

import com.dongni.open.bean.bo.area.AbstractAreaStatReadConfig;

import java.util.Collection;

/**
 * 区域学校课程档次统计
 *
 * <AUTHOR>
 * @date 2023/4/21 18:33
 */
public class AreaExamSchoolCourseConsistStat extends AbstractAreaStatReadConfig {
    @Override
    public String getCollectionName() {
        return "examSchoolCourseConsistStat";
    }

    @Override
    public String getJsonPath() {
        return "areaExamSchoolCourseConsistStat.json";
    }

    @Override
    public FilePath getFiePath() {
        return FilePath.SCH;
    }

    @Override
    public String getAreaPipelineStr(String[] instanceIds) {
        return "[\n" +
                "    {\n" +
                "        $match: {\n" +
                "            examId: " + instanceIds[0] + ", statId: " + instanceIds[1] + "\n" +
                "        }\n" +
                "    },\n" +
                "    {\n" +
                "        $project: {\n" +
                "            _id: 0,\n" +
                "            examId: 1,\n" +
                "            schoolId: 1,\n" +
                "            schoolName: 1,\n" +
                "            statId: 1,\n" +
                "            courseId: 1,\n" +
                "            courseName: 1,\n" +
                "            participationNumber: 1, totalStudent: 1, highestScore: 1, lowestScore: 1, averageScore: 1, consist: {\n" +
                "                $concatArrays: [{\n" +
                "                    $map: {\n" +
                "                        input: '$consist',\n" +
                "                        as: 'item',\n" +
                "                        in: {\n" +
                "                            '档次': '$$item.name',\n" +
                "                            '最大分值': '$$item.maxScore',\n" +
                "                            '最小分值': '$$item.minScore',\n" +
                "                            '人数': '$$item.number',\n" +
                "                            '比例': '$$item.rate'\n" +
                "                        }\n" +
                "                    }\n" +
                "                }]\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "]";
    }
}
