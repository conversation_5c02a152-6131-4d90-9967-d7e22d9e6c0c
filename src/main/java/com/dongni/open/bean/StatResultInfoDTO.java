package com.dongni.open.bean;

import com.hqjl.bi.annotations.BIColumn;

/**
 * @author: hzw
 * @date: 2023/3/8
 */
public class StatResultInfoDTO {

	@BIColumn(value = "instanceID", hidden = true)
	private String instanceID;
	@BIColumn("统计项名称")
	private String resultName;
	@BIColumn(value = "nextFunc", hidden = true)
	private String nextFunc;
	@BIColumn(value = "instanceName", hidden = true)
	private String instanceName;

	public StatResultInfoDTO(String instanceID, String reportName, String nextFunc, String instanceName) {
		this.instanceID = instanceID;
		this.resultName = reportName;
		this.nextFunc = nextFunc;
		this.instanceName = instanceName;
	}

	public String getInstanceID() {
		return instanceID;
	}

	public StatResultInfoDTO setInstanceID(String instanceID) {
		this.instanceID = instanceID;
		return this;
	}

	public String getReportName() {
		return resultName;
	}

	public StatResultInfoDTO setReportName(String reportName) {
		this.resultName = reportName;
		return this;
	}

	public String getNextFunc() {
		return nextFunc;
	}

	public StatResultInfoDTO setNextFunc(String nextFunc) {
		this.nextFunc = nextFunc;
		return this;
	}

	public String getInstanceName() {
		return instanceName;
	}

	public StatResultInfoDTO setInstanceName(String instanceName) {
		this.instanceName = instanceName;
		return this;
	}
}
